import { NextRequest } from 'next/server';
import { GET } from '@/app/api/dashboard/overview/route';
import { prisma } from '@/lib/prisma';

// Mock getServerSession
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}));

// Mock Prisma client
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
    },
    userRole: {
      findMany: jest.fn(),
    },
    userProfile: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
    session: {
      count: jest.fn(),
    },
    auditLog: {
      count: jest.fn(),
    },
    treaty: {
      count: jest.fn(),
    },
    nationTreaty: {
      count: jest.fn(),
    },
    remoteServer: {
      count: jest.fn(),
    },
    ordinance: {
      count: jest.fn(),
    },
  },
}));

// Mock NextResponse
const mockJson = jest.fn();
jest.mock('next/server', () => ({
  NextResponse: {
    json: mockJson,
  },
}));

// Mock getServerSession
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}));

describe('GET /api/dashboard/overview', () => {
  const { getServerSession } = require('next-auth');

  beforeEach(() => {
    jest.clearAllMocks();
    mockJson.mockClear();
  });

  it('should return 401 when user is not authenticated', async () => {
    // Mock unauthenticated session
    getServerSession.mockResolvedValue(null);

    const request = {} as NextRequest;
    await GET(request);

    expect(mockJson).toHaveBeenCalledWith(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  });

  it('should return 404 when user is not found', async () => {
    // Mock authenticated session
    getServerSession.mockResolvedValue({
      user: { email: '<EMAIL>' }
    });

    // Mock user not found
    (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

    const request = {} as NextRequest;
    await GET(request);

    expect(mockJson).toHaveBeenCalledWith(
      { error: 'User not found' },
      { status: 404 }
    );
  });

  it('should return dashboard data for regular user', async () => {
    // Mock authenticated session
    getServerSession.mockResolvedValue({
      user: { email: '<EMAIL>' }
    });

    // Mock user data
    const mockUser = {
      id: 'user_123',
      name: 'Test User',
      email: '<EMAIL>',
      createdAt: new Date(),
    };

    (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);
    (prisma.userRole.findMany as jest.Mock).mockResolvedValue([
      { role: { name: 'user' } }
    ]);
    (prisma.userProfile.findUnique as jest.Mock).mockResolvedValue(null);
    (prisma.userProfile.create as jest.Mock).mockResolvedValue({
      id: 'profile_123',
      userId: 'user_123',
      firstName: 'Test',
      lastName: 'User',
      personalEmail: '<EMAIL>',
    });

    // Mock statistics
    (prisma.user.count as jest.Mock).mockResolvedValue(100);
    (prisma.treaty.count as jest.Mock).mockResolvedValue(50);
    (prisma.nationTreaty.count as jest.Mock).mockResolvedValue(25);
    (prisma.remoteServer.count as jest.Mock).mockResolvedValue(10);
    (prisma.session.count as jest.Mock).mockResolvedValue(5);
    (prisma.auditLog.count as jest.Mock).mockResolvedValue(2);

    const request = {} as NextRequest;
    await GET(request);

    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        user: expect.objectContaining({
          id: 'user_123',
          name: 'Test User',
          email: '<EMAIL>',
        }),
        stats: expect.objectContaining({
          totalUsers: 100,
          totalTreaties: 50,
          totalNationTreaties: 25,
          totalServers: 10,
        }),
        quickActions: expect.arrayContaining([
          expect.objectContaining({
            id: 'view-profile',
            label: 'View Profile',
            href: '/profile',
          }),
        ]),
      })
    );
  });

  it('should return dashboard data for admin user', async () => {
    // Mock authenticated session
    getServerSession.mockResolvedValue({
      user: { email: '<EMAIL>' }
    });

    // Mock admin user data
    const mockUser = {
      id: 'admin_123',
      name: 'Admin User',
      email: '<EMAIL>',
      createdAt: new Date(),
    };

    (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);
    (prisma.userRole.findMany as jest.Mock).mockResolvedValue([
      { role: { name: 'admin' } }
    ]);
    (prisma.userProfile.findUnique as jest.Mock).mockResolvedValue(null);
    (prisma.userProfile.create as jest.Mock).mockResolvedValue({
      id: 'profile_123',
      userId: 'admin_123',
      firstName: 'Admin',
      lastName: 'User',
      personalEmail: '<EMAIL>',
    });

    // Mock statistics
    (prisma.user.count as jest.Mock).mockResolvedValue(100);
    (prisma.treaty.count as jest.Mock).mockResolvedValue(50);
    (prisma.nationTreaty.count as jest.Mock).mockResolvedValue(25);
    (prisma.remoteServer.count as jest.Mock).mockResolvedValue(10);
    (prisma.ordinance.count as jest.Mock).mockResolvedValue(15);
    (prisma.session.count as jest.Mock).mockResolvedValue(5);
    (prisma.auditLog.count as jest.Mock).mockResolvedValue(2);

    const request = {} as NextRequest;
    await GET(request);

    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        user: expect.objectContaining({
          id: 'admin_123',
          name: 'Admin User',
          email: '<EMAIL>',
        }),
        stats: expect.objectContaining({
          totalUsers: 100,
          totalTreaties: 50,
          totalNationTreaties: 25,
          totalServers: 10,
          totalOrdinances: 15,
        }),
        adminStats: expect.objectContaining({
          systemHealth: expect.objectContaining({
            database: 'healthy',
            cache: 'healthy',
            storage: 'healthy',
          }),
        }),
        quickActions: expect.arrayContaining([
          expect.objectContaining({
            id: 'create-user',
            label: 'Create New User',
            href: '/admin/manage/createuser',
          }),
        ]),
      })
    );
  });

  it('should handle errors gracefully', async () => {
    // Mock authenticated session
    getServerSession.mockResolvedValue({
      user: { email: '<EMAIL>' }
    });

    // Mock user data
    (prisma.user.findUnique as jest.Mock).mockRejectedValue(new Error('Database error'));

    const request = {} as NextRequest;
    await GET(request);

    expect(mockJson).toHaveBeenCalledWith(
      { error: 'Internal server error' },
      { status: 500 }
    );
  });
});