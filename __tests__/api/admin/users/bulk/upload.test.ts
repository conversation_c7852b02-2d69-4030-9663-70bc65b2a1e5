/**
 * @jest-environment node
 */

import { createMocks } from 'node-mocks-http';
import { POST } from '@/app/api/admin/users/bulk/upload/route';
import { prisma } from '@/lib/prisma';
import * as bcrypt from 'bcryptjs';

// Mock dependencies
jest.mock('@/lib/prisma');
jest.mock('bcryptjs');
jest.mock('@/lib/middleware/require-auth', () => ({
  withAuthHandler: (handler: any, options: any) => {
    return async (request: any) => {
      // Mock authenticated user
      const auth = {
        userId: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test Admin',
        roles: ['admin'],
        permissions: ['bulk_upload_users']
      };
      return handler(request, auth);
    };
  }
}));

jest.mock('@/lib/middleware/audit-logging', () => ({
  auditLoggingMiddleware: {
    logApiAccess: jest.fn().mockResolvedValue(undefined)
  }
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('/api/admin/users/bulk/upload', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementations
    mockBcrypt.hash.mockResolvedValue('hashed-password');
    
    // Mock database operations
    mockPrisma.user.findUnique.mockResolvedValue(null);
    mockPrisma.country.findUnique.mockResolvedValue({ id: 1, code: 'USA', name: 'United States' });
    mockPrisma.role.findUnique.mockResolvedValue({ id: 'role-1', name: 'user' });
    mockPrisma.$transaction.mockImplementation(async (callback: any) => {
      const mockTx = {
        user: {
          create: jest.fn().mockResolvedValue({
            id: 'created-user-id',
            email: '<EMAIL>',
            name: 'Test User'
          })
        },
        userProfile: {
          create: jest.fn().mockResolvedValue({})
        },
        userRole: {
          create: jest.fn().mockResolvedValue({})
        },
        country: {
          findUnique: jest.fn().mockResolvedValue({ id: 1, code: 'USA' })
        }
      };
      return callback(mockTx);
    });
  });

  describe('File validation', () => {
    it('should reject request without file', async () => {
      const formData = new FormData();
      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.message).toBe('No file uploaded');
    });

    it('should reject oversized files', async () => {
      const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', largeFile);

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.message).toContain('File size exceeds maximum limit');
    });

    it('should reject unsupported file types', async () => {
      const txtFile = new File(['data'], 'test.txt', {
        type: 'text/plain'
      });
      
      const formData = new FormData();
      formData.append('file', txtFile);

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.message).toBe('Unsupported file type. Please upload a CSV or Excel file');
    });
  });

  describe('CSV processing', () => {
    const createValidCSV = () => {
      const csvContent = [
        'email,name,firstName,lastName',
        '<EMAIL>,John Doe,John,Doe',
        '<EMAIL>,Jane Smith,Jane,Smith'
      ].join('\\n');

      return new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
    };

    it('should validate CSV with exact header matches', async () => {
      const file = createValidCSV();
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('validateOnly', 'true');

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.validation.totalRows).toBe(2);
      expect(data.validation.validRows).toBe(2);
    });

    it('should suggest header mappings for non-exact matches', async () => {
      const csvContent = [
        'email_address,full_name,first_name',
        '<EMAIL>,John Doe,John'
      ].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('validateOnly', 'true');

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.headerAnalysis).toBeDefined();
      expect(data.headerAnalysis.suggestedMappings.length).toBeGreaterThan(0);
    });

    it('should handle custom field mappings', async () => {
      const csvContent = [
        'user_email,user_name',
        '<EMAIL>,John Doe'
      ].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const customMappings = [
        { csvHeader: 'user_email', databaseField: 'email', confirmed: true },
        { csvHeader: 'user_name', databaseField: 'name', confirmed: true }
      ];

      const formData = new FormData();
      formData.append('file', file);
      formData.append('mode', 'custom');
      formData.append('customMappings', JSON.stringify(customMappings));
      formData.append('validateOnly', 'true');

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.validation.validRows).toBe(1);
    });

    it('should reject CSV with too many rows', async () => {
      // Create CSV with more than MAX_ROWS (1000)
      const headers = 'email,name';
      const rows = Array.from({ length: 1001 }, (_, i) => 
        `test${i}@example.com,User ${i}`
      );
      const csvContent = [headers, ...rows].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.message).toContain('too many rows');
    });
  });

  describe('Data validation', () => {
    it('should validate email format', async () => {
      const csvContent = [
        'email,name',
        'invalid-email,John Doe',
        '<EMAIL>,Jane Smith'
      ].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('validateOnly', 'true');

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.validation.validRows).toBe(1);
      expect(data.validation.invalidRows).toBe(1);
      expect(data.validation.errors).toContain(
        expect.stringContaining('Invalid value for email')
      );
    });

    it('should detect duplicate emails in file', async () => {
      const csvContent = [
        'email,name',
        '<EMAIL>,John Doe',
        '<EMAIL>,Jane Smith'
      ].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('validateOnly', 'true');

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.validation.validRows).toBe(1);
      expect(data.validation.errors).toContain(
        expect.stringContaining('Duplicate email')
      );
    });

    it('should warn about existing users', async () => {
      // Mock existing user
      mockPrisma.user.findUnique.mockResolvedValueOnce({
        id: 'existing-user',
        email: '<EMAIL>',
        name: 'Existing User'
      });

      const csvContent = [
        'email,name',
        '<EMAIL>,John Doe',
        '<EMAIL>,Jane Smith'
      ].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('validateOnly', 'true');

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.validation.validRows).toBe(1); // Only new user
      expect(data.validation.warnings).toContain(
        expect.stringContaining('already exists')
      );
    });
  });

  describe('User creation', () => {
    it('should create users successfully', async () => {
      const csvContent = [
        'email,name,firstName,lastName',
        '<EMAIL>,John Doe,John,Doe',
        '<EMAIL>,Jane Smith,Jane,Smith'
      ].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.results.processedUsers).toBe(2);
      expect(data.results.createdUsers).toHaveLength(2);
    });

    it('should create user profiles with additional data', async () => {
      const csvContent = [
        'email,name,firstName,lastName,phone,countryCode',
        '<EMAIL>,John Doe,John,Doe,555-1234,USA'
      ].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);

      // Verify user profile was created
      const mockTx = mockPrisma.$transaction.mock.calls[0][0];
      await mockTx({
        user: { create: jest.fn().mockResolvedValue({ id: 'user-id' }) },
        userProfile: { create: jest.fn() },
        country: { findUnique: jest.fn().mockResolvedValue({ id: 1 }) }
      });

      expect(mockPrisma.$transaction).toHaveBeenCalled();
    });

    it('should assign roles to users', async () => {
      const csvContent = [
        'email,name,roles',
        '<EMAIL>,John Doe,\"admin,user\"'
      ].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });

    it('should handle creation errors gracefully', async () => {
      // Mock transaction to throw error for second user
      let callCount = 0;
      mockPrisma.$transaction.mockImplementation(async (callback: any) => {
        const mockTx = {
          user: {
            create: jest.fn().mockImplementation(() => {
              callCount++;
              if (callCount === 2) {
                throw new Error('Database constraint violation');
              }
              return {
                id: `user-${callCount}`,
                email: `test${callCount}@example.com`,
                name: `User ${callCount}`
              };
            })
          },
          userProfile: {
            create: jest.fn().mockResolvedValue({})
          }
        };
        return callback(mockTx);
      });

      const csvContent = [
        'email,name',
        '<EMAIL>,John Doe',
        '<EMAIL>,Jane Smith'
      ].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.results.processedUsers).toBe(1); // Only first user succeeded
      expect(data.results.failedUsers).toBe(1);
      expect(data.results.errors).toContain(
        expect.stringContaining('Failed to create user')
      );
    });

    it('should return error when no valid users to create', async () => {
      const csvContent = [
        'email,name',
        'invalid-email,John Doe'
      ].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.message).toBe('No valid users to create');
    });
  });

  describe('Error handling', () => {
    it('should handle invalid JSON in custom mappings', async () => {
      const file = new File(['email\\<EMAIL>'], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('customMappings', 'invalid-json');

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.message).toBe('Invalid custom mappings JSON format');
    });

    it('should handle database errors', async () => {
      mockPrisma.$transaction.mockRejectedValue(new Error('Database connection failed'));

      const csvContent = [
        'email,name',
        '<EMAIL>,John Doe'
      ].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.message).toBe('Failed to process bulk upload');
    });

    it('should handle empty CSV files', async () => {
      const file = new File([''], 'empty.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      const response = await POST(req);
      const data = await response.json();

      expect(response.status).toBe(500); // parseCSV throws error for empty file
    });
  });

  describe('Audit logging', () => {
    it('should log successful uploads', async () => {
      const { auditLoggingMiddleware } = require('@/lib/middleware/audit-logging');

      const csvContent = [
        'email,name',
        '<EMAIL>,John Doe'
      ].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      await POST(req);

      expect(auditLoggingMiddleware.logApiAccess).toHaveBeenCalledWith(
        'global',
        'test-user-id',
        'bulk_upload_users',
        'bulk_upload',
        true,
        expect.any(Object),
        expect.objectContaining({
          statusCode: 200,
          metadata: expect.objectContaining({
            fileName: 'users.csv',
            totalRows: 1,
            processedUsers: 1
          })
        })
      );
    });

    it('should log validation-only requests', async () => {
      const { auditLoggingMiddleware } = require('@/lib/middleware/audit-logging');

      const csvContent = [
        'email,name',
        '<EMAIL>,John Doe'
      ].join('\\n');

      const file = new File([csvContent], 'users.csv', {
        type: 'text/csv'
      });
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('validateOnly', 'true');

      const { req } = createMocks({
        method: 'POST',
        body: formData
      });

      await POST(req);

      expect(auditLoggingMiddleware.logApiAccess).toHaveBeenCalledWith(
        'global',
        'test-user-id',
        'validate_bulk_upload',
        'bulk_upload',
        true,
        expect.any(Object),
        expect.objectContaining({
          metadata: expect.objectContaining({
            validateOnly: true
          })
        })
      );
    });
  });
});
