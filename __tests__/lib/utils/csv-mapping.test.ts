import { 
  analyzeCSVHeaders, 
  createFieldMapping, 
  validateCustomMappings, 
  validateAndTransformRow,
  estimateProcessingTime,
  STANDARD_FIELD_MAPPINGS,
  REQUIRED_FIELDS
} from '@/lib/utils/csv-mapping';

// Mock prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    country: {
      findUnique: jest.fn()
    }
  }
}));

describe('CSV Mapping Utilities', () => {
  describe('analyzeCSVHeaders', () => {
    it('should identify exact matches in smart mode', () => {
      const headers = ['email', 'name', 'firstName', 'lastName'];
      const result = analyzeCSVHeaders(headers, 'smart');
      
      expect(result.exactMatches).toContain('email');
      expect(result.exactMatches).toContain('name');
      expect(result.exactMatches).toContain('firstName');
      expect(result.exactMatches).toContain('lastName');
      expect(result.validationPassed).toBe(true);
      expect(result.mode).toBe('smart');
    });

    it('should suggest mappings for similar headers', () => {
      const headers = ['email_address', 'full_name', 'first_name'];
      const result = analyzeCSVHeaders(headers, 'smart');
      
      expect(result.suggestedMappings.length).toBeGreaterThan(0);
      const emailMapping = result.suggestedMappings.find(m => m.csvHeader === 'email_address');
      expect(emailMapping?.suggestedField).toBe('email');
      expect(emailMapping?.confidence).toBeGreaterThan(0.5);
    });

    it('should fail validation when required fields are missing', () => {
      const headers = ['name', 'phone'];
      const result = analyzeCSVHeaders(headers, 'smart');
      
      expect(result.validationPassed).toBe(false);
      expect(result.missingRequired).toContain('email');
    });

    it('should handle strict mode correctly', () => {
      const headers = ['email_address', 'full_name'];
      const result = analyzeCSVHeaders(headers, 'strict');
      
      expect(result.exactMatches).toHaveLength(0);
      expect(result.suggestedMappings).toHaveLength(0);
      expect(result.unmappedHeaders).toContain('email_address');
      expect(result.unmappedHeaders).toContain('full_name');
    });

    it('should handle empty headers', () => {
      const result = analyzeCSVHeaders([], 'smart');
      
      expect(result.exactMatches).toHaveLength(0);
      expect(result.validationPassed).toBe(false);
      expect(result.missingRequired).toEqual(['email']);
    });
  });

  describe('createFieldMapping', () => {
    it('should create mapping from exact matches', () => {
      const headers = ['email', 'name', 'firstName'];
      const mapping = createFieldMapping(headers);
      
      expect(mapping['email']).toBe('email');
      expect(mapping['name']).toBe('name');
      expect(mapping['firstName']).toBe('firstName');
    });

    it('should create mapping from custom mappings', () => {
      const headers = ['user_email', 'user_name'];
      const customMappings = [
        { csvHeader: 'user_email', databaseField: 'email', confirmed: true },
        { csvHeader: 'user_name', databaseField: 'name', confirmed: true }
      ];
      
      const mapping = createFieldMapping(headers, customMappings);
      
      expect(mapping['user_email']).toBe('email');
      expect(mapping['user_name']).toBe('name');
    });

    it('should only include confirmed custom mappings', () => {
      const headers = ['user_email'];
      const customMappings = [
        { csvHeader: 'user_email', databaseField: 'email', confirmed: false }
      ];
      
      const mapping = createFieldMapping(headers, customMappings);
      
      expect(mapping['user_email']).toBeUndefined();
    });
  });

  describe('validateCustomMappings', () => {
    it('should validate correct mappings', () => {
      const mappings = [
        { csvHeader: 'user_email', databaseField: 'email', confirmed: true },
        { csvHeader: 'user_name', databaseField: 'name', confirmed: true }
      ];
      
      const result = validateCustomMappings(mappings);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect duplicate database fields', () => {
      const mappings = [
        { csvHeader: 'email1', databaseField: 'email', confirmed: true },
        { csvHeader: 'email2', databaseField: 'email', confirmed: true }
      ];
      
      const result = validateCustomMappings(mappings);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain("Database field 'email' is mapped multiple times");
    });

    it('should detect duplicate CSV headers', () => {
      const mappings = [
        { csvHeader: 'email', databaseField: 'email', confirmed: true },
        { csvHeader: 'email', databaseField: 'personalEmail', confirmed: true }
      ];
      
      const result = validateCustomMappings(mappings);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain("CSV header 'email' is used multiple times");
    });

    it('should detect missing required fields', () => {
      const mappings = [
        { csvHeader: 'name', databaseField: 'name', confirmed: true }
        // Missing email mapping
      ];
      
      const result = validateCustomMappings(mappings);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Required fields are missing: email');
    });

    it('should warn about non-standard fields', () => {
      const mappings = [
        { csvHeader: 'email', databaseField: 'email', confirmed: true },
        { csvHeader: 'custom', databaseField: 'nonStandardField', confirmed: true }
      ];
      
      const result = validateCustomMappings(mappings);
      
      expect(result.warnings).toContain("Database field 'nonStandardField' is not a standard field");
    });
  });

  describe('validateAndTransformRow', () => {
    const mockPrisma = require('@/lib/prisma').prisma;

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should validate and transform valid email', async () => {
      const row = { email: '  <EMAIL>  ' };
      const fieldMapping = { email: 'email' };
      
      const result = await validateAndTransformRow(row, fieldMapping, 0);
      
      expect(result.valid).toBe(true);
      expect(result.transformedRow.email).toBe('<EMAIL>');
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid email format', async () => {
      const row = { email: 'invalid-email' };
      const fieldMapping = { email: 'email' };
      
      const result = await validateAndTransformRow(row, fieldMapping, 0);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Row 1: Invalid value for email: Invalid email format');
    });

    it('should validate and transform date', async () => {
      const row = { 
        email: '<EMAIL>',
        birthDate: '1985-03-15' 
      };
      const fieldMapping = { 
        email: 'email',
        birthDate: 'dateOfBirth' 
      };
      
      const result = await validateAndTransformRow(row, fieldMapping, 0);
      
      expect(result.valid).toBe(true);
      expect(result.transformedRow.dateOfBirth).toBeInstanceOf(Date);
    });

    it('should reject invalid date format', async () => {
      const row = { 
        email: '<EMAIL>',
        birthDate: 'invalid-date' 
      };
      const fieldMapping = { 
        email: 'email',
        birthDate: 'dateOfBirth' 
      };
      
      const result = await validateAndTransformRow(row, fieldMapping, 0);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Row 1: Invalid value for dateOfBirth: Invalid date format. Use YYYY-MM-DD');
    });

    it('should validate boolean values', async () => {
      const row = { 
        email: '<EMAIL>',
        active: 'true',
        mfa: '1',
        enabled: 'yes',
        disabled: 'false'
      };
      const fieldMapping = { 
        email: 'email',
        active: 'isActive',
        mfa: 'twoFactorEnabled',
        enabled: 'twoFactorEnabled',
        disabled: 'isActive'
      };
      
      const result = await validateAndTransformRow(row, fieldMapping, 0);
      
      expect(result.valid).toBe(true);
      expect(result.transformedRow.isActive).toBe(false); // 'disabled' = 'false' maps to false
      expect(result.transformedRow.twoFactorEnabled).toBe(true); // 'enabled' = 'yes' maps to true
    });

    it('should validate country code', async () => {
      mockPrisma.country.findUnique.mockResolvedValue({ id: 1, code: 'USA' });
      
      const row = { 
        email: '<EMAIL>',
        country: 'usa' 
      };
      const fieldMapping = { 
        email: 'email',
        country: 'countryCode' 
      };
      
      const result = await validateAndTransformRow(row, fieldMapping, 0);
      
      expect(result.valid).toBe(true);
      expect(result.transformedRow.countryCode).toBe('USA');
      expect(mockPrisma.country.findUnique).toHaveBeenCalledWith({
        where: { code: 'USA' }
      });
    });

    it('should reject invalid country code', async () => {
      mockPrisma.country.findUnique.mockResolvedValue(null);
      
      const row = { 
        email: '<EMAIL>',
        country: 'INVALID' 
      };
      const fieldMapping = { 
        email: 'email',
        country: 'countryCode' 
      };
      
      const result = await validateAndTransformRow(row, fieldMapping, 0);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain("Row 1: Invalid value for countryCode: Country code 'INVALID' not found");
    });

    it('should parse comma-separated arrays', async () => {
      const row = { 
        email: '<EMAIL>',
        roles: 'admin, user,  moderator  ' 
      };
      const fieldMapping = { 
        email: 'email',
        roles: 'roles' 
      };
      
      const result = await validateAndTransformRow(row, fieldMapping, 0);
      
      expect(result.valid).toBe(true);
      expect(result.transformedRow.roles).toEqual(['admin', 'user', 'moderator']);
    });

    it('should handle empty values', async () => {
      const row = { 
        email: '<EMAIL>',
        name: '',
        phone: '   ' 
      };
      const fieldMapping = { 
        email: 'email',
        name: 'name',
        phone: 'phone' 
      };
      
      const result = await validateAndTransformRow(row, fieldMapping, 0);
      
      expect(result.valid).toBe(true);
      expect(result.transformedRow.name).toBeUndefined();
      expect(result.transformedRow.phone).toBeUndefined();
    });

    it('should detect missing required fields', async () => {
      const row = { name: 'Test User' };
      const fieldMapping = { name: 'name' };
      
      const result = await validateAndTransformRow(row, fieldMapping, 0);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain("Row 1: Required field 'email' is missing or empty");
    });
  });

  describe('estimateProcessingTime', () => {
    it('should estimate time for small uploads', () => {
      const result = estimateProcessingTime(10);
      
      expect(result.estimated).toBe(10); // 10 * 0.5 + 5 = 10
      expect(result.description).toBe('Quick processing');
    });

    it('should estimate time for medium uploads', () => {
      const result = estimateProcessingTime(200);
      
      expect(result.estimated).toBe(105); // 200 * 0.5 + 5 = 105
      expect(result.description).toBe('This may take a few minutes');
    });

    it('should estimate time for large uploads', () => {
      const result = estimateProcessingTime(500);
      
      expect(result.estimated).toBe(255); // 500 * 0.5 + 5 = 255
      expect(result.description).toBe('This may take several minutes');
    });

    it('should estimate time for very large uploads', () => {
      const result = estimateProcessingTime(1500);
      
      expect(result.estimated).toBe(755); // 1500 * 0.5 + 5 = 755
      expect(result.description).toBe('This may take 10+ minutes');
    });
  });

  describe('STANDARD_FIELD_MAPPINGS', () => {
    it('should include all required fields', () => {
      const allMappedFields = Object.keys(STANDARD_FIELD_MAPPINGS);
      
      REQUIRED_FIELDS.forEach(requiredField => {
        expect(allMappedFields).toContain(requiredField);
      });
    });

    it('should have valid mapping variations', () => {
      Object.entries(STANDARD_FIELD_MAPPINGS).forEach(([field, variations]) => {
        expect(Array.isArray(variations)).toBe(true);
        expect(variations.length).toBeGreaterThan(0);
        expect(variations[0]).toBe(field); // First variation should be the field name itself
        
        variations.forEach(variation => {
          expect(typeof variation).toBe('string');
          expect(variation.length).toBeGreaterThan(0);
        });
      });
    });

    it('should have unique variations within each field', () => {
      Object.entries(STANDARD_FIELD_MAPPINGS).forEach(([field, variations]) => {
        const uniqueVariations = new Set(variations);
        expect(uniqueVariations.size).toBe(variations.length);
      });
    });
  });
});
