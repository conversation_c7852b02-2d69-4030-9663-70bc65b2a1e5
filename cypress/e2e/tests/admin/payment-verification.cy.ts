/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = Cypress.env('testPassword') || 'password123';

interface PaymentRecord {
    id: string;
    amount: number;
    currency: string;
    paymentMethod: string;
    status: string;
    transactionId: string;
    payerName: string;
    payerEmail: string;
    payerPhone?: string;
    notes?: string;
    receiptUrl?: string;
    createdAt: string;
    updatedAt: string;
    userTreatyType: {
        id: string;
        status: string;
        user: {
            id: string;
            name: string;
            email: string;
        };
        treatyType: {
            id: string;
            name: string;
            category: string;
        };
    };
}

const buildPayment = (overrides: Partial<PaymentRecord> = {}): PaymentRecord => {
    const base: PaymentRecord = {
        id: 'payment-1',
        amount: 15000,
        currency: 'USD',
        paymentMethod: 'Credit Card',
        status: 'PENDING',
        transactionId: 'txn-123',
        payerName: '<PERSON>',
        payerEmail: '<EMAIL>',
        createdAt: new Date('2024-01-15T09:30:00Z').toISOString(),
        updatedAt: new Date('2024-01-15T09:30:00Z').toISOString(),
        notes: 'Initial submission',
        receiptUrl: 'https://example.com/receipt.pdf',
        userTreatyType: {
            id: 'utt-1',
            status: 'PENDING',
            user: {
                id: 'user-1',
                name: 'Ava Patel',
                email: '<EMAIL>',
            },
            treatyType: {
                id: 'treaty-1',
                name: 'Economic Cooperation Treaty',
                category: 'ECONOMIC',
            },
        },
    };

    return {
        ...base,
        ...overrides,
        userTreatyType: {
            ...base.userTreatyType,
            ...overrides.userTreatyType,
            user: {
                ...base.userTreatyType.user,
                ...(overrides.userTreatyType?.user ?? {}),
            },
            treatyType: {
                ...base.userTreatyType.treatyType,
                ...(overrides.userTreatyType?.treatyType ?? {}),
            },
        },
    };
};

const loadPaymentsPage = (payments: PaymentRecord[]) => {
    cy.intercept('GET', '**/api/admin/check-permissions', {
        statusCode: 200,
        body: { isAdmin: true }
    }).as('checkPermissions');

    cy.intercept('GET', '**/api/admin/treaty-payments', {
        statusCode: 200,
        body: { success: true, payments }
    }).as('getAdminPayments');

    cy.visit('/admin/payments');
    cy.wait('@checkPermissions');
    cy.wait('@getAdminPayments');
};

describe('Admin - Payment Verification Dashboard', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        cy.loginAs(adminEmail, adminPassword, { redirectTo: '/dashboard' });
    });

    afterEach(() => {
        cy.logout();
    });

    it('should display the payment verification dashboard with key metrics', () => {
        const payments = [
            buildPayment({ status: 'PENDING', id: 'payment-pending' }),
            buildPayment({
                id: 'payment-verified',
                status: 'PAID',
                amount: 12500,
                paymentMethod: 'Bank Transfer',
                payerName: 'Liam Chen',
                payerEmail: '<EMAIL>',
                userTreatyType: {
                    id: 'utt-2',
                    status: 'APPROVED',
                    user: {
                        id: 'user-2',
                        name: 'Liam Chen',
                        email: '<EMAIL>',
                    },
                    treatyType: {
                        id: 'treaty-2',
                        name: 'Environmental Agreement',
                        category: 'ENVIRONMENT',
                    },
                },
            }),
            buildPayment({
                id: 'payment-failed',
                status: 'FAILED',
                amount: 8000,
                payerName: 'Noor Hassan',
                payerEmail: '<EMAIL>',
            }),
        ];

        loadPaymentsPage(payments);

        cy.contains('Payment Verification Dashboard').should('be.visible');

        cy.contains('p', 'Pending').next().should('have.text', '1');
        cy.contains('p', 'Verified').next().should('have.text', '1');
        cy.contains('p', 'Failed').next().should('have.text', '1');
        cy.contains('p', 'Total Revenue').next().invoke('text').should('contain', '12,500');

        cy.contains('Payment Queue').should('be.visible');
        cy.get('table tbody tr').should('have.length', payments.length);
        TestReporter.recordTestResult('Payment verification dashboard displayed with metrics', 'passed', 400);
    });

    it('should allow filtering payments', () => {
        const payments = [
            buildPayment({
                id: 'payment-pending-1',
                status: 'PENDING',
                payerName: 'Kai Rivera',
                payerEmail: '<EMAIL>',
            }),
            buildPayment({
                id: 'payment-pending-2',
                status: 'PENDING',
                payerName: 'Sofia Martins',
                payerEmail: '<EMAIL>',
            }),
            buildPayment({
                id: 'payment-verified-1',
                status: 'PAID',
                payerName: 'Elias Novak',
                payerEmail: '<EMAIL>',
            }),
        ];

        loadPaymentsPage(payments);

        cy.contains('Payment Queue').should('be.visible');

        cy.get('[data-testid="admin-payments-status-filter-trigger"]').click();
        cy.get('[data-testid="admin-payments-status-filter-options"]').contains('Pending').click();

        cy.get('table tbody tr').should('have.length', 2);
        cy.get('table tbody tr').each(($row) => {
            cy.wrap($row).contains('PENDING');
        });
        TestReporter.recordTestResult('Payment filtering works', 'passed', 300);
    });

    it('should redirect non-admin users to unauthorized page', () => {
        cy.intercept('GET', '**/api/admin/check-permissions', {
            statusCode: 200,
            body: { isAdmin: false }
        }).as('checkPermissionsNonAdmin');

        cy.intercept('GET', '**/api/admin/treaty-payments', {
            statusCode: 403,
            body: { success: false, error: 'Forbidden' }
        }).as('getAdminPaymentsDenied');

        cy.visit('/admin/payments', { failOnStatusCode: false });
        cy.wait('@checkPermissionsNonAdmin');
        cy.url().should('include', '/unauthorized');
        TestReporter.recordTestResult('Non-admin redirected from payment dashboard', 'passed', 300);
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
