/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';

const adminEmail = Cypress.env('adminUser') || '<EMAIL>';
const adminPassword = Cypress.env('adminPassword') || 'AdminPass123!';

const buildSecurityDashboardResponse = () => {
    const now = new Date().toISOString();

    return {
        events: [
            {
                id: 'event-1',
                type: 'AUTHENTICATION_FAILURE',
                severity: 'HIGH',
                description: 'Multiple failed login attempts detected',
                source: 'AUTHENTICATION_SERVICE',
                userId: 'user-1',
                serverId: null,
                ipAddress: '************',
                userAgent: 'Mozilla/5.0',
                metadata: {
                    region: 'us-central',
                },
                detectedAt: now,
                resolvedAt: null,
                status: 'OPEN',
                assignedTo: '<EMAIL>',
                resolution: null,
            },
        ],
        alerts: [
            {
                id: 'alert-1',
                eventId: 'event-1',
                ruleId: 'critical_security_alert',
                channels: [
                    {
                        type: 'EMAIL',
                        config: {
                            recipients: ['<EMAIL>'],
                        },
                    },
                ],
                sentAt: now,
                status: 'SENT',
                errorMessage: null,
                metadata: {
                    deliveryTimeMs: 320,
                },
            },
        ],
        stats: {
            totalEvents: 1,
            unresolvedEvents: 1,
            totalAlerts: 1,
            failedAlerts: 0,
            timeframe: 'day',
            eventsByType: [
                {
                    type: 'AUTHENTICATION_FAILURE',
                    count: 1,
                },
            ],
            eventsBySeverity: [
                {
                    severity: 'HIGH',
                    count: 1,
                },
            ],
        },
    };
};

const stubSecurityDashboard = () => {
    cy.intercept('GET', '**/api/admin/security-dashboard*', {
        statusCode: 200,
        body: buildSecurityDashboardResponse(),
    }).as('getSecurityDashboard');
};

const buildNonAdminPermissionsResponse = () => ({
    id: 'non-admin-user',
    userId: 'non-admin-user',
    permissions: [
        {
            id: 'auth-access',
            name: 'Authentication Access',
            resource: 'AUTHENTICATION',
            action: 'ACCESS',
            description: 'Basic authentication access',
        },
    ],
    roles: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
});

const visitSecurityDashboard = () => {
    cy.visit('/admin/security');
    cy.wait('@getSecurityDashboard');
    // Wait for the page to load
    cy.contains('Security Dashboard', { timeout: 10000 }).should('be.visible');
};

describe('Admin - Security Dashboard', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        stubSecurityDashboard();
        // Login as admin user
        cy.loginAs(adminEmail, adminPassword);
    });

    afterEach(() => {
        cy.logout();
    });

    it('should display the security dashboard with key metrics', () => {
        visitSecurityDashboard();
        cy.contains('Security Dashboard').should('be.visible');
        cy.contains('Total Events').should('be.visible');
        cy.contains('Unresolved Events').should('be.visible');
        cy.contains('Total Alerts').should('be.visible');
        cy.contains('Failed Alerts').should('be.visible');
        cy.contains('Security Events').should('be.visible');
        cy.contains('Alert History').should('be.visible');
        cy.contains('Analytics').should('be.visible');
        TestReporter.recordTestResult('Security dashboard displayed with metrics', 'passed', 400, 'Security dashboard loads correctly');
    });

    it('should allow refreshing security metrics', () => {
        visitSecurityDashboard();
        cy.get('[data-testid="refresh-button"]').click();
        cy.wait('@getSecurityDashboard');
        cy.contains('Security Dashboard').should('be.visible'); // Page should still be visible after refresh
        TestReporter.recordTestResult('Security metrics refreshed', 'passed', 300, 'Refresh button works');
    });

    it('should redirect non-admin users to unauthorized page', () => {
        cy.intercept('GET', '**/api/user/permissions', {
            statusCode: 200,
            body: buildNonAdminPermissionsResponse(),
        }).as('getNonAdminPermissions');

        cy.intercept('GET', '**/api/admin/security-dashboard*', {
            statusCode: 403,
            body: { error: 'Forbidden' },
        }).as('getSecurityDashboard');

        cy.visit('/admin/security', { failOnStatusCode: false });
        cy.wait('@getNonAdminPermissions');
        cy.url().should('include', '/unauthorized');
        TestReporter.recordTestResult('Non-admin redirected from security dashboard', 'passed', 300, 'Non-admin users cannot access security dashboard');
    });

    after(function() {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
