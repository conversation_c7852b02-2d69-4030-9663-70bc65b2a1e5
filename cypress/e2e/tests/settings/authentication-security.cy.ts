"use strict";
const adminEmail = '<EMAIL>';
const adminPassword = 'AdminPass123!';

describe('System Settings - Authentication Security', () => {
    beforeEach(() => {
        // Ensure deterministic security baseline before each test
        cy.task('db:reset-system-settings');
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
        cy.task('db:reset-password', { email: adminEmail, password: adminPassword });
        // Login as admin user
        cy.loginAs(adminEmail, adminPassword);
        // Navigate to system settings
        cy.visit('/settings');
        cy.contains('System Settings').click();
    });

    afterEach(() => {
        cy.task('db:reset-system-settings');
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
    });

    it('should update authentication security settings', () => {
        // Test login attempts
        cy.get('input[id="maxLoginAttempts"]').clear().type('3');
        cy.get('input[id="maxLoginAttempts"]').should('have.value', '3');
        // Test lockout duration
        cy.get('input[id="lockoutDuration"]').clear().type('60');
        cy.get('input[id="lockoutDuration"]').should('have.value', '60');
        // Test session timeout
        cy.get('input[id="sessionTimeout"]').clear().type('120');
        cy.get('input[id="sessionTimeout"]').should('have.value', '120');
        // Test 2FA requirement
        cy.get('input[id="twoFactorAuthRequired"]').click();
        // Test email verification
        cy.get('input[id="emailVerificationRequired"]').click();
        // Save settings
        cy.contains('Save Settings').click();
        // Should show success message
        cy.contains('System settings saved successfully').should('be.visible');
    });
});
