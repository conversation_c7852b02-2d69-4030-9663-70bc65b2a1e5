"use strict";
const adminEmail = '<EMAIL>';
const adminPassword = 'AdminPass123!';

describe('System Settings - API and Logging', () => {
    beforeEach(() => {
        // Ensure deterministic security baseline before each test
        cy.task('db:reset-system-settings');
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
        cy.task('db:reset-password', { email: adminEmail, password: adminPassword });
        // Login as admin user
        cy.loginAs(adminEmail, adminPassword);
        // Navigate to system settings
        cy.visit('/settings');
        cy.contains('System Settings').click();
    });

    afterEach(() => {
        cy.task('db:reset-system-settings');
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
    });

    it('should update API and logging settings', () => {
        // Test API rate limit
        cy.get('input[id="apiRateLimit"]').clear().type('500');
        cy.get('input[id="apiRateLimit"]').should('have.value', '500');
        // Test audit log retention
        cy.get('input[id="auditLogRetention"]').clear().type('180');
        cy.get('input[id="auditLogRetention"]').should('have.value', '180');
        // Test password reset token expiry
        cy.get('input[id="passwordResetTokenExpiry"]').clear().type('4');
        cy.get('input[id="passwordResetTokenExpiry"]').should('have.value', '4');
        // Save settings
        cy.contains('Save Settings').click();
        // Should show success message
        cy.contains('System settings saved successfully').should('be.visible');
    });
});
