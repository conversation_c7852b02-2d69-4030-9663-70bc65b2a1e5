"use strict";
const adminEmail = '<EMAIL>';
const adminPassword = 'AdminPass123!';

describe('System Settings - General', () => {
    beforeEach(() => {
        // Ensure deterministic security baseline before each test
        cy.task('db:reset-system-settings');
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
        cy.task('db:reset-password', { email: adminEmail, password: adminPassword });
        // Login as admin user
        cy.loginAs(adminEmail, adminPassword);
        // Navigate to system settings
        cy.visit('/settings');
        cy.contains('System Settings').click();
    });

    afterEach(() => {
        cy.task('db:reset-system-settings');
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
    });

    it('should load current system settings', () => {
        // Should display all settings sections
        cy.contains('Authentication Security').should('be.visible');
        cy.contains('Password Policy').should('be.visible');
        cy.contains('Network Security').should('be.visible');
        cy.contains('API & Logging').should('be.visible');
        // Should show current values
        cy.get('input[type="number"]').should('have.length.at.least', 6);
        cy.get('input[type="checkbox"], [type="switch"]').should('have.length.at.least', 6);
    });
});
