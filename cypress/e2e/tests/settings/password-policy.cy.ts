"use strict";
const adminEmail = '<EMAIL>';
const adminPassword = 'AdminPass123!';

describe('System Settings - Password Policy', () => {
    beforeEach(() => {
        // Ensure deterministic security baseline before each test
        cy.task('db:reset-system-settings');
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
        cy.task('db:reset-password', { email: adminEmail, password: adminPassword });
        // Login as admin user
        cy.loginAs(adminEmail, adminPassword);
        // Navigate to system settings
        cy.visit('/settings');
        cy.contains('System Settings').click();
    });

    afterEach(() => {
        cy.task('db:reset-system-settings');
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
    });

    it('should update password policy settings', () => {
        // Test password minimum length
        cy.get('input[id="passwordMinLength"]').clear().type('16');
        cy.get('input[id="passwordMinLength"]').should('have.value', '16');
        // Test character requirements
        cy.get('input[id="passwordRequireUppercase"]').click();
        cy.get('input[id="passwordRequireLowercase"]').click();
        cy.get('input[id="passwordRequireNumbers"]').click();
        cy.get('input[id="passwordRequireSpecialChars"]').click();
        // Save settings
        cy.contains('Save Settings').click();
        // Should show success message
        cy.contains('System settings saved successfully').should('be.visible');
    });
});
