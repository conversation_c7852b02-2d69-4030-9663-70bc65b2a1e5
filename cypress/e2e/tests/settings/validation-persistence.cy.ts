"use strict";
const adminEmail = '<EMAIL>';
const adminPassword = 'AdminPass123!';

describe('System Settings - Validation and Persistence', () => {
    beforeEach(() => {
        // Ensure deterministic security baseline before each test
        cy.task('db:reset-system-settings');
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
        cy.task('db:reset-password', { email: adminEmail, password: adminPassword });
        // Login as admin user
        cy.loginAs(adminEmail, adminPassword);
        // Navigate to system settings
        cy.visit('/settings');
        cy.contains('System Settings').click();
    });

    afterEach(() => {
        cy.task('db:reset-system-settings');
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
    });

    it('should handle validation errors', () => {
        // Test invalid values
        cy.get('input[id="passwordMinLength"]').clear().type('200'); // Too high
        cy.get('input[id="maxLoginAttempts"]').clear().type('0'); // Too low
        // Try to save
        cy.contains('Save Settings').click();
        // Should handle validation gracefully
        // (The exact behavior depends on your validation implementation)
    });

    it('should persist settings between sessions', () => {
        // Set specific values
        cy.get('input[id="passwordMinLength"]').clear().type('14');
        cy.get('input[id="maxLoginAttempts"]').clear().type('7');
        cy.contains('Save Settings').click();
        // Refresh page
        cy.reload();
        // Values should persist
        cy.get('input[id="passwordMinLength"]').should('have.value', '14');
        cy.get('input[id="maxLoginAttempts"]').should('have.value', '7');
    });
});
