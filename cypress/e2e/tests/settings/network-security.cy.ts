"use strict";
const adminEmail = '<EMAIL>';
const adminPassword = 'AdminPass123!';

describe('System Settings - Network Security', () => {
    beforeEach(() => {
        // Ensure deterministic security baseline before each test
        cy.task('db:reset-system-settings');
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
        cy.task('db:reset-password', { email: adminEmail, password: adminPassword });
        // Login as admin user
        cy.loginAs(adminEmail, adminPassword);
        // Navigate to system settings
        cy.visit('/settings');
        cy.contains('System Settings').click();
    });

    afterEach(() => {
        cy.task('db:reset-system-settings');
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
    });

    it('should update network security settings', () => {
        // Test IP whitelisting
        cy.get('input[id="ipWhitelistEnabled"]').click();
        // Test IP whitelist input
        cy.get('textarea[id="ipWhitelist"]').clear().type('*************\n10.0.0.0/8\n172.16.0.1');
        cy.get('textarea[id="ipWhitelist"]').should('contain.value', '*************');
        // Test CORS origins
        cy.get('textarea[id="corsOrigins"]').clear().type('https://app.nwaalliance.org\nhttps://admin.nwaalliance.org');
        cy.get('textarea[id="corsOrigins"]').should('contain.value', 'https://app.nwaalliance.org');
        // Save settings
        cy.contains('Save Settings').click();
        // Should show success message
        cy.contains('System settings saved successfully').should('be.visible');
    });
});
