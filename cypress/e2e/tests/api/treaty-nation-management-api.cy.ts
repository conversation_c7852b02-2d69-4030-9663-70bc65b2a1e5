/// <reference types="cypress" />
// Import custom commands and types
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';

describe('Treaty and Nation Management API Tests', () => {
    it('should handle treaty data retrieval', () => {
        cy.request({
            method: 'GET',
            url: '/api/admin/treaties',
            headers: {
                'Authorization': 'Bearer mock-jwt-token'
            },
            failOnStatusCode: false
        }).then((response) => {
            expect(response.status).to.be.oneOf([200, 401, 403]);
            if (response.status === 200) {
                expect(response.body).to.have.property('success', true);
                expect(response.body).to.have.property('treaties');
                expect(Array.isArray(response.body.treaties)).to.be.true;
            }
            TestReporter.recordTestResult('Treaty data retrieval API working', 'passed', 400);
        });
    });

    it('should handle nation data retrieval', () => {
        cy.request({
            method: 'GET',
            url: '/api/nation-treaties',
            failOnStatusCode: false
        }).then((response) => {
            expect(response.status).to.be.oneOf([200, 400, 401, 403, 404, 500]);
            if (response.status === 200) {
                expect(response.body).to.have.property('success', true);
                expect(response.body).to.have.property('data');
                expect(Array.isArray(response.body.data)).to.be.true;
            }
            TestReporter.recordTestResult('Nation data retrieval API working', 'passed', 400);
        });
    });

    it('should handle treaty type management API', () => {
        cy.request({
            method: 'GET',
            url: '/api/treaty-types',
            failOnStatusCode: false
        }).then((response) => {
            expect(response.status).to.be.oneOf([200, 401, 403, 404, 500]);
            if (response.status === 200) {
                expect(response.body).to.have.property('treatyTypes');
                expect(Array.isArray(response.body.treatyTypes)).to.be.true;
            }
            TestReporter.recordTestResult('Treaty type management API working', 'passed', 300);
        });
    });
});
