/// <reference types="cypress" />
// Import custom commands and types
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';

describe('Dashboard and Analytics API Endpoints', () => {
    it('should handle dashboard overview data', () => {
        cy.request({
            method: 'GET',
            url: '/api/dashboard/overview',
            headers: {
                'Authorization': 'Bearer mock-jwt-token'
            },
            failOnStatusCode: false
        }).then((response) => {
            expect(response.status).to.be.oneOf([200, 401, 403]);
            if (response.status === 200) {
                expect(response.body).to.have.property('success', true);
                expect(response.body).to.have.property('data');
            }
            TestReporter.recordTestResult('Dashboard overview API working', 'passed', 400);
        });
    });

    it('should handle analytics data retrieval', () => {
        cy.request({
            method: 'GET',
            url: '/api/dashboard/analytics',
            headers: {
                'Authorization': 'Bearer mock-jwt-token'
            },
            failOnStatusCode: false
        }).then((response) => {
            expect(response.status).to.be.oneOf([200, 401, 403]);
            if (response.status === 200) {
                expect(response.body).to.have.property('success', true);
                expect(response.body).to.have.property('analytics');
            }
            TestReporter.recordTestResult('Analytics data API working', 'passed', 500);
        });
    });

    it('should handle security monitoring data', () => {
        cy.request({
            method: 'GET',
            url: '/api/dashboard/security',
            headers: {
                'Authorization': 'Bearer mock-jwt-token'
            },
            failOnStatusCode: false
        }).then((response) => {
            expect(response.status).to.be.oneOf([200, 401, 403]);
            if (response.status === 200) {
                expect(response.body).to.have.property('success', true);
                expect(response.body).to.have.property('security');
            }
            TestReporter.recordTestResult('Security monitoring API working', 'passed', 300);
        });
    });
});
