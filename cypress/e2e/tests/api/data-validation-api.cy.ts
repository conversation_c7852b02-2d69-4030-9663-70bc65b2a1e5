/// <reference types="cypress" />
// Import custom commands and types
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';

describe('API Response Validation and Data Integrity', () => {
    it('should validate JSON schema compliance', () => {
        cy.request({
            method: 'GET',
            url: '/api/users',
            headers: {
                'Authorization': 'Bearer mock-jwt-token'
            },
            failOnStatusCode: false
        }).then((response) => {
            if (response.status === 200) {
                expect(response.headers['content-type']).to.contain('application/json');
                expect(response.body).to.have.property('success');
                if (response.body.users && Array.isArray(response.body.users)) {
                    response.body.users.forEach((user) => {
                        // Validate user object structure
                        expect(user).to.be.an('object');
                        expect(user).to.have.property('id');
                        expect(user).to.have.property('email');
                        expect(typeof user.id).to.be('string');
                        expect(typeof user.email).to.be('string');
                    });
                }
            }
            TestReporter.recordTestResult('API JSON schema validation working', 'passed', 500);
        });
    });

    it('should handle pagination metadata correctly', () => {
        cy.request({
            method: 'GET',
            url: '/api/users?page=1&limit=10',
            headers: {
                'Authorization': 'Bearer mock-jwt-token'
            },
            failOnStatusCode: false
        }).then((response) => {
            if (response.status === 200) {
                expect(response.body).to.have.property('success', true);
                if (response.body.pagination) {
                    expect(response.body.pagination).to.have.property('page');
                    expect(response.body.pagination).to.have.property('limit');
                    expect(response.body.pagination).to.have.property('total');
                    expect(response.body.pagination).to.have.property('totalPages');
                }
            }
            TestReporter.recordTestResult('API pagination metadata working', 'passed', 300);
        });
    });

    it('should validate data consistency across endpoints', () => {
        // Get user data from different endpoints and compare
        cy.request({
            method: 'GET',
            url: '/api/users',
            headers: {
                'Authorization': 'Bearer mock-jwt-token'
            },
            failOnStatusCode: false
        }).then((usersResponse) => {
            if (usersResponse.status === 200 && usersResponse.body.users) {
                const user = usersResponse.body.users[0];
                if (user) {
                    cy.request({
                        method: 'GET',
                        url: `/api/users/${user.id}`,
                        headers: {
                            'Authorization': 'Bearer mock-jwt-token'
                        },
                        failOnStatusCode: false
                    }).then((userDetailResponse) => {
                        if (userDetailResponse.status === 200) {
                            expect(userDetailResponse.body).to.have.property('success', true);
                            expect(userDetailResponse.body).to.have.property('user');
                            expect(userDetailResponse.body.user.id).to.eq(user.id);
                            expect(userDetailResponse.body.user.email).to.eq(user.email);
                        }
                        TestReporter.recordTestResult('API data consistency validation working', 'passed', 600);
                    });
                }
            }
        });
    });
});
