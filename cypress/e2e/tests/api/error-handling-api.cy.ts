/// <reference types="cypress" />
// Import custom commands and types
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';

describe('Error Handling and Status Code Validation', () => {
    const expectErrorPayload = (body: any) => {
        if (typeof body === 'string') {
            expect(body).to.be.a('string');
        } else if (body && typeof body === 'object') {
            expect(body).to.satisfy((value: Record<string, unknown>) => {
                return 'error' in value || 'message' in value || 'success' in value;
            });
        } else {
            throw new Error('Unexpected error payload shape');
        }
    };

    it('should handle 400 Bad Request errors properly', () => {
        cy.request({
            method: 'POST',
            url: '/api/auth/signup',
            body: {
                email: 'invalid-email-format',
                password: 'short',
                name: ''
            },
            failOnStatusCode: false
        }).then((response) => {
            expect(response.status).to.eq(400);
            expectErrorPayload(response.body);
            TestReporter.recordTestResult('400 Bad Request error handling working', 'passed', 200);
        });
    });

    it('should handle 401 Unauthorized errors properly', () => {
        cy.request({
            method: 'GET',
            url: '/api/admin/users',
            failOnStatusCode: false
        }).then((response) => {
            expect([401, 403, 500]).to.include(response.status);
            expectErrorPayload(response.body);
            TestReporter.recordTestResult('401 Unauthorized error handling working', 'passed', 200);
        });
    });

    it('should handle permission errors from admin endpoints', () => {
        cy.request({
            method: 'POST',
            url: '/api/admin/users',
            failOnStatusCode: false
        }).then((response) => {
            expect([401, 403, 405, 500]).to.include(response.status);
            expectErrorPayload(response.body);
            TestReporter.recordTestResult('Admin endpoint permission error handling working', 'passed', 200);
        });
    });

    it('should handle 404 Not Found errors properly', () => {
        cy.request({
            method: 'GET',
            url: '/api/nonexistent-endpoint',
            failOnStatusCode: false
        }).then((response) => {
            expect(response.status).to.eq(404);
            expectErrorPayload(response.body);
            TestReporter.recordTestResult('404 Not Found error handling working', 'passed', 200);
        });
    });
});
