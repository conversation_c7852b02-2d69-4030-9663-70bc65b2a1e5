/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';
import { navigationHelpers, assertionHelpers } from '../../../support/test-utils';
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = 'AdminPass123!';

describe('Document Management - Documents Page', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        // Login as admin user
        cy.loginAs(adminEmail, adminPassword);
        cy.visit('/documents');
    });

    afterEach(() => {
        cy.logout();
    });

    it('should display document management tabs', () => {
        cy.get(selectors.documents.page).should('be.visible');
        cy.get(selectors.documents.ordinancesTab).should('be.visible');
        cy.get(selectors.documents.directivesTab).should('be.visible');
        cy.contains('Documents Management').should('be.visible');
        TestReporter.recordTestResult('Document management tabs displayed', 'passed', 200);
    });

    it('should switch between Ordinances and Directives tabs', () => {
        // Default tab should be Ordinances
        cy.get(selectors.documents.ordinancesTab).should('have.class', 'border-slate-700');
        cy.get(selectors.documents.ordinancesComponent).should('be.visible');

        // Switch to Directives tab
        cy.get(selectors.documents.directivesTab).click();
        cy.get(selectors.documents.directivesTab).should('have.class', 'border-slate-700');
        cy.get(selectors.documents.directivesComponent).should('be.visible');

        // Switch back to Ordinances tab
        cy.get(selectors.documents.ordinancesTab).click();
        cy.get(selectors.documents.ordinancesTab).should('have.class', 'border-slate-700');
        cy.get(selectors.documents.ordinancesComponent).should('be.visible');
        TestReporter.recordTestResult('Document management tabs switch correctly', 'passed', 400);
    });

    // Further tests for OrdinancesTab and DirectivesTab would go into their own files
    // e.g., ordinance-management/ordinances.cy.ts and document-management/directives.cy.ts

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
