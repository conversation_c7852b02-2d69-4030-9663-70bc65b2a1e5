/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';
import { navigationHelpers, assertionHelpers } from '../../../support/test-utils';
import { selectors } from '../../../support/selectors';

const regularUserEmail = '<EMAIL>';
const regularUserPassword = 'RegularPassword123!';

describe('Notifications - Notifications Page', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        // Login as a regular user
        cy.loginAs(regularUserEmail, regularUserPassword);
        cy.visit('/notifications');
    });

    afterEach(() => {
        cy.logout();
    });

    it('should display user notifications', () => {
        // Mock notifications API call
        cy.intercept('GET', '/api/notifications', {
            statusCode: 200,
            body: {
                success: true,
                notifications: [n
                    { id: '1', message: 'Welcome to NWA Portal!', read: false, type: 'info', createdAt: new Date().toISOString() },
                    { id: '2', message: 'Your password was changed.', read: true, type: 'alert', createdAt: new Date().toISOString() }
                ]
            }
        }).as('getNotifications');

        cy.reload(); // Reload to trigger intercept
        cy.wait('@getNotifications');

        cy.get(selectors.notifications.page).should('be.visible');
        cy.contains('Welcome to NWA Portal!').should('be.visible');
        cy.contains('Your password was changed.').should('be.visible');
        TestReporter.recordTestResult('User notifications displayed', 'passed', 400);
    });

    it('should allow marking a notification as read', () => {
        // Mock notifications API call
        cy.intercept('GET', '/api/notifications', {
            statusCode: 200,
            body: {
                success: true,
                notifications: [
                    { id: '1', message: 'Unread notification', read: false, type: 'info', createdAt: new Date().toISOString() }
                ]
            }
        }).as('getNotifications');

        // Mock mark as read API call
        cy.intercept('PUT', '/api/notifications/1/read', {
            statusCode: 200,
            body: {
                success: true,
                message: 'Notification marked as read'
            }
        }).as('markAsRead');

        cy.reload(); // Reload to trigger intercept
        cy.wait('@getNotifications');

        cy.get(selectors.notifications.notificationItem('1')).should('not.have.class', 'read'); // Assuming 'read' class for read notifications
        cy.get(selectors.notifications.markAsReadButton('1')).click();

        cy.wait('@markAsRead');
        assertionHelpers.shouldContainText(selectors.notifications.successMessage, 'Notification marked as read');
        cy.get(selectors.notifications.notificationItem('1')).should('have.class', 'read');
        TestReporter.recordTestResult('Notification marked as read', 'passed', 600);
    });

    it('should allow deleting a notification', () => {
        // Mock notifications API call
        cy.intercept('GET', '/api/notifications', {
            statusCode: 200,
            body: {
                success: true,
                notifications: [
                    { id: '1', message: 'Notification to delete', read: false, type: 'info', createdAt: new Date().toISOString() }
                ]
            }
        }).as('getNotifications');

        // Mock delete notification API call
        cy.intercept('DELETE', '/api/notifications/1', {
            statusCode: 200,
            body: {
                success: true,
                message: 'Notification deleted'
            }
        }).as('deleteNotification');

        cy.reload(); // Reload to trigger intercept
        cy.wait('@getNotifications');

        cy.get(selectors.notifications.deleteButton('1')).click();

        cy.wait('@deleteNotification');
        assertionHelpers.shouldContainText(selectors.notifications.successMessage, 'Notification deleted');
        cy.contains('Notification to delete').should('not.exist');
        TestReporter.recordTestResult('Notification deleted successfully', 'passed', 600);
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
