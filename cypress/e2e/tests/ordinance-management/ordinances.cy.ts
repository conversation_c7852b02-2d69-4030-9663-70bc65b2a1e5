/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';
import { navigationHelpers, formHelpers, assertionHelpers } from '../../../support/test-utils';
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = 'AdminPass123!';

describe('Ordinance Management - Ordinances Page', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        // Login as admin user
        cy.loginAs(adminEmail, adminPassword);
        cy.visit('/ordinances');
    });

    afterEach(() => {
        cy.logout();
    });

    it('should display ordinance management tabs and default to upload', () => {
        cy.get(selectors.ordinances.page).should('be.visible');
        cy.get(selectors.ordinances.uploadTab).should('be.visible').and('have.class', 'border-slate-700');
        cy.get(selectors.ordinances.viewTab).should('be.visible');
        cy.get(selectors.ordinances.uploadForm).should('be.visible');
        TestReporter.recordTestResult('Ordinance management tabs and default view displayed', 'passed', 300);
    });

    it('should allow switching to view ordinances tab and display search/list', () => {
        cy.get(selectors.ordinances.viewTab).click();
        cy.get(selectors.ordinances.viewTab).should('have.class', 'border-slate-700');
        cy.get(selectors.ordinances.searchComponent).should('be.visible');
        cy.get(selectors.ordinances.listComponent).should('be.visible');
        TestReporter.recordTestResult('Switched to view ordinances tab', 'passed', 200);
    });

    it('should allow uploading a new ordinance', () => {
        const ordinanceTitle = 'Test Ordinance';
        const ordinanceDescription = 'Description for test ordinance.';

        // Mock successful upload
        cy.intercept('POST', '/api/ordinances', {
            statusCode: 201,
            body: {
                success: true,
                message: 'Ordinance uploaded successfully'
            }
        }).as('uploadOrdinance');

        cy.get(selectors.ordinances.uploadTab).click(); // Ensure upload tab is active
        cy.get(selectors.ordinances.uploadFormTitleField).type(ordinanceTitle);
        cy.get(selectors.ordinances.uploadFormDescriptionField).type(ordinanceDescription);
        // Assuming a file input is available
        // cy.get('input[type="file"]').selectFile('cypress/fixtures/test-ordinance.pdf');
        cy.get(selectors.ordinances.uploadFormSubmitButton).click();

        cy.wait('@uploadOrdinance');
        assertionHelpers.shouldContainText(selectors.ordinances.successMessage, 'Ordinance uploaded successfully');
        TestReporter.recordTestResult('New ordinance uploaded successfully', 'passed', 600);
    });

    it('should handle search and pagination for ordinances', () => {
        // Mock search results
        cy.intercept('GET', '/api/ordinances?*', (req) => {
            const query = req.query.search || '';
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 10;

            const mockOrdinances = Array.from({ length: 25 }, (_, i) => ({
                id: `ord-${i + 1}`,
                title: `Ordinance ${i + 1} ${query ? `(${query})` : ''}`,
                description: `Description for ordinance ${i + 1}`,
                status: 'active',
                createdAt: new Date().toISOString()
            }));

            const filteredOrdinances = query
                ? mockOrdinances.filter(o => o.title.includes(query as string))
                : mockOrdinances;

            const totalPages = Math.ceil(filteredOrdinances.length / limit);
            const paginatedOrdinances = filteredOrdinances.slice((page - 1) * limit, page * limit);

            req.reply({
                statusCode: 200,
                body: {
                    ordinances: paginatedOrdinances,
                    totalPages: totalPages,
                    totalCount: filteredOrdinances.length
                }
            });
        }).as('searchOrdinances');

        cy.get(selectors.ordinances.viewTab).click(); // Switch to view tab
        cy.wait('@searchOrdinances');

        // Test search
        cy.get(selectors.ordinances.searchInput).type('Test');
        cy.get(selectors.ordinances.searchButton).click();
        cy.wait('@searchOrdinances');
        cy.get(selectors.ordinances.listComponent).should('contain.text', 'Test');

        // Test pagination (click next page)
        cy.get(selectors.ordinances.nextPageButton).click();
        cy.wait('@searchOrdinances');
        cy.get(selectors.ordinances.listComponent).should('not.contain.text', 'Ordinance 1'); // Should be on next page
        TestReporter.recordTestResult('Ordinance search and pagination working', 'passed', 800);
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
