/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';
import { navigationHelpers, assertionHelpers } from '../../../support/test-utils';
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = 'AdminPass123!';
const regularUserEmail = '<EMAIL>';
const regularUserPassword = 'RegularPassword123!';

describe('Security - Access Control', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        // Ensure clean state for users
        cy.task('db:reset-user', adminEmail);
        cy.task('db:reset-user', regularUserEmail);
        cy.task('db:reset-password', { email: adminEmail, password: adminPassword });
        cy.task('db:reset-password', { email: regularUserEmail, password: regularUserPassword });
        cy.task('db:set-user-role', { email: adminEmail, role: 'admin' });
        cy.task('db:set-user-role', { email: regularUserEmail, role: 'user' });
    });

    afterEach(() => {
        cy.logout();
    });

    context('Unauthenticated User Access', () => {
        it('should redirect unauthenticated users from protected UI routes to login', () => {
            cy.visit('/dashboard', { failOnStatusCode: false });
            cy.url().should('include', '/login');
            TestReporter.recordTestResult('Unauthenticated user redirected from UI', 'passed', 200);
        });

        it('should deny unauthenticated users access to protected API endpoints', () => {
            cy.request({
                url: '/api/admin/users',
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.be.oneOf([401, 403]);
                TestReporter.recordTestResult('Unauthenticated user denied API access', 'passed', 200);
            });
        });
    });

    context('Regular User Access', () => {
        beforeEach(() => {
            cy.loginAs(regularUserEmail, regularUserPassword);
        });

        it('should allow regular users access to their permitted UI routes', () => {
            cy.visit('/profile');
            cy.url().should('include', '/profile');
            cy.contains('User Profile').should('be.visible');
            TestReporter.recordTestResult('Regular user access to permitted UI', 'passed', 200);
        });

        it('should deny regular users access to admin-only UI routes', () => {
            cy.visit('/admin/security', { failOnStatusCode: false });
            cy.url().should('include', '/unauthorized');
            TestReporter.recordTestResult('Regular user denied admin UI access', 'passed', 200);
        });

        it('should allow regular users access to their permitted API endpoints', () => {
            cy.request({
                url: '/api/profile',
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                expect(response.body.user.email).to.eq(regularUserEmail);
                TestReporter.recordTestResult('Regular user access to permitted API', 'passed', 200);
            });
        });

        it('should deny regular users access to admin-only API endpoints', () => {
            cy.request({
                url: '/api/admin/users',
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.be.oneOf([401, 403]);
                TestReporter.recordTestResult('Regular user denied admin API access', 'passed', 200);
            });
        });
    });

    context('Admin User Access', () => {
        beforeEach(() => {
            cy.loginAs(adminEmail, adminPassword);
        });

        it('should allow admin users access to all UI routes', () => {
            cy.visit('/admin/security');
            cy.url().should('include', '/admin/security');
            cy.contains('Security Dashboard').should('be.visible');
            TestReporter.recordTestResult('Admin user access to all UI', 'passed', 200);
        });

        it('should allow admin users access to all API endpoints', () => {
            cy.request({
                url: '/api/admin/users',
                failOnStatusCode: false
            }).then((response) => {
                expect(response.status).to.eq(200);
                expect(Array.isArray(response.body.users)).to.be.true;
                TestReporter.recordTestResult('Admin user access to all API', 'passed', 200);
            });
        });
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
