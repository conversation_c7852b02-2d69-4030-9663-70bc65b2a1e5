// Role-Based Access Control Tests
// Tests sidebar menu visibility, dashboard tab visibility, and password reset functionality
describe('Role-Based Access Control - Dashboard Tab Visibility', () => {
    it('should show appropriate dashboard tabs for admin users', () => {
        // Login as admin using the test credentials that work with the system
        cy.visit('/login');
        cy.get('input[id="email"]').type('<EMAIL>');
        cy.get('input[id="password"]').type('password');
        cy.get('button[type="submit"]').click();
        // Wait for successful login
        cy.url().should('include', '/dashboard');
        // Navigate to dashboard
        cy.visit('/dashboard');
        // Check dashboard tab visibility for admin users
        cy.get('nav[aria-label="Tabs"], [role="tablist"]').then($nav => {
            const navText = $nav.text();
            // Basic tabs (visible to all)
            cy.wrap(navText).should('include', 'Overview');
            cy.wrap(navText).should('include', 'Notifications');
            // Admin-only tabs
            cy.wrap(navText).should('include', 'Servers');
            cy.wrap(navText).should('include', 'Analytics');
            cy.wrap(navText).should('include', 'Security');
            cy.wrap(navText).should('include', 'System');
        });
    });
    it('should show limited dashboard tabs for regular users', () => {
        // Login as regular user using the test credentials that work with the system
        cy.visit('/login');
        cy.get('input[id="email"]').type('<EMAIL>');
        cy.get('input[id="password"]').type('password');
        cy.get('button[type="submit"]').click();
        // Wait for successful login
        cy.url().should('include', '/dashboard');
        // Navigate to dashboard
        cy.visit('/dashboard');
        // Check dashboard tab visibility for regular users
        cy.get('nav[aria-label="Tabs"], [role="tablist"]').then($nav => {
            const navText = $nav.text();
            // Basic tabs (visible to all)
            cy.wrap(navText).should('include', 'Overview');
            cy.wrap(navText).should('include', 'Notifications');
            // Admin-only tabs should NOT be visible
            cy.wrap(navText).should('not.include', 'Servers');
            cy.wrap(navText).should('not.include', 'Analytics');
            cy.wrap(navText).should('not.include', 'Security');
            cy.wrap(navText).should('not.include', 'System');
        });
    });
});
