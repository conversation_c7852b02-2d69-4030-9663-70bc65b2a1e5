/// <reference types="cypress" />
import { selectors } from '../../../support/selectors';

describe('Dashboard - General', () => {
    beforeEach(() => {
        cy.loginAs(Cypress.env('testUser'), Cypress.env('testPassword'));
        cy.visit('/dashboard');
    });

    it('loads dashboard page and displays key elements', () => {
        cy.get(selectors.dashboard.page).should('be.visible');
        cy.get(selectors.dashboard.heading).should('contain', 'Member Dashboard');
        cy.get(selectors.dashboard.welcomeMessage).should('not.be.empty');
        cy.get(selectors.dashboard.quickStats).should('be.visible');
        cy.get(selectors.dashboard.recentActivity).should('be.visible');
    });

    it('allows navigation to different sections from dashboard', () => {
        cy.get(selectors.dashboard.treatyManagementLink).click();
        cy.url().should('include', '/treaty-management');
        cy.visit('/dashboard');
        cy.get(selectors.dashboard.userManagementLink).click();
        cy.url().should('include', '/user-management');
    });

    it('displays correct quick stats numbers', () => {
        cy.get(selectors.dashboard.activeTreatiesStat).should('not.equal', '0');
        cy.get(selectors.dashboard.pendingOrdinancesStat).should('not.equal', '0');
        cy.get(selectors.dashboard.activeUsersStat).should('not.equal', '0');
    });

    it('shows recent activities feed with items', () => {
        cy.get(selectors.dashboard.recentActivityFeed).children().should('have.length.greaterThan', 0);
    });
});

describe('Dashboard - Analytics', () => {
    beforeEach(() => {
        cy.loginAs(Cypress.env('testUser'), Cypress.env('testPassword'));
        cy.visit('/dashboard/analytics');
    });

    it('loads analytics page with charts and data', () => {
        cy.get(selectors.analytics.page).should('be.visible');
        cy.get(selectors.analytics.treatyStatusChart).should('be.visible');
        cy.get(selectors.analytics.userActivityChart).should('be.visible');
        cy.get(selectors.analytics.ordinanceAdoptionChart).should('be.visible');
    });

    it('interacts with treaty status chart', () => {
        cy.get(selectors.analytics.treatyStatusChart).find('canvas').should('be.visible');
        // Add interactions here if possible, e.g., clicking on a segment
    });

    it('interacts with user activity chart', () => {
        cy.get(selectors.analytics.userActivityChart).find('canvas').should('be.visible');
        // Add interactions here if possible
    });

    it('interacts with ordinance adoption chart', () => {
        cy.get(selectors.analytics.ordinanceAdoptionChart).find('canvas').should('be.visible');
        // Add interactions here if possible
    });
});
