/// <reference types="cypress" />
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = Cypress.env('testPassword') || 'password123';

const signOutViaRequest = () => {
    cy.request({
        method: 'POST',
        url: '/api/auth/signout',
        form: true,
        body: { callbackUrl: '/login' },
        failOnStatusCode: false,
    });
};

const loginAsAdmin = () => {
    cy.loginAs(adminEmail, adminPassword);
    cy.location('pathname', { timeout: 15000 }).should('eq', '/dashboard');
};

describe('User Management - Search User', () => {
    beforeEach(() => {
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
        cy.task('db:reset-password', { email: adminEmail, password: adminPassword });
        signOutViaRequest();
        cy.clearCookies();
        loginAsAdmin();
        cy.visit('/users');
        cy.location('pathname', { timeout: 15000 }).should('eq', '/users');
        cy.get(selectors.users.page, { timeout: 15000 }).should('be.visible');
    });

    afterEach(() => {
        signOutViaRequest();
        cy.clearCookies();
    });

    it('searches seeded users via the search tab filters', () => {
        cy.get(selectors.users.navItem('search')).click();
        cy.get('input#email').clear().type(adminEmail);
        cy.get(selectors.usersSearch.submit).click();
        cy.get(selectors.usersSearch.table, { timeout: 10000 })
            .should('contain.text', adminEmail);
        cy.get(selectors.usersSearch.clear).click();
    });
});
