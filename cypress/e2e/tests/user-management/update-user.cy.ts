/// <reference types="cypress" />
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = Cypress.env('testPassword') || 'password123';

const signOutViaRequest = () => {
    cy.request({
        method: 'POST',
        url: '/api/auth/signout',
        form: true,
        body: { callbackUrl: '/login' },
        failOnStatusCode: false,
    });
};

const loginAsAdmin = () => {
    cy.loginAs(adminEmail, adminPassword);
    cy.location('pathname', { timeout: 15000 }).should('eq', '/dashboard');
};

describe('User Management - Update User', () => {
    beforeEach(() => {
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
        cy.task('db:reset-password', { email: adminEmail, password: adminPassword });
        signOutViaRequest();
        cy.clearCookies();
        loginAsAdmin();
        cy.visit('/users');
        cy.location('pathname', { timeout: 15000 }).should('eq', '/users');
        cy.get(selectors.users.page, { timeout: 15000 }).should('be.visible');
    });

    afterEach(() => {
        signOutViaRequest();
        cy.clearCookies();
    });

    it('updates a seeded user profile and restores defaults afterward', () => {
        const updatedPhone = '******-0999';
        cy.get(selectors.users.navItem('update')).click();
        cy.intercept('GET', '/api/users?search*').as('searchUsers');
        cy.get(selectors.usersUpdate.searchInput).clear().type('<EMAIL>');
        cy.wait('@searchUsers');
        cy.get(selectors.usersUpdate.suggestions).find('[data-testid^="users-update-suggestion-"]').first().click();
        cy.get(selectors.usersUpdate.selectedHeader).should('contain', 'Update User');
        cy.get(selectors.usersUpdate.tab('contact')).click({ force: true });
        cy.get(selectors.usersUpdate.section('contact')).should('be.visible');
        cy.get(selectors.usersUpdate.section('contact'))
            .scrollIntoView({ offset: { top: -100, left: 0 } })
            .should('be.visible')
            .within(() => {
            cy.get('input#phone', { timeout: 10000 })
                .scrollIntoView({ offset: { top: -80, left: 0 } })
                .should('be.visible')
                .clear()
                .type(updatedPhone);
        });
        cy.intercept('PUT', '/api/users/*').as('updateUser');
        cy.get(selectors.usersUpdate.submit).click();
        cy.wait('@updateUser');
        cy.contains('User updated successfully!', { timeout: 10000 }).should('be.visible');
        cy.task('db:reset-user-profile', { email: adminEmail });
    });
});
