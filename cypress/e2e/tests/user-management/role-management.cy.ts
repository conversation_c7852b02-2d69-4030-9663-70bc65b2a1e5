/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';
import { navigationHelpers, formHelpers, assertionHelpers } from '../../../support/test-utils';
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = Cypress.env('testPassword') || 'password123';

describe('User Management - Role Management', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        // Ensure a clean state for roles
        cy.task('db:reset-roles');
        // Login as admin user
        cy.loginAs(adminEmail, adminPassword);
        cy.visit('/admin/roles');
    });

    afterEach(() => {
        // Clean up after each test
        cy.task('db:reset-roles');
        cy.logout();
    });

    it('should display existing roles', () => {
        // Mock existing roles
        cy.intercept('GET', '/api/admin/roles', {
            statusCode: 200,
            body: {
                success: true,
                roles: [
                    { id: '1', name: 'Admin', description: 'Administrator role', permissions: ['admin.*'] },
                    { id: '2', name: 'User', description: 'Standard user role', permissions: ['user.*'] }
                ]
            }
        }).as('getRoles');

        cy.reload(); // Reload to trigger intercept
        cy.wait('@getRoles');

        cy.get(selectors.roles.page).should('be.visible');
        cy.contains('Admin').should('be.visible');
        cy.contains('User').should('be.visible');
        TestReporter.recordTestResult('Existing roles displayed', 'passed', 300);
    });

    it('should allow creating a new role', () => {
        const newRoleName = 'Editor';
        const newRoleDescription = 'Content editor role';

        cy.intercept('POST', '/api/admin/roles', {
            statusCode: 201,
            body: {
                success: true,
                message: 'Role created successfully',
                role: { id: '3', name: newRoleName, description: newRoleDescription, permissions: ['content.edit'] }
            }
        }).as('createRole');

        cy.get(selectors.roles.createButton).click();
        cy.get(selectors.roles.nameField).type(newRoleName);
        cy.get(selectors.roles.descriptionField).type(newRoleDescription);
        cy.get(selectors.roles.permissionsField).type('content.edit');
        cy.get(selectors.roles.submitButton).click();

        cy.wait('@createRole');
        assertionHelpers.shouldContainText(selectors.roles.successMessage, 'Role created successfully');
        cy.contains(newRoleName).should('be.visible');
        TestReporter.recordTestResult('New role created successfully', 'passed', 500);
    });

    it('should allow editing an existing role', () => {
        const updatedRoleName = 'Super Admin';
        const updatedRoleDescription = 'Super Administrator role with all permissions';

        // Mock existing roles
        cy.intercept('GET', '/api/admin/roles', {
            statusCode: 200,
            body: {
                success: true,
                roles: [
                    { id: '1', name: 'Admin', description: 'Administrator role', permissions: ['admin.*'] }
                ]
            }
        }).as('getRoles');

        // Mock role update
        cy.intercept('PUT', '/api/admin/roles/1', {
            statusCode: 200,
            body: {
                success: true,
                message: 'Role updated successfully',
                role: { id: '1', name: updatedRoleName, description: updatedRoleDescription, permissions: ['admin.*', 'super.*'] }
            }
        }).as('updateRole');

        cy.reload(); // Reload to trigger intercept
        cy.wait('@getRoles');

        cy.get(selectors.roles.editButton('1')).click();
        cy.get(selectors.roles.nameField).clear().type(updatedRoleName);
        cy.get(selectors.roles.descriptionField).clear().type(updatedRoleDescription);
        cy.get(selectors.roles.permissionsField).type(',super.*'); // Add new permission
        cy.get(selectors.roles.submitButton).click();

        cy.wait('@updateRole');
        assertionHelpers.shouldContainText(selectors.roles.successMessage, 'Role updated successfully');
        cy.contains(updatedRoleName).should('be.visible');
        TestReporter.recordTestResult('Existing role updated successfully', 'passed', 700);
    });

    it('should allow deleting a role', () => {
        // Mock existing roles
        cy.intercept('GET', '/api/admin/roles', {
            statusCode: 200,
            body: {
                success: true,
                roles: [
                    { id: '1', name: 'Obsolete Role', description: 'Role to be deleted', permissions: [] }
                ]
            }
        }).as('getRoles');

        // Mock role deletion
        cy.intercept('DELETE', '/api/admin/roles/1', {
            statusCode: 200,
            body: {
                success: true,
                message: 'Role deleted successfully',
                roleId: '1'
            }
        }).as('deleteRole');

        cy.reload(); // Reload to trigger intercept
        cy.wait('@getRoles');

        cy.get(selectors.roles.deleteButton('1')).click();
        cy.get(selectors.roles.confirmDeleteButton).click();

        cy.wait('@deleteRole');
        assertionHelpers.shouldContainText(selectors.roles.successMessage, 'Role deleted successfully');
        cy.contains('Obsolete Role').should('not.exist');
        TestReporter.recordTestResult('Role deleted successfully', 'passed', 600);
    });

    it('should handle validation errors when creating/editing roles', () => {
        cy.intercept('POST', '/api/admin/roles', {
            statusCode: 400,
            body: {
                success: false,
                message: 'Role name is required'
            }
        }).as('createRoleError');

        cy.get(selectors.roles.createButton).click();
        cy.get(selectors.roles.submitButton).click(); // Submit empty form

        cy.wait('@createRoleError');
        assertionHelpers.shouldContainText(selectors.roles.errorMessage, 'Role name is required');
        TestReporter.recordTestResult('Role validation errors handled', 'passed', 400);
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
