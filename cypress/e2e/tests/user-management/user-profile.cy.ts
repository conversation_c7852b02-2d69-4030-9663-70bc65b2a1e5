/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';
import { navigationHelpers, formHelpers, assertionHelpers } from '../../../support/test-utils';
import { selectors } from '../../../support/selectors';

const regularUserEmail = '<EMAIL>';
const regularUserPassword = 'RegularPassword123!';

describe('User Management - User Profile', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        // Ensure a clean state for the regular user
        cy.task('db:reset-user', regularUserEmail);
        cy.task('db:reset-password', { email: regularUserEmail, password: regularUserPassword });
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', regularUserEmail);

        // Login as a regular user
        cy.loginAs(regularUserEmail, regularUserPassword);
        cy.visit('/profile');
    });

    afterEach(() => {
        // Clean up after each test
        cy.task('db:reset-user', regularUserEmail);
        cy.task('db:reset-password', { email: regularUserEmail, password: regularUserPassword });
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', regularUserEmail);
        cy.logout();
    });

    it('should display user profile information correctly', () => {
        cy.get(selectors.profile.page).should('be.visible');
        cy.get(selectors.profile.emailField).should('have.value', regularUserEmail);
        cy.get(selectors.profile.nameField).should('not.be.empty');
        cy.get(selectors.profile.bioField).should('exist');
        TestReporter.recordTestResult('User profile information displayed', 'passed', 300);
    });

    it('should allow updating basic profile information', () => {
        const newName = 'Updated Regular User';
        const newBio = 'This is an updated bio for the regular user.';

        cy.get(selectors.profile.nameField).clear().type(newName);
        cy.get(selectors.profile.bioField).clear().type(newBio);
        cy.get(selectors.profile.saveButton).click();

        assertionHelpers.shouldContainText(selectors.profile.successMessage, 'Profile updated successfully');
        cy.get(selectors.profile.nameField).should('have.value', newName);
        cy.get(selectors.profile.bioField).should('have.value', newBio);
        TestReporter.recordTestResult('Basic profile information updated', 'passed', 500);
    });

    it('should allow updating contact information', () => {
        const newPhone = '******-1234';
        const newAddress = '123 New Street, New City, NW';

        cy.get(selectors.profile.contactTab).click();
        cy.get(selectors.profile.phoneField).clear().type(newPhone);
        cy.get(selectors.profile.addressField).clear().type(newAddress);
        cy.get(selectors.profile.saveButton).click();

        assertionHelpers.shouldContainText(selectors.profile.successMessage, 'Contact information updated successfully');
        cy.get(selectors.profile.phoneField).should('have.value', newPhone);
        cy.get(selectors.profile.addressField).should('have.value', newAddress);
        TestReporter.recordTestResult('Contact information updated', 'passed', 500);
    });

    it('should allow changing password', () => {
        const newPassword = 'NewSecurePassword123!';

        cy.get(selectors.profile.securityTab).click();
        cy.get(selectors.profile.currentPasswordField).type(regularUserPassword);
        cy.get(selectors.profile.newPasswordField).type(newPassword);
        cy.get(selectors.profile.confirmNewPasswordField).type(newPassword);
        cy.get(selectors.profile.changePasswordButton).click();

        assertionHelpers.shouldContainText(selectors.profile.successMessage, 'Password changed successfully');
        // Verify re-login with new password
        cy.logout();
        cy.loginAs(regularUserEmail, newPassword);
        cy.url().should('include', '/dashboard');
        TestReporter.recordTestResult('Password changed successfully', 'passed', 700);
    });

    it('should handle validation errors when updating profile', () => {
        cy.get(selectors.profile.nameField).clear().type('A'); // Too short
        cy.get(selectors.profile.saveButton).click();
        assertionHelpers.shouldContainText(selectors.profile.errorMessage, 'Name must be at least 2 characters');
        TestReporter.recordTestResult('Profile update validation errors handled', 'passed', 400);
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
