/// <reference types="cypress" />
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = Cypress.env('testPassword') || 'password123';

const signOutViaRequest = () => {
    cy.request({
        method: 'POST',
        url: '/api/auth/signout',
        form: true,
        body: { callbackUrl: '/login' },
        failOnStatusCode: false,
    });
};

const loginAsAdmin = () => {
    cy.loginAs(adminEmail, adminPassword);
    cy.location('pathname', { timeout: 15000 }).should('eq', '/dashboard');
};

describe('User Management - Navigation', () => {
    beforeEach(() => {
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
        cy.task('db:reset-password', { email: adminEmail, password: adminPassword });
        signOutViaRequest();
        cy.clearCookies();
        loginAsAdmin();
        cy.visit('/users');
        cy.location('pathname', { timeout: 15000 }).should('eq', '/users');
        cy.get(selectors.users.page, { timeout: 15000 }).should('be.visible');
    });

    afterEach(() => {
        signOutViaRequest();
        cy.clearCookies();
    });

    it('renders navigation menu and defaults to Create User tab', () => {
        cy.get(selectors.users.nav).should('be.visible');
        cy.get(selectors.usersCreate.tabList).should('exist');
        const tabOrder = [
            ['create', 'Create User'],
            ['treaty', 'User Treaty'],
            ['update', 'Update User'],
            ['search', 'Search User'],
        ];
        tabOrder.forEach(([tabId, label]) => {
            cy.get(selectors.users.navItem(tabId)).should('be.visible').and('contain', label);
        });
        cy.get(selectors.users.contentPanel('create')).should('be.visible');
        cy.contains('h2', 'Create User').should('be.visible');
        cy.get(selectors.usersCreate.section('user')).should('be.visible');
    });

    it('switches between each management tab using seeded data views', () => {
        const sections = [
            ['create', 'Create User'],
            ['treaty', 'User Treaty'],
            ['update', 'Update User'],
            ['search', 'Search User'],
        ];
        sections.forEach(([tabId, heading]) => {
            cy.get(selectors.users.navItem(tabId)).click();
            cy.get(selectors.users.contentPanel(tabId)).should('be.visible');
            cy.contains('h2', heading).should('be.visible');
        });
    });
});
