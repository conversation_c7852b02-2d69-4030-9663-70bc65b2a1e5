/// <reference types="cypress" />
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = Cypress.env('testPassword') || 'password123';

const signOutViaRequest = () => {
    cy.request({
        method: 'POST',
        url: '/api/auth/signout',
        form: true,
        body: { callbackUrl: '/login' },
        failOnStatusCode: false,
    });
};

const loginAsAdmin = () => {
    cy.loginAs(adminEmail, adminPassword);
    cy.location('pathname', { timeout: 15000 }).should('eq', '/dashboard');
};

describe('User Management - Create User', () => {
    beforeEach(() => {
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', adminEmail);
        cy.task('db:reset-password', { email: adminEmail, password: adminPassword });
        signOutViaRequest();
        cy.clearCookies();
        loginAsAdmin();
        cy.visit('/users');
        cy.location('pathname', { timeout: 15000 }).should('eq', '/users');
        cy.get(selectors.users.page, { timeout: 15000 }).should('be.visible');
    });

    afterEach(() => {
        signOutViaRequest();
        cy.clearCookies();
    });

    it('cycles through create-user subtabs', () => {
        const createSubtabs = ['user', 'contact', 'identification', 'positions', 'ordinance', 'treaty'];
        cy.get(selectors.users.navItem('create')).click();
        cy.get(selectors.users.contentPanel('create')).should('be.visible');
        createSubtabs.forEach((tabId) => {
            cy.get(selectors.usersCreate.tab(tabId)).then(($tab) => {
                const isDisabled = $tab.attr('aria-disabled') === 'true' || $tab.is(':disabled');
                if (isDisabled) {
                    cy.wrap($tab).should('have.attr', 'aria-disabled', 'true');
                    return;
                }
                cy.wrap($tab).click();
                cy.get(selectors.usersCreate.section(tabId)).should('be.visible');
                if (tabId === 'user') {
                    cy.get(selectors.usersCreateBasic.form).within(() => {
                        cy.get(selectors.usersCreateBasic.firstName).should('exist');
                        cy.get(selectors.usersCreateBasic.submit).should('exist');
                    });
                }
                if (tabId === 'contact') {
                    cy.get(selectors.usersCreateContact.form).within(() => {
                        cy.get(selectors.usersCreateContact.field('streetName')).should('exist');
                        cy.get(selectors.usersCreateContact.submit).should('exist');
                    });
                }
                if (tabId === 'identification') {
                    cy.get(selectors.usersCreateIdentification.form).within(() => {
                        cy.get(selectors.usersCreateIdentification.nationSearch).should('exist');
                        cy.get(selectors.usersCreateIdentification.submit).should('exist');
                    });
                }
                if (tabId === 'positions') {
                    cy.get(selectors.usersCreatePositions.form).within(() => {
                        cy.get(selectors.usersCreatePositions.titleSelect).should('exist');
                        cy.get(selectors.usersCreatePositions.submit).should('exist');
                    });
                }
                if (tabId === 'ordinance') {
                    cy.get(selectors.usersCreateOrdinance.form).within(() => {
                        cy.get(selectors.usersCreateOrdinance.select).should('exist');
                        cy.get(selectors.usersCreateOrdinance.submit).should('exist');
                    });
                }
                if (tabId === 'treaty') {
                    cy.get(selectors.usersCreateTreaty.form).within(() => {
                        cy.get(selectors.usersCreateTreaty.selectTreaty).should('exist');
                        cy.get(selectors.usersCreateTreaty.submit).should('exist');
                    });
                }
            });
        });
    });

    it('completes the create-user wizard with deterministic fixture data', () => {
        const timestamp = Date.now();
        const firstName = `Cypress${timestamp}`;
        const surname = 'Wizard';
        cy.wrap(null).as('selectedCity');
        cy.get(selectors.users.navItem('create')).click();
        // Basic information
        cy.get(selectors.usersCreateBasic.firstName).clear().type(firstName);
        cy.get(selectors.usersCreateBasic.surname).clear().type(surname);
        cy.get(selectors.usersCreateBasic.personalEmail).type(`cypress.${timestamp}@example.org`);
        cy.get(selectors.usersCreateBasic.phone).type('******-0100');
        cy.get(selectors.usersCreateBasic.submit).click();
        cy.get(selectors.usersCreate.section('contact')).should('be.visible');
        cy.window().its('__nwaCreateWizardStep').should('eq', 'basic-saved');
        // Contact information (requires street + country)
        cy.get(selectors.usersCreateContact.field('streetName')).clear().type('123 Cypress Avenue');
        cy.get(selectors.usersCreateContact.field('aptSuiteUnit')).clear().type('Suite 200');
        cy.intercept('GET', '/api/cities?all=true*').as('searchCities');
        cy.get(selectors.usersCreateContact.field('townCity')).clear().type('Austin');
        cy.wait('@searchCities').then(({ response }) => {
            const body = response?.body;
            const cityCandidates = Array.isArray(body?.cities)
                ? body.cities
                : Array.isArray(body?.body?.cities)
                    ? body.body.cities
                    : [];
            if (cityCandidates.length > 0) {
                const firstOption = cityCandidates[0];
                const optionSelector = `[data-testid="users-create-contact-city-option-${firstOption.id}"]`;
                cy.wrap(firstOption).as('selectedCity');
                cy.get('body').then(($body) => {
                    const options = $body.find(optionSelector);
                    if (options.length > 0) {
                        cy.wrap(options.first()).click();
                    }
                    else {
                        cy.get(selectors.usersCreateContact.field('townCity'))
                            .should('have.value', firstOption.name);
                    }
                });
            }
            else {
                // Autocomplete may have auto-selected the only match already.
                cy.get(selectors.usersCreateContact.field('townCity'))
                    .should('have.value', 'Austin');
            }
        });
        cy.get('@selectedCity').then((city) => {
            const expectedCountryName = city?.country?.name ?? city?.countryName;
            if (expectedCountryName) {
                cy.get(selectors.usersCreateContact.countryInput)
                    .should('have.value', expectedCountryName);
            }
            else {
                cy.get(selectors.usersCreateContact.countryInput)
                    .should('not.have.value', '');
            }
        });
        cy.get(selectors.usersCreateContact.submit).click();
        cy.get(selectors.usersCreate.section('identification')).should('be.visible');
        cy.window().its('__nwaCreateWizardStep').should('eq', 'contact-saved');
        // Identification (use defaults)
        cy.get(selectors.usersCreateIdentification.submit).click();
        cy.get(selectors.usersCreate.section('positions')).should('be.visible');
        cy.window().its('__nwaCreateWizardStep').should('eq', 'identification-saved');
        // Positions
        cy.get(selectors.usersCreatePositions.submit).click();
        cy.window().its('__nwaCreateWizardStep').should('eq', 'positions-saved');
        // Treaties (optional)
        cy.get(selectors.usersCreate.tab('treaty')).then(($tab) => {
            const isDisabled = $tab.attr('aria-disabled') === 'true' || $tab.is(':disabled');
            if (isDisabled) {
                cy.wrap($tab).should('have.attr', 'aria-disabled', 'true');
                cy.get(selectors.usersCreate.section('ordinance')).should('be.visible');
                cy.window().its('__nwaCreateWizardStep').should('eq', 'positions-saved');
            }
            else {
                cy.wrap($tab).click();
                cy.get(selectors.usersCreateTreaty.submit).click();
                cy.get(selectors.usersCreate.section('treaty')).should('be.visible');
                cy.get(selectors.usersCreate.section('ordinance')).should('be.visible');
                cy.window().its('__nwaCreateWizardStep').should('eq', 'treaty-saved');
            }
        });
        // Ordinances / final confirmation
        cy.get(selectors.usersCreate.tab('ordinance')).click();
        cy.get(selectors.usersCreateOrdinance.submit).click();
        cy.contains('body', 'User created successfully!', { timeout: 10000 });
        cy.window().its('__nwaCreateWizardStep').should('eq', 'completed');
    });
});
