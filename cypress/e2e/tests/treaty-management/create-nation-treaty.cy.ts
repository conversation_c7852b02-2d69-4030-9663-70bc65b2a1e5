/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';
import { navigationHelpers, formHelpers, assertionHelpers } from '../../../support/test-utils';
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = Cypress.env('testPassword') || 'password123';

describe('Treaty Management - Create Nation Treaty', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        // Ensure a clean state for treaties
        cy.task('db:reset-treaties');
        // Login as admin user
        cy.loginAs(adminEmail, adminPassword);
        cy.visit('/nation-treaties/create');
    });

    afterEach(() => {
        // Clean up after each test
        cy.task('db:reset-treaties');
        cy.logout();
    });

    it('should display the create nation treaty form', () => {
        cy.get(selectors.createTreaty.page).should('be.visible');
        cy.get(selectors.createTreaty.titleField).should('be.visible');
        cy.get(selectors.createTreaty.descriptionField).should('be.visible');
        cy.get(selectors.createTreaty.treatyTypeSelect).should('be.visible');
        cy.get(selectors.createTreaty.submitButton).should('be.visible');
        TestReporter.recordTestResult('Create nation treaty form displayed', 'passed', 300);
    });

    it('should allow creating a new nation treaty successfully', () => {
        const treatyTitle = 'Cypress Test Treaty';
        const treatyDescription = 'This is a test treaty created by Cypress.';
        const treatyTypeId = '1'; // Assuming a default treaty type exists

        // Mock treaty type selection
        cy.intercept('GET', '/api/treaty-types', {
            statusCode: 200,
            body: {
                success: true,
                treatyTypes: [
                    { id: treatyTypeId, name: 'Alliance', description: 'Military Alliance' }
                ]
            }
        }).as('getTreatyTypes');

        // Mock treaty creation
        cy.intercept('POST', '/api/nation-treaties', {
            statusCode: 201,
            body: {
                success: true,
                message: 'Nation treaty created successfully',
                treaty: {
                    id: 'test-treaty-id',
                    title: treatyTitle,
                    description: treatyDescription,
                    treatyTypeId: treatyTypeId,
                    status: 'draft'
                }
            }
        }).as('createTreaty');

        cy.wait('@getTreatyTypes');
        cy.get(selectors.createTreaty.titleField).type(treatyTitle);
        cy.get(selectors.createTreaty.descriptionField).type(treatyDescription);
        cy.get(selectors.createTreaty.treatyTypeSelect).select(treatyTypeId);
        cy.get(selectors.createTreaty.submitButton).click();

        cy.wait('@createTreaty');
        assertionHelpers.shouldContainText(selectors.createTreaty.successMessage, 'Nation treaty created successfully');
        cy.url().should('include', '/nation-treaties/test-treaty-id'); // Assuming redirect to treaty details page
        TestReporter.recordTestResult('New nation treaty created successfully', 'passed', 800);
    });

    it('should handle validation errors when creating a treaty', () => {
        cy.intercept('POST', '/api/nation-treaties', {
            statusCode: 400,
            body: {
                success: false,
                message: 'Treaty title is required'
            }
        }).as('createTreatyError');

        cy.get(selectors.createTreaty.submitButton).click(); // Submit empty form

        cy.wait('@createTreatyError');
        assertionHelpers.shouldContainText(selectors.createTreaty.errorMessage, 'Treaty title is required');
        TestReporter.recordTestResult('Treaty creation validation errors handled', 'passed', 500);
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
