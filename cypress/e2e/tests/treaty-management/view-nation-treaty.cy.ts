/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';
import { navigationHelpers, formHelpers, assertionHelpers } from '../../../support/test-utils';
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = Cypress.env('testPassword') || 'password123';

describe('Treaty Management - View Nation Treaty', () => {
    const treatyId = 'test-treaty-id-123';
    const treatyTypeId = 'test-treaty-type-id-456';
    const userId = 'test-user-id-789';

    beforeEach(() => {
        TestReporter.clearResults();
        // Ensure a clean state for treaties
        cy.task('db:reset-treaties');
        // Login as admin user
        cy.loginAs(adminEmail, adminPassword);
        cy.visit(`/nation-treaties/${userId}/${treatyTypeId}`);
    });

    afterEach(() => {
        // Clean up after each test
        cy.task('db:reset-treaties');
        cy.logout();
    });

    it('should display nation treaty details correctly', () => {
        // Mock treaty details API call
        cy.intercept('GET', `/api/nation-treaties/${userId}/${treatyTypeId}`, {
            statusCode: 200,
            body: {
                success: true,
                treaty: {
                    id: treatyId,
                    title: 'Test Nation Treaty',
                    description: 'Description for test nation treaty.',
                    status: 'active',
                    treatyType: { id: treatyTypeId, name: 'Alliance' },
                    nation: { id: userId, name: 'Test Nation' },
                    documents: [{ id: 'doc1', name: 'Treaty Document 1.pdf' }],
                    assignedUsers: [{ id: 'user1', name: 'John Doe' }]
                }
            }
        }).as('getTreatyDetails');

        cy.reload(); // Reload to trigger intercept
        cy.wait('@getTreatyDetails');

        cy.get(selectors.viewTreaty.page).should('be.visible');
        cy.contains('Test Nation Treaty').should('be.visible');
        cy.contains('Description for test nation treaty.').should('be.visible');
        cy.contains('Status: active').should('be.visible');
        cy.contains('Treaty Type: Alliance').should('be.visible');
        cy.contains('Nation: Test Nation').should('be.visible');
        cy.contains('Treaty Document 1.pdf').should('be.visible');
        cy.contains('Assigned Users: John Doe').should('be.visible');
        TestReporter.recordTestResult('Nation treaty details displayed', 'passed', 500);
    });

    it('should allow editing treaty details', () => {
        const updatedDescription = 'Updated description for test nation treaty.';

        // Mock treaty details API call
        cy.intercept('GET', `/api/nation-treaties/${userId}/${treatyTypeId}`, {
            statusCode: 200,
            body: {
                success: true,
                treaty: {
                    id: treatyId,
                    title: 'Test Nation Treaty',
                    description: 'Description for test nation treaty.',
                    status: 'active',
                    treatyType: { id: treatyTypeId, name: 'Alliance' },
                    nation: { id: userId, name: 'Test Nation' },
                    documents: [],
                    assignedUsers: []
                }
            }
        }).as('getTreatyDetails');

        // Mock treaty update API call
        cy.intercept('PUT', `/api/nation-treaties/${userId}/${treatyTypeId}`, {
            statusCode: 200,
            body: {
                success: true,
                message: 'Treaty updated successfully'
            }
        }).as('updateTreaty');

        cy.reload(); // Reload to trigger intercept
        cy.wait('@getTreatyDetails');

        cy.get(selectors.viewTreaty.editButton).click();
        cy.get(selectors.viewTreaty.descriptionField).clear().type(updatedDescription);
        cy.get(selectors.viewTreaty.saveButton).click();

        cy.wait('@updateTreaty');
        assertionHelpers.shouldContainText(selectors.viewTreaty.successMessage, 'Treaty updated successfully');
        cy.contains(updatedDescription).should('be.visible');
        TestReporter.recordTestResult('Treaty details updated successfully', 'passed', 700);
    });

    it('should allow uploading new documents', () => {
        // Mock treaty details API call
        cy.intercept('GET', `/api/nation-treaties/${userId}/${treatyTypeId}`, {
            statusCode: 200,
            body: {
                success: true,
                treaty: {
                    id: treatyId,
                    title: 'Test Nation Treaty',
                    description: 'Description for test nation treaty.',
                    status: 'active',
                    treatyType: { id: treatyTypeId, name: 'Alliance' },
                    nation: { id: userId, name: 'Test Nation' },
                    documents: [],
                    assignedUsers: []
                }
            }
        }).as('getTreatyDetails');

        // Mock document upload API call
        cy.intercept('POST', `/api/nation-treaties/${userId}/${treatyTypeId}/documents`, {
            statusCode: 201,
            body: {
                success: true,
                message: 'Document uploaded successfully'
            }
        }).as('uploadDocument');

        cy.reload(); // Reload to trigger intercept
        cy.wait('@getTreatyDetails');

        cy.get(selectors.viewTreaty.uploadDocumentButton).click();
        // Assuming a file input is available after clicking the button
        // cy.get('input[type="file"]').selectFile('cypress/fixtures/test-document.pdf');
        cy.get(selectors.viewTreaty.uploadSubmitButton).click();

        cy.wait('@uploadDocument');
        assertionHelpers.shouldContainText(selectors.viewTreaty.successMessage, 'Document uploaded successfully');
        TestReporter.recordTestResult('New document uploaded successfully', 'passed', 600);
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
