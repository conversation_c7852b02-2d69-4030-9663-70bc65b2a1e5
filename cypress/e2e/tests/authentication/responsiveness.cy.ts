// Comprehensive Authentication System Verification Tests
// Verifies all authentication components work together correctly
import { TestDataFactory } from '../../../support/test-data-factory';
import { TestReporter } from '../../../support/test-reporter';
import { formHelpers, navigationHelpers, assertionHelpers } from '../../../support/test-utils';

describe('Authentication System - Responsiveness', () => {
    beforeEach(() => {
        TestReporter.clearResults();
    });

    it('should verify authentication responsiveness', () => {
        const responsiveUser = TestDataFactory.createUser({
            email: '<EMAIL>',
            password: 'ResponsivePassword123!'
        });
        const viewports = [
            { width: 320, height: 568, name: 'Mobile' },
            { width: 768, height: 1024, name: 'Tablet' },
            { width: 1280, height: 720, name: 'Desktop' }
        ];
        viewports.forEach((viewport, index) => {
            cy.viewport(viewport.width, viewport.height);
            // Mock responsive login
            cy.intercept('POST', '/api/auth/**', (req) => {
                req.reply({
                    statusCode: 200,
                    body: {
                        success: true,
                        message: 'Login successful',
                        user: {
                            id: responsiveUser.id,
                            name: responsiveUser.name,
                            email: responsiveUser.email,
                            role: 'user'
                        },
                        token: `mock-jwt-token-${viewport.name.toLowerCase()}`
                    }
                });
            }).as(`responsiveLogin${index}`);
            navigationHelpers.goToLogin();
            formHelpers.fillLoginForm(responsiveUser.email, responsiveUser.password);
            cy.wait(`@responsiveLogin${index}`);
            // Check responsive layout
            cy.get('body').then($body => {
                const isResponsive = $body[0].scrollWidth <= $body[0].clientWidth + 10;
                if (isResponsive) {
                    cy.log(`${viewport.name} layout is responsive`);
                }
            });
            // Check form elements are accessible
            cy.get('input[type="email"]').should('be.visible');
            cy.get('input[type="password"]').should('be.visible');
            cy.get('button[type="submit"]').should('be.visible');
        });
        TestReporter.recordTestResult('Authentication responsiveness verified', 'passed', 1200);
    });

    after(() => {
        // Generate comprehensive test report
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
        cy.log('🎉 Authentication responsiveness verification completed successfully!');
    });
});
