// Comprehensive Authentication System Verification Tests
// Verifies all authentication components work together correctly
import { TestDataFactory } from '../../../support/test-data-factory';
import { TestReporter } from '../../../support/test-reporter';
import { formHelpers, navigationHelpers, assertionHelpers } from '../../../support/test-utils';

describe('Authentication System - Performance', () => {
    beforeEach(() => {
        TestReporter.clearResults();
    });

    it('should verify authentication performance', () => {
        const performanceUser = TestDataFactory.createUser({
            email: '<EMAIL>',
            password: 'PerformancePassword123!'
        });
        // Mock fast authentication response
        cy.intercept('POST', '/api/auth/**', (req) => {
            req.reply({
                statusCode: 200,
                body: {
                    success: true,
                    message: 'Login successful',
                    user: {
                        id: performanceUser.id,
                        name: performanceUser.name,
                        email: performanceUser.email,
                        role: 'user'
                    },
                    token: 'mock-jwt-token-performance'
                }
            });
        }).as('performanceLogin');
        // Mock session validation
        cy.intercept('GET', '/api/auth/session', (req) => {
            req.reply({
                statusCode: 200,
                body: {
                    valid: true,
                    user: {
                        id: performanceUser.id,
                        name: performanceUser.name,
                        email: performanceUser.email,
                        role: 'user'
                    }
                }
            });
        }).as('performanceSession');
        const startTime = performance.now();
        navigationHelpers.goToLogin();
        formHelpers.fillLoginForm(performanceUser.email, performanceUser.password);
        cy.wait('@performanceLogin');
        const loginTime = performance.now() - startTime;
        // Login should be fast (< 2 seconds)
        expect(loginTime).to.be.lessThan(2000);
        // Test session validation performance
        cy.visit('/dashboard');
        cy.wait('@performanceSession');
        const sessionTime = performance.now() - loginTime;
        // Session validation should also be fast
        expect(sessionTime).to.be.lessThan(1000);
        cy.log(`Login time: ${Math.round(loginTime)}ms`);
        cy.log(`Session validation time: ${Math.round(sessionTime)}ms`);
        TestReporter.recordTestResult('Authentication performance within acceptable limits', 'passed', Math.round(loginTime + sessionTime));
    });

    after(() => {
        // Generate comprehensive test report
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
        cy.log('🎉 Authentication performance verification completed successfully!');
    });
});
