/// <reference types="cypress" />
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = Cypress.env('testPassword') || 'password123';
const defaultPassword = adminPassword;
const newPassword = 'NewPass123!';

const signOutViaRequest = () => {
    cy.request({
        method: 'POST',
        url: '/api/auth/signout',
        form: true,
        body: { callbackUrl: '/' },
        failOnStatusCode: false,
    });
};

describe('Authentication - Password Reset', () => {
    beforeEach(() => {
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-password', { email: adminEmail, password: defaultPassword });
        signOutViaRequest();
        cy.clearCookies();
    });

    afterEach(() => {
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-password', { email: adminEmail, password: defaultPassword });
        signOutViaRequest();
    });

    it('allows password reset via token and login with new password', () => {
        cy.task('db:create-password-reset-token', adminEmail).then((token) => {
            expect(token, 'reset token created').to.be.a('string');
            if (!token) {
                throw new Error('No reset token was generated');
            }
            cy.visit(`/reset-password?token=${token}`);
            cy.get('input#password').clear().type(newPassword);
            cy.get('input#confirmPassword').clear().type(newPassword);
            cy.get('button[type="submit"]').click();
            cy.contains('Password reset successfully', { timeout: 10000 }).should('be.visible');
            cy.url({ timeout: 10000 }).should('include', '/');
            cy.loginAs(adminEmail, newPassword);
            cy.url({ timeout: 10000 }).should('include', '/dashboard');
            cy.logout();
            cy.url({ timeout: 10000 }).should('include', '/');
            // Restore default password explicitly
            cy.task('db:reset-password', { email: adminEmail, password: defaultPassword });
        });
    });
});
