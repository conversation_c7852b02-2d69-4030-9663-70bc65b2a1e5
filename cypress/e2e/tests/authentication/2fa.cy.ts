/// <reference types="cypress" />
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = Cypress.env('testPassword') || 'password123';
const twoFactorEmail = '<EMAIL>';
const defaultPassword = adminPassword;

const signOutViaRequest = () => {
    cy.request({
        method: 'POST',
        url: '/api/auth/signout',
        form: true,
        body: { callbackUrl: '/' },
        failOnStatusCode: false,
    });
};

describe('Authentication - 2FA', () => {
    beforeEach(() => {
        // Ensure defaults before each scenario
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', twoFactorEmail);
        cy.task('db:reset-password', { email: adminEmail, password: defaultPassword });
        signOutViaRequest();
        cy.clearCookies();
        cy.visit('/');
    });

    afterEach(() => {
        // Restore defaults after each scenario
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', twoFactorEmail);
        cy.task('db:reset-password', { email: adminEmail, password: defaultPassword });
        signOutViaRequest();
    });

    it('forces two-factor setup when globally required', () => {
        cy.task('db:set-twofactor-required', true);
        cy.task('db:reset-twofactor', twoFactorEmail);
        cy.visit('/');
        cy.get(selectors.auth.loginEmail).clear().type(twoFactorEmail);
        cy.get(selectors.auth.loginPassword).clear().type(defaultPassword);
        cy.get(selectors.auth.loginSubmit).click();
        cy.location('pathname', { timeout: 10000 }).should('eq', '/setup-2fa');
        cy.get(selectors.auth.setup2faQr).should('be.visible');
    });
});
