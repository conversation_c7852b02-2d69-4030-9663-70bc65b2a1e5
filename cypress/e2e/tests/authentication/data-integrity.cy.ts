// Comprehensive Authentication System Verification Tests
// Verifies all authentication components work together correctly
import { TestDataFactory } from '../../../support/test-data-factory';
import { TestReporter } from '../../../support/test-reporter';
import { formHelpers, navigationHelpers, assertionHelpers } from '../../../support/test-utils';

describe('Authentication System - Data Integrity', () => {
    beforeEach(() => {
        TestReporter.clearResults();
    });

    it('should verify authentication data integrity', () => {
        const integrityUser = TestDataFactory.createUser({
            email: '<EMAIL>',
            password: 'IntegrityPassword123!',
            name: 'Data Integrity User'
        });
        // Mock login with data integrity checks
        cy.intercept('POST', '/api/auth/**', (req) => {
            req.reply({
                statusCode: 200,
                body: {
                    success: true,
                    message: 'Login successful',
                    user: {
                        id: integrityUser.id,
                        name: integrityUser.name,
                        email: integrityUser.email,
                        role: 'user',
                        permissions: ['read'],
                        lastLogin: new Date().toISOString(),
                        loginCount: 1
                    },
                    token: 'mock-jwt-token-integrity',
                    checksum: 'data-integrity-checksum-12345'
                }
            });
        }).as('integrityLogin');
        // Mock session validation with integrity check
        cy.intercept('GET', '/api/auth/session', (req) => {
            req.reply({
                statusCode: 200,
                body: {
                    valid: true,
                    user: {
                        id: integrityUser.id,
                        name: integrityUser.name,
                        email: integrityUser.email,
                        role: 'user'
                    },
                    dataIntegrity: true,
                    timestamp: new Date().toISOString()
                }
            });
        }).as('integritySession');
        navigationHelpers.goToLogin();
        formHelpers.fillLoginForm(integrityUser.email, integrityUser.password);
        cy.wait('@integrityLogin');
        // Verify data integrity
        cy.get('@integrityLogin').its('response.body').then((response) => {
            expect(response.success).to.be.true;
            expect(response.user.email).to.equal(integrityUser.email);
            expect(response.user.name).to.equal(integrityUser.name);
            expect(response).to.have.property('checksum');
        });
        // Test session data integrity
        cy.visit('/dashboard');
        cy.wait('@integritySession');
        cy.get('@integritySession').its('response.body').then((response) => {
            expect(response.valid).to.be.true;
            expect(response.dataIntegrity).to.be.true;
            expect(response).to.have.property('timestamp');
        });
        TestReporter.recordTestResult('Authentication data integrity verified', 'passed', 600);
    });

    after(() => {
        // Generate comprehensive test report
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
        cy.log('🎉 Authentication data integrity verification completed successfully!');
    });
});
