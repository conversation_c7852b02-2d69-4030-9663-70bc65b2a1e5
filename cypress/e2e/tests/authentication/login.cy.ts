/// <reference types="cypress" />
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = Cypress.env('testPassword') || 'password123';
const twoFactorEmail = '<EMAIL>';
const defaultPassword = adminPassword;

const signOutViaRequest = () => {
    cy.request({
        method: 'POST',
        url: '/api/auth/signout',
        form: true,
        body: { callbackUrl: '/' },
        failOnStatusCode: false,
    });
};

describe('Authentication - Login', () => {
    beforeEach(() => {
        // Ensure defaults before each scenario
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', twoFactorEmail);
        cy.task('db:reset-password', { email: adminEmail, password: defaultPassword });
        signOutViaRequest();
        cy.clearCookies();
        cy.visit('/');
    });

    afterEach(() => {
        // Restore defaults after each scenario
        cy.task('db:set-twofactor-required', false);
        cy.task('db:reset-twofactor', twoFactorEmail);
        cy.task('db:reset-password', { email: adminEmail, password: defaultPassword });
        signOutViaRequest();
    });

    it('should display login form', () => {
        cy.getByTestId('login-form').should('be.visible');
        cy.getByTestId('login-email').should('be.visible');
        cy.getByTestId('login-password').should('be.visible');
        cy.getByTestId('login-submit').should('be.visible');
    });

    it('should allow typing in email and password fields', () => {
        cy.getByTestId('login-email').type('<EMAIL>');
        cy.getByTestId('login-email').should('have.value', '<EMAIL>');

        cy.getByTestId('login-password').type('password123');
        cy.getByTestId('login-password').should('have.value', 'password123');
    });

    it('should toggle password visibility', () => {
        cy.getByTestId('login-password').should('have.attr', 'type', 'password');
        cy.getByTestId('login-password').type('password123');

        // Click the eye button to toggle visibility
        cy.getByTestId('password-toggle').click();
        cy.getByTestId('login-password').should('have.attr', 'type', 'text');

        // Click again to hide
        cy.getByTestId('password-toggle').click();
        cy.getByTestId('login-password').should('have.attr', 'type', 'password');
    });

    it('should submit login form and logs in and logs out successfully', () => {
        cy.get(selectors.auth.loginEmail).clear().type(adminEmail);
        cy.get(selectors.auth.loginPassword).clear().type(adminPassword);
        cy.get(selectors.auth.loginSubmit).click();
        cy.url({ timeout: 10000 }).should('include', '/dashboard');
        cy.get(selectors.layout.sidebar).should('be.visible');
        cy.logout();
        cy.url({ timeout: 10000 }).should('include', '/');
        // Ensure session is cleared by visiting a protected route
        cy.visit('/dashboard');
        cy.location('pathname', { timeout: 10000 }).should('eq', '/login');
    });

    it('shows error message when credentials are invalid', () => {
        cy.get(selectors.auth.loginEmail).clear().type('<EMAIL>');
        cy.get(selectors.auth.loginPassword).clear().type('WrongPassword!');
        cy.get(selectors.auth.loginSubmit).click();
        cy.get(selectors.auth.loginError).should('contain', 'Invalid email or password');
        cy.location('pathname').should('eq', '/');
    });
});