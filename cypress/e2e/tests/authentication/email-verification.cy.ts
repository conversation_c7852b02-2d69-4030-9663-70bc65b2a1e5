/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';
import { navigationHelpers, assertionHelpers } from '../../../support/test-utils';

describe('Authentication - Email Verification', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        cy.visit('/');
    });

    it('should successfully verify email with a valid token', () => {
        const userEmail = '<EMAIL>'; // Use existing test user

        // Create email verification token for the user
        cy.task('db:create-email-verification-token', userEmail).then((token) => {
            expect(token, 'verification token created').to.be.a('string');
            if (!token) {
                throw new Error('No verification token was generated');
            }

            // Visit the verification page with the token (no need to mock - let it use the real API)
            cy.visit(`/verify-email?token=${token}`);

            // Assert success message
            cy.contains('<PERSON><PERSON> Verified!').should('be.visible');
            cy.contains('<PERSON><PERSON> verified successfully! You can now log in to your account.').should('be.visible');
            // Verify the Go to Login button is visible
            cy.contains('Go to Login').should('be.visible');
        });
        TestReporter.recordTestResult('Email verification with valid token', 'passed', 800);
    });

    it('should show an error with an invalid or expired token', () => {
        // Visit the verification page with an invalid token (no mocking - let it use the real API)
        cy.visit('/verify-email?token=invalid-token-123');

        // Assert error message
        cy.contains('Verification Failed').should('be.visible');
        cy.contains('Invalid or expired token').should('be.visible');
        TestReporter.recordTestResult('Email verification with invalid token', 'passed', 500);
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
