/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';
import { navigationHelpers, formHelpers, assertionHelpers } from '../../../support/test-utils';
import { selectors } from '../../../support/selectors';

const regularUserEmail = '<EMAIL>';
const regularUserPassword = 'RegularPassword123!';

describe('General - Categories Page', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        // Login as a regular user
        cy.loginAs(regularUserEmail, regularUserPassword);
        cy.visit('/categories');
    });

    afterEach(() => {
        cy.logout();
    });

    it('should display a list of categories with subcategories, search and pagination', () => {
        // Mock categories API call
        cy.intercept('GET', '/api/categories?*', (req) => {
            const query = req.query.q || '';
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 20;

            const mockCategories = Array.from({ length: 50 }, (_, i) => ({
                id: i + 1,
                name: `Category ${i + 1} ${query ? `(${query})` : ''}`,
                description: `Description for category ${i + 1}`,
                subcategories: [
                    { id: i + 100, name: `Sub ${i + 1}-1`, description: null },
                    { id: i + 101, name: `Sub ${i + 1}-2`, description: null }
                ]
            }));

            const filteredCategories = query
                ? mockCategories.filter(c => c.name.includes(query as string))
                : mockCategories;

            const totalPages = Math.ceil(filteredCategories.length / limit);
            const paginatedCategories = filteredCategories.slice((page - 1) * limit, page * limit);

            req.reply({
                statusCode: 200,
                body: {
                    categories: paginatedCategories,
                    pagination: {
                        currentPage: page,
                        totalPages: totalPages,
                        totalCount: filteredCategories.length,
                        limit: limit,
                        hasNextPage: page < totalPages,
                        hasPrevPage: page > 1
                    }
                }
            });
        }).as('getCategories');

        cy.reload(); // Reload to trigger intercept
        cy.wait('@getCategories');

        cy.get(selectors.categories.page).should('be.visible');
        cy.contains('Categories').should('be.visible');
        cy.get(selectors.categories.categoryItem).should('have.length.at.least', 1);
        cy.get(selectors.categories.subcategoryItem).should('have.length.at.least', 1);
        TestReporter.recordTestResult('Categories page loaded with data', 'passed', 500);
    });

    it('should allow searching for categories', () => {
        cy.intercept('GET', '/api/categories?*', (req) => {
            const query = req.query.q || '';
            const mockCategories = [
                { id: 1, name: 'Technology', description: null, subcategories: [] },
                { id: 2, name: 'Science', description: null, subcategories: [] },
                { id: 3, name: 'History', description: null, subcategories: [] }
            ];
            const filtered = query ? mockCategories.filter(c => c.name.includes(query as string)) : mockCategories;
            req.reply({
                statusCode: 200,
                body: {
                    categories: filtered, pagination: { totalCount: filtered.length } }
            });
        }).as('searchCategories');

        cy.get(selectors.categories.searchInput).type('Tech');
        cy.get(selectors.categories.searchButton).click();
        cy.wait('@searchCategories');
        cy.get(selectors.categories.categoryItem).should('have.length', 1);
        cy.contains('Technology').should('be.visible');
        cy.contains('Science').should('not.exist');
        TestReporter.recordTestResult('Searching for categories works', 'passed', 400);
    });

    it('should handle pagination correctly', () => {
        cy.intercept('GET', '/api/categories?*', (req) => {
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 20;
            const mockCategories = Array.from({ length: 50 }, (_, i) => ({
                id: i + 1, name: `Category ${i + 1}`, description: null, subcategories: []
            }));
            const totalPages = Math.ceil(mockCategories.length / limit);
            const paginatedCategories = mockCategories.slice((page - 1) * limit, page * limit);
            req.reply({
                statusCode: 200,
                body: {
                    categories: paginatedCategories,
                    pagination: {
                        currentPage: page, totalPages: totalPages, totalCount: mockCategories.length, limit: limit,
                        hasNextPage: page < totalPages, hasPrevPage: page > 1
                    }
                }
            });
        }).as('paginateCategories');

        cy.get(selectors.categories.nextPageButton).click();
        cy.wait('@paginateCategories');
        cy.get(selectors.categories.categoryItem).should('not.contain.text', 'Category 1'); // Should be on next page
        cy.get(selectors.categories.prevPageButton).click();
        cy.wait('@paginateCategories');
        cy.get(selectors.categories.categoryItem).should('contain.text', 'Category 1'); // Should be back on first page
        TestReporter.recordTestResult('Pagination for categories works', 'passed', 500);
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
