/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { navigationHelpers, assertionHelpers } from '../../../support/test-utils';
import { selectors } from '../../../support/selectors';

const adminEmail = Cypress.env('testUser') || '<EMAIL>';
const adminPassword = 'AdminPass123!';

describe('General - Unauthorized Page', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        // Ensure user is logged out
        cy.logout();
    });

    it('should redirect unauthenticated users to unauthorized page when accessing protected routes', () => {
        cy.visit('/admin/security', { failOnStatusCode: false }); // Access a protected route
        cy.url().should('include', '/unauthorized');
        cy.get(selectors.unauthorized.page).should('be.visible');
        cy.contains('Access Denied').should('be.visible');
        TestReporter.recordTestResult('Unauthenticated user redirected to unauthorized page', 'passed', 300);
    });

    it('should display correct content on the unauthorized page', () => {
        cy.visit('/unauthorized');
        cy.get(selectors.unauthorized.page).should('be.visible');
        cy.contains('Access Denied').should('be.visible');
        cy.contains('You don\'t have permission to access this page.').should('be.visible');
        cy.get(selectors.unauthorized.goBackButton).should('be.visible');
        cy.get(selectors.unauthorized.returnToDashboardButton).should('be.visible');
        TestReporter.recordTestResult('Unauthorized page content displayed', 'passed', 200);
    });

    it('should navigate back when "Go Back" button is clicked', () => {
        cy.visit('/login'); // Navigate to a page first
        cy.visit('/unauthorized');
        cy.get(selectors.unauthorized.goBackButton).click();
        cy.url().should('include', '/login'); // Should go back to the previous page
        TestReporter.recordTestResult('Go Back button navigates correctly', 'passed', 300);
    });

    it('should navigate to dashboard when "Return to Dashboard" button is clicked', () => {
        cy.visit('/unauthorized');
        cy.get(selectors.unauthorized.returnToDashboardButton).click();
        cy.url().should('eq', Cypress.config('baseUrl') + '/'); // Should go to the homepage/dashboard
        TestReporter.recordTestResult('Return to Dashboard button navigates correctly', 'passed', 300);
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
