/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';
import { navigationHelpers, formHelpers, assertionHelpers } from '../../../support/test-utils';
import { selectors } from '../../../support/selectors';

const regularUserEmail = '<EMAIL>';
const regularUserPassword = 'RegularPassword123!';

describe('General - Countries Page', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        // Login as a regular user
        cy.loginAs(regularUserEmail, regularUserPassword);
        cy.visit('/countries');
    });

    afterEach(() => {
        cy.logout();
    });

    it('should display a list of countries with search and pagination', () => {
        // Mock countries API call
        cy.intercept('GET', '/api/countries?*', (req) => {
            const query = req.query.q || '';
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 20;

            const mockCountries = Array.from({ length: 50 }, (_, i) => ({
                id: i + 1,
                name: `Country ${i + 1} ${query ? `(${query})` : ''}`,
                code: `C${i + 1}`
            }));

            const filteredCountries = query
                ? mockCountries.filter(c => c.name.includes(query as string))
                : mockCountries;

            const totalPages = Math.ceil(filteredCountries.length / limit);
            const paginatedCountries = filteredCountries.slice((page - 1) * limit, page * limit);

            req.reply({
                statusCode: 200,
                body: {
                    countries: paginatedCountries,
                    pagination: {
                        currentPage: page,
                        totalPages: totalPages,
                        totalCount: filteredCountries.length,
                        limit: limit,
                        hasNextPage: page < totalPages,
                        hasPrevPage: page > 1
                    }
                }
            });
        }).as('getCountries');

        cy.reload(); // Reload to trigger intercept
        cy.wait('@getCountries');

        cy.get(selectors.countries.page).should('be.visible');
        cy.contains('Countries').should('be.visible');
        cy.get(selectors.countries.countryItem).should('have.length.at.least', 1);
        TestReporter.recordTestResult('Countries page loaded with data', 'passed', 500);
    });

    it('should allow searching for countries', () => {
        cy.intercept('GET', '/api/countries?*', (req) => {
            const query = req.query.q || '';
            const mockCountries = [
                { id: 1, name: 'United States', code: 'USA' },
                { id: 2, name: 'Canada', code: 'CAN' },
                { id: 3, name: 'Mexico', code: 'MEX' }
            ];
            const filtered = query ? mockCountries.filter(c => c.name.includes(query as string)) : mockCountries;
            req.reply({
                statusCode: 200,
                body: {
                    countries: filtered, pagination: { totalCount: filtered.length } }
            });
        }).as('searchCountries');

        cy.get(selectors.countries.searchInput).type('United');
        cy.get(selectors.countries.searchButton).click();
        cy.wait('@searchCountries');
        cy.get(selectors.countries.countryItem).should('have.length', 1);
        cy.contains('United States').should('be.visible');
        cy.contains('Canada').should('not.exist');
        TestReporter.recordTestResult('Searching for countries works', 'passed', 400);
    });

    it('should handle pagination correctly', () => {
        cy.intercept('GET', '/api/countries?*', (req) => {
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 20;
            const mockCountries = Array.from({ length: 50 }, (_, i) => ({
                id: i + 1, name: `Country ${i + 1}`, code: `C${i + 1}`
            }));
            const totalPages = Math.ceil(mockCountries.length / limit);
            const paginatedCountries = mockCountries.slice((page - 1) * limit, page * limit);
            req.reply({
                statusCode: 200,
                body: {
                    countries: paginatedCountries,
                    pagination: {
                        currentPage: page, totalPages: totalPages, totalCount: mockCountries.length, limit: limit,
                        hasNextPage: page < totalPages, hasPrevPage: page > 1
                    }
                }
            });
        }).as('paginateCountries');

        cy.get(selectors.countries.nextPageButton).click();
        cy.wait('@paginateCountries');
        cy.get(selectors.countries.countryItem).should('not.contain.text', 'Country 1'); // Should be on next page
        cy.get(selectors.countries.prevPageButton).click();
        cy.wait('@paginateCountries');
        cy.get(selectors.countries.countryItem).should('contain.text', 'Country 1'); // Should be back on first page
        TestReporter.recordTestResult('Pagination for countries works', 'passed', 500);
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
