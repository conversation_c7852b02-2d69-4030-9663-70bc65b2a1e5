/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { navigationHelpers, assertionHelpers } from '../../../support/test-utils';
import { selectors } from '../../../support/selectors';

const regularUserEmail = '<EMAIL>';
const regularUserPassword = 'RegularPassword123!';

describe('General - Support Page', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        // Login as a regular user
        cy.loginAs(regularUserEmail, regularUserPassword);
        cy.visit('/support');
    });

    afterEach(() => {
        cy.logout();
    });

    it('should display all expected support sections', () => {
        cy.get(selectors.support.page).should('be.visible');
        cy.contains('Support & Documentation').should('be.visible');
        cy.contains('Two-Factor Authentication Setup').should('be.visible');
        cy.contains('Password Reset Guide').should('be.visible');
        cy.contains('Mobile Device Access').should('be.visible');
        cy.contains('Security Best Practices').should('be.visible');
        // Admin guide should not be visible to regular user
        cy.contains('Administrator Guide').should('not.exist');
        TestReporter.recordTestResult('Support sections displayed correctly for regular user', 'passed', 400);
    });

    it('should display admin guide for admin users', () => {
        cy.logout();
        cy.loginAs(Cypress.env('testUser'), Cypress.env('testPassword')); // Login as admin
        cy.visit('/support');
        cy.contains('Administrator Guide').should('be.visible');
        TestReporter.recordTestResult('Admin guide displayed for admin user', 'passed', 200);
    });

    it('should display quick downloads section', () => {
        cy.get(selectors.support.quickDownloadsSection).should('be.visible');
        cy.contains('2FA Setup Guide (PDF)').should('be.visible');
        cy.contains('Password Policy (PDF)').should('be.visible');
        TestReporter.recordTestResult('Quick downloads section displayed', 'passed', 200);
    });

    it('should display contact support information', () => {
        cy.get(selectors.support.contactSupportSection).should('be.visible');
        cy.contains('Email: <EMAIL>').should('be.visible');
        TestReporter.recordTestResult('Contact support information displayed', 'passed', 200);
    });

    it('should allow viewing a guide (example: 2FA Setup)', () => {
        // Mock the guide content if it loads dynamically or navigates
        // For now, just click the button and assert something on the page
        cy.get(selectors.support.twoFactorSetupGuideButton).click();
        // Assuming clicking "View Guide" expands content or navigates
        // Further assertions would depend on the actual implementation
        cy.contains('Step 1: Download an Authenticator App').should('be.visible');
        TestReporter.recordTestResult('Viewing 2FA Setup Guide', 'passed', 300);
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
