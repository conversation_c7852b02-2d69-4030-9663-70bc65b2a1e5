/// <reference types="cypress" />

describe('Homepage - Basic Tests', () => {
    beforeEach(() => {
        cy.visit('/');
    });

    it('should load the homepage', () => {
        // Check for the NWA Member Portal title in the navigation
        cy.get('nav').should('exist');
        cy.contains('NWA Member Portal').should('be.visible');
    });

    it('should have working navigation', () => {
        // Check if main navigation elements exist
        cy.get('nav').should('exist');
        cy.get('a[href="/dashboard"]').should('exist');
        cy.get('a[href="/settings"]').should('exist');
    });

    it('should load the simple test page', () => {
        cy.visit('/simple');
        cy.contains('Simple Test Page').should('be.visible');
        cy.contains('If you can see this, routing is working.').should('be.visible');
        TestReporter.recordTestResult('Simple test page loaded', 'passed', 100);
    });
});
