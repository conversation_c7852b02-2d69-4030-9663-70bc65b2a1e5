/// <reference types="cypress" />
import { TestReporter } from '../../../support/test-reporter';
import { TestDataFactory } from '../../../support/test-data-factory';
import { navigationHelpers, formHelpers, assertionHelpers } from '../../../support/test-utils';
import { selectors } from '../../../support/selectors';

const regularUserEmail = '<EMAIL>';
const regularUserPassword = 'RegularPassword123!';

describe('General - Cities Page', () => {
    beforeEach(() => {
        TestReporter.clearResults();
        // Login as a regular user
        cy.loginAs(regularUserEmail, regularUserPassword);
        cy.visit('/cities');
    });

    afterEach(() => {
        cy.logout();
    });

    it('should display a list of cities with search and pagination', () => {
        // Mock cities API call
        cy.intercept('GET', '/api/cities?*', (req) => {
            const query = req.query.q || '';
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 20;

            const mockCities = Array.from({ length: 50 }, (_, i) => ({
                id: i + 1,
                name: `City ${i + 1} ${query ? `(${query})` : ''}`,
                country: { id: i + 100, name: `Country ${i + 1}`, code: `C${i + 1}` }
            }));

            const filteredCities = query
                ? mockCities.filter(c => c.name.includes(query as string))
                : mockCities;

            const totalPages = Math.ceil(filteredCities.length / limit);
            const paginatedCities = filteredCities.slice((page - 1) * limit, page * limit);

            req.reply({
                statusCode: 200,
                body: {
                    cities: paginatedCities,
                    pagination: {
                        currentPage: page,
                        totalPages: totalPages,
                        totalCount: filteredCities.length,
                        limit: limit,
                        hasNextPage: page < totalPages,
                        hasPrevPage: page > 1
                    }
                }
            });
        }).as('getCities');

        cy.reload(); // Reload to trigger intercept
        cy.wait('@getCities');

        cy.get(selectors.cities.page).should('be.visible');
        cy.contains('Cities').should('be.visible');
        cy.get(selectors.cities.cityItem).should('have.length.at.least', 1);
        TestReporter.recordTestResult('Cities page loaded with data', 'passed', 500);
    });

    it('should allow searching for cities', () => {
        cy.intercept('GET', '/api/cities?*', (req) => {
            const query = req.query.q || '';
            const mockCities = [
                { id: 1, name: 'New York', country: { id: 101, name: 'USA', code: 'USA' } },
                { id: 2, name: 'London', country: { id: 102, name: 'UK', code: 'GBR' } },
                { id: 3, name: 'Paris', country: { id: 103, name: 'France', code: 'FRA' } }
            ];
            const filtered = query ? mockCities.filter(c => c.name.includes(query as string)) : mockCities;
            req.reply({
                statusCode: 200,
                body: {
                    cities: filtered, pagination: { totalCount: filtered.length } }
            });
        }).as('searchCities');

        cy.get(selectors.cities.searchInput).type('New');
        cy.get(selectors.cities.searchButton).click();
        cy.wait('@searchCities');
        cy.get(selectors.cities.cityItem).should('have.length', 1);
        cy.contains('New York').should('be.visible');
        cy.contains('London').should('not.exist');
        TestReporter.recordTestResult('Searching for cities works', 'passed', 400);
    });

    it('should handle pagination correctly', () => {
        cy.intercept('GET', '/api/cities?*', (req) => {
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 20;
            const mockCities = Array.from({ length: 50 }, (_, i) => ({
                id: i + 1, name: `City ${i + 1}`, country: { id: i + 100, name: `Country ${i + 1}`, code: `C${i + 1}` }
            }));
            const totalPages = Math.ceil(mockCities.length / limit);
            const paginatedCities = mockCities.slice((page - 1) * limit, page * limit);
            req.reply({
                statusCode: 200,
                body: {
                    cities: paginatedCities,
                    pagination: {
                        currentPage: page, totalPages: totalPages, totalCount: mockCities.length, limit: limit,
                        hasNextPage: page < totalPages, hasPrevPage: page > 1
                    }
                }
            });
        }).as('paginateCities');

        cy.get(selectors.cities.nextPageButton).click();
        cy.wait('@paginateCities');
        cy.get(selectors.cities.cityItem).should('not.contain.text', 'City 1'); // Should be on next page
        cy.get(selectors.cities.prevPageButton).click();
        cy.wait('@paginateCities');
        cy.get(selectors.cities.cityItem).should('contain.text', 'City 1'); // Should be back on first page
        TestReporter.recordTestResult('Pagination for cities works', 'passed', 500);
    });

    after(() => {
        TestReporter.generateHTMLReport();
        TestReporter.generateJSONReport();
        TestReporter.logPerformanceMetrics();
    });
});
