/// <reference types="@prisma/client" />

type ProfileDefaultsProfile = Partial<
  Pick<
    Prisma.UserProfileUncheckedCreateInput,
    | 'firstName'
    | 'lastName'
    | 'personalEmail'
    | 'nwaEmail'
    | 'phone'
    | 'mobile'
    | 'streetAddress1'
    | 'streetAddress2'
    | 'town'
    | 'regionText'
    | 'postalCode'
    | 'bio'
    | 'peaceAmbassadorNumber'
    | 'countryId'
    | 'cityId'
    | 'regionId'
  >
> & {
  stateProvince?: string | null
  postcodeZip?: string | null
}

type ProfileDefaultsEntry = {
  userName?: string | null
  profile: ProfileDefaultsProfile
  countryCode?: string | null
  cityName?: string | null
  regionCode?: string | null
}
