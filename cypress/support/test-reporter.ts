// Test reporting utilities for Cypress
export class TestReporter {
    static screenshots = new Map();
    static videos = new Map();
    static results = [];
    // Capture screenshot
    static captureScreenshot(name, selector) {
        if (selector) {
            cy.get(selector).screenshot(name);
        }
        else {
            cy.screenshot(name);
        }
        this.screenshots.set(name, `${name}.png`);
    }
    // Capture element screenshot
    static captureElementScreenshot(element, name) {
        cy.get(element).screenshot(name);
        this.screenshots.set(name, `${name}.png`);
    }
    // Capture full page screenshot
    static captureFullPageScreenshot(name) {
        cy.screenshot(name);
        this.screenshots.set(name, `${name}.png`);
    }
    // Record test result
    static recordTestResult(title, status, duration, error) {
        const result = {
            title,
            status,
            duration,
            error,
            screenshot: this.screenshots.get(title),
            video: this.videos.get(title)
        };
        this.results.push(result);
        cy.log(`Test ${status}: ${title} (${duration}ms)`);
    }
    // Generate HTML report
    static generateHTMLReport() {
        const report = this.generateReportData();
        const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cypress Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .suite { margin-bottom: 30px; border: 1px solid #ddd; border-radius: 5px; }
        .suite-header { background: #333; color: white; padding: 15px; }
        .test { padding: 10px; border-bottom: 1px solid #eee; }
        .test.passed { background: #d4edda; }
        .test.failed { background: #f8d7da; }
        .test.skipped { background: #fff3cd; }
        .error { color: red; font-family: monospace; white-space: pre-wrap; }
        .screenshot { max-width: 300px; margin: 10px 0; }
        .stats { display: flex; gap: 20px; }
        .stat { text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; }
        .stat-label { color: #666; }
    </style>
</head>
<body>
    <h1>Cypress E2E Test Report</h1>

    <div class="summary">
        <h2>Test Summary</h2>
        <div class="stats">
            <div class="stat">
                <div class="stat-number">${report.summary.total}</div>
                <div class="stat-label">Total Tests</div>
            </div>
            <div class="stat">
                <div class="stat-number" style="color: green;">${report.summary.passed}</div>
                <div class="stat-label">Passed</div>
            </div>
            <div class="stat">
                <div class="stat-number" style="color: red;">${report.summary.failed}</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat">
                <div class="stat-number" style="color: orange;">${report.summary.skipped}</div>
                <div class="stat-label">Skipped</div>
            </div>
        </div>
        <p><strong>Duration:</strong> ${Math.round(report.summary.duration / 1000)}s</p>
        <p><strong>Timestamp:</strong> ${new Date(report.summary.timestamp).toLocaleString()}</p>
    </div>

    <div class="environment">
        <h2>Environment</h2>
        <p><strong>Browser:</strong> ${report.environment.browser}</p>
        <p><strong>Viewport:</strong> ${report.environment.viewport}</p>
        <p><strong>Base URL:</strong> ${report.environment.baseUrl}</p>
        <p><strong>User Agent:</strong> ${report.environment.userAgent}</p>
    </div>

    ${report.suites.map(suite => `
    <div class="suite">
        <div class="suite-header">
            <h3>${suite.name}</h3>
            <p>Status: ${suite.status} | Duration: ${Math.round(suite.duration / 1000)}s</p>
        </div>
        ${suite.tests.map(test => `
        <div class="test ${test.status}">
            <h4>${test.title}</h4>
            <p>Duration: ${test.duration}ms | Status: ${test.status}</p>
            ${test.error ? `<div class="error">${test.error}</div>` : ''}
            ${test.screenshot ? `<img src="../screenshots/${test.screenshot}" class="screenshot" alt="Screenshot for ${test.title}">` : ''}
        </div>
        `).join('')}
    </div>
    `).join('')}
</body>
</html>`;
        cy.writeFile('cypress/reports/test-report.html', html);
    }
    // Generate JSON report
    static generateJSONReport() {
        const report = this.generateReportData();
        cy.writeFile('cypress/reports/test-report.json', JSON.stringify(report, null, 2));
    }
    // Generate report data
    static generateReportData() {
        const passed = this.results.filter(r => r.status === 'passed').length;
        const failed = this.results.filter(r => r.status === 'failed').length;
        const skipped = this.results.filter(r => r.status === 'skipped').length;
        const total = this.results.length;
        const duration = this.results.reduce((sum, r) => sum + r.duration, 0);
        // Group tests by suite (simplified - in real implementation, you'd track suite names)
        const suites = [{
                name: 'All Tests',
                tests: this.results,
                duration,
                status: failed > 0 ? 'failed' : passed > 0 ? 'passed' : 'skipped'
            }];
        return {
            summary: {
                total,
                passed,
                failed,
                skipped,
                duration,
                timestamp: new Date().toISOString()
            },
            suites,
            environment: {
                browser: 'chrome', // Default browser
                viewport: '1280x720', // Default viewport
                baseUrl: 'http://localhost:3001', // Default base URL
                userAgent: 'Cypress/13.15.0' // Default user agent
            }
        };
    }
    // Log test performance metrics
    static logPerformanceMetrics() {
        const report = this.generateReportData();
        cy.log('=== Test Performance Summary ===');
        cy.log(`Total Tests: ${report.summary.total}`);
        cy.log(`Passed: ${report.summary.passed}`);
        cy.log(`Failed: ${report.summary.failed}`);
        cy.log(`Skipped: ${report.summary.skipped}`);
        cy.log(`Total Duration: ${Math.round(report.summary.duration / 1000)}s`);
        cy.log(`Average Test Duration: ${Math.round(report.summary.duration / report.summary.total)}ms`);
        if (report.summary.failed > 0) {
            cy.log('=== Failed Tests ===');
            this.results
                .filter(r => r.status === 'failed')
                .forEach(test => {
                cy.log(`❌ ${test.title}: ${test.error}`);
            });
        }
    }
    // Export test results
    static exportResults() {
        this.generateHTMLReport();
        this.generateJSONReport();
        this.logPerformanceMetrics();
    }
    // Clear previous results
    static clearResults() {
        this.screenshots.clear();
        this.videos.clear();
        this.results = [];
    }
}
//# sourceMappingURL=data:application/json;base64,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