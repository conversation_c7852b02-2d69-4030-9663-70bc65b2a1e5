// Test utilities and helper functions for Cypress tests
// Generate random test data
export const generateTestData = {
    email: () => `test.${Date.now()}@example.com`,
    password: () => `TestPass${Math.random().toString(36).substring(7)}123`,
    name: () => `Test User ${Math.random().toString(36).substring(7)}`,
    phone: () => `+1${Math.floor(Math.random() * 9000000000) + 1000000000}`,
    address: () => `${Math.floor(Math.random() * 9999)} Test Street`,
    city: () => `Test City ${Math.floor(Math.random() * 100)}`,
    postalCode: () => `${Math.floor(Math.random() * 90000) + 10000}`,
};
// API helper functions
export const apiHelpers = {
    // Login helper
    login: (email, password) => {
        return cy.request({
            method: 'POST',
            url: '/api/auth/callback/credentials',
            body: { email, password },
            failOnStatusCode: false,
        });
    },
    // Get user data
    getUser: (userId) => {
        return cy.request({
            method: 'GET',
            url: `/api/users/${userId}`,
            failOnStatusCode: false,
        });
    },
    // Create test user
    createTestUser: (userData) => {
        return cy.request({
            method: 'POST',
            url: '/api/users',
            body: userData,
            failOnStatusCode: false,
        });
    },
    // Update user
    updateUser: (userId, userData) => {
        return cy.request({
            method: 'PUT',
            url: `/api/users/${userId}`,
            body: userData,
            failOnStatusCode: false,
        });
    },
    // Delete user
    deleteUser: (userId) => {
        return cy.request({
            method: 'DELETE',
            url: `/api/users/${userId}`,
            failOnStatusCode: false,
        });
    },
};
// Form helpers
export const formHelpers = {
    // Fill login form
    fillLoginForm: (email, password) => {
        cy.get('input[type="email"]').clear().type(email);
        cy.get('input[type="password"]').clear().type(password);
        cy.get('button[type="submit"]').click();
    },
    // Fill registration form
    fillRegistrationForm: (userData) => {
        cy.get('input[name="name"]').clear().type(userData.name);
        cy.get('input[name="email"]').clear().type(userData.email);
        cy.get('input[name="password"]').clear().type(userData.password);
        cy.get('input[name="confirmPassword"]').clear().type(userData.password);
        cy.get('button[type="submit"]').click();
    },
    // Fill profile form
    fillProfileForm: (profileData) => {
        if (profileData.name) {
            cy.get('input[name="name"]').clear().type(profileData.name);
        }
        if (profileData.email) {
            cy.get('input[name="email"]').clear().type(profileData.email);
        }
        if (profileData.phone) {
            cy.get('input[name="phone"]').clear().type(profileData.phone);
        }
        cy.get('button[type="submit"]').click();
    },
};
// Navigation helpers
export const navigationHelpers = {
    // Navigate to login page (login form is on homepage)
    goToLogin: () => {
        cy.visit('/');
    },
    // Navigate to dashboard
    goToDashboard: () => {
        cy.visit('/dashboard');
    },
    // Navigate to admin users
    goToAdminUsers: () => {
        cy.visit('/admin/users');
    },
    // Navigate to settings
    goToSettings: () => {
        cy.visit('/settings');
    },
    // Navigate to profile
    goToProfile: () => {
        cy.visit('/profile');
    },
};
// Assertion helpers
export const assertionHelpers = {
    // Check if user is logged in
    shouldBeLoggedIn: () => {
        cy.get('[data-testid="user-menu"]').should('be.visible');
        cy.get('[data-testid="logout-button"]').should('be.visible');
    },
    // Check if user is logged out
    shouldBeLoggedOut: () => {
        cy.get('[data-testid="login-button"]').should('be.visible');
        cy.get('[data-testid="user-menu"]').should('not.exist');
    },
    // Check page title
    shouldHaveTitle: (title) => {
        cy.title().should('include', title);
    },
    // Check URL contains
    shouldHaveUrl: (url) => {
        cy.url().should('include', url);
    },
    // Check element contains text
    shouldContainText: (selector, text) => {
        cy.get(selector).should('contain.text', text);
    },
    // Check element is visible
    shouldBeVisible: (selector) => {
        cy.get(selector).should('be.visible');
    },
    // Check element exists
    shouldExist: (selector) => {
        cy.get(selector).should('exist');
    },
};
// Wait helpers
export const waitHelpers = {
    // Wait for page load
    waitForPageLoad: () => {
        cy.get('body').should('be.visible');
    },
    // Wait for API response
    waitForApiResponse: (url, method = 'GET') => {
        cy.intercept(method, url).as('apiCall');
        cy.wait('@apiCall');
    },
    // Wait for element to appear
    waitForElement: (selector, timeout = 10000) => {
        cy.get(selector, { timeout }).should('be.visible');
    },
    // Wait for element to disappear
    waitForElementToDisappear: (selector, timeout = 10000) => {
        cy.get(selector, { timeout }).should('not.exist');
    },
};
// Database helpers
export const dbHelpers = {
    // Clean up test data
    cleanupTestData: () => {
        // This would typically call an API endpoint to clean up test data
        cy.request('POST', '/api/test/cleanup', {});
    },
    // Seed test data
    seedTestData: (data) => {
        cy.request('POST', '/api/test/seed', data);
    },
    // Reset database to clean state
    resetDatabase: () => {
        cy.request('POST', '/api/test/reset', {});
    },
};
// File upload helpers
export const fileHelpers = {
    // Upload file to input
    uploadFile: (selector, fileName, fileType = 'image/png') => {
        cy.fixture(fileName).then((fileContent) => {
            cy.get(selector).upload({
                fileContent,
                fileName,
                mimeType: fileType
            });
        });
    },
    // Generate test file
    generateTestFile: (fileName, content) => {
        cy.writeFile(`cypress/fixtures/${fileName}`, content);
    },
};
// Accessibility helpers
export const a11yHelpers = {
    // Check for accessibility violations
    checkAccessibility: () => {
        cy.injectAxe();
        cy.checkA11y();
    },
    // Check specific element for accessibility
    checkElementAccessibility: (selector) => {
        cy.injectAxe();
        cy.checkA11y(selector);
    },
};
// Performance helpers
export const performanceHelpers = {
    // Measure page load time
    measurePageLoadTime: () => {
        const start = performance.now();
        cy.window().then(() => {
            const end = performance.now();
            const loadTime = end - start;
            cy.log(`Page load time: ${loadTime}ms`);
            cy.wrap(loadTime).should('be.lessThan', 3000); // 3 seconds max
        });
    },
    // Measure API response time
    measureApiResponseTime: (url, method = 'GET') => {
        const start = performance.now();
        cy.request(method, url).then(() => {
            const end = performance.now();
            const responseTime = end - start;
            cy.log(`API response time for ${url}: ${responseTime}ms`);
            cy.wrap(responseTime).should('be.lessThan', 2000); // 2 seconds max
        });
    },
};
//# sourceMappingURL=data:application/json;base64,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