/**
 * Centralised map of `data-testid` selectors grouped by feature areas.
 * Cypress specs should import from here instead of hard-coding attributes.
 */
export const selectors = {
    layout: {
        mainContent: '[data-testid="main-content"]',
        dashboardLayout: '[data-testid="dashboard-layout"]',
        sidebar: '[data-testid="sidebar"]',
        sidebarLogout: '[data-testid="sidebar-logout"]',
    },
    auth: {
        loginForm: '[data-testid="login-form"]',
        loginEmail: '[data-testid="login-email"]',
        loginPassword: '[data-testid="login-password"]',
        loginRemember: '[data-testid="login-remember"]',
        loginSubmit: '[data-testid="login-submit"]',
        loginError: '[data-testid="login-error"]',
        setup2faQr: '[data-testid="setup-2fa-qr"]',
        setup2faCopy: '[data-testid="setup-2fa-copy"]',
        setup2faDownload: '[data-testid="setup-2fa-download"]',
        setup2faEnable: '[data-testid="setup-2fa-enable"]',
    },
    dashboard: {
        page: '[data-testid="dashboard-page"]',
        tab: (tabId) => `[data-testid="tab-${tabId}"]`,
        overview: '[data-testid="dashboard-overview"]',
        totalNotifications: '[data-testid="dashboard-card-total-notifications"]',
        activeServers: '[data-testid="dashboard-card-active-servers"]',
        activeTreaties: '[data-testid="dashboard-card-active-treaties"]',
        profileStatus: '[data-testid="dashboard-card-profile-status"]',
    },
    users: {
        page: '[data-testid="users-page"]',
        nav: '[data-testid="users-nav"]',
        navItem: (sectionId) => `[data-testid="users-nav-${sectionId}"]`,
        contentPanel: (sectionId) => `[data-testid="users-content-${sectionId}"]`,
    },
    usersCreate: {
        tabList: '[data-testid="users-create-tab-list"]',
        tab: (tabId) => `[data-testid="users-create-tab-${tabId}"]`,
        section: (tabId) => `[data-testid="users-create-section-${tabId}"]`,
        status: '[data-testid="users-create-status"]',
    },
    usersCreateBasic: {
        form: '[data-testid="users-create-basic-form"]',
        firstName: '[data-testid="users-create-basic-first-name"]',
        surname: '[data-testid="users-create-basic-surname"]',
        dob: '[data-testid="users-create-basic-dob"]',
        personalEmail: '[data-testid="users-create-basic-personal-email"]',
        nwaEmail: '[data-testid="users-create-basic-nwa-email"]',
        phone: '[data-testid="users-create-basic-phone"]',
        mobile: '[data-testid="users-create-basic-mobile"]',
        bio: '[data-testid="users-create-basic-bio"]',
        submit: '[data-testid="users-create-basic-submit"]',
    },
    usersCreateContact: {
        form: '[data-testid="users-create-contact-form"]',
        field: (field) => `[data-testid="users-create-contact-${field}"]`,
        countryInput: '[data-testid="users-create-contact-country-input"]',
        countryOption: (id) => `[data-testid="users-create-contact-country-option-${id}"]`,
        submit: '[data-testid="users-create-contact-submit"]',
    },
    usersCreateIdentification: {
        form: '[data-testid="users-create-identification-form"]',
        nationSearch: '[data-testid="users-create-identification-nation-search"]',
        nationAdd: '[data-testid="users-create-identification-nation-add"]',
        nationClear: '[data-testid="users-create-identification-nation-clear"]',
        submit: '[data-testid="users-create-identification-submit"]',
    },
    usersCreatePositions: {
        form: '[data-testid="users-create-positions-form"]',
        titleSelect: '[data-testid="users-create-positions-title"]',
        search: '[data-testid="users-create-positions-search"]',
        submit: '[data-testid="users-create-positions-submit"]',
    },
    usersCreateTreaty: {
        form: '[data-testid="users-create-treaty-form"]',
        selectTreaty: '[data-testid="users-create-treaty-select"]',
        selectType: '[data-testid="users-create-treaty-type-select"]',
        addBusiness: '[data-testid="users-create-treaty-add-business"]',
        closeBusiness: '[data-testid="users-create-treaty-close-business"]',
        businessName: '[data-testid="users-create-treaty-business-name"]',
        registrationNumber: '[data-testid="users-create-treaty-registration-number"]',
        address1: '[data-testid="users-create-treaty-address1"]',
        city: '[data-testid="users-create-treaty-city"]',
        submit: '[data-testid="users-create-treaty-submit"]',
    },
    usersCreateOrdinance: {
        form: '[data-testid="users-create-ordinance-form"]',
        select: '[data-testid="users-create-ordinance-select"]',
        add: '[data-testid="users-create-ordinance-add"]',
        remove: (id) => `[data-testid="users-create-ordinance-remove-${id}"]`,
        submit: '[data-testid="users-create-ordinance-submit"]',
    },
    usersUpdate: {
        searchForm: '[data-testid="users-update-search-form"]',
        searchInput: '[data-testid="users-update-search-input"]',
        suggestions: '[data-testid="users-update-suggestions"]',
        suggestion: (id) => `[data-testid="users-update-suggestion-${id}"]`,
        selectedHeader: '[data-testid="users-update-selected-header"]',
        form: '[data-testid="users-update-form"]',
        submitVerification: '[data-testid="users-update-submit-verification"]',
        submit: '[data-testid="users-update-submit"]',
        tab: (tabId) => `[data-testid="users-update-tab-${tabId}"]`,
        section: (tabId) => `[data-testid="users-update-section-${tabId}"]`,
    },
    usersSearch: {
        submit: '[data-testid="users-search-submit"]',
        clear: '[data-testid="users-search-clear"]',
        results: '[data-testid="users-search-results"]',
        table: '[data-testid="users-search-table"]',
    },
    treaties: {
        page: '[data-testid="treaty-management-page"]',
        nav: '[data-testid="treaty-management-nav"]',
        navItem: (sectionId) => `[data-testid="treaty-nav-${sectionId}"]`,
        contentPanel: (sectionId) => `[data-testid="treaty-content-${sectionId}"]`,
        nationTab: (tabId) => `[data-testid="treaty-nation-tab-${tabId}"]`,
    },
    ordinances: {
        page: '[data-testid="ordinances-page"]',
        tabs: '[data-testid="ordinances-tabs"]',
        uploadTab: '[data-testid="ordinances-tab-upload"]',
        viewTab: '[data-testid="ordinances-tab-view"]',
    },
    settings: {
        page: '[data-testid="settings-page"]',
        nav: '[data-testid="settings-nav"]',
        navItem: (sectionId) => `[data-testid="settings-nav-${sectionId}"]`,
        contentWrapper: '[data-testid="settings-content-wrapper"]',
        contentPanel: (sectionId) => `[data-testid="settings-content-${sectionId}"]`,
        sessionActions: '[data-testid="settings-session-actions"]',
        sessionAutoRefresh: '[data-testid="settings-session-auto-refresh"]',
        sessionRefresh: '[data-testid="settings-session-refresh"]',
        sessionTerminate: '[data-testid="settings-session-terminate"]',
        twoFactorToggle: '[data-testid="settings-twofactor-toggle"]',
    },
    notifications: {
        page: '[data-testid="notifications-page"]',
        loading: '[data-testid="notifications-loading"]',
        error: '[data-testid="notifications-error"]',
        center: '[data-testid="notifications-center"]',
        header: '[data-testid="notifications-header"]',
        headerActions: '[data-testid="notifications-header-actions"]',
        markAllRead: '[data-testid="notifications-mark-all-read"]',
        export: '[data-testid="notifications-export"]',
        stats: '[data-testid="notifications-stats"]',
        statTotal: '[data-testid="notifications-stat-total"]',
        statUnread: '[data-testid="notifications-stat-unread"]',
        statRead: '[data-testid="notifications-stat-read"]',
        statCategories: '[data-testid="notifications-stat-categories"]',
        search: '[data-testid="notifications-search"]',
        filterPriority: '[data-testid="notifications-filter-priority"]',
        filterCategory: '[data-testid="notifications-filter-category"]',
        filterStatus: '[data-testid="notifications-filter-status"]',
        bulkActions: '[data-testid="notifications-bulk-actions"]',
        bulkMarkRead: '[data-testid="notifications-bulk-mark-read"]',
        bulkDelete: '[data-testid="notifications-bulk-delete"]',
        tableContainer: '[data-testid="notifications-table-container"]',
        table: '[data-testid="notifications-table"]',
        row: (notificationId) => `[data-testid="notification-row-${notificationId}"]`,
        rowMarkRead: '[data-testid="notification-mark-read"]',
        rowDelete: '[data-testid="notification-delete"]',
    },
    profile: {
        page: '[data-testid="profile-page"]',
        card: '[data-testid="profile-card"]',
        loading: '[data-testid="profile-loading"]',
        nav: '[data-testid="profile-nav"]',
        navItem: (tabId) => `[data-testid="profile-nav-${tabId}"]`,
        content: '[data-testid="profile-content"]',
        personalForm: '[data-testid="profile-personal-form"]',
        firstName: '[data-testid="profile-first-name"]',
        lastName: '[data-testid="profile-last-name"]',
        dateOfBirth: '[data-testid="profile-date-of-birth"]',
        bio: '[data-testid="profile-bio"]',
        personalSave: '[data-testid="profile-personal-save"]',
        contactSection: '[data-testid="profile-contact-section"]',
        aptSuiteUnit: '[data-testid="profile-apt-suite-unit"]',
        streetName: '[data-testid="profile-street-name"]',
        townCity: '[data-testid="profile-town-city"]',
        stateProvince: '[data-testid="profile-state-province"]',
        postcode: '[data-testid="profile-postcode"]',
        country: '[data-testid="profile-country"]',
        personalEmail: '[data-testid="profile-personal-email"]',
        nwaEmail: '[data-testid="profile-nwa-email"]',
        phone: '[data-testid="profile-phone"]',
        mobile: '[data-testid="profile-mobile"]',
        contactSave: '[data-testid="profile-contact-save"]',
        photoSection: '[data-testid="profile-photo-section"]',
        photoPreview: '[data-testid="profile-photo-preview"]',
        photoInput: '[data-testid="profile-photo-input"]',
        photoChange: '[data-testid="profile-photo-change"]',
        photoRemove: '[data-testid="profile-photo-remove"]',
        photoSave: '[data-testid="profile-photo-save"]',
        securitySection: '[data-testid="profile-security-section"]',
        currentPassword: '[data-testid="profile-current-password"]',
        newPassword: '[data-testid="profile-new-password"]',
        confirmPassword: '[data-testid="profile-confirm-password"]',
        twoFaCard: '[data-testid="profile-twofa-card"]',
        twoFaDisable: '[data-testid="profile-twofa-disable"]',
        twoFaEnable: '[data-testid="profile-twofa-enable"]',
        twoFaQr: '[data-testid="profile-twofa-qr"]',
        securitySave: '[data-testid="profile-security-save"]',
    },
};
export const getSelector = (feature, key, ...args) => {
    const value = selectors[feature][key];
    if (typeof value === 'function') {
        return value(...args);
    }
    return value;
};
//# sourceMappingURL=data:application/json;base64,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