// Test data factory for generating dynamic test data
import { generateTestData } from './test-utils';
export class TestDataFactory {
    static userCounter = 0;
    static treatyCounter = 0;
    // Generate user data
    static createUser(overrides = {}) {
        const counter = ++this.userCounter;
        return {
            id: overrides.id || `user-${counter}`,
            name: overrides.name || generateTestData.name(),
            email: overrides.email || `test.user.${counter}@example.com`,
            password: overrides.password || generateTestData.password(),
            role: overrides.role || 'user',
            permissions: overrides.permissions || ['read'],
            isActive: overrides.isActive !== undefined ? overrides.isActive : true,
            phone: overrides.phone || generateTestData.phone(),
            address: overrides.address || generateTestData.address(),
            city: overrides.city || generateTestData.city(),
            postalCode: overrides.postalCode || generateTestData.postalCode(),
            country: overrides.country || 'Test Country',
            ...overrides
        };
    }
    // Generate multiple users
    static createUsers(count, overrides = {}) {
        return Array.from({ length: count }, () => this.createUser(overrides));
    }
    // Generate admin user
    static createAdminUser(overrides = {}) {
        return this.createUser({
            role: 'admin',
            permissions: ['read', 'write', 'delete', 'admin'],
            ...overrides
        });
    }
    // Generate treaty data
    static createTreaty(overrides = {}) {
        const counter = ++this.treatyCounter;
        return {
            title: overrides.title || `Test Treaty ${counter}`,
            type: overrides.type || 'treaty-type-001',
            parties: overrides.parties || ['nation-001', 'nation-002'],
            status: overrides.status || 'draft',
            content: overrides.content || `This is test treaty content for treaty ${counter}`,
            createdBy: overrides.createdBy || 'admin-001',
            createdAt: overrides.createdAt || new Date().toISOString(),
            updatedAt: overrides.updatedAt || new Date().toISOString(),
            ...overrides
        };
    }
    // Generate multiple treaties
    static createTreaties(count, overrides = {}) {
        return Array.from({ length: count }, () => this.createTreaty(overrides));
    }
    // Generate API response
    static createApiResponse(statusCode, body) {
        return {
            statusCode,
            body
        };
    }
    // Generate error response
    static createErrorResponse(message, errorCode = 'GENERIC_ERROR') {
        return this.createApiResponse(400, {
            success: false,
            message,
            error: errorCode
        });
    }
    // Generate success response
    static createSuccessResponse(data, message = 'Success') {
        return this.createApiResponse(200, {
            success: true,
            message,
            ...data
        });
    }
    // Generate paginated response
    static createPaginatedResponse(items, page = 1, limit = 10, total) {
        const totalItems = total || items.length;
        const totalPages = Math.ceil(totalItems / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedItems = items.slice(startIndex, endIndex);
        return this.createApiResponse(200, {
            success: true,
            data: paginatedItems,
            pagination: {
                page,
                limit,
                total: totalItems,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1
            }
        });
    }
    // Generate form validation error
    static createValidationError(errors) {
        return this.createApiResponse(400, {
            success: false,
            message: 'Validation failed',
            errors
        });
    }
    // Generate random ID
    static generateId(prefix = 'test') {
        return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(7)}`;
    }
    // Generate random string
    static generateRandomString(length = 10) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    // Generate random number
    static generateRandomNumber(min = 0, max = 1000) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    // Generate random date
    static generateRandomDate(start = new Date(2020, 0, 1), end = new Date()) {
        return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    }
    // Generate random boolean
    static generateRandomBoolean() {
        return Math.random() >= 0.5;
    }
    // Generate random array element
    static getRandomArrayElement(array) {
        return array[Math.floor(Math.random() * array.length)];
    }
    // Generate random array
    static getRandomArray(array, count) {
        const shuffled = [...array].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, count);
    }
    // Reset counters (useful for test isolation)
    static resetCounters() {
        this.userCounter = 0;
        this.treatyCounter = 0;
    }
}
//# sourceMappingURL=data:application/json;base64,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