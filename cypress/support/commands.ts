// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
/// <reference types="cypress" />
import { selectors } from './selectors';

type AxeWindow = Window & { axe?: { run: (context: unknown, options?: Record<string, unknown>) => Promise<AxeResults>; }; };

interface AxeResults {
  violations: Array<{
    id: string;
    impact?: string;
    description: string;
    helpUrl?: string;
    nodes: Array<{ target: string[]; html: string }>;
  }>;
}

let cachedAxeSource: string | null = null;

const loadAxeSource = () => {
  if (cachedAxeSource) {
    return cy.wrap(cachedAxeSource, { log: false });
  }

  return cy
    .readFile('node_modules/axe-core/axe.min.js', { log: false })
    .then((source) => {
      cachedAxeSource = source;
      return source;
    });
};

Cypress.Commands.add('getByTestId', (testId: string) => {
  return cy.get(`[data-testid="${testId}"]`);
});

Cypress.Commands.add('injectAxe', () => {
  cy.window({ log: false }).then((windowObject) => {
    const axeWindow = windowObject as AxeWindow;
    if (axeWindow.axe) {
      return;
    }

    return loadAxeSource().then((source) => {
      const script = axeWindow.document.createElement('script');
      script.type = 'text/javascript';
      script.text = source;
      axeWindow.document.head.appendChild(script);
    });
  });
});

Cypress.Commands.add('checkA11y', (context?: string, options?: Record<string, unknown>) => {
  cy.window({ log: false }).then((windowObject) => {
    const axeWindow = windowObject as AxeWindow;
    const axe = axeWindow.axe;

    if (!axe) {
      throw new Error('axe has not been injected. Call cy.injectAxe() before cy.checkA11y().');
    }

    const root = context ? axeWindow.document.querySelector(context) : axeWindow.document;
    const target = root ?? axeWindow.document;

    return axe.run(target, options).then((results) => {
      if (!results.violations.length) {
        Cypress.log({ name: 'axe', message: 'No accessibility violations found' });
        return;
      }

      const details = results.violations
        .map((violation) => {
          const impactedNodes = violation.nodes.map((node) => node.target.join(', ')).join('; ');
          return `${violation.id} (${violation.impact ?? 'unknown impact'}): ${impactedNodes}`;
        })
        .join('\n');

      throw new Error(`Accessibility violations found:\n${details}`);
    });
  });
});
// Custom command to login using real UI flow
Cypress.Commands.add('loginAs', (email, password, options) => {
    const remember = options?.remember ?? false;
    const redirectTo = options?.redirectTo ?? '/dashboard';
    cy.visit(`/login?callbackUrl=${encodeURIComponent(redirectTo)}`);
    cy.get(selectors.auth.loginEmail).clear().type(email);
    cy.get(selectors.auth.loginPassword).clear().type(password, { log: false });
    if (remember) {
        cy.get(selectors.auth.loginRemember).check();
    }
    cy.get(selectors.auth.loginSubmit).click();
    cy.url({ timeout: 15000 }).should('include', redirectTo);
});
// Custom command to check if user is authenticated
Cypress.Commands.add('isAuthenticated', () => {
    cy.window().should('have.property', 'localStorage');
    cy.window().then((window) => {
        expect(window.localStorage.getItem('next-auth.session-token')).to.exist;
    });
});
// Custom command to logout
// Custom command to logout via sidebar
Cypress.Commands.add('logout', () => {
    const logoutSelector = selectors.layout.sidebarLogout;
    cy.get('body').then(($body) => {
        if ($body.find(logoutSelector).length) {
            cy.get(logoutSelector).click({ force: true });
        } else {
            cy.log('sidebar logout not available, skipping');
        }
    });
});
// Custom command to wait for API response
Cypress.Commands.add('waitForApi', (url, method = 'GET') => {
    cy.intercept(method, url).as('apiCall');
    cy.wait('@apiCall');
});
// Custom command to check page title
Cypress.Commands.add('checkPageTitle', (title) => {
    cy.title().should('include', title);
});
// Custom command to check if element contains text
Cypress.Commands.add('containsText', (selector, text) => {
    cy.get(selector).should('contain.text', text);
});
// Custom command to check if element exists
Cypress.Commands.add('shouldExist', (selector) => {
    cy.get(selector).should('exist');
});
// Custom command to check if element is visible
Cypress.Commands.add('shouldBeVisible', (selector) => {
    cy.get(selector).should('be.visible');
});
// Custom command to check if element is not visible
Cypress.Commands.add('shouldNotBeVisible', (selector) => {
    cy.get(selector).should('not.be.visible');
});
// Custom command to check if element is disabled
Cypress.Commands.add('shouldBeDisabled', (selector) => {
    cy.get(selector).should('be.disabled');
});
// Custom command to check if element is enabled
Cypress.Commands.add('shouldBeEnabled', (selector) => {
    cy.get(selector).should('not.be.disabled');
});
// Custom command to check URL
Cypress.Commands.add('checkUrl', (url) => {
    cy.url().should('include', url);
});
// Custom command to check if button is clickable
Cypress.Commands.add('shouldBeClickable', (selector) => {
    cy.get(selector).should('be.visible').and('not.be.disabled');
});
// Custom command to fill form field
Cypress.Commands.add('fillField', (selector, value) => {
    cy.get(selector).clear().type(value);
});
// Custom command to select dropdown option
Cypress.Commands.add('selectOption', (selector, option) => {
    cy.get(selector).select(option);
});
// Custom command to check if checkbox is checked
Cypress.Commands.add('shouldBeChecked', (selector) => {
    cy.get(selector).should('be.checked');
});
// Custom command to check if checkbox is not checked
Cypress.Commands.add('shouldNotBeChecked', (selector) => {
    cy.get(selector).should('not.be.checked');
});
// Custom command to check table row count
Cypress.Commands.add('checkTableRowCount', (selector, count) => {
    cy.get(selector).should('have.length', count);
});
// Custom command to check if alert contains text
Cypress.Commands.add('checkAlertText', (text) => {
    cy.on('window:alert', (alertText) => {
        expect(alertText).to.contain(text);
    });
});
// Custom command to wait for element to be visible
Cypress.Commands.add('waitForElement', (selector, timeout = 10000) => {
    cy.get(selector, { timeout }).should('be.visible');
});
// Custom command to wait for element to not exist
Cypress.Commands.add('waitForElementToDisappear', (selector, timeout = 10000) => {
    cy.get(selector, { timeout }).should('not.exist');
});
//# sourceMappingURL=data:application/json;base64,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
