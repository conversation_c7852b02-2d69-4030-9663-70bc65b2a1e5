/// <reference types="cypress" />

declare global {
  namespace Cypress {
    interface Chainable {
      getByTestId(testId: string): Chainable<JQuery<HTMLElement>>;
      login(email: string, password: string): Chainable<void>;
      loginAs(email: string, password: string, options?: { remember?: boolean; redirectTo?: string }): Chainable<void>;
      logout(): Chainable<void>;
      isAuthenticated(): Chainable<void>;
      waitForApi(url: string, method?: string): Chainable<void>;
      waitForElement(selector: string, timeout?: number): Chainable<JQuery<HTMLElement>>;
      waitForElementToDisappear(selector: string, timeout?: number): Chainable<JQuery<HTMLElement>>;
      fillField(selector: string, value: string): Chainable<JQuery<HTMLElement>>;
      injectAxe(): Chainable<void>;
      checkA11y(context?: string, options?: Record<string, unknown>): Chainable<void>;
    }
  }
}

export {};
