# Cypress E2E Testing

This directory contains end-to-end tests for the NWA Member Portal using Cypress.

## Setup

Cypress has been installed and configured for the project. The following dependencies have been added:

- `cypress` - The main testing framework
- `@types/cypress` - TypeScript definitions for Cypress
- `start-server-and-test` - Utility to start the server and run tests

## Configuration

- **Config file**: `cypress.config.ts` - Main Cypress configuration
- **Base URL**: `http://localhost:3001` (matches the dev server port)
- **Test files**: Located in `cypress/e2e/` directory
- **Support files**: Located in `cypress/support/` directory

## Available Scripts

```bash
# Open Cypress Test Runner (interactive mode)
npm run cypress:open

# Run all tests in headless mode
npm run cypress:run

# Run tests with browser UI visible
npm run cypress:run:headed

# Run tests with recording (for CI/CD)
npm run cypress:run:record

# Run e2e tests with server startup (recommended for development)
npm run test:e2e

# Run e2e tests with browser UI (for debugging)
npm run test:e2e:headed
```

## Test Structure

### Basic Tests (`basic-tests.cy.js`)
- Homepage loading
- Navigation functionality
- Login page access
- Dashboard access
- Settings page functionality
- API endpoint testing

### Custom Commands
The following custom commands are available in `cypress/support/commands.ts`:

- `cy.loginAs(email, password, { remember?, redirectTo? })` - Login with credentials via the live UI flow
- `cy.isAuthenticated()` - Check if user is authenticated
- `cy.logout()` - Logout current user
- `cy.waitForApi(url, method)` - Wait for API response
- `cy.checkPageTitle(title)` - Verify page title
- `cy.containsText(selector, text)` - Check if element contains text
- `cy.shouldExist(selector)` - Check if element exists
- `cy.shouldBeVisible(selector)` - Check if element is visible
- `cy.fillField(selector, value)` - Fill form field
- `cy.selectOption(selector, option)` - Select dropdown option
- `cy.shouldBeChecked(selector)` - Check if checkbox is checked
- `cy.waitForElement(selector, timeout)` - Wait for element to be visible

## Running Tests

### Development Mode
```bash
# Start the development server
npm run dev

# In another terminal, run the e2e tests
npm run test:e2e
```

### Production Mode
```bash
# Build the application
npm run build

# Start the production server
npm start

# In another terminal, run the e2e tests
npm run test:e2e
```

### Interactive Mode
```bash
# Open Cypress Test Runner
npm run cypress:open
```

## Test Examples

### Testing User Authentication
```javascript
describe('User Authentication', () => {
  it('should login successfully', () => {
    cy.visit('/login')
    cy.get('input[type="email"]').type('<EMAIL>')
    cy.get('input[type="password"]').type('password123')
    cy.get('button[type="submit"]').click()
    cy.url().should('include', '/dashboard')
    cy.isAuthenticated()
  })
})
```

### Testing API Endpoints
```javascript
describe('API Tests', () => {
  it('should return user data', () => {
    cy.request('/api/users').then((response) => {
      expect(response.status).to.eq(200)
      expect(response.body).to.have.property('users')
    })
  })
})
```

### Testing UI Components
```javascript
describe('Settings Page', () => {
  it('should display permission tabs', () => {
    cy.visit('/settings')
    cy.contains('Local Permissions').click()
    cy.contains('Assign Roles').should('be.visible')
    cy.contains('Assign Permissions').should('be.visible')
  })
})
```

## Environment Variables

The following environment variables are used in the Cypress configuration:

- `CYPRESS_BASE_URL` - Base URL for tests (default: http://localhost:3001)
- `CYPRESS_API_URL` - API base URL (default: http://localhost:3001/api)

## Troubleshooting

### Common Issues

1. **Port already in use**: Make sure no other process is using port 3001
2. **Authentication issues**: Tests may need to be updated based on your auth setup
3. **Database connection**: Ensure the database is running for API tests
4. **Missing dependencies**: Run `npm install` to ensure all dependencies are installed

### Debug Mode
To run tests in debug mode with browser UI:
```bash
npm run test:e2e:headed
```

### Skipping Tests
To skip specific tests during development:
```javascript
describe.skip('Skipped Tests', () => {
  // Tests in here will be skipped
})
```

## Best Practices

1. **Test Isolation**: Each test should be independent
2. **Live Auth Flows**: Use `cy.loginAs`/`cy.logout` so middleware, cookies, and redirects mirror production behaviour.
3. **Page Objects**: Use page objects for complex UI interactions
4. **API Testing**: Test API endpoints directly when possible
5. **Visual Testing**: Use screenshots for UI regression testing
6. **CI/CD Integration**: Configure tests to run in your CI/CD pipeline

## Next Steps

1. Run the basic tests to ensure setup is working
2. Add authentication tests for your specific login flow
3. Create tests for critical user journeys
4. Set up CI/CD integration for automated testing
5. Add visual regression testing if needed
