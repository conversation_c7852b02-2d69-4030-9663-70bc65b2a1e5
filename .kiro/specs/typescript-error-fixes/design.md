# Design Document

## Overview

This design addresses TypeScript compilation errors across the codebase by systematically fixing database schema mismatches, missing API exports, test mocking issues, and component interface inconsistencies. The approach focuses on aligning code with the actual Prisma schema and ensuring type safety throughout the application.

## Architecture

The fix strategy is organized into four main categories:

1. **Database Schema Alignment** - Update test files to match the current Prisma schema
2. **API Route Export Consistency** - Ensure all expected exports are available
3. **Test Mock Type Safety** - Fix Jest mock type mismatches
4. **Component Interface Consistency** - Align component props with their interfaces

## Components and Interfaces

### Database Schema Fixes

**Problem Analysis:**
- Tests reference `treatyTypeId` field on Treaty model, but schema uses many-to-many relationship via `TreatyTreatyType`
- Tests reference `treatyTypeDetails` model that doesn't exist in schema
- Tests use incorrect field names for relationships

**Solution:**
- Replace direct `treatyTypeId` references with proper many-to-many relationship handling
- Remove references to non-existent `treatyTypeDetails` model
- Update test data creation to use correct field names and relationships

### API Route Export Fixes

**Problem Analysis:**
- Missing POST export in user password route
- Missing route file for admin user projects

**Solution:**
- Add missing exports to existing route files
- Create missing route files with proper exports
- Ensure consistent naming conventions

### Test Mock Type Safety

**Problem Analysis:**
- Jest mocks have incorrect type signatures
- Mock return values don't match expected types
- NextResponse.json mock implementation issues

**Solution:**
- Use proper Jest mock typing with `jest.MockedFunction`
- Ensure mock return types match original function signatures
- Fix NextResponse mock implementation

### Component Interface Consistency

**Problem Analysis:**
- Header component tests pass props that don't exist in interface
- Component interfaces don't match actual usage

**Solution:**
- Update component interfaces to match expected usage
- Remove unused props from test files
- Ensure consistent prop naming

## Data Models

### Treaty-TreatyType Relationship

Current schema uses many-to-many relationship:
```prisma
model Treaty {
  treatyTreatyTypes TreatyTreatyType[]
}

model TreatyType {
  treatyTreatyTypes TreatyTreatyType[]
}

model TreatyTreatyType {
  treatyId     String
  treatyTypeId String
  treaty       Treaty     @relation(fields: [treatyId], references: [id])
  treatyType   TreatyType @relation(fields: [treatyTypeId], references: [id])
}
```

Tests should use this relationship instead of direct `treatyTypeId` field.

### User Profile Relationships

Schema uses proper foreign key relationships:
```prisma
model UserProfile {
  countryId Int?
  cityId    Int?
  country   Country? @relation(fields: [countryId], references: [id])
  city      City?    @relation(fields: [cityId], references: [id])
}
```

Tests should create related records properly.

## Error Handling

### Database Operation Errors
- Wrap database operations in try-catch blocks
- Provide meaningful error messages for schema mismatches
- Ensure proper cleanup in test teardown

### Type Safety Errors
- Use TypeScript strict mode compliance
- Implement proper type guards where needed
- Ensure all function signatures match their implementations

### Mock Implementation Errors
- Validate mock return types against original functions
- Use proper Jest typing utilities
- Ensure mock implementations don't break at runtime

## Testing Strategy

### Database Test Fixes
1. Update all treaty-related tests to use many-to-many relationships
2. Fix user profile creation to use proper foreign key relationships
3. Remove references to non-existent models
4. Ensure proper test data cleanup

### Mock Test Fixes
1. Replace generic Jest mocks with properly typed versions
2. Ensure mock return values match expected types
3. Fix NextResponse mock implementation
4. Validate all service mocks have correct signatures

### Component Test Fixes
1. Update component test props to match interfaces
2. Remove unused prop references
3. Ensure consistent component usage patterns

### Integration Test Validation
1. Verify all database operations work with actual schema
2. Test API routes with proper exports
3. Validate component rendering with correct props
4. Ensure end-to-end type safety

## Implementation Phases

### Phase 1: Database Schema Alignment
- Fix all treaty-related test files
- Update user profile test creation
- Remove non-existent model references

### Phase 2: API Route Consistency
- Add missing exports to existing routes
- Create missing route files
- Validate all import statements

### Phase 3: Test Mock Type Safety
- Fix Jest mock implementations
- Update service mock types
- Ensure NextResponse mock works correctly

### Phase 4: Component Interface Updates
- Update Header component interface
- Fix component test props
- Validate component usage consistency

### Phase 5: Validation and Testing
- Run TypeScript compiler to verify fixes
- Execute test suites to ensure functionality
- Perform integration testing