# Implementation Plan

- [ ] 1. Fix database schema mismatches in treaty-related tests
  - Update treaty test files to use proper many-to-many relationships instead of direct treatyTypeId references
  - Replace treatyTypeId field usage with TreatyTreatyType junction table operations
  - Remove references to non-existent treatyTypeDetails model
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 1.1 Fix treaties.test.ts schema mismatches
  - Replace treatyTypeId field with proper TreatyTreatyType relationship creation
  - Update treaty creation to use many-to-many relationship pattern
  - Fix treaty include statements to use correct relationship names
  - _Requirements: 2.1, 2.2_

- [ ] 1.2 Fix treaty-attachments.test.ts schema mismatches
  - Remove treatyTypeId field usage from treaty creation
  - Update test to create treaties without direct treaty type reference
  - Use proper relationship creation if treaty type association is needed
  - _Requirements: 2.1, 2.2_

- [ ] 1.3 Fix treaty-type-details test files
  - Remove all references to non-existent treatyTypeDetails model
  - Update treaty type tests to use actual schema fields
  - Remove treatyId field from TreatyType model operations
  - _Requirements: 2.1, 2.2_

- [ ] 2. Fix user profile and database relationship issues
  - Update user profile creation in tests to use proper foreign key relationships
  - Fix country and city field assignments to use nested create or connect operations
  - Remove account model references that don't exist in current schema
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 2.1 Fix database.test.ts user profile creation
  - Replace string country/city assignments with proper relationship creation
  - Use connect or create operations for country and city relationships
  - Remove non-existent account model operations
  - _Requirements: 2.1, 2.2_

- [ ] 3. Fix missing API route exports
  - Add missing POST export to user password route
  - Create missing admin user projects route file
  - Ensure all route files have consistent export patterns
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 3.1 Fix user password route missing export
  - Add POST export to src/app/api/user/password/route.ts
  - Ensure export name matches import expectations
  - Verify route functionality is not affected
  - _Requirements: 3.1, 3.2_

- [ ] 3.2 Create missing admin user projects route
  - Create src/app/api/admin/users/[userId]/projects/[projectId]/route.ts file
  - Implement basic route structure with proper exports
  - Add placeholder implementations for expected HTTP methods
  - _Requirements: 3.1, 3.2_

- [ ] 4. Fix Jest mock type safety issues
  - Replace generic Jest mocks with properly typed MockedFunction implementations
  - Fix NextResponse.json mock to have correct type signature
  - Ensure all service mocks return values compatible with original function types
  - _Requirements: 4.1, 4.2, 4.3_

- [ ] 4.1 Fix auth-middleware.test.ts mock implementations
  - Replace jest.fn() with jest.MockedFunction for all service mocks
  - Fix NextResponse.json mock implementation to have proper typing
  - Ensure mock return values match expected function signatures
  - _Requirements: 4.1, 4.2_

- [ ] 5. Fix component interface consistency issues
  - Update Header component interface to match test expectations or remove unused test props
  - Ensure component prop types are consistent across usage sites
  - Fix any component test prop mismatches
  - _Requirements: 5.1, 5.2, 5.3_

- [ ] 5.1 Fix Header component test prop mismatches
  - Remove isSidebarCollapsed and onToggleSidebar props from Header tests
  - Update Header component interface if these props are actually needed
  - Ensure test props match actual component interface
  - _Requirements: 5.1, 5.2_

- [ ] 6. Fix remaining type safety issues
  - Fix permission-sync-service.test.ts undefined data access
  - Add proper type guards for potentially undefined values
  - Ensure all function return types are properly handled
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 6.1 Fix permission-sync-service undefined access
  - Add type guard or assertion for result.data before accessing permissions
  - Ensure test handles undefined data scenarios properly
  - Update test to match actual service return type structure
  - _Requirements: 1.1, 4.1_

- [ ] 7. Validate all fixes with TypeScript compilation
  - Run npx tsc --noEmit to verify all errors are resolved
  - Fix any remaining compilation errors discovered
  - Ensure no new type errors are introduced
  - _Requirements: 1.1, 1.2, 1.3_