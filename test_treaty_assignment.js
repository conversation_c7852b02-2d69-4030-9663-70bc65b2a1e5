// Test treaty number assignment process
const testUserId = 'cmfbrh33h002appsj5bf3uh1j'; // <PERSON>
const treatyTypeId = 'cf6b3706-bab9-4561-baa9-5513ca01485f'; // Medium Charitable Trusts

async function testTreatyAssignment() {
  try {
    console.log('Testing treaty number assignment process...');
    console.log('User ID:', testUserId);
    console.log('Treaty Type ID:', treatyTypeId);
    
    // First, check current state before assignment
    const beforeResponse = await fetch(`http://localhost:3001/api/users/${testUserId}/treaties`);
    const beforeData = await beforeResponse.json();
    console.log('Treaties before assignment:', beforeData.treaties?.length || 0);
    
    // Assign a new treaty number
    const assignResponse = await fetch(`http://localhost:3001/api/users/${testUserId}/treaty-numbers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        treatyNumber: 'mct123', // Medium Charitable Trusts
        treatyTypeId: treatyTypeId
      })
    });
    
    console.log('Assignment response status:', assignResponse.status);
    
    if (assignResponse.ok) {
      const assignData = await assignResponse.json();
      console.log('Assignment successful:', assignData);
      
      // Check state after assignment
      const afterResponse = await fetch(`http://localhost:3001/api/users/${testUserId}/treaties`);
      const afterData = await afterResponse.json();
      console.log('Treaties after assignment:', afterData.treaties?.length || 0);
      
      // Check treaty numbers
      const treatyNumbersResponse = await fetch(`http://localhost:3001/api/users/${testUserId}/treaty-numbers`);
      const treatyNumbersData = await treatyNumbersResponse.json();
      console.log('Treaty numbers after assignment:', treatyNumbersData.treatyNumbers?.length || 0);
      
      // Verify the UserTreaty record was created correctly
      console.log('\\n=== VERIFICATION RESULTS ===');
      console.log('✓ Treaty number assigned successfully');
      console.log('✓ UserTreaty record should be linked to Peace & Trade Treaty');
      console.log('✓ Treaty should appear in selection dropdown');
      
    } else {
      const errorText = await assignResponse.text();
      console.error('Assignment failed:', errorText);
    }
    
  } catch (error) {
    console.error('Error testing treaty assignment:', error);
  }
}

// Run the test
testTreatyAssignment();