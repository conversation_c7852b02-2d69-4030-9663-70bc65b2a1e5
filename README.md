# NWA Member Portal

A comprehensive member management system built with Next.js 15, TypeScript, and shadcn/ui.

## 🚀 Features

- **Secure Authentication**: NextAuth.js integration with database sessions
- **External API Authentication**: API key and JWT-based authentication for external services
- **Modern UI**: shadcn/ui components with custom NWA branding
- **Database Integration**: PostgreSQL with Prisma ORM
- **Type Safety**: Full TypeScript implementation
- **Responsive Design**: Mobile-first approach with Tailwind CSS

## 🛠 Tech Stack

- **Framework**: Next.js 15.1 with App Router
- **Language**: TypeScript 5.7
- **Styling**: Tailwind CSS 3.4 + shadcn/ui
- **Database**: PostgreSQL (Docker)
- **Cache**: Redis (Docker)
- **Storage**: MinIO S3-compatible (Docker)
- **ORM**: Prisma 6.1
- **Authentication**: NextAuth.js 4.24

## 🔒 Security & Audit Logging

The NWA Member Portal includes comprehensive audit logging for security and compliance:

- **User Activity Tracking**: All user creation, updates, and deletions are logged
- **Detailed Change Logging**: Field-level tracking of what changed and who made the change
- **Authentication Events**: Login, logout, and token refresh events are recorded
- **API Access Logging**: All API requests are logged with timestamps and metadata
- **Geographic Tracking**: IP addresses and user agent information for security analysis
- **Compliance Ready**: Structured logs suitable for compliance reporting

For implementation details, see the [Audit Logging Documentation](docs/audit-logging.md).

## 🗃 Database Schema

For detailed information about the Prisma schema and database models, see [PRISMA_SCHEMA.md](PRISMA_SCHEMA.md).

This includes:
- Complete documentation of all database models
- OAuth 2.0 implementation details
- Setup and migration instructions
- Seeding data configuration
- Development guidelines

## 📦 Infrastructure Setup

The application uses a complete Docker stack:

### PostgreSQL Database
- **Database**: `nwa_member_portal`
- **User**: `nwa_user`
- **Port**: `5432`

### Redis Cache
- **Port**: `6379`
- **Usage**: Session storage, caching, rate limiting

### MinIO Object Storage
- **Ports**: `9000-9001`
- **Usage**: File uploads, profile photos, document storage
- **Admin UI**: http://localhost:9001

All services are configured in `.env.local`.

## 🎨 Design System

The application features a custom design system with:

- **Primary Colors**: Emerald green (#10b981)
- **Typography**: Inter + Space Grotesk fonts
- **Components**: Glass morphism effects and gradient backgrounds
- **Accessibility**: Focus states and screen reader support

## 🚦 Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

3. **Open browser**:
   Navigate to [http://localhost:3005](http://localhost:3005)

## 📁 Project Structure

```
├── .agent-os/                 # Agent OS documentation
│   ├── product/              # Product specifications
│   └── instructions/         # Development guidelines
├── src/
│   ├── app/                  # Next.js App Router
│   ├── components/           # React components
│   │   └── ui/              # shadcn/ui components
│   └── lib/                 # Utility functions
├── backup/                   # Original design files
└── ...config files
```

## 🔐 Environment Variables

Copy `.env.example` to `.env.local` and configure:

```env
DATABASE_URL="postgresql://nwa_user:nwa_secure_password_2025@localhost:5432/nwa_member_portal"
REDIS_URL="redis://localhost:6379"
MINIO_ENDPOINT="localhost"
MINIO_PORT="9000"
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key"
```

## 📋 Development Roadmap

See `.agent-os/product/roadmap.md` for detailed development phases:

- **Phase 1**: Authentication & Profile System
- **Phase 2**: Role Management
- **Phase 3**: Ordinances & Treaties
- **Phase 4**: Admin Dashboard
- **Phase 5**: External API Authentication System
- **Phase 6**: Polish & Advanced Features

## 🏗 Architecture Decisions

All technical decisions are documented in `.agent-os/product/decisions.md`, including:

- Next.js full-stack architecture choice
- Security implementation with NextAuth.js
- External API authentication with API keys and JWT tokens
- Database-first development approach
- Component library selection

## 🤝 Contributing

This project follows the Agent OS framework for structured development. See `.agent-os/instructions/` for development guidelines.

---

**NWA Member Portal** - Peace, Love, Prosperity for Humanity