const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkSessions() {
  try {
    const sessionCount = await prisma.session.count();
    console.log(`Total sessions in database: ${sessionCount}`);

    if (sessionCount > 0) {
      const sessions = await prisma.session.findMany({
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        },
        take: 5
      });
      console.log('Sample sessions:', JSON.stringify(sessions, null, 2));
    }
  } catch (error) {
    console.error('Error checking sessions:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSessions();