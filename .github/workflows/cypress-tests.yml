name: Cypress E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  cypress-run:
    runs-on: ubuntu-latest
    timeout-minutes: 60

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
          POSTGRES_DB: nwa_test
        options: >-
          --health-cmd "pg_isready -U postgres"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    strategy:
      fail-fast: false
      matrix:
        node-version: [18.x, 20.x]
        spec-group:
          - cypress/e2e/basic-tests.cy.ts,cypress/e2e/auth-tests.cy.ts,cypress/e2e/auth-verification.cy.ts
          - cypress/e2e/system-settings-tests.cy.ts,cypress/e2e/setup-2fa-tests.cy.ts,cypress/e2e/user-management-tests.cy.ts
          - cypress/e2e/dashboard-analytics-tests.cy.ts,cypress/e2e/role-based-access-control-tests.cy.ts,cypress/e2e/api-integration-tests.cy.ts,cypress/e2e/treaty-management-tests.cy.ts,cypress/e2e/infrastructure-verification.cy.ts

    env:
      DATABASE_URL: postgresql://postgres:password@localhost:5432/nwa_test
      REDIS_URL: redis://localhost:6379
      NEXTAUTH_SECRET: cypress-e2e-secret-key-**********
      NEXTAUTH_URL: http://localhost:3001
      NODE_ENV: test

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run database migrations
      run: npx prisma migrate deploy

    - name: Seed database
      run: npm run prisma:seed

    - name: Build application
      run: npm run build

    - name: Run Cypress tests
      uses: cypress-io/github-action@v6
      with:
        install: false
        spec: ${{ matrix.spec-group }}
        start: npm start
        wait-on: 'http://localhost:3001'
        wait-on-timeout: 120
        browser: chrome
        headed: false
        record: true
        parallel: true
        group: 'E2E Tests'
      env:
        CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        PORT: 3001

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: cypress-results-${{ matrix.node-version }}
        path: |
          cypress/screenshots/
          cypress/videos/
          cypress/results/

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      if: always()
      with:
        file: ./coverage/lcov.info
        flags: e2e
        name: cypress-tests
