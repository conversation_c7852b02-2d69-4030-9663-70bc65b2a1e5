# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Development Commands

### Core Development
```bash
# Start development server (runs on port 3001)
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Lint code
npm run lint
```

### Testing
```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Generate test coverage report
npm run test:coverage

# Verify API endpoints
npm run verify-api
```

### Database & Prisma
```bash
# Generate Prisma client
npm run prisma:generate

# Run database migrations (development)
npm run prisma:migrate

# Deploy migrations (production)
npm run prisma:deploy

# Open Prisma Studio (database browser)
npm run prisma:studio

# Seed database with initial data
npm run prisma:seed

# Apply migrations using custom script
npm run migrate

# Generate new migration
npm run migrate:generate
```

### Infrastructure
```bash
# Start Docker services (PostgreSQL, Redis, MinIO)
docker-compose up -d

# Stop Docker services
docker-compose down

# View container logs
docker-compose logs -f [service-name]
```

## Architecture Overview

### Tech Stack
- **Framework**: Next.js 15.1 with App Router
- **Language**: TypeScript 5.7
- **Database**: PostgreSQL with Prisma 6.1 ORM
- **Authentication**: NextAuth.js 4.24 with custom credentials provider
- **Caching**: Redis with connection pooling
- **Storage**: MinIO S3-compatible object storage
- **UI**: Tailwind CSS 3.4 + shadcn/ui components
- **Testing**: Jest with React Testing Library

### Key Architectural Patterns

**Full-Stack Next.js Architecture**
- App Router with server components and API routes
- Middleware for global security, rate limiting, and 2FA enforcement
- Server-side rendering and client-side hydration

**Authentication & Authorization**
- NextAuth.js with Prisma adapter for session management
- Role-based access control (RBAC) with flexible permissions
- OAuth 2.0 server implementation for external API authentication
- Two-factor authentication support with backup codes

**Database-First Development**
- Comprehensive Prisma schema with 25+ models
- NextAuth.js integration with User, Account, Session, VerificationToken models
- Complex relationships: Users → Profiles → Roles → Permissions
- Organizational hierarchy: Positions, Titles, TitlePositions
- Content management: Treaties, Ordinances with file attachments
- External API authentication: Projects, Scopes, UserProjectScopes
- Comprehensive audit logging for all operations

**Security & Compliance**
- Comprehensive audit logging with IP tracking and geographic data
- Rate limiting and CORS protection
- Password hashing with bcrypt
- API key authentication for external services
- Session-based and JWT token authentication

### Directory Structure

**Application Code**
- `src/app/` - Next.js App Router pages and API routes
- `src/components/` - Reusable React components (shadcn/ui based)
- `src/lib/` - Utility functions, authentication, services
- `src/middleware.ts` - Global middleware for security and authentication

**Database & Infrastructure**
- `prisma/` - Database schema, migrations, and seed files
- `docker-compose.yml` - Multi-service containerized development environment
- `.env.local` - Environment configuration (not in git)

**Testing & Scripts**
- `tests/` - Test files organized by feature
- `scripts/` - Database utilities and deployment scripts
- `__tests__/` - Additional test files

### Key Features

**Multi-Tenant Architecture**
- Support for external API consumers via OAuth 2.0
- Project-based API key authentication
- Granular scope-based permissions for external access

**Organizational Management**
- Hierarchical position system with parent-child relationships
- Title-position mapping for diplomatic roles
- User-position assignments with time tracking
- Peace Ambassador and Trade Treaty number management

**Document & Content Management**
- Treaty and Ordinance tracking with status management
- File attachment support via MinIO object storage
- Bulk upload system with approval workflow

**Geographic & Demographic Data**
- Country and city management with relationships
- Category and subcategory system for content organization
- Full demographic tracking in user profiles

### Development Guidelines

**Claude Documentation Rule**
- Don't use `pkill` command - use `netstat` and `lsof -ti :port,port | xargs kill -9` instead
- If that fails, use `netstat port` to find the PID manually

**Database Development**
- Always create migrations for schema changes: `npx prisma migrate dev --name descriptive_name`
- Update seed script when adding new required data
- Use Prisma Studio for database inspection: `npm run prisma:studio`
- Test database changes with `npm run test` before committing

**API Development**
- All API routes include comprehensive audit logging
- Authentication middleware is applied globally via `src/middleware.ts`
- External API authentication supports both API keys and OAuth 2.0 flows
- Rate limiting is enforced at the middleware level

**Component Development**
- Use shadcn/ui components as base building blocks
- Follow the established glass morphism and gradient design patterns
- Maintain accessibility with proper focus states and ARIA attributes
- Components are organized by feature in `src/components/`

**Environment Setup**
- Copy `.env.example` to `.env.local` and configure database URLs
- Development server runs on port 3001 (configurable)
- Docker services use custom ports: PostgreSQL (5435), Redis (6370), MinIO (9002-9003)

### Special Configurations

**Next.js Configuration**
- Optimized package imports for lucide-react and Radix UI
- Custom webpack watch options to ignore build artifacts
- Standalone output for containerized deployments
- Image optimization with WebP and AVIF support

**Testing Configuration**
- Jest with TypeScript support and React Testing Library
- Custom test environment with database setup/teardown
- Path mapping for `@/` imports
- Comprehensive coverage collection from `src/` directory

### Production Deployment

**Database Preparation**
```bash
npx prisma generate
npx prisma migrate deploy
npm run prisma:seed  # if needed
```

**Docker Deployment**
```bash
docker-compose up -d postgres redis minio  # Start infrastructure
docker-compose up app  # Start application
```

The application uses a comprehensive multi-service architecture with strong security, audit logging, and support for both internal user management and external API consumers.
