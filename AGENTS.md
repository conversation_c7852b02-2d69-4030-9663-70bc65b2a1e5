<!-- FAST-TOOLS PROMPT v1 | codex-mastery | watermark:do-not-alter -->

## CRITICAL: Use ripgrep, not grep

NEVER use grep for project-wide searches (slow, ignores .gitignore). ALWAYS use rg.

- `rg "pattern"` — search content
- `rg --files | rg "name"` — find files
- `rg -t python "def"` — language filters

## File finding

- Prefer `fd` (or `fdfind` on Debian/Ubuntu). Respects .gitignore.

## JSON

- Use `jq` for parsing and transformations.

## Install Guidance

- macOS: `brew install ripgrep fd jq`
- Debian/Ubuntu: `sudo apt update && sudo apt install -y ripgrep fd-find jq` (alias `fd=fdfind`)

## Agent Instructions

- Replace commands: grep→rg, find→rg --files/fd, ls -R→rg --files, cat|grep→rg pattern file
- Cap reads at 250 lines; prefer `rg -n -A 3 -B 3` for context
- Use `jq` for JSON instead of regex

# Icon Integration & Security Requirements for Agents

## MANDATORY: Icon Integration Protocol

**CRITICAL REQUIREMENT**: All Lucide React icons MUST be wired into the UI using Tailwind CSS. Never leave icons as unused imports.

### Icon Implementation Checklist

When working with components containing icon imports:

1. **Identify unused icons**: Check ESLint output for unused import warnings
2. **Locate appropriate UI elements**: Headers, buttons, status indicators, cards
3. **Apply standard pattern**:
   ```tsx
   <IconName className="h-4 w-4 mr-2 text-color-name" />
   ```
4. **Verify icon visibility**: Ensure icon renders in the UI
5. **Test lint compliance**: Run `npm run lint` to verify no unused imports

### Standard Icon Classes by Context

- **Headers**: `h-6 w-6 mr-3 text-blue-600`
- **Buttons**: `h-4 w-4 mr-2` (inherited button text color)
- **Status indicators**: `h-5 w-5 mr-2 text-green-600/red-600/yellow-600`
- **Card titles**: `h-5 w-5 mr-2 text-gray-600`

### Required Parent Container Classes

Always wrap icon-text combinations with:
```tsx
<div className="flex items-center">
  <IconName className="h-4 w-4 mr-2" />
  <span>Text</span>
</div>
```

## MANDATORY: Feature-Based Directory Architecture

**CRITICAL REQUIREMENT**: Agents MUST organize code using feature-based directories. Never create files over 400 lines.

### Directory Structure Template

When creating new features or refactoring existing code:

```bash
src/app/admin/manage/[feature]/
├── components/
│   ├── tabs/                     # Tab-based components (max 300 lines)
│   │   ├── CreateFeatureTab.tsx
│   │   ├── UpdateFeatureTab.tsx
│   │   └── SearchFeatureTab.tsx
│   ├── forms/                    # Form components (max 250 lines)
│   │   ├── FeatureForm.tsx
│   │   └── FeatureSearchForm.tsx
│   ├── ui/                       # Feature-specific UI components (max 200 lines)
│   │   ├── FeatureCard.tsx
│   │   └── FeatureList.tsx
│   └── sections/                 # Page sections (max 300 lines)
│       ├── FeatureOverview.tsx
│       └── FeatureStats.tsx
├── types/
│   ├── feature.types.ts          # Feature-specific types (max 100 lines)
│   ├── feature-api.types.ts      # API response types (max 100 lines)
│   └── feature-form.types.ts     # Form-related types (max 80 lines)
├── services/
│   ├── feature-service.ts        # Business logic services (max 200 lines)
│   └── feature-api.service.ts    # API service layer (max 200 lines)
├── hooks/
│   ├── use-feature-data.ts       # Data fetching hooks (max 150 lines)
│   └── use-feature-form.ts       # Form management hooks (max 150 lines)
├── utils/
│   ├── feature-validation.ts     # Zod validation schemas (max 150 lines)
│   └── feature-helpers.ts        # Utility functions (max 150 lines)
└── constants/
    └── feature-constants.ts      # Feature constants (max 100 lines)
```

### File Creation Rules for Agents

#### Before Creating Any File

1. **Check existing structure**: Use `rg` to find similar patterns
2. **Identify feature boundaries**: Does this belong to an existing feature?
3. **Plan file organization**: Create directory structure before files
4. **Verify file size limits**: Plan to keep files under 400 lines

#### File Size Enforcement

**MANDATORY**: Agents must monitor file size and refactor when limits are exceeded:

```bash
# Check file sizes
find src/ -name "*.tsx" -o -name "*.ts" | xargs wc -l | sort -n | tail -10

# Files over 400 lines MUST be refactored
```

#### Refactoring Protocol

When files exceed 400 lines:

1. **Stop development** - Don't add more code to oversized files
2. **Create feature directory structure** following the template
3. **Extract logical components** into separate files
4. **Update imports systematically**
5. **Test each extracted component**
6. **Update documentation**

### Import Organization Requirements

Agents must organize imports in this exact order:

```tsx
// 1. External libraries (React, Next.js, etc.)
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

// 2. Internal types (type-only imports)
import type { User, UserProfile } from './types/user.types';

// 3. Internal components
import { UserCard } from './components/ui/UserCard';
import { UserForm } from './components/forms/UserForm';

// 4. Internal services/hooks/utils
import { userService } from './services/user-service';
import { useUserData } from './hooks/use-user-data';
```

### Naming Convention Enforcement

- **Components**: PascalCase with descriptive names
  - ✅ `UserManagementTab.tsx`
  - ❌ `usertab.tsx`

- **Services**: kebab-case with `-service` suffix
  - ✅ `user-service.ts`
  - ❌ `userService.ts`

- **Types**: Descriptive with `.types.ts` suffix
  - ✅ `user-api.types.ts`
  - ❌ `types.ts`

- **Hooks**: `use-` prefix with camelCase
  - ✅ `useUserData.ts`
  - ❌ `userData.ts`

## Security & Type Safety Requirements

### TypeScript Security Enforcement

**STRICT PROHIBITION**: Never use `any` type. Agents must:

1. **Create proper interfaces** for all data structures
2. **Use Zod validation** for API responses and user input
3. **Implement type guards** for runtime type checking
4. **Use generic types** with proper constraints

### ESLint Security Rules Compliance

Agents must follow these critical ESLint rules:

1. **@typescript-eslint/no-unused-vars** - All variables must be used
2. **@typescript-eslint/no-explicit-any** - Zero tolerance for `any` types
3. **@typescript-eslint/consistent-type-imports** - Use `import type` for type-only imports
4. **no-case-declarations** - Use block scopes in switch cases
5. **no-constant-condition** - Avoid infinite loops without breaks
6. **no-undef** - All variables must be defined

### Secure Coding Patterns

#### API Response Handling
```tsx
// ✅ REQUIRED PATTERN
import { z } from 'zod';

const ResponseSchema = z.object({
  id: z.string(),
  data: z.array(z.unknown()),
});

const response = await fetch('/api/data');
const jsonData = await response.json();
const validatedData = ResponseSchema.parse(jsonData);
```

#### Input Validation
```tsx
// ✅ REQUIRED PATTERN
const UserInputSchema = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email(),
});

const validatedInput = UserInputSchema.parse(userInput);
```

#### Error Handling
```tsx
// ✅ SECURE PATTERN
try {
  const result = await operation();
  return result;
} catch (error) {
  console.error('Operation failed:', error instanceof Error ? error.message : 'Unknown error');
  throw new Error('Operation failed');
}
```

## Agent Workflow Requirements

### Before Code Implementation

1. **Check ESLint status**: Run `npm run lint` to identify current issues
2. **Review existing patterns**: Use `rg` to find similar implementations
3. **Plan icon integration**: Identify all unused icons and their UI placement
4. **Design type interfaces**: Create proper TypeScript types

### During Implementation

1. **Wire all icons**: Every imported icon must be visible in UI
2. **Use proper TypeScript**: Avoid `any`, use proper interfaces
3. **Follow naming conventions**: Consistent with existing codebase
4. **Implement validation**: Use Zod for data validation

### After Implementation

1. **Run linting**: `npm run lint` - must pass without unused import errors
2. **Type checking**: `npm run typecheck` - must pass without type errors
3. **Build verification**: `npm run build` - must compile successfully
4. **Icon verification**: Visually confirm all icons are visible

### Search Patterns for Icon & File Organization Work

```bash
# Icon-related searches
# Find files with unused icon imports
rg "from 'lucide-react'" src/ -A 1 -B 1

# Check ESLint unused import warnings
npm run lint | grep "unused import"

# Find existing icon usage patterns
rg "className=\"h-[0-9] w-[0-9]" src/ -A 1 -B 1

# File organization searches
# Find files over 400 lines that need refactoring
find src/ -name "*.tsx" -o -name "*.ts" | xargs wc -l | awk '$1 > 400 {print $0}' | sort -nr

# Find large components by pattern
rg -l "export.*function.*Component" src/ | xargs wc -l | sort -n | tail -10

# Check import organization
rg "^import" src/ --type ts --type tsx -A 20

# Find duplicate functionality that could be consolidated
rg "interface.*User" src/ -A 5
rg "type.*User" src/ -A 5

# Find missing type-only imports
rg "^import.*{.*}" src/ --type ts --type tsx | grep -v "import type"
```

## Security Validation Checklist

Before completing any task, agents must verify:

- [ ] No `any` types used
- [ ] All variables are used or properly prefixed with `_`
- [ ] Proper input validation implemented
- [ ] Generic error messages (no internal details exposed)
- [ ] All icons wired with Tailwind CSS
- [ ] ESLint passes without security warnings
- [ ] TypeScript compilation succeeds
- [ ] Build process completes successfully

## Quality Assurance

### Code Review Points

1. **Icon Integration**: Every icon import has corresponding UI element
2. **Type Safety**: Proper TypeScript interfaces and validation
3. **Security**: Input validation and secure error handling
4. **ESLint Compliance**: All linting rules pass
5. **Best Practices**: Following established patterns and conventions

### Testing Requirements

1. **Lint Test**: `npm run lint` - zero errors/warnings
2. **Type Test**: `npm run typecheck` - zero errors
3. **Build Test**: `npm run build` - successful compilation
4. **Visual Test**: All icons visible and properly styled

<!-- END FAST-TOOLS PROMPT v1 | codex-mastery -->
