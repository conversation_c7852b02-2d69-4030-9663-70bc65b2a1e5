const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function migrateTreatyData() {
  console.log('🌱 Starting treaty data migration...')

  try {
    // Get the test user first
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (!testUser) {
      throw new Error('Test user not found')
    }

    // Create a temporary treaty ID for existing treaty types
    const tempTreatyId = 'temp-treaty-id-for-migration'

    // Update existing treaty types to have a temporary treaty ID so we can query them
    await prisma.$executeRaw`UPDATE treaty_types SET treaty_id = ${tempTreatyId} WHERE treaty_id IS NULL`

    console.log('✅ Updated existing treaty types with temporary treaty ID')

    // Get an existing treaty type to use
    const existingTreatyType = await prisma.treatyType.findFirst()
    if (!existingTreatyType) {
      throw new Error('No treaty types found in database')
    }

    // Create the main treaty
    const peaceAndTradeTreaty = await prisma.treaty.upsert({
      where: { name: 'Peace & Trade Treaty' },
      update: {},
      create: {
        name: 'Peace & Trade Treaty',
        description: 'The foundational treaty for peace and trade relationships within the NWA Alliance',
        status: 'ACTIVE',
      },
    })

    console.log('✅ Created Peace & Trade Treaty:', peaceAndTradeTreaty.id)

    // Update existing treaty types to reference the Peace & Trade Treaty
    const updateResult = await prisma.treatyType.updateMany({
      where: { treatyId: tempTreatyId },
      data: { treatyId: peaceAndTradeTreaty.id }
    })

    console.log('✅ Updated', updateResult.count, 'treaty types to reference Peace & Trade Treaty')

    if (testUser) {
      // Assign the test user to the treaty
      const testUserTreaty = await prisma.userTreaty.upsert({
        where: {
          userId_treatyId: {
            userId: testUser.id,
            treatyId: peaceAndTradeTreaty.id
          }
        },
        update: {},
        create: {
          userId: testUser.id,
          treatyId: peaceAndTradeTreaty.id,
          status: 'ACTIVE',
          notes: 'Automatically assigned during migration',
        },
      })

      console.log('✅ Assigned test user to treaty')

      // Find a treaty type to assign to the test user
      const membershipCovenant = await prisma.treatyType.findFirst({
        where: {
          name: 'Membership Covenant',
          treatyId: peaceAndTradeTreaty.id
        }
      })

      if (membershipCovenant) {
        await prisma.userTreatyType.upsert({
          where: {
            userId_treatyTypeId: {
              userId: testUser.id,
              treatyTypeId: membershipCovenant.id
            }
          },
          update: {},
          create: {
            userId: testUser.id,
            treatyTypeId: membershipCovenant.id,
            status: 'ACTIVE',
            notes: 'Automatically assigned during migration',
          },
        })

        console.log('✅ Assigned test user to treaty type')
      }
    }

    console.log('🎉 Treaty data migration completed successfully!')

  } catch (error) {
    console.error('❌ Error during treaty data migration:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

migrateTreatyData()