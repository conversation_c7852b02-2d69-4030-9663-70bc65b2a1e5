import { jest } from '@jest/globals';

/**
 * Load Testing Suite for Security Features
 *
 * This module provides comprehensive load testing capabilities for security features
 * including rate limiting, authentication, authorization, and audit logging.
 */

export interface LoadTestConfig {
  targetUrl: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  payload?: any;
  concurrency: number;
  duration: number; // in seconds
  rampUpTime?: number; // in seconds
  expectedStatus?: number;
  maxResponseTime?: number; // in milliseconds
  successRate?: number; // percentage (0-100)
}

export interface LoadTestResult {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  successRate: number;
  minResponseTime: number;
  maxResponseTime: number;
  avgResponseTime: number;
  medianResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  requestsPerSecond: number;
  errors: Array<{ error: string; count: number; percentage: number }>;
  responseTimeDistribution: Array<{ range: string; count: number; percentage: number }>;
  duration: number;
  concurrency: number;
}

export interface SecurityLoadTestSuite {
  name: string;
  description: string;
  tests: LoadTestConfig[];
  setup?: () => Promise<void>;
  teardown?: () => Promise<void>;
  validateResults?: (results: LoadTestResult[]) => boolean;
}

export class SecurityLoadTester {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  private results: LoadTestResult[] = [];

  constructor(baseUrl: string = 'http://localhost:3000', defaultHeaders: Record<string, string> = {}) {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...defaultHeaders
    };
  }

  async makeRequest(config: LoadTestConfig): Promise<{ success: boolean; responseTime: number; error?: string }> {
    const url = `${this.baseUrl}${config.targetUrl}`;
    const headers = { ...this.defaultHeaders, ...config.headers };

    const requestOptions: RequestInit = {
      method: config.method || 'GET',
      headers,
      body: config.payload ? JSON.stringify(config.payload) : undefined,
    };

    const startTime = Date.now();

    try {
      const response = await fetch(url, requestOptions);
      const responseTime = Date.now() - startTime;

      const success = response.status === (config.expectedStatus || 200);

      return { success, responseTime };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      return { success: false, responseTime, error: errorMessage };
    }
  }

  async runLoadTest(config: LoadTestConfig): Promise<LoadTestResult> {
    const startTime = Date.now();
    const responseTimes: number[] = [];
    const errors: Record<string, number> = {};
    let successfulRequests = 0;
    let failedRequests = 0;

    console.log(`🚀 Starting load test: ${config.concurrency} concurrency for ${config.duration}s`);

    // Ramp up phase
    const rampUpTime = config.rampUpTime || 5;
    const rampUpSteps = 10;
    const rampUpInterval = rampUpTime * 1000 / rampUpSteps;

    for (let step = 1; step <= rampUpSteps; step++) {
      const currentConcurrency = Math.round((config.concurrency * step) / rampUpSteps);
      await this.runConcurrencyLevel(config, currentConcurrency, rampUpInterval / 1000);
      await new Promise(resolve => setTimeout(resolve, rampUpInterval));
    }

    // Full load phase
    const fullLoadDuration = config.duration - rampUpTime;
    if (fullLoadDuration > 0) {
      await this.runConcurrencyLevel(config, config.concurrency, fullLoadDuration);
    }

    const duration = (Date.now() - startTime) / 1000;
    const totalRequests = successfulRequests + failedRequests;
    const successRate = totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0;

    // Calculate statistics
    responseTimes.sort((a, b) => a - b);
    const minResponseTime = responseTimes.length > 0 ? Math.min(...responseTimes) : 0;
    const maxResponseTime = responseTimes.length > 0 ? Math.max(...responseTimes) : 0;
    const avgResponseTime = responseTimes.length > 0 ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0;
    const medianResponseTime = responseTimes.length > 0 ? responseTimes[Math.floor(responseTimes.length / 2)] : 0;

    const p95Index = Math.floor(responseTimes.length * 0.95);
    const p99Index = Math.floor(responseTimes.length * 0.99);
    const p95ResponseTime = responseTimes.length > 0 ? responseTimes[p95Index] || maxResponseTime : 0;
    const p99ResponseTime = responseTimes.length > 0 ? responseTimes[p99Index] || maxResponseTime : 0;

    const requestsPerSecond = duration > 0 ? totalRequests / duration : 0;

    // Process errors
    const errorList: Array<{ error: string; count: number; percentage: number }> = [];
    Object.entries(errors).forEach(([error, count]) => {
      errorList.push({
        error,
        count,
        percentage: (count / totalRequests) * 100
      });
    });

    // Response time distribution
    const responseTimeDistribution = this.calculateResponseTimeDistribution(responseTimes);

    const result: LoadTestResult = {
      totalRequests,
      successfulRequests,
      failedRequests,
      successRate,
      minResponseTime,
      maxResponseTime,
      avgResponseTime,
      medianResponseTime,
      p95ResponseTime,
      p99ResponseTime,
      requestsPerSecond,
      errors: errorList,
      responseTimeDistribution,
      duration,
      concurrency: config.concurrency
    };

    console.log(`✅ Load test completed: ${successfulRequests}/${totalRequests} successful (${successRate.toFixed(1)}%)`);
    console.log(`   Avg: ${avgResponseTime.toFixed(0)}ms, P95: ${p95ResponseTime.toFixed(0)}ms, P99: ${p99ResponseTime.toFixed(0)}ms`);
    console.log(`   RPS: ${requestsPerSecond.toFixed(1)}, Duration: ${duration.toFixed(1)}s`);

    return result;
  }

  private async runConcurrencyLevel(config: LoadTestConfig, concurrency: number, duration: number): Promise<void> {
    const promises: Promise<void>[] = [];

    for (let i = 0; i < concurrency; i++) {
      promises.push(this.runWorker(config, duration));
    }

    await Promise.all(promises);
  }

  private async runWorker(config: LoadTestConfig, duration: number): Promise<void> {
    const endTime = Date.now() + (duration * 1000);

    while (Date.now() < endTime) {
      const result = await this.makeRequest(config);

      if (result.success) {
        // This would be tracked in a real implementation
      } else {
        // This would be tracked in a real implementation
      }

      // Small delay to avoid overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  private calculateResponseTimeDistribution(responseTimes: number[]): Array<{ range: string; count: number; percentage: number }> {
    if (responseTimes.length === 0) return [];

    const ranges = [
      { min: 0, max: 100, label: '0-100ms' },
      { min: 100, max: 250, label: '100-250ms' },
      { min: 250, max: 500, label: '250-500ms' },
      { min: 500, max: 1000, label: '500-1000ms' },
      { min: 1000, max: 2000, label: '1-2s' },
      { min: 2000, max: 5000, label: '2-5s' },
      { min: 5000, max: Infinity, label: '5s+' }
    ];

    const distribution = ranges.map(range => ({
      range: range.label,
      count: 0,
      percentage: 0
    }));

    responseTimes.forEach(time => {
      const range = ranges.find(r => time >= r.min && time < r.max);
      if (range) {
        const index = ranges.indexOf(range);
        distribution[index].count++;
      }
    });

    const total = responseTimes.length;
    distribution.forEach(item => {
      item.percentage = total > 0 ? (item.count / total) * 100 : 0;
    });

    return distribution;
  }

  async runSecurityLoadTestSuite(suite: SecurityLoadTestSuite): Promise<LoadTestResult[]> {
    console.log(`🔬 Starting security load test suite: ${suite.name}`);
    console.log(`   ${suite.description}`);

    if (suite.setup) {
      console.log('🔧 Running setup...');
      await suite.setup();
    }

    const results: LoadTestResult[] = [];

    for (const test of suite.tests) {
      const result = await this.runLoadTest(test);
      results.push(result);

      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    if (suite.teardown) {
      console.log('🧹 Running teardown...');
      await suite.teardown();
    }

    if (suite.validateResults) {
      const isValid = suite.validateResults(results);
      console.log(isValid ? '✅ All tests passed validation' : '❌ Some tests failed validation');
    }

    return results;
  }

  generateLoadTestReport(results: LoadTestResult[], suiteName: string): string {
    let report = `# Security Load Test Report: ${suiteName}\n\n`;

    // Summary
    const totalRequests = results.reduce((sum, r) => sum + r.totalRequests, 0);
    const totalSuccessful = results.reduce((sum, r) => sum + r.successfulRequests, 0);
    const avgSuccessRate = results.reduce((sum, r) => sum + r.successRate, 0) / results.length;
    const avgResponseTime = results.reduce((sum, r) => sum + r.avgResponseTime, 0) / results.length;
    const avgRPS = results.reduce((sum, r) => sum + r.requestsPerSecond, 0) / results.length;

    report += `## Summary\n`;
    report += `- Total Requests: ${totalRequests.toLocaleString()}\n`;
    report += `- Successful Requests: ${totalSuccessful.toLocaleString()}\n`;
    report += `- Overall Success Rate: ${avgSuccessRate.toFixed(1)}%\n`;
    report += `- Average Response Time: ${avgResponseTime.toFixed(0)}ms\n`;
    report += `- Average Requests/Second: ${avgRPS.toFixed(1)}\n\n`;

    // Detailed Results
    report += `## Detailed Results\n\n`;

    results.forEach((result, index) => {
      report += `### Test ${index + 1}\n`;
      report += `- Requests: ${result.totalRequests.toLocaleString()}\n`;
      report += `- Success Rate: ${result.successRate.toFixed(1)}%\n`;
      report += `- Response Time (Avg/Min/Max): ${result.avgResponseTime.toFixed(0)}ms / ${result.minResponseTime.toFixed(0)}ms / ${result.maxResponseTime.toFixed(0)}ms\n`;
      report += `- P95/P99: ${result.p95ResponseTime.toFixed(0)}ms / ${result.p99ResponseTime.toFixed(0)}ms\n`;
      report += `- Requests/Second: ${result.requestsPerSecond.toFixed(1)}\n`;
      report += `- Duration: ${result.duration.toFixed(1)}s\n`;
      report += `- Concurrency: ${result.concurrency}\n\n`;

      if (result.errors.length > 0) {
        report += `**Errors:**\n`;
        result.errors.slice(0, 5).forEach(error => {
          report += `- ${error.error}: ${error.count} (${error.percentage.toFixed(1)}%)\n`;
        });
        report += `\n`;
      }

      report += `**Response Time Distribution:**\n`;
      result.responseTimeDistribution.forEach(dist => {
        if (dist.count > 0) {
          report += `- ${dist.range}: ${dist.count} (${dist.percentage.toFixed(1)}%)\n`;
        }
      });
      report += `\n`;
    });

    // Performance Analysis
    report += `## Performance Analysis\n\n`;

    const slowResponses = results.filter(r => r.p95ResponseTime > 1000);
    if (slowResponses.length > 0) {
      report += `⚠️ **Performance Issues Detected:**\n`;
      slowResponses.forEach((result, index) => {
        report += `- Test ${index + 1}: P95 response time ${result.p95ResponseTime.toFixed(0)}ms (threshold: 1000ms)\n`;
      });
      report += `\n`;
    }

    const lowSuccessRates = results.filter(r => r.successRate < 99);
    if (lowSuccessRates.length > 0) {
      report += `⚠️ **Reliability Issues Detected:**\n`;
      lowSuccessRates.forEach((result, index) => {
        report += `- Test ${index + 1}: Success rate ${result.successRate.toFixed(1)}% (threshold: 99%)\n`;
      });
      report += `\n`;
    }

    const highErrorRates = results.filter(r => r.errors.some(e => e.percentage > 5));
    if (highErrorRates.length > 0) {
      report += `❌ **High Error Rates Detected:**\n`;
      highErrorRates.forEach((result, index) => {
        const highErrors = result.errors.filter(e => e.percentage > 5);
        report += `- Test ${index + 1}: ${highErrors.map(e => `${e.error} (${e.percentage.toFixed(1)}%)`).join(', ')}\n`;
      });
      report += `\n`;
    }

    // Recommendations
    report += `## Recommendations\n\n`;

    if (slowResponses.length > 0 || lowSuccessRates.length > 0 || highErrorRates.length > 0) {
      report += `- **Immediate Actions Required:**\n`;
      report += `  - Investigate slow response times and optimize security middleware\n`;
      report += `  - Fix error conditions causing high failure rates\n`;
      report += `  - Review rate limiting configuration if causing bottlenecks\n`;
      report += `  - Consider caching for frequently accessed secure resources\n\n`;
    } else {
      report += `- **System Performing Well:**\n`;
      report += `  - No immediate performance issues detected\n`;
      report += `  - Security features handling load effectively\n`;
      report += `  - Monitor for degradation under sustained high load\n\n`;
    }

    report += `- **General Recommendations:**\n`;
    report += `  - Implement connection pooling for database connections\n`;
    report += `  - Use caching for permission checks where appropriate\n`;
    report += `  - Consider async processing for audit logging\n`;
    report += `  - Monitor memory usage under sustained load\n`;
    report += `  - Implement circuit breakers for external security services\n\n`;

    return report;
  }
}

// Pre-configured security load test suites
export const securityLoadTestSuites: SecurityLoadTestSuite[] = [
  {
    name: 'Authentication Load Test',
    description: 'Tests authentication system performance under high load',
    tests: [
      {
        targetUrl: '/api/auth/login',
        method: 'POST',
        payload: { email: '<EMAIL>', password: 'password123' },
        concurrency: 50,
        duration: 60,
        expectedStatus: 200,
        maxResponseTime: 500,
        successRate: 99
      },
      {
        targetUrl: '/api/auth/login',
        method: 'POST',
        payload: { email: '<EMAIL>', password: 'wrong' },
        concurrency: 100,
        duration: 30,
        expectedStatus: 401,
        maxResponseTime: 200,
        successRate: 99
      }
    ]
  },
  {
    name: 'Rate Limiting Load Test',
    description: 'Tests rate limiting effectiveness under various load conditions',
    tests: [
      {
        targetUrl: '/api/auth/login',
        method: 'POST',
        payload: { email: '<EMAIL>', password: 'password123' },
        concurrency: 200,
        duration: 30,
        expectedStatus: 429,
        successRate: 95
      },
      {
        targetUrl: '/api/users',
        method: 'GET',
        concurrency: 150,
        duration: 45,
        expectedStatus: 429,
        successRate: 90
      }
    ]
  },
  {
    name: 'Authorization Load Test',
    description: 'Tests authorization system performance with permission checks',
    tests: [
      {
        targetUrl: '/api/users/123',
        method: 'GET',
        headers: { 'Authorization': 'Bearer valid-token' },
        concurrency: 75,
        duration: 60,
        expectedStatus: 200,
        maxResponseTime: 300,
        successRate: 99
      },
      {
        targetUrl: '/api/admin/users',
        method: 'GET',
        headers: { 'Authorization': 'Bearer admin-token' },
        concurrency: 25,
        duration: 30,
        expectedStatus: 200,
        maxResponseTime: 500,
        successRate: 99
      }
    ]
  },
  {
    name: 'Security Middleware Load Test',
    description: 'Tests security middleware performance under sustained load',
    tests: [
      {
        targetUrl: '/api/users',
        method: 'GET',
        headers: { 'Authorization': 'Bearer valid-token' },
        concurrency: 100,
        duration: 120,
        expectedStatus: 200,
        maxResponseTime: 200,
        successRate: 99
      },
      {
        targetUrl: '/api/files/upload',
        method: 'POST',
        headers: { 'Authorization': 'Bearer valid-token' },
        payload: { file: 'test-data' },
        concurrency: 50,
        duration: 60,
        expectedStatus: 200,
        maxResponseTime: 1000,
        successRate: 95
      }
    ]
  },
  {
    name: 'Audit Logging Load Test',
    description: 'Tests audit logging system performance under high activity',
    tests: [
      {
        targetUrl: '/api/users/profile',
        method: 'PUT',
        headers: { 'Authorization': 'Bearer valid-token' },
        payload: { name: 'Updated Name' },
        concurrency: 80,
        duration: 90,
        expectedStatus: 200,
        maxResponseTime: 400,
        successRate: 99
      }
    ]
  }
];

export function createCustomSecurityLoadTest(
  name: string,
  targetUrl: string,
  options: Partial<LoadTestConfig> = {}
): LoadTestConfig {
  return {
    targetUrl,
    method: 'GET',
    concurrency: 50,
    duration: 30,
    expectedStatus: 200,
    maxResponseTime: 500,
    successRate: 99,
    ...options
  };
}