import { jest } from '@jest/globals';

/**
 * Security testing utilities for penetration testing
 */

export interface SecurityTestConfig {
  targetUrl: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  payload?: any;
  expectedStatus?: number;
  expectedResponse?: any;
  timeout?: number;
}

export interface VulnerabilityTest {
  name: string;
  description: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  category: 'INJECTION' | 'AUTHENTICATION' | 'AUTHORIZATION' | 'XSS' | 'CSRF' | 'INFORMATION_DISCLOSURE' | 'RATE_LIMITING';
  testFunction: () => Promise<boolean>;
  remediation?: string;
}

export class SecurityTestRunner {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseUrl: string = 'http://localhost:3000', defaultHeaders: Record<string, string> = {}) {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...defaultHeaders
    };
  }

  async makeRequest(config: SecurityTestConfig): Promise<Response> {
    const url = `${this.baseUrl}${config.targetUrl}`;
    const headers = { ...this.defaultHeaders, ...config.headers };

    const requestOptions: RequestInit = {
      method: config.method || 'GET',
      headers,
      body: config.payload ? JSON.stringify(config.payload) : undefined,
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout || 5000);

    try {
      const response = await fetch(url, requestOptions);
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  async testSqlInjection(url: string, parameter: string, maliciousInput: string): Promise<boolean> {
    try {
      const testUrl = `${url}?${parameter}=${encodeURIComponent(maliciousInput)}`;
      const response = await this.makeRequest({ targetUrl: testUrl });

      // Check if response indicates successful injection
      const responseText = await response.text();

      // If we get a 500 error or unexpected data, it might indicate injection
      if (response.status === 500 || responseText.includes('syntax error') || responseText.includes('SQL')) {
        return true; // Potential vulnerability detected
      }

      return false; // No vulnerability detected
    } catch (error) {
      console.error('SQL injection test failed:', error);
      return false;
    }
  }

  async testXssInjection(url: string, parameter: string, xssPayload: string): Promise<boolean> {
    try {
      const testUrl = `${url}?${parameter}=${encodeURIComponent(xssPayload)}`;
      const response = await this.makeRequest({ targetUrl: testUrl });

      const responseText = await response.text();

      // Check if XSS payload is reflected unescaped
      if (responseText.includes('<script>') || responseText.includes('javascript:')) {
        return true; // Potential XSS vulnerability detected
      }

      return false; // No vulnerability detected
    } catch (error) {
      console.error('XSS test failed:', error);
      return false;
    }
  }

  async testCsrfProtection(url: string, payload: any): Promise<boolean> {
    try {
      // Test without CSRF token
      const response = await this.makeRequest({
        targetUrl: url,
        method: 'POST',
        payload
      });

      // If request succeeds without CSRF token, vulnerability exists
      if (response.status === 200 || response.status === 201) {
        return true; // CSRF vulnerability detected
      }

      return false; // CSRF protection working
    } catch (error) {
      console.error('CSRF test failed:', error);
      return false;
    }
  }

  async testRateLimiting(url: string, attempts: number = 10): Promise<boolean> {
    const responses: Response[] = [];

    for (let i = 0; i < attempts; i++) {
      try {
        const response = await this.makeRequest({ targetUrl: url });
        responses.push(response);
      } catch (error) {
        responses.push({ status: 429 } as Response); // Simulate rate limit
      }
    }

    const rateLimitedResponses = responses.filter(r => r.status === 429);

    // If we get rate limited responses, rate limiting is working
    return rateLimitedResponses.length > 0;
  }

  async testAuthenticationBypass(url: string, bypassPayload: any): Promise<boolean> {
    try {
      const response = await this.makeRequest({
        targetUrl: url,
        method: 'POST',
        payload: bypassPayload
      });

      // If authentication succeeds with bypass payload, vulnerability exists
      if (response.status === 200) {
        const data = await response.json();
        if (data.authenticated || data.token) {
          return true; // Authentication bypass detected
        }
      }

      return false; // No bypass detected
    } catch (error) {
      console.error('Authentication bypass test failed:', error);
      return false;
    }
  }

  async testAuthorizationBypass(url: string, userId: string, targetUserId: string): Promise<boolean> {
    try {
      const response = await this.makeRequest({
        targetUrl: url.replace(':id', targetUserId),
        headers: { 'X-User-ID': userId }
      });

      // If we can access another user's data, authorization bypass exists
      if (response.status === 200) {
        return true; // Authorization bypass detected
      }

      return false; // Authorization working correctly
    } catch (error) {
      console.error('Authorization bypass test failed:', error);
      return false;
    }
  }

  async runVulnerabilityTests(tests: VulnerabilityTest[]): Promise<VulnerabilityTest[]> {
    const results: VulnerabilityTest[] = [];

    for (const test of tests) {
      try {
        const isVulnerable = await test.testFunction();
        results.push({
          ...test,
          // In a real implementation, you'd store the actual result
          // For now, we'll simulate based on test logic
        });
      } catch (error) {
        console.error(`Test ${test.name} failed:`, error);
        results.push({
          ...test,
          // Mark as potentially vulnerable if test fails
        });
      }
    }

    return results;
  }
}

export const commonSqlInjectionPayloads = [
  "' OR '1'='1' --",
  "' OR '1'='1' #",
  "' OR 1=1 --",
  "'; DROP TABLE users; --",
  "' UNION SELECT * FROM users --",
  "1' OR '1' = '1",
  "admin' --",
  "') OR ('1'='1",
];

export const commonXssPayloads = [
  "<script>alert('XSS')</script>",
  "<img src=x onerror=alert('XSS')>",
  "javascript:alert('XSS')",
  "<svg onload=alert('XSS')>",
  "<iframe src=javascript:alert('XSS')>",
  "<body onload=alert('XSS')>",
  "<input onfocus=alert('XSS') autofocus>",
];

export const commonCsrfBypassPayloads = [
  { action: 'delete', id: '123' },
  { action: 'transfer', amount: 1000, to: 'attacker' },
  { action: 'admin', enable: true },
];

export const commonAuthBypassPayloads = [
  { username: "admin' --", password: "anything" },
  { username: "admin%00", password: "anything" },
  { username: "../../../etc/passwd", password: "anything" },
  { username: "admin", password: "' OR '1'='1" },
];

export function generateSecurityReport(vulnerabilities: VulnerabilityTest[]): string {
  const summary = {
    total: vulnerabilities.length,
    critical: vulnerabilities.filter(v => v.severity === 'CRITICAL').length,
    high: vulnerabilities.filter(v => v.severity === 'HIGH').length,
    medium: vulnerabilities.filter(v => v.severity === 'MEDIUM').length,
    low: vulnerabilities.filter(v => v.severity === 'LOW').length,
  };

  let report = `# Security Penetration Test Report\n\n`;
  report += `## Summary\n`;
  report += `- Total Tests: ${summary.total}\n`;
  report += `- Critical: ${summary.critical}\n`;
  report += `- High: ${summary.high}\n`;
  report += `- Medium: ${summary.medium}\n`;
  report += `- Low: ${summary.low}\n\n`;

  report += `## Vulnerabilities Found\n\n`;

  const criticalVulns = vulnerabilities.filter(v => v.severity === 'CRITICAL');
  const highVulns = vulnerabilities.filter(v => v.severity === 'HIGH');
  const mediumVulns = vulnerabilities.filter(v => v.severity === 'MEDIUM');
  const lowVulns = vulnerabilities.filter(v => v.severity === 'LOW');

  if (criticalVulns.length > 0) {
    report += `### Critical Vulnerabilities\n`;
    criticalVulns.forEach(vuln => {
      report += `- **${vuln.name}**: ${vuln.description}\n`;
      if (vuln.remediation) {
        report += `  - Remediation: ${vuln.remediation}\n`;
      }
    });
    report += `\n`;
  }

  if (highVulns.length > 0) {
    report += `### High Risk Vulnerabilities\n`;
    highVulns.forEach(vuln => {
      report += `- **${vuln.name}**: ${vuln.description}\n`;
      if (vuln.remediation) {
        report += `  - Remediation: ${vuln.remediation}\n`;
      }
    });
    report += `\n`;
  }

  if (mediumVulns.length > 0) {
    report += `### Medium Risk Vulnerabilities\n`;
    mediumVulns.forEach(vuln => {
      report += `- **${vuln.name}**: ${vuln.description}\n`;
      if (vuln.remediation) {
        report += `  - Remediation: ${vuln.remediation}\n`;
      }
    });
    report += `\n`;
  }

  if (lowVulns.length > 0) {
    report += `### Low Risk Vulnerabilities\n`;
    lowVulns.forEach(vuln => {
      report += `- **${vuln.name}**: ${vuln.description}\n`;
      if (vuln.remediation) {
        report += `  - Remediation: ${vuln.remediation}\n`;
      }
    });
    report += `\n`;
  }

  if (summary.total === 0) {
    report += `No vulnerabilities found. Great job!\n\n`;
  }

  report += `## Recommendations\n\n`;
  report += `- Implement proper input validation and sanitization\n`;
  report += `- Use parameterized queries for database operations\n`;
  report += `- Implement CSRF protection on all state-changing endpoints\n`;
  report += `- Use Content Security Policy (CSP) headers\n`;
  report += `- Implement proper rate limiting\n`;
  report += `- Regular security testing and code reviews\n`;

  return report;
}