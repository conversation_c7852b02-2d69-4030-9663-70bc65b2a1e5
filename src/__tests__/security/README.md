# Security Penetration Testing Suite

This comprehensive security penetration testing suite provides automated testing for common web application vulnerabilities and security weaknesses.

## Overview

The security testing suite includes:

- **SQL Injection Testing**: Tests for SQL injection vulnerabilities in API endpoints
- **XSS Testing**: Tests for Cross-Site Scripting vulnerabilities
- **CSRF Testing**: Tests for Cross-Site Request Forgery protection
- **Authentication Bypass Testing**: Tests for authentication bypass attempts
- **Authorization Testing**: Tests for authorization and access control issues
- **Rate Limiting Testing**: Tests for rate limiting effectiveness
- **Information Disclosure Testing**: Tests for sensitive information leakage
- **Input Validation Testing**: Tests for input validation bypass attempts
- **Session Security Testing**: Tests for session management security

## Files

- `penetration-tests.test.ts` - Main Jest test suite
- `security-test-utils.ts` - Utility functions and test runner
- `security-test-config.json` - Configuration for test parameters
- `run-security-penetration-tests.ts` - Standalone test runner script

## Usage

### Running Tests via npm scripts

```bash
# Run all security penetration tests
npm run security:penetration-tests

# Run Jest-based security tests
npm run security:test

# Run security test utilities
npm run security:test:utils

# Run complete security audit (penetration tests + unit tests)
npm run security:audit
```

### Running Tests Directly

```bash
# Using tsx (recommended)
npx tsx src/scripts/run-security-penetration-tests.ts

# Using node with ts-node
npx ts-node src/scripts/run-security-penetration-tests.ts

# Using Jest for unit tests
npx jest src/__tests__/security/penetration-tests.test.ts
```

### Configuration

The test suite can be configured via `security-test-config.json`:

```json
{
  "testEnvironment": {
    "baseUrl": "http://localhost:3000",
    "timeout": 5000,
    "retries": 3
  },
  "vulnerabilityProfiles": {
    "sqlInjection": {
      "enabled": true,
      "payloads": ["' OR '1'='1' --", "'; DROP TABLE users; --"]
    },
    "xss": {
      "enabled": true,
      "payloads": ["<script>alert('XSS')</script>"]
    }
  }
}
```

## Test Categories

### SQL Injection Tests

Tests common SQL injection patterns:
- Classic boolean-based injection
- Time-based injection
- Union-based injection
- Error-based injection
- Stacked queries

### XSS Tests

Tests various XSS attack vectors:
- Reflected XSS
- DOM-based XSS
- Stored XSS
- HTML injection
- JavaScript injection

### CSRF Tests

Tests CSRF protection mechanisms:
- Missing CSRF tokens
- Weak token validation
- Token reuse
- Cross-origin requests

### Authentication Bypass Tests

Tests authentication bypass attempts:
- SQL injection in login
- Null byte injection
- Path traversal
- HTTP method override
- Session fixation

### Authorization Tests

Tests authorization controls:
- Insecure Direct Object Reference (IDOR)
- Privilege escalation
- Role-based access control
- Function-level access control

### Rate Limiting Tests

Tests rate limiting effectiveness:
- Brute force protection
- API rate limiting
- IP-based restrictions
- User-based restrictions

## Environment Variables

- `TEST_BASE_URL`: Base URL for testing (default: http://localhost:3000)
- `TEST_TIMEOUT`: Request timeout in milliseconds (default: 5000)
- `TEST_RETRIES`: Number of retries for failed requests (default: 3)

## Output

The test suite generates:

1. **Console Output**: Real-time test progress and results
2. **Security Report**: Detailed vulnerability report with remediation advice
3. **Performance Metrics**: Test execution times and statistics
4. **Exit Codes**: 0 for success, 1 for vulnerabilities detected

## Example Output

```
🚀 Starting Security Penetration Tests...

🧪 Running: SQL Injection Test 1 (CRITICAL - INJECTION)
   ❌ VULNERABLE

🧪 Running: XSS Test 1 (HIGH - XSS)
   ✅ SECURE

🧪 Running: CSRF Test 1 (HIGH - CSRF)
   ✅ SECURE

============================================================
SECURITY PENETRATION TEST REPORT
============================================================

## Summary
- Total Tests: 25
- Critical: 1
- High: 0
- Medium: 0
- Low: 0

## Vulnerabilities Found

### Critical Vulnerabilities
- **SQL Injection Test 1**: Tests for SQL injection vulnerability with payload: ' OR '1'='1' --
  - Remediation: Use parameterized queries and input validation

## Recommendations
- Implement proper input validation and sanitization
- Use parameterized queries for database operations
- Implement CSRF protection on all state-changing endpoints
- Use Content Security Policy (CSP) headers
- Implement proper rate limiting
- Regular security testing and code reviews

============================================================
EXECUTION SUMMARY
============================================================
Total Tests: 25
Passed (Secure): 24
Failed (Vulnerable): 1
Errors: 0
Success Rate: 96.0%

Performance Metrics:
Total Execution Time: 15420ms
Average Test Time: 616.80ms

============================================================
❌ SECURITY ISSUES DETECTED - IMMEDIATE ATTENTION REQUIRED
```

## Integration with CI/CD

The security tests can be integrated into your CI/CD pipeline:

```yaml
# GitHub Actions example
- name: Run Security Tests
  run: npm run security:audit

- name: Upload Security Report
  uses: actions/upload-artifact@v2
  with:
    name: security-report
    path: security-reports/
```

## Security Considerations

- **Test Environment**: Always run penetration tests in a dedicated test environment
- **Rate Limiting**: The tests may trigger rate limiting - adjust delays if needed
- **Logging**: Monitor application logs during testing for suspicious activity
- **Cleanup**: Ensure test data is cleaned up after testing
- **Permissions**: Run tests with minimal required permissions

## Extending the Test Suite

To add new test cases:

1. Create a new test function in `security-test-utils.ts`
2. Add the test to the appropriate category in `run-security-penetration-tests.ts`
3. Update the configuration in `security-test-config.json` if needed
4. Add corresponding Jest tests in `penetration-tests.test.ts`

## Best Practices

- Run security tests regularly (daily/weekly)
- Test after code changes and deployments
- Combine with other security tools (SAST, DAST)
- Review and update test payloads regularly
- Monitor for false positives and adjust thresholds
- Keep test credentials separate from production

## Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure the target application is running
2. **Rate Limiting**: Increase delays between tests or reduce test frequency
3. **Authentication Errors**: Verify test credentials are correct
4. **Timeout Errors**: Increase timeout values in configuration

### Debug Mode

Enable debug mode by setting environment variables:

```bash
DEBUG=security:* npm run security:penetration-tests
```

## Contributing

When contributing to the security test suite:

1. Follow the existing code structure and patterns
2. Add comprehensive test cases for new vulnerabilities
3. Include remediation advice for each test
4. Update documentation for new features
5. Ensure tests work in different environments