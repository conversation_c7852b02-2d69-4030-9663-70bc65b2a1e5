import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { createTestUser, deleteTestUser } from '../test-utils';

describe('Admin Treaty API', () => {
  let prisma: PrismaClient;
  let testUser: any;
  let testAdminUser: any;
  let testTreatyType: any;
  let testTreaty: any;
  let adminRole: any;

  beforeAll(async () => {
    prisma = new PrismaClient();
    testUser = await createTestUser(prisma);
    testAdminUser = await createTestUser(prisma, '<EMAIL>');
    
    // Create a test treaty type with a unique name
    const timestamp = Date.now();
    testTreatyType = await prisma.treatyType.create({
      data: {
        name: `Test Treaty Type ${timestamp}`,
        description: 'Test treaty type for admin API testing',
        category: 'TEST',
      },
    });
    
    // Create admin role if it doesn't exist
    adminRole = await prisma.role.upsert({
      where: { name: 'admin' },
      update: {},
      create: {
        name: 'admin',
        description: 'Administrator role',
        isSystem: true,
      },
    });
    
    // Assign admin role to test admin user
    await prisma.userRole.create({
      data: {
        userId: testAdminUser.id,
        roleId: adminRole.id,
      },
    });
  });

  afterAll(async () => {
    if (testUser) {
      await deleteTestUser(prisma, testUser.id);
    }
    if (testAdminUser) {
      await deleteTestUser(prisma, testAdminUser.id);
    }
    if (testTreatyType) {
      await prisma.treatyType.delete({ where: { id: testTreatyType.id } });
    }
    if (adminRole) {
      // Remove the user role assignment but don't delete the role itself
      await prisma.userRole.deleteMany({
        where: {
          userId: testAdminUser.id,
          roleId: adminRole.id,
        },
      });
    }
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Create a test treaty before each test with many-to-many relationship
    testTreaty = await prisma.treaty.create({
      data: {
        userId: testUser.id,
        status: 'ACTIVE',
        notes: 'Test treaty notes',
        signedDate: new Date(),
        treatyTreatyTypes: {
          create: {
            treatyTypeId: testTreatyType.id,
          },
        },
      },
      include: {
        treatyTreatyTypes: {
          include: {
            treatyType: true,
          },
        },
      },
    });
  });

  afterEach(async () => {
    // Delete the test treaty after each test
    if (testTreaty) {
      await prisma.treaty.delete({ where: { id: testTreaty.id } });
      testTreaty = null;
    }
  });

  it('should retrieve all treaties for admin user', async () => {
    // Create another treaty
    const anotherTreaty = await prisma.treaty.create({
      data: {
        userId: testUser.id,
        status: 'EXPIRED',
        notes: 'Another test treaty',
        signedDate: new Date(),
        expirationDate: new Date(),
        treatyTreatyTypes: {
          create: {
            treatyTypeId: testTreatyType.id,
          },
        },
      },
    });

    // Retrieve treaties as admin
    const adminTreaties = await prisma.treaty.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        treatyTreatyTypes: {
          include: {
            treatyType: true,
          },
        },
      },
    });

    expect(adminTreaties).toHaveLength(2);
    expect(adminTreaties.some(t => t.id === testTreaty.id)).toBe(true);
    expect(adminTreaties.some(t => t.id === anotherTreaty.id)).toBe(true);

    // Verify admin user data is included
    const treatyWithUser = adminTreaties.find(t => t.id === testTreaty.id);
    expect(treatyWithUser?.user?.id).toBe(testUser.id);
    expect(treatyWithUser?.user?.name).toBe('Test User');

    // Clean up
    await prisma.treaty.delete({ where: { id: anotherTreaty.id } });
  });

  it('should filter treaties by user ID', async () => {
    // Create another user and treaty
    const otherUser = await createTestUser(prisma, '<EMAIL>');
    const otherTreaty = await prisma.treaty.create({
      data: {
        userId: otherUser.id,
        status: 'ACTIVE',
        notes: 'Other user treaty',
        signedDate: new Date(),
        treatyTreatyTypes: {
          create: {
            treatyTypeId: testTreatyType.id,
          },
        },
      },
    });

    // Retrieve treaties filtered by user ID
    const userTreaties = await prisma.treaty.findMany({
      where: {
        userId: testUser.id,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        treatyTreatyTypes: {
          include: {
            treatyType: true,
          },
        },
      },
    });

    expect(userTreaties).toHaveLength(1);
    expect(userTreaties[0].id).toBe(testTreaty.id);
    expect(userTreaties[0].userId).toBe(testUser.id);

    // Clean up
    await prisma.treaty.delete({ where: { id: otherTreaty.id } });
    await deleteTestUser(prisma, otherUser.id);
  });

  it('should filter treaties by status', async () => {
    // Create another treaty with different status
    const expiredTreaty = await prisma.treaty.create({
      data: {
        userId: testUser.id,
        status: 'EXPIRED',
        notes: 'Expired treaty',
        signedDate: new Date(),
        expirationDate: new Date(),
        treatyTreatyTypes: {
          create: {
            treatyTypeId: testTreatyType.id,
          },
        },
      },
    });

    // Retrieve treaties filtered by status and user
    const activeTreaties = await prisma.treaty.findMany({
      where: {
        userId: testUser.id,
        status: 'ACTIVE',
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        treatyTreatyTypes: {
          include: {
            treatyType: true,
          },
        },
      },
    });

    expect(activeTreaties).toHaveLength(1);
    expect(activeTreaties[0].id).toBe(testTreaty.id);
    expect(activeTreaties[0].status).toBe('ACTIVE');

    // Clean up
    await prisma.treaty.delete({ where: { id: expiredTreaty.id } });
  });
});