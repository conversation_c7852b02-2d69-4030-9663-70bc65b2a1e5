import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { createTestUser, deleteTestUser } from '../test-utils';

describe('TreatyTypeDetails Model', () => {
  let prisma: PrismaClient;
  let testUser: any;
  let testTreaty: any;
  let testTreatyType: any;

  beforeAll(async () => {
    prisma = new PrismaClient();
    testUser = await createTestUser(prisma);

    // Create test treaty type first (it doesn't require a treaty reference)
    testTreatyType = await prisma.treatyType.create({
      data: {
        name: 'Test Treaty Type',
        description: 'Test treaty type for testing',
        treatyId: 'dummy-treaty-id', // This will be updated
      },
    });

    // Create test treaty that references the treaty type
    testTreaty = await prisma.treaty.create({
      data: {
        name: 'Test Peace & Trade Treaty',
        description: 'Test treaty for treaty type details',
        status: 'ACTIVE',
        userId: testUser.id,
        treatyTypeId: testTreatyType.id,
      },
    });

    // Update the treaty type to reference the treaty (circular relationship)
    await prisma.treatyType.update({
      where: { id: testTreatyType.id },
      data: { treatyId: testTreaty.id },
    });
  });

  afterAll(async () => {
    if (testTreatyType) {
      await prisma.treatyType.delete({ where: { id: testTreatyType.id } });
    }
    if (testTreaty) {
      await prisma.treaty.delete({ where: { id: testTreaty.id } });
    }
    if (testUser) {
      await deleteTestUser(prisma, testUser.id);
    }
    await prisma.$disconnect();
  });

  it('should create a new treaty type details record', async () => {
    const treatyTypeDetails = await prisma.treatyTypeDetails.create({
      data: {
        userId: testUser.id,
        treatyId: testTreaty.id,
        treatyTypeId: testTreatyType.id,
        status: 'DRAFT',
        fullLegalName: 'John Doe',
        email: '<EMAIL>',
        currentCountryId: 1,
        currentCityId: 1,
        residentialAddress: '123 Test Street',
        peaceProtectedPremises: false,
        declarationAccepted: false,
      },
    });

    expect(treatyTypeDetails).toBeDefined();
    expect(treatyTypeDetails.userId).toBe(testUser.id);
    expect(treatyTypeDetails.treatyId).toBe(testTreaty.id);
    expect(treatyTypeDetails.treatyTypeId).toBe(testTreatyType.id);
    expect(treatyTypeDetails.status).toBe('DRAFT');
    expect(treatyTypeDetails.fullLegalName).toBe('John Doe');
    expect(treatyTypeDetails.email).toBe('<EMAIL>');

    // Clean up
    await prisma.treatyTypeDetails.delete({ where: { id: treatyTypeDetails.id } });
  });

  it('should enforce unique constraint on user+treaty+treatyType', async () => {
    const treatyTypeDetails1 = await prisma.treatyTypeDetails.create({
      data: {
        userId: testUser.id,
        treatyId: testTreaty.id,
        treatyTypeId: testTreatyType.id,
        status: 'DRAFT',
        fullLegalName: 'John Doe',
        email: '<EMAIL>',
      },
    });

    // Try to create another record with same combination
    await expect(
      prisma.treatyTypeDetails.create({
        data: {
          userId: testUser.id,
          treatyId: testTreaty.id,
          treatyTypeId: testTreatyType.id,
          status: 'DRAFT',
          fullLegalName: 'Jane Doe',
          email: '<EMAIL>',
        },
      })
    ).rejects.toThrow();

    // Clean up
    await prisma.treatyTypeDetails.delete({ where: { id: treatyTypeDetails1.id } });
  });

  it('should store and retrieve JSONB business details', async () => {
    const businessDetails = [
      {
        name: 'Test Business',
        treatyType: 'personal',
        categoryId: 1,
        subcategoryIds: [1, 2],
        ownershipStatus: 'fully_owned',
        countryId: 1,
        cityId: 1,
      },
    ];

    const treatyTypeDetails = await prisma.treatyTypeDetails.create({
      data: {
        userId: testUser.id,
        treatyId: testTreaty.id,
        treatyTypeId: testTreatyType.id,
        status: 'DRAFT',
        fullLegalName: 'John Doe',
        email: '<EMAIL>',
        businessDetails: businessDetails,
      },
    });

    expect(treatyTypeDetails.businessDetails).toEqual(businessDetails);

    // Clean up
    await prisma.treatyTypeDetails.delete({ where: { id: treatyTypeDetails.id } });
  });

  it('should store and retrieve JSONB protection items', async () => {
    const protectionItems = [
      {
        type: 'land',
        description: 'Test land description',
        countryId: 1,
        cityId: 1,
        proofImages: ['/test/path/image1.jpg'],
      },
    ];

    const treatyTypeDetails = await prisma.treatyTypeDetails.create({
      data: {
        userId: testUser.id,
        treatyId: testTreaty.id,
        treatyTypeId: testTreatyType.id,
        status: 'DRAFT',
        fullLegalName: 'John Doe',
        email: '<EMAIL>',
        protectionItems: protectionItems,
      },
    });

    expect(treatyTypeDetails.protectionItems).toEqual(protectionItems);

    // Clean up
    await prisma.treatyTypeDetails.delete({ where: { id: treatyTypeDetails.id } });
  });

  it('should update treaty type details status', async () => {
    const treatyTypeDetails = await prisma.treatyTypeDetails.create({
      data: {
        userId: testUser.id,
        treatyId: testTreaty.id,
        treatyTypeId: testTreatyType.id,
        status: 'DRAFT',
        fullLegalName: 'John Doe',
        email: '<EMAIL>',
      },
    });

    const updatedDetails = await prisma.treatyTypeDetails.update({
      where: { id: treatyTypeDetails.id },
      data: {
        status: 'SUBMITTED',
        submittedAt: new Date(),
      },
    });

    expect(updatedDetails.status).toBe('SUBMITTED');
    expect(updatedDetails.submittedAt).toBeDefined();

    // Clean up
    await prisma.treatyTypeDetails.delete({ where: { id: treatyTypeDetails.id } });
  });

  it('should retrieve treaty type details with relations', async () => {
    const treatyTypeDetails = await prisma.treatyTypeDetails.create({
      data: {
        userId: testUser.id,
        treatyId: testTreaty.id,
        treatyTypeId: testTreatyType.id,
        status: 'DRAFT',
        fullLegalName: 'John Doe',
        email: '<EMAIL>',
        currentCountryId: 1,
        currentCityId: 1,
      },
    });

    const retrievedDetails = await prisma.treatyTypeDetails.findUnique({
      where: { id: treatyTypeDetails.id },
      include: {
        user: true,
        treaty: true,
        treatyType: true,
        currentCountry: true,
        currentCity: true,
      },
    });

    expect(retrievedDetails?.user.id).toBe(testUser.id);
    expect(retrievedDetails?.treaty.id).toBe(testTreaty.id);
    expect(retrievedDetails?.treatyType.id).toBe(testTreatyType.id);
    expect(retrievedDetails?.currentCountry?.id).toBe(1);
    expect(retrievedDetails?.currentCity?.id).toBe(1);

    // Clean up
    await prisma.treatyTypeDetails.delete({ where: { id: treatyTypeDetails.id } });
  });

  it('should delete treaty type details', async () => {
    const treatyTypeDetails = await prisma.treatyTypeDetails.create({
      data: {
        userId: testUser.id,
        treatyId: testTreaty.id,
        treatyTypeId: testTreatyType.id,
        status: 'DRAFT',
        fullLegalName: 'John Doe',
        email: '<EMAIL>',
      },
    });

    await prisma.treatyTypeDetails.delete({
      where: { id: treatyTypeDetails.id },
    });

    const deletedDetails = await prisma.treatyTypeDetails.findUnique({
      where: { id: treatyTypeDetails.id },
    });

    expect(deletedDetails).toBeNull();
  });
});