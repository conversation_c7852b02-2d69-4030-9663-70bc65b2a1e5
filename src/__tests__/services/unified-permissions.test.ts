import { UnifiedPermissionService, PermissionContext, PermissionResult } from '@/lib/services/unified-permissions'
import { prisma } from '@/lib/prisma'
import { permissionInheritance } from '@/lib/services/permission-inheritance'

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    userRole: {
      findMany: jest.fn()
    },
    user_remote_server_access: {
      findMany: jest.fn()
    }
  }
}))

// Mock permission inheritance service
jest.mock('@/lib/services/permission-inheritance', () => ({
  permissionInheritance: {
    hasPermission: jest.fn(),
    calculateUserServerPermissions: jest.fn()
  }
}))

describe('UnifiedPermissionService', () => {
  let service: UnifiedPermissionService

  beforeEach(() => {
    service = new UnifiedPermissionService()
    jest.clearAllMocks()
  })

  describe('checkPermission', () => {
    it('should check local permissions when serverId is undefined', async () => {
      const context: PermissionContext = {
        userId: 'user123',
        permission: 'user:read'
      }

      const mockUserRoles = [
        {
          id: 'role1',
          userId: 'user123',
          roleId: 'role1',
          role: {
            id: 'role1',
            name: 'Member',
            rolePermissions: [
              {
                permission: {
                  name: 'user:read'
                }
              }
            ]
          }
        }
      ]

      ;(prisma.userRole.findMany as jest.Mock).mockResolvedValue(mockUserRoles)

      const result = await service.checkPermission(context)

      expect(result).toEqual({
        allowed: true,
        source: 'local',
        reason: 'Granted by role: Member',
        details: {
          roleBased: true,
          grantedBy: 'System'
        }
      })

      expect(prisma.userRole.findMany).toHaveBeenCalledWith({
        where: { userId: 'user123' },
        include: {
          role: {
            include: {
              rolePermissions: {
                include: {
                  permission: true
                }
              }
            }
          }
        }
      })
    })

    it('should check remote permissions when serverId is provided', async () => {
      const context: PermissionContext = {
        userId: 'user123',
        permission: 'user:read',
        serverId: 'server456'
      }

      const mockPermissionResult = {
        userId: 'user123',
        serverName: 'Test Server',
        permissions: [
          {
            name: 'user:read',
            source: 'role',
            roleName: 'Member',
            grantedBy: 'admin',
            expiresAt: null
          }
        ]
      }

      ;(permissionInheritance.hasPermission as jest.Mock).mockResolvedValue(true)
      ;(permissionInheritance.calculateUserServerPermissions as jest.Mock).mockResolvedValue(mockPermissionResult)

      const result = await service.checkPermission(context)

      expect(result).toEqual({
        allowed: true,
        source: 'remote',
        serverId: 'server456',
        reason: 'Granted by role on Test Server',
        details: {
          roleBased: true,
          individual: false,
          expiresAt: undefined,
          grantedBy: 'admin'
        }
      })

      expect(permissionInheritance.hasPermission).toHaveBeenCalledWith('user123', 'server456', 'user:read')
      expect(permissionInheritance.calculateUserServerPermissions).toHaveBeenCalledWith('user123', 'server456')
    })

    it('should return false for local permission not found', async () => {
      const context: PermissionContext = {
        userId: 'user123',
        permission: 'user:write'
      }

      const mockUserRoles = [
        {
          id: 'role1',
          userId: 'user123',
          roleId: 'role1',
          role: {
            id: 'role1',
            name: 'Member',
            rolePermissions: [
              {
                permission: {
                  name: 'user:read'
                }
              }
            ]
          }
        }
      ]

      ;(prisma.userRole.findMany as jest.Mock).mockResolvedValue(mockUserRoles)

      const result = await service.checkPermission(context)

      expect(result).toEqual({
        allowed: false,
        source: 'local',
        reason: 'Permission not found in user roles'
      })
    })

    it('should return false for remote permission not found', async () => {
      const context: PermissionContext = {
        userId: 'user123',
        permission: 'user:write',
        serverId: 'server456'
      }

      ;(permissionInheritance.hasPermission as jest.Mock).mockResolvedValue(false)

      const result = await service.checkPermission(context)

      expect(result).toEqual({
        allowed: false,
        source: 'remote',
        serverId: 'server456',
        reason: 'Permission not granted on this server'
      })
    })

    it('should handle errors gracefully', async () => {
      const context: PermissionContext = {
        userId: 'user123',
        permission: 'user:read'
      }

      ;(prisma.userRole.findMany as jest.Mock).mockRejectedValue(new Error('Database error'))

      const result = await service.checkPermission(context)

      expect(result).toEqual({
        allowed: false,
        source: 'none',
        reason: 'Error checking permission'
      })
    })
  })

  describe('getAllUserPermissions', () => {
    it('should get all permissions for a user', async () => {
      const userId = 'user123'

      const mockUserRoles = [
        {
          id: 'role1',
          userId,
          roleId: 'role1',
          role: {
            id: 'role1',
            name: 'Member',
            rolePermissions: [
              {
                permission: {
                  name: 'user:read'
                }
              },
              {
                permission: {
                  name: 'user:write'
                }
              }
            ]
          }
        }
      ]

      const mockServerAccess = [
        {
          remote_server_id: 'server456'
        }
      ]

      const mockServerPermissions = {
        userId,
        serverName: 'Test Server',
        permissions: [
          {
            name: 'document:read',
            source: 'role',
            roleName: 'Member',
            grantedBy: 'admin',
            expiresAt: null
          }
        ]
      }

      ;(prisma.userRole.findMany as jest.Mock).mockResolvedValue(mockUserRoles)
      ;(prisma.user_remote_server_access.findMany as jest.Mock).mockResolvedValue(mockServerAccess)
      ;(permissionInheritance.calculateUserServerPermissions as jest.Mock).mockResolvedValue(mockServerPermissions)

      const result = await service.getAllUserPermissions(userId)

      expect(result.local).toHaveLength(2)
      expect(result.remote).toHaveProperty('server456')
      expect(result.remote.server456).toHaveLength(1)

      // Check local permissions
      const localPerms = result.local.filter(p => p.allowed)
      expect(localPerms.length).toBeGreaterThan(0)
      expect(localPerms.some(p => p.reason?.includes('Member'))).toBe(true)

      // Check remote permissions
      const remotePerms = result.remote.server456
      expect(remotePerms[0].allowed).toBe(true)
      expect(remotePerms[0].serverId).toBe('server456')
    })

    it('should handle users with no permissions', async () => {
      const userId = 'user123'

      ;(prisma.userRole.findMany as jest.Mock).mockResolvedValue([])
      ;(prisma.user_remote_server_access.findMany as jest.Mock).mockResolvedValue([])

      const result = await service.getAllUserPermissions(userId)

      expect(result.local).toHaveLength(0)
      expect(Object.keys(result.remote)).toHaveLength(0)
    })
  })

  describe('validateContext', () => {
    it('should validate valid context', () => {
      const validContext: PermissionContext = {
        userId: 'user123',
        permission: 'user:read'
      }

      const result = service.validateContext(validContext)
      expect(result).toEqual({ valid: true })
    })

    it('should reject context without userId', () => {
      const invalidContext = {
        permission: 'user:read'
      } as any

      const result = service.validateContext(invalidContext)
      expect(result).toEqual({
        valid: false,
        reason: 'User ID is required'
      })
    })

    it('should reject context without permission', () => {
      const invalidContext = {
        userId: 'user123'
      } as any

      const result = service.validateContext(invalidContext)
      expect(result).toEqual({
        valid: false,
        reason: 'Permission name is required'
      })
    })
  })

  describe('convenience functions', () => {
    it('should export convenience functions', () => {
      // These are just smoke tests to ensure the functions exist
      expect(typeof service.checkPermission).toBe('function')
      expect(typeof service.getAllUserPermissions).toBe('function')
      expect(typeof service.validateContext).toBe('function')
    })
  })
})