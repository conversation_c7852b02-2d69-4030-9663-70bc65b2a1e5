import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { SecurityEventDetectionService, SecurityEventType, SecurityEventSeverity } from '@/lib/services/security-event-detection';
import { SecurityAlertingService } from '@/lib/services/security-alerting';
import { SecurityMetricsService } from '@/lib/services/security-metrics';
import { SecurityAuditAnalysisService } from '@/lib/services/security-audit-analysis';
import { SecurityIncidentResponseService } from '@/lib/services/security-incident-response';

describe('Security Monitoring System', () => {
  let eventDetectionService: SecurityEventDetectionService;
  let alertingService: SecurityAlertingService;
  let metricsService: SecurityMetricsService;
  let auditAnalysisService: SecurityAuditAnalysisService;
  let incidentResponseService: SecurityIncidentResponseService;

  beforeEach(() => {
    eventDetectionService = new SecurityEventDetectionService();
    alertingService = new SecurityAlertingService();
    metricsService = new SecurityMetricsService();
    auditAnalysisService = new SecurityAuditAnalysisService();
    incidentResponseService = new SecurityIncidentResponseService();
  });

  describe('SecurityEventDetectionService', () => {
    it('should detect brute force attacks', async () => {
      const mockAuditEntries = [
        {
          id: '1',
          userId: 'user1',
          action: 'LOGIN_FAILED',
          ipAddress: '***********',
          timestamp: new Date().toISOString()
        },
        {
          id: '2',
          userId: 'user1',
          action: 'LOGIN_FAILED',
          ipAddress: '***********',
          timestamp: new Date().toISOString()
        },
        {
          id: '3',
          userId: 'user1',
          action: 'LOGIN_FAILED',
          ipAddress: '***********',
          timestamp: new Date().toISOString()
        },
        {
          id: '4',
          userId: 'user1',
          action: 'LOGIN_FAILED',
          ipAddress: '***********',
          timestamp: new Date().toISOString()
        },
        {
          id: '5',
          userId: 'user1',
          action: 'LOGIN_FAILED',
          ipAddress: '***********',
          timestamp: new Date().toISOString()
        },
        {
          id: '6',
          userId: 'user1',
          action: 'LOGIN_SUCCESS',
          ipAddress: '***********',
          timestamp: new Date().toISOString()
        }
      ];

      // Mock the analyzeAuditLogEntry method
      const result = await eventDetectionService.analyzeAuditLogEntry({
        event_type: 'AUTHENTICATION_FAILURE',
        count: 6,
        ipAddress: '***********',
        userId: 'user1'
      });

      expect(result).not.toBeNull();
      expect(result?.type).toBe(SecurityEventType.BRUTE_FORCE_ATTACK);
      expect(result?.severity).toBe(SecurityEventSeverity.HIGH);
    });

    it('should get security events with filters', async () => {
      const events = await eventDetectionService.getSecurityEvents({
        type: SecurityEventType.BRUTE_FORCE_ATTACK,
        limit: 10
      });

      expect(Array.isArray(events)).toBe(true);
    });

    it('should get event statistics', async () => {
      const stats = await eventDetectionService.getEventStatistics('day');

      expect(stats).toHaveProperty('totalEvents');
      expect(stats).toHaveProperty('eventsByType');
      expect(stats).toHaveProperty('eventsBySeverity');
      expect(stats).toHaveProperty('unresolvedEvents');
    });
  });

  describe('SecurityAlertingService', () => {
    it('should process security events and send alerts', async () => {
      const mockEvent = {
        id: 'event1',
        type: SecurityEventType.BRUTE_FORCE_ATTACK,
        severity: SecurityEventSeverity.CRITICAL,
        description: 'Brute force attack detected',
        ip_address: '***********',
        user_id: 'user1'
      };

      // Mock console.log to verify alert sending
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await alertingService.processSecurityEvent(mockEvent);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Sending email alert')
      );

      consoleSpy.mockRestore();
    });

    it('should get alert rules', async () => {
      const rules = await alertingService.getAlertRules();

      expect(Array.isArray(rules)).toBe(true);
      expect(rules.length).toBeGreaterThan(0);
      expect(rules[0]).toHaveProperty('id');
      expect(rules[0]).toHaveProperty('name');
      expect(rules[0]).toHaveProperty('channels');
    });

    it('should get alert history', async () => {
      const alerts = await alertingService.getAlertHistory({
        limit: 10
      });

      expect(Array.isArray(alerts)).toBe(true);
    });

    it('should get alert statistics', async () => {
      const stats = await alertingService.getAlertStatistics('day');

      expect(stats).toHaveProperty('totalAlerts');
      expect(stats).toHaveProperty('alertsByChannel');
      expect(stats).toHaveProperty('alertsByStatus');
      expect(stats).toHaveProperty('failedAlerts');
    });
  });

  describe('SecurityMetricsService', () => {
    it('should record and retrieve metrics', async () => {
      await metricsService.recordMetric('test_metric', 42, {
        eventType: SecurityEventType.BRUTE_FORCE_ATTACK,
        severity: SecurityEventSeverity.HIGH
      });

      const metrics = await metricsService.getMetrics({
        name: 'test_metric'
      });

      expect(metrics.length).toBeGreaterThan(0);
      expect(metrics[0].name).toBe('test_metric');
      expect(metrics[0].value).toBe(42);
    });

    it('should get time series data', async () => {
      const startTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
      const endTime = new Date();

      await metricsService.recordMetric('time_series_test', 10, {}, 'COUNTER');
      await metricsService.recordMetric('time_series_test', 20, {}, 'COUNTER');

      const timeSeries = await metricsService.getTimeSeries(
        'time_series_test',
        {},
        startTime,
        endTime
      );

      expect(timeSeries).toHaveProperty('metric');
      expect(timeSeries).toHaveProperty('data');
      expect(Array.isArray(timeSeries.data)).toBe(true);
    });

    it('should get KPIs', async () => {
      const kpis = await metricsService.getKPIs();

      expect(Array.isArray(kpis)).toBe(true);
      expect(kpis.length).toBeGreaterThan(0);
      expect(kpis[0]).toHaveProperty('id');
      expect(kpis[0]).toHaveProperty('name');
      expect(kpis[0]).toHaveProperty('target');
      expect(kpis[0]).toHaveProperty('current');
    });

    it('should get metric summary', async () => {
      const summary = await metricsService.getMetricSummary('day');

      expect(summary).toHaveProperty('timeframe');
      expect(summary).toHaveProperty('totalEvents');
      expect(summary).toHaveProperty('totalResponses');
      expect(summary).toHaveProperty('totalAlerts');
    });

    it('should record security events', async () => {
      await metricsService.recordSecurityEvent(
        SecurityEventType.BRUTE_FORCE_ATTACK,
        SecurityEventSeverity.HIGH,
        'user1',
        'server1'
      );

      const metrics = await metricsService.getMetrics({
        category: 'EVENTS'
      });

      expect(metrics.length).toBeGreaterThan(0);
    });

    it('should record response times', async () => {
      await metricsService.recordResponseTime(1500, 'BRUTE_FORCE_ATTACK');

      const metrics = await metricsService.getMetrics({
        name: 'response_time'
      });

      expect(metrics.length).toBeGreaterThan(0);
      expect(metrics[0].value).toBe(1500);
    });

    it('should record alerts sent', async () => {
      await metricsService.recordAlertSent('EMAIL', true);

      const metrics = await metricsService.getMetrics({
        name: 'alert_sent'
      });

      expect(metrics.length).toBeGreaterThan(0);
    });

    it('should record incidents resolved', async () => {
      await metricsService.recordIncidentResolved(300000, SecurityEventSeverity.HIGH);

      const metrics = await metricsService.getMetrics({
        name: 'incident_resolved'
      });

      expect(metrics.length).toBeGreaterThan(0);
    });
  });

  describe('SecurityAuditAnalysisService', () => {
    it('should analyze audit trails', async () => {
      const mockAuditEntries = [
        {
          id: '1',
          user_id: 'user1',
          action: 'LOGIN_FAILED',
          ip_address: '***********',
          timestamp: new Date().toISOString(),
          severity: SecurityEventSeverity.HIGH
        },
        {
          id: '2',
          user_id: 'user1',
          action: 'LOGIN_SUCCESS',
          ip_address: '***********',
          timestamp: new Date(Date.now() + 300000).toISOString(), // 5 minutes later
          severity: SecurityEventSeverity.MEDIUM
        }
      ];

      const analysis = await auditAnalysisService.analyzeAuditTrail(mockAuditEntries);

      expect(analysis).toHaveProperty('patterns');
      expect(analysis).toHaveProperty('anomalies');
      expect(analysis).toHaveProperty('trends');
      expect(analysis).toHaveProperty('insights');
      expect(Array.isArray(analysis.patterns)).toBe(true);
      expect(Array.isArray(analysis.anomalies)).toBe(true);
      expect(Array.isArray(analysis.trends)).toBe(true);
      expect(Array.isArray(analysis.insights)).toBe(true);
    });

    it('should generate security reports', async () => {
      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
      const endDate = new Date();

      const report = await auditAnalysisService.generateReport(
        'WEEKLY',
        'Weekly Security Report',
        'Security analysis for the past week',
        startDate,
        endDate,
        'test-user'
      );

      expect(report).toHaveProperty('id');
      expect(report).toHaveProperty('title');
      expect(report).toHaveProperty('type');
      expect(report).toHaveProperty('sections');
      expect(report).toHaveProperty('summary');
      expect(report).toHaveProperty('recommendations');
      expect(Array.isArray(report.sections)).toBe(true);
      expect(Array.isArray(report.recommendations)).toBe(true);
    });

    it('should export analysis data', async () => {
      const testData = {
        patterns: [],
        anomalies: [],
        trends: [],
        insights: ['Test insight']
      };

      const blob = await auditAnalysisService.exportAnalysis('JSON', testData);

      expect(blob).toBeInstanceOf(Blob);
      expect(blob.type).toBe('application/json');

      // In test environment, we can't easily read blob content, so just verify it exists
      expect(blob.size).toBeGreaterThan(0);
    });

    it('should get analysis history', async () => {
      const history = await auditAnalysisService.getAnalysisHistory({
        limit: 10
      });

      expect(Array.isArray(history)).toBe(true);
    });
  });

  describe('Integration Tests', () => {
    it('should integrate event detection with alerting', async () => {
      const mockEvent = {
        id: 'integration_test_event',
        type: SecurityEventType.BRUTE_FORCE_ATTACK,
        severity: SecurityEventSeverity.CRITICAL,
        description: 'Integration test brute force attack',
        ipAddress: '***********00',
        userId: 'test_user',
        count: 10
      };

      // Mock console.log to verify integration
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      // Process event through detection service
      const detectedEvent = await eventDetectionService.analyzeAuditLogEntry(mockEvent);

      if (detectedEvent) {
        // Process through alerting service
        await alertingService.processSecurityEvent(detectedEvent);

        // Record metrics
        await metricsService.recordSecurityEvent(
          detectedEvent.type,
          detectedEvent.severity,
          detectedEvent.userId,
          'server1'
        );

        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('Sending email alert')
        );
      }

      consoleSpy.mockRestore();
    });

    it('should handle high-volume security events', async () => {
      const events = Array.from({ length: 100 }, (_, i) => ({
        id: `event_${i}`,
        type: SecurityEventType.RATE_LIMIT_EXCEEDED,
        severity: SecurityEventSeverity.MEDIUM,
        description: `Rate limit event ${i}`,
        ipAddress: `192.168.1.${i % 255}`,
        userId: `user_${i}`,
        count: 50 + i
      }));

      // Process multiple events
      for (const event of events) {
        await eventDetectionService.analyzeAuditLogEntry(event);
        await metricsService.recordSecurityEvent(
          event.type,
          event.severity,
          event.userId,
          'server1'
        );
      }

      const eventMetrics = await metricsService.getMetrics({
        category: 'EVENTS'
      });

      expect(eventMetrics.length).toBeGreaterThan(0);
    });

    it('should maintain performance under load', async () => {
      const startTime = Date.now();

      // Process 50 events
      const events = Array.from({ length: 50 }, (_, i) => ({
        id: `perf_event_${i}`,
        type: SecurityEventType.SUSPICIOUS_ACTIVITY,
        severity: SecurityEventSeverity.LOW,
        description: `Performance test event ${i}`,
        ipAddress: '***********',
        userId: 'perf_user',
        count: i + 1
      }));

      for (const event of events) {
        await eventDetectionService.analyzeAuditLogEntry(event);
        await metricsService.recordSecurityEvent(
          event.type,
          event.severity,
          event.userId,
          'server1'
        );
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (less than 5 seconds for 50 events)
      expect(duration).toBeLessThan(5000);

      const metrics = await metricsService.getMetrics({
        category: 'EVENTS'
      });

      expect(metrics.length).toBeGreaterThan(0);
    });
  });

  describe('SecurityIncidentResponseService', () => {
    it('should process security events and execute responses', async () => {
      const mockEvent = {
        id: 'test_event',
        type: SecurityEventType.BRUTE_FORCE_ATTACK,
        severity: SecurityEventSeverity.HIGH,
        description: 'Test brute force attack',
        ipAddress: '***********',
        userId: 'test_user',
        count: 10
      };

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await incidentResponseService.processSecurityEvent(mockEvent);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Account lockout executed')
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('IP block executed')
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Session termination executed')
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Notification sent')
      );

      consoleSpy.mockRestore();
    });

    it('should evaluate rules correctly', async () => {
      const mockEvent = {
        id: 'test_event',
        type: SecurityEventType.BRUTE_FORCE_ATTACK,
        severity: SecurityEventSeverity.HIGH,
        count: 10,
        ipAddress: '***********',
        userId: 'test_user'
      };

      // Test rule evaluation - this is a private method, so we'll test it indirectly
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await incidentResponseService.processSecurityEvent(mockEvent);

      // Should trigger brute force response rule
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Account lockout executed')
      );

      consoleSpy.mockRestore();
    });

    it('should respect cooldown periods', async () => {
      const mockEvent = {
        id: 'test_event_1',
        type: SecurityEventType.BRUTE_FORCE_ATTACK,
        severity: SecurityEventSeverity.HIGH,
        count: 10,
        ipAddress: '***********',
        userId: 'test_user'
      };

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      // First event should trigger response
      await incidentResponseService.processSecurityEvent(mockEvent);

      // Second event immediately after should be blocked by cooldown
      await incidentResponseService.processSecurityEvent({
        ...mockEvent,
        id: 'test_event_2'
      });

      // Should only see one set of responses due to cooldown
      const lockoutCalls = consoleSpy.mock.calls.filter(call =>
        call[0].includes('Account lockout executed')
      );

      expect(lockoutCalls.length).toBe(1);

      consoleSpy.mockRestore();
    });

    it('should execute account lockout action', async () => {
      const mockEvent = {
        id: 'test_event',
        type: SecurityEventType.BRUTE_FORCE_ATTACK,
        severity: SecurityEventSeverity.HIGH,
        userId: 'test_user',
        count: 10
      };

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      // Process event to trigger account lockout
      await incidentResponseService.processSecurityEvent(mockEvent);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'test_user',
          duration: 30,
          notifyUser: true,
          notifyAdmins: true
        })
      );

      consoleSpy.mockRestore();
    });

    it('should execute session termination action', async () => {
      const mockEvent = {
        id: 'test_event',
        type: SecurityEventType.BRUTE_FORCE_ATTACK,
        severity: SecurityEventSeverity.HIGH,
        userId: 'test_user',
        count: 10
      };

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await incidentResponseService.processSecurityEvent(mockEvent);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'test_user',
          terminateAll: true
        })
      );

      consoleSpy.mockRestore();
    });

    it('should execute IP block action', async () => {
      const mockEvent = {
        id: 'test_event',
        type: SecurityEventType.BRUTE_FORCE_ATTACK,
        severity: SecurityEventSeverity.HIGH,
        ipAddress: '***********',
        userId: 'test_user',
        count: 10
      };

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await incidentResponseService.processSecurityEvent(mockEvent);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          ipAddress: '***********',
          duration: 60,
          blockType: 'TEMPORARY'
        })
      );

      consoleSpy.mockRestore();
    });

    it('should execute notification action', async () => {
      const mockEvent = {
        id: 'test_event',
        type: SecurityEventType.BRUTE_FORCE_ATTACK,
        severity: SecurityEventSeverity.HIGH,
        userId: 'test_user',
        count: 10
      };

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await incidentResponseService.processSecurityEvent(mockEvent);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          recipients: ['<EMAIL>'],
          priority: 'HIGH',
          template: 'brute_force_incident'
        })
      );

      consoleSpy.mockRestore();
    });

    it('should execute escalation action', async () => {
      const mockEvent = {
        id: 'test_event',
        type: SecurityEventType.PRIVILEGE_ESCALATION,
        severity: SecurityEventSeverity.CRITICAL,
        userId: 'test_user'
      };

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await incidentResponseService.processSecurityEvent(mockEvent);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          level: 'IMMEDIATE',
          requireHumanReview: true
        })
      );

      consoleSpy.mockRestore();
    });

    it('should execute log event action', async () => {
      const mockEvent = {
        id: 'test_event',
        type: SecurityEventType.SUSPICIOUS_ACTIVITY,
        severity: SecurityEventSeverity.HIGH,
        userId: 'test_user'
      };

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await incidentResponseService.processSecurityEvent(mockEvent);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Event logged')
      );

      consoleSpy.mockRestore();
    });

    it('should get incident responses', async () => {
      const responses = await incidentResponseService.getIncidentResponses({
        limit: 10
      });

      expect(Array.isArray(responses)).toBe(true);
    });

    it('should get response statistics', async () => {
      const stats = await incidentResponseService.getResponseStatistics('day');

      expect(stats).toHaveProperty('totalResponses');
      expect(stats).toHaveProperty('responsesByStatus');
      expect(stats).toHaveProperty('responsesByRule');
      expect(stats).toHaveProperty('failedResponses');
      expect(stats).toHaveProperty('timeframe');
    });

    it('should handle events that do not match any rules', async () => {
      const mockEvent = {
        id: 'test_event',
        type: SecurityEventType.AUTHENTICATION_FAILURE,
        severity: SecurityEventSeverity.LOW,
        userId: 'test_user',
        count: 1
      };

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await incidentResponseService.processSecurityEvent(mockEvent);

      // Should not trigger any responses for low severity auth failure
      const responseCalls = consoleSpy.mock.calls.filter(call =>
        call[0].includes('executed')
      );

      expect(responseCalls.length).toBe(0);

      consoleSpy.mockRestore();
    });

    it('should handle multiple applicable rules', async () => {
      const mockEvent = {
        id: 'test_event',
        type: SecurityEventType.RATE_LIMIT_EXCEEDED,
        severity: SecurityEventSeverity.HIGH,
        userId: 'test_user',
        count: 150
      };

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await incidentResponseService.processSecurityEvent(mockEvent);

      // Should trigger rate limit response rule
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('IP block executed')
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Notification sent')
      );

      consoleSpy.mockRestore();
    });

    it('should respect rule priority order', async () => {
      const mockEvent = {
        id: 'test_event',
        type: SecurityEventType.BRUTE_FORCE_ATTACK,
        severity: SecurityEventSeverity.CRITICAL,
        userId: 'test_user',
        count: 20
      };

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

      await incidentResponseService.processSecurityEvent(mockEvent);

      // Should execute higher priority rule first
      const calls = consoleSpy.mock.calls;
      const lockoutIndex = calls.findIndex(call =>
        call[0].includes('Account lockout executed')
      );
      const ipBlockIndex = calls.findIndex(call =>
        call[0].includes('IP block executed')
      );

      expect(lockoutIndex).toBeLessThan(ipBlockIndex);

      consoleSpy.mockRestore();
    });
  });
});