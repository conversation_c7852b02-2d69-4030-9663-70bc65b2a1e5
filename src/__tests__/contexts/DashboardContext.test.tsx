import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import { DashboardProvider, useDashboard } from '@/lib/contexts/DashboardContext';
import { PERMISSIONS } from '@/lib/constants/permissions';

// Mock fetch globally
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  clear: jest.fn(),
  removeItem: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('DashboardContext', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    mockLocalStorage.getItem.mockClear();
    mockLocalStorage.setItem.mockClear();
    mockLocalStorage.clear();
  });

  const TestComponent = () => {
    const { state, hasPermission, hasRole, isAdmin, isMember } = useDashboard();

    return (
      <div>
        <div data-testid="user-role">{state.user?.role || 'none'}</div>
        <div data-testid="user-permissions">{state.user?.permissions.join(',') || 'none'}</div>
        <div data-testid="active-tab">{state.activeTab}</div>
        <div data-testid="loading-user">{state.loading.user.toString()}</div>
        <div data-testid="error">{state.error || 'none'}</div>
        <div data-testid="has-server-permission">{hasPermission(PERMISSIONS.REMOTE_SERVER_READ).toString()}</div>
        <div data-testid="has-admin-role">{hasRole('ADMIN').toString()}</div>
        <div data-testid="is-admin">{isAdmin.toString()}</div>
        <div data-testid="is-member">{isMember.toString()}</div>
      </div>
    );
  };

  it('provides initial state correctly', () => {
    render(
      <DashboardProvider>
        <TestComponent />
      </DashboardProvider>
    );

    expect(screen.getByTestId('user-role')).toHaveTextContent('none');
    expect(screen.getByTestId('user-permissions')).toHaveTextContent('none');
    expect(screen.getByTestId('active-tab')).toHaveTextContent('overview');
    expect(screen.getByTestId('loading-user')).toHaveTextContent('true');
    expect(screen.getByTestId('error')).toHaveTextContent('none');
    expect(screen.getByTestId('has-server-permission')).toHaveTextContent('false');
    expect(screen.getByTestId('has-admin-role')).toHaveTextContent('false');
    expect(screen.getByTestId('is-admin')).toHaveTextContent('false');
    expect(screen.getByTestId('is-member')).toHaveTextContent('false');
  });

  it('loads user data from API on mount', async () => {
    const mockUser = {
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'MEMBER' as const,
      permissions: [PERMISSIONS.REMOTE_SERVER_READ, PERMISSIONS.PROFILE_READ],
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockUser,
    } as Response);

    render(
      <DashboardProvider>
        <TestComponent />
      </DashboardProvider>
    );

    // Initially loading
    expect(screen.getByTestId('loading-user')).toHaveTextContent('true');

    // After API call
    await waitFor(() => {
      expect(screen.getByTestId('user-role')).toHaveTextContent('MEMBER');
      expect(screen.getByTestId('user-permissions')).toHaveTextContent('remote_server:read,profile:read');
      expect(screen.getByTestId('loading-user')).toHaveTextContent('false');
    });

    expect(mockFetch).toHaveBeenCalledWith('/api/user/profile');
  });

  it('handles API errors gracefully', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
    } as Response);

    render(
      <DashboardProvider>
        <TestComponent />
      </DashboardProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent('Failed to load user data');
      expect(screen.getByTestId('loading-user')).toHaveTextContent('false');
    });
  });

  it('loads settings from localStorage', () => {
    const mockSettings = {
      theme: 'dark' as const,
      refreshInterval: 60000,
      notifications: { enabled: false, sound: false, desktop: true },
      layout: { compact: true, sidebarCollapsed: true },
    };

    localStorage.setItem('dashboard-settings', JSON.stringify(mockSettings));

    render(
      <DashboardProvider>
        <TestComponent />
      </DashboardProvider>
    );

    // Settings should be loaded from localStorage
    // Note: We can't easily test the settings state directly, but we can verify localStorage is read
    expect(localStorage.getItem).toHaveBeenCalledWith('dashboard-settings');
  });

  it('saves settings to localStorage when they change', () => {
    const TestSettingsComponent = () => {
      const { setSettings } = useDashboard();

      React.useEffect(() => {
        setSettings({ theme: 'dark', refreshInterval: 60000 });
      }, [setSettings]);

      return <div>Settings updated</div>;
    };

    render(
      <DashboardProvider>
        <TestSettingsComponent />
      </DashboardProvider>
    );

    // Verify localStorage.setItem was called
    expect(localStorage.setItem).toHaveBeenCalledWith(
      'dashboard-settings',
      expect.stringContaining('"theme":"dark"')
    );
  });

  it('updates active tab correctly', () => {
    const TestTabComponent = () => {
      const { setActiveTab, state } = useDashboard();

      return (
        <button onClick={() => setActiveTab('notifications')} data-testid="change-tab">
          Change to Notifications (current: {state.activeTab})
        </button>
      );
    };

    render(
      <DashboardProvider>
        <TestTabComponent />
      </DashboardProvider>
    );

    expect(screen.getByTestId('change-tab')).toHaveTextContent('current: overview');

    act(() => {
      screen.getByTestId('change-tab').click();
    });

    expect(screen.getByTestId('change-tab')).toHaveTextContent('current: notifications');
  });

  it('computes permission checks correctly', () => {
    const mockUser = {
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'MEMBER' as const,
      permissions: [PERMISSIONS.REMOTE_SERVER_READ, PERMISSIONS.PROFILE_READ],
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockUser,
    } as Response);

    render(
      <DashboardProvider>
        <TestComponent />
      </DashboardProvider>
    );

    waitFor(() => {
      expect(screen.getByTestId('has-server-permission')).toHaveTextContent('true');
      expect(screen.getByTestId('has-admin-role')).toHaveTextContent('false');
      expect(screen.getByTestId('is-admin')).toHaveTextContent('false');
      expect(screen.getByTestId('is-member')).toHaveTextContent('true');
    });
  });

  it('handles admin user correctly', async () => {
    const mockAdminUser = {
      id: '1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'ADMIN' as const,
      permissions: [PERMISSIONS.ADMIN_PANEL_ACCESS, PERMISSIONS.REMOTE_SERVER_READ],
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockAdminUser,
    } as Response);

    render(
      <DashboardProvider>
        <TestComponent />
      </DashboardProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('user-role')).toHaveTextContent('ADMIN');
      expect(screen.getByTestId('has-admin-role')).toHaveTextContent('true');
      expect(screen.getByTestId('is-admin')).toHaveTextContent('true');
      expect(screen.getByTestId('is-member')).toHaveTextContent('true');
    });
  });

  it('throws error when used outside provider', () => {
    const TestOutsideProvider = () => {
      try {
        useDashboard();
        return <div>Should not reach here</div>;
      } catch (error) {
        return <div data-testid="error">{(error as Error).message}</div>;
      }
    };

    render(<TestOutsideProvider />);

    expect(screen.getByTestId('error')).toHaveTextContent(
      'useDashboard must be used within a DashboardProvider'
    );
  });

  it('resets state correctly', () => {
    const TestResetComponent = () => {
      const { resetState, state } = useDashboard();

      return (
        <button onClick={resetState} data-testid="reset-button">
          Reset (current role: {state.user?.role || 'none'})
        </button>
      );
    };

    render(
      <DashboardProvider>
        <TestResetComponent />
      </DashboardProvider>
    );

    expect(screen.getByTestId('reset-button')).toHaveTextContent('current role: none');
  });

  it('updates last updated timestamp', () => {
    const TestUpdateComponent = () => {
      const { updateLastUpdated, state } = useDashboard();

      return (
        <button onClick={updateLastUpdated} data-testid="update-button">
          Update (last updated: {state.lastUpdated?.toISOString() || 'never'})
        </button>
      );
    };

    render(
      <DashboardProvider>
        <TestUpdateComponent />
      </DashboardProvider>
    );

    const initialTime = screen.getByTestId('update-button').textContent;

    act(() => {
      screen.getByTestId('update-button').click();
    });

    const updatedTime = screen.getByTestId('update-button').textContent;
    expect(updatedTime).not.toBe(initialTime);
  });
});