import {
  PERMISSIONS,
  PERMISSION_DESCRIPTIONS,
  ROLE_PERMISSIONS,
  PROFILE_FIELD_PERMISSIONS,
  PERMISSION_CATEGORIES,
  PERMISSION_ACTIONS,
  PERMISSION_RESOURCES
} from '@/lib/constants/permissions'

describe('Permission Constants', () => {
  describe('PERMISSIONS', () => {
    it('should contain all expected permission strings', () => {
      expect(PERMISSIONS.USER_CREATE).toBe('user:create')
      expect(PERMISSIONS.USER_READ).toBe('user:read')
      expect(PERMISSIONS.USER_WRITE).toBe('user:write')
      expect(PERMISSIONS.USER_DELETE).toBe('user:delete')
      expect(PERMISSIONS.USER_MANAGE).toBe('user:manage')

      expect(PERMISSIONS.PROFILE_READ).toBe('profile:read')
      expect(PERMISSIONS.PROFILE_WRITE).toBe('profile:write')
      expect(PERMISSIONS.PROFILE_DELETE).toBe('profile:delete')

      expect(PERMISSIONS.ROLE_CREATE).toBe('role:create')
      expect(PERMISSIONS.ROLE_READ).toBe('role:read')
      expect(PERMISSIONS.ROLE_ASSIGN).toBe('role:assign')
      expect(PERMISSIONS.ROLE_MANAGE).toBe('role:manage')

      expect(PERMISSIONS.ADMIN_PANEL_ACCESS).toBe('admin_panel:access')
      expect(PERMISSIONS.ADMIN_USERS_MANAGE).toBe('admin_users:manage')
      expect(PERMISSIONS.ADMIN_ROLES_MANAGE).toBe('admin_roles:manage')
    })

    it('should have unique permission values', () => {
      const permissionValues = Object.values(PERMISSIONS)
      const uniqueValues = new Set(permissionValues)
      expect(uniqueValues.size).toBe(permissionValues.length)
    })

    it('should follow consistent naming convention', () => {
      Object.values(PERMISSIONS).forEach(permission => {
        expect(permission).toMatch(/^[a-z_]+:[a-z]+$/)
      })
    })
  })

  describe('PERMISSION_DESCRIPTIONS', () => {
    it('should have descriptions for all permissions', () => {
      Object.values(PERMISSIONS).forEach(permission => {
        expect(PERMISSION_DESCRIPTIONS[permission]).toBeDefined()
        expect(typeof PERMISSION_DESCRIPTIONS[permission]).toBe('string')
        expect(PERMISSION_DESCRIPTIONS[permission].length).toBeGreaterThan(0)
      })
    })

    it('should have meaningful descriptions', () => {
      expect(PERMISSION_DESCRIPTIONS[PERMISSIONS.USER_CREATE]).toBe('Create new user accounts')
      expect(PERMISSION_DESCRIPTIONS[PERMISSIONS.PROFILE_READ]).toBe('View own profile information')
      expect(PERMISSION_DESCRIPTIONS[PERMISSIONS.ADMIN_PANEL_ACCESS]).toBe('Access admin panel')
    })
  })

  describe('ROLE_PERMISSIONS', () => {
    it('should define permissions for each role', () => {
      expect(ROLE_PERMISSIONS.PEACE).toBeDefined()
      expect(ROLE_PERMISSIONS.MEMBER).toBeDefined()
      expect(ROLE_PERMISSIONS.ADMIN).toBeDefined()
    })

    it('should have increasing permissions from peace to admin', () => {
      const peacePerms = ROLE_PERMISSIONS.PEACE
      const memberPerms = ROLE_PERMISSIONS.MEMBER
      const adminPerms = ROLE_PERMISSIONS.ADMIN

      // Peace should have fewer permissions than member
      expect(peacePerms.length).toBeLessThan(memberPerms.length)

      // Member should have fewer permissions than admin
      expect(memberPerms.length).toBeLessThan(adminPerms.length)
    })

    it('should contain valid permission strings', () => {
      Object.values(ROLE_PERMISSIONS).forEach(rolePerms => {
        rolePerms.forEach(permission => {
          expect(Object.values(PERMISSIONS)).toContain(permission)
        })
      })
    })

    it('should follow role hierarchy correctly', () => {
      const peacePerms = new Set(ROLE_PERMISSIONS.PEACE)
      const memberPerms = new Set(ROLE_PERMISSIONS.MEMBER)
      const adminPerms = new Set(ROLE_PERMISSIONS.ADMIN)

      // Peace permissions should be subset of member permissions
      peacePerms.forEach(perm => {
        expect(memberPerms.has(perm)).toBe(true)
      })

      // Member permissions should be subset of admin permissions
      memberPerms.forEach(perm => {
        expect(adminPerms.has(perm)).toBe(true)
      })
    })
  })

  describe('PROFILE_FIELD_PERMISSIONS', () => {
    it('should define field permissions for each role', () => {
      expect(PROFILE_FIELD_PERMISSIONS.firstName).toBeDefined()
      expect(PROFILE_FIELD_PERMISSIONS.lastName).toBeDefined()
      expect(PROFILE_FIELD_PERMISSIONS.email).toBeDefined()
      expect(PROFILE_FIELD_PERMISSIONS.role).toBeDefined()
    })

    it('should have valid action arrays for each field and role', () => {
      Object.entries(PROFILE_FIELD_PERMISSIONS).forEach(([field, rolePermissions]) => {
        expect(rolePermissions.peace).toBeDefined()
        expect(rolePermissions.member).toBeDefined()
        expect(rolePermissions.admin).toBeDefined()

        ;['peace', 'member', 'admin'].forEach(role => {
          const actions = rolePermissions[role as keyof typeof rolePermissions]
          expect(Array.isArray(actions)).toBe(true)
          actions.forEach(action => {
            expect(['read', 'write', 'delete']).toContain(action)
          })
        })
      })
    })

    it('should follow role hierarchy for field permissions', () => {
      Object.entries(PROFILE_FIELD_PERMISSIONS).forEach(([field, rolePermissions]) => {
        const peaceActions = rolePermissions.peace
        const memberActions = rolePermissions.member
        const adminActions = rolePermissions.admin

        // Peace should have fewer or equal actions than member
        expect(peaceActions.length).toBeLessThanOrEqual(memberActions.length)

        // Member should have fewer or equal actions than admin
        expect(memberActions.length).toBeLessThanOrEqual(adminActions.length)
      })
    })
  })

  describe('PERMISSION_CATEGORIES', () => {
    it('should define all permission categories', () => {
      expect(PERMISSION_CATEGORIES.USER_MANAGEMENT).toBe('user_management')
      expect(PERMISSION_CATEGORIES.PROFILE).toBe('profile')
      expect(PERMISSION_CATEGORIES.ROLES).toBe('roles')
      expect(PERMISSION_CATEGORIES.TREATIES).toBe('treaties')
      expect(PERMISSION_CATEGORIES.DOCUMENTS).toBe('documents')
      expect(PERMISSION_CATEGORIES.SYSTEM).toBe('system')
      expect(PERMISSION_CATEGORIES.REMOTE_SERVERS).toBe('remote_servers')
      expect(PERMISSION_CATEGORIES.ADMIN).toBe('admin')
    })

    it('should have unique category values', () => {
      const categoryValues = Object.values(PERMISSION_CATEGORIES)
      const uniqueValues = new Set(categoryValues)
      expect(uniqueValues.size).toBe(categoryValues.length)
    })
  })

  describe('PERMISSION_ACTIONS', () => {
    it('should define all permission actions', () => {
      expect(PERMISSION_ACTIONS.CREATE).toBe('create')
      expect(PERMISSION_ACTIONS.READ).toBe('read')
      expect(PERMISSION_ACTIONS.WRITE).toBe('write')
      expect(PERMISSION_ACTIONS.DELETE).toBe('delete')
      expect(PERMISSION_ACTIONS.MANAGE).toBe('manage')
      expect(PERMISSION_ACTIONS.ASSIGN).toBe('assign')
      expect(PERMISSION_ACTIONS.SYNC).toBe('sync')
      expect(PERMISSION_ACTIONS.AUDIT).toBe('audit')
    })

    it('should have unique action values', () => {
      const actionValues = Object.values(PERMISSION_ACTIONS)
      const uniqueValues = new Set(actionValues)
      expect(uniqueValues.size).toBe(actionValues.length)
    })
  })

  describe('PERMISSION_RESOURCES', () => {
    it('should define all permission resources', () => {
      expect(PERMISSION_RESOURCES.USER).toBe('user')
      expect(PERMISSION_RESOURCES.PROFILE).toBe('profile')
      expect(PERMISSION_RESOURCES.ROLE).toBe('role')
      expect(PERMISSION_RESOURCES.TREATY).toBe('treaty')
      expect(PERMISSION_RESOURCES.DOCUMENT).toBe('document')
      expect(PERMISSION_RESOURCES.SYSTEM_CONFIG).toBe('system_config')
      expect(PERMISSION_RESOURCES.REMOTE_SERVER).toBe('remote_server')
      expect(PERMISSION_RESOURCES.ADMIN_PANEL).toBe('admin_panel')
    })

    it('should have unique resource values', () => {
      const resourceValues = Object.values(PERMISSION_RESOURCES)
      const uniqueValues = new Set(resourceValues)
      expect(uniqueValues.size).toBe(resourceValues.length)
    })
  })

  describe('Type Exports', () => {
    it('should export correct types', () => {
      // This test ensures the type exports are working correctly
      // by checking that the constants match the expected types
      const permissionValues = Object.values(PERMISSIONS)
      const categoryValues = Object.values(PERMISSION_CATEGORIES)
      const actionValues = Object.values(PERMISSION_ACTIONS)
      const resourceValues = Object.values(PERMISSION_RESOURCES)

      expect(permissionValues.length).toBeGreaterThan(0)
      expect(categoryValues.length).toBeGreaterThan(0)
      expect(actionValues.length).toBeGreaterThan(0)
      expect(resourceValues.length).toBeGreaterThan(0)
    })
  })
})