import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useSession } from 'next-auth/react';
import ManageUsersLayout from '@/app/admin/manage/layout';
import ManageUsersPage from '@/app/admin/manage/users/page';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}));

// Mock next-auth/react
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}));

// Mock check-admin utility
jest.mock('@/lib/check-admin', () => ({
  checkAdmin: jest.fn(),
}));

describe('Admin Manage Section', () => {
  const mockSession = {
    user: {
      id: '1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ManageUsersLayout', () => {
    it('should render layout for authenticated admin user', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockResolvedValue(true);

      render(
        <ManageUsersLayout>
          <div>Test Content</div>
        </ManageUsersLayout>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Content')).toBeInTheDocument();
      });
    });

    it('should redirect non-admin users', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockResolvedValue(false);

      const mockPush = jest.fn();
      const originalUseRouter = require('next/navigation').useRouter;

      // Mock useRouter for this test
      require('next/navigation').useRouter = jest.fn(() => ({
        push: mockPush,
        replace: jest.fn(),
        back: jest.fn(),
        forward: jest.fn(),
        refresh: jest.fn(),
        prefetch: jest.fn(),
      }));

      render(
        <ManageUsersLayout>
          <div>Test Content</div>
        </ManageUsersLayout>
      );

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/unauthorized');
      });

      // Restore original mock
      require('next/navigation').useRouter = originalUseRouter;
    });

    it('should show loading state while checking permissions', () => {
      (useSession as jest.Mock).mockReturnValue({
        data: null,
        status: 'loading',
      });

      render(
        <ManageUsersLayout>
          <div>Test Content</div>
        </ManageUsersLayout>
      );

      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('should render VerticalNav component for admin users', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockResolvedValue(true);

      render(
        <ManageUsersLayout>
          <div>Test Content</div>
        </ManageUsersLayout>
      );

      await waitFor(() => {
        expect(screen.getByText('User Management')).toBeInTheDocument();
        expect(screen.getByText('Manage Users')).toBeInTheDocument();
        expect(screen.getByText('Contact Details')).toBeInTheDocument();
      });
    });
  });

  describe('ManageUsersPage', () => {
    it('should render manage users page for admin user', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockResolvedValue(true);

      render(<ManageUsersPage />);

      await waitFor(() => {
        expect(screen.getByText(/manage users/i)).toBeInTheDocument();
      });
    });

    it('should display user management tabs', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockResolvedValue(true);

      render(<ManageUsersPage />);

      await waitFor(() => {
        // Check for tab buttons specifically
        expect(screen.getByRole('button', { name: /create user/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /update user/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /search users/i })).toBeInTheDocument();
      });
    });

  });
});