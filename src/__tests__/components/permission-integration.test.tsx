import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { PermissionProvider, usePermissions } from '@/lib/contexts/PermissionContext';
import { PermissionWrapper } from '@/components/auth/PermissionWrapper';
import { ProtectedPage } from '@/components/auth/ProtectedPage';
import { PermissionNavigation } from '@/components/navigation/PermissionNavigation';
import { PermissionErrorHandler } from '@/components/auth/PermissionErrorBoundary';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
  usePathname: () => '/test',
}));

// Mock fetch for API calls
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({
      id: 'test-user',
      userId: 'test-user',
      permissions: [
        { id: '1', name: 'Profile Access', resource: 'USER_PROFILE', action: 'READ' },
        { id: '2', name: 'Admin Access', resource: 'ADMIN_PANEL', action: 'ACCESS' },
      ],
      roles: [
        { id: '1', name: 'ADMIN', permissions: [] },
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
    }),
  })
) as jest.MockedFunction<typeof fetch>;

describe('Frontend Permission Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('PermissionProvider', () => {
    it('should provide permission context to children', async () => {
      function TestComponent() {
        const { hasPermission, loading, permissions } = usePermissions();

        if (loading) return <div>Loading...</div>;

        return (
          <div>
            <div data-testid="has-permission">
              {hasPermission('USER_PROFILE', 'READ') ? 'Has Permission' : 'No Permission'}
            </div>
            <div data-testid="permissions-count">{permissions?.permissions.length}</div>
          </div>
        );
      }

      render(
        <PermissionProvider>
          <TestComponent />
        </PermissionProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('has-permission')).toHaveTextContent('Has Permission');
        expect(screen.getByTestId('permissions-count')).toHaveTextContent('2');
      });
    });

    it('should handle permission fetch errors', async () => {
      (global.fetch as jest.MockedFunction<typeof fetch>).mockImplementationOnce(() =>
        Promise.resolve({
          ok: false,
          status: 500,
        })
      );

      function TestComponent() {
        const { error, loading } = usePermissions();

        if (loading) return <div>Loading...</div>;

        return <div data-testid="error">{error || 'No error'}</div>;
      }

      render(
        <PermissionProvider>
          <TestComponent />
        </PermissionProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Failed to fetch permissions');
      });
    });
  });

  describe('PermissionWrapper', () => {
    it('should render children when user has required permission', async () => {
      function TestComponent() {
        return (
          <PermissionWrapper resource="USER_PROFILE" action="READ">
            <div data-testid="content">Protected Content</div>
          </PermissionWrapper>
        );
      }

      render(
        <PermissionProvider>
          <TestComponent />
        </PermissionProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('content')).toBeInTheDocument();
      });
    });

    it('should render fallback when user lacks permission', async () => {
      function TestComponent() {
        return (
          <PermissionWrapper
            resource="ADMIN_PANEL"
            action="DELETE"
            fallback={<div data-testid="fallback">Access Denied</div>}
          >
            <div data-testid="content">Protected Content</div>
          </PermissionWrapper>
        );
      }

      render(
        <PermissionProvider>
          <TestComponent />
        </PermissionProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('fallback')).toBeInTheDocument();
        expect(screen.queryByTestId('content')).not.toBeInTheDocument();
      });
    });

    it('should handle role-based permissions', async () => {
      function TestComponent() {
        return (
          <PermissionWrapper
            roles={['ADMIN']}
            fallback={<div data-testid="fallback">Access Denied</div>}
          >
            <div data-testid="content">Admin Content</div>
          </PermissionWrapper>
        );
      }

      render(
        <PermissionProvider>
          <TestComponent />
        </PermissionProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('content')).toBeInTheDocument();
      });
    });

    it('should handle admin-only permissions', async () => {
      function TestComponent() {
        return (
          <PermissionWrapper
            adminOnly
            fallback={<div data-testid="fallback">Access Denied</div>}
          >
            <div data-testid="content">Admin Content</div>
          </PermissionWrapper>
        );
      }

      render(
        <PermissionProvider>
          <TestComponent />
        </PermissionProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('content')).toBeInTheDocument();
      });
    });
  });

  describe('ProtectedPage', () => {
    it('should render children when user has permission', async () => {
      function TestComponent() {
        return (
          <ProtectedPage resource="USER_PROFILE" action="READ">
            <div data-testid="content">Protected Page Content</div>
          </ProtectedPage>
        );
      }

      render(
        <PermissionProvider>
          <TestComponent />
        </PermissionProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('content')).toBeInTheDocument();
      });
    });

    it('should redirect when user lacks permission', async () => {
      const mockPush = jest.fn();

      // Mock the router
      (jest.requireMock('next/navigation') as any).useRouter.mockReturnValue({
        push: mockPush,
      });

      function TestComponent() {
        return (
          <ProtectedPage
            resource="ADMIN_PANEL"
            action="DELETE"
            redirectTo="/unauthorized"
          >
            <div data-testid="content">Protected Page Content</div>
          </ProtectedPage>
        );
      }

      render(
        <PermissionProvider>
          <TestComponent />
        </PermissionProvider>
      );

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/unauthorized');
      });
    });

    it('should render loading state while permissions are loading', () => {
      // Mock fetch to delay response
      (global.fetch as jest.MockedFunction<typeof fetch>).mockImplementationOnce(() =>
        new Promise(() => {}) // Never resolves
      );

      function TestComponent() {
        return (
          <ProtectedPage resource="USER_PROFILE" action="READ">
            <div data-testid="content">Protected Page Content</div>
          </ProtectedPage>
        );
      }

      render(
        <PermissionProvider>
          <TestComponent />
        </PermissionProvider>
      );

      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });

  describe('PermissionNavigation', () => {
    const mockItems = [
      {
        name: 'Profile',
        href: '/profile',
        permissions: [{ resource: 'USER_PROFILE', action: 'READ' }],
      },
      {
        name: 'Admin',
        href: '/admin',
        adminOnly: true,
      },
      {
        name: 'Settings',
        href: '/settings',
      },
    ];

    it('should render navigation items user has access to', async () => {
      render(
        <PermissionProvider>
          <PermissionNavigation items={mockItems} />
        </PermissionProvider>
      );

      await waitFor(() => {
        expect(screen.getByText('Profile')).toBeInTheDocument();
        expect(screen.getByText('Admin')).toBeInTheDocument();
        expect(screen.getByText('Settings')).toBeInTheDocument();
      });
    });

    it('should highlight active navigation item', async () => {
      // Mock pathname to match an item
      (jest.requireMock('next/navigation') as any).usePathname.mockReturnValue('/profile');

      render(
        <PermissionProvider>
          <PermissionNavigation items={mockItems} />
        </PermissionProvider>
      );

      await waitFor(() => {
        const profileLink = screen.getByText('Profile').closest('a');
        expect(profileLink).toHaveClass('bg-primary-100');
      });
    });
  });

  describe('PermissionErrorHandler', () => {
    it('should render children when no error occurs', async () => {
      function TestComponent() {
        return <div data-testid="content">Normal Content</div>;
      }

      render(
        <PermissionProvider>
          <PermissionErrorHandler>
            <TestComponent />
          </PermissionErrorHandler>
        </PermissionProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('content')).toBeInTheDocument();
      });
    });

    it('should render error UI when permission error occurs', async () => {
      (global.fetch as jest.MockedFunction<typeof fetch>).mockImplementationOnce(() =>
        Promise.resolve({
          ok: false,
          status: 500,
        })
      );

      function TestComponent() {
        const { error } = usePermissions();
        if (error) {
          throw new Error('Permission system error');
        }
        return <div data-testid="content">Normal Content</div>;
      }

      render(
        <PermissionProvider>
          <PermissionErrorHandler>
            <TestComponent />
          </PermissionErrorHandler>
        </PermissionProvider>
      );

      await waitFor(() => {
        expect(screen.getByText('Permission System Error')).toBeInTheDocument();
      });
    });
  });

  describe('Integration Tests', () => {
    it('should work together as a complete permission system', async () => {
      function TestApp() {
        return (
          <PermissionProvider>
            <PermissionErrorHandler>
              <div className="app">
                <nav>
                  <PermissionNavigation
                    items={[
                      {
                        name: 'Profile',
                        href: '/profile',
                        permissions: [{ resource: 'USER_PROFILE', action: 'READ' }],
                      },
                      {
                        name: 'Admin',
                        href: '/admin',
                        adminOnly: true,
                      },
                    ]}
                  />
                </nav>
                <main>
                  <PermissionWrapper
                    resource="USER_PROFILE"
                    action="READ"
                    fallback={<div>Access Denied</div>}
                  >
                    <div data-testid="profile-content">Profile Content</div>
                  </PermissionWrapper>
                  <ProtectedPage
                    adminOnly
                    fallback={<div>Admin Access Required</div>}
                  >
                    <div data-testid="admin-content">Admin Content</div>
                  </ProtectedPage>
                </main>
              </div>
            </PermissionErrorHandler>
          </PermissionProvider>
        );
      }

      render(<TestApp />);

      await waitFor(() => {
        // Should show navigation items user has access to
        expect(screen.getByText('Profile')).toBeInTheDocument();
        expect(screen.getByText('Admin')).toBeInTheDocument();

        // Should show protected content
        expect(screen.getByTestId('profile-content')).toBeInTheDocument();
        expect(screen.getByTestId('admin-content')).toBeInTheDocument();
      });
    });
  });
});