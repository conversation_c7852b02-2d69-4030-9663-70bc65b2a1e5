import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useSession } from 'next-auth/react';
import UserProfile from '@/components/UserProfile';

// Mock the next-auth useSession hook
jest.mock('next-auth/react');

// Mock fetch for API calls
const mockFetch = jest.fn();
global.fetch = mockFetch;

let defaultFetchImplementation: (url: string) => Promise<any>;

// Mock data
const mockSession = {
  user: {
    id: '123',
    name: '<PERSON>',
    email: '<EMAIL>',
    nwaEmail: '<EMAIL>'
  }
};

const mockUserData = {
  user: {
    id: '123',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    nwaEmail: '<EMAIL>',
    phone: '******-0123',
    mobile: '******-0456',
    streetAddress1: '123 Main Street',
    streetAddress2: 'Apt 4B',
    town: 'Springfield',
    city: 'Springfield',
    country: 'United States',
    postalCode: '12345',
    regionId: '1',
    regionText: 'California',
    dob: '1980-01-01',
    license: 'DL123456',
    passport: 'PA987654',
    peaceAmbassadorNumber: 'PA-001',
    tradeTreatyNumber: 'TT-001',
    bio: 'Experienced diplomat with a passion for international relations.',
    titleId: '1',
    positionId: '1'
  }
};

const mockLegacyUserData = {
  user: {
    id: '124',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    streetAddress: '456 Legacy Street', // Old field format
    town: 'Springfield',
    country: 'United States',
    postalCode: '54321',
    regionText: 'Texas'
  }
};

const mockCountries = [
  { id: '1', name: 'United States' },
  { id: '2', name: 'United Kingdom' }
];

const mockUSAddressFormat = {
  addressFormat: {
    id: 1,
    countryId: 1,
    postalCodeLabel: 'ZIP Code',
    postalCodeFormat: '^\\d{5}(-\\d{4})?$',
    postalCodeRequired: true,
    regionLabel: 'State',
    regionRequired: true,
    townLabel: 'City',
    townRequired: true,
    addressTemplate: '{street}\\n{town}, {region} {postalCode}\\n{country}',
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  }
};

const mockUKAddressFormat = {
  addressFormat: {
    id: 2,
    countryId: 2,
    postalCodeLabel: 'Postcode',
    postalCodeFormat: '^[A-Z]{1,2}\\d[A-Z\\d]? ?\\d[A-Z]{2}$',
    postalCodeRequired: true,
    regionLabel: 'County',
    regionRequired: false,
    townLabel: 'Town/City',
    townRequired: true,
    addressTemplate: '{street}\\n{town}\\n{region}\\n{postalCode}\\n{country}',
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  }
};

describe('UserProfile', () => {
  const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;

  beforeEach(() => {
    jest.clearAllMocks();

    defaultFetchImplementation = (url: string) => {
      if (url.includes('/api/users/123')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockUserData),
        });
      }
      if (url.includes('/api/users/124')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockLegacyUserData),
        });
      }
      if (url.includes('/api/countries') && url.includes('q=')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockCountries),
        });
      }
      if (url.includes('/api/countries/1/address-format')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockUSAddressFormat),
        });
      }
      if (url.includes('/api/countries/2/address-format')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockUKAddressFormat),
        });
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({}),
      });
    };

    mockFetch.mockImplementation(defaultFetchImplementation);
  });

  test('shows loading state while fetching data', () => {
    mockUseSession.mockReturnValue({
      data: null,
      status: 'loading'
    });

    render(<UserProfile />);

    const skeletons = screen.getAllByRole('generic');
    expect(skeletons.some((element) => element.classList.contains('animate-pulse'))).toBe(true);
  });

  test('shows not logged in message for unauthenticated users', () => {
    mockUseSession.mockReturnValue({
      data: null,
      status: 'unauthenticated'
    });

    render(<UserProfile />);
    
    expect(screen.getByText('Not logged in')).toBeInTheDocument();
    expect(screen.getByText('Please log in to view your profile.')).toBeInTheDocument();
  });

  test('renders basic user information from session when no additional data', async () => {
    mockUseSession.mockReturnValue({
      data: mockSession,
      status: 'authenticated'
    });

    // Mock API to return no user data
    mockFetch.mockImplementation((url: string) => {
      if (url.includes('/api/users/123')) {
        return Promise.resolve({ ok: false, status: 404 });
      }
      return defaultFetchImplementation(url);
    });

    render(<UserProfile />);

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      const emailInstances = screen.getAllByText('<EMAIL>');
      expect(emailInstances.length).toBeGreaterThan(0);
    });
  });

  describe('Address Display Formatting', () => {
    beforeEach(() => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated'
      });
    });

    test('formats complete address using country template', async () => {
      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Wait for address format to be fetched and applied
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/countries?q=United%20States');
        expect(mockFetch).toHaveBeenCalledWith('/api/countries/1/address-format');
      });

      // Should display formatted address using US template
      await waitFor(() => {
        const addressText = screen.getByText(/123 Main Street/);
        expect(addressText).toBeInTheDocument();
      });
    });

    test('handles partial address data gracefully', async () => {
      const partialUserData = {
        user: {
          ...mockUserData.user,
          streetAddress2: '', // Missing apt/suite
          regionText: '', // Missing region
        }
      };

      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/users/123')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(partialUserData)
          });
        }
        return defaultFetchImplementation(url);
      });

      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Should still display available address components
      await waitFor(() => {
        const addressText = screen.getByText(/123 Main Street/);
        expect(addressText).toBeInTheDocument();
      });
    });

    test('displays fallback when no address template is available', async () => {
      const noCountryUserData = {
        user: {
          ...mockUserData.user,
          country: '', // No country to fetch address format
        }
      };

      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/users/123')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(noCountryUserData)
          });
        }
        return defaultFetchImplementation(url);
      });

      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Should display basic comma-separated address
      await waitFor(() => {
        const addressText = screen.getByText(/123 Main Street/);
        expect(addressText).toBeInTheDocument();
      });
    });

    test('handles missing address components without breaking layout', async () => {
      const emptyAddressUserData = {
        user: {
          ...mockUserData.user,
          streetAddress1: '',
          streetAddress2: '',
          town: '',
          country: '',
          postalCode: '',
          regionText: '',
        }
      };

      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/users/123')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(emptyAddressUserData)
          });
        }
        return defaultFetchImplementation(url);
      });

      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Should display "No address information" message
      await waitFor(() => {
        expect(screen.getByText('No address information')).toBeInTheDocument();
      });
    });

    test('formats address using different country templates', async () => {
      const ukUserData = {
        user: {
          ...mockUserData.user,
          country: 'United Kingdom',
          streetAddress1: '123 High Street',
          town: 'London',
          regionText: 'Greater London',
          postalCode: 'SW1A 1AA',
        }
      };

      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/users/123')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(ukUserData)
          });
        }
        if (url.includes('/api/countries') && url.includes('United Kingdom')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve([{ id: '2', name: 'United Kingdom' }])
          });
        }
        return defaultFetchImplementation(url);
      });

      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Should fetch UK address format
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/countries?q=United%20Kingdom');
        expect(mockFetch).toHaveBeenCalledWith('/api/countries/2/address-format');
      });
    });

    test('displays region information correctly (prefer regionText over regionId)', async () => {
      const regionUserData = {
        user: {
          ...mockUserData.user,
          regionId: '1',
          regionText: 'California State',
        }
      };

      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/users/123')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(regionUserData)
          });
        }
        return defaultFetchImplementation(url);
      });

      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Should prefer regionText over regionId
      await waitFor(() => {
        const addressText = screen.getByText(/California State/);
        expect(addressText).toBeInTheDocument();
      });
    });

    test('implements proper fallback chain: regionText → regionId → empty', async () => {
      const noRegionTextUserData = {
        user: {
          ...mockUserData.user,
          regionId: '1',
          regionText: '', // Empty regionText, should use fallback
        }
      };

      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/users/123')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(noRegionTextUserData)
          });
        }
        return defaultFetchImplementation(url);
      });

      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Should still display address even without regionText
      await waitFor(() => {
        const addressText = screen.getByText(/123 Main Street/);
        expect(addressText).toBeInTheDocument();
      });
    });
  });

  describe('Backward Compatibility', () => {
    test('handles users with old address format data', async () => {
      mockUseSession.mockReturnValue({
        data: { ...mockSession, user: { ...mockSession.user, id: '124' }},
        status: 'authenticated'
      });

      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });

      // Should display legacy streetAddress field
      await waitFor(() => {
        const addressText = screen.getByText(/456 Legacy Street/);
        expect(addressText).toBeInTheDocument();
      });
    });

    test('properly maps legacy streetAddress to new structure', async () => {
      mockUseSession.mockReturnValue({
        data: { ...mockSession, user: { ...mockSession.user, id: '124' }},
        status: 'authenticated'
      });

      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });

      // Verify legacy field is used in address formatting
      await waitFor(() => {
        const addressContainer = screen.getByText(/456 Legacy Street/);
        expect(addressContainer).toBeInTheDocument();
      });
    });
  });

  describe('Personal Information Display', () => {
    beforeEach(() => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated'
      });
    });

    test('displays all personal information fields when available', async () => {
      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Check personal information fields
      const primaryEmailInstances = screen.getAllByText('<EMAIL>');
      expect(primaryEmailInstances.length).toBeGreaterThan(0);
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('******-0123')).toBeInTheDocument();
      expect(screen.getByText('******-0456')).toBeInTheDocument();
      const dobDisplay = screen.getByText((content) => content.includes('1980'));
      expect(dobDisplay).toBeInTheDocument();
    });

    test('displays identification information when available', async () => {
      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Check identification fields
      expect(screen.getByText('Peace Ambassador Number')).toBeInTheDocument();
      expect(screen.getByText('PA-001')).toBeInTheDocument();
    });

    test('displays biography when available', async () => {
      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Check biography
      expect(screen.getByText('Experienced diplomat with a passion for international relations.')).toBeInTheDocument();
    });

    test('hides sections when no data is available', async () => {
      const minimalUserData = {
        user: {
          id: '123',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>'
        }
      };

      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/users/123')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(minimalUserData)
          });
        }
        return defaultFetchImplementation(url);
      });

      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Identification section should not be rendered
      expect(screen.queryByText('Identification')).not.toBeInTheDocument();
      
      // Biography section should not be rendered
      expect(screen.queryByText('Biography')).not.toBeInTheDocument();
    });
  });

  describe('User Avatar and Header', () => {
    beforeEach(() => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated'
      });
    });

    test('displays user initials in avatar', async () => {
      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('JD')).toBeInTheDocument(); // John Doe initials
      });
    });

    test('handles single name gracefully', async () => {
      const singleNameUserData = {
        user: {
          ...mockUserData.user,
          firstName: 'John',
          lastName: ''
        }
      };

      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/users/123')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(singleNameUserData)
          });
        }
        return defaultFetchImplementation(url);
      });

      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('John')).toBeInTheDocument();
      });

      // Should display at least the first initial in the avatar
      const avatarInitial = screen.getByText('J', { selector: 'span' });
      expect(avatarInitial).toBeInTheDocument();
    });
  });

  test('handles API errors gracefully', async () => {
    mockUseSession.mockReturnValue({
      data: mockSession,
      status: 'authenticated'
    });

    // Mock API error
    mockFetch.mockImplementation(() => Promise.reject(new Error('API Error')));

    render(<UserProfile />);
    
    // Should still render basic session data without crashing
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
  });
});
