import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { UpdateUserTab } from '@/components/users/manage/UpdateUserTab';
import { toast } from 'sonner';

// Mock the toast module
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  }
}));

// Mock fetch for different endpoints
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock data
const mockTitles = [
  { id: '1', name: 'Ambassador', description: 'Diplomatic position', isActive: true },
  { id: '2', name: 'Consul', description: 'Consular position', isActive: true }
];

const mockPositions = [
  { id: '1', title: 'Senior Ambassador', description: 'Senior diplomatic position', level: 1, parentId: null, isActive: true },
  { id: '2', title: 'Trade Representative', description: 'Trade position', level: 2, parentId: '1', isActive: true }
];

const mockCountries = [
  { id: '1', name: 'United States' },
  { id: '2', name: 'United Kingdom' },
  { id: '3', name: 'Canada' }
];

const mockRegions = [
  { id: '1', name: 'California', code: 'CA' },
  { id: '2', name: 'Texas', code: 'TX' },
  { id: '3', name: 'Ontario', code: 'ON' }
];

const mockUSAddressFormat = {
  format: [
    { field: 'streetAddress1', label: 'Street Address', required: true },
    { field: 'streetAddress2', label: 'Apt/Suite/Unit', required: false },
    { field: 'town', label: 'City', required: true },
    { field: 'regionText', label: 'State', required: true },
    { field: 'postalCode', label: 'ZIP Code', required: true }
  ],
  example: '123 Main St, Apt 4B, New York, NY 10001'
};

const mockUKAddressFormat = {
  format: [
    { field: 'streetAddress1', label: 'Address Line 1', required: true },
    { field: 'streetAddress2', label: 'Address Line 2', required: false },
    { field: 'town', label: 'Town/City', required: true },
    { field: 'regionText', label: 'County', required: false },
    { field: 'postalCode', label: 'Postcode', required: true }
  ],
  example: '123 High Street, London, SW1A 1AA'
};

const mockUser = {
  id: '123',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '******-0123',
  mobile: '******-0456',
  streetAddress: '123 Old Street', // Legacy field for backward compatibility testing
  streetAddress1: '',
  streetAddress2: '',
  town: 'Springfield',
  city: 'Springfield',
  country: 'United States',
  postalCode: '12345',
  regionId: '1',
  regionText: 'California',
  dob: '1980-01-01',
  peaceAmbassadorNumber: 'PA-001',
  bio: 'Experienced diplomat',
  titleId: '1',
  positionId: '1',
  position: 'Ambassador'
};

const mockUserSearchResults = {
  users: [
    {
      id: '123',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      position: 'Ambassador'
    }
  ]
};

describe('UpdateUserTab', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock responses
    mockFetch.mockImplementation((url: string, options?: any) => {
      if (url.includes('/api/positions/titles')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockTitles)
        });
      }
      if (url.includes('/api/positions/positions')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ positions: mockPositions })
        });
      }
      if (url.includes('/api/countries') && url.includes('q=')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockCountries)
        });
      }
      if (url.includes('/api/regions')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockRegions)
        });
      }
      if (url.includes('/api/countries/1/address-format')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockUSAddressFormat)
        });
      }
      if (url.includes('/api/countries/2/address-format')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockUKAddressFormat)
        });
      }
      if (url.includes('/api/users') && url.includes('search=')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockUserSearchResults)
        });
      }
      if (url.includes('/api/users/123') && !options?.method) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ user: mockUser })
        });
      }
      if (url.includes('/api/users/123') && options?.method === 'PUT') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ success: true, message: 'User updated successfully' })
        });
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve([])
      });
    });
  });

  test('renders user search interface', () => {
    render(<UpdateUserTab />);
    
    expect(screen.getByLabelText('Search User')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search by name or email...')).toBeInTheDocument();
  });

  test('shows user suggestions when typing', async () => {
    render(<UpdateUserTab />);
    
    const searchInput = screen.getByPlaceholderText('Search by name or email...');
    
    await userEvent.type(searchInput, 'John Doe');
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Ambassador')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
  });

  test('loads user data when suggestion is clicked', async () => {
    render(<UpdateUserTab />);
    
    const searchInput = screen.getByPlaceholderText('Search by name or email...');
    
    await userEvent.type(searchInput, 'John Doe');
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    fireEvent.mouseDown(screen.getByText('John Doe'));

    await waitFor(() => {
      expect(screen.getByDisplayValue('John')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    });
  });

  describe('Address Fields Structure', () => {
    beforeEach(async () => {
      render(<UpdateUserTab />);
      
      const searchInput = screen.getByPlaceholderText('Search by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      fireEvent.mouseDown(screen.getByText('John Doe'));

      await waitFor(() => {
        expect(screen.getByDisplayValue('John')).toBeInTheDocument();
      });
    });

    test('renders new address field structure with streetAddress1 and streetAddress2', () => {
      // Check that streetAddress1 field is present
      expect(screen.getByLabelText(/street address/i)).toBeInTheDocument();
      
      // Check that streetAddress2 field is present
      expect(screen.getByLabelText(/apt.*suite.*unit/i)).toBeInTheDocument();
    });

    test('renders town field separate from city field', () => {
      // Check that both town and city fields are present
      expect(screen.getByLabelText(/town/i) || screen.getByLabelText(/city/i)).toBeInTheDocument();
      expect(screen.getByDisplayValue('Springfield')).toBeInTheDocument();
    });

    test('renders postal code field with proper structure', () => {
      // Check that postal code field is present
      expect(screen.getByLabelText(/postal code/i) || screen.getByLabelText(/zip code/i)).toBeInTheDocument();
      expect(screen.getByDisplayValue('12345')).toBeInTheDocument();
    });

    test('renders region selection dropdown', () => {
      // Check that region selection is present
      const regionField = screen.getByLabelText(/region/i) || screen.getByLabelText(/state/i);
      expect(regionField).toBeInTheDocument();
    });

    test('handles backward compatibility with old streetAddress field', () => {
      // When loading a user with the old streetAddress field, it should map to streetAddress1
      expect(screen.getByDisplayValue('123 Old Street')).toBeInTheDocument();
    });
  });

  describe('Country-Specific Field Labels', () => {
    beforeEach(async () => {
      render(<UpdateUserTab />);
      
      const searchInput = screen.getByPlaceholderText('Search by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      fireEvent.mouseDown(screen.getByText('John Doe'));

      await waitFor(() => {
        expect(screen.getByDisplayValue('John')).toBeInTheDocument();
      });
    });

    test('updates field labels when US is selected', async () => {
      const countryInput = screen.getByDisplayValue('United States');
      
      await userEvent.clear(countryInput);
      await userEvent.type(countryInput, 'United States');
      
      // Wait for country dropdown to appear
      await waitFor(() => {
        if (screen.queryByText('United States')) {
          fireEvent.mouseDown(screen.getByText('United States'));
        }
      });

      // Check for US-specific labels (these will be set by address format)
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/countries/1/address-format');
      });
    });

    test('fetches address format when country is selected', async () => {
      const countryInput = screen.getByDisplayValue('United States');
      
      await userEvent.clear(countryInput);
      await userEvent.type(countryInput, 'United Kingdom');
      
      await waitFor(() => {
        if (screen.queryByText('United Kingdom')) {
          fireEvent.mouseDown(screen.getByText('United Kingdom'));
        }
      });

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/countries/2/address-format');
      });
    });
  });

  describe('Region Selection Functionality', () => {
    beforeEach(async () => {
      render(<UpdateUserTab />);
      
      const searchInput = screen.getByPlaceholderText('Search by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      fireEvent.mouseDown(screen.getByText('John Doe'));

      await waitFor(() => {
        expect(screen.getByDisplayValue('John')).toBeInTheDocument();
      });
    });

    test('loads regions when country is selected', async () => {
      const countryInput = screen.getByDisplayValue('United States');
      
      await userEvent.clear(countryInput);
      await userEvent.type(countryInput, 'United States');
      
      await waitFor(() => {
        if (screen.queryByText('United States')) {
          fireEvent.mouseDown(screen.getByText('United States'));
        }
      });

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/regions?countryId=1');
      });
    });

    test('clears region selection when country changes', async () => {
      const countryInput = screen.getByDisplayValue('United States');
      
      await userEvent.clear(countryInput);
      await userEvent.type(countryInput, 'Canada');
      
      await waitFor(() => {
        if (screen.queryByText('Canada')) {
          fireEvent.mouseDown(screen.getByText('Canada'));
        }
      });

      // Region should be cleared when country changes
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/regions?countryId=3');
      });
    });

    test('pre-selects current region value when editing user', () => {
      // Check that the current region value is pre-selected
      const regionField = screen.getByDisplayValue('1') || screen.getByDisplayValue('California');
      expect(regionField).toBeInTheDocument();
    });

    test('displays region dropdown when regions are available', async () => {
      // Should display a dropdown when regions are available
      const regionSelect = screen.queryByRole('combobox', { name: /region/i }) || 
                          screen.queryByRole('combobox', { name: /state/i });
      
      if (regionSelect) {
        expect(regionSelect).toBeInTheDocument();
      }
    });

    test('allows free-form region entry when regions are not available', async () => {
      // Mock empty regions response
      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/regions')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve([])
          });
        }
        return Promise.resolve({ ok: true, json: () => Promise.resolve([]) });
      });

      const countryInput = screen.getByDisplayValue('United States');
      
      await userEvent.clear(countryInput);
      await userEvent.type(countryInput, 'United States');
      
      await waitFor(() => {
        if (screen.queryByText('United States')) {
          fireEvent.mouseDown(screen.getByText('United States'));
        }
      });

      // Should show text input for region when no regions available
      await waitFor(() => {
        const regionTextInput = screen.queryByLabelText(/region/i);
        if (regionTextInput && regionTextInput.tagName === 'INPUT') {
          expect(regionTextInput).toBeInTheDocument();
        }
      });
    });
  });

  describe('Form Submission with New Address Structure', () => {
    beforeEach(async () => {
      render(<UpdateUserTab />);
      
      const searchInput = screen.getByPlaceholderText('Search by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      fireEvent.mouseDown(screen.getByText('John Doe'));

      await waitFor(() => {
        expect(screen.getByDisplayValue('John')).toBeInTheDocument();
      });
    });

    test('submits form with new address field structure', async () => {
      // Fill in address fields
      const streetAddress1Input = screen.getByLabelText(/street address/i);
      await userEvent.clear(streetAddress1Input);
      await userEvent.type(streetAddress1Input, '456 New Street');

      const streetAddress2Input = screen.getByLabelText(/apt.*suite.*unit/i);
      await userEvent.type(streetAddress2Input, 'Suite 200');

      // Submit form
      const submitButton = screen.getByText('Update User');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/users/123', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: expect.stringContaining('streetAddress1')
        });
      });
    });

    test('includes all new address fields in submission data', async () => {
      let submittedData: any;
      
      mockFetch.mockImplementation((url: string, options?: any) => {
        if (url.includes('/api/users/123') && options?.method === 'PUT') {
          submittedData = JSON.parse(options.body);
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ success: true, message: 'User updated successfully' })
          });
        }
        // Return original mocked responses for other calls
        return mockFetch.getMockImplementation()!(url, options);
      });

      // Submit form
      const submitButton = screen.getByText('Update User');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(submittedData).toBeDefined();
        
        // Verify new address structure is present
        expect(submittedData).toHaveProperty('streetAddress1');
        expect(submittedData).toHaveProperty('streetAddress2');
        expect(submittedData).toHaveProperty('town');
        expect(submittedData).toHaveProperty('regionId');
        expect(submittedData).toHaveProperty('regionText');
        expect(submittedData).toHaveProperty('postalCode');
        
        // Verify old single streetAddress field is not present
        expect(submittedData).not.toHaveProperty('streetAddress');
      });
    });

    test('shows success message when user is updated', async () => {
      const submitButton = screen.getByText('Update User');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('User updated successfully!');
      });
    });

    test('validates required fields based on country rules', async () => {
      // Clear required field
      const streetAddress1Input = screen.getByLabelText(/street address/i);
      await userEvent.clear(streetAddress1Input);

      const submitButton = screen.getByText('Update User');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(
          expect.stringContaining('required fields')
        );
      });
    });
  });

  describe('Data Migration and Backward Compatibility', () => {
    test('handles users with old address format data', async () => {
      const legacyUser = {
        ...mockUser,
        streetAddress: '789 Legacy Street', // Old field
        streetAddress1: '', // New field empty
        streetAddress2: ''
      };

      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/users/123') && !url.includes('search=')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ user: legacyUser })
          });
        }
        return mockFetch.getMockImplementation()!(url);
      });

      render(<UpdateUserTab />);
      
      const searchInput = screen.getByPlaceholderText('Search by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      fireEvent.mouseDown(screen.getByText('John Doe'));

      // Should map legacy streetAddress to streetAddress1
      await waitFor(() => {
        expect(screen.getByDisplayValue('789 Legacy Street')).toBeInTheDocument();
      });
    });

    test('preserves address data integrity during updates', async () => {
      render(<UpdateUserTab />);
      
      const searchInput = screen.getByPlaceholderText('Search by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      fireEvent.mouseDown(screen.getByText('John Doe'));

      await waitFor(() => {
        expect(screen.getByDisplayValue('John')).toBeInTheDocument();
      });

      // Verify all address fields are preserved
      expect(screen.getByDisplayValue('123 Old Street')).toBeInTheDocument(); // streetAddress1
      expect(screen.getByDisplayValue('Springfield')).toBeInTheDocument(); // town
      expect(screen.getByDisplayValue('United States')).toBeInTheDocument(); // country
      expect(screen.getByDisplayValue('12345')).toBeInTheDocument(); // postalCode
    });
  });

  test('shows loading state while searching', async () => {
    // Mock delayed response
    mockFetch.mockImplementation(() => new Promise(resolve => 
      setTimeout(() => resolve({
        ok: true,
        json: () => Promise.resolve(mockUserSearchResults)
      }), 100)
    ));

    render(<UpdateUserTab />);
    
    const searchInput = screen.getByPlaceholderText('Search by name or email...');
    await userEvent.type(searchInput, 'John Doe');
    
    // Should show some loading indication (this depends on the exact implementation)
    // Since the component uses debounced search, we wait for the response
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalled();
    }, { timeout: 1000 });
  });

  test('handles user not found scenario', async () => {
    mockFetch.mockImplementation((url: string) => {
      if (url.includes('/api/users') && url.includes('search=')) {
        return Promise.resolve({
          ok: false,
          status: 404,
          json: () => Promise.resolve({ error: 'User not found' })
        });
      }
      return Promise.resolve({ ok: true, json: () => Promise.resolve([]) });
    });

    render(<UpdateUserTab />);
    
    const searchInput = screen.getByPlaceholderText('Search by name or email...');
    await userEvent.type(searchInput, 'NonExistent User');

    // Should handle gracefully without crashing
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalled();
    });
  });
});
