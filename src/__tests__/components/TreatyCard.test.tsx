import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { TreatyCard, TreatyCardData } from '@/components/treaties/TreatyCard';

describe('TreatyCard', () => {
  const mockTreaty: TreatyCardData = {
    id: 'treaty-123',
    userId: 'user-456',
    treatyTypeId: 'treaty-type-789',
    treatyTypeName: 'Business License Application',
    status: 'draft',
    createdAt: '2025-01-15T10:00:00Z',
    updatedAt: '2025-01-15T10:00:00Z',
    applicantName: 'John <PERSON>',
    businessName: 'Doe Enterprises',
    country: 'United States',
    city: 'New York',
    hasSignature: true,
    hasAttachments: true
  };

  const mockActions = {
    onView: jest.fn(),
    onEdit: jest.fn(),
    onArchive: jest.fn(),
    onDownloadPDF: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders treaty card with basic information', () => {
    render(<TreatyCard treaty={mockTreaty} {...mockActions} />);

    expect(screen.getByText('Business License Application')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Doe Enterprises')).toBeInTheDocument();
    expect(screen.getByText('New York, United States')).toBeInTheDocument();
  });

  it('displays status badge with correct styling', () => {
    render(<TreatyCard treaty={mockTreaty} {...mockActions} />);

    const statusBadge = screen.getByText('draft');
    expect(statusBadge).toBeInTheDocument();
    expect(statusBadge).toHaveClass('capitalize');
  });

  it('shows signature and attachments badges when present', () => {
    render(<TreatyCard treaty={mockTreaty} {...mockActions} />);

    expect(screen.getByText('Signed')).toBeInTheDocument();
    expect(screen.getByText('Attachments')).toBeInTheDocument();
  });

  it('does not show signature and attachments badges when absent', () => {
    const treatyWithoutExtras = {
      ...mockTreaty,
      hasSignature: false,
      hasAttachments: false
    };

    render(<TreatyCard treaty={treatyWithoutExtras} {...mockActions} />);

    expect(screen.queryByText('Signed')).not.toBeInTheDocument();
    expect(screen.queryByText('Attachments')).not.toBeInTheDocument();
  });

  it('displays formatted creation date', () => {
    render(<TreatyCard treaty={mockTreaty} {...mockActions} />);

    expect(screen.getByText(/Created:/)).toBeInTheDocument();
  });

  it('displays submitted date when status is submitted', () => {
    const submittedTreaty = {
      ...mockTreaty,
      status: 'submitted' as const,
      submittedAt: '2025-01-16T14:30:00Z'
    };

    render(<TreatyCard treaty={submittedTreaty} {...mockActions} />);

    expect(screen.getByText(/Submitted:/)).toBeInTheDocument();
  });

  it('renders all action buttons', () => {
    render(<TreatyCard treaty={mockTreaty} {...mockActions} />);

    expect(screen.getByText('View')).toBeInTheDocument();
    expect(screen.getByText('Edit')).toBeInTheDocument();
    expect(screen.getByText('PDF')).toBeInTheDocument();
    expect(screen.getByText('Archive')).toBeInTheDocument();
  });

  it('calls onView when View button is clicked', () => {
    render(<TreatyCard treaty={mockTreaty} {...mockActions} />);

    fireEvent.click(screen.getByText('View'));
    expect(mockActions.onView).toHaveBeenCalledWith(mockTreaty);
  });

  it('calls onEdit when Edit button is clicked for draft status', () => {
    render(<TreatyCard treaty={mockTreaty} {...mockActions} />);

    fireEvent.click(screen.getByText('Edit'));
    expect(mockActions.onEdit).toHaveBeenCalledWith(mockTreaty);
  });

  it('does not show Edit button for non-draft status', () => {
    const submittedTreaty = {
      ...mockTreaty,
      status: 'submitted' as const
    };

    render(<TreatyCard treaty={submittedTreaty} {...mockActions} />);

    expect(screen.queryByText('Edit')).not.toBeInTheDocument();
  });

  it('calls onDownloadPDF when PDF button is clicked', () => {
    render(<TreatyCard treaty={mockTreaty} {...mockActions} />);

    fireEvent.click(screen.getByText('PDF'));
    expect(mockActions.onDownloadPDF).toHaveBeenCalledWith(mockTreaty);
  });

  it('calls onArchive when Archive button is clicked', () => {
    render(<TreatyCard treaty={mockTreaty} {...mockActions} />);

    fireEvent.click(screen.getByText('Archive'));
    expect(mockActions.onArchive).toHaveBeenCalledWith(mockTreaty);
  });

  it('does not show Archive button for archived status', () => {
    const archivedTreaty = {
      ...mockTreaty,
      status: 'archived' as const
    };

    render(<TreatyCard treaty={archivedTreaty} {...mockActions} />);

    expect(screen.queryByText('Archive')).not.toBeInTheDocument();
  });

  it('applies correct status colors for different statuses', () => {
    const statuses = ['draft', 'submitted', 'approved', 'rejected', 'archived'] as const;

    statuses.forEach(status => {
      const treatyWithStatus = { ...mockTreaty, status };
      const { rerender } = render(<TreatyCard treaty={treatyWithStatus} {...mockActions} />);

      const statusBadge = screen.getByText(status);
      expect(statusBadge).toBeInTheDocument();

      rerender(<TreatyCard treaty={{ ...mockTreaty, status: statuses[0] }} {...mockActions} />);
    });
  });

  it('handles missing optional fields gracefully', () => {
    const minimalTreaty: TreatyCardData = {
      id: 'treaty-123',
      userId: 'user-456',
      treatyTypeId: 'treaty-type-789',
      treatyTypeName: 'Basic Application',
      status: 'draft',
      createdAt: '2025-01-15T10:00:00Z',
      updatedAt: '2025-01-15T10:00:00Z'
    };

    render(<TreatyCard treaty={minimalTreaty} {...mockActions} />);

    expect(screen.getByText('Basic Application')).toBeInTheDocument();
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
    expect(screen.queryByText('Doe Enterprises')).not.toBeInTheDocument();
  });

  it('truncates long text appropriately', () => {
    const treatyWithLongNames = {
      ...mockTreaty,
      treatyTypeName: 'Very Long Treaty Type Name That Should Be Truncated',
      applicantName: 'Very Long Applicant Name That Should Also Be Truncated',
      businessName: 'Very Long Business Name That Should Definitely Be Truncated'
    };

    render(<TreatyCard treaty={treatyWithLongNames} {...mockActions} />);

    // Check that the text is present (truncation is handled by CSS classes)
    expect(screen.getByText('Very Long Treaty Type Name That Should Be Truncated')).toBeInTheDocument();
  });
});