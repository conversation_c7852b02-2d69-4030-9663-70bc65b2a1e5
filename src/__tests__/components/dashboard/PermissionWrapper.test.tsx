import React from 'react';
import { render, screen } from '@testing-library/react';
import { PermissionWrapper, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ServerAccess, AnalyticsAccess } from '@/components/dashboard/PermissionWrapper';
import { PERMISSIONS } from '@/lib/constants/permissions';

describe('PermissionWrapper', () => {
  const mockChildren = <div data-testid="protected-content">Protected Content</div>;
  const mockFallback = <div data-testid="fallback-content">Access Denied</div>;
  const mockLoading = <div data-testid="loading-spinner">Loading...</div>;

  describe('Basic PermissionWrapper', () => {
    it('renders children when no permissions are required', () => {
      render(
        <PermissionWrapper>
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('renders children when user has required permissions', () => {
      const userPermissions = [PERMISSIONS.REMOTE_SERVER_READ];

      render(
        <PermissionWrapper
          requiredPermissions={[PERMISSIONS.REMOTE_SERVER_READ]}
          userPermissions={userPermissions}
        >
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('renders children when user has required role', () => {
      render(
        <PermissionWrapper
          requiredRoles={['ADMIN']}
          userRole="ADMIN"
        >
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('renders fallback when user lacks required permissions', () => {
      const userPermissions = [PERMISSIONS.PROFILE_READ];

      render(
        <PermissionWrapper
          requiredPermissions={[PERMISSIONS.REMOTE_SERVER_READ]}
          userPermissions={userPermissions}
          fallback={mockFallback}
        >
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('renders fallback when user lacks required role', () => {
      render(
        <PermissionWrapper
          requiredRoles={['ADMIN']}
          userRole="MEMBER"
          fallback={mockFallback}
        >
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('renders null when no fallback is provided and access is denied', () => {
      const userPermissions = [PERMISSIONS.PROFILE_READ];

      render(
        <PermissionWrapper
          requiredPermissions={[PERMISSIONS.REMOTE_SERVER_READ]}
          userPermissions={userPermissions}
        >
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.queryByTestId('fallback-content')).not.toBeInTheDocument();
    });

    it('shows loading component when permissions are loading', () => {
      render(
        <PermissionWrapper
          permissionsLoading={true}
          loadingComponent={mockLoading}
        >
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('shows default loading spinner when permissions are loading', () => {
      render(
        <PermissionWrapper
          permissionsLoading={true}
        >
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.getByText('Loading...')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('accepts multiple required permissions (OR logic)', () => {
      const userPermissions = [PERMISSIONS.PROFILE_READ];

      render(
        <PermissionWrapper
          requiredPermissions={[PERMISSIONS.REMOTE_SERVER_READ, PERMISSIONS.PROFILE_READ]}
          userPermissions={userPermissions}
        >
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('accepts multiple required roles (OR logic)', () => {
      render(
        <PermissionWrapper
          requiredRoles={['MEMBER', 'ADMIN']}
          userRole="MEMBER"
        >
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('requires both role AND permission when both are specified', () => {
      const userPermissions = [PERMISSIONS.REMOTE_SERVER_READ];

      render(
        <PermissionWrapper
          requiredRoles={['ADMIN']}
          requiredPermissions={[PERMISSIONS.REMOTE_SERVER_READ]}
          userPermissions={userPermissions}
          userRole="MEMBER"
          fallback={mockFallback}
        >
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('AdminOnly wrapper', () => {
    it('renders children for admin users', () => {
      render(
        <AdminOnly userRole="ADMIN">
          {mockChildren}
        </AdminOnly>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('renders fallback for non-admin users', () => {
      render(
        <AdminOnly userRole="MEMBER" fallback={mockFallback}>
          {mockChildren}
        </AdminOnly>
      );

      expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('MemberOnly wrapper', () => {
    it('renders children for member users', () => {
      render(
        <MemberOnly userRole="MEMBER">
          {mockChildren}
        </MemberOnly>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('renders children for admin users', () => {
      render(
        <MemberOnly userRole="ADMIN">
          {mockChildren}
        </MemberOnly>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('renders fallback for non-member users', () => {
      render(
        <MemberOnly userRole="PEACE" fallback={mockFallback}>
          {mockChildren}
        </MemberOnly>
      );

      expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('ServerAccess wrapper', () => {
    it('renders children for users with server permissions', () => {
      const userPermissions = [PERMISSIONS.REMOTE_SERVER_READ];

      render(
        <ServerAccess userPermissions={userPermissions}>
          {mockChildren}
        </ServerAccess>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('renders fallback for users without server permissions', () => {
      const userPermissions = [PERMISSIONS.PROFILE_READ];

      render(
        <ServerAccess userPermissions={userPermissions} fallback={mockFallback}>
          {mockChildren}
        </ServerAccess>
      );

      expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('AnalyticsAccess wrapper', () => {
    it('renders children for users with admin permissions', () => {
      const userPermissions = [PERMISSIONS.ADMIN_PANEL_ACCESS];

      render(
        <AnalyticsAccess userPermissions={userPermissions}>
          {mockChildren}
        </AnalyticsAccess>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('renders fallback for users without admin permissions', () => {
      const userPermissions = [PERMISSIONS.REMOTE_SERVER_READ];

      render(
        <AnalyticsAccess userPermissions={userPermissions} fallback={mockFallback}>
          {mockChildren}
        </AnalyticsAccess>
      );

      expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('Edge cases', () => {
    it('handles empty permission arrays gracefully', () => {
      render(
        <PermissionWrapper requiredPermissions={[]}>
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('handles empty role arrays gracefully', () => {
      render(
        <PermissionWrapper requiredRoles={[]}>
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    });

    it('handles undefined permissions gracefully', () => {
      render(
        <PermissionWrapper requiredPermissions={[PERMISSIONS.REMOTE_SERVER_READ]}>
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('handles undefined role gracefully', () => {
      render(
        <PermissionWrapper requiredRoles={['ADMIN']}>
          {mockChildren}
        </PermissionWrapper>
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });
});