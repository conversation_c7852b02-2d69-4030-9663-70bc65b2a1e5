import React from 'react';
import { render, screen } from '@testing-library/react';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';

describe('DashboardLayout', () => {
  it('renders without crashing with valid children', () => {
    render(
      <DashboardLayout>
        <div>Test Content</div>
      </DashboardLayout>
    );

    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('renders with test id for testing', () => {
    render(
      <DashboardLayout>
        <div>Test Content</div>
      </DashboardLayout>
    );

    const layoutContainer = screen.getByTestId('dashboard-layout');
    expect(layoutContainer).toBeInTheDocument();
  });

  it('applies correct CSS classes for layout structure', () => {
    render(
      <DashboardLayout>
        <div>Test Content</div>
      </DashboardLayout>
    );

    const layoutContainer = screen.getByTestId('dashboard-layout');
    expect(layoutContainer).toHaveClass('flex', 'flex-col', 'h-screen', 'bg-white', 'dark:bg-gray-900');
  });

  it('handles missing children gracefully', () => {
    render(<DashboardLayout />);

    const layoutContainer = screen.getByTestId('dashboard-layout');
    expect(layoutContainer).toBeInTheDocument();
  });

  it('passes through DOM attributes correctly', () => {
    render(
      <DashboardLayout data-testid="custom-dashboard" className="custom-class">
        <div>Test Content</div>
      </DashboardLayout>
    );

    const layoutContainer = screen.getByTestId('custom-dashboard');
    expect(layoutContainer).toHaveClass('custom-class');
  });

  it('renders children in the correct location', () => {
    render(
      <DashboardLayout>
        <div data-testid="child-content">Child Content</div>
      </DashboardLayout>
    );

    const childContent = screen.getByTestId('child-content');
    expect(childContent).toBeInTheDocument();
    expect(childContent).toHaveTextContent('Child Content');
  });

  it('maintains proper component structure', () => {
    render(
      <DashboardLayout>
        <header>Header Content</header>
        <main>Main Content</main>
        <footer>Footer Content</footer>
      </DashboardLayout>
    );

    expect(screen.getByText('Header Content')).toBeInTheDocument();
    expect(screen.getByText('Main Content')).toBeInTheDocument();
    expect(screen.getByText('Footer Content')).toBeInTheDocument();
  });

  it('handles complex nested children', () => {
    render(
      <DashboardLayout>
        <div>
          <h1>Dashboard Title</h1>
          <section>
            <p>Section 1</p>
            <p>Section 2</p>
          </section>
        </div>
      </DashboardLayout>
    );

    expect(screen.getByText('Dashboard Title')).toBeInTheDocument();
    expect(screen.getByText('Section 1')).toBeInTheDocument();
    expect(screen.getByText('Section 2')).toBeInTheDocument();
  });
});