import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { TabNavigation } from '@/components/dashboard/TabNavigation';
import { PERMISSIONS } from '@/lib/constants/permissions';

describe('TabNavigation', () => {
  const mockTabs = [
    { id: 'overview', label: 'Overview', icon: 'LayoutDashboard' },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: 'Bell'
    },
    {
      id: 'servers',
      label: 'Servers',
      icon: 'Server',
      requiredPermissions: [PERMISSIONS.REMOTE_SERVER_READ]
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: 'BarChart3',
      requiredPermissions: [PERMISSIONS.ADMIN_PANEL_ACCESS]
    },
    {
      id: 'security',
      label: 'Security',
      icon: 'Shield',
      requiredPermissions: [PERMISSIONS.ADMIN_PANEL_ACCESS]
    },
    {
      id: 'system',
      label: 'System',
      icon: 'Settings',
      requiredPermissions: [PERMISSIONS.ADMIN_PANEL_ACCESS]
    },
  ];

  const defaultProps = {
    tabs: mockTabs,
    activeTab: 'overview',
    onTabChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders only tabs available to all users when no permissions are provided', () => {
    render(<TabNavigation {...defaultProps} />);

    // Tabs available to all users (no required permissions)
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Notifications')).toBeInTheDocument();

    // Tabs with required permissions should be filtered out
    expect(screen.queryByText('Servers')).not.toBeInTheDocument();
    expect(screen.queryByText('Analytics')).not.toBeInTheDocument();
    expect(screen.queryByText('Security')).not.toBeInTheDocument();
    expect(screen.queryByText('System')).not.toBeInTheDocument();
  });

  it('filters tabs based on user permissions', () => {
    const userPermissions = [PERMISSIONS.REMOTE_SERVER_READ];

    render(
      <TabNavigation
        {...defaultProps}
        userPermissions={userPermissions}
      />
    );

    // Should show tabs available to all users + servers tab
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByText('Servers')).toBeInTheDocument();

    // Should hide admin-only tabs
    expect(screen.queryByText('Analytics')).not.toBeInTheDocument();
    expect(screen.queryByText('Security')).not.toBeInTheDocument();
    expect(screen.queryByText('System')).not.toBeInTheDocument();
  });

  it('shows admin tabs when user has admin permissions', () => {
    const userPermissions = [
      PERMISSIONS.REMOTE_SERVER_READ,
      PERMISSIONS.ADMIN_PANEL_ACCESS
    ];

    render(
      <TabNavigation
        {...defaultProps}
        userPermissions={userPermissions}
      />
    );

    // Should show all tabs
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByText('Servers')).toBeInTheDocument();
    expect(screen.getByText('Analytics')).toBeInTheDocument();
    expect(screen.getByText('Security')).toBeInTheDocument();
    expect(screen.getByText('System')).toBeInTheDocument();
  });

  it('filters tabs based on user role', () => {
    const tabsWithRoles = [
      { id: 'overview', label: 'Overview', icon: 'LayoutDashboard' },
      {
        id: 'admin',
        label: 'Admin Panel',
        icon: 'Settings',
        roles: ['ADMIN']
      },
    ];

    render(
      <TabNavigation
        tabs={tabsWithRoles}
        activeTab="overview"
        onTabChange={jest.fn()}
        userRole="ADMIN"
      />
    );

    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Admin Panel')).toBeInTheDocument();
  });

  it('hides role-restricted tabs for non-matching roles', () => {
    const tabsWithRoles = [
      { id: 'overview', label: 'Overview', icon: 'LayoutDashboard' },
      {
        id: 'admin',
        label: 'Admin Panel',
        icon: 'Settings',
        roles: ['ADMIN']
      },
    ];

    render(
      <TabNavigation
        tabs={tabsWithRoles}
        activeTab="overview"
        onTabChange={jest.fn()}
        userRole="MEMBER"
      />
    );

    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.queryByText('Admin Panel')).not.toBeInTheDocument();
  });

  it('highlights the active tab correctly', () => {
    render(
      <TabNavigation
        {...defaultProps}
        activeTab="notifications"
      />
    );

    const notificationsTab = screen.getByText('Notifications');
    const overviewTab = screen.getByText('Overview');

    expect(notificationsTab).toHaveClass('border-blue-500', 'text-blue-600');
    expect(overviewTab).toHaveClass('border-transparent', 'text-gray-500');
  });

  it('calls onTabChange when a tab is clicked', () => {
    const mockOnTabChange = jest.fn();

    render(
      <TabNavigation
        {...defaultProps}
        onTabChange={mockOnTabChange}
      />
    );

    const notificationsTab = screen.getByText('Notifications');
    fireEvent.click(notificationsTab);

    expect(mockOnTabChange).toHaveBeenCalledWith('notifications');
  });

  it('applies custom className when provided', () => {
    render(
      <TabNavigation
        {...defaultProps}
        className="custom-nav-class"
      />
    );

    const nav = screen.getByRole('navigation').closest('div');
    expect(nav).toHaveClass('custom-nav-class');
  });

  it('handles empty tabs array gracefully', () => {
    render(
      <TabNavigation
        tabs={[]}
        activeTab="overview"
        onTabChange={jest.fn()}
      />
    );

    // Should render navigation container but no tab buttons
    const nav = screen.getByRole('navigation');
    expect(nav).toBeInTheDocument();
    expect(nav.children).toHaveLength(0);
  });

  it('maintains accessibility attributes', () => {
    render(<TabNavigation {...defaultProps} />);

    const nav = screen.getByRole('navigation');
    expect(nav).toHaveAttribute('aria-label', 'Tabs');

    const overviewTab = screen.getByText('Overview');
    expect(overviewTab.closest('button')).toHaveAttribute('aria-current', 'page');
  });
});