import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ApplicantInformationSection } from '@/components/treaties/ApplicantInformationSection';
import { TreatyTypeDetailsData } from '@/types/user-treaty-assignment';

describe('ApplicantInformationSection', () => {
  const mockOnChange = jest.fn();
  const mockFormData: TreatyTypeDetailsData = {
    fullLegalName: '<PERSON>',
    dateOfBirth: '1990-01-01',
    genderIdentity: 'male',
    email: '<EMAIL>',
    identificationNumber: '123456789',
    photographPath: 'photo.jpg',
    phoneNumbers: [
      { number: '************', type: 'mobile', primary: true },
      { number: '************', type: 'home', primary: false }
    ]
  };
  const mockErrors: Record<string, string> = {};

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all form fields correctly', () => {
    render(
      <ApplicantInformationSection
        formData={mockFormData}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    expect(screen.getByLabelText(/Full Legal Name/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Date of Birth/)).toBeInTheDocument();
    expect(screen.getByText('Gender / Identity')).toBeInTheDocument();
    expect(screen.getByLabelText(/Email Address/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Identification/)).toBeInTheDocument();
    expect(screen.getByText(/Photograph/)).toBeInTheDocument();
  });

  it('displays form data values correctly', () => {
    render(
      <ApplicantInformationSection
        formData={mockFormData}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument();
    expect(screen.getByDisplayValue('1990-01-01')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByDisplayValue('123456789')).toBeInTheDocument();
  });

  it('calls onChange when text inputs change', () => {
    render(
      <ApplicantInformationSection
        formData={{}}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    const nameInput = screen.getByLabelText(/Full Legal Name/);
    fireEvent.change(nameInput, { target: { value: 'Jane Smith' } });

    expect(mockOnChange).toHaveBeenCalledWith({ fullLegalName: 'Jane Smith' });
  });

  it('calls onChange when date input changes', () => {
    render(
      <ApplicantInformationSection
        formData={{}}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    const dateInput = screen.getByLabelText(/Date of Birth/);
    fireEvent.change(dateInput, { target: { value: '1985-05-15' } });

    expect(mockOnChange).toHaveBeenCalledWith({ dateOfBirth: '1985-05-15' });
  });

  it('calls onChange when gender select changes', () => {
    render(
      <ApplicantInformationSection
        formData={{}}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    const selectTrigger = screen.getByText('Select gender/identity');
    fireEvent.click(selectTrigger);

    const femaleOption = screen.getByText('Female');
    fireEvent.click(femaleOption);

    expect(mockOnChange).toHaveBeenCalledWith({ genderIdentity: 'female' });
  });

  it('renders phone numbers correctly', () => {
    render(
      <ApplicantInformationSection
        formData={mockFormData}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    expect(screen.getByDisplayValue('************')).toBeInTheDocument();
    expect(screen.getByDisplayValue('************')).toBeInTheDocument();
    expect(screen.getByText('Phone 1')).toBeInTheDocument();
    expect(screen.getByText('Phone 2')).toBeInTheDocument();
  });

  it('adds a new phone number when Add Phone button is clicked', () => {
    const formDataWithoutPhones: TreatyTypeDetailsData = {};
    render(
      <ApplicantInformationSection
        formData={formDataWithoutPhones}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    const addButton = screen.getByText('Add Phone');
    fireEvent.click(addButton);

    expect(mockOnChange).toHaveBeenCalledWith({
      phoneNumbers: [{ number: '', type: 'mobile', primary: false }]
    });
  });

  it('removes a phone number when remove button is clicked', () => {
    render(
      <ApplicantInformationSection
        formData={mockFormData}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    const removeButtons = screen.getAllByRole('button', { name: '' }); // Remove buttons have no text, just X icon
    const firstRemoveButton = removeButtons.find(button =>
      button.classList.contains('text-red-600')
    );
    fireEvent.click(firstRemoveButton!);

    const expectedPhones = [
      { number: '************', type: 'home', primary: false }
    ];
    expect(mockOnChange).toHaveBeenCalledWith({ phoneNumbers: expectedPhones });
  });

  it('sets a phone as primary when Primary button is clicked', () => {
    render(
      <ApplicantInformationSection
        formData={mockFormData}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    const primaryButtons = screen.getAllByText('Primary');
    fireEvent.click(primaryButtons[1]); // Click primary on second phone

    const expectedPhones = [
      { number: '************', type: 'mobile', primary: false },
      { number: '************', type: 'home', primary: true }
    ];
    expect(mockOnChange).toHaveBeenCalledWith({ phoneNumbers: expectedPhones });
  });

  it('updates phone number when input changes', () => {
    render(
      <ApplicantInformationSection
        formData={mockFormData}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    const phoneInputs = screen.getAllByDisplayValue('************');
    fireEvent.change(phoneInputs[0], { target: { value: '************' } });

    // The component may adjust primary status when updating phones
    expect(mockOnChange).toHaveBeenCalledWith(
      expect.objectContaining({
        phoneNumbers: expect.arrayContaining([
          expect.objectContaining({ number: '************', type: 'mobile' }),
          expect.objectContaining({ number: '************', type: 'home' })
        ])
      })
    );
  });

  it('updates phone type when select changes', () => {
    render(
      <ApplicantInformationSection
        formData={mockFormData}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    const typeSelects = screen.getAllByText('mobile'); // The select triggers show current value
    fireEvent.click(typeSelects[0]);

    const workOption = screen.getByText('Work');
    fireEvent.click(workOption);

    // The component may adjust primary status when updating phones
    expect(mockOnChange).toHaveBeenCalledWith(
      expect.objectContaining({
        phoneNumbers: expect.arrayContaining([
          expect.objectContaining({ number: '************', type: 'work' }),
          expect.objectContaining({ number: '************', type: 'home' })
        ])
      })
    );
  });

  it('handles file upload correctly', () => {
    render(
      <ApplicantInformationSection
        formData={{}}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    const fileInput = screen.getByLabelText('Click to upload photograph');
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

    fireEvent.change(fileInput, { target: { files: [file] } });

    expect(mockOnChange).toHaveBeenCalledWith({ photographPath: 'test.jpg' });
  });

  it('displays uploaded file name', () => {
    render(
      <ApplicantInformationSection
        formData={mockFormData}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    expect(screen.getByText('photo.jpg')).toBeInTheDocument();
  });

  it('displays error messages for required fields', () => {
    const errorsWithMessages = {
      fullLegalName: 'Full legal name is required',
      email: 'Email address is required'
    };

    render(
      <ApplicantInformationSection
        formData={{}}
        onChange={mockOnChange}
        errors={errorsWithMessages}
      />
    );

    expect(screen.getByText('Full legal name is required')).toBeInTheDocument();
    expect(screen.getByText('Email address is required')).toBeInTheDocument();
  });

  it('shows no phone numbers message when empty', () => {
    render(
      <ApplicantInformationSection
        formData={{}}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    expect(screen.getByText('No phone numbers added yet.')).toBeInTheDocument();
  });

  it('renders section title and description', () => {
    render(
      <ApplicantInformationSection
        formData={{}}
        onChange={mockOnChange}
        errors={mockErrors}
      />
    );

    expect(screen.getByText('Applicant Information')).toBeInTheDocument();
    expect(screen.getByText('Please provide your personal information and identification details.')).toBeInTheDocument();
  });
});