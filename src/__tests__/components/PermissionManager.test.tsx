import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useSession } from 'next-auth/react';
import PermissionManager from '@/components/settings/PermissionManager';

// Mock the next-auth useSession hook
jest.mock('next-auth/react');

// Mock fetch for API calls
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock data for testing
const mockSession = {
  user: {
    id: '123',
    name: '<PERSON>',
    email: '<EMAIL>',
    nwaEmail: '<EMAIL>'
  },
  expires: '2025-01-01T00:00:00.000Z'
};

const mockUsers = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    profile: {
      firstName: '<PERSON>',
      lastName: '<PERSON>'
    }
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    profile: {
      firstName: '<PERSON>',
      lastName: '<PERSON>'
    }
  }
];

const mockRoles = [
  {
    id: 'role-1',
    name: 'Admin',
    description: 'Full system access',
    permissions: ['user.read', 'user.write', 'admin.access']
  },
  {
    id: 'role-2',
    name: 'Editor',
    description: 'Content editing permissions',
    permissions: ['content.read', 'content.write']
  }
];

const mockUserPermissions = {
  userId: '1',
  user: {
    name: 'Alice Johnson',
    email: '<EMAIL>',
    profile: {
      firstName: 'Alice',
      lastName: 'Johnson'
    }
  },
  roles: [mockRoles[0]],
  directPermissions: [],
  allPermissions: ['user.read', 'user.write', 'admin.access']
};

describe('PermissionManager', () => {
  const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mock responses
    mockFetch.mockImplementation((url: string) => {
      if (url.includes('/api/users')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ users: mockUsers })
        });
      }
      if (url.includes('/api/roles')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ roles: mockRoles })
        });
      }
      if (url.includes('/api/users/1/permissions')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ data: mockUserPermissions })
        });
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({})
      });
    });
  });

  const mockUpdateSession = jest.fn();

  describe('Component Initialization', () => {
    test('renders component with correct title and structure', async () => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
        update: mockUpdateSession
      });

      render(<PermissionManager />);

      // Check main heading
      expect(screen.getByText('Permission Management')).toBeInTheDocument();

      // Check that tabs are rendered
      await waitFor(() => {
        expect(screen.getByRole('tab', { name: /user roles/i })).toBeInTheDocument();
        expect(screen.getByRole('tab', { name: /role permissions/i })).toBeInTheDocument();
      });
    });

    test('shows loading state initially', () => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
        update: mockUpdateSession
      });

      render(<PermissionManager />);

      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    test('displays error state when API fails', async () => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
        update: mockUpdateSession
      });

      // Mock API failure
      mockFetch.mockImplementation(() => Promise.resolve({
        ok: false,
        status: 500,
        json: () => Promise.resolve({ error: 'Server error' })
      }));

      render(<PermissionManager />);

      await waitFor(() => {
        expect(screen.getByText(/failed to load permission data/i)).toBeInTheDocument();
      });
    });

    test('shows not authenticated message for unauthenticated users', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
        update: mockUpdateSession
      });

      render(<PermissionManager />);

      expect(screen.getByText(/not authenticated/i)).toBeInTheDocument();
      expect(screen.getByText(/please log in to manage permissions/i)).toBeInTheDocument();
    });

    test('shows loading state for authentication check', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'loading',
        update: mockUpdateSession
      });

      render(<PermissionManager />);

      expect(screen.getByText(/checking authentication/i)).toBeInTheDocument();
    });
  });

  describe('Tab Structure and Navigation', () => {
    beforeEach(() => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
        update: mockUpdateSession
      });
    });

    test('renders two main tabs: User Roles and Role Permissions', async () => {
      render(<PermissionManager />);

      await waitFor(() => {
        const userRolesTab = screen.getByRole('tab', { name: /user roles/i });
        const rolePermissionsTab = screen.getByRole('tab', { name: /role permissions/i });

        expect(userRolesTab).toBeInTheDocument();
        expect(rolePermissionsTab).toBeInTheDocument();
      });
    });

    test('defaults to User Roles tab as active', async () => {
      render(<PermissionManager />);

      await waitFor(() => {
        const userRolesTab = screen.getByRole('tab', { name: /user roles/i });
        expect(userRolesTab).toHaveAttribute('data-state', 'active');
      });
    });

    test('switches between tabs when clicked', async () => {
      render(<PermissionManager />);

      await waitFor(() => {
        const userRolesTab = screen.getByRole('tab', { name: /user roles/i });
        const rolePermissionsTab = screen.getByRole('tab', { name: /role permissions/i });

        // Initially User Roles should be active
        expect(userRolesTab).toHaveAttribute('data-state', 'active');

        // Click Role Permissions tab
        fireEvent.click(rolePermissionsTab);

        // Role Permissions should now be active
        expect(rolePermissionsTab).toHaveAttribute('data-state', 'active');
        expect(userRolesTab).toHaveAttribute('data-state', 'inactive');
      });
    });

    test('displays correct content for User Roles tab', async () => {
      render(<PermissionManager />);

      await waitFor(() => {
        expect(screen.getByText(/user roles/i)).toBeInTheDocument();
      });

      // Should show user selection interface
      await waitFor(() => {
        expect(screen.getByText(/select user/i)).toBeInTheDocument();
        expect(screen.getByPlaceholderText(/search users/i)).toBeInTheDocument();
      });
    });

    test('displays correct content for Role Permissions tab', async () => {
      render(<PermissionManager />);

      await waitFor(() => {
        const rolePermissionsTab = screen.getByRole('tab', { name: /role permissions/i });
        fireEvent.click(rolePermissionsTab);
      });

      await waitFor(() => {
        expect(screen.getByText(/role permissions/i)).toBeInTheDocument();
      });

      // Should show role management interface
      await waitFor(() => {
        expect(screen.getByText(/manage roles and permissions/i)).toBeInTheDocument();
      });
    });
  });

  describe('User Roles Tab Functionality', () => {
    beforeEach(() => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
        update: mockUpdateSession
      });
    });

    test('loads and displays users list', async () => {
      render(<PermissionManager />);

      await waitFor(() => {
        expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
        expect(screen.getByText('Bob Smith')).toBeInTheDocument();
      });
    });

    test('displays user search functionality', async () => {
      render(<PermissionManager />);

      await waitFor(() => {
        const searchInput = screen.getByPlaceholderText(/search users/i);
        expect(searchInput).toBeInTheDocument();
      });

      // Test search functionality
      const searchInput = screen.getByPlaceholderText(/search users/i);
      fireEvent.change(searchInput, { target: { value: 'alice' } });

      await waitFor(() => {
        expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
        expect(screen.queryByText('Bob Smith')).not.toBeInTheDocument();
      });
    });

    test('allows user selection', async () => {
      render(<PermissionManager />);

      await waitFor(() => {
        const aliceUser = screen.getByText('Alice Johnson');
        expect(aliceUser).toBeInTheDocument();
      });

      // Click on Alice Johnson
      const aliceUserButton = screen.getByText('Alice Johnson').closest('button');
      fireEvent.click(aliceUserButton!);

      // Should show user selected state
      await waitFor(() => {
        expect(screen.getByText(/selected user/i)).toBeInTheDocument();
      });
    });

    test('displays user permission summary when user is selected', async () => {
      render(<PermissionManager />);

      await waitFor(() => {
        const aliceUser = screen.getByText('Alice Johnson');
        expect(aliceUser).toBeInTheDocument();
      });

      // Click on Alice Johnson
      const aliceUserButton = screen.getByText('Alice Johnson').closest('button');
      fireEvent.click(aliceUserButton!);

      // Should load and display user permissions
      await waitFor(() => {
        expect(screen.getByText('Admin')).toBeInTheDocument();
        expect(screen.getByText('Full system access')).toBeInTheDocument();
      });
    });

    test('shows empty state when no users are available', async () => {
      // Mock empty users response
      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/users')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ users: [] })
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({})
        });
      });

      render(<PermissionManager />);

      await waitFor(() => {
        expect(screen.getByText(/no users available/i)).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling and Edge Cases', () => {
    beforeEach(() => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
        update: mockUpdateSession
      });
    });

    test('handles network errors gracefully', async () => {
      // Mock network error
      mockFetch.mockImplementation(() => Promise.reject(new Error('Network error')));

      render(<PermissionManager />);

      await waitFor(() => {
        expect(screen.getByText(/failed to load permission data/i)).toBeInTheDocument();
      });
    });

    test('handles malformed API responses', async () => {
      // Mock malformed response
      mockFetch.mockImplementation(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ invalidData: true })
      }));

      render(<PermissionManager />);

      // Should handle gracefully without crashing
      await waitFor(() => {
        expect(screen.getByText(/permission management/i)).toBeInTheDocument();
      });
    });

    test('handles permission loading errors for specific users', async () => {
      render(<PermissionManager />);

      await waitFor(() => {
        const aliceUser = screen.getByText('Alice Johnson');
        expect(aliceUser).toBeInTheDocument();
      });

      // Mock permission loading error
      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/users/1/permissions')) {
          return Promise.resolve({
            ok: false,
            status: 404,
            json: () => Promise.resolve({ error: 'User not found' })
          });
        }
        if (url.includes('/api/users')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ users: mockUsers })
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({})
        });
      });

      // Click on Alice Johnson
      const aliceUserButton = screen.getByText('Alice Johnson').closest('button');
      fireEvent.click(aliceUserButton!);

      // Should show error state for user permissions
      await waitFor(() => {
        expect(screen.getByText(/failed to load user permissions/i)).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
        update: mockUpdateSession
      });
    });

    test('has proper ARIA labels for tabs', async () => {
      render(<PermissionManager />);

      await waitFor(() => {
        const userRolesTab = screen.getByRole('tab', { name: /user roles/i });
        const rolePermissionsTab = screen.getByRole('tab', { name: /role permissions/i });

        expect(userRolesTab).toHaveAttribute('aria-selected');
        expect(rolePermissionsTab).toHaveAttribute('aria-selected');
      });
    });

    test('has proper heading hierarchy', async () => {
      render(<PermissionManager />);

      await waitFor(() => {
        const mainHeading = screen.getByRole('heading', { level: 1 });
        expect(mainHeading).toBeInTheDocument();
      });
    });

    test('has proper button labels and roles', async () => {
      render(<PermissionManager />);

      await waitFor(() => {
        const userButtons = screen.getAllByRole('button');
        expect(userButtons.length).toBeGreaterThan(0);
      });
    });
  });
});