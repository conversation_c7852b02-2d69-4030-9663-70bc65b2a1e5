import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TreatyGrouping } from '@/components/treaties/TreatyGrouping';
import { TreatyCardData } from '@/components/treaties/TreatyCard';

describe('TreatyGrouping', () => {
  const mockTreatyGroups = [
    {
      treatyId: 'treaty-1',
      treatyName: 'Business License Treaty',
      treatyTypeApplications: [
        {
          id: 'app-1',
          userId: 'user-123',
          treatyTypeId: 'type-1',
          treatyTypeName: 'General Business License',
          status: 'draft' as const,
          createdAt: '2025-01-15T10:00:00Z',
          updatedAt: '2025-01-15T10:00:00Z',
          applicantName: '<PERSON>',
          businessName: 'Doe Enterprises',
          hasSignature: false,
          hasAttachments: true
        },
        {
          id: 'app-2',
          userId: 'user-123',
          treatyTypeId: 'type-2',
          treatyTypeName: 'Special Permit',
          status: 'submitted' as const,
          createdAt: '2025-01-16T10:00:00Z',
          updatedAt: '2025-01-16T10:00:00Z',
          applicantName: '<PERSON>',
          businessName: 'Doe Enterprises',
          hasSignature: true,
          hasAttachments: false
        }
      ] as TreatyCardData[],
      createdAt: '2025-01-15T09:00:00Z',
      status: 'active' as const
    }
  ];

  const mockActions = {
    onViewApplication: jest.fn(),
    onEditApplication: jest.fn(),
    onArchiveApplication: jest.fn(),
    onDownloadPDF: jest.fn(),
    onCreateNewApplication: jest.fn(),
    onCreateNewTreaty: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders treaty groups with correct information', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    expect(screen.getByText('Business License Treaty')).toBeInTheDocument();
    expect(screen.getByText('2 applications')).toBeInTheDocument();
    expect(screen.getByText('active')).toBeInTheDocument();
  });

  it('displays application status summary', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    expect(screen.getByText(/1 draft, 1 submitted/)).toBeInTheDocument();
  });

  it('starts with groups collapsed', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    expect(screen.queryByText('General Business License')).not.toBeInTheDocument();
    expect(screen.queryByText('Special Permit')).not.toBeInTheDocument();
  });

  it('expands group when clicked', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    const treatyHeader = screen.getByText('Business License Treaty');
    fireEvent.click(treatyHeader);

    expect(screen.getByText('General Business License')).toBeInTheDocument();
    expect(screen.getByText('Special Permit')).toBeInTheDocument();
  });

  it('calls onCreateNewApplication when Add Application button is clicked', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    const addButton = screen.getByText('Add Application');
    fireEvent.click(addButton);

    expect(mockActions.onCreateNewApplication).toHaveBeenCalledWith('treaty-1');
  });

  it('calls onCreateNewTreaty when New Treaty button is clicked', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    const newTreatyButton = screen.getByText('New Treaty');
    fireEvent.click(newTreatyButton);

    expect(mockActions.onCreateNewTreaty).toHaveBeenCalled();
  });

  it('renders application cards with correct information when expanded', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    const treatyHeader = screen.getByText('Business License Treaty');
    fireEvent.click(treatyHeader);

    expect(screen.getByText('General Business License')).toBeInTheDocument();
    expect(screen.getByText('Special Permit')).toBeInTheDocument();
    expect(screen.getAllByText('John Doe')).toHaveLength(2); // Both applications have same applicant
    expect(screen.getAllByText('Doe Enterprises')).toHaveLength(2); // Both applications have same business
  });

  it('calls onViewApplication when View button is clicked', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    const treatyHeader = screen.getByText('Business License Treaty');
    fireEvent.click(treatyHeader);

    const viewButtons = screen.getAllByText('View');
    fireEvent.click(viewButtons[0]);

    expect(mockActions.onViewApplication).toHaveBeenCalledWith(mockTreatyGroups[0].treatyTypeApplications[0]);
  });

  it('calls onEditApplication when Edit button is clicked for draft applications', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    const treatyHeader = screen.getByText('Business License Treaty');
    fireEvent.click(treatyHeader);

    const editButtons = screen.getAllByText('Edit');
    fireEvent.click(editButtons[0]);

    expect(mockActions.onEditApplication).toHaveBeenCalledWith(mockTreatyGroups[0].treatyTypeApplications[0]);
  });

  it('does not show Edit button for non-draft applications', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    const treatyHeader = screen.getByText('Business License Treaty');
    fireEvent.click(treatyHeader);

    const editButtons = screen.queryAllByText('Edit');
    expect(editButtons).toHaveLength(1); // Only one draft application
  });

  it('calls onDownloadPDF when PDF button is clicked', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    const treatyHeader = screen.getByText('Business License Treaty');
    fireEvent.click(treatyHeader);

    const pdfButtons = screen.getAllByText('PDF');
    fireEvent.click(pdfButtons[0]);

    expect(mockActions.onDownloadPDF).toHaveBeenCalledWith(mockTreatyGroups[0].treatyTypeApplications[0]);
  });

  it('calls onArchiveApplication when Archive button is clicked', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    const treatyHeader = screen.getByText('Business License Treaty');
    fireEvent.click(treatyHeader);

    const archiveButtons = screen.getAllByText('Archive');
    fireEvent.click(archiveButtons[0]);

    expect(mockActions.onArchiveApplication).toHaveBeenCalledWith(mockTreatyGroups[0].treatyTypeApplications[0]);
  });

  it('shows empty state when no treaty groups exist', () => {
    render(<TreatyGrouping treatyGroups={[]} {...mockActions} />);

    expect(screen.getByText('No treaties found')).toBeInTheDocument();
    expect(screen.getByText('Create New Treaty')).toBeInTheDocument();
  });

  it('displays correct status badges for applications', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    const treatyHeader = screen.getByText('Business License Treaty');
    fireEvent.click(treatyHeader);

    expect(screen.getByText('draft')).toBeInTheDocument();
    expect(screen.getByText('submitted')).toBeInTheDocument();
  });

  it('shows signature and attachment indicators', () => {
    render(<TreatyGrouping treatyGroups={mockTreatyGroups} {...mockActions} />);

    const treatyHeader = screen.getByText('Business License Treaty');
    fireEvent.click(treatyHeader);

    expect(screen.getByText('Signed')).toBeInTheDocument();
    expect(screen.getAllByText('Attachments')).toHaveLength(1); // Only one has attachments
  });

  it('handles multiple treaty groups correctly', () => {
    const multipleGroups = [
      ...mockTreatyGroups,
      {
        ...mockTreatyGroups[0],
        treatyId: 'treaty-2',
        treatyName: 'Import License Treaty',
        treatyTypeApplications: [mockTreatyGroups[0].treatyTypeApplications[0]]
      }
    ];

    render(<TreatyGrouping treatyGroups={multipleGroups} {...mockActions} />);

    expect(screen.getByText('Business License Treaty')).toBeInTheDocument();
    expect(screen.getByText('Import License Treaty')).toBeInTheDocument();
  });

  it('toggles expansion independently for each group', () => {
    const multipleGroups = [
      ...mockTreatyGroups,
      {
        ...mockTreatyGroups[0],
        treatyId: 'treaty-2',
        treatyName: 'Import License Treaty',
        treatyTypeApplications: [mockTreatyGroups[0].treatyTypeApplications[0]]
      }
    ];

    render(<TreatyGrouping treatyGroups={multipleGroups} {...mockActions} />);

    // Expand first group
    const firstTreatyHeader = screen.getByText('Business License Treaty');
    fireEvent.click(firstTreatyHeader);

    expect(screen.getByText('General Business License')).toBeInTheDocument();

    // Second group should still be collapsed
    expect(screen.queryByText('Import License Treaty')).toBeInTheDocument();
    // The application from the second group shouldn't be visible yet
  });
});