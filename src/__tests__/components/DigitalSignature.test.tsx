import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DigitalSignature } from '@/components/treaties/DigitalSignature';

describe('DigitalSignature', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the signature component correctly', () => {
    render(<DigitalSignature onChange={mockOnChange} />);

    expect(screen.getByText('Digital Signature *')).toBeInTheDocument();
    expect(screen.getByText('Please sign in the box below using your mouse or finger')).toBeInTheDocument();
    expect(screen.getByText('Sign here')).toBeInTheDocument();
    expect(screen.getByText('No signature yet')).toBeInTheDocument();
    expect(screen.getByText('Clear')).toBeInTheDocument();
  });

  it('shows signature captured status when signature exists', () => {
    render(<DigitalSignature value="data:image/png;base64,test" onChange={mockOnChange} />);

    expect(screen.getByText('Signature captured')).toBeInTheDocument();
  });

  it('renders canvas element for signature drawing', () => {
    render(<DigitalSignature onChange={mockOnChange} />);

    const canvas = document.querySelector('canvas');
    expect(canvas).toBeInTheDocument();
    expect(canvas).toHaveAttribute('width', '400');
    expect(canvas).toHaveAttribute('height', '200');
  });

  it('clears signature when clear button is clicked', () => {
    render(<DigitalSignature value="data:image/png;base64,test" onChange={mockOnChange} />);

    const clearButton = screen.getByText('Clear');
    fireEvent.click(clearButton);

    expect(mockOnChange).toHaveBeenCalledWith('');
    expect(screen.getByText('No signature yet')).toBeInTheDocument();
  });

  it('shows preview when preview button is clicked', () => {
    render(<DigitalSignature value="data:image/png;base64,test" onChange={mockOnChange} />);

    const previewButton = screen.getByText('Preview');
    fireEvent.click(previewButton);

    expect(screen.getByText('Signature Preview')).toBeInTheDocument();
    expect(screen.getByAltText('Signature preview')).toBeInTheDocument();
  });

  it('hides preview when hide button is clicked', () => {
    render(<DigitalSignature value="data:image/png;base64,test" onChange={mockOnChange} />);

    const previewButton = screen.getByText('Preview');
    fireEvent.click(previewButton);

    expect(screen.getByText('Signature Preview')).toBeInTheDocument();

    const hideButton = screen.getByText('Hide');
    fireEvent.click(hideButton);

    expect(screen.queryByText('Signature Preview')).not.toBeInTheDocument();
  });

  it('shows download button when signature exists', () => {
    render(<DigitalSignature value="data:image/png;base64,test" onChange={mockOnChange} />);

    expect(screen.getByText('Download')).toBeInTheDocument();
  });

  it('loads existing signature from value prop', () => {
    const signatureData = 'data:image/png;base64,test-signature';
    render(<DigitalSignature value={signatureData} onChange={mockOnChange} />);

    expect(screen.getByText('Signature captured')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<DigitalSignature onChange={mockOnChange} className="custom-class" />);

    const container = screen.getByText('Digital Signature *').closest('.space-y-4');
    expect(container).toHaveClass('custom-class');
  });

  it('shows preview button when signature exists', () => {
    render(<DigitalSignature value="data:image/png;base64,test" onChange={mockOnChange} />);

    expect(screen.getByText('Preview')).toBeInTheDocument();
  });

  it('shows hide button when preview is open', () => {
    render(<DigitalSignature value="data:image/png;base64,test" onChange={mockOnChange} />);

    const previewButton = screen.getByText('Preview');
    fireEvent.click(previewButton);

    expect(screen.getByText('Hide')).toBeInTheDocument();
  });

  it('displays signature preview when preview is clicked', () => {
    render(<DigitalSignature value="data:image/png;base64,test" onChange={mockOnChange} />);

    const previewButton = screen.getByText('Preview');
    fireEvent.click(previewButton);

    expect(screen.getByText('Signature Preview')).toBeInTheDocument();
    expect(screen.getByAltText('Signature preview')).toBeInTheDocument();
  });

  it('renders canvas with correct dimensions', () => {
    render(<DigitalSignature onChange={mockOnChange} />);

    const canvas = document.querySelector('canvas');
    expect(canvas).toHaveAttribute('width', '400');
    expect(canvas).toHaveAttribute('height', '200');
  });
});