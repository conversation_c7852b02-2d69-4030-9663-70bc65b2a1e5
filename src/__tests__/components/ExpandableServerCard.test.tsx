import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
// import { ExpandableServerCard } from '@/components/settings/server-assignment/ExpandableServerCard';
import { UserServerAssignment } from '@/components/settings/server-assignment/types';

// Mock component for testing (will be replaced with actual component)
const ExpandableServerCard = ({ assignment, isExpanded, onToggleExpansion, loading, error }: any) => {
  if (loading) return <div>Loading...</div>;
  if (error) return <div>{error}</div>;

  return (
    <div className={`border border-slate-200 dark:border-slate-700 rounded-lg p-3 ${isExpanded ? 'border-blue-500' : ''}`}>
      <div className="flex items-center gap-2 mb-2">
        <span className="font-medium">{assignment.server.name}</span>
      </div>
      <div className="text-xs text-slate-600 dark:text-slate-400 truncate" title={assignment.server.url}>
        {assignment.server.url}
      </div>
      {assignment.server.description && (
        <div className="text-sm text-slate-700 dark:text-slate-300 mt-2">
          {assignment.server.description}
        </div>
      )}
      <div className="flex items-center gap-2 text-xs text-slate-500 mt-3">
        Assigned {assignment.assignedAt.toLocaleDateString()}
      </div>
      <button
        onClick={() => onToggleExpansion(assignment.serverId)}
        className="mt-2 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        {isExpanded ? 'Hide Permissions' : 'Manage Permissions'}
      </button>
    </div>
  );
};

describe('ExpandableServerCard', () => {
  const mockServerAssignment: UserServerAssignment = {
    id: 'assignment-123',
    userId: 'user-456',
    serverId: 'server-789',
    server: {
      id: 'server-789',
      name: 'Discord Bot Server',
      url: 'https://discord.example.com',
      description: 'Main Discord server for the alliance',
      isActive: true
    },
    assignedAt: new Date('2025-01-15T10:00:00Z')
  };

  const mockProps = {
    assignment: mockServerAssignment,
    isExpanded: false,
    onToggleExpansion: jest.fn(),
    onManagePermissions: jest.fn(),
    loading: false,
    error: null
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders server card with basic information', () => {
    render(<ExpandableServerCard {...mockProps} />);

    expect(screen.getByText('Discord Bot Server')).toBeInTheDocument();
    expect(screen.getByText('https://discord.example.com')).toBeInTheDocument();
    expect(screen.getByText(/Assigned/)).toBeInTheDocument();
  });

  it('displays server description when present', () => {
    render(<ExpandableServerCard {...mockProps} />);

    expect(screen.getByText('Main Discord server for the alliance')).toBeInTheDocument();
  });

  it('shows expand button with correct icon', () => {
    render(<ExpandableServerCard {...mockProps} />);

    const expandButton = screen.getByRole('button', { name: /manage permissions/i });
    expect(expandButton).toBeInTheDocument();
    expect(expandButton).toBeInTheDocument();
  });

  it('shows collapse button when expanded', () => {
    render(<ExpandableServerCard {...mockProps} isExpanded={true} />);

    const collapseButton = screen.getByRole('button', { name: /hide permissions/i });
    expect(collapseButton).toBeInTheDocument();
  });

  it('calls onToggleExpansion when expand button is clicked', () => {
    render(<ExpandableServerCard {...mockProps} />);

    const expandButton = screen.getByRole('button', { name: /manage permissions/i });
    fireEvent.click(expandButton);

    expect(mockProps.onToggleExpansion).toHaveBeenCalledWith('server-789');
  });

  it('calls onToggleExpansion when collapse button is clicked', () => {
    render(<ExpandableServerCard {...mockProps} isExpanded={true} />);

    const collapseButton = screen.getByRole('button', { name: /hide permissions/i });
    fireEvent.click(collapseButton);

    expect(mockProps.onToggleExpansion).toHaveBeenCalledWith('server-789');
  });

  it('shows loading state when loading is true', () => {
    render(<ExpandableServerCard {...mockProps} loading={true} />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('shows error state when error is provided', () => {
    const errorMessage = 'Failed to load server data';
    render(<ExpandableServerCard {...mockProps} error={errorMessage} />);

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('displays formatted assignment date', () => {
    render(<ExpandableServerCard {...mockProps} />);

    // Should show the formatted date (e.g., "1/15/2025")
    expect(screen.getByText(/1\/15\/2025/)).toBeInTheDocument();
  });

  it('handles server without description gracefully', () => {
    const assignmentWithoutDescription = {
      ...mockServerAssignment,
      server: {
        ...mockServerAssignment.server,
        description: null
      }
    };

    render(<ExpandableServerCard {...mockProps} assignment={assignmentWithoutDescription} />);

    expect(screen.getByText('Discord Bot Server')).toBeInTheDocument();
    expect(screen.getByText('https://discord.example.com')).toBeInTheDocument();
    // Should not show description section
    expect(screen.queryByText('Main Discord server for the alliance')).not.toBeInTheDocument();
  });

  it('handles server with inactive status', () => {
    const inactiveServerAssignment = {
      ...mockServerAssignment,
      server: {
        ...mockServerAssignment.server,
        isActive: false
      }
    };

    render(<ExpandableServerCard {...mockProps} assignment={inactiveServerAssignment} />);

    expect(screen.getByText('Discord Bot Server')).toBeInTheDocument();
    // Should still show the server even if inactive
  });

  it('applies correct styling for different states', () => {
    const { rerender } = render(<ExpandableServerCard {...mockProps} />);

    // Default state
    const card = screen.getByText('Discord Bot Server').closest('.border');
    expect(card).toBeInTheDocument();

    // Expanded state
    rerender(<ExpandableServerCard {...mockProps} isExpanded={true} />);
    // The mock component adds border-blue-500 class when expanded
    expect(card).toHaveClass('border-blue-500');
  });

  it('handles missing assignment data gracefully', () => {
    const minimalAssignment: UserServerAssignment = {
      id: 'assignment-123',
      userId: 'user-456',
      serverId: 'server-789',
      server: {
        id: 'server-789',
        name: 'Test Server',
        url: 'https://test.com',
        description: null,
        isActive: true
      },
      assignedAt: new Date()
    };

    render(<ExpandableServerCard {...mockProps} assignment={minimalAssignment} />);

    expect(screen.getByText('Test Server')).toBeInTheDocument();
    expect(screen.getByText('https://test.com')).toBeInTheDocument();
  });

  it('truncates long server names appropriately', () => {
    const longNameAssignment = {
      ...mockServerAssignment,
      server: {
        ...mockServerAssignment.server,
        name: 'Very Long Server Name That Should Be Handled Gracefully And Not Break The Layout'
      }
    };

    render(<ExpandableServerCard {...mockProps} assignment={longNameAssignment} />);

    expect(screen.getByText('Very Long Server Name That Should Be Handled Gracefully And Not Break The Layout')).toBeInTheDocument();
  });

  it('truncates long URLs appropriately', () => {
    const longUrlAssignment = {
      ...mockServerAssignment,
      server: {
        ...mockServerAssignment.server,
        url: 'https://very-long-subdomain.very-long-domain-name.very-long-tld-path.very-long-extension/more/path'
      }
    };

    render(<ExpandableServerCard {...mockProps} assignment={longUrlAssignment} />);

    expect(screen.getByText('https://very-long-subdomain.very-long-domain-name.very-long-tld-path.very-long-extension/more/path')).toBeInTheDocument();
  });
});