import React from 'react';
import { render, screen } from '@testing-library/react';
import { usePathname } from 'next/navigation';
import { VerticalNav } from '@/components/admin/VerticalNav';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}));

describe('VerticalNav', () => {
  const mockNavItems = [
    { label: 'Manage Users', href: '/admin/manage/users' },
    { label: 'Contact Details', href: '/admin/manage/contact' },
    { label: 'Identification', href: '/admin/manage/identification' },
    { label: 'Positions', href: '/admin/manage/positions' },
    { label: 'Treaties', href: '/admin/manage/treaties' },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render all provided navigation links correctly', () => {
    (usePathname as jest.Mock).mockReturnValue('/admin/manage/users');

    render(<VerticalNav />);

    mockNavItems.forEach((item) => {
      expect(screen.getByText(item.label)).toBeInTheDocument();
    });
  });

  it('should apply active styles to the link that matches the current URL path', () => {
    (usePathname as jest.Mock).mockReturnValue('/admin/manage/users');

    render(<VerticalNav />);

    const activeLink = screen.getByText('Manage Users');
    expect(activeLink).toHaveClass('bg-emerald-100', 'text-emerald-700');
  });

  it('should not apply active styles to inactive links', () => {
    (usePathname as jest.Mock).mockReturnValue('/admin/manage/users');

    render(<VerticalNav />);

    const inactiveLink = screen.getByText('Contact Details');
    expect(inactiveLink).not.toHaveClass('bg-emerald-100', 'text-emerald-700');
  });

  it('should handle different active paths correctly', () => {
    (usePathname as jest.Mock).mockReturnValue('/admin/manage/contact');

    render(<VerticalNav />);

    const activeLink = screen.getByText('Contact Details');
    const inactiveLink = screen.getByText('Manage Users');

    expect(activeLink).toHaveClass('bg-emerald-100', 'text-emerald-700');
    expect(inactiveLink).not.toHaveClass('bg-emerald-100', 'text-emerald-700');
  });

  it('should render navigation links as anchor tags with correct hrefs', () => {
    (usePathname as jest.Mock).mockReturnValue('/admin/manage/users');

    render(<VerticalNav />);

    mockNavItems.forEach((item) => {
      const link = screen.getByText(item.label).closest('a');
      expect(link).toHaveAttribute('href', item.href);
    });
  });
});