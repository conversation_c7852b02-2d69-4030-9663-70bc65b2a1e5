import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { UpdateUserTab } from '@/app/admin/manage/components/UpdateUserTab';
import { toast } from 'sonner';

// Mock the toast module
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  }
}));

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useSearchParams: () => new URLSearchParams(),
  useRouter: () => ({
    replace: jest.fn(),
  }),
}));

// Mock NextAuth session
jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: { user: { id: 'admin', email: '<EMAIL>' } },
    status: 'authenticated'
  }),
}));

// Mock fetch for different endpoints
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock data for enhanced components
const mockTitles = [
  { id: '1', name: 'Ambassador', description: 'Diplomatic position', isActive: true },
  { id: '2', name: 'Consul', description: 'Consular position', isActive: true }
];

const mockPositions = [
  { id: '1', title: 'Senior Ambassador', description: 'Senior diplomatic position', level: 1, parentId: null, isActive: true },
  { id: '2', title: 'Trade Representative', description: 'Trade position', level: 2, parentId: '1', isActive: true },
  { id: '3', title: 'Head of Maritime Affairs', description: 'Maritime position', level: 2, parentId: '1', isActive: true }
];

const mockUser = {
  id: '123',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  personalEmail: '<EMAIL>',
  nwaEmail: '<EMAIL>',
  phone: '******-0123',
  mobile: '******-0456',
  bio: 'Experienced diplomat',
  streetAddress1: '123 Main Street',
  streetAddress2: 'Apt 4B',
  town: 'Springfield',
  city: 'Springfield',
  country: 'United States',
  postalCode: '12345',
  regionId: '1',
  regionText: 'California',
  dob: '1980-01-01',
  peaceAmbassadorNumber: 'PA-001',
  titleId: '1',
  positionId: '1',
  profile: {
    firstName: 'John',
    lastName: 'Doe',
    dateOfBirth: '1980-01-01T00:00:00Z',
    personalEmail: '<EMAIL>',
    nwaEmail: '<EMAIL>',
    phone: '******-0123',
    mobile: '******-0456',
    bio: 'Experienced diplomat',
    streetAddress1: '123 Main Street',
    streetAddress2: 'Apt 4B',
    town: 'Springfield',
    city: 'Springfield',
    country: 'United States',
    postalCode: '12345',
    regionId: '1',
    regionText: 'California',
    peaceAmbassadorNumber: 'PA-001'
  },
  userPositions: [
    { titleId: '1', positionId: '1' }
  ],
  identifications: [],
  treaties: [],
  ordinances: []
};

const mockUserSearchResults = {
  users: [
    {
      id: '123',
      name: 'John Doe',
      email: '<EMAIL>',
      nwaEmail: '<EMAIL>',
      profile: {
        firstName: 'John',
        lastName: 'Doe'
      }
    }
  ]
};

const mockAddressFormat = {
  format: [
    { field: 'streetAddress', label: 'Street Address', required: true },
    { field: 'aptSuiteUnit', label: 'Apt/Suite/Unit', required: false },
    { field: 'townCity', label: 'City', required: true },
    { field: 'stateProvince', label: 'State', required: true },
    { field: 'postcodeZip', label: 'ZIP Code', required: true }
  ],
  example: '123 Main St, Apt 4B, New York, NY 10001'
};

describe('UpdateUserTab Enhanced Components Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock responses
    mockFetch.mockImplementation((url: string, options?: any) => {
      if (url.includes('/api/positions/titles')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockTitles)
        });
      }
      if (url.includes('/api/positions/positions')) {
        const urlObj = new URL(url, 'http://localhost');
        const titleId = urlObj.searchParams.get('titleId');
        const search = urlObj.searchParams.get('search');
        
        if (search && search.length >= 2) {
          // Filter positions based on search term for autocomplete
          const filteredPositions = mockPositions.filter(pos => 
            pos.title.toLowerCase().includes(search.toLowerCase())
          );
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(filteredPositions)
          });
        }
        
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockPositions)
        });
      }
      if (url.includes('/api/countries') && url.includes('q=')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve([
            { id: '1', name: 'United States' },
            { id: '2', name: 'United Kingdom' },
            { id: '3', name: 'Canada' }
          ])
        });
      }
      if (url.includes('/api/countries/1/address-format')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockAddressFormat)
        });
      }
      // Mock search API call with proper URL pattern
      if (url.includes('/api/users/search') && url.includes('q=')) {
        const urlObj = new URL(url, 'http://localhost');
        const searchQuery = urlObj.searchParams.get('q');
        if (searchQuery === 'John Doe') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockUserSearchResults)
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ users: [] })
        });
      }
      if (url.includes('/api/users/123/full')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ user: mockUser })
        });
      }
      if (url.includes('/api/users/123') && options?.method === 'PUT') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ success: true, message: 'User updated successfully' })
        });
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve([])
      });
    });
  });

  describe('User Search and Selection', () => {
    test('successfully searches and selects a user', async () => {
      render(<UpdateUserTab />);
      
      const searchInput = screen.getByPlaceholderText('Search for a user by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      // Wait for search results to be loaded and displayed
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/users/search?q=John')
        );
      });
      
      // Wait for search results to appear in the DOM
      await waitFor(() => {
        const searchResults = screen.queryAllByText('John Doe');
        expect(searchResults.length).toBeGreaterThan(0);
      }, { timeout: 5000 });
      
      // Find and click on the search result
      const searchResultButton = screen.getByRole('button', { name: /John Doe.*john\.doe@example\.com/ });
      fireEvent.click(searchResultButton);
      
      // Wait for user data to be loaded
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/users/123/full');
      }, { timeout: 5000 });
    });
  });

  describe('Enhanced Position Search Autocomplete', () => {
    test('renders position search autocomplete component after user selection', async () => {
      render(<UpdateUserTab />);
      
      // First select a user
      const searchInput = screen.getByPlaceholderText('Search for a user by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        const searchResultButton = screen.getByRole('button', { name: /John Doe.*john\.doe@example\.com/ });
        fireEvent.click(searchResultButton);
      });
      
      // Wait for user data to be loaded
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/users/123/full');
      }, { timeout: 5000 });
      
      // Now check for position search component
      await waitFor(() => {
        expect(screen.getByText('Position')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Search positions...')).toBeInTheDocument();
      });
    });

    test('shows position suggestions when typing', async () => {
      render(<UpdateUserTab />);
      
      // First select a user
      const searchInput = screen.getByPlaceholderText('Search for a user by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        const searchResultButton = screen.getByRole('button', { name: /John Doe.*john\.doe@example\.com/ });
        fireEvent.click(searchResultButton);
      });
      
      // Wait for user data to be loaded and position input to appear
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Search positions...')).toBeInTheDocument();
      }, { timeout: 5000 });
      
      const positionInput = screen.getByPlaceholderText('Search positions...');
      
      await userEvent.type(positionInput, 'Head');
      
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/positions/positions?titleId=1&search=Head')
        );
      });
    });
  });

  describe('Enhanced Contact Details Form Integration', () => {
    test('renders enhanced contact form with proper field structure after user selection', async () => {
      render(<UpdateUserTab />);
      
      // First select a user
      const searchInput = screen.getByPlaceholderText('Search for a user by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        const searchResultButton = screen.getByRole('button', { name: /John Doe.*john\.doe@example\.com/ });
        fireEvent.click(searchResultButton);
      });
      
      // Wait for user data to be loaded
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/users/123/full');
      }, { timeout: 5000 });
      
      // Switch to contact tab
      const contactTab = screen.getByText('Contact Details');
      fireEvent.click(contactTab);
      
      await waitFor(() => {
        expect(screen.getByText('Update Contact Info')).toBeInTheDocument();
      });
      
      // Check for contact form fields
      expect(screen.getByDisplayValue('123 Main Street')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Springfield')).toBeInTheDocument();
      expect(screen.getByDisplayValue('United States')).toBeInTheDocument();
    });

    test('shows country-specific address format', async () => {
      render(<UpdateUserTab />);
      
      // First select a user
      const searchInput = screen.getByPlaceholderText('Search for a user by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        const searchResultButton = screen.getByRole('button', { name: /John Doe.*john\.doe@example\.com/ });
        fireEvent.click(searchResultButton);
      });
      
      // Wait for user data to be loaded
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/users/123/full');
      }, { timeout: 5000 });
      
      // Switch to contact tab
      const contactTab = screen.getByText('Contact Details');
      fireEvent.click(contactTab);
      
      await waitFor(() => {
        expect(screen.getByText('Update Contact Info')).toBeInTheDocument();
      });
      
      // Should have fetched address format for US
      expect(mockFetch).toHaveBeenCalledWith('/api/countries/1/address-format');
    });
  });

  describe('Integration Testing Across Multiple Forms', () => {
    test('maintains data consistency across form sections', async () => {
      render(<UpdateUserTab />);
      
      // Select a user
      const searchInput = screen.getByPlaceholderText('Search for a user by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        const searchResultButton = screen.getByRole('button', { name: /John Doe.*john\.doe@example\.com/ });
        fireEvent.click(searchResultButton);
      });
      
      // Wait for user data to be loaded
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/users/123/full');
      }, { timeout: 5000 });
      
      // Switch to contact tab
      const contactTab = screen.getByText('Contact Details');
      fireEvent.click(contactTab);
      
      await waitFor(() => {
        expect(screen.getByText('Update Contact Info')).toBeInTheDocument();
      });
      
      // Verify data is loaded correctly in contact section
      expect(screen.getByDisplayValue('123 Main Street')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Springfield')).toBeInTheDocument();
      expect(screen.getByDisplayValue('United States')).toBeInTheDocument();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('handles API errors gracefully in enhanced components', async () => {
      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/positions/positions')) {
          return Promise.resolve({
            ok: false,
            status: 500,
            json: () => Promise.resolve({ error: 'Server error' })
          });
        }
        if (url.includes('/api/users/search') && url.includes('q=John Doe')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockUserSearchResults)
          });
        }
        if (url.includes('/api/users/123/full')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ user: mockUser })
          });
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve([])
        });
      });

      render(<UpdateUserTab />);
      
      // Select a user
      const searchInput = screen.getByPlaceholderText('Search for a user by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        const searchResultButton = screen.getByRole('button', { name: /John Doe.*john\.doe@example\.com/ });
        fireEvent.click(searchResultButton);
      });
      
      // Wait for user data to be loaded
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/users/123/full');
      }, { timeout: 5000 });
      
      // Try to search for positions
      const positionInput = screen.getByPlaceholderText('Search positions...');
      await userEvent.type(positionInput, 'Head');
      
      // Should handle error without crashing
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalled();
      });
    });
  });
});