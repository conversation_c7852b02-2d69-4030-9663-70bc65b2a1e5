import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useSession } from 'next-auth/react';
import ManageUsersPage from '@/app/admin/manage/users/page';
import { Sidebar } from '@/components/layout/Sidebar';

// Mock next-auth/react
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}));

// Mock check-admin utility
jest.mock('@/lib/check-admin', () => ({
  checkAdmin: jest.fn(),
}));

describe('Admin Manage Integration', () => {
  const mockSession = {
    user: {
      id: '1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Sidebar Integration', () => {
    it('should display "User Management" link in sidebar', () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      render(<Sidebar isCollapsed={false} onToggle={() => {}} />);

      expect(screen.getByText('User Management')).toBeInTheDocument();
    });
  });

  describe('Tabbed Interface Integration', () => {
    beforeEach(() => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockResolvedValue(true);
    });

    it('should render tabbed interface with all required tabs', async () => {
      render(<ManageUsersPage />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /create user/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /update user/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /search users/i })).toBeInTheDocument();
      });
    });

    it('should switch between tabs correctly', async () => {
      render(<ManageUsersPage />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /create user/i })).toBeInTheDocument();
      });

      // Click on Update User tab
      const updateTab = screen.getByRole('button', { name: /update user/i });
      fireEvent.click(updateTab);

      await waitFor(() => {
        expect(screen.getByText(/Update User Form/i)).toBeInTheDocument();
      });

      // Click on Search Users tab
      const searchTab = screen.getByRole('button', { name: /search users/i });
      fireEvent.click(searchTab);

      await waitFor(() => {
        expect(screen.getByText(/Search Users Component/i)).toBeInTheDocument();
      });
    });

    it('should show active tab styling correctly', async () => {
      render(<ManageUsersPage />);

      await waitFor(() => {
        const createTab = screen.getByRole('button', { name: /create user/i });
        expect(createTab).toHaveClass('border-emerald-500', 'text-emerald-600');
      });

      // Click on Update User tab
      const updateTab = screen.getByRole('button', { name: /update user/i });
      fireEvent.click(updateTab);

      await waitFor(() => {
        expect(updateTab).toHaveClass('border-emerald-500', 'text-emerald-600');
        const createTab = screen.getByRole('button', { name: /create user/i });
        expect(createTab).not.toHaveClass('border-emerald-500', 'text-emerald-600');
      });
    });
  });

  describe('Component Movement Integration', () => {
    it('should integrate moved user components correctly', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockResolvedValue(true);

      render(<ManageUsersPage />);

      await waitFor(() => {
        expect(screen.getByText(/Manage Users/i)).toBeInTheDocument();
        expect(screen.getByText(/Create, update, and manage user accounts/i)).toBeInTheDocument();
      });

      // Test that the page renders without errors after component movement
      expect(screen.getByText(/Create User Form Component/i)).toBeInTheDocument();
    });
  });

  describe('Navigation Integration', () => {
    it('should integrate VerticalNav with page layout', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockResolvedValue(true);

      // This test would need to render the full layout with VerticalNav
      // For now, we'll test that the components can be imported without errors
      const ManageUsersLayout = (await import('@/app/admin/manage/layout')).default;
      const VerticalNav = (await import('@/components/admin/VerticalNav')).VerticalNav;

      expect(ManageUsersLayout).toBeDefined();
      expect(VerticalNav).toBeDefined();
    });
  });
});