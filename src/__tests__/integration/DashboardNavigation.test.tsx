import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DashboardProvider } from '@/lib/contexts/DashboardContext';
import DashboardPage from '@/app/dashboard/page';
import { PERMISSIONS } from '@/lib/constants/permissions';

// Extend Jest matchers
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveClass(...classNames: string[]): R;
      toHaveBeenCalledWith(...args: any[]): R;
    }
  }
}

// Mock fetch globally
const mockFetch = jest.fn();

// Setup fetch mock before tests
beforeAll(() => {
  global.fetch = mockFetch;
  mockFetch.mockResolvedValue({
    ok: true,
    json: async () => ({
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'PEACE',
      permissions: [],
    }),
    headers: { get: () => 'application/json' },
    status: 200,
    statusText: 'OK',
  } as Response);
});

// Mock the tab components to avoid complex dependencies
jest.mock('@/components/dashboard/tabs/OverviewTab', () => ({
  OverviewTab: () => <div data-testid="overview-tab">Overview Content</div>
}));

jest.mock('@/components/dashboard/tabs/NotificationsTab', () => ({
  NotificationsTab: () => <div data-testid="notifications-tab">Notifications Content</div>
}));

jest.mock('@/components/dashboard/tabs/ServersTab', () => ({
  ServersTab: () => <div data-testid="servers-tab">Servers Content</div>
}));

jest.mock('@/components/dashboard/tabs/AnalyticsTab', () => ({
  AnalyticsTab: () => <div data-testid="analytics-tab">Analytics Content</div>
}));

jest.mock('@/components/dashboard/tabs/SecurityTab', () => ({
  SecurityTab: () => <div data-testid="security-tab">Security Content</div>
}));

jest.mock('@/components/dashboard/tabs/SystemTab', () => ({
  SystemTab: () => <div data-testid="system-tab">System Content</div>
}));

describe('Dashboard Navigation Integration', () => {
  const renderDashboard = (userPermissions: string[] = [], userRole: string = 'PEACE') => {
    // Mock the fetch call to return user data
    const mockResponse = {
      ok: true,
      json: async () => ({
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        role: userRole,
        permissions: userPermissions,
      }),
      headers: { get: () => 'application/json' },
      status: 200,
      statusText: 'OK',
    } as Response;

    mockFetch.mockResolvedValue(mockResponse);

    return render(
      <DashboardProvider>
        <DashboardPage />
      </DashboardProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders dashboard with basic tabs for regular user', async () => {
    renderDashboard();

    // Should show loading initially
    expect(screen.getByTestId('dashboard-layout')).toBeInTheDocument();

    // Wait for user data to load
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /Overview/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Notifications/i })).toBeInTheDocument();
    });

    // Should not show admin-only tabs
    expect(screen.queryByRole('button', { name: /Analytics/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /Security/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /System/i })).not.toBeInTheDocument();
  });

  it('shows server tab for users with server permissions', async () => {
    renderDashboard([PERMISSIONS.REMOTE_SERVER_READ]);

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /Overview/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Notifications/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Servers/i })).toBeInTheDocument();
    });
  });

  it('shows all tabs for admin users', async () => {
    renderDashboard([PERMISSIONS.ADMIN_PANEL_ACCESS], 'ADMIN');

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /Overview/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Notifications/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Servers/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Analytics/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Security/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /System/i })).toBeInTheDocument();
    });
  });

  it('switches between tabs correctly', async () => {
    renderDashboard([PERMISSIONS.ADMIN_PANEL_ACCESS], 'ADMIN');

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /Overview/i })).toBeInTheDocument();
    });

    // Click on notifications tab
    const notificationsTab = screen.getByRole('button', { name: /Notifications/i });
    fireEvent.click(notificationsTab);

    // Should show notifications content
    await waitFor(() => {
      expect(screen.getByTestId('notifications-tab')).toBeInTheDocument();
    });

    // Click on servers tab (admin users have access to all tabs)
    const serversTab = screen.getByRole('button', { name: /Servers/i });
    fireEvent.click(serversTab);

    // Should show servers content
    await waitFor(() => {
      expect(screen.getByTestId('servers-tab')).toBeInTheDocument();
    });
  });

  it('highlights active tab correctly', async () => {
    renderDashboard([PERMISSIONS.ADMIN_PANEL_ACCESS], 'ADMIN');

    await waitFor(() => {
      const overviewTab = screen.getByRole('button', { name: /Overview/i });
      expect(overviewTab).toHaveClass('border-blue-500', 'text-blue-600');
    });

    // Click on notifications tab
    const notificationsTab = screen.getByRole('button', { name: /Notifications/i });
    fireEvent.click(notificationsTab);

    await waitFor(() => {
      const notificationsTabElement = screen.getByRole('button', { name: /Notifications/i });
      const overviewTabElement = screen.getByRole('button', { name: /Overview/i });

      expect(notificationsTabElement).toHaveClass('border-blue-500', 'text-blue-600');
      expect(overviewTabElement).toHaveClass('border-transparent', 'text-gray-500');
    });
  });

  it('maintains responsive design on different screen sizes', async () => {
    renderDashboard();

    await waitFor(() => {
      expect(screen.getByText('Overview')).toBeInTheDocument();
    });

    // Check that tab navigation has responsive classes
    const tabNav = screen.getByRole('navigation').closest('div');
    expect(tabNav).toHaveClass('overflow-x-auto', 'px-4', 'md:px-6');
  });

  it('handles tab content overflow properly', async () => {
    renderDashboard([PERMISSIONS.ADMIN_PANEL_ACCESS], 'ADMIN');

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /Overview/i })).toBeInTheDocument();
    });

    // Click on servers tab which should be available for admin users
    const serversTab = screen.getByRole('button', { name: /Servers/i });
    fireEvent.click(serversTab);

    await waitFor(() => {
      expect(screen.getByTestId('servers-tab')).toBeInTheDocument();
    });

    // Check that content area has proper overflow handling
    const contentArea = screen.getByTestId('system-tab').parentElement;
    expect(contentArea).toHaveClass('overflow-auto', 'min-h-0');
  });

  it('loads user data correctly on mount', async () => {
    const mockUser = {
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'MEMBER',
      permissions: [PERMISSIONS.REMOTE_SERVER_READ],
    };

    const mockResponse = {
      ok: true,
      json: async () => mockUser,
      headers: { get: () => 'application/json' },
      status: 200,
      statusText: 'OK',
    } as Response;

    mockFetch.mockResolvedValue(mockResponse);

    renderDashboard();

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/user/profile');
    });
  });

  it('handles API errors gracefully', async () => {
    const mockErrorResponse = {
      ok: false,
      status: 500,
      statusText: 'Internal Server Error',
      json: async () => ({ error: 'Internal Server Error' }),
      headers: { get: () => 'application/json' },
    } as Response;

    mockFetch.mockResolvedValue(mockErrorResponse);

    renderDashboard();

    // Should still render dashboard even with API error
    await waitFor(() => {
      expect(screen.getByTestId('dashboard-layout')).toBeInTheDocument();
    });
  });

  it('maintains tab state during navigation', async () => {
    renderDashboard([PERMISSIONS.ADMIN_PANEL_ACCESS], 'ADMIN');

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /Overview/i })).toBeInTheDocument();
    });

    // Navigate to notifications
    fireEvent.click(screen.getByRole('button', { name: /Notifications/i }));
    await waitFor(() => {
      expect(screen.getByTestId('notifications-tab')).toBeInTheDocument();
    });

    // Navigate to servers (admin users have access to all tabs)
    fireEvent.click(screen.getByRole('button', { name: /Servers/i }));
    await waitFor(() => {
      expect(screen.getByTestId('servers-tab')).toBeInTheDocument();
    });

    // Go back to notifications - should still work
    fireEvent.click(screen.getByRole('button', { name: /Notifications/i }));
    await waitFor(() => {
      expect(screen.getByTestId('notifications-tab')).toBeInTheDocument();
    });
  });

  it('filters tabs correctly based on permissions', async () => {
    // Test with minimal permissions
    renderDashboard([PERMISSIONS.PROFILE_READ]);

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /Overview/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Notifications/i })).toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /Servers/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /Analytics/i })).not.toBeInTheDocument();
    });

    // Test with server permissions - re-render with new permissions
    renderDashboard([PERMISSIONS.REMOTE_SERVER_READ, PERMISSIONS.PROFILE_READ]);

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /Overview/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Notifications/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Servers/i })).toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /Analytics/i })).not.toBeInTheDocument();
    });
  });
});