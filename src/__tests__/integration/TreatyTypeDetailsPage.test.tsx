import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter, useParams } from 'next/navigation';
import UserTreatyAssignmentPage from '@/app/treaties/[userId]/[treatyTypeId]/page';

// Alias for backward compatibility in tests
const TreatyTypeDetailsPage = UserTreatyAssignmentPage;

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useParams: jest.fn(),
}));

// Mock next-auth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({ data: { user: { id: 'user-123' } } })),
}));

// Mock DashboardLayout
jest.mock('@/components/layout/DashboardLayout', () => ({
  DashboardLayout: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Mock TreatyTypeDetailsForm
jest.mock('@/components/treaties/TreatyTypeDetailsForm', () => ({
  TreatyTypeDetailsForm: ({ onSubmit, onSave, onCancel }: any) => (
    <div data-testid="treaty-form">
      <button onClick={() => onSubmit({ test: 'data' })}>Submit</button>
      <button onClick={() => onSave({ test: 'data' })}>Save</button>
      <button onClick={onCancel}>Cancel</button>
    </div>
  ),
}));

describe('TreatyTypeDetailsPage Integration', () => {
  const mockRouter = {
    push: jest.fn(),
  };

  const mockParams = {
    userId: 'user-123',
    treatyTypeId: 'treaty-456',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useParams as jest.Mock).mockReturnValue(mockParams);

    // Mock fetch
    global.fetch = jest.fn();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders the page with form integration', async () => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({}),
    });

    render(<TreatyTypeDetailsPage />);

    await waitFor(() => {
      expect(screen.getByText('Treaty Type Details')).toBeInTheDocument();
    });

    expect(screen.getByTestId('treaty-form')).toBeInTheDocument();
  });

  it('handles form submission successfully', async () => {
    (global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({}),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      });

    render(<TreatyTypeDetailsPage />);

    await waitFor(() => {
      expect(screen.getByText('Submit')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Submit'));

    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith('/treaties?success=submitted');
    });
  });

  it('handles form cancellation', async () => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({}),
    });

    render(<TreatyTypeDetailsPage />);

    await waitFor(() => {
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Cancel'));

    expect(mockRouter.push).toHaveBeenCalledWith('/treaties');
  });

  it('loads existing form data on mount', async () => {
    const existingData = { fullLegalName: 'John Doe' };

    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => existingData,
    });

    render(<TreatyTypeDetailsPage />);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/treaty-type-details/user-123/treaty-456');
    });
  });

  it('handles API errors gracefully', async () => {
    (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('API Error'));

    render(<TreatyTypeDetailsPage />);

    await waitFor(() => {
      expect(screen.getByText('Failed to load existing form data')).toBeInTheDocument();
    });
  });

  it('shows back button and navigation', async () => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({}),
    });

    render(<TreatyTypeDetailsPage />);

    await waitFor(() => {
      expect(screen.getByText('Back to Treaties')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Back to Treaties'));
    expect(mockRouter.push).toHaveBeenCalledWith('/treaties');
  });
});