import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useSession } from 'next-auth/react';
import ManageUsersLayout from '@/app/admin/manage/layout';
import ManageUsersPage from '@/app/admin/manage/users/page';

// Mock next-auth/react
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}));

// Mock check-admin utility
jest.mock('@/lib/check-admin', () => ({
  checkAdmin: jest.fn(),
}));

// Mock next/navigation for redirect testing
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
  })),
  usePathname: jest.fn(() => '/admin/manage/users'),
}));

describe('Admin Security and Audit Integration', () => {
  const mockAdminSession = {
    user: {
      id: '1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
    },
  };

  const mockNonAdminSession = {
    user: {
      id: '2',
      name: 'Regular User',
      email: '<EMAIL>',
      role: 'user',
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Admin Permission Checks', () => {
    it('should allow access for authenticated admin users', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockAdminSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockResolvedValue(true);

      render(
        <ManageUsersLayout>
          <div>Admin Content</div>
        </ManageUsersLayout>
      );

      await waitFor(() => {
        expect(screen.getByText('Admin Content')).toBeInTheDocument();
      });

      expect(checkAdmin).toHaveBeenCalled();
    });

    it('should deny access for non-admin users', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockNonAdminSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockResolvedValue(false);

      const mockRouter = { push: jest.fn() };
      require('next/navigation').useRouter.mockReturnValue(mockRouter);

      render(
        <ManageUsersLayout>
          <div>Admin Content</div>
        </ManageUsersLayout>
      );

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/unauthorized');
      });

      expect(screen.queryByText('Admin Content')).not.toBeInTheDocument();
    });

    it('should handle unauthenticated users', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: null,
        status: 'unauthenticated',
      });

      const mockRouter = { push: jest.fn() };
      require('next/navigation').useRouter.mockReturnValue(mockRouter);

      render(
        <ManageUsersLayout>
          <div>Admin Content</div>
        </ManageUsersLayout>
      );

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/unauthorized');
      });
    });

    it('should show loading state during permission check', () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockAdminSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockImplementation(() => new Promise(resolve => setTimeout(() => resolve(true), 100)));

      render(
        <ManageUsersLayout>
          <div>Admin Content</div>
        </ManageUsersLayout>
      );

      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });
  });

  describe('Audit Trail Integration', () => {
    it('should log admin access attempts', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockAdminSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockResolvedValue(true);

      // Mock console.log to capture audit logs
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      render(
        <ManageUsersLayout>
          <div>Admin Content</div>
        </ManageUsersLayout>
      );

      await waitFor(() => {
        expect(screen.getByText('Admin Content')).toBeInTheDocument();
      });

      // Check if audit logging occurred
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Admin access granted'),
        expect.any(Object)
      );

      consoleSpy.mockRestore();
    });

    it('should log unauthorized access attempts', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockNonAdminSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockResolvedValue(false);

      const mockRouter = { push: jest.fn() };
      require('next/navigation').useRouter.mockReturnValue(mockRouter);

      // Mock console.warn to capture security logs
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      render(
        <ManageUsersLayout>
          <div>Admin Content</div>
        </ManageUsersLayout>
      );

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/unauthorized');
      });

      // Check if security logging occurred
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Unauthorized access attempt'),
        expect.any(Object)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Session Management', () => {
    it('should handle session loading state', () => {
      (useSession as jest.Mock).mockReturnValue({
        data: null,
        status: 'loading',
      });

      render(
        <ManageUsersLayout>
          <div>Admin Content</div>
        </ManageUsersLayout>
      );

      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('should handle session errors gracefully', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: mockAdminSession,
        status: 'authenticated',
      });

      const { checkAdmin } = require('@/lib/check-admin');
      (checkAdmin as jest.Mock).mockRejectedValue(new Error('Database connection failed'));

      const mockRouter = { push: jest.fn() };
      require('next/navigation').useRouter.mockReturnValue(mockRouter);

      // Mock console.error to capture error logs
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      render(
        <ManageUsersLayout>
          <div>Admin Content</div>
        </ManageUsersLayout>
      );

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/unauthorized');
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error verifying admin permissions'),
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });
});