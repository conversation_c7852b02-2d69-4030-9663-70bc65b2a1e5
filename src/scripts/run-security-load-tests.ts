#!/usr/bin/env node --loader ts-node/esm

/**
 * Security Load Testing Script
 *
 * This script runs comprehensive load tests for security features to verify
 * they perform correctly under high traffic conditions.
 *
 * Usage:
 * npm run security:load-tests
 * or
 * npx tsx src/scripts/run-security-load-tests.ts
 */

import { SecurityLoadTester, SecurityLoadTestSuite, securityLoadTestSuites, createCustomSecurityLoadTest } from '../__tests__/security/load-testing';
import * as fs from 'fs';
import * as path from 'path';

interface LoadTestOptions {
  baseUrl?: string;
  concurrency?: number;
  duration?: number;
  outputFile?: string;
  suite?: string;
  verbose?: boolean;
}

class SecurityLoadTestRunner {
  private loadTester: SecurityLoadTester;
  private options: LoadTestOptions;

  constructor(options: LoadTestOptions = {}) {
    this.options = {
      baseUrl: 'http://localhost:3000',
      concurrency: 50,
      duration: 30,
      outputFile: './security-load-test-report.md',
      verbose: false,
      ...options
    };

    this.loadTester = new SecurityLoadTester(this.options.baseUrl!);
  }

  async runAllSuites(): Promise<void> {
    console.log('🚀 Starting Security Load Testing Suite...\n');
    console.log(`Target: ${this.options.baseUrl}`);
    console.log(`Concurrency: ${this.options.concurrency}`);
    console.log(`Duration: ${this.options.duration}s per test\n`);

    const allResults: Array<{ suite: SecurityLoadTestSuite; results: any[] }> = [];

    for (const suite of securityLoadTestSuites) {
      console.log(`📋 Running suite: ${suite.name}`);
      console.log(`   ${suite.description}\n`);

      try {
        const results = await this.loadTester.runSecurityLoadTestSuite(suite);
        allResults.push({ suite, results });

        // Generate individual suite report
        const report = this.loadTester.generateLoadTestReport(results, suite.name);
        const suiteReportFile = this.options.outputFile!.replace('.md', `-${suite.name.toLowerCase().replace(/\s+/g, '-')}.md`);
        fs.writeFileSync(suiteReportFile, report);

        console.log(`✅ Suite completed. Report saved to: ${suiteReportFile}\n`);

      } catch (error) {
        console.error(`❌ Suite failed: ${suite.name}`, error);
      }

      // Delay between suites
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    // Generate comprehensive report
    await this.generateComprehensiveReport(allResults);
  }

  async runSpecificSuite(suiteName: string): Promise<void> {
    const suite = securityLoadTestSuites.find(s => s.name.toLowerCase() === suiteName.toLowerCase());

    if (!suite) {
      console.error(`❌ Suite not found: ${suiteName}`);
      console.log('Available suites:');
      securityLoadTestSuites.forEach(s => console.log(`  - ${s.name}`));
      return;
    }

    console.log(`📋 Running specific suite: ${suite.name}`);
    console.log(`   ${suite.description}\n`);

    const results = await this.loadTester.runSecurityLoadTestSuite(suite);

    const report = this.loadTester.generateLoadTestReport(results, suite.name);
    fs.writeFileSync(this.options.outputFile!, report);

    console.log(`✅ Suite completed. Report saved to: ${this.options.outputFile}`);
  }

  async runCustomTest(targetUrl: string, options: Partial<LoadTestOptions> = {}): Promise<void> {
    const testConfig = createCustomSecurityLoadTest(
      `Custom Load Test - ${targetUrl}`,
      targetUrl,
      {
        concurrency: options.concurrency || this.options.concurrency,
        duration: options.duration || this.options.duration,
        method: 'GET',
        expectedStatus: 200,
        maxResponseTime: 1000,
        successRate: 95
      }
    );

    console.log(`🧪 Running custom load test: ${targetUrl}`);
    console.log(`   Concurrency: ${testConfig.concurrency}, Duration: ${testConfig.duration}s\n`);

    const result = await this.loadTester.runLoadTest(testConfig);

    const report = this.loadTester.generateLoadTestReport([result], `Custom Test - ${targetUrl}`);
    fs.writeFileSync(this.options.outputFile!, report);

    console.log(`✅ Custom test completed. Report saved to: ${this.options.outputFile}`);
  }

  private async generateComprehensiveReport(allResults: Array<{ suite: SecurityLoadTestSuite; results: any[] }>): Promise<void> {
    console.log('📊 Generating comprehensive security load test report...\n');

    let report = `# Comprehensive Security Load Test Report\n\n`;
    report += `**Generated:** ${new Date().toISOString()}\n`;
    report += `**Target:** ${this.options.baseUrl}\n`;
    report += `**Total Suites:** ${allResults.length}\n\n`;

    // Executive Summary
    const totalTests = allResults.reduce((sum, s) => sum + s.results.length, 0);
    const totalRequests = allResults.reduce((sum, s) => sum + s.results.reduce((s2, r) => s2 + r.totalRequests, 0), 0);
    const avgSuccessRate = allResults.reduce((sum, s) => sum + s.results.reduce((s2, r) => s2 + r.successRate, 0) / s.results.length, 0) / allResults.length;
    const avgResponseTime = allResults.reduce((sum, s) => sum + s.results.reduce((s2, r) => s2 + r.avgResponseTime, 0) / s.results.length, 0) / allResults.length;

    report += `## Executive Summary\n\n`;
    report += `- **Total Test Suites:** ${allResults.length}\n`;
    report += `- **Total Individual Tests:** ${totalTests}\n`;
    report += `- **Total Requests:** ${totalRequests.toLocaleString()}\n`;
    report += `- **Average Success Rate:** ${avgSuccessRate.toFixed(1)}%\n`;
    report += `- **Average Response Time:** ${avgResponseTime.toFixed(0)}ms\n\n`;

    // Suite-by-Suite Results
    report += `## Suite Results\n\n`;

    allResults.forEach(({ suite, results }) => {
      const suiteRequests = results.reduce((sum, r) => sum + r.totalRequests, 0);
      const suiteSuccessRate = results.reduce((sum, r) => sum + r.successRate, 0) / results.length;
      const suiteAvgResponseTime = results.reduce((sum, r) => sum + r.avgResponseTime, 0) / results.length;

      report += `### ${suite.name}\n`;
      report += `- **Description:** ${suite.description}\n`;
      report += `- **Tests:** ${results.length}\n`;
      report += `- **Requests:** ${suiteRequests.toLocaleString()}\n`;
      report += `- **Success Rate:** ${suiteSuccessRate.toFixed(1)}%\n`;
      report += `- **Avg Response Time:** ${suiteAvgResponseTime.toFixed(0)}ms\n`;
      report += `- **Report:** See [${suite.name.toLowerCase().replace(/\s+/g, '-')}-report.md](${suite.name.toLowerCase().replace(/\s+/g, '-')}-report.md)\n\n`;
    });

    // Performance Analysis
    report += `## Performance Analysis\n\n`;

    const slowSuites = allResults.filter(s =>
      s.results.some(r => r.p95ResponseTime > 1000)
    );

    if (slowSuites.length > 0) {
      report += `### ⚠️ Performance Issues\n\n`;
      slowSuites.forEach(({ suite, results }) => {
        const slowTests = results.filter(r => r.p95ResponseTime > 1000);
        report += `**${suite.name}:**\n`;
        slowTests.forEach((result, index) => {
          report += `- Test ${index + 1}: P95 ${result.p95ResponseTime.toFixed(0)}ms (threshold: 1000ms)\n`;
        });
      });
      report += `\n`;
    }

    const failingSuites = allResults.filter(s =>
      s.results.some(r => r.successRate < 99)
    );

    if (failingSuites.length > 0) {
      report += `### ❌ Reliability Issues\n\n`;
      failingSuites.forEach(({ suite, results }) => {
        const failingTests = results.filter(r => r.successRate < 99);
        report += `**${suite.name}:**\n`;
        failingTests.forEach((result, index) => {
          report += `- Test ${index + 1}: ${result.successRate.toFixed(1)}% success rate (threshold: 99%)\n`;
        });
      });
      report += `\n`;
    }

    // Recommendations
    report += `## Recommendations\n\n`;

    if (slowSuites.length > 0 || failingSuites.length > 0) {
      report += `### 🔧 Immediate Actions Required\n\n`;
      report += `- **Performance Optimization:**\n`;
      report += `  - Review and optimize security middleware for high-load scenarios\n`;
      report += `  - Implement caching for expensive permission checks\n`;
      report += `  - Consider async processing for audit logging\n`;
      report += `  - Optimize database queries in security-critical paths\n\n`;

      report += `- **Reliability Improvements:**\n`;
      report += `  - Fix error conditions causing request failures\n`;
      report += `  - Implement proper error handling and recovery\n`;
      report += `  - Add circuit breakers for external security services\n`;
      report += `  - Review rate limiting configuration\n\n`;
    } else {
      report += `### ✅ System Performing Well\n\n`;
      report += `- No critical performance or reliability issues detected\n`;
      report += `- Security features handling load effectively\n`;
      report += `- Continue monitoring under sustained high load\n\n`;
    }

    report += `### 📈 Long-term Improvements\n\n`;
    report += `- **Scalability:**\n`;
    report += `  - Implement horizontal scaling for security services\n`;
    report += `  - Use distributed caching for session and permission data\n`;
    report += `  - Consider microservices architecture for security components\n\n`;

    report += `- **Monitoring:**\n`;
    report += `  - Implement real-time performance monitoring\n`;
    report += `  - Set up alerting for performance degradation\n`;
    report += `  - Track security metrics in production dashboards\n\n`;

    report += `- **Optimization:**\n`;
    report += `  - Regular performance profiling of security features\n`;
    report += `  - Implement connection pooling for database connections\n`;
    report += `  - Use efficient data structures for permission lookups\n\n`;

    // Save comprehensive report
    const comprehensiveReportFile = this.options.outputFile!.replace('.md', '-comprehensive.md');
    fs.writeFileSync(comprehensiveReportFile, report);

    console.log(`📋 Comprehensive report saved to: ${comprehensiveReportFile}`);
    console.log(`📋 Individual suite reports saved to: ./*-report.md`);
  }

  static async runFromCommandLine(): Promise<void> {
    const args = process.argv.slice(2);
    const options: LoadTestOptions = {};

    // Parse command line arguments
    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      switch (arg) {
        case '--base-url':
          options.baseUrl = args[++i];
          break;
        case '--concurrency':
          options.concurrency = parseInt(args[++i]);
          break;
        case '--duration':
          options.duration = parseInt(args[++i]);
          break;
        case '--output':
          options.outputFile = args[++i];
          break;
        case '--suite':
          options.suite = args[++i];
          break;
        case '--verbose':
          options.verbose = true;
          break;
        case '--help':
          console.log(`
Security Load Testing Tool

Usage:
  npm run security:load-tests [options]

Options:
  --base-url <url>        Target URL for testing (default: http://localhost:3000)
  --concurrency <num>     Number of concurrent users (default: 50)
  --duration <seconds>    Test duration per suite (default: 30)
  --output <file>         Output report file (default: ./security-load-test-report.md)
  --suite <name>          Run specific test suite
  --verbose               Enable verbose output
  --help                  Show this help message

Examples:
  npm run security:load-tests -- --concurrency 100 --duration 60
  npm run security:load-tests -- --suite "Authentication Load Test"
  npm run security:load-tests -- --base-url https://api.example.com
          `);
          return;
      }
    }

    const runner = new SecurityLoadTestRunner(options);

    try {
      if (options.suite) {
        await runner.runSpecificSuite(options.suite);
      } else {
        await runner.runAllSuites();
      }
    } catch (error) {
      console.error('Load testing failed:', error);
      process.exit(1);
    }
  }
}

// Main execution
if (require.main === module) {
  SecurityLoadTestRunner.runFromCommandLine().catch((error) => {
    console.error('Load test execution failed:', error);
    process.exit(1);
  });
}

export { SecurityLoadTestRunner };