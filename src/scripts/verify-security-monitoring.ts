#!/usr/bin/env tsx

/**
 * Security Monitoring System Verification Script
 *
 * This script simulates various security threats and verifies that the
 * security monitoring system correctly detects, classifies, and responds to them.
 */

import { SecurityEventDetectionService, SecurityEventType, SecurityEventSeverity } from '../lib/services/security-event-detection';
import { SecurityAlertingService } from '../lib/services/security-alerting';
import { SecurityMetricsService } from '../lib/services/security-metrics';
import { SecurityAuditAnalysisService } from '../lib/services/security-audit-analysis';

interface ThreatScenario {
  name: string;
  description: string;
  events: any[];
  expectedDetection: SecurityEventType;
  expectedSeverity: SecurityEventSeverity;
  expectedAlerts: number;
  expectedResponses: number;
}

class SecurityMonitoringVerifier {
  private eventDetection: SecurityEventDetectionService;
  private alerting: SecurityAlertingService;
  private metrics: SecurityMetricsService;
  private auditAnalysis: SecurityAuditAnalysisService;

  constructor() {
    this.eventDetection = new SecurityEventDetectionService();
    this.alerting = new SecurityAlertingService();
    this.metrics = new SecurityMetricsService();
    this.auditAnalysis = new SecurityAuditAnalysisService();
  }

  async runVerification(): Promise<void> {
    console.log('🔒 Starting Security Monitoring System Verification...\n');

    const scenarios: ThreatScenario[] = [
      this.createBruteForceScenario(),
      this.createPrivilegeEscalationScenario(),
      this.createDataExfiltrationScenario(),
      this.createRateLimitScenario(),
      this.createSuspiciousActivityScenario()
    ];

    const results: VerificationResult[] = [];

    for (const scenario of scenarios) {
      console.log(`🧪 Testing: ${scenario.name}`);
      console.log(`   Description: ${scenario.description}`);

      const result = await this.testScenario(scenario);
      results.push(result);

      console.log(`   ✅ Result: ${result.passed ? 'PASSED' : 'FAILED'}`);
      if (!result.passed) {
        console.log(`   ❌ Issues: ${result.issues.join(', ')}`);
      }
      console.log('');
    }

    this.printSummary(results);
  }

  private createBruteForceScenario(): ThreatScenario {
    const now = new Date();
    const events = [];

    // Generate 10 failed login attempts
    for (let i = 0; i < 10; i++) {
      events.push({
        id: `failed_login_${i}`,
        user_id: 'target_user',
        action: 'LOGIN_FAILED',
        ip_address: '*************',
        timestamp: new Date(now.getTime() + i * 1000).toISOString(),
        metadata: { reason: 'Invalid password' }
      });
    }

    // Followed by successful login
    events.push({
      id: 'successful_login',
      user_id: 'target_user',
      action: 'LOGIN_SUCCESS',
      ip_address: '*************',
      timestamp: new Date(now.getTime() + 11000).toISOString(),
      metadata: { session_duration: 3600 }
    });

    return {
      name: 'Brute Force Attack',
      description: 'Multiple failed login attempts followed by success',
      events,
      expectedDetection: SecurityEventType.BRUTE_FORCE_ATTACK,
      expectedSeverity: SecurityEventSeverity.HIGH,
      expectedAlerts: 1,
      expectedResponses: 1
    };
  }

  private createPrivilegeEscalationScenario(): ThreatScenario {
    const now = new Date();
    const events = [];

    // Normal user activity
    events.push({
      id: 'normal_access_1',
      user_id: 'normal_user',
      action: 'DATA_ACCESS',
      resource: 'user_data',
      ip_address: '*************',
      timestamp: new Date(now.getTime()).toISOString(),
      metadata: { access_level: 'read' }
    });

    // Privilege escalation attempt
    events.push({
      id: 'privilege_escalation',
      user_id: 'normal_user',
      action: 'DATA_ACCESS',
      resource: 'admin_panel',
      ip_address: '*************',
      timestamp: new Date(now.getTime() + 5000).toISOString(),
      metadata: { access_level: 'admin', data_size: 5000000 }
    });

    return {
      name: 'Privilege Escalation',
      description: 'User attempting to access admin resources',
      events,
      expectedDetection: SecurityEventType.PRIVILEGE_ESCALATION,
      expectedSeverity: SecurityEventSeverity.CRITICAL,
      expectedAlerts: 1,
      expectedResponses: 1
    };
  }

  private createDataExfiltrationScenario(): ThreatScenario {
    const now = new Date();
    const events = [];

    // Large data downloads
    for (let i = 0; i < 5; i++) {
      events.push({
        id: `large_download_${i}`,
        user_id: 'suspicious_user',
        action: 'DATA_ACCESS',
        resource: 'sensitive_data',
        ip_address: '*************',
        timestamp: new Date(now.getTime() + i * 2000).toISOString(),
        metadata: { data_size: ********, download_speed: 'high' }
      });
    }

    // Account termination
    events.push({
      id: 'account_termination',
      user_id: 'suspicious_user',
      action: 'ACCOUNT_TERMINATION',
      resource: 'user_account',
      ip_address: '*************',
      timestamp: new Date(now.getTime() + 12000).toISOString(),
      metadata: { reason: 'Data exfiltration detected' }
    });

    return {
      name: 'Data Exfiltration',
      description: 'Large data downloads followed by account termination',
      events,
      expectedDetection: SecurityEventType.DATA_EXFILTRATION,
      expectedSeverity: SecurityEventSeverity.CRITICAL,
      expectedAlerts: 1,
      expectedResponses: 1
    };
  }

  private createRateLimitScenario(): ThreatScenario {
    const now = new Date();
    const events = [];

    // Generate 150 API requests in short time
    for (let i = 0; i < 150; i++) {
      events.push({
        id: `api_request_${i}`,
        user_id: 'rate_limited_user',
        action: 'API_REQUEST',
        resource: 'api_endpoint',
        ip_address: '*************',
        timestamp: new Date(now.getTime() + Math.floor(i / 10) * 1000).toISOString(),
        metadata: { endpoint: '/api/sensitive-data', method: 'GET' }
      });
    }

    return {
      name: 'Rate Limit Violation',
      description: 'Excessive API requests exceeding rate limits',
      events,
      expectedDetection: SecurityEventType.RATE_LIMIT_EXCEEDED,
      expectedSeverity: SecurityEventSeverity.MEDIUM,
      expectedAlerts: 1,
      expectedResponses: 1
    };
  }

  private createSuspiciousActivityScenario(): ThreatScenario {
    const now = new Date();
    const events = [];

    // Unusual access patterns
    events.push({
      id: 'unusual_login_1',
      user_id: 'normal_user',
      action: 'LOGIN_SUCCESS',
      ip_address: '***********', // Unusual IP
      timestamp: new Date(now.getTime()).toISOString(),
      metadata: { location: 'Unknown', device: 'New device' }
    });

    events.push({
      id: 'unusual_login_2',
      user_id: 'normal_user',
      action: 'LOGIN_SUCCESS',
      ip_address: '***********', // Different unusual IP
      timestamp: new Date(now.getTime() + 300000).toISOString(), // 5 minutes later
      metadata: { location: 'Unknown', device: 'Different device' }
    });

    return {
      name: 'Suspicious Activity',
      description: 'Unusual login patterns from different locations',
      events,
      expectedDetection: SecurityEventType.SUSPICIOUS_ACTIVITY,
      expectedSeverity: SecurityEventSeverity.HIGH,
      expectedAlerts: 1,
      expectedResponses: 1
    };
  }

  private async testScenario(scenario: ThreatScenario): Promise<VerificationResult> {
    const issues: string[] = [];
    let detectedEvents: any[] = [];
    let sentAlerts: any[] = [];
    let executedResponses: any[] = [];

    try {
      // Mock console.log to capture alerts
      const originalConsoleLog = console.log;
      console.log = (...args) => {
        const message = args.join(' ');
        if (message.includes('Sending') && message.includes('alert')) {
          sentAlerts.push({ message, timestamp: new Date() });
        }
        originalConsoleLog.apply(console, args);
      };

      // Process each event in the scenario
      for (const event of scenario.events) {
        // Record metrics
        await this.metrics.recordSecurityEvent(
          event.action === 'LOGIN_FAILED' ? SecurityEventType.AUTHENTICATION_FAILURE :
          event.action === 'DATA_ACCESS' ? SecurityEventType.DATA_EXFILTRATION :
          SecurityEventType.SUSPICIOUS_ACTIVITY,
          this.getSeverityFromEvent(event),
          event.user_id,
          'test_server'
        );

        // Detect security events
        const detectedEvent = await this.eventDetection.analyzeAuditLogEntry(event);
        if (detectedEvent) {
          detectedEvents.push(detectedEvent);

          // Process alerting
          await this.alerting.processSecurityEvent(detectedEvent);

          // Process incident response
          // Note: This would normally trigger automated responses
          // For verification, we'll just check that the system can handle it
        }
      }

      // Restore console.log
      console.log = originalConsoleLog;

      // Verify detection
      const relevantDetections = detectedEvents.filter(e =>
        e.type === scenario.expectedDetection
      );

      if (relevantDetections.length === 0) {
        issues.push(`Expected ${scenario.expectedDetection} detection but got none`);
      } else {
        const detection = relevantDetections[0];
        if (detection.severity !== scenario.expectedSeverity) {
          issues.push(`Expected severity ${scenario.expectedSeverity} but got ${detection.severity}`);
        }
      }

      // Verify alerting
      if (sentAlerts.length !== scenario.expectedAlerts) {
        issues.push(`Expected ${scenario.expectedAlerts} alerts but got ${sentAlerts.length}`);
      }

      // Verify metrics
      const eventMetrics = await this.metrics.getMetrics({ category: 'EVENTS' });
      if (eventMetrics.length === 0) {
        issues.push('No metrics were recorded');
      }

      // Analyze audit trail
      const analysis = await this.auditAnalysis.analyzeAuditTrail(scenario.events);
      if (analysis.patterns.length === 0 && analysis.anomalies.length === 0) {
        issues.push('No patterns or anomalies detected in audit analysis');
      }

    } catch (error) {
      issues.push(`Error during scenario execution: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      scenario: scenario.name,
      passed: issues.length === 0,
      issues,
      detectedEvents: detectedEvents.length,
      sentAlerts: sentAlerts.length,
      recordedMetrics: (await this.metrics.getMetrics({ category: 'EVENTS' })).length
    };
  }

  private getSeverityFromEvent(event: any): SecurityEventSeverity {
    if (event.metadata?.data_size > 1000000) return SecurityEventSeverity.CRITICAL;
    if (event.action === 'LOGIN_FAILED') return SecurityEventSeverity.HIGH;
    if (event.resource === 'admin_panel') return SecurityEventSeverity.CRITICAL;
    return SecurityEventSeverity.MEDIUM;
  }

  private printSummary(results: VerificationResult[]): void {
    console.log('📊 Verification Summary');
    console.log('======================');

    const passed = results.filter(r => r.passed).length;
    const total = results.length;

    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`❌ Failed: ${total - passed}/${total}`);

    if (passed === total) {
      console.log('\n🎉 All security monitoring tests PASSED!');
      console.log('The system successfully:');
      console.log('  • Detects various types of security threats');
      console.log('  • Classifies threats with appropriate severity levels');
      console.log('  • Sends alerts for critical events');
      console.log('  • Records metrics for monitoring and analysis');
      console.log('  • Analyzes audit trails for patterns and anomalies');
    } else {
      console.log('\n⚠️  Some tests failed. Review the issues above.');
      console.log('The system may need adjustments to properly detect and respond to threats.');
    }

    console.log('\n🔒 Security Monitoring System Verification Complete');
  }
}

interface VerificationResult {
  scenario: string;
  passed: boolean;
  issues: string[];
  detectedEvents: number;
  sentAlerts: number;
  recordedMetrics: number;
}

// Run verification if this script is executed directly
if (require.main === module) {
  const verifier = new SecurityMonitoringVerifier();
  verifier.runVerification().catch(console.error);
}

export type { ThreatScenario };
export { SecurityMonitoringVerifier };