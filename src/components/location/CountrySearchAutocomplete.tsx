'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Combobox, ComboboxInput, ComboboxOptions, ComboboxOption } from '@headlessui/react';

interface Country {
  id: number;
  name: string;
  code: string;
}

interface CountrySearchAutocompleteProps {
  value: string;
  onChange: (countryId: string) => void;
  onCountrySelect?: (country: Country) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

export function CountrySearchAutocomplete({
  value,
  onChange,
  onCountrySelect,
  disabled = false,
  placeholder = 'Search countries...',
  className = ''
}: CountrySearchAutocompleteProps) {
  const [query, setQuery] = useState('');
  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Debounced search function
  const searchCountries = useCallback(async (searchQuery: string) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        q: searchQuery,
        limit: '20'
      });

      const response = await fetch(`/api/countries?${params}`, {
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setCountries(Array.isArray(data) ? data : []);
    } catch (err) {
      if (err instanceof Error && err.name !== 'AbortError') {
        setError(err.message);
        console.error('Error searching countries:', err);
      }
    } finally {
      setLoading(false);
    }
  }, []);

  // Effect for debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query.trim().length >= 2) {
        searchCountries(query);
      } else {
        setCountries([]);
      }
    }, 300); // 300ms debounce

    return () => {
      clearTimeout(timeoutId);
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [query, searchCountries]);

  // Get selected country
  const selectedCountry = countries.find(c => c.id.toString() === value);

  // Filter countries based on query (client-side filtering)
  const filteredCountries = query.trim() === '' 
    ? countries 
    : countries.filter(country =>
        country.name.toLowerCase().includes(query.toLowerCase()) ||
        country.code.toLowerCase().includes(query.toLowerCase())
      );

  return (
    <div className={`relative ${className}`}>
      <Combobox value={value} onChange={onChange} disabled={disabled}>
        <div className="relative">
          <ComboboxInput
            displayValue={(countryId: string) => {
              const country = countries.find(c => c.id.toString() === countryId);
              return country ? country.name : '';
            }}
            onChange={(event) => setQuery(event.target.value)}
            placeholder={disabled ? 'Search disabled' : placeholder}
            disabled={disabled}
            className={`
              w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
              rounded-md shadow-sm focus:outline-none focus:ring-slate-500 
              focus:border-slate-500 dark:bg-gray-700 dark:text-white
              disabled:opacity-50 disabled:cursor-not-allowed
              ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
            `}
          />
          
          {/* Loading indicator */}
          {loading && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <div className="animate-spin h-4 w-4 border-2 border-slate-500 border-t-transparent rounded-full"></div>
            </div>
          )}
        </div>

        <ComboboxOptions
          className={`
            absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 
            border border-gray-300 dark:border-gray-600 rounded-md 
            shadow-lg max-h-60 overflow-auto
            ${filteredCountries.length === 0 && !loading && !error ? 'hidden' : ''}
          `}
        >
          {error && (
            <div className="px-3 py-2 text-sm text-red-600 dark:text-red-400 border-b border-gray-200 dark:border-gray-600">
              Error: {error}
            </div>
          )}
          
          {loading && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              Searching...
            </div>
          )}
          
          {!loading && !error && filteredCountries.length === 0 && query.trim().length >= 2 && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              No countries found
            </div>
          )}
          
          {!loading && !error && query.trim().length < 2 && query.trim().length > 0 && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              Type at least 2 characters to search
            </div>
          )}
          
          {filteredCountries.map((country) => (
            <ComboboxOption
              key={country.id}
              value={country.id.toString()}
              className={({ active }) => `
                px-3 py-2 text-sm cursor-pointer transition-colors duration-200
                ${active 
                  ? 'bg-slate-100 dark:bg-slate-600 text-slate-900 dark:text-white' 
                  : 'text-gray-900 dark:text-gray-100'
                }
              `}
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="font-medium">{country.name}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {country.code}
                  </div>
                </div>
              </div>
            </ComboboxOption>
          ))}
        </ComboboxOptions>
      </Combobox>
      
      {/* Error message display */}
      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}

      {/* Selected country info */}
      {selectedCountry && (
        <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-md text-sm">
          <div className="font-medium text-gray-900 dark:text-white">
            {selectedCountry.name}
          </div>
          <div className="text-gray-600 dark:text-gray-400">
            {selectedCountry.code}
          </div>
        </div>
      )}
    </div>
  );
}