'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Combobox, ComboboxInput, ComboboxOptions, ComboboxOption } from '@headlessui/react';

interface City {
  id: number;
  name: string;
  countryId: number;
}

interface CitySearchAutocompleteProps {
  value: string;
  onChange: (cityId: string) => void;
  onCitySelect?: (city: City) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  countryId?: string; // Required to filter cities by country
}

export function CitySearchAutocomplete({
  value,
  onChange,
  onCitySelect,
  disabled = false,
  placeholder = 'Search cities...',
  className = '',
  countryId
}: CitySearchAutocompleteProps) {
  const [query, setQuery] = useState('');
  const [cities, setCities] = useState<City[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Debounced search function
  const searchCities = useCallback(async (searchQuery: string) => {
    if (!countryId) {
      setError('Please select a country first');
      return;
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        q: searchQuery,
        limit: '20',
        countryId: countryId
      });

      const response = await fetch(`/api/cities?${params}`, {
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setCities(Array.isArray(data.cities) ? data.cities : []);
    } catch (err) {
      if (err instanceof Error && err.name !== 'AbortError') {
        setError(err.message);
        console.error('Error searching cities:', err);
      }
    } finally {
      setLoading(false);
    }
  }, [countryId]);

  // Effect for debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query.trim().length >= 2 && countryId) {
        searchCities(query);
      } else {
        setCities([]);
      }
    }, 300); // 300ms debounce

    return () => {
      clearTimeout(timeoutId);
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [query, searchCities, countryId]);

  // Get selected city
  const selectedCity = cities.find(c => c.id.toString() === value);

  // Filter cities based on query (client-side filtering)
  const filteredCities = query.trim() === '' 
    ? cities 
    : cities.filter(city =>
        city.name.toLowerCase().includes(query.toLowerCase())
      );

  return (
    <div className={`relative ${className}`}>
      <Combobox value={value} onChange={onChange} disabled={disabled || !countryId}>
        <div className="relative">
          <ComboboxInput
            displayValue={(cityId: string) => {
              const city = cities.find(c => c.id.toString() === cityId);
              return city ? city.name : '';
            }}
            onChange={(event) => setQuery(event.target.value)}
            placeholder={
              disabled 
                ? 'Search disabled' 
                : !countryId 
                ? 'Select a country first'
                : placeholder
            }
            disabled={disabled || !countryId}
            className={`
              w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
              rounded-md shadow-sm focus:outline-none focus:ring-slate-500 
              focus:border-slate-500 dark:bg-gray-700 dark:text-white
              disabled:opacity-50 disabled:cursor-not-allowed
              ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
            `}
          />
          
          {/* Loading indicator */}
          {loading && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <div className="animate-spin h-4 w-4 border-2 border-slate-500 border-t-transparent rounded-full"></div>
            </div>
          )}
        </div>

        <ComboboxOptions
          className={`
            absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 
            border border-gray-300 dark:border-gray-600 rounded-md 
            shadow-lg max-h-60 overflow-auto
            ${filteredCities.length === 0 && !loading && !error ? 'hidden' : ''}
          `}
        >
          {!countryId && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              Please select a country first
            </div>
          )}
          
          {error && (
            <div className="px-3 py-2 text-sm text-red-600 dark:text-red-400 border-b border-gray-200 dark:border-gray-600">
              Error: {error}
            </div>
          )}
          
          {loading && countryId && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              Searching...
            </div>
          )}
          
          {!loading && !error && countryId && filteredCities.length === 0 && query.trim().length >= 2 && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              No cities found for this country
            </div>
          )}
          
          {!loading && !error && countryId && query.trim().length < 2 && query.trim().length > 0 && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              Type at least 2 characters to search
            </div>
          )}
          
          {filteredCities.map((city) => (
            <ComboboxOption
              key={city.id}
              value={city.id.toString()}
              className={({ active }) => `
                px-3 py-2 text-sm cursor-pointer transition-colors duration-200
                ${active 
                  ? 'bg-slate-100 dark:bg-slate-600 text-slate-900 dark:text-white' 
                  : 'text-gray-900 dark:text-gray-100'
                }
              `}
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="font-medium">{city.name}</div>
                </div>
              </div>
            </ComboboxOption>
          ))}
        </ComboboxOptions>
      </Combobox>
      
      {/* Error message display */}
      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}

      {/* Selected city info */}
      {selectedCity && (
        <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-md text-sm">
          <div className="font-medium text-gray-900 dark:text-white">
            {selectedCity.name}
          </div>
        </div>
      )}
    </div>
  );
}