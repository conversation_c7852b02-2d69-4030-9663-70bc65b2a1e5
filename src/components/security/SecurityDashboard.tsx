'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Shield,
  AlertTriangle,
  Activity,
  Clock,
  RefreshCw,
  Eye,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { z } from 'zod';
import {
  SECURITY_ALERT_CHANNEL_TYPES,
  SECURITY_ALERT_STATUSES,
  SECURITY_DASHBOARD_TIMEFRAMES,
  SECURITY_EVENT_SEVERITIES,
  SECURITY_EVENT_STATUSES,
} from '@/lib/types/security-dashboard.types';
import type {
  SecurityAlertChannelType,
  SecurityAlertStatus,
  SecurityDashboardApiResponse,
  SecurityDashboardTimeframe,
  SecurityEventSeverity,
  SecurityEventStatus,
} from '@/lib/types/security-dashboard.types';

interface SecurityEvent {
  id: string;
  type: string;
  severity: SecurityEventSeverity;
  description: string;
  source: string;
  userId?: string;
  serverId?: string;
  ipAddress: string;
  userAgent?: string;
  metadata: Record<string, unknown>;
  detectedAt: Date;
  resolvedAt?: Date;
  status: SecurityEventStatus;
  assignedTo?: string;
  resolution?: string;
}

interface SecurityAlert {
  id: string;
  eventId: string;
  ruleId: string;
  channels: Array<{ type: SecurityAlertChannelType; config: Record<string, unknown> }>;
  sentAt: Date;
  status: SecurityAlertStatus;
  errorMessage?: string;
  metadata: Record<string, unknown>;
}

interface DashboardStats {
  totalEvents: number;
  eventsByType: Array<{ type: string; count: number }>;
  eventsBySeverity: Array<{ severity: SecurityEventSeverity; count: number }>;
  unresolvedEvents: number;
  totalAlerts: number;
  failedAlerts: number;
  timeframe: SecurityDashboardTimeframe;
}

const securityEventSchema = z.object({
  id: z.string(),
  type: z.string(),
  severity: z.enum(SECURITY_EVENT_SEVERITIES),
  description: z.string(),
  source: z.string(),
  userId: z.string().nullish(),
  serverId: z.string().nullish(),
  ipAddress: z.string(),
  userAgent: z.string().nullish(),
  metadata: z.record(z.string(), z.unknown()),
  detectedAt: z.string(),
  resolvedAt: z.string().nullish(),
  status: z.enum(SECURITY_EVENT_STATUSES),
  assignedTo: z.string().nullish(),
  resolution: z.string().nullish(),
});

const alertChannelSchema = z.object({
  type: z.enum(SECURITY_ALERT_CHANNEL_TYPES),
  config: z.record(z.string(), z.unknown()),
});

const securityAlertSchema = z.object({
  id: z.string(),
  eventId: z.string(),
  ruleId: z.string(),
  channels: z.array(alertChannelSchema),
  sentAt: z.string(),
  status: z.enum(SECURITY_ALERT_STATUSES),
  errorMessage: z.string().nullish(),
  metadata: z.record(z.string(), z.unknown()),
});

const dashboardStatsSchema = z.object({
  totalEvents: z.number(),
  unresolvedEvents: z.number(),
  totalAlerts: z.number(),
  failedAlerts: z.number(),
  timeframe: z.enum(SECURITY_DASHBOARD_TIMEFRAMES),
  eventsByType: z.array(
    z.object({
      type: z.string(),
      count: z.number(),
    })
  ),
  eventsBySeverity: z.array(
    z.object({
      severity: z.enum(SECURITY_EVENT_SEVERITIES),
      count: z.number(),
    })
  ),
});

const securityDashboardResponseSchema = z.object({
  events: z.array(securityEventSchema),
  alerts: z.array(securityAlertSchema),
  stats: dashboardStatsSchema,
});

const isValidTimeframe = (value: string): value is SecurityDashboardTimeframe =>
  SECURITY_DASHBOARD_TIMEFRAMES.includes(value as SecurityDashboardTimeframe);

export default function SecurityDashboard() {
  const [events, setEvents] = useState<SecurityEvent[]>([]);
  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState<SecurityDashboardTimeframe>('day');

  const loadDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const params = new URLSearchParams({ timeframe: selectedTimeframe });

      const queryString = params.toString();
      const endpoint = queryString ? `/api/admin/security-dashboard?${queryString}` : '/api/admin/security-dashboard';

      const response = await fetch(endpoint, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to load dashboard data (${response.status})`);
      }

      const rawData = await response.json();
      const validated: SecurityDashboardApiResponse = securityDashboardResponseSchema.parse(rawData);

      const normalisedEvents: SecurityEvent[] = validated.events.map((event) => ({
        id: event.id,
        type: event.type,
        severity: event.severity,
        description: event.description,
        source: event.source,
        userId: event.userId ?? undefined,
        serverId: event.serverId ?? undefined,
        ipAddress: event.ipAddress,
        userAgent: event.userAgent ?? undefined,
        metadata: event.metadata,
        detectedAt: new Date(event.detectedAt),
        resolvedAt: event.resolvedAt ? new Date(event.resolvedAt) : undefined,
        status: event.status,
        assignedTo: event.assignedTo ?? undefined,
        resolution: event.resolution ?? undefined,
      }));

      const normalisedAlerts: SecurityAlert[] = validated.alerts.map((alert) => ({
        id: alert.id,
        eventId: alert.eventId,
        ruleId: alert.ruleId,
        channels: alert.channels,
        sentAt: new Date(alert.sentAt),
        status: alert.status,
        errorMessage: alert.errorMessage ?? undefined,
        metadata: alert.metadata,
      }));

      const normalisedStats: DashboardStats = {
        totalEvents: validated.stats.totalEvents,
        unresolvedEvents: validated.stats.unresolvedEvents,
        totalAlerts: validated.stats.totalAlerts,
        failedAlerts: validated.stats.failedAlerts,
        timeframe: validated.stats.timeframe,
        eventsByType: validated.stats.eventsByType,
        eventsBySeverity: validated.stats.eventsBySeverity,
      };

      setEvents(normalisedEvents);
      setAlerts(normalisedAlerts);
      setStats(normalisedStats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  }, [selectedTimeframe]);

  useEffect(() => {
    void loadDashboardData();
  }, [loadDashboardData]);

  const getSeverityColor = (severity: SecurityEventSeverity) => {
    switch (severity) {
      case 'CRITICAL':
        return 'destructive';
      case 'HIGH':
        return 'destructive';
      case 'MEDIUM':
        return 'default';
      case 'LOW':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  const getStatusColor = (status: SecurityEventStatus) => {
    switch (status) {
      case 'OPEN':
        return 'destructive';
      case 'INVESTIGATING':
        return 'default';
      case 'RESOLVED':
        return 'secondary';
      case 'FALSE_POSITIVE':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getAlertStatusColor = (status: SecurityAlertStatus) => {
    switch (status) {
      case 'SENT':
        return 'secondary';
      case 'FAILED':
        return 'destructive';
      case 'PENDING':
        return 'default';
      default:
        return 'secondary';
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="border-red-200 bg-red-50">
        <AlertCircle className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-800">
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Security Dashboard</h1>
          <p className="text-gray-600 dark:text-gray-300 mt-1">
            Monitor and manage security events and alerts
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select
            value={selectedTimeframe}
            onValueChange={(value) => {
              if (isValidTimeframe(value)) {
                setSelectedTimeframe(value);
              }
            }}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="hour">Last Hour</SelectItem>
              <SelectItem value="day">Last Day</SelectItem>
              <SelectItem value="week">Last Week</SelectItem>
              <SelectItem value="month">Last Month</SelectItem>
            </SelectContent>
          </Select>
          <Button
            data-testid="refresh-button"
            onClick={() => {
              void loadDashboardData();
            }}
            variant="outline"
            size="sm"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Events</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalEvents}</div>
              <p className="text-xs text-muted-foreground">
                {stats.timeframe} timeframe
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Unresolved Events</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.unresolvedEvents}</div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Alerts</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalAlerts}</div>
              <p className="text-xs text-muted-foreground">
                Sent notifications
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed Alerts</CardTitle>
              <XCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.failedAlerts}</div>
              <p className="text-xs text-muted-foreground">
                Delivery failures
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs defaultValue="events" className="space-y-4">
        <TabsList>
          <TabsTrigger value="events">Security Events</TabsTrigger>
          <TabsTrigger value="alerts">Alert History</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Security Events</CardTitle>
              <CardDescription>
                Latest security events detected by the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {events.length === 0 ? (
                  <p className="text-center text-gray-500 py-8">No security events found</p>
                ) : (
                  events.map((event) => (
                    <div key={event.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          {event.severity === 'CRITICAL' && (
                            <AlertTriangle className="h-5 w-5 text-red-500" />
                          )}
                          {event.severity === 'HIGH' && (
                            <AlertTriangle className="h-5 w-5 text-orange-500" />
                          )}
                          {event.severity === 'MEDIUM' && (
                            <AlertCircle className="h-5 w-5 text-yellow-500" />
                          )}
                          {event.severity === 'LOW' && (
                            <Shield className="h-5 w-5 text-blue-500" />
                          )}
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <h4 className="text-sm font-medium">{event.type}</h4>
                            <Badge variant={getSeverityColor(event.severity)}>
                              {event.severity}
                            </Badge>
                            <Badge variant={getStatusColor(event.status)}>
                              {event.status}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                            {event.description}
                          </p>
                          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            <span className="flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              {formatDate(event.detectedAt)}
                            </span>
                            <span>IP: {event.ipAddress}</span>
                            {event.userId && <span>User: {event.userId}</span>}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                        {event.status === 'OPEN' && (
                          <Button variant="outline" size="sm">
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Resolve
                          </Button>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Alert History</CardTitle>
              <CardDescription>
                History of security alerts sent by the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.length === 0 ? (
                  <p className="text-center text-gray-500 py-8">No alerts found</p>
                ) : (
                  alerts.map((alert) => (
                    <div key={alert.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          {alert.status === 'SENT' && (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          )}
                          {alert.status === 'FAILED' && (
                            <XCircle className="h-5 w-5 text-red-500" />
                          )}
                          {alert.status === 'PENDING' && (
                            <Clock className="h-5 w-5 text-yellow-500" />
                          )}
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <h4 className="text-sm font-medium">Alert {alert.ruleId}</h4>
                            <Badge variant={getAlertStatusColor(alert.status)}>
                              {alert.status}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                            Event ID: {alert.eventId}
                          </p>
                          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            <span className="flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              {formatDate(alert.sentAt)}
                            </span>
                            <span>Channels: {alert.channels.length}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          Details
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Events by Type</CardTitle>
              </CardHeader>
              <CardContent>
                {stats?.eventsByType.map((item) => (
                  <div key={item.type} className="flex items-center justify-between py-2">
                    <span className="text-sm">{item.type}</span>
                    <span className="text-sm font-medium">{item.count}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Events by Severity</CardTitle>
              </CardHeader>
              <CardContent>
                {stats?.eventsBySeverity.map((item) => (
                  <div key={item.severity} className="flex items-center justify-between py-2">
                    <span className="text-sm">{item.severity}</span>
                    <span className="text-sm font-medium">{item.count}</span>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
