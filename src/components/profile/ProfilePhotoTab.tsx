'use client';

import React, { useRef } from 'react';
import { Camera } from 'lucide-react';
import Image from 'next/image';

interface ProfilePhotoTabProps {
  imagePreview: string | null;
  onImageChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onTriggerFileInput: () => void;
  onRemoveImage: () => void;
  saving: boolean;
}

export default function ProfilePhotoTab({
  imagePreview,
  onImageChange,
  onTriggerFileInput,
  onRemoveImage,
  saving
}: ProfilePhotoTabProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  return (
    <div className="space-y-6" data-testid="profile-photo-section">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <Camera className="h-5 w-5 mr-2" />
          Profile Image
        </h2>
        <div className="flex items-center space-x-6">
          <div className="relative">
            {imagePreview ? (
              <Image
                src={imagePreview}
                alt="Profile preview"
                width={96}
                height={96}
                className="rounded-full object-cover border-2 border-gray-300 dark:border-gray-600"
                data-testid="profile-photo-preview"
              />
            ) : (
              <div className="w-24 h-24 rounded-full bg-gray-200 dark:bg-gray-700 border-2 border-gray-300 dark:border-gray-600 flex items-center justify-center">
                <span className="text-gray-500 dark:text-gray-400">No Image</span>
              </div>
            )}
          </div>
          <div className="flex flex-col space-y-2">
            <input
              type="file"
              ref={fileInputRef}
              onChange={onImageChange}
              accept="image/*"
              className="hidden"
              data-testid="profile-photo-input"
            />
            <button
              type="button"
              onClick={onTriggerFileInput}
              className="px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 text-sm"
              data-testid="profile-photo-change"
            >
              Change Image
            </button>
            {imagePreview && (
              <button
                type="button"
                onClick={onRemoveImage}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                data-testid="profile-photo-remove"
              >
                Remove
              </button>
            )}
            <p className="text-xs text-gray-500 dark:text-gray-400">
              JPG, PNG or GIF. Max 5MB.
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <button
          type="button"
          onClick={() => {}} // This will be handled by the parent component
          className="px-6 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50"
          disabled={saving}
          data-testid="profile-photo-save"
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>
    </div>
  );
}
