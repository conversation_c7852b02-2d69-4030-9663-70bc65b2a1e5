'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';

interface UserProfileData {
  id: string;
  name: string | null;
  email: string | null;
  firstName: string | null;
  lastName: string | null;
  bio: string | null;
  phone: string | null;
  dateOfBirth: string | null;
  country: string | null;
  city: string | null;
  image: string | null;
}

interface FormData {
  firstName: string;
  lastName: string;
  bio: string;
  phone: string;
  dateOfBirth: string;
  streetName: string;
  aptSuiteUnit: string;
  townCity: string;
  stateProvince: string;
  postcodeZip: string;
  country: string;
  city: string;
}

interface ContactData {
  personalEmail: string;
  nwaEmail: string;
  mobile: string;
}

interface PasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export function useProfileForm() {
  const { data: session, status, update } = useSession();
  const [profile, setProfile] = useState<UserProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [twoFAEnabled, setTwoFAEnabled] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState('');

  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    bio: '',
    phone: '',
    dateOfBirth: '',
    streetName: '',
    aptSuiteUnit: '',
    townCity: '',
    stateProvince: '',
    postcodeZip: '',
    country: '',
    city: '',
  });

  const [contactData, setContactData] = useState<ContactData>({
    personalEmail: '',
    nwaEmail: '',
    mobile: '',
  });

  const [passwordData, setPasswordData] = useState<PasswordData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (status === 'authenticated') {
      fetchProfile();
    }
  }, [status]);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      console.log('🔍 [DEBUG] Fetching profile from /api/user');

      const response = await fetch('/api/user');
      console.log('🔍 [DEBUG] Response status:', response.status);
      console.log('🔍 [DEBUG] Response ok:', response.ok);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🔍 [DEBUG] Response not ok:', {
          status: response.status,
          statusText: response.statusText,
          errorText: errorText
        });
        throw new Error(`Failed to fetch profile: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('🔍 [DEBUG] Profile data received successfully');
      setProfile(data);

      // Initialize form data
      setFormData({
        firstName: data.firstName || '',
        lastName: data.lastName || '',
        bio: data.bio || '',
        phone: data.phone || '',
        dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth).toISOString().split('T')[0] : '',
        streetName: data.streetName || '',
        aptSuiteUnit: data.aptSuiteUnit || '',
        townCity: data.townCity || '',
        stateProvince: data.stateProvince || '',
        postcodeZip: data.postcodeZip || '',
        country: data.country || '',
        city: data.city || '',
      });

      // Initialize contact data
      setContactData({
        personalEmail: data.personalEmail || '',
        nwaEmail: data.nwaEmail || '',
        mobile: data.mobile || '',
      });

      // Set 2FA status
      setTwoFAEnabled(data.twoFactorEnabled || false);

      if (data.image) {
        setImagePreview(data.image);
      }
    } catch (error) {
      toast.error('Failed to load profile');
      console.error('Error fetching profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleContactChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setContactData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleContactDataChange = (value: string, field: keyof ContactData) => {
    setContactData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('Image size should be less than 5MB');
        return;
      }

      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const removeImage = () => {
    setImagePreview(null);
    setImageFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      // Handle image upload first if there's a new image
      if (imageFile) {
        const formData = new FormData();
        formData.append('image', imageFile);

        const imageResponse = await fetch('/api/user/image', {
          method: 'POST',
          body: formData,
        });

        if (!imageResponse.ok) {
          const errorData = await imageResponse.json();
          throw new Error(errorData.error || 'Failed to upload image');
        }

        const imageData = await imageResponse.json();

        // Update session with new image
        await update({
          ...session,
          user: {
            ...session?.user,
            image: imageData.url,
          },
        });
      } else if (imagePreview === null && profile?.image) {
        // If user removed their image
        const deleteResponse = await fetch('/api/user/image', {
          method: 'DELETE',
        });

        if (!deleteResponse.ok) {
          const errorData = await deleteResponse.json();
          throw new Error(errorData.error || 'Failed to remove image');
        }

        // Update session to remove image
        await update({
          ...session,
          user: {
            ...session?.user,
            image: null,
          },
        });
      }

      // Update profile data
      const profileResponse = await fetch('/api/user', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          ...contactData,
        }),
      });

      if (!profileResponse.ok) {
        throw new Error('Failed to update profile');
      }

      const updatedProfile = await profileResponse.json();
      setProfile(updatedProfile);

      // Update session with new name if it changed
      const fullName = `${formData.firstName} ${formData.lastName}`.trim();
      if (fullName !== (profile?.firstName && profile?.lastName ? `${profile.firstName} ${profile.lastName}`.trim() : profile?.name || '')) {
        await update({
          ...session,
          user: {
            ...session?.user,
            name: fullName || null,
          },
        });
      }

      toast.success('Profile updated successfully');
    } catch (error: any) {
      toast.error(error.message || 'Failed to update profile');
      console.error('Error updating profile:', error);
    } finally {
      setSaving(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // If all password fields are empty, skip password update
    if (!passwordData.currentPassword && !passwordData.newPassword && !passwordData.confirmPassword) {
      return;
    }

    // Validate password fields
    if (!passwordData.currentPassword) {
      toast.error('Please enter your current password');
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }

    if (passwordData.newPassword.length < 8) {
      toast.error('Password must be at least 8 characters long');
      return;
    }

    setSaving(true);

    try {
      const response = await fetch('/api/user/password', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update password');
      }

      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });

      toast.success('Password updated successfully');
    } catch (error: any) {
      toast.error(error.message || 'Failed to update password');
      console.error('Error updating password:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleEnable2FA = async () => {
    try {
      const response = await fetch('/api/user/2fa', {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to enable 2FA');
      }

      const data = await response.json();
      setTwoFAEnabled(true);
      setQrCodeUrl(data.qrCodeUrl);
      setShowQRCode(true);

      toast.success('2FA enabled! Scan the QR code with your authenticator app.');
    } catch (error: any) {
      toast.error(error.message || 'Failed to enable 2FA');
      console.error('Error enabling 2FA:', error);
    }
  };

  const handleDisable2FA = async () => {
    try {
      const response = await fetch('/api/user/2fa', {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to disable 2FA');
      }

      setTwoFAEnabled(false);
      setShowQRCode(false);
      setQrCodeUrl('');

      toast.success('2FA disabled');
    } catch (error: any) {
      toast.error(error.message || 'Failed to disable 2FA');
      console.error('Error disabling 2FA:', error);
    }
  };

  return {
    // State
    profile,
    loading,
    saving,
    twoFAEnabled,
    showQRCode,
    qrCodeUrl,
    formData,
    contactData,
    passwordData,
    imagePreview,
    imageFile,
    fileInputRef,

    // Handlers
    handleInputChange,
    handleContactChange,
    handleContactDataChange,
    handlePasswordChange,
    handleImageChange,
    triggerFileInput,
    removeImage,
    handleSubmit,
    handlePasswordSubmit,
    handleEnable2FA,
    handleDisable2FA,
  };
}