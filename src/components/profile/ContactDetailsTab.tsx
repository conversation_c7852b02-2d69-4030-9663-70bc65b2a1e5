'use client';

import React from 'react';
import { Mail, Phone, Smartphone, MapPin, Building2 } from 'lucide-react';
import { EmailInput } from '@/components/users/manage/shared/EmailInput';

interface FormData {
  firstName: string;
  lastName: string;
  bio: string;
  phone: string;
  dateOfBirth: string;
  streetName: string;
  aptSuiteUnit: string;
  townCity: string;
  stateProvince: string;
  postcodeZip: string;
  country: string;
  city: string;
}

interface ContactData {
  personalEmail: string;
  nwaEmail: string;
  mobile: string;
}

interface ContactDetailsTabProps {
  formData: FormData;
  contactData: ContactData;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onContactChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onContactDataChange: (value: string, field: keyof ContactData) => void;
  saving: boolean;
}

export default function ContactDetailsTab({
  formData,
  contactData,
  onInputChange,
  onContactChange,
  onContactDataChange,
  saving
}: ContactDetailsTabProps) {
  return (
    <div className="space-y-6" data-testid="profile-contact-section">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <Mail className="h-5 w-5 mr-2" />
          Contact Information
        </h2>

        <div className="space-y-6">
          {/* Address Information */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label htmlFor="aptSuiteUnit" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Building2 className="inline h-4 w-4 mr-1" />
                  Apt/Suite/Unit <span className="text-gray-500">(Optional)</span>
                </label>
                <input
                  type="text"
                  id="aptSuiteUnit"
                  name="aptSuiteUnit"
                  value={formData.aptSuiteUnit}
                  onChange={onInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Apt 4B, Suite 100, etc. (optional)"
                  data-testid="profile-apt-suite-unit"
                />
              </div>

              <div>
                <label htmlFor="streetName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <MapPin className="inline h-4 w-4 mr-1" />
                  Street Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="streetName"
                  name="streetName"
                  value={formData.streetName}
                  onChange={onInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Main Street"
                  data-testid="profile-street-name"
                />
              </div>

              <div>
                <label htmlFor="townCity" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <MapPin className="inline h-4 w-4 mr-1" />
                  Town/City <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="townCity"
                  name="townCity"
                  value={formData.townCity}
                  onChange={onInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter town or city"
                  data-testid="profile-town-city"
                />
              </div>

              <div>
                <label htmlFor="stateProvince" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <MapPin className="inline h-4 w-4 mr-1" />
                  State/Province/Region <span className="text-gray-500">(Optional)</span>
                </label>
                <input
                  type="text"
                  id="stateProvince"
                  name="stateProvince"
                  value={formData.stateProvince}
                  onChange={onInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
                  placeholder="State, province, or region (optional)"
                  data-testid="profile-state-province"
                />
              </div>

              <div>
                <label htmlFor="postcodeZip" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <MapPin className="inline h-4 w-4 mr-1" />
                  Postcode <span className="text-gray-500">(Optional)</span>
                </label>
                <input
                  type="text"
                  id="postcodeZip"
                  name="postcodeZip"
                  value={formData.postcodeZip}
                  onChange={onInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Postcode (optional)"
                  data-testid="profile-postcode"
                />
              </div>

              <div>
                <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <MapPin className="inline h-4 w-4 mr-1" />
                  Country <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="country"
                  name="country"
                  value={formData.country}
                  onChange={onInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter your country"
                  data-testid="profile-country"
                />
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
            <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <Mail className="h-4 w-4 mr-2" />
              Contact Information
            </h3>

            <div className="space-y-6">
              <div data-testid="profile-personal-email">
                <EmailInput
                  name="personalEmail"
                  label="Personal Email"
                  placeholder="Enter personal email address"
                  defaultValue={contactData.personalEmail}
                  onChange={(value) => onContactDataChange(value, 'personalEmail')}
                  required={true}
                />
              </div>

              <div data-testid="profile-nwa-email">
                <EmailInput
                  name="nwaEmail"
                  label="NWA Email"
                  placeholder="Enter NWA email address"
                  defaultValue={contactData.nwaEmail}
                  onChange={(value) => onContactDataChange(value, 'nwaEmail')}
                  required={false}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    <Phone className="inline h-4 w-4 mr-1" />
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={onInputChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
                    placeholder="+****************"
                    data-testid="profile-phone"
                  />
                </div>

                <div>
                  <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    <Smartphone className="inline h-4 w-4 mr-1" />
                    Mobile Number
                  </label>
                  <input
                    type="tel"
                    id="mobile"
                    name="mobile"
                    value={contactData.mobile}
                    onChange={onContactChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
                    placeholder="+****************"
                    data-testid="profile-mobile"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <button
          type="button"
          onClick={() => {}} // This will be handled by the parent component
          className="px-6 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50"
          disabled={saving}
          data-testid="profile-contact-save"
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>
    </div>
  );
}
