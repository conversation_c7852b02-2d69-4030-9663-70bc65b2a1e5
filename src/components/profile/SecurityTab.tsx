'use client';

import React from 'react';
import { Shield } from 'lucide-react';
import Image from 'next/image';

interface PasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface SecurityTabProps {
  passwordData: PasswordData;
  twoFAEnabled: boolean;
  showQRCode: boolean;
  qrCodeUrl: string;
  onPasswordChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onPasswordSubmit: (e: React.FormEvent) => void;
  onEnable2FA: () => void;
  onDisable2FA: () => void;
  saving: boolean;
}

export default function SecurityTab({
  passwordData,
  twoFAEnabled,
  showQRCode,
  qrCodeUrl,
  onPasswordChange,
  onPasswordSubmit,
  onEnable2FA,
  onDisable2FA,
  saving
}: SecurityTabProps) {
  return (
    <div className="space-y-6" data-testid="profile-security-section">
      {/* Password Section */}
      <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <Shield className="h-5 w-5 mr-2" />
          Change Password
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Current Password
            </label>
            <input
              type="password"
              id="currentPassword"
              name="currentPassword"
              value={passwordData.currentPassword}
              onChange={onPasswordChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
              data-testid="profile-current-password"
            />
          </div>

          <div></div>

          <div>
            <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              New Password
            </label>
            <input
              type="password"
              id="newPassword"
              name="newPassword"
              value={passwordData.newPassword}
              onChange={onPasswordChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
              minLength={8}
              data-testid="profile-new-password"
            />
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Leave blank to keep current password
            </p>
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Confirm New Password
            </label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              value={passwordData.confirmPassword}
              onChange={onPasswordChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
              data-testid="profile-confirm-password"
            />
          </div>
        </div>
      </div>

      {/* 2FA Section */}
      <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <Shield className="h-5 w-5 mr-2" />
          Two-Factor Authentication
        </h2>
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg" data-testid="profile-twofa-card">
          <div>
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">Two-Factor Authentication</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Add an extra layer of security to your account
            </p>
          </div>
          {twoFAEnabled ? (
            <button
              type="button"
              onClick={onDisable2FA}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm"
              data-testid="profile-twofa-disable"
            >
              Disable
            </button>
          ) : (
            <button
              type="button"
              onClick={onEnable2FA}
              className="px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 text-sm"
              data-testid="profile-twofa-enable"
            >
              Enable
            </button>
          )}
        </div>

        {showQRCode && qrCodeUrl && (
          <div className="mt-4 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg" data-testid="profile-twofa-qr">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Scan QR Code</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
              Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)
            </p>
            <div className="flex justify-center">
              <Image
                src={qrCodeUrl}
                alt="2FA QR Code"
                width={128}
                height={128}
                className="w-32 h-32"
              />
            </div>
            <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
              After scanning, click "Enable" to complete the setup
            </p>
          </div>
        )}
      </div>

      <div className="flex justify-end">
        <button
          type="button"
          onClick={onPasswordSubmit}
          className="px-6 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50"
          disabled={saving}
          data-testid="profile-security-save"
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>
    </div>
  );
}
