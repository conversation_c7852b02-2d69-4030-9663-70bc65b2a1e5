'use client';

import React, { useState, useEffect } from 'react';
import { Clock, User, Filter, ChevronLeft, ChevronRight, Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';

interface AuditLog {
  id: string;
  action: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    displayName: string;
  } | null;
}

interface AuditLogDisplayProps {
  userId: string;
  className?: string;
}

export const AuditLogDisplay: React.FC<AuditLogDisplayProps> = ({ userId, className = '' }) => {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });
  const [actionFilter, setActionFilter] = useState('');
  const [showDetails, setShowDetails] = useState<Set<string>>(new Set());
  const [targetUser, setTargetUser] = useState<{ name: string; email: string } | null>(null);

  // Fetch audit logs
  const fetchAuditLogs = React.useCallback(async (page = 1, action = '') => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
      });
      
      if (action) {
        params.set('action', action);
      }

      const response = await fetch(`/api/users/${userId}/audit?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch audit logs');
      }

      const data = await response.json();
      setAuditLogs(data.auditLogs);
      setPagination(data.pagination);
      setTargetUser(data.targetUser);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      toast.error('Failed to load audit logs');
    } finally {
      setLoading(false);
    }
  }, [userId, pagination.limit]);

  // Initial load
  useEffect(() => {
    if (userId) {
      fetchAuditLogs(1, actionFilter);
    }
  }, [userId, actionFilter, fetchAuditLogs]);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.pages) {
      fetchAuditLogs(newPage, actionFilter);
    }
  };

  // Toggle details visibility
  const toggleDetails = (logId: string) => {
    setShowDetails(prev => {
      const newSet = new Set(prev);
      if (newSet.has(logId)) {
        newSet.delete(logId);
      } else {
        newSet.add(logId);
      }
      return newSet;
    });
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get action color
  const getActionColor = (action: string) => {
    if (action.includes('create')) return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/20';
    if (action.includes('update')) return 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/20';
    if (action.includes('delete')) return 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/20';
    return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/20';
  };

  // Parse details for better display
  const parseDetails = (details: string) => {
    try {
      const parsed = JSON.parse(details);
      return typeof parsed === 'object' ? parsed : details;
    } catch {
      return details;
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Audit Log
          </h3>
          {targetUser && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Changes for {targetUser.name} ({targetUser.email})
            </p>
          )}
        </div>
        
        {/* Filter */}
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-400" />
          <select
            value={actionFilter}
            onChange={(e) => setActionFilter(e.target.value)}
            className="text-sm border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">All actions</option>
            <option value="create">Create</option>
            <option value="update">Update</option>
            <option value="delete">Delete</option>
          </select>
        </div>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Audit logs list */}
      {!loading && (
        <div className="space-y-3">
          {auditLogs.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              No audit logs found for this user.
            </div>
          ) : (
            auditLogs.map((log) => (
              <div
                key={log.id}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getActionColor(log.action)}`}>
                        {log.action}
                      </span>
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <Clock className="h-4 w-4 mr-1" />
                        {formatDate(log.createdAt)}
                      </div>
                    </div>
                    
                    {log.user && (
                      <div className="flex items-center text-sm text-gray-600 dark:text-gray-300 mb-2">
                        <User className="h-4 w-4 mr-1" />
                        <span>by {log.user.displayName}</span>
                        <span className="text-gray-400 ml-1">({log.user.email})</span>
                      </div>
                    )}
                    
                    {/* Details toggle */}
                    <div className="text-sm">
                      <button
                        onClick={() => toggleDetails(log.id)}
                        className="flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                      >
                        {showDetails.has(log.id) ? (
                          <>
                            <EyeOff className="h-4 w-4 mr-1" />
                            Hide details
                          </>
                        ) : (
                          <>
                            <Eye className="h-4 w-4 mr-1" />
                            Show details
                          </>
                        )}
                      </button>
                      
                      {showDetails.has(log.id) && (
                        <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-900 rounded border">
                          <div className="space-y-2 text-xs">
                            <div>
                              <strong>Details:</strong>
                              <pre className="mt-1 whitespace-pre-wrap text-gray-700 dark:text-gray-300">
                                {typeof parseDetails(log.details) === 'object' 
                                  ? JSON.stringify(parseDetails(log.details), null, 2)
                                  : log.details
                                }
                              </pre>
                            </div>
                            <div>
                              <strong>IP Address:</strong> {log.ipAddress}
                            </div>
                            <div>
                              <strong>User Agent:</strong> 
                              <span className="text-gray-600 dark:text-gray-400 ml-1">
                                {log.userAgent.length > 100 
                                  ? `${log.userAgent.substring(0, 100)}...`
                                  : log.userAgent
                                }
                              </span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* Pagination */}
      {!loading && pagination.pages > 1 && (
        <div className="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-4">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
            {pagination.total} results
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="p-2 border border-gray-300 dark:border-gray-600 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Page {pagination.page} of {pagination.pages}
            </span>
            
            <button
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.pages}
              className="p-2 border border-gray-300 dark:border-gray-600 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuditLogDisplay;