'use client';

import React, { useState, useEffect } from 'react';
import { Clock, User, FileText, Eye, EyeOff } from 'lucide-react';

interface AuditLog {
  id: string;
  action: string;
  details: any;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    displayName: string;
  } | null;
}

interface UserAuditLogProps {
  userId: string;
  userName: string;
}

export const UserAuditLog: React.FC<UserAuditLogProps> = ({ userId, userName }) => {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [showDetails, setShowDetails] = useState<{ [key: string]: boolean }>({});

  const fetchAuditLogs = async (pageNum: number, append: boolean = false) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/users/${userId}/audit?page=${pageNum}&limit=20`);
      
      if (!response.ok) {
        if (response.status === 403) {
          setError('You do not have permission to view audit logs');
          return;
        }
        if (response.status === 404) {
          setError('No audit logs found for this user');
          return;
        }
        throw new Error('Failed to fetch audit logs');
      }

      const data = await response.json();
      
      if (append) {
        setAuditLogs(prev => [...prev, ...data.auditLogs]);
      } else {
        setAuditLogs(data.auditLogs);
      }
      
      setHasMore(data.pagination.page < data.pagination.pages);
      setPage(pageNum);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      setError('Failed to load audit logs');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAuditLogs(1, false);
  }, [userId]);

  const loadMore = () => {
    if (hasMore && !loading) {
      fetchAuditLogs(page + 1, true);
    }
  };

  const toggleDetails = (logId: string) => {
    setShowDetails(prev => ({
      ...prev,
      [logId]: !prev[logId]
    }));
  };

  const formatAction = (action: string) => {
    return action
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatDetails = (details: any) => {
    if (!details || typeof details !== 'object') {
      return 'No details available';
    }

    return Object.entries(details).map(([key, value]) => {
      if (key === 'password') {
        return `${key}: [HIDDEN]`;
      }
      if (typeof value === 'object' && value !== null) {
        return `${key}: ${JSON.stringify(value, null, 2)}`;
      }
      return `${key}: ${String(value)}`;
    }).join('\n');
  };

  const getActionIcon = (action: string) => {
    if (action.includes('create') || action.includes('add')) {
      return <FileText className="h-4 w-4 text-green-500" />;
    }
    if (action.includes('update') || action.includes('edit')) {
      return <FileText className="h-4 w-4 text-blue-500" />;
    }
    if (action.includes('delete') || action.includes('remove')) {
      return <FileText className="h-4 w-4 text-red-500" />;
    }
    return <FileText className="h-4 w-4 text-gray-500" />;
  };

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Clock className="h-5 w-5 text-red-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Audit Log Error
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
          <Clock className="h-5 w-5 mr-2" />
          Audit Log - {userName}
        </h3>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {auditLogs.length} entries
        </span>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:px-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
              Recent Changes
            </h3>
          </div>
        </div>

        <div className="border-t border-gray-200 dark:border-gray-700">
          {loading && auditLogs.length === 0 ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : auditLogs.length === 0 ? (
            <div className="text-center py-8">
              <Clock className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                No audit logs found
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                No changes have been recorded for this user yet.
              </p>
            </div>
          ) : (
            <ul className="divide-y divide-gray-200 dark:divide-gray-700">
              {auditLogs.map((log) => (
                <li key={log.id} className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getActionIcon(log.action)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {formatAction(log.action)}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          {log.user && (
                            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                              <User className="h-3 w-3 mr-1" />
                              {log.user.displayName || log.user.email}
                            </div>
                          )}
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {new Date(log.createdAt).toLocaleString()}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => toggleDetails(log.id)}
                        className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        {showDetails[log.id] ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  </div>
                  
                  {showDetails[log.id] && (
                    <div className="mt-3 pl-7">
                      <div className="bg-gray-50 dark:bg-gray-900 rounded-md p-3">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                          Details
                        </h4>
                        <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap break-words">
                          {formatDetails(log.details)}
                        </pre>
                        {log.ipAddress && (
                          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                            IP: {log.ipAddress}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </li>
              ))}
            </ul>
          )}
        </div>

        {hasMore && (
          <div className="px-4 py-4 sm:px-6 border-t border-gray-200 dark:border-gray-700">
            <div className="flex justify-center">
              <button
                onClick={loadMore}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? 'Loading...' : 'Load More'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};