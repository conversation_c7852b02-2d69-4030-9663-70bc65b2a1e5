'use client';

import React, { useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { User, CheckCircle, MapPin, IdCard, Briefcase, FileText, BookOpen, AlertCircle } from 'lucide-react';
import {
  UserBasicInfoForm, type UserBasicInfoData,
  UserContactForm, type UserContactData,
  UserIdentificationForm, type UserIdentificationData,
  UserPositionsForm, type UserPositionData,
  UserTreatiesForm, type UserTreatyData,
  UserOrdinancesForm, type UserOrdinanceData
} from './shared';

type StatusEntry = {
  id: string;
  text: string;
  variant: 'success' | 'error';
};

export const CreateUserTab: React.FC = () => {
  const [activeCreateUserTab, setActiveCreateUserTab] = useState<'user' | 'contact' | 'identification' | 'positions' | 'treaty' | 'ordinance'>('user');
  const [statusMessages, setStatusMessages] = useState<StatusEntry[]>([]);

  // User form data state organized by sections
  const [basicInfo, setBasicInfo] = useState<UserBasicInfoData>({
    firstName: '',
    surname: '',
    dob: '',
    personalEmail: '',
    nwaEmail: '',
    phone: '',
    mobile: '',
    bio: ''
  });

  const [contactInfo, setContactInfo] = useState<UserContactData>({
    streetAddress1: '',
    streetAddress2: '',
    town: '',
    city: '',
    country: '',
    postalCode: '',
    regionId: '',
    regionText: ''
  });

  const [identificationInfo, setIdentificationInfo] = useState<UserIdentificationData>({
    nationOrTribeId: '',
    nationOrTribeName: '',
    nationOrTribeType: null,
    ambassadorialTitles: [],
    identifications: [],
    treatyNumbers: [],
    treatyTypeDetails: [],
    peaceAmbassadorNumber: ''
  });

  const [positionInfo, setPositionInfo] = useState<UserPositionData>({
    titleId: '',
    positionId: ''
  });

  
const [treatyInfo, setTreatyInfo] = useState<UserTreatyData>({
  treatyEntries: []
});

const [ordinanceInfo, setOrdinanceInfo] = useState<UserOrdinanceData>({
  selectedOrdinances: []
});

// Handle form section state changes
const handleBasicInfoChange = (data: UserBasicInfoData) => {
  setBasicInfo(data);
};

  const handleContactInfoChange = (data: UserContactData) => {
    setContactInfo(data);
  };

  const handleIdentificationInfoChange = (data: UserIdentificationData) => {
    setIdentificationInfo(data);
  };

  const handlePositionInfoChange = (data: UserPositionData) => {
    setPositionInfo(data);
  };

  
  const handleTreatyInfoChange = (data: UserTreatyData) => {
    setTreatyInfo(data);
  };

  const handleOrdinanceInfoChange = (data: UserOrdinanceData) => {
  setOrdinanceInfo(data);
};

// Handle form section submissions
const pushStatusMessage = (text: string, variant: 'success' | 'error' = 'success') => {
    const id = `${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
    setStatusMessages((prev) => [...prev, { id, text, variant }]);

    setTimeout(() => {
      setStatusMessages((prev) => prev.filter((entry) => entry.id !== id));
    }, 5000);

    if (typeof window !== 'undefined') {
      const containerId = 'users-status-announcer';
      let container = document.getElementById(containerId);

      if (!container) {
        container = document.createElement('div');
        container.id = containerId;
        container.style.position = 'fixed';
        container.style.top = '1rem';
        container.style.right = '1rem';
        container.style.zIndex = '2147483647';
        container.style.display = 'flex';
        container.style.flexDirection = 'column';
        container.style.gap = '0.5rem';
        document.body.appendChild(container);
      }

      const announcement = document.createElement('div');
      announcement.setAttribute('data-cy-status-message', 'true');
      announcement.textContent = text;
      announcement.style.padding = '0.75rem 1rem';
      announcement.style.borderRadius = '0.5rem';
      announcement.style.boxShadow = '0 10px 25px rgba(15, 118, 110, 0.2)';
      announcement.style.fontSize = '0.875rem';
      announcement.style.fontWeight = '500';
      announcement.style.color = variant === 'success' ? 'rgb(4 120 87)' : 'rgb(185 28 28)';
      announcement.style.backgroundColor = variant === 'success' ? 'rgb(236 253 245)' : 'rgb(254 242 242)';
      announcement.style.border = variant === 'success'
        ? '1px solid rgb(16 185 129 / 0.3)'
        : '1px solid rgb(248 113 113 / 0.3)';

      container.appendChild(announcement);

      setTimeout(() => {
        announcement.remove();
        if (container && container.childElementCount === 0) {
          container.remove();
        }
      }, 5200);
  }
};

const updateDebugStep = (step: string) => {
  if (typeof window !== 'undefined') {
    (window as any).__nwaCreateWizardStep = step;
  }
};

const treatyStepEnabled = useMemo(() => {
  const identificationTreaties = identificationInfo.treatyNumbers?.length ?? 0;
  const plannedTreaties = treatyInfo.treatyEntries?.length ?? 0;
  return identificationTreaties > 0 || plannedTreaties > 0;
}, [identificationInfo.treatyNumbers, treatyInfo.treatyEntries]);

const wizardSteps = useMemo(
  () => [
    { id: 'user' as const, label: 'Basic Info', icon: <User className="h-5 w-5" />, isEnabled: true },
    { id: 'contact' as const, label: 'Contact', icon: <MapPin className="h-5 w-5" />, isEnabled: true },
    { id: 'identification' as const, label: 'Identification', icon: <IdCard className="h-5 w-5" />, isEnabled: true },
    { id: 'positions' as const, label: 'Positions', icon: <Briefcase className="h-5 w-5" />, isEnabled: true },
    { id: 'ordinance' as const, label: 'Ordinances', icon: <BookOpen className="h-5 w-5" />, isEnabled: true },
    { id: 'treaty' as const, label: 'Treaties', icon: <FileText className="h-5 w-5" />, isEnabled: treatyStepEnabled },
  ],
  [treatyStepEnabled]
);

const goToNextEnabledStep = (currentId: typeof wizardSteps[number]['id']) => {
  const currentIndex = wizardSteps.findIndex((step) => step.id === currentId);
  for (let idx = currentIndex + 1; idx < wizardSteps.length; idx += 1) {
    if (wizardSteps[idx].isEnabled) {
      setActiveCreateUserTab(wizardSteps[idx].id);
      return;
    }
  }
  setActiveCreateUserTab(currentId);
};

useEffect(() => {
  const current = wizardSteps.find((step) => step.id === activeCreateUserTab);
  if (!current?.isEnabled) {
    const firstEnabled = wizardSteps.find((step) => step.isEnabled);
    if (firstEnabled) {
      setActiveCreateUserTab(firstEnabled.id);
    }
  }
}, [activeCreateUserTab, wizardSteps]);

  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    // Skip auto-scroll on initial basic tab render to avoid shifting the layout
    if (activeCreateUserTab === 'user') {
      return;
    }

    const selector = `[data-testid="users-create-section-${activeCreateUserTab}"]`;
    const activeSection = document.querySelector(selector) as HTMLElement | null;
    const scroller = document.querySelector('[data-testid="main-content"]') as HTMLElement | null;

    if (activeSection && scroller) {
      const scrollerRect = scroller.getBoundingClientRect();
      const sectionRect = activeSection.getBoundingClientRect();
      const offset = sectionRect.top - scrollerRect.top + scroller.scrollTop - 96;

      scroller.scrollTo({
        top: Math.max(offset, 0),
        behavior: 'auto',
      });
      scroller.scrollLeft = 0;
    }
  }, [activeCreateUserTab]);

  const handleBasicInfoSave = (data: UserBasicInfoData) => {
    console.info('[CreateUserTab] Basic information saved step');
    setBasicInfo(data);
    goToNextEnabledStep('user');
    pushStatusMessage('Basic information saved!');
    toast.success('Basic information saved!');
    updateDebugStep('basic-saved');
  };

  const handleContactInfoSave = (data: UserContactData) => {
    console.info('[CreateUserTab] Contact information saved step');
    setContactInfo(data);
    goToNextEnabledStep('contact');
    pushStatusMessage('Contact information saved!');
    toast.success('Contact information saved!');
    updateDebugStep('contact-saved');
  };

  const handleIdentificationInfoSave = (data: UserIdentificationData) => {
    console.info('[CreateUserTab] Identification information saved step');
    setIdentificationInfo(data);
    goToNextEnabledStep('identification');
    pushStatusMessage('Identification information saved!');
    toast.success('Identification information saved!');
    updateDebugStep('identification-saved');
  };

  const handlePositionInfoSave = (data: UserPositionData) => {
    console.info('[CreateUserTab] Position information saved step');
    setPositionInfo(data);
    if (treatyStepEnabled) {
      setActiveCreateUserTab('treaty');
    } else {
      goToNextEnabledStep('positions');
    }
    pushStatusMessage('Position information saved!');
    toast.success('Position information saved!');
    updateDebugStep('positions-saved');
  };

  const handleTreatyInfoSave = (data: UserTreatyData) => {
    console.info('[CreateUserTab] Treaty information saved step');
    setTreatyInfo(data);
    setActiveCreateUserTab('ordinance');
    pushStatusMessage('Treaty information saved!');
    toast.success('Treaty information saved!');
    updateDebugStep('treaty-saved');
  };

  const handleOrdinanceInfoSave = (data: UserOrdinanceData) => {
    setOrdinanceInfo(data);
    handleUserCreation();
  };

  const handleUserCreation = async () => {
    try {
      // Combine all the form data
      const userData = {
        ...basicInfo,
        ...contactInfo,
        nationOrTribeId: identificationInfo.nationOrTribeId,
        nationOrTribeName: identificationInfo.nationOrTribeName,
        nationOrTribeType: identificationInfo.nationOrTribeType,
        ambassadorialTitles: identificationInfo.ambassadorialTitles,
        identifications: identificationInfo.identifications,
        treatyNumbers: identificationInfo.treatyNumbers,
        ...positionInfo,
        treatyEntries: treatyInfo.treatyEntries,
        selectedOrdinances: ordinanceInfo.selectedOrdinances
      };

      // Submit user data to the server (simulated for now)
      pushStatusMessage('User created successfully!');
      toast.success('User created successfully!');
      updateDebugStep('completed');

      // Reset form
      setBasicInfo({
        firstName: '',
        surname: '',
        dob: '',
        personalEmail: '',
        nwaEmail: '',
        phone: '',
        mobile: '',
        bio: ''
      });

      setContactInfo({
        streetAddress1: '',
        streetAddress2: '',
        town: '',
        city: '',
        country: '',
        postalCode: '',
        regionId: '',
        regionText: ''
      });

      setIdentificationInfo({
        nationOrTribeId: '',
        nationOrTribeName: '',
        nationOrTribeType: null,
        ambassadorialTitles: [],
        identifications: [],
        treatyNumbers: [],
        treatyTypeDetails: [],
        peaceAmbassadorNumber: ''
      });

      setPositionInfo({
        titleId: '',
        positionId: ''
      });

      setTreatyInfo({
        treatyEntries: []
      });

      setOrdinanceInfo({
        selectedOrdinances: []
      });

      setActiveCreateUserTab('user');
    } catch (error) {
      console.error('Error creating user:', error);
      pushStatusMessage('Failed to create user. Please try again.', 'error');
      toast.error('Failed to create user. Please try again.');
      updateDebugStep('error');
    }
  };


  return (
    <div className="p-6">
      <div>

          {statusMessages.length > 0 && (
            <div className="mb-4 space-y-2" data-testid="users-create-status">
              {statusMessages.map(({ id, text, variant }) => (
                <div
                  key={id}
                  className={`flex items-center gap-2 rounded-md px-4 py-3 text-sm shadow-sm ${
                    variant === 'success'
                      ? 'border border-emerald-200 bg-emerald-50 text-emerald-800'
                      : 'border border-red-200 bg-red-50 text-red-800'
                  }`}
                >
                  {variant === 'success' ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertCircle className="h-4 w-4" />
                  )}
                  <span>{text}</span>
                </div>
              ))}
            </div>
          )}

          {/* Tab navigation */}
          <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
            <nav className="-mb-px flex space-x-8" data-testid="users-create-tab-list">
              {wizardSteps.map((step) => (
                <button
                  key={step.id}
                  type="button"
                  onClick={() => step.isEnabled && setActiveCreateUserTab(step.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 relative focus:outline-none transition-all duration-200 ${activeCreateUserTab === step.id
                    ? 'border-emerald-500 text-emerald-600 dark:text-emerald-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                    } ${step.isEnabled ? '' : 'cursor-not-allowed opacity-50'}`}
                  data-testid={`users-create-tab-${step.id}`}
                  aria-disabled={!step.isEnabled}
                  disabled={!step.isEnabled}
                >
                  <span className="text-emerald-600 dark:text-emerald-400">
                    {step.icon}
                  </span>
                  <span>{step.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Tab content */}
          <div className="space-y-6 pb-8">
            {activeCreateUserTab === 'user' ? (
              <div data-testid="users-create-section-user" className="scroll-mt-24">
                <UserBasicInfoForm
                  mode="create"
                  data={basicInfo}
                  onChange={handleBasicInfoChange}
                  onSave={handleBasicInfoSave}
                />
              </div>
            ) : activeCreateUserTab === 'contact' ? (
              <div data-testid="users-create-section-contact" className="scroll-mt-24">
                <UserContactForm
                  mode="create"
                  data={contactInfo}
                  onChange={handleContactInfoChange}
                  onSave={handleContactInfoSave}
                />
              </div>
            ) : activeCreateUserTab === 'identification' ? (
              <div data-testid="users-create-section-identification" className="scroll-mt-24">
                <UserIdentificationForm
                  mode="create"
                  data={identificationInfo}
                  onChange={handleIdentificationInfoChange}
                  onSave={handleIdentificationInfoSave}
                />
              </div>
            ) : activeCreateUserTab === 'positions' ? (
              <div data-testid="users-create-section-positions" className="scroll-mt-24">
                <UserPositionsForm
                  mode="create"
                  data={positionInfo}
                  onChange={handlePositionInfoChange}
                  onSave={handlePositionInfoSave}
                />
              </div>
            ) : activeCreateUserTab === 'ordinance' ? (
              <div data-testid="users-create-section-ordinance" className="scroll-mt-24">
                <UserOrdinancesForm
                  mode="create"
                  data={ordinanceInfo}
                  onChange={handleOrdinanceInfoChange}
                  onSave={handleOrdinanceInfoSave}
                />
              </div>
            ) : activeCreateUserTab === 'treaty' ? (
              <div data-testid="users-create-section-treaty" className="scroll-mt-24">
                <UserTreatiesForm
                  mode="create"
                  data={treatyInfo}
                  onChange={handleTreatyInfoChange}
                  onSave={handleTreatyInfoSave}
                  purchasedTreaties={[]} // Empty array for create mode
                />
              </div>
            ) : null}
          </div>
        </div>
    </div>
  );
};
