'use client';

import React, { useState } from 'react';
import { toast } from 'sonner';
import { Search, X, Download, UserPlus } from 'lucide-react';
import { Button } from '@/components/ui/button';

export const SearchUserTab: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchFilters, setSearchFilters] = useState({
    position: '',
    title: '',
    email: '',
    phone: '',
    country: '',
    city: '',
  });
  const [users, setUsers] = useState<any[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 10
  });

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setSearchFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSearch = async () => {
    setLoading(true);

    try {
      // Build search parameters
      const searchParams = new URLSearchParams();
      if (searchQuery) searchParams.append('search', searchQuery);
      if (searchFilters.position) searchParams.append('positionId', searchFilters.position);
      if (searchFilters.title) searchParams.append('titleId', searchFilters.title);
      if (searchFilters.email) searchParams.append('email', searchFilters.email);
      if (searchFilters.phone) searchParams.append('phone', searchFilters.phone);
      if (searchFilters.country) searchParams.append('country', searchFilters.country);
      if (searchFilters.city) searchParams.append('city', searchFilters.city);

      const response = await fetch(`/api/users?${searchParams.toString()}`);

      if (!response.ok) {
        if (response.status === 404) {
          // No users found
          setUsers([]);
          setPagination({
            currentPage: 1,
            totalPages: 1,
            totalCount: 0,
            pageSize: 10
          });
          toast.info('No users found matching your search criteria');
          return;
        }
        throw new Error('Failed to search users');
      }

      const data = await response.json();
      setUsers(data.users || []);
      setPagination({
        currentPage: data.pagination?.currentPage || 1,
        totalPages: data.pagination?.totalPages || 1,
        totalCount: data.pagination?.totalCount || 0,
        pageSize: data.pagination?.pageSize || 10
      });
    } catch (error: any) {
      toast.error(error.message || 'Failed to search users');
      console.error('Error searching users:', error);
      setUsers([]);
      setPagination({
        currentPage: 1,
        totalPages: 1,
        totalCount: 0,
        pageSize: 10
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    setSearchFilters({
      position: '',
      title: '',
      email: '',
      phone: '',
      country: '',
      city: '',
    });
    setUsers([]);
  };

  const handleSelectUser = (userId: number) => {
    const newSelectedUsers = new Set(selectedUsers);
    if (newSelectedUsers.has(userId)) {
      newSelectedUsers.delete(userId);
    } else {
      newSelectedUsers.add(userId);
    }
    setSelectedUsers(newSelectedUsers);
  };

  const handleSelectAll = () => {
    if (selectedUsers.size === users.length) {
      setSelectedUsers(new Set());
    } else {
      setSelectedUsers(new Set(users.map(user => user.id)));
    }
  };

  const handleExportSelected = () => {
    if (selectedUsers.size === 0) {
      toast.error('Please select at least one user to export.');
      return;
    }
    
    try {
      // In a real implementation, you would send the export request to the API
      // For now, we'll just show a success message
      toast.success(`Exporting ${selectedUsers.size} selected users to CSV...`);
    } catch (error: any) {
      toast.error(error.message || 'Failed to export users');
      console.error('Error exporting users:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div>
      {/* Search Filters */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
          <div>
            <label htmlFor="position" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Position
            </label>
            <select
              id="position"
              name="position"
              value={searchFilters.position}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Positions</option>
              <option value="1">Administrator</option>
              <option value="2">Manager</option>
              <option value="3">Officer</option>
              <option value="4">Specialist</option>
              <option value="5">Director</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Title
            </label>
            <select
              id="title"
              name="title"
              value={searchFilters.title}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Titles</option>
              <option value="1">Mr.</option>
              <option value="2">Mrs.</option>
              <option value="3">Ms.</option>
              <option value="4">Dr.</option>
              <option value="5">Prof.</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={searchFilters.email}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              placeholder="<EMAIL>"
            />
          </div>
          
          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Phone
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={searchFilters.phone}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              placeholder="****** 567 8900"
            />
          </div>
          
          <div>
            <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Country
            </label>
            <input
              type="text"
              id="country"
              name="country"
              value={searchFilters.country}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              placeholder="Country"
            />
          </div>
          
          <div>
            <label htmlFor="city" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              City
            </label>
            <input
              type="text"
              id="city"
              name="city"
              value={searchFilters.city}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              placeholder="City"
            />
          </div>
        </div>
        
        <div className="flex justify-end space-x-3" data-testid="users-search-actions">
          <button
            type="button"
            onClick={handleClearFilters}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 flex items-center"
            data-testid="users-search-clear"
          >
            <X className="h-4 w-4 mr-2" />
            Clear Filters
          </button>
          <button
            type="button"
            onClick={handleSearch}
            disabled={loading}
            className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 transition-colors duration-200 flex items-center"
            data-testid="users-search-submit"
          >
            {loading ? 'Searching...' : (
              <>
                <Search className="h-4 w-4 mr-2" />
                Search Users
              </>
            )}
          </button>
        </div>
      </div>

      {/* Results and Actions */}
      {users.length > 0 && (
        <div className="mb-6">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-700 dark:text-gray-300">
              Showing {users.length} results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={handleSelectAll}
                className="px-3 py-1 text-sm text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-300 transition-colors duration-200 flex items-center"
              >
                {selectedUsers.size === users.length ? (
                  <>
                    <X className="h-4 w-4 mr-1" />
                    Deselect All
                  </>
                ) : (
                  <>
                    <UserPlus className="h-4 w-4 mr-1" />
                    Select All
                  </>
                )}
              </button>
              <button
                onClick={handleExportSelected}
                disabled={selectedUsers.size === 0}
                className="flex items-center px-3 py-1 text-sm text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-300 disabled:opacity-50 transition-colors duration-200"
              >
                <Download className="h-4 w-4 mr-1" />
                Export Selected ({selectedUsers.size})
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Users Table */}
      {users.length > 0 ? (
        <div className="overflow-x-auto" data-testid="users-search-results">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700" data-testid="users-search-table">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={selectedUsers.size === users.length && users.length > 0}
                    onChange={handleSelectAll}
                    className="h-4 w-4 text-slate-600 focus:ring-slate-500 border-gray-300 rounded"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Location
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Position
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Dates
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {users.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedUsers.has(user.id)}
                      onChange={() => handleSelectUser(user.id)}
                      className="h-4 w-4 text-slate-600 focus:ring-slate-500 border-gray-300 rounded"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {user.firstName} {user.surname}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {user.title}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {user.email}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {user.phone}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {user.city}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {user.country}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {user.position}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      License: {user.license}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <div>Created: {formatDate(user.created)}</div>
                    <div>Updated: {formatDate(user.updated)}</div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-12">
          <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No users found</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Try adjusting your search filters to find what you&#39;re looking for.
          </p>
        </div>
      )}
    </div>
  );
};
