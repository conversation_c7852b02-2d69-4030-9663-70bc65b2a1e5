import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { Plus } from 'lucide-react';
import { 
  Treaty, 
  TreatyType, 
  TreatyNumberResponse, 
  UserTreatyManagementProps 
} from './treaties/UserTreatyManagement.types';
import { TreatyFilters } from './treaties/TreatyFilters';
import { AddTreatyForm } from './treaties/AddTreatyForm';
import { TreatiesList } from './treaties/TreatiesList';

interface BusinessDetails {
  businessName: string;
  businessAddress: string;
  businessEmail: string;
  businessPhone?: string;
  businessWebsite?: string;
  businessDescription?: string;
  categoryId: string;
  subcategoryId: string;
  businessType: string;
}

interface PersonalDetails {
  fullLegalName: string;
  email: string;
  currentCountryId: string;
  currentCityId: string;
  phoneNumbers?: string[];
  nationality?: string[];
  dateOfBirth?: string;
  genderIdentity?: string;
  residentialAddress?: string;
  identificationNumber?: string;
  peaceProtectedPremises?: boolean;
  declarationAccepted?: boolean;
}

interface TreatyTypeBusinessDetails {
  treatyTypeId: string;
  businessDetails: BusinessDetails;
  personalDetails: PersonalDetails;
}

interface NewTreatyFormData {
  selectedTreatyId: string;
  selectedTreatyTypeIds: string[];
  signedDate: string;
  expirationDate: string;
  description: string;
  businessDetailsMap: Record<string, TreatyTypeBusinessDetails>;
}

export const UserTreatyManagement: React.FC<UserTreatyManagementProps> = ({ userId, userName }) => {
  const [treaties, setTreaties] = useState<Treaty[]>([]);
  const [availableTreatyTypes, setAvailableTreatyTypes] = useState<TreatyType[]>([]);
  const [userTreaties, setUserTreaties] = useState<Treaty[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showAddForm, setShowAddForm] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  // Form state for adding treaty types to user's assigned treaty
  const [newTreaty, setNewTreaty] = useState<NewTreatyFormData>({
    selectedTreatyId: '',
    selectedTreatyTypeIds: [] as string[],
    signedDate: '',
    expirationDate: '',
    description: '',
    businessDetailsMap: {}
  });

  const fetchTreaties = useCallback(async () => {
    try {
      // Fetch both full treaties and treaty numbers
      const [treatiesResponse, treatyNumbersResponse] = await Promise.all([
        fetch(`/api/users/${userId}/treaties`),
        fetch(`/api/users/${userId}/treaty-numbers`)
      ]);
      
      if (!treatiesResponse.ok || !treatyNumbersResponse.ok) {
        throw new Error('Failed to fetch treaties');
      }
      
      const treatiesData = await treatiesResponse.json();
      const treatyNumbersData = await treatyNumbersResponse.json();
      
      // Combine both data sources
      const fullTreaties = treatiesData.treaties || [];
      const treatyNumbers = treatyNumbersData.treatyNumbers || [];
      
      // Convert treaty numbers to treaty-like objects for display
      const treatyNumberTreaties = treatyNumbers.map((tn: TreatyNumberResponse) => ({
        id: `treaty-number-${tn.id}`,
        name: tn.treatyType?.name || 'Unknown Treaty',
        status: 'ACTIVE',
        signedDate: null,
        expirationDate: null,
        renewalDate: null,
        treatyTypes: [{ id: tn.treatyType?.id, name: tn.treatyType?.name || 'Unknown' }],
        notes: `Treaty Number: ${tn.treatyNumber}`,
        createdAt: tn.createdAt,
        updatedAt: tn.updatedAt,
        isTreatyNumber: true
      }));
      
      // Combine and deduplicate by ID
      const combinedTreaties = [...fullTreaties, ...treatyNumberTreaties];
      setTreaties(combinedTreaties);
      setTotalPages(Math.ceil(combinedTreaties.length / 10));
    } catch (error) {
      console.error('Error fetching treaties:', error);
      toast.error('Failed to load treaties');
      setTreaties([]);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  const fetchUserTreaties = async () => {
    try {
      const response = await fetch(`/api/users/${userId}/treaty-numbers`);
      if (!response.ok) throw new Error('Failed to fetch user treaties');
      
      const data = await response.json();
      const userTreatyNumbers = data.treatyNumbers || [];
      
      // Convert to treaty objects for the select dropdown
      const treatyOptions = userTreatyNumbers.map((tn: TreatyNumberResponse) => ({
        id: tn.id,
        name: tn.treatyType?.name || 'Unknown Treaty',
        status: 'ACTIVE',
        signedDate: null,
        expirationDate: null,
        renewalDate: null,
        treatyTypes: [{ id: tn.treatyType?.id, name: tn.treatyType?.name || 'Unknown' }],
        notes: tn.treatyNumber,
        createdAt: tn.createdAt,
        updatedAt: tn.updatedAt,
        treatyNumber: tn.treatyNumber,
        isTreatyNumber: true
      }));
      
      setUserTreaties(treatyOptions);
    } catch (error) {
      console.error('Error fetching user treaties:', error);
      toast.error('Failed to load user treaties');
      setUserTreaties([]);
    }
  };

  const fetchTreatyTypes = async () => {
    try {
      const response = await fetch('/api/treaty-types');
      if (!response.ok) throw new Error('Failed to fetch treaty types');
      
      const data = await response.json();
      setAvailableTreatyTypes(data.treatyTypes || []);
    } catch (error) {
      console.error('Error fetching treaty types:', error);
      toast.error('Failed to load treaty types');
      setAvailableTreatyTypes([]);
    }
  };

  const handleAddTreaty = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (!newTreaty.selectedTreatyId || newTreaty.selectedTreatyTypeIds.length === 0) {
        toast.error('Please select a treaty and at least one treaty type');
        return;
      }

      // Find the selected treaty to get its treaty number
      const selectedTreaty = userTreaties.find(t => t.id === newTreaty.selectedTreatyId);
      if (!selectedTreaty) {
        toast.error('Selected treaty not found');
        return;
      }

      // Validate business details for each selected treaty type
      for (const treatyTypeId of newTreaty.selectedTreatyTypeIds) {
        const businessDetails = newTreaty.businessDetailsMap[treatyTypeId];
        if (!businessDetails) {
          toast.error(`Business details are required for treaty type ${treatyTypeId}`);
          return;
        }

        // Validate required business fields
        const requiredBusinessFields = ['businessName', 'businessAddress', 'businessEmail', 'categoryId', 'subcategoryId', 'businessType'];
        for (const field of requiredBusinessFields) {
          if (!businessDetails.businessDetails[field as keyof typeof businessDetails.businessDetails]) {
            toast.error(`${field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} is required for treaty type`);
            return;
          }
        }

        // Validate required personal fields
        const requiredPersonalFields = ['fullLegalName', 'email', 'currentCountryId', 'currentCityId'];
        for (const field of requiredPersonalFields) {
          if (!businessDetails.personalDetails[field as keyof typeof businessDetails.personalDetails]) {
            toast.error(`${field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} is required for treaty type`);
            return;
          }
        }

        if (!businessDetails.personalDetails.declarationAccepted) {
          toast.error('You must accept the declaration for each treaty type');
          return;
        }
      }

      // Calculate total cost
      const selectedTypes = availableTreatyTypes.filter(type => 
        newTreaty.selectedTreatyTypeIds.includes(type.id)
      );
      const totalCost = selectedTypes.reduce((sum, type) => sum + type.price, 0);

      // Create treaty type assignments
      const assignments = newTreaty.selectedTreatyTypeIds.map(typeId => {
        const type = availableTreatyTypes.find(t => t.id === typeId);
        return {
          treatyTypeId: typeId,
          price: type?.price || 0,
          signedDate: newTreaty.signedDate || null,
          expirationDate: newTreaty.expirationDate || null,
          description: newTreaty.description || null
        };
      });

      // First create treaty type assignments
      const response = await fetch(`/api/users/${userId}/treaties/${selectedTreaty.id}/types`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          assignments,
          totalCost
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add treaty types');
      }

      // Then create treaty type details for each selected treaty type
      for (const treatyTypeId of newTreaty.selectedTreatyTypeIds) {
        const businessDetails = newTreaty.businessDetailsMap[treatyTypeId];
        if (businessDetails) {
          try {
            const detailsResponse = await fetch('/api/treaty-type-details', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                treatyId: selectedTreaty.id,
                treatyTypeId: treatyTypeId,
                businessDetails: businessDetails.businessDetails,
                ...businessDetails.personalDetails,
                phoneNumbers: businessDetails.personalDetails.phoneNumbers || [],
                nationality: businessDetails.personalDetails.nationality || [],
              }),
            });

            if (!detailsResponse.ok) {
              const errorData = await detailsResponse.json();
              console.error(`Failed to create treaty type details for ${treatyTypeId}:`, errorData.error);
              // Don't throw here - continue with other treaty types but log the error
            }
          } catch (error) {
            console.error(`Error creating treaty type details for ${treatyTypeId}:`, error);
            // Continue with other treaty types
          }
        }
      }

      toast.success('Treaty types and business details added successfully');
      setShowAddForm(false);
      setNewTreaty({
        selectedTreatyId: '',
        selectedTreatyTypeIds: [],
        signedDate: '',
        expirationDate: '',
        description: '',
        businessDetailsMap: {}
      });
      
      // Refresh treaties list
      await fetchTreaties();
    } catch (error) {
      console.error('Error adding treaty types:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to add treaty types');
    }
  };

  const handleRemoveTreaty = async (treatyId: string) => {
    if (!confirm('Are you sure you want to remove this treaty?')) return;

    try {
      // Check if it's a treaty number or a regular treaty
      if (treatyId.startsWith('treaty-number-')) {
        // It's a treaty number, remove it via the treaty numbers endpoint
        const actualTreatyId = treatyId.replace('treaty-number-', '');
        const response = await fetch(`/api/users/${userId}/treaty-numbers/${actualTreatyId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error('Failed to remove treaty number');
        }
      } else {
        // It's a regular treaty, remove it via the treaties endpoint
        const response = await fetch(`/api/users/${userId}/treaties/${treatyId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error('Failed to remove treaty');
        }
      }

      toast.success('Treaty removed successfully');
      await fetchTreaties();
    } catch (error) {
      console.error('Error removing treaty:', error);
      toast.error('Failed to remove treaty');
    }
  };

  useEffect(() => {
    fetchTreaties();
    fetchUserTreaties();
    fetchTreatyTypes();
  }, [fetchTreaties]);

  // Filter treaties based on search and status
  const filteredTreaties = treaties.filter(treaty => {
    const matchesSearch = treaty.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         treaty.treatyTypes.some(type => type.name.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesStatus = statusFilter === 'all' || treaty.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Pagination
  const startIndex = (currentPage - 1) * 10;
  const endIndex = startIndex + 10;
  const paginatedTreaties = filteredTreaties.slice(startIndex, endIndex);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-lg font-medium text-slate-800 dark:text-slate-200">
            Treaties for {userName}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Manage user treaty assignments and track treaty status
          </p>
        </div>

        <button
          onClick={() => setShowAddForm(true)}
          className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200 flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Treaty Types
        </button>
      </div>

      <TreatyFilters
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
      />

      <AddTreatyForm
        showAddForm={showAddForm}
        setShowAddForm={setShowAddForm}
        userTreaties={userTreaties}
        availableTreatyTypes={availableTreatyTypes}
        newTreaty={newTreaty}
        setNewTreaty={setNewTreaty}
        handleAddTreaty={handleAddTreaty}
        userId={userId}
      />

      <TreatiesList
        treaties={paginatedTreaties}
        searchQuery={searchQuery}
        statusFilter={statusFilter}
        currentPage={currentPage}
        totalPages={Math.ceil(filteredTreaties.length / 10)}
        setCurrentPage={setCurrentPage}
        handleRemoveTreaty={handleRemoveTreaty}
      />
    </div>
  );
};