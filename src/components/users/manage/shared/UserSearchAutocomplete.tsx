'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Combobox, ComboboxInput, ComboboxOptions, ComboboxOption } from '@headlessui/react';

interface User {
  id: string;
  name: string;
  email: string;
  title?: string;
  isActive: boolean;
}

interface UserSearchAutocompleteProps {
  value: string;
  onChange: (userId: string) => void;
  onUserSelect?: (user: User) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  roleFilter?: string[];
}

export function UserSearchAutocomplete({
  value,
  onChange,
  onUserSelect,
  disabled = false,
  placeholder = 'Search users...',
  className = '',
  roleFilter = []
}: UserSearchAutocompleteProps) {
  const [query, setQuery] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Debounced search function
  const searchUsers = useCallback(async (searchQuery: string) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        search: searchQuery,
        limit: '20'
      });

      if (roleFilter.length > 0) {
        params.append('roles', roleFilter.join(','));
      }

      const response = await fetch(`/api/users/search?${params}`, {
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Handle response format
      const usersData = data.data || data.users || [];
      setUsers(Array.isArray(usersData) ? usersData : []);
    } catch (err) {
      if (err instanceof Error && err.name !== 'AbortError') {
        setError(err.message);
        console.error('Error searching users:', err);
      }
    } finally {
      setLoading(false);
    }
  }, [roleFilter]);

  // Effect for debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query.trim().length >= 2) {
        searchUsers(query);
      } else {
        setUsers([]);
      }
    }, 300); // 300ms debounce

    return () => {
      clearTimeout(timeoutId);
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [query, searchUsers]);

  // Get selected user
  const selectedUser = users.find(u => u.id === value);

  // Filter users based on query (client-side filtering)
  const filteredUsers = query.trim() === '' 
    ? users 
    : users.filter(user =>
        user.name.toLowerCase().includes(query.toLowerCase()) ||
        user.email.toLowerCase().includes(query.toLowerCase()) ||
        (user.title && user.title.toLowerCase().includes(query.toLowerCase()))
      );

  return (
    <div className={`relative ${className}`}>
      <Combobox value={value} onChange={onChange} disabled={disabled}>
        <div className="relative">
          <ComboboxInput
            displayValue={(userId: string) => {
              const user = users.find(u => u.id === userId);
              return user ? user.name : '';
            }}
            onChange={(event) => setQuery(event.target.value)}
            placeholder={disabled ? 'Search disabled' : placeholder}
            disabled={disabled}
            className={`
              w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
              rounded-md shadow-sm focus:outline-none focus:ring-slate-500 
              focus:border-slate-500 dark:bg-gray-700 dark:text-white
              disabled:opacity-50 disabled:cursor-not-allowed
              ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
            `}
          />
          
          {/* Loading indicator */}
          {loading && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <div className="animate-spin h-4 w-4 border-2 border-slate-500 border-t-transparent rounded-full"></div>
            </div>
          )}
        </div>

        <ComboboxOptions
          className={`
            absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 
            border border-gray-300 dark:border-gray-600 rounded-md 
            shadow-lg max-h-60 overflow-auto
            ${filteredUsers.length === 0 && !loading && !error ? 'hidden' : ''}
          `}
        >
          {error && (
            <div className="px-3 py-2 text-sm text-red-600 dark:text-red-400 border-b border-gray-200 dark:border-gray-600">
              Error: {error}
            </div>
          )}
          
          {loading && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              Searching...
            </div>
          )}
          
          {!loading && !error && filteredUsers.length === 0 && query.trim().length >= 2 && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              No users found
            </div>
          )}
          
          {!loading && !error && query.trim().length < 2 && query.trim().length > 0 && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              Type at least 2 characters to search
            </div>
          )}
          
          {filteredUsers.map((user) => (
            <ComboboxOption
              key={user.id}
              value={user.id}
              disabled={!user.isActive}
              className={({ active, disabled }) => `
                px-3 py-2 text-sm cursor-pointer transition-colors duration-200
                ${disabled 
                  ? 'opacity-50 cursor-not-allowed bg-gray-100 dark:bg-gray-600 text-gray-400 dark:text-gray-500'
                  : active 
                    ? 'bg-slate-100 dark:bg-slate-600 text-slate-900 dark:text-white' 
                    : 'text-gray-900 dark:text-gray-100'
                }
              `}
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="font-medium">{user.name}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {user.email}
                  </div>
                  {user.title && (
                    <div className="text-xs text-slate-600 dark:text-slate-400 mt-1">
                      {user.title}
                    </div>
                  )}
                  {!user.isActive && (
                    <div className="text-xs text-red-600 dark:text-red-400 mt-1">
                      Inactive
                    </div>
                  )}
                </div>
              </div>
            </ComboboxOption>
          ))}
        </ComboboxOptions>
      </Combobox>
      
      {/* Error message display */}
      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
    </div>
  );
}