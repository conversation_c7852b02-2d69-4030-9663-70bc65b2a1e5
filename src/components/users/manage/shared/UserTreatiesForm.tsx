'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { FileText, Plus, Building2, Upload, X, MapPin } from 'lucide-react';

export interface BusinessDetails {
  businessName: string;
  streetAddress1: string;
  streetAddress2?: string;
  town: string;
  city: string;
  country: string;
  postalCode: string;
  regionId?: string;
  regionText?: string;
  phone?: string;
  email?: string;
  website?: string;
  registrationNumber?: string;
  logoFile?: File;
  registrationFile?: File;
}

export interface TreatyTypeEntry {
  id: string;
  treatyId: string;
  treatyTypeId: string;
  businessDetails: BusinessDetails;
  createdAt?: string;
}

export interface UserTreatyData {
  treatyEntries: TreatyTypeEntry[];
}

interface Treaty {
  id: string;
  name: string;
  description?: string;
  status?: string;
}

interface TreatyType {
  id: string;
  name: string;
  description?: string;
  category?: string;
}

interface UserTreatiesFormProps {
  mode: 'create' | 'update';
  data: UserTreatyData;
  onChange: (data: UserTreatyData) => void;
  onSave: (data: UserTreatyData) => void;
  purchasedTreaties?: string[]; // Array of treaty IDs from identification
}

export function UserTreatiesForm({ mode, data, onChange, onSave, purchasedTreaties = [] }: UserTreatiesFormProps) {
  const [treaties, setTreaties] = useState<Treaty[]>([]);
  const [treatyTypes, setTreatyTypes] = useState<TreatyType[]>([]);
  const [selectedTreatyId, setSelectedTreatyId] = useState('');
  const [selectedTreatyTypeId, setSelectedTreatyTypeId] = useState('');
  const [showBusinessForm, setShowBusinessForm] = useState(false);
  const [businessDetails, setBusinessDetails] = useState<BusinessDetails>({
    businessName: '',
    streetAddress1: '',
    streetAddress2: '',
    town: '',
    city: '',
    country: '',
    postalCode: '',
    regionId: '',
    regionText: '',
    phone: '',
    email: '',
    website: '',
    registrationNumber: '',
  });

  
  // Initial fetch on mount and when purchasedTreaties changes
  useEffect(() => {
    // Skip if no purchased treaties or empty array
    if (!purchasedTreaties || purchasedTreaties.length === 0) {
      setTreaties([]);
      return;
    }

    // Prevent infinite loops with state comparison
    if (treaties.length > 0 && treatyTypes.length > 0) {
      return; // Data already loaded
    }

    const fetchTreatiesData = async () => {
      try {
        const response = await fetch('/api/admin/treaties');
        if (response.ok) {
          const result = await response.json();
          const allTreaties = Array.isArray(result) ? result : (result.treaties || []);
          // Filter to only show purchased treaties
          const purchased = allTreaties.filter((treaty: Treaty) => purchasedTreaties.includes(treaty.id));
          setTreaties(purchased);
        }
      } catch (error) {
        console.error('Error fetching treaties:', error);
        setTreaties([]);
      }
    };

    const fetchTreatyTypesData = async () => {
      try {
        const response = await fetch('/api/treaty-types');
        if (response.ok) {
          const result = await response.json();
          const types = Array.isArray(result) ? result : (result.treatyTypes || []);
          setTreatyTypes(types);
        }
      } catch (error) {
        console.error('Error fetching treaty types:', error);
        setTreatyTypes([]);
      }
    };

    fetchTreatiesData();
    fetchTreatyTypesData();
  }, [purchasedTreaties, treaties.length, treatyTypes.length]); // Add state length to prevent re-runs


  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(data);
  };

  const handleBusinessDetailsChange = (field: keyof BusinessDetails, value: string | File | undefined) => {
    setBusinessDetails(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTreatyEntry = () => {
    if (!selectedTreatyId || !selectedTreatyTypeId) return;

    const newEntry: TreatyTypeEntry = {
      id: `entry-${Date.now()}`,
      treatyId: selectedTreatyId,
      treatyTypeId: selectedTreatyTypeId,
      businessDetails: { ...businessDetails },
      createdAt: new Date().toISOString()
    };

    onChange({
      treatyEntries: [...(data.treatyEntries || []), newEntry]
    });

    // Reset form
    setSelectedTreatyId('');
    setSelectedTreatyTypeId('');
    setBusinessDetails({
      businessName: '',
      streetAddress1: '',
      streetAddress2: '',
      town: '',
      city: '',
      country: '',
      postalCode: '',
      regionId: '',
      regionText: '',
      phone: '',
      email: '',
      website: '',
      registrationNumber: '',
    });
    setShowBusinessForm(false);
  };

  const removeTreatyEntry = (entryId: string) => {
    onChange({
      treatyEntries: (data.treatyEntries || []).filter(entry => entry.id !== entryId)
    });
  };

  // Group entries by treaty
  const groupedEntries = (data.treatyEntries || []).reduce((acc, entry) => {
    if (!acc[entry.treatyId]) {
      acc[entry.treatyId] = [];
    }
    acc[entry.treatyId].push(entry);
    return acc;
  }, {} as Record<string, TreatyTypeEntry[]>);

  return (
    <form onSubmit={handleSubmit} className="space-y-6" data-testid="users-create-treaty-form">
      <div className="text-center py-6">
        <Building2 className="mx-auto h-12 w-12 text-green-500" />
        <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-white">Business Treaty Management</h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Configure your business details for each treaty and treaty type combination.
        </p>
      </div>

      {/* Treaty Selection Form */}
      {!showBusinessForm && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="treaty" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Select Treaty
              </label>
              <select
                id="treaty"
                value={selectedTreatyId}
                onChange={(e) => setSelectedTreatyId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                data-testid="users-create-treaty-select"
              >
                <option value="">Choose a treaty...</option>
                {Array.isArray(treaties) && treaties.map((treaty) => (
                  <option key={treaty.id} value={treaty.id}>
                    {treaty.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="treatyType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Select Treaty Type (Business)
              </label>
              <select
                id="treatyType"
                value={selectedTreatyTypeId}
                onChange={(e) => setSelectedTreatyTypeId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                disabled={!selectedTreatyId}
                data-testid="users-create-treaty-type-select"
              >
                <option value="">Choose a business type...</option>
                {Array.isArray(treatyTypes) && treatyTypes.map((treatyType) => (
                  <option key={treatyType.id} value={treatyType.id}>
                    {treatyType.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex justify-center">
            <button
              type="button"
              onClick={() => selectedTreatyId && selectedTreatyTypeId && setShowBusinessForm(true)}
              disabled={!selectedTreatyId || !selectedTreatyTypeId}
              className="inline-flex items-center px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              data-testid="users-create-treaty-add-business"
            >
              <Plus className="h-4 w-4 mr-2 text-green-400" />
              Add Business Details
            </button>
          </div>
        </div>
      )}

      {/* Business Details Form */}
      {showBusinessForm && (
        <div className="space-y-6 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">Business Details</h4>
            <button
              type="button"
              onClick={() => setShowBusinessForm(false)}
              className="text-gray-400 hover:text-gray-600"
              data-testid="users-create-treaty-close-business"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Business Name *
              </label>
              <input
                type="text"
                value={businessDetails.businessName}
                onChange={(e) => handleBusinessDetailsChange('businessName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                required
                data-testid="users-create-treaty-business-name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Registration Number
              </label>
              <input
                type="text"
                value={businessDetails.registrationNumber}
                onChange={(e) => handleBusinessDetailsChange('registrationNumber', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                data-testid="users-create-treaty-registration-number"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                <MapPin className="h-4 w-4 mr-2" />
                Street Address *
              </label>
              <input
                type="text"
                value={businessDetails.streetAddress1}
                onChange={(e) => handleBusinessDetailsChange('streetAddress1', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                required
                data-testid="users-create-treaty-address1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                City *
              </label>
              <input
                type="text"
                value={businessDetails.city}
                onChange={(e) => handleBusinessDetailsChange('city', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                required
                data-testid="users-create-treaty-city"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Country *
              </label>
              <input
                type="text"
                value={businessDetails.country}
                onChange={(e) => handleBusinessDetailsChange('country', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Business Phone
              </label>
              <input
                type="tel"
                value={businessDetails.phone}
                onChange={(e) => handleBusinessDetailsChange('phone', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Business Email
              </label>
              <input
                type="email"
                value={businessDetails.email}
                onChange={(e) => handleBusinessDetailsChange('email', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                <Upload className="h-4 w-4 mr-2" />
                Business Logo
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={(e) => handleBusinessDetailsChange('logoFile', e.target.files?.[0])}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                <FileText className="h-4 w-4 mr-2" />
                Registration Document (PDF)
              </label>
              <input
                type="file"
                accept=".pdf"
                onChange={(e) => handleBusinessDetailsChange('registrationFile', e.target.files?.[0])}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => setShowBusinessForm(false)}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </button>
            <button
              type="button"
              onClick={addTreatyEntry}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Business
            </button>
          </div>
        </div>
      )}

      {/* Grouped Treaty Display */}
      {(data.treatyEntries || []).length > 0 && (
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white">Your Business Entries</h4>
          {Object.entries(groupedEntries).map(([treatyId, entries]) => {
            const treaty = treaties.find(t => t.id === treatyId);
            return (
              <div key={treatyId} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h5 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-3">
                  {treaty?.name || 'Unknown Treaty'}
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {entries.map((entry) => {
                    const treatyType = treatyTypes.find(tt => tt.id === entry.treatyTypeId);
                    return (
                      <div key={entry.id} className="bg-slate-50 dark:bg-slate-800 rounded-lg p-3 relative">
                        <button
                          type="button"
                          onClick={() => removeTreatyEntry(entry.id)}
                          className="absolute top-2 right-2 text-red-500 hover:text-red-700"
                          data-testid={`users-create-treaty-remove-entry-${entry.id}`}
                        >
                          <X className="h-4 w-4" />
                        </button>
                        <div className="pr-6">
                          <h6 className="font-medium text-sm text-gray-900 dark:text-white">
                            {treatyType?.name || 'Unknown Type'}
                          </h6>
                          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            {entry.businessDetails.businessName}
                          </p>
                          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
                            <MapPin className="h-3 w-3 mr-1" />
                            {entry.businessDetails.city}, {entry.businessDetails.country}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      )}

      <div className="flex justify-end space-x-3">
        <button
          type="submit"
          className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
          data-testid="users-create-treaty-submit"
        >
          {mode === 'create' ? 'Save & Continue' : 'Update Treaties'}
        </button>
      </div>
    </form>
  );
}
