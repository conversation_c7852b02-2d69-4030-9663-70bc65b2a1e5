'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';

export interface AmbassadorialTitle {
  id: string;
  titleId: string;
  titleName: string;
  identificationNumber: string;
  isActive: boolean;
  issuedDate?: string;
  expiryDate?: string;
}

export interface IdentificationDocument {
  id: string;
  type?: string;
  number?: string;
  idNumber?: string;
  issuedDate?: string;
  expiryDate?: string;
  identificationType?: {
    id: string;
    name: string;
    category: string;
    description?: string;
  };
}

export interface TreatyNumber {
  id: string;
  treatyId: string;
  treatyNumber: string;
  treatyName?: string;
}

export interface TreatyTypeDetail {
  id: string;
  treatyId: string;
  treatyTypeId: string;
  treatyName: string;
  treatyTypeName: string;
  treatyTypeCategory: string;
  status: string;
  businessDetails?: {
    businessName: string;
    businessAddress: string;
    businessEmail: string;
    businessPhone?: string;
    businessWebsite?: string;
    businessDescription?: string;
    categoryId: string;
    subcategoryId: string;
    businessType: string;
  };
  fullLegalName: string;
  email: string;
  currentCountryId: string;
  currentCityId: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserIdentificationData {
  nationOrTribeId: string;
  nationOrTribeName: string;
  nationOrTribeOfficialName?: string;
  nationOrTribeType: 'nation' | 'tribe' | null;
  ambassadorialTitles: AmbassadorialTitle[];
  identifications: IdentificationDocument[];
  treatyNumbers: TreatyNumber[];
  treatyTypeDetails: TreatyTypeDetail[];
  peaceAmbassadorNumber: string;
  titleName?: string; // Add title name for dynamic field label
}

interface Treaty {
  id: string;
  name: string;
  description?: string;
  status?: string;
}

interface NationTreaty {
  id: string;
  name: string;
  officialName: string;
  status: string;
  type: 'NATION' | 'TRIBE';
}

interface Title {
  id: string;
  name: string;
  description?: string;
  isAmbassadorial: boolean;
}

interface TreatyType {
  id: string;
  name: string;
  description?: string;
  category: string;
  price?: number;
  isActive: boolean;
  status: string;
}

interface Category {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

interface Subcategory {
  id: string;
  name: string;
  description?: string;
  categoryId: string;
  isActive: boolean;
}

interface Country {
  id: string;
  name: string;
  code: string;
  isActive: boolean;
}

interface City {
  id: string;
  name: string;
  countryId: string;
  isActive: boolean;
}

interface UserIdentificationFormProps {
  mode: 'create' | 'update';
  data: UserIdentificationData;
  onChange: (data: UserIdentificationData) => void;
  onSave: (data: UserIdentificationData) => void;
  userId?: string;
}

export function UserIdentificationForm({ mode, data, onChange, onSave, userId }: UserIdentificationFormProps) {
  console.log('DEBUG: UserIdentificationForm received data:', {
    peaceAmbassadorNumber: data.peaceAmbassadorNumber,
    typeof: typeof data.peaceAmbassadorNumber,
    hasValue: !!data.peaceAmbassadorNumber,
    fullData: data
  });

  const [treaties, setTreaties] = useState<Treaty[]>([]);
  const [treatyTypes, setTreatyTypes] = useState<TreatyType[]>([]);
  const [filteredTreatyTypes, setFilteredTreatyTypes] = useState<TreatyType[]>([]);
  const [titles, setTitles] = useState<Title[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [countries, setCountries] = useState<Country[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [nationTribes, setNationTribes] = useState<NationTreaty[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedNationTribe, setSelectedNationTribe] = useState<NationTreaty | null>(null);
  
  const [newIdentification, setNewIdentification] = useState({ type: '', number: '', expiryDate: '' });
  const [newTreatyNumber, setNewTreatyNumber] = useState({ treatyId: '', treatyNumber: '' });
  const [newAmbassadorialTitle, setNewAmbassadorialTitle] = useState({ titleId: '', identificationNumber: '', issuedDate: '', expiryDate: '' });
  
  const [selectedTreaty, setSelectedTreaty] = useState<string>('');
  const [selectedTreatyType, setSelectedTreatyType] = useState<string>('');
  const [businessDetails, setBusinessDetails] = useState({
    businessName: '',
    businessAddress: '',
    businessEmail: '',
    businessPhone: '',
    businessWebsite: '',
    businessDescription: '',
    categoryId: '',
    subcategoryId: '',
    businessType: ''
  });
  const [personalDetails, setPersonalDetails] = useState({
    fullLegalName: '',
    email: '',
    currentCountryId: '',
    currentCityId: '',
    phoneNumbers: [] as string[],
    nationality: [] as string[],
    dateOfBirth: '',
    genderIdentity: '',
    residentialAddress: '',
    identificationNumber: '',
    peaceProtectedPremises: false,
    declarationAccepted: false
  });
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});

  useEffect(() => {
    fetchTreaties();
    fetchTreatyTypes();
    fetchTitles();
    fetchCategories();
    fetchCountries();
    fetchNationTribes();
  }, []);

  useEffect(() => {
    if (businessDetails.categoryId) {
      fetchSubcategories(businessDetails.categoryId);
    } else {
      setSubcategories([]);
    }
  }, [businessDetails.categoryId]);

  useEffect(() => {
    if (personalDetails.currentCountryId) {
      fetchCities(personalDetails.currentCountryId);
    } else {
      setCities([]);
    }
  }, [personalDetails.currentCountryId]);

  const fetchTreaties = async () => {
    try {
      const response = await fetch('/api/admin/treaties');
      if (response.ok) {
        const data = await response.json();
        // Handle both direct array and object with treaties property
        const treaties = Array.isArray(data) ? data : (data.treaties || []);
        setTreaties(treaties);
      }
    } catch (error) {
      console.error('Error fetching treaties:', error);
      setTreaties([]);
    }
  };

  const fetchTreatyTypes = async () => {
    try {
      const response = await fetch('/api/treaty-types');
      if (response.ok) {
        const data = await response.json();
        const treatyTypes = Array.isArray(data) ? data : (data.treatyTypes || []);
        setTreatyTypes(treatyTypes);
      }
    } catch (error) {
      console.error('Error fetching treaty types:', error);
      setTreatyTypes([]);
    }
  };

  const fetchTitles = async () => {
    try {
      const response = await fetch('/api/titles');
      if (response.ok) {
        const data = await response.json();
        // Filter for ambassadorial titles only
        const ambassadorialTitles = Array.isArray(data) 
          ? data.filter((title: any) => title.isAmbassadorial)
          : (data.titles || []).filter((title: any) => title.isAmbassadorial);
        setTitles(ambassadorialTitles);
      }
    } catch (error) {
      console.error('Error fetching titles:', error);
      setTitles([]);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      if (response.ok) {
        const data = await response.json();
        const categories = Array.isArray(data) ? data : (data.categories || []);
        setCategories(categories);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      setCategories([]);
    }
  };

  const fetchSubcategories = async (categoryId: string) => {
    try {
      const response = await fetch(`/api/subcategories?categoryId=${categoryId}`);
      if (response.ok) {
        const data = await response.json();
        const subcategories = Array.isArray(data) ? data : (data.subcategories || []);
        setSubcategories(subcategories);
      }
    } catch (error) {
      console.error('Error fetching subcategories:', error);
      setSubcategories([]);
    }
  };

  const fetchCountries = async () => {
    try {
      const response = await fetch('/api/countries');
      if (response.ok) {
        const data = await response.json();
        const countries = Array.isArray(data) ? data : (data.countries || []);
        setCountries(countries);
      }
    } catch (error) {
      console.error('Error fetching countries:', error);
      setCountries([]);
    }
  };

  const fetchCities = async (countryId: string) => {
    try {
      const response = await fetch(`/api/cities?countryId=${countryId}`);
      if (response.ok) {
        const data = await response.json();
        const cities = Array.isArray(data) ? data : (data.cities || []);
        setCities(cities);
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
      setCities([]);
    }
  };

  const fetchTreatyTypesForTreaty = async (treatyId: string) => {
    try {
      const response = await fetch(`/api/treaties/${treatyId}/types`);
      if (response.ok) {
        const data = await response.json();
        const types = Array.isArray(data) ? data : (data.treatyTypes || []);
        setFilteredTreatyTypes(types);
      }
    } catch (error) {
      console.error('Error fetching treaty types for treaty:', error);
      setFilteredTreatyTypes([]);
    }
  };

  const fetchNationTribes = async () => {
    try {
      const response = await fetch('/api/nation-treaties?status=ACTIVE');
      if (response.ok) {
        const result = await response.json();
        const allNationTribes = result.data || [];
        setNationTribes(allNationTribes);
      }
    } catch (error) {
      console.error('Error fetching nation/tribes:', error);
      setNationTribes([]);
    }
  };

  const handleChange = (field: keyof UserIdentificationData, value: any) => {
    console.log('DEBUG: UserIdentificationForm handleChange:', {
      field,
      value,
      currentValue: data[field],
      newValue: value
    });
    onChange({
      ...data,
      [field]: value,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(data);
  };

  const addIdentification = () => {
    const { type, number } = newIdentification;
    
    // Check if any field has value
    const hasAnyValue = type || number || newIdentification.expiryDate;
    
    if (!hasAnyValue) {
      toast.error('Please fill in at least one field before adding an identification');
      return;
    }
    
    if (!type || !number) {
      toast.error('Please select ID type and enter ID number before adding an identification');
      return;
    }
    
    const identification: IdentificationDocument = {
      id: `id-${Date.now()}`,
      type: newIdentification.type,
      number: newIdentification.number,
      issuedDate: '',
      expiryDate: newIdentification.expiryDate,
    };

    handleChange('identifications', [...data.identifications, identification]);
    setNewIdentification({ type: '', number: '', expiryDate: '' });
    toast.success('Identification added successfully');
  };

  const removeIdentification = (id: string) => {
    handleChange('identifications', data.identifications.filter(doc => doc.id !== id));
  };

  const addTreatyNumber = () => {
    const { treatyId, treatyNumber } = newTreatyNumber;
    
    // Check if any field has value
    const hasAnyValue = treatyId || treatyNumber;
    
    if (!hasAnyValue) {
      toast.error('Please fill in at least one field before adding a treaty');
      return;
    }
    
    if (!treatyId || !treatyNumber) {
      toast.error('Please select a treaty and enter treaty number before adding');
      return;
    }
    
    const selectedTreaty = treaties.find(t => t.id === newTreatyNumber.treatyId);

    const treatyEntry: TreatyNumber = {
      id: `treaty-${Date.now()}`,
      treatyId: newTreatyNumber.treatyId,
      treatyNumber: newTreatyNumber.treatyNumber,
      treatyName: selectedTreaty?.name,
    };

    handleChange('treatyNumbers', [...data.treatyNumbers, treatyEntry]);
    setNewTreatyNumber({ treatyId: '', treatyNumber: '' });
    toast.success('Treaty added successfully');
  };

  const removeTreatyNumber = (id: string) => {
    handleChange('treatyNumbers', data.treatyNumbers.filter(treaty => treaty.id !== id));
  };

  const addAmbassadorialTitle = () => {
    const { titleId, identificationNumber } = newAmbassadorialTitle;
    
    // Check if any field has value
    const hasAnyValue = titleId || identificationNumber || newAmbassadorialTitle.issuedDate || newAmbassadorialTitle.expiryDate;
    
    if (!hasAnyValue) {
      toast.error('Please fill in at least one field before adding an ambassadorial title');
      return;
    }
    
    if (!validateAmbassadorialTitle()) {
      toast.error('Please complete all required fields before adding an ambassadorial title');
      return;
    }
    
    const title = titles.find(t => t.id === newAmbassadorialTitle.titleId);
    if (!title) return;

    const ambassadorialTitle: AmbassadorialTitle = {
      id: `ambassadorial-${Date.now()}`,
      titleId: newAmbassadorialTitle.titleId,
      titleName: title.name,
      identificationNumber: newAmbassadorialTitle.identificationNumber,
      isActive: true,
      issuedDate: newAmbassadorialTitle.issuedDate || undefined,
      expiryDate: newAmbassadorialTitle.expiryDate || undefined,
    };

    handleChange('ambassadorialTitles', [...data.ambassadorialTitles, ambassadorialTitle]);
    setNewAmbassadorialTitle({ titleId: '', identificationNumber: '', issuedDate: '', expiryDate: '' });
    setValidationErrors({});
    toast.success('Ambassadorial title added successfully');
  };

  const removeAmbassadorialTitle = (id: string) => {
    handleChange('ambassadorialTitles', data.ambassadorialTitles.filter(title => title.id !== id));
  };

  const handleTreatySelection = async (treatyId: string) => {
    setSelectedTreaty(treatyId);
    setSelectedTreatyType('');
    if (treatyId) {
      await fetchTreatyTypesForTreaty(treatyId);
    } else {
      setFilteredTreatyTypes([]);
    }
  };

  const handleTreatyTypeSelection = (treatyTypeId: string) => {
    setSelectedTreatyType(treatyTypeId);
  };

  const handleBusinessDetailChange = (field: string, value: any) => {
    setBusinessDetails(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePersonalDetailChange = (field: string, value: any) => {
    setPersonalDetails(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNationTribeSelect = (nationTribe: NationTreaty) => {
    setSelectedNationTribe(nationTribe);
    setSearchTerm('');
  };

  const handleNationTribeAdd = () => {
    if (selectedNationTribe && selectedNationTribe.type) {
      handleChange('nationOrTribeId', selectedNationTribe.id);
      handleChange('nationOrTribeName', selectedNationTribe.name);
      handleChange('nationOrTribeType', selectedNationTribe.type.toLowerCase() as 'nation' | 'tribe');
      setSelectedNationTribe(null);
      toast.success('Nation or Tribe added successfully');
    }
  };

  const handleNationTribeRemove = () => {
    handleChange('nationOrTribeId', '');
    handleChange('nationOrTribeName', '');
    handleChange('nationOrTribeType', null);
    setSelectedNationTribe(null);
  };

  const addTreatyTypeDetail = async () => {
    if (!selectedTreaty || !selectedTreatyType) {
      toast.error('Please select both a treaty and treaty type');
      return;
    }

    // Validate required fields
    const requiredBusinessFields = ['businessName', 'businessAddress', 'businessEmail', 'categoryId', 'subcategoryId', 'businessType'];
    const requiredPersonalFields = ['fullLegalName', 'email', 'currentCountryId', 'currentCityId'];
    
    const businessErrors: {[key: string]: string} = {};
    const personalErrors: {[key: string]: string} = {};

    requiredBusinessFields.forEach(field => {
      if (!businessDetails[field as keyof typeof businessDetails]) {
        businessErrors[field] = `${field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} is required`;
      }
    });

    requiredPersonalFields.forEach(field => {
      if (!personalDetails[field as keyof typeof personalDetails]) {
        personalErrors[field] = `${field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} is required`;
      }
    });

    if (!personalDetails.declarationAccepted) {
      personalErrors.declarationAccepted = 'You must accept the declaration';
    }

    if (Object.keys(businessErrors).length > 0 || Object.keys(personalErrors).length > 0) {
      setValidationErrors({ ...businessErrors, ...personalErrors });
      return;
    }

    try {
      const response = await fetch('/api/treaty-type-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          treatyId: selectedTreaty,
          treatyTypeId: selectedTreatyType,
          businessDetails,
          ...personalDetails,
          phoneNumbers: personalDetails.phoneNumbers,
          nationality: personalDetails.nationality,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        const newTreatyTypeDetail: TreatyTypeDetail = result.treatyTypeDetails;
        
        handleChange('treatyTypeDetails', [...(data.treatyTypeDetails || []), newTreatyTypeDetail]);
        
        // Reset form
        setSelectedTreaty('');
        setSelectedTreatyType('');
        setBusinessDetails({
          businessName: '',
          businessAddress: '',
          businessEmail: '',
          businessPhone: '',
          businessWebsite: '',
          businessDescription: '',
          categoryId: '',
          subcategoryId: '',
          businessType: ''
        });
        setPersonalDetails({
          fullLegalName: '',
          email: '',
          currentCountryId: '',
          currentCityId: '',
          phoneNumbers: [],
          nationality: [],
          dateOfBirth: '',
          genderIdentity: '',
          residentialAddress: '',
          identificationNumber: '',
          peaceProtectedPremises: false,
          declarationAccepted: false
        });
        setFilteredTreatyTypes([]);
        setValidationErrors({});
        
        toast.success('Treaty type detail added successfully');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to add treaty type detail');
      }
    } catch (error) {
      console.error('Error adding treaty type detail:', error);
      toast.error('Failed to add treaty type detail');
    }
  };

  const removeTreatyTypeDetail = (id: string) => {
    handleChange('treatyTypeDetails', (data.treatyTypeDetails || []).filter(detail => detail.id !== id));
  };

  const filteredNationTribes = nationTribes.filter(nationTribe =>
    nationTribe.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    nationTribe.officialName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const validateAmbassadorialTitle = () => {
    const errors: {[key: string]: string} = {};
    const { titleId, identificationNumber, issuedDate, expiryDate } = newAmbassadorialTitle;
    
    // Check if any field has a value
    const hasAnyValue = titleId || identificationNumber || issuedDate || expiryDate;
    
    if (hasAnyValue) {
      if (!titleId) {
        errors.titleId = 'Ambassadorial title is required';
      }
      if (!identificationNumber) {
        errors.identificationNumber = 'Identification number is required';
      }
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6" data-testid="users-create-identification-form">
      <div className="space-y-6">
        {/* Nation or Tribe Section */}
        <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-4 pt-8 relative">
          <h3 className="absolute top-0 left-6 transform -translate-y-1/2 bg-white dark:bg-gray-900 px-3 text-lg font-medium text-gray-900 dark:text-white whitespace-nowrap">
            Nation or Tribe
          </h3>
          <div className="space-y-4">
            {/* Search Input and Add Button */}
            <div className="flex items-center gap-2">
              <div className="flex-1">
                <label htmlFor="nation-tribe-search" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Search Nation or Tribe
                </label>
                <input
                  id="nation-tribe-search"
                  type="text"
                  placeholder="Type to search nations or tribes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:text-gray-900 dark:text-white"
                  data-testid="users-create-identification-nation-search"
                />
              </div>
              <div className="pt-6">
                <button
                  type="button"
                  onClick={handleNationTribeAdd}
                  disabled={!selectedNationTribe}
                  className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                  data-testid="users-create-identification-nation-add"
                >
                  Add
                </button>
              </div>
            </div>
            
            {/* Selected Item Display */}
            {selectedNationTribe && (
              <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800 rounded-md">
                <div className="flex-1">
                  <div className="font-medium text-gray-900 dark:text-white">
                    {selectedNationTribe.name}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {selectedNationTribe.officialName} • {selectedNationTribe.type}
                  </div>
                </div>
                <button 
                  type="button"
                  onClick={() => setSelectedNationTribe(null)}
                  className="text-red-500 hover:text-red-700 text-sm ml-4"
                  data-testid="users-create-identification-nation-clear"
                >
                  Clear
                </button>
              </div>
            )}
            
            {/* Search Results */}
            {searchTerm && (
              <div className="max-h-60 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-lg">
                {filteredNationTribes.length === 0 ? (
                  <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                    No nations or tribes found
                  </div>
                ) : (
                  <div className="space-y-1">
                    {filteredNationTribes.map((nationTribe) => (
                      <button
                        key={nationTribe.id}
                        type="button"
                        onClick={() => handleNationTribeSelect(nationTribe)}
                        className={`w-full p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                          selectedNationTribe?.id === nationTribe.id
                            ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500'
                            : ''
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">
                              {nationTribe.name}
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">
                              {nationTribe.officialName}
                            </div>
                          </div>
                          <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                            nationTribe.status === 'ACTIVE'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                          }`}>
                            {nationTribe.type}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}
            
            {/* Display saved nation/tribe */}
            {data.nationOrTribeId && !selectedNationTribe && !searchTerm && (
              <div className="mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Current Assignment</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Official Name</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Type</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      <tr>
                        <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          {data.nationOrTribeName}
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                          {data.nationOrTribeOfficialName || '-'}
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                          {data.nationOrTribeType?.toUpperCase() || '-'}
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm">
                          <button
                            type="button"
                            onClick={handleNationTribeRemove}
                            className="text-red-500 hover:text-red-700"
                          >
                            Remove
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>

  
        {/* Ambassadorial Titles Section */}
        <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-4 pt-8 relative">
          <h3 className="absolute top-0 left-6 transform -translate-y-1/2 bg-white dark:bg-gray-900 px-3 text-lg font-medium text-gray-900 dark:text-white whitespace-nowrap">
            Ambassadorial Titles
          </h3>
          <div className="space-y-4">
            {/* Title and Identification Number Row */}
            <div className="flex items-center gap-2">
              <div className="w-[45%]">
                <label htmlFor="ambassadorial-title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Ambassadorial Title {validationErrors.titleId && <span className="text-red-500">*</span>}
                </label>
                <select 
                  id="ambassadorial-title"
                  value={newAmbassadorialTitle.titleId}
                  onChange={(e) => {
                    setNewAmbassadorialTitle(prev => ({ ...prev, titleId: e.target.value }));
                    // Clear validation error when field changes
                    if (validationErrors.titleId) {
                      setValidationErrors(prev => ({ ...prev, titleId: '' }));
                    }
                  }}
                  className={`w-full px-3 py-2 bg-white dark:bg-gray-800 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:text-gray-900 dark:text-white ${
                    validationErrors.titleId ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                >
                  <option value="">Select Ambassadorial Title</option>
                  {titles.map((title) => (
                    <option key={title.id} value={title.id}>
                      {title.name}
                    </option>
                  ))}
                </select>
                {validationErrors.titleId && (
                  <p className="mt-1 text-sm text-red-600">{validationErrors.titleId}</p>
                )}
              </div>
              <div className="flex-1">
                <label htmlFor="identification-number" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Identification Number {validationErrors.identificationNumber && <span className="text-red-500">*</span>}
                </label>
                <input
                  id="identification-number"
                  type="text"
                  placeholder="Enter identification number"
                  value={newAmbassadorialTitle.identificationNumber}
                  onChange={(e) => {
                    setNewAmbassadorialTitle(prev => ({ ...prev, identificationNumber: e.target.value }));
                    // Clear validation error when field changes
                    if (validationErrors.identificationNumber) {
                      setValidationErrors(prev => ({ ...prev, identificationNumber: '' }));
                    }
                  }}
                  className={`w-full px-3 py-2 bg-white dark:bg-gray-800 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:text-gray-900 dark:text-white ${
                    validationErrors.identificationNumber ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                />
                {validationErrors.identificationNumber && (
                  <p className="mt-1 text-sm text-red-600">{validationErrors.identificationNumber}</p>
                )}
              </div>
            </div>

            {/* Date Pickers and Add Button Row */}
            <div className="flex items-center gap-2">
              <div className="flex-1">
                <label htmlFor="date-issued" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Date Issued
                </label>
                <input
                  id="date-issued"
                  type="date"
                  value={newAmbassadorialTitle.issuedDate}
                  onChange={(e) => setNewAmbassadorialTitle(prev => ({ ...prev, issuedDate: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div className="flex-1">
                <label htmlFor="expiry-date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Expiry Date
                </label>
                <input
                  id="expiry-date"
                  type="date"
                  value={newAmbassadorialTitle.expiryDate}
                  onChange={(e) => setNewAmbassadorialTitle(prev => ({ ...prev, expiryDate: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div className="pt-6">
                <button
                  type="button"
                  onClick={addAmbassadorialTitle}
                  className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                >
                  Add
                </button>
              </div>
            </div>
            
            {/* Pre-populated Ambassadorial Titles Display */}
            {data.ambassadorialTitles.length > 0 && (
              <div className="mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Current Ambassadorial Titles</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Title</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Number</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date Issued</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date Expire</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      {data.ambassadorialTitles.map((title) => (
                        <tr key={title.id}>
                          <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{title.titleName}</td>
                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{title.identificationNumber}</td>
                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{title.issuedDate || '-'}</td>
                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{title.expiryDate || '-'}</td>
                          <td className="px-4 py-2 whitespace-nowrap text-sm">
                            <button 
                              type="button"
                              onClick={() => removeAmbassadorialTitle(title.id)}
                              className="text-red-500 hover:text-red-700"
                            >
                              Remove
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Identification Section */}
        <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-4 pt-8 relative">
          <h3 className="absolute top-0 left-6 transform -translate-y-1/2 bg-white dark:bg-gray-900 px-3 text-lg font-medium text-gray-900 dark:text-white whitespace-nowrap">
            Identification
          </h3>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <select 
                value={newIdentification.type}
                onChange={(e) => setNewIdentification(prev => ({ ...prev, type: e.target.value }))}
                className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:text-gray-900 dark:text-white"
              >
                <option value="">Select ID Type</option>
                <option value="drivers_license">Drivers License</option>
                <option value="passport">Passport</option>
                <option value="national_id">National ID</option>
              </select>
              <input
                type="text"
                placeholder="Enter ID number"
                value={newIdentification.number}
                onChange={(e) => setNewIdentification(prev => ({ ...prev, number: e.target.value }))}
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
              <input
                type="date"
                placeholder="Expiry Date"
                value={newIdentification.expiryDate}
                onChange={(e) => setNewIdentification(prev => ({ ...prev, expiryDate: e.target.value }))}
                className="w-40 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
              <button
                type="button"
                onClick={addIdentification}
                className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
              >
                Add
              </button>
            </div>
            
            {/* Pre-populated Government IDs Display */}
            {data.identifications.length > 0 && (
              <div className="mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Current Government IDs</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Type</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Number</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date Expire</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      {data.identifications.map((identification) => (
                        <tr key={identification.id}>
                          <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            {identification.identificationType?.name || identification.type?.replace('_', ' ').toUpperCase() || 'Unknown Type'}
                          </td>
                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                            {identification.idNumber || identification.number || '-'}
                          </td>
                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                            {identification.expiryDate || '-'}
                          </td>
                          <td className="px-4 py-2 whitespace-nowrap text-sm">
                            <button 
                              type="button"
                              onClick={() => removeIdentification(identification.id)}
                              className="text-red-500 hover:text-red-700"
                            >
                              Remove
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Treaty Type Section */}
        <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-4 pt-8 relative">
          <h3 className="absolute top-0 left-6 transform -translate-y-1/2 bg-white dark:bg-gray-900 px-3 text-lg font-medium text-gray-900 dark:text-white whitespace-nowrap">
            Treaty
          </h3>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <select 
                value={newTreatyNumber.treatyId}
                onChange={(e) => setNewTreatyNumber(prev => ({ ...prev, treatyId: e.target.value }))}
                className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:text-gray-900 dark:text-white"
              >
                <option value="">Select Treaty</option>
                {Array.isArray(treaties) && treaties.map((treaty) => (
                  <option key={treaty.id} value={treaty.id}>
                    {treaty.name}
                  </option>
                ))}
              </select>
              <input
                type="text"
                placeholder="Enter treaty number (e.g. PPT234234wef234)"
                value={newTreatyNumber.treatyNumber}
                onChange={(e) => setNewTreatyNumber(prev => ({ ...prev, treatyNumber: e.target.value }))}
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
              <button
                type="button"
                onClick={addTreatyNumber}
                className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
              >
                Add
              </button>
            </div>
            
            {/* Pre-populated Treaty Numbers Display */}
            {data.treatyNumbers.length > 0 && (
              <div className="mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Current Treaty Numbers</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Treaty</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Number</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      {data.treatyNumbers.map((treatyNum) => {
                        const treaty = treaties.find(t => t.id === treatyNum.treatyId);
                        const treatyDisplayName = treatyNum.treatyName || treaty?.name;
                        return (
                          <tr key={treatyNum.id}>
                            <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                              {treatyDisplayName || 'Unknown Treaty'}
                            </td>
                            <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                              {treatyNum.treatyNumber}
                            </td>
                            <td className="px-4 py-2 whitespace-nowrap text-sm">
                              <button 
                                type="button"
                                onClick={() => removeTreatyNumber(treatyNum.id)}
                                className="text-red-500 hover:text-red-700"
                              >
                                Remove
                              </button>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="submit"
          className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
          data-testid="users-create-identification-submit"
        >
          {mode === 'create' ? 'Save & Continue' : 'Update Identification'}
        </button>
      </div>
    </form>
  );
}
