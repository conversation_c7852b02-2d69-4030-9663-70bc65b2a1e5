'use client';

import React from 'react';
import { User, Phone } from 'lucide-react';
import { EmailInput } from './EmailInput';

export interface UserBasicInfoData {
  firstName: string;
  surname: string;
  dob: string;
  personalEmail: string;
  nwaEmail: string;
  phone: string;
  mobile: string;
  bio: string;
}

interface UserBasicInfoFormProps {
  mode: 'create' | 'update';
  data: UserBasicInfoData;
  onChange: (data: UserBasicInfoData) => void;
  onSave: (data: UserBasicInfoData) => void;
}

export function UserBasicInfoForm({ mode, data, onChange, onSave }: UserBasicInfoFormProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    onChange({
      ...data,
      [name]: value,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!data.firstName.trim() || !data.surname.trim()) {
      return;
    }
    
    onSave(data);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6" data-testid="users-create-basic-form">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="firstName" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
            First Name <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <User className="h-5 w-5 text-slate-400" />
            </div>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={data.firstName}
              onChange={handleChange}
              className="w-full pl-10 pr-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
              required
              data-testid="users-create-basic-first-name"
            />
          </div>
        </div>

        <div>
          <label htmlFor="surname" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
            Surname <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <User className="h-5 w-5 text-slate-400" />
            </div>
            <input
              type="text"
              id="surname"
              name="surname"
              value={data.surname}
              onChange={handleChange}
              className="w-full pl-10 pr-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
              required
              data-testid="users-create-basic-surname"
            />
          </div>
        </div>

        <div className="md:col-span-2">
          <label htmlFor="dob" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Date of Birth
          </label>
          <input
            type="date"
            id="dob"
            name="dob"
            value={data.dob}
            onChange={handleChange}
            className="w-full md:w-1/2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
            data-testid="users-create-basic-dob"
          />
        </div>

        <div>
          <EmailInput
            name="personalEmail"
            label="Personal Email"
            placeholder="Enter personal email address"
            required={false}
            defaultValue={data.personalEmail}
            onChange={(value) => onChange({ ...data, personalEmail: value })}
            showAutoComplete={false}
            showHelperText={false}
            dataTestId="users-create-basic-personal-email"
          />
        </div>

        <div>
          <EmailInput
            name="nwaEmail"
            label="NWA Email"
            placeholder="Enter NWA email address"
            required={false}
            defaultValue={data.nwaEmail}
            onChange={(value) => onChange({ ...data, nwaEmail: value })}
            showHelperText={false}
            dataTestId="users-create-basic-nwa-email"
          />
        </div>

        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            <Phone className="inline h-4 w-4 mr-1" />
            Phone
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            value={data.phone}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
            placeholder="+64 (21) 123-4567"
            data-testid="users-create-basic-phone"
          />
        </div>

        <div>
          <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            <Phone className="inline h-4 w-4 mr-1" />
            Mobile
          </label>
          <input
            type="tel"
            id="mobile"
            name="mobile"
            value={data.mobile}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
            placeholder="+64 (21) 987-6543"
            data-testid="users-create-basic-mobile"
          />
        </div>

        <div className="md:col-span-2">
          <label htmlFor="bio" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Bio
          </label>
          <textarea
            id="bio"
            name="bio"
            value={data.bio}
            onChange={handleChange}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
            data-testid="users-create-basic-bio"
          />
        </div>
      </div>

      <div className="flex justify-end">
        <button
          type="submit"
          className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
          data-testid="users-create-basic-submit"
        >
          {mode === 'create' ? 'Create User' : 'Update User'}
        </button>
      </div>
    </form>
  );
}
