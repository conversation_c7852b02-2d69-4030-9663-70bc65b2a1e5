'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { ContactDetailsForm } from './ContactDetailsForm';

export interface UserContactData {
  streetAddress1: string;
  streetAddress2: string;
  town: string;
  city: string;
  country: string;
  postalCode: string;
  regionId: string;
  regionText: string;
}

interface Country {
  id: string;
  name: string;
}

interface City {
  id: string;
  name: string;
}

interface Region {
  id: string;
  name: string;
}

interface AddressFormat {
  format: Array<{
    field: string;
    label: string;
    required: boolean;
  }>;
  example: string;
}

interface UserContactFormProps {
  mode: 'create' | 'update';
  data: UserContactData;
  onChange: (data: UserContactData) => void;
  onSave: (data: UserContactData) => void;
}

export function UserContactForm({ mode, data, onChange, onSave }: UserContactFormProps) {

  // Transform UserContactData to ContactFormData for ContactDetailsForm
  const transformData = (userData: UserContactData) => ({
    streetName: userData.streetAddress1 || '',
    aptSuiteUnit: userData.streetAddress2,
    townCity: userData.town,
    stateProvince: userData.regionText,
    postcodeZip: userData.postalCode,
    country: userData.country,
  });

  // Transform ContactFormData back to UserContactData
  const transformBackData = (contactData: any) => ({
    streetAddress1: contactData.streetName || '',
    streetAddress2: contactData.aptSuiteUnit || '',
    town: contactData.townCity || '',
    city: '', // ContactDetailsForm doesn't have a separate city field
    country: contactData.country || '',
    postalCode: contactData.postcodeZip || '',
    regionId: '',
    regionText: contactData.stateProvince || '',
  });

  return (
    <ContactDetailsForm
      mode={mode}
      initialData={transformData(data)}
      onSubmit={(contactData) => {
        onChange(transformBackData(contactData));
        onSave(transformBackData(contactData));
      }}
      onCountryChange={(countryName, countryId) => {
        // Handle country change if needed
        console.log('Country changed:', countryName, countryId);
      }}
      className=""
    />
  );
}
