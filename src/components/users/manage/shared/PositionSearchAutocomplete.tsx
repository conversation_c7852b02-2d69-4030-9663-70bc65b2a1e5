'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Combobox, ComboboxInput, ComboboxOptions, ComboboxOption } from '@headlessui/react';

interface Position {
  id: string;
  title: string;
  description?: string;
  level: number;
  parentId?: string | null;
  isActive: boolean;
  isAssigned?: boolean; // Track if position is already assigned to another user
  assignedTo?: string; // Name of user who has this position
}

interface PositionSearchAutocompleteProps {
  titleId: string;
  value: string;
  onChange: (positionId: string) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  currentUserId?: string; // For excluding current user from assignment check
  dataTestId?: string;
}

export function PositionSearchAutocomplete({
  titleId,
  value,
  onChange,
  disabled = false,
  placeholder = 'Search positions...',
  className = '',
  currentUserId,
  dataTestId,
}: PositionSearchAutocompleteProps) {
  const [query, setQuery] = useState('');
  const [positions, setPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Debounced search function
  const searchPositions = useCallback(async (searchQuery: string) => {
    if (!titleId) {
      setPositions([]);
      return;
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        titleId,
        search: searchQuery,
        limit: '20'
      });

      const response = await fetch(`/api/positions/positions?${params}`, {
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Handle both new and old response formats
      const positionsData = data.data || data.positions || [];
      
      // Check if positions are already assigned
      const positionsWithAssignment = await Promise.all(
        (Array.isArray(positionsData) ? positionsData : []).map(async (position) => {
          try {
            const assignmentResponse = await fetch(`/api/positions/positions/${position.id}/assignments`);
            if (assignmentResponse.ok) {
              const assignmentData = await assignmentResponse.json();
              return {
                ...position,
                isAssigned: assignmentData.count > 0 && (!currentUserId || assignmentData.assignedUserId !== currentUserId),
                assignedTo: assignmentData.count > 0 ? assignmentData.assignedUserName : undefined
              };
            }
          } catch (error) {
            console.error('Error checking position assignment:', error);
          }
          return position;
        })
      );
      
      setPositions(positionsWithAssignment);
    } catch (err) {
      if (err instanceof Error && err.name !== 'AbortError') {
        setError(err.message);
        console.error('Error searching positions:', err);
      }
    } finally {
      setLoading(false);
    }
  }, [titleId, currentUserId]);

  // Effect for debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query.trim().length >= 2) {
        searchPositions(query);
      } else {
        setPositions([]);
      }
    }, 300); // 300ms debounce

    return () => {
      clearTimeout(timeoutId);
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [query, searchPositions]);

  // Reset positions when titleId changes
  useEffect(() => {
    setQuery('');
    setPositions([]);
    setError(null);
  }, [titleId]);

  // Get selected position
  const selectedPosition = positions.find(p => p.id === value);

  // Filter positions based on query (client-side filtering)
  const filteredPositions = query.trim() === '' 
    ? positions 
    : positions.filter(position =>
        position.title.toLowerCase().includes(query.toLowerCase()) ||
        (position.description && position.description.toLowerCase().includes(query.toLowerCase()))
      );

  return (
    <div className={`relative ${className}`}>
      <Combobox value={value} onChange={onChange} disabled={disabled}>
        <div className="relative">
          <ComboboxInput
            displayValue={(positionId: string) => {
              const position = positions.find(p => p.id === positionId);
              return position ? position.title : '';
            }}
            onChange={(event) => setQuery(event.target.value)}
            placeholder={disabled ? 'Select an ambassadorial title first' : placeholder}
            disabled={disabled || !titleId}
            className={`
              w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
              rounded-md shadow-sm focus:outline-none focus:ring-slate-500 
              focus:border-slate-500 dark:bg-gray-700 dark:text-white
              disabled:opacity-50 disabled:cursor-not-allowed
              ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
            `}
            data-testid={dataTestId}
          />
          
          {/* Loading indicator */}
          {loading && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <div className="animate-spin h-4 w-4 border-2 border-slate-500 border-t-transparent rounded-full"></div>
            </div>
          )}
        </div>

        <ComboboxOptions
          className={`
            absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 
            border border-gray-300 dark:border-gray-600 rounded-md 
            shadow-lg max-h-60 overflow-auto
            ${filteredPositions.length === 0 && !loading && !error ? 'hidden' : ''}
          `}
        >
          {error && (
            <div className="px-3 py-2 text-sm text-red-600 dark:text-red-400 border-b border-gray-200 dark:border-gray-600">
              Error: {error}
            </div>
          )}
          
          {loading && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              Searching...
            </div>
          )}
          
          {!loading && !error && filteredPositions.length === 0 && query.trim().length >= 2 && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              No positions found
            </div>
          )}
          
          {!loading && !error && query.trim().length < 2 && query.trim().length > 0 && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              Type at least 2 characters to search
            </div>
          )}
          
          {filteredPositions.map((position) => (
            <ComboboxOption
              key={position.id}
              value={position.id}
              disabled={position.isAssigned && position.assignedTo !== currentUserId}
              className={({ active, disabled }) => `
                px-3 py-2 text-sm cursor-pointer transition-colors duration-200
                ${disabled 
                  ? 'opacity-50 cursor-not-allowed bg-gray-100 dark:bg-gray-600 text-gray-400 dark:text-gray-500'
                  : active 
                    ? 'bg-slate-100 dark:bg-slate-600 text-slate-900 dark:text-white' 
                    : 'text-gray-900 dark:text-gray-100'
                }
              `}
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="font-medium">{position.title}</div>
                  {position.description && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {position.description}
                    </div>
                  )}
                  {position.level > 1 && (
                    <div className="text-xs text-slate-600 dark:text-slate-400 mt-1">
                      Level {position.level}
                    </div>
                  )}
                </div>
                {position.isAssigned && position.assignedTo !== currentUserId && (
                  <div className="ml-2 flex-shrink-0">
                    <div className="text-xs text-red-600 dark:text-red-400 font-medium">
                      Assigned to {position.assignedTo || 'another user'}
                    </div>
                  </div>
                )}
                {position.isAssigned && position.assignedTo === currentUserId && (
                  <div className="ml-2 flex-shrink-0">
                    <div className="text-xs text-green-600 dark:text-green-400 font-medium">
                      Your position
                    </div>
                  </div>
                )}
              </div>
            </ComboboxOption>
          ))}
        </ComboboxOptions>
      </Combobox>
      
      {/* Error message display */}
      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
      
      {/* Helper text */}
      {!disabled && !titleId && (
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Select an ambassadorial title first
        </p>
      )}
    </div>
  );
}
