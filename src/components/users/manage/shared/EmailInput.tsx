'use client';

import React, { useState, useCallback, useRef } from 'react';
import { useController, Control } from 'react-hook-form';

interface EmailInputProps {
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  className?: string;
  disabled?: boolean;
  defaultValue?: string;
  onChange?: (value: string) => void;
  control?: Control<any>;
  showAutoComplete?: boolean;
  showHelperText?: boolean;
  dataTestId?: string;
}

const DOMAIN = '@newworldalliances.com';

// Separate component that safely handles useController
const ControlledEmailInput = ({ 
  name, 
  defaultValue, 
  control, 
  required, 
  onChange, 
  showAutoComplete, 
  showHelperText,
  label,
  placeholder,
  className,
  disabled,
  dataTestId,
}: Omit<EmailInputProps, 'control'> & { control: Control<any> }) => {
  const field = useController({
    name,
    defaultValue,
    control,
    rules: {
      required: required ? 'Email address is required' : false,
      pattern: {
        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
        message: 'Please enter a valid email address'
      }
    }
  });

  const [showDomainSuggestion, setShowDomainSuggestion] = useState(false);
  const [isAutoCompleted, setIsAutoCompleted] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const value = field.field.value || '';

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    field.field.onChange(newValue);
    setIsAutoCompleted(false);

    // Check if we should show domain suggestion (only if auto-complete is enabled)
    if (showAutoComplete && newValue && !newValue.includes('@') && newValue.length > 0) {
      setShowDomainSuggestion(true);
    } else {
      setShowDomainSuggestion(false);
    }

    // Call external onChange if provided
    if (onChange) {
      onChange(newValue);
    }
  }, [field.field, onChange, showAutoComplete]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (showAutoComplete && e.key === 'Tab' && showDomainSuggestion) {
      e.preventDefault();
      const completedValue = value + DOMAIN;
      field.field.onChange(completedValue);
      setIsAutoCompleted(true);
      setShowDomainSuggestion(false);

      // Call external onChange if provided
      if (onChange) {
        onChange(completedValue);
      }

      // Move cursor to the end
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.setSelectionRange(completedValue.length, completedValue.length);
        }
      }, 0);
    }
  }, [value, showDomainSuggestion, field.field, onChange, showAutoComplete]);

  const handleSuggestionClick = useCallback(() => {
    if (!showAutoComplete) return;
    const completedValue = value + DOMAIN;
    field.field.onChange(completedValue);
    setIsAutoCompleted(true);
    setShowDomainSuggestion(false);

    // Call external onChange if provided
    if (onChange) {
      onChange(completedValue);
    }

    // Focus and move cursor to the end
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.setSelectionRange(completedValue.length, completedValue.length);
      }
    }, 0);
  }, [value, field.field, onChange, showAutoComplete]);

  const handleBlur = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    field.field.onBlur();
    // Hide suggestion when losing focus, unless clicking the suggestion
    setTimeout(() => {
      setShowDomainSuggestion(false);
    }, 200);
  }, [field.field]);

  const suggestedEmail = value ? value + DOMAIN : '';

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      
      <div className="relative">
        <input
          ref={inputRef}
          type="email"
          id={name}
          name={name}
          value={value}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={`
            w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
            rounded-md shadow-sm focus:outline-none focus:ring-slate-500 
            focus:border-slate-500 dark:bg-gray-700 dark:text-white
            disabled:opacity-50 disabled:cursor-not-allowed
            ${field.fieldState.error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
          `}
          data-testid={dataTestId}
        />
        
        {/* Domain suggestion */}
        {showAutoComplete && showDomainSuggestion && (
          <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg">
            <div className="p-2">
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                Press Tab or click to complete with:
              </p>
              <button
                type="button"
                onClick={handleSuggestionClick}
                className="w-full text-left px-3 py-2 text-sm text-slate-700 dark:text-slate-300 
                         hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md 
                         transition-colors duration-200 font-medium"
              >
                {suggestedEmail}
              </button>
            </div>
          </div>
        )}
        
        {/* Auto-complete indicator */}
        {isAutoCompleted && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <div className="text-xs text-green-600 dark:text-green-400">
              ✓ Auto-completed
            </div>
          </div>
        )}
        
        {/* Error indicator */}
        {field.fieldState.error && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <div className="text-red-500">
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        )}
      </div>
      
      {/* Error message */}
      {field.fieldState.error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
          {field.fieldState.error.message}
        </p>
      )}
      
      {/* Helper text */}
      {showHelperText && !field.fieldState.error && !showDomainSuggestion && (
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {required ? 'Email address is required' : 'Email address is optional'}
        </p>
      )}
      
      {/* Auto-complete hint */}
      {showAutoComplete && !value && !field.fieldState.error && (
        <p className="mt-1 text-xs text-slate-500 dark:text-slate-400">
          Type local part and press Tab to auto-complete @{DOMAIN.replace('@', '')}
        </p>
      )}
    </div>
  );
};

// Separate component for uncontrolled version
const UncontrolledEmailInput = ({ 
  name, 
  label, 
  placeholder, 
  required, 
  className, 
  disabled, 
  defaultValue, 
  onChange, 
  showAutoComplete, 
  showHelperText,
  dataTestId,
}: Omit<EmailInputProps, 'control'>) => {
  const [showDomainSuggestion, setShowDomainSuggestion] = useState(false);
  const [isAutoCompleted, setIsAutoCompleted] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const [internalValue, setInternalValue] = useState(defaultValue || '');
  const [error, setError] = useState<string | null>(null);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInternalValue(newValue);
    
    // Validate email when not using form control
    if (required && !newValue.trim()) {
      setError('Email address is required');
    } else if (newValue && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(newValue)) {
      setError('Please enter a valid email address');
    } else {
      setError(null);
    }
    
    setIsAutoCompleted(false);

    // Check if we should show domain suggestion (only if auto-complete is enabled)
    if (showAutoComplete && newValue && !newValue.includes('@') && newValue.length > 0) {
      setShowDomainSuggestion(true);
    } else {
      setShowDomainSuggestion(false);
    }

    // Call external onChange if provided
    if (onChange) {
      onChange(newValue);
    }
  }, [onChange, required, showAutoComplete]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (showAutoComplete && e.key === 'Tab' && showDomainSuggestion) {
      e.preventDefault();
      const completedValue = internalValue + DOMAIN;
      setInternalValue(completedValue);
      setError(null);
      setIsAutoCompleted(true);
      setShowDomainSuggestion(false);

      // Call external onChange if provided
      if (onChange) {
        onChange(completedValue);
      }

      // Move cursor to the end
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.setSelectionRange(completedValue.length, completedValue.length);
        }
      }, 0);
    }
  }, [showAutoComplete, showDomainSuggestion, internalValue, onChange]);

  const handleSuggestionClick = useCallback(() => {
    if (!showAutoComplete) return;
    const completedValue = internalValue + DOMAIN;
    setInternalValue(completedValue);
    setError(null);
    setIsAutoCompleted(true);
    setShowDomainSuggestion(false);

    // Call external onChange if provided
    if (onChange) {
      onChange(completedValue);
    }

    // Focus and move cursor to the end
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.setSelectionRange(completedValue.length, completedValue.length);
      }
    }, 0);
  }, [internalValue, onChange, showAutoComplete]);

  const handleBlur = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    // Hide suggestion when losing focus, unless clicking the suggestion
    setTimeout(() => {
      setShowDomainSuggestion(false);
    }, 200);
  }, []);

  const suggestedEmail = internalValue ? internalValue + DOMAIN : '';

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      
      <div className="relative">
        <input
          ref={inputRef}
          type="email"
          id={name}
          name={name}
          value={internalValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={`
            w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
            rounded-md shadow-sm focus:outline-none focus:ring-slate-500 
            focus:border-slate-500 dark:bg-gray-700 dark:text-white
            disabled:opacity-50 disabled:cursor-not-allowed
            ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
          `}
          data-testid={dataTestId}
        />
        
        {/* Domain suggestion */}
        {showAutoComplete && showDomainSuggestion && (
          <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg">
            <div className="p-2">
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                Press Tab or click to complete with:
              </p>
              <button
                type="button"
                onClick={handleSuggestionClick}
                className="w-full text-left px-3 py-2 text-sm text-slate-700 dark:text-slate-300 
                         hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md 
                         transition-colors duration-200 font-medium"
              >
                {suggestedEmail}
              </button>
            </div>
          </div>
        )}
        
        {/* Auto-complete indicator */}
        {isAutoCompleted && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <div className="text-xs text-green-600 dark:text-green-400">
              ✓ Auto-completed
            </div>
          </div>
        )}
        
        {/* Error indicator */}
        {error && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <div className="text-red-500">
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        )}
      </div>
      
      {/* Error message */}
      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
      
      {/* Helper text */}
      {showHelperText && !error && !showDomainSuggestion && (
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {required ? 'Email address is required' : 'Email address is optional'}
        </p>
      )}
      
      {/* Auto-complete hint */}
      {showAutoComplete && !internalValue && !error && (
        <p className="mt-1 text-xs text-slate-500 dark:text-slate-400">
          Type local part and press Tab to auto-complete @{DOMAIN.replace('@', '')}
        </p>
      )}
    </div>
  );
};

export function EmailInput({
  name,
  label = 'Email Address',
  placeholder = 'Enter email address',
  required = false,
  className = '',
  disabled = false,
  defaultValue = '',
  onChange,
  control,
  showAutoComplete = true,
  showHelperText = true,
  dataTestId,
}: EmailInputProps) {
  // If control is provided, use the controlled version
  if (control) {
    return (
      <ControlledEmailInput
        name={name}
        label={label}
        placeholder={placeholder}
        required={required}
        className={className}
        disabled={disabled}
        defaultValue={defaultValue}
        onChange={onChange}
        control={control}
        showAutoComplete={showAutoComplete}
        showHelperText={showHelperText}
        dataTestId={dataTestId}
      />
    );
  }

  // Otherwise, use the uncontrolled version
  return (
    <UncontrolledEmailInput
      name={name}
      label={label}
      placeholder={placeholder}
      required={required}
      className={className}
      disabled={disabled}
      defaultValue={defaultValue}
      onChange={onChange}
      showAutoComplete={showAutoComplete}
      showHelperText={showHelperText}
      dataTestId={dataTestId}
    />
  );
}
