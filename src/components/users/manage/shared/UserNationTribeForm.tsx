'use client';

import React, { useState, useEffect } from 'react';
import { Search, Globe, Users, AlertTriangle } from 'lucide-react';

export interface UserNationTribeData {
  nationId: string;
  tribeId: string;
  nationRole: 'member' | 'envoy' | 'admin' | null;
  tribeRole: 'member' | 'envoy' | 'admin' | null;
}

interface NationTreaty {
  id: string;
  name: string;
  officialName: string;
  status: string;
  description?: string;
  contactEmail?: string;
  contactPhone?: string;
  contactAddress?: string;
  website?: string;
  notes?: string;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
  isDeleted: boolean;
  deletedAt?: string;
}

interface UserNationTribeFormProps {
  mode: 'create' | 'update';
  data: UserNationTribeData;
  onChange: (data: UserNationTribeData) => void;
  onSave: (data: UserNationTribeData) => void;
  currentUserId?: string;
}

export function UserNationTribeForm({ mode, data, onChange, onSave, currentUserId }: UserNationTribeFormProps) {
  const [nations, setNations] = useState<NationTreaty[]>([]);
  const [tribes, setTribes] = useState<NationTreaty[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchNationTerm, setSearchNationTerm] = useState('');
  const [searchTribeTerm, setSearchTribeTerm] = useState('');

  // Debug logging
  console.log('UserNationTribeForm rendered with data:', data);
  console.log('Mode:', mode, 'Current User ID:', currentUserId);

  useEffect(() => {
    console.log('UserNationTribeForm: Initializing component');
    fetchNations();
    fetchTribes();
  }, []);

  const fetchNations = async () => {
    try {
      setLoading(true);
      console.log('Fetching nations from API...');
      const response = await fetch('/api/nation-treaties?status=ACTIVE&limit=100');
      console.log('Nations API response status:', response.status);
      if (response.ok) {
        const result = await response.json();
        console.log('Nations API response:', result);
        console.log('Total treaties received:', result.data?.length || 0);
        console.log('Sample treaty data:', result.data?.[0] || 'No data');
        console.log('All treaty names:', result.data?.map((t: any) => `${t.name} (${t.officialName})`) || []);

        // Filter for nations based on name patterns - simplified logic
        const nationTreaties = result.data.filter((treaty: NationTreaty) => {
          const officialName = treaty.officialName?.toLowerCase() || '';
          const name = treaty.name?.toLowerCase() || '';
          const isTribe = officialName.includes('tribe') || name.includes('tribe');
          const isNation = !isTribe; // If it's not a tribe, treat it as a nation
          console.log(`Treaty "${treaty.name}" (${treaty.officialName}) - Is Tribe: ${isTribe}, Is Nation: ${isNation}`);
          return isNation;
        });
        console.log('Filtered nations:', nationTreaties.length);

        // If no nations found, show all treaties as fallback for debugging
        if (nationTreaties.length === 0) {
          console.log('No nations found, showing all treaties as fallback');
          setNations(result.data);
        } else {
          setNations(nationTreaties);
        }
      } else {
        console.error('Failed to fetch nations:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error fetching nations:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTribes = async () => {
    try {
      setLoading(true);
      console.log('Fetching tribes from API...');
      const response = await fetch('/api/nation-treaties?status=ACTIVE&limit=100');
      console.log('Tribes API response status:', response.status);
      if (response.ok) {
        const result = await response.json();
        console.log('Tribes API response:', result);
        console.log('Total treaties received:', result.data?.length || 0);
        console.log('Sample treaty data:', result.data?.[0] || 'No data');
        console.log('All treaty names:', result.data?.map((t: any) => `${t.name} (${t.officialName})`) || []);

        // Filter for tribes based on name patterns - simplified logic
        const tribeTreaties = result.data.filter((treaty: NationTreaty) => {
          const officialName = treaty.officialName?.toLowerCase() || '';
          const name = treaty.name?.toLowerCase() || '';
          const isTribe = officialName.includes('tribe') || name.includes('tribe');
          console.log(`Treaty "${treaty.name}" (${treaty.officialName}) - Is Tribe: ${isTribe}`);
          return isTribe;
        });
        console.log('Filtered tribes:', tribeTreaties.length);

        // If no tribes found, show all treaties as fallback for debugging
        if (tribeTreaties.length === 0) {
          console.log('No tribes found, showing all treaties as fallback');
          setTribes(result.data);
        } else {
          setTribes(tribeTreaties);
        }
      } else {
        console.error('Failed to fetch tribes:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error fetching tribes:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredNations = nations.filter(nation =>
    nation.name.toLowerCase().includes(searchNationTerm.toLowerCase()) ||
    nation.officialName.toLowerCase().includes(searchNationTerm.toLowerCase())
  );

  const filteredTribes = tribes.filter(tribe =>
    tribe.name.toLowerCase().includes(searchTribeTerm.toLowerCase()) ||
    tribe.officialName.toLowerCase().includes(searchTribeTerm.toLowerCase())
  );

  const handleNationChange = (nationId: string) => {
    onChange({
      ...data,
      nationId,
      nationRole: nationId ? 'member' : null
    });
  };

  const handleTribeChange = (tribeId: string) => {
    onChange({
      ...data,
      tribeId,
      tribeRole: tribeId ? 'member' : null
    });
  };

  const handleNationRoleChange = (role: 'member' | 'envoy' | 'admin' | null) => {
    onChange({
      ...data,
      nationRole: role
    });
  };

  const handleTribeRoleChange = (role: 'member' | 'envoy' | 'admin' | null) => {
    onChange({
      ...data,
      tribeRole: role
    });
  };

  const handleSave = () => {
    onSave(data);
  };

  const selectedNation = nations.find(nation => nation.id === data.nationId);
  const selectedTribe = tribes.find(tribe => tribe.id === data.tribeId);

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <Globe className="h-5 w-5 mr-2 text-blue-600" />
          Nation & Tribe Assignment
        </h3>

        {/* Warning about mutual exclusivity */}
        {(data.nationId && data.tribeId) && (
          <div className="mb-4 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 mr-3 flex-shrink-0" />
              <div className="text-sm text-amber-800 dark:text-amber-200">
                <p className="font-medium mb-1">Nation and Tribe Assignment</p>
                <p>A user can only be assigned to either a nation OR a tribe, not both. Please select only one.</p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Nation Selection */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Nation Assignment
              </label>
              
              {/* Search */}
              <div className="relative mb-3">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search nations..."
                  value={searchNationTerm}
                  onChange={(e) => setSearchNationTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              {/* Nation List */}
              <div className="max-h-60 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-lg">
                {loading ? (
                  <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                    Loading nations...
                  </div>
                ) : filteredNations.length === 0 ? (
                  <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                    No nations found
                  </div>
                ) : (
                  <div className="space-y-1">
                    {filteredNations.map((nation) => (
                      <button
                        key={nation.id}
                        onClick={() => handleNationChange(nation.id)}
                        className={`w-full p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                          data.nationId === nation.id
                            ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500'
                            : ''
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">
                              {nation.name}
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">
                              {nation.officialName}
                            </div>
                          </div>
                          <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                            nation.status === 'ACTIVE'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                          }`}>
                            {nation.status}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Nation Role Selection */}
              {data.nationId && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nation Role
                  </label>
                  <select
                    value={data.nationRole || ''}
                    onChange={(e) => handleNationRoleChange(e.target.value as any)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Select a role...</option>
                    <option value="member">Member</option>
                    <option value="envoy">Envoy</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>
              )}
            </div>
          </div>

          {/* Tribe Selection */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tribe Assignment
              </label>
              
              {/* Search */}
              <div className="relative mb-3">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search tribes..."
                  value={searchTribeTerm}
                  onChange={(e) => setSearchTribeTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              {/* Tribe List */}
              <div className="max-h-60 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-lg">
                {loading ? (
                  <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                    Loading tribes...
                  </div>
                ) : filteredTribes.length === 0 ? (
                  <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                    No tribes found
                  </div>
                ) : (
                  <div className="space-y-1">
                    {filteredTribes.map((tribe) => (
                      <button
                        key={tribe.id}
                        onClick={() => handleTribeChange(tribe.id)}
                        className={`w-full p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                          data.tribeId === tribe.id
                            ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500'
                            : ''
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">
                              {tribe.name}
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">
                              {tribe.officialName}
                            </div>
                          </div>
                          <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                            tribe.status === 'ACTIVE'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                          }`}>
                            {tribe.status}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Tribe Role Selection */}
              {data.tribeId && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tribe Role
                  </label>
                  <select
                    value={data.tribeRole || ''}
                    onChange={(e) => handleTribeRoleChange(e.target.value as any)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Select a role...</option>
                    <option value="member">Member</option>
                    <option value="envoy">Envoy</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Selected Summary */}
        {(selectedNation || selectedTribe) && (
          <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Selected Assignment:</h4>
            {selectedNation && (
              <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <Globe className="h-4 w-4" />
                <span>
                  Nation: {selectedNation.name} ({selectedNation.officialName})
                  {data.nationRole && ` - ${data.nationRole}`}
                </span>
              </div>
            )}
            {selectedTribe && (
              <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <Users className="h-4 w-4" />
                <span>
                  Tribe: {selectedTribe.name} ({selectedTribe.officialName})
                  {data.tribeRole && ` - ${data.tribeRole}`}
                </span>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={() => {
              onChange({
                nationId: '',
                tribeId: '',
                nationRole: null,
                tribeRole: null
              });
            }}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
          >
            Clear Selection
          </button>
          <button
            onClick={handleSave}
            disabled={loading || (!data.nationId && !data.tribeId) || (!!data.nationId && !!data.tribeId)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {mode === 'create' ? 'Continue to Treaties' : 'Save Assignment'}
          </button>
        </div>
      </div>
    </div>
  );
}