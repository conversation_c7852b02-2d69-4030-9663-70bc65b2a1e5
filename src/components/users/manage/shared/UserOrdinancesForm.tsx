'use client';

import React, { useState, useEffect } from 'react';
import { BookOpen } from 'lucide-react';

export interface UserOrdinanceData {
  selectedOrdinances: string[];
}

interface Ordinance {
  id: string;
  title: string;
  status: string;
  notes?: string;
  completedDate?: string;
  expirationDate?: string;
  ordinanceTypeName?: string;
  documents?: Array<{
    id: string;
    fileName: string;
    fileSize: number;
    fileType: string;
    uploadedAt: string;
  }>;
}

interface UserOrdinancesFormProps {
  mode: 'create' | 'update';
  data: UserOrdinanceData;
  onChange: (data: UserOrdinanceData) => void;
  onSave: (data: UserOrdinanceData) => void;
}

export function UserOrdinancesForm({ mode, data, onChange, onSave }: UserOrdinancesFormProps) {
  const [availableOrdinances, setAvailableOrdinances] = useState<Ordinance[]>([]);
  const [selectedOrdinanceId, setSelectedOrdinanceId] = useState('');

  useEffect(() => {
    fetchOrdinances();
  }, []);

  const fetchOrdinances = async () => {
    try {
      const response = await fetch('/api/ordinances');
      if (response.ok) {
        const result = await response.json();
        // Handle both direct array and object with ordinances property
        const ordinances = Array.isArray(result) ? result : (result.ordinances || []);
        setAvailableOrdinances(ordinances);
      }
    } catch (error) {
      console.error('Error fetching ordinances:', error);
      setAvailableOrdinances([]);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(data);
  };

  const addOrdinance = () => {
    if (selectedOrdinanceId && !data.selectedOrdinances.includes(selectedOrdinanceId)) {
      onChange({
        ...data,
        selectedOrdinances: [...data.selectedOrdinances, selectedOrdinanceId],
      });
      setSelectedOrdinanceId('');
    }
  };

  const removeOrdinance = (ordinanceId: string) => {
    onChange({
      ...data,
      selectedOrdinances: data.selectedOrdinances.filter(id => id !== ordinanceId),
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6" data-testid="users-create-ordinance-form">
      <div className="grid grid-cols-1 gap-6">
        {availableOrdinances.length === 0 ? (
          <div className="text-center py-8">
            <BookOpen className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No ordinances available</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              There are currently no ordinances in the system. You can create ordinances from the Ordinances page.
            </p>
          </div>
        ) : (
          <div>
            <label htmlFor="ordinanceSelect" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Select Ordinance
            </label>
            <div className="flex space-x-2">
              <select
                id="ordinanceSelect"
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 bg-white dark:bg-gray-800 dark:text-white"
                value={selectedOrdinanceId}
                onChange={(e) => setSelectedOrdinanceId(e.target.value)}
                data-testid="users-create-ordinance-select"
              >
                <option value="">Select an ordinance</option>
                {Array.isArray(availableOrdinances) && availableOrdinances.map((ordinance) => (
                  <option key={ordinance.id} value={ordinance.id}>
                    {ordinance.title}
                  </option>
                ))}
              </select>
              <button
                type="button"
                onClick={addOrdinance}
                disabled={!selectedOrdinanceId || data.selectedOrdinances.includes(selectedOrdinanceId)}
                className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200 disabled:opacity-50"
                data-testid="users-create-ordinance-add"
              >
                Add Ordinance
              </button>
            </div>
          </div>
        )}

        {/* Display selected ordinances */}
        <div>
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-2">Selected Ordinances</h4>
          <div className="border border-gray-300 dark:border-gray-600 rounded-md p-4 min-h-32">
            {data.selectedOrdinances.length > 0 ? (
              <ul className="space-y-2">
                {Array.isArray(data.selectedOrdinances) && data.selectedOrdinances.map((ordinanceId) => {
                  const ordinance = Array.isArray(availableOrdinances) ? availableOrdinances.find(o => o.id === ordinanceId) : null;
                  return ordinance ? (
                    <li key={ordinanceId} className="flex justify-between items-start bg-slate-100 dark:bg-slate-700 p-3 rounded">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {ordinance.title}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            ordinance.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                            ordinance.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                            ordinance.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {ordinance.status}
                          </span>
                        </div>
                        {ordinance.ordinanceTypeName && (
                          <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">
                            Type: {ordinance.ordinanceTypeName}
                          </p>
                        )}
                        {ordinance.notes && (
                          <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                            {ordinance.notes}
                          </p>
                        )}
                        {ordinance.documents && ordinance.documents.length > 0 && (
                          <div className="mt-2">
                            <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              Documents ({ordinance.documents.length}):
                            </p>
                            <ul className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                              {ordinance.documents.slice(0, 3).map((doc) => (
                                <li key={doc.id} className="flex items-center space-x-2">
                                  <span>• {doc.fileName}</span>
                                  <span className="text-gray-400">
                                    ({(doc.fileSize / 1024).toFixed(1)} KB)
                                  </span>
                                </li>
                              ))}
                              {ordinance.documents.length > 3 && (
                                <li className="text-gray-400">
                                  +{ordinance.documents.length - 3} more documents
                                </li>
                              )}
                            </ul>
                          </div>
                        )}
                      </div>
                      <button
                        type="button"
                        className="text-red-500 hover:text-red-700 text-sm ml-2 flex-shrink-0"
                        onClick={() => removeOrdinance(ordinanceId)}
                        data-testid={`users-create-ordinance-remove-${ordinanceId}`}
                      >
                        Remove
                      </button>
                    </li>
                  ) : null;
                })}
              </ul>
            ) : (
              <p className="text-gray-500 dark:text-gray-400 text-sm">No ordinances selected</p>
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="submit"
          className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
          data-testid="users-create-ordinance-submit"
        >
          {mode === 'create' ? 'Save Ordinances' : 'Update Ordinances'}
        </button>
      </div>
    </form>
  );
}
