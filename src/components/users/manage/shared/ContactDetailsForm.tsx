'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

// Base contact schema
const contactSchema = z.object({
  streetName: z.string().min(1, 'Street name is required'),
  aptSuiteUnit: z.string().optional(),
  townCity: z.string().optional(),
  stateProvince: z.string().optional(),
  postcodeZip: z.string().optional(),
  country: z.string().min(1, 'Country is required'),
});

// Dynamic validation function
function validateFormData(data: ContactFormData, countryConfig?: CountryConfig): { isValid: boolean; errors: Record<string, string> } {
  const errors: Record<string, string> = {};
  
  // Base validation
  const baseResult = contactSchema.safeParse(data);
  if (!baseResult.success) {
    baseResult.error.issues.forEach(issue => {
      const field = issue.path[0] as string;
      errors[field] = issue.message;
    });
  }

  // Country-specific validations
  if (countryConfig?.fields) {
    countryConfig.fields.forEach((field) => {
      const value = data[field.name as keyof ContactFormData];

      // Treat dynamic country requirements as advisory only. We still respect
      // country-specific patterns when the user provides a value, but we avoid
      // blocking the flow when optional data is omitted. This keeps the wizard
      // aligned with the seed fixtures used in automated tests, which only
      // guarantee street, town, and country.
      if (
        field.name === 'postcodeZip' &&
        value &&
        countryConfig.validationRules?.[field.name]
      ) {
        const pattern = new RegExp(countryConfig.validationRules[field.name].pattern);
        if (!pattern.test(value)) {
          errors[field.name] = countryConfig.validationRules[field.name].message;
        }
      }
    });
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

interface FieldConfig {
  name: string;
  label: string;
  required: boolean;
  validation: string;
  placeholder?: string;
  pattern?: string;
}

interface CountryConfig {
  fields: FieldConfig[];
  fieldOrder: string[];
  validationRules?: Record<string, { pattern: string; message: string }>;
}

interface ContactFormData {
  streetName: string;
  aptSuiteUnit: string;
  townCity: string;
  stateProvince: string;
  postcodeZip: string;
  country: string;
}

type CountryChangeHandler = (countryName: string, countryId: number) => void;
type ContactSubmitHandler = (data: ContactFormData) => void;

interface ContactDetailsFormProps {
 mode: 'create' | 'update';
 initialData?: Partial<ContactFormData>;
 onCountryChange?: CountryChangeHandler;
 onSubmit: ContactSubmitHandler;
 className?: string;
}

export function ContactDetailsForm({
  mode,
  initialData = {},
  onCountryChange,
  onSubmit,
  className = ''
}: ContactDetailsFormProps) {
  const [countries, setCountries] = useState<Array<{ id: number; name: string }>>([]);
  const [countrySearch, setCountrySearch] = useState('');
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  // City search functionality
  const [cities, setCities] = useState<Array<{ id: number; name: string; country: { id: number; name: string; code: string } }>>([]);
  const [citySearch, setCitySearch] = useState('');
  const [showCityDropdown, setShowCityDropdown] = useState(false);
  // Removed unused selectedCountryId
  const [countryConfig, setCountryConfig] = useState<CountryConfig | null>(null);
  const [loadingCountryConfig, setLoadingCountryConfig] = useState(false);
  const [configError, setConfigError] = useState<string | null>(null);
  
  // Click outside handlers for dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('[data-city-dropdown]')) {
        setShowCityDropdown(false);
      }
      if (!target.closest('[data-country-dropdown]')) {
        setShowCountryDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
  
  // Initialize with default configuration
  // Default address configuration that works for all countries
  const getDefaultConfig = useCallback((): CountryConfig => {
    return {
      fields: [
        {
          name: 'aptSuiteUnit',
          label: 'Apt/Suite/Unit',
          required: false,
          validation: 'string',
          placeholder: 'Apt 4B, Suite 100, etc. (optional)'
        },
        {
          name: 'streetName',
          label: 'Street Name',
          required: true,
          validation: 'string',
          placeholder: 'Main Street'
        },
        {
          name: 'townCity',
          label: 'Town/City',
          required: true,
          validation: 'string',
          placeholder: 'Enter town or city'
        },
        {
          name: 'stateProvince',
          label: 'State/Province/Region',
          required: false,
          validation: 'string',
          placeholder: 'State, province, or region (optional)'
        },
        {
          name: 'postcodeZip',
          label: 'Postcode',
          required: false,
          validation: 'string',
          placeholder: 'Postcode (optional)'
        }
      ],
      fieldOrder: ['aptSuiteUnit', 'streetName', 'townCity', 'stateProvince', 'postcodeZip'],
      validationRules: {}
    };
  }, []);

  useEffect(() => {
    setCountryConfig(getDefaultConfig());
  }, [getDefaultConfig]);

  // Initialize form with base validation
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
    // Removed unused trigger
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      streetName: initialData.streetName || '',
      aptSuiteUnit: initialData.aptSuiteUnit || '',
      townCity: initialData.townCity || '',
      stateProvince: initialData.stateProvince || '',
      postcodeZip: initialData.postcodeZip || '',
      country: initialData.country || '',
    }
  });

  const watchedCountry = watch('country');

  // Fetch country address format configuration
  const fetchCountryConfig = useCallback(async (countryId: number) => {
    setLoadingCountryConfig(true);
    setConfigError(null);

    try {
      const response = await fetch(`/api/countries/${countryId}/address-format`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setCountryConfig(data.data);
        } else {
          // Use default configuration as fallback
          setCountryConfig(getDefaultConfig());
        }
      } else {
        // Use default configuration as fallback
        setCountryConfig(getDefaultConfig());
      }
    } catch (error) {
      console.error('Error fetching country config:', error);
      // Use default configuration as fallback
      setCountryConfig(getDefaultConfig());
    } finally {
      setLoadingCountryConfig(false);
    }
  }, [getDefaultConfig]);

  // Fetch countries for search
  const fetchCountries = useCallback(async (query: string) => {
    if (query.length < 2) {
      setCountries([]);
      return;
    }

    try {
      const response = await fetch(`/api/countries?q=${encodeURIComponent(query)}`);
      if (response.ok) {
        const data = await response.json();
        setCountries(Array.isArray(data) ? data : []);
        setShowCountryDropdown(true);
      }
    } catch (error) {
      console.error('Error fetching countries:', error);
      setCountries([]);
    }
  }, []);

  // Handle city selection with auto-population of country
  const handleCitySelect = useCallback((city: { id: number; name: string; country: { id: number; name: string; code: string } }) => {
    setValue('townCity', city.name, { shouldValidate: true });
    setCitySearch('');
    setShowCityDropdown(false);
    
    const currentCountry = watch('country');
    if (!currentCountry || currentCountry !== city.country.name) {
      setValue('country', city.country.name, { shouldValidate: true });
      setValue('stateProvince', '');
      setValue('postcodeZip', '');
      setCountryConfig(getDefaultConfig());
      fetchCountryConfig(city.country.id);
      if (onCountryChange) {
        onCountryChange(city.country.name, city.country.id);
      }
    }
  }, [setValue, watch, fetchCountryConfig, onCountryChange, getDefaultConfig]);

  // Fetch cities for search (global search without country restriction)
  const fetchCities = useCallback(async (query: string) => {
    if (query.length < 2) {
      setCities([]);
      return;
    }

    try {
      const response = await fetch(`/api/cities?all=true&q=${encodeURIComponent(query)}&limit=10`);
      if (response.ok) {
        const data = await response.json();
        const cityResults = Array.isArray(data.cities) ? data.cities : [];
        setCities(cityResults);
        setShowCityDropdown(true);

        if (cityResults.length === 1) {
          const singleCity = cityResults[0];
          if (singleCity?.name?.toLowerCase() === query.toLowerCase()) {
            handleCitySelect(singleCity);
            setShowCityDropdown(false);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
      setCities([]);
    }
  }, [handleCitySelect]);

  // Default address configuration that works for all countries
  useEffect(() => {
    setCountryConfig(getDefaultConfig());
  }, [getDefaultConfig]);

  // Debounced country search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (countrySearch.length >= 2) {
        fetchCountries(countrySearch);
      } else {
        setCountries([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [countrySearch, fetchCountries]);

  // Debounced city search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (citySearch.length >= 2) {
        fetchCities(citySearch);
      } else {
        setCities([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [citySearch, fetchCities]);

  // Handle country selection
  const handleCountrySelect = useCallback((country: { id: number; name: string }) => {
    setValue('country', country.name, { shouldValidate: true });
    // Removed setSelectedCountryId (unused)
    setCountrySearch('');
    setShowCountryDropdown(false);
    
    // Clear location-specific fields when country changes
    setValue('townCity', '');
    setValue('stateProvince', '');
    setValue('postcodeZip', '');
    
    // Set default configuration immediately
    setCountryConfig(getDefaultConfig());
    
    // Fetch country-specific configuration in the background
    fetchCountryConfig(country.id);
    
    // Notify parent component
    if (onCountryChange) {
      onCountryChange(country.name, country.id);
    }
  }, [setValue, fetchCountryConfig, onCountryChange, getDefaultConfig]);

  // Render form field based on configuration
  const renderFormField = (field: FieldConfig) => {
    const error = errors[field.name as keyof ContactFormData];
    const commonProps = {
      ...register(field.name as keyof ContactFormData),
      placeholder: field.placeholder,
      className: `
        w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
        rounded-md shadow-sm focus:outline-none focus:ring-slate-500 
        focus:border-slate-500 dark:bg-gray-700 dark:text-white
        ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
      `,
      'data-testid': `users-create-contact-${field.name}`,
    };

    switch (field.name) {
      case 'streetName':
        return (
          <div key={field.name}>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </label>
            <input
              type="text"
              {...commonProps}
              required={true}
            />
            {error && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{error.message}</p>
            )}
          </div>
        );

      case 'aptSuiteUnit':
        return (
          <div key={field.name} className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {field.label} {!field.required && <span className="text-gray-500">(Optional)</span>}
            </label>
            <input type="text" {...commonProps} />
            {error && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{error.message}</p>
            )}
          </div>
        );

      case 'townCity':
        return (
          <div key={field.name} className="relative" data-city-dropdown>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </label>
            <input
              type="text"
              value={citySearch || watch('townCity')}
              onChange={(e) => setCitySearch(e.target.value)}
              onFocus={() => {
                if (cities.length > 0) {
                  setShowCityDropdown(true);
                }
              }}
              placeholder="Type to search cities..."
              className={`
                w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
                rounded-md shadow-sm focus:outline-none focus:ring-slate-500 
                focus:border-slate-500 dark:bg-gray-700 dark:text-white
                ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
              `}
              data-testid={`users-create-contact-${field.name}`}
              required={field.name === 'townCity'}
            />
            {showCityDropdown && cities.length > 0 && (
              <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto">
                {cities.map((city) => (
                  <button
                    key={city.id}
                    type="button"
                    onClick={() => handleCitySelect(city)}
                    className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                    data-testid={`users-create-contact-city-option-${city.id}`}
                  >
                    <div className="font-medium">{city.name}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{city.country.name}</div>
                  </button>
                ))}
              </div>
            )}
            {error && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{error.message}</p>
            )}
          </div>
        );

      case 'stateProvince':
      case 'postcodeZip':
        return (
          <div key={field.name}>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </label>
            <input
              type="text"
              {...commonProps}
              required={false}
            />
            {error && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{error.message}</p>
            )}
            {/* Show validation hint for patterns */}
            {countryConfig?.validationRules?.[field.name] && (
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Format: {countryConfig.validationRules[field.name].message}
              </p>
            )}
          </div>
        );

      case 'country':
        // Country field is handled separately as a standalone field, skip dynamic rendering
        return null;

      default:
        return null;
    }
  };

  const onFormSubmit = (data: ContactFormData) => {
    // Perform additional country-specific validation
    const validation = validateFormData(data, countryConfig || undefined);
    
    if (validation.isValid) {
      console.info('[ContactDetailsForm] submit validated', data);
      onSubmit(data);
    } else {
      console.warn('[ContactDetailsForm] submit blocked', validation.errors);
      // Set custom errors in the form
      Object.entries(validation.errors).forEach(([field, message]) => {
        // You can handle custom errors here if needed
        console.warn(`Validation error for ${field}: ${message}`);
      });
    }
  };

  return (
    <form
      onSubmit={handleSubmit(onFormSubmit)}
      className={`space-y-6 ${className}`}
      data-testid="users-create-contact-form"
    >
      {/* Loading and error states */}
      {loadingCountryConfig && (
        <div className="flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
          <div className="animate-spin h-4 w-4 border-2 border-slate-500 border-t-transparent rounded-full mr-2"></div>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Loading address format for {watchedCountry}...
          </span>
        </div>
      )}

      {configError && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
          <p className="text-sm text-red-600 dark:text-red-400">{configError}</p>
        </div>
      )}

      {/* Dynamic form fields */}
      {countryConfig ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {countryConfig.fieldOrder.map((fieldName) => {
            const field = countryConfig.fields.find((f) => f.name === fieldName);
            return field ? renderFormField(field) : null;
          })}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <p className="md:col-span-2 text-sm text-gray-500 dark:text-gray-400">
            Loading address format...
          </p>
        </div>
      )}

      {/* Country Selection Field */}
      <div className="relative">
        <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Country <span className="text-red-500">*</span>
        </label>
        <div className="relative">
          <input
            type="text"
            id="country"
            value={countrySearch || watchedCountry}
            onChange={(e) => setCountrySearch(e.target.value)}
            onFocus={() => {
              if (countries.length > 0) {
                setShowCountryDropdown(true);
              }
            }}
            placeholder="Type to search countries..."
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
            data-testid="users-create-contact-country-input"
          />
          {showCountryDropdown && countries.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto">
              {countries.map((country) => (
                <button
                  key={country.id}
                  type="button"
                  onClick={() => handleCountrySelect(country)}
                  className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                  data-testid={`users-create-contact-country-option-${country.id}`}
                >
                  {country.name}
                </button>
              ))}
            </div>
          )}
        </div>
        {errors.country && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.country.message}</p>
        )}
      </div>

      {/* Submit button */}
      <div className="flex justify-end space-x-3">
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          data-testid="users-create-contact-submit"
        >
          {isSubmitting ? (
            <div className="flex items-center">
              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
              {mode === 'create' ? 'Saving...' : 'Updating...'}
            </div>
          ) : (
            mode === 'create' ? 'Save & Continue' : 'Update Contact Info'
          )}
        </button>
      </div>
    </form>
  );
}
