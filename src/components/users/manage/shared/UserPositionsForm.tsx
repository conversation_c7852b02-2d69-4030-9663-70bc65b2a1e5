'use client';

import React, { useState, useEffect } from 'react';
import { PositionSearchAutocomplete } from './PositionSearchAutocomplete';

export interface UserPositionData {
  titleId: string;
  positionId: string;
}

interface Title {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
}

interface Position {
  id: string;
  title: string;
  description: string;
  level: number;
  parentId: string | null;
  isActive: boolean;
}

interface UserPositionsFormProps {
  mode: 'create' | 'update';
  data: UserPositionData;
  onChange: (data: UserPositionData) => void;
  onSave: (data: UserPositionData) => void;
  currentUserId?: string;
}

export function UserPositionsForm({ mode, data, onChange, onSave, currentUserId }: UserPositionsFormProps) {
  const [titles, setTitles] = useState<Title[]>([]);
  const [displayPositions, setDisplayPositions] = useState<Position[]>([]);

  useEffect(() => {
    fetchTitles();
  }, []);

  useEffect(() => {
    if (data.titleId) {
      fetchPositions(data.titleId);
    } else {
      setDisplayPositions([]);
    }
  }, [data.titleId]);

  const fetchTitles = async () => {
    try {
      const response = await fetch('/api/positions/titles');
      if (response.ok) {
        const data = await response.json();
        // Handle both direct array and object with titles property
        const titles = Array.isArray(data) ? data : (data.titles || []);
        setTitles(titles);
      }
    } catch (error) {
      console.error('Error fetching titles:', error);
      setTitles([]);
    }
  };

  const fetchPositions = async (titleId: string) => {
    try {
      const response = await fetch(`/api/positions/positions?titleId=${titleId}`);
      if (response.ok) {
        const data = await response.json();
        // Handle both direct array and object with positions property
        const positions = Array.isArray(data) ? data : (data.positions || []);
        setDisplayPositions(positions);
      }
    } catch (error) {
      console.error('Error fetching positions:', error);
      setDisplayPositions([]);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    onChange({
      ...data,
      [name]: value,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(data);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6" data-testid="users-create-positions-form">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="titleId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Ambassadorial Title
          </label>
          <select
            id="titleId"
            name="titleId"
            value={data.titleId}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 bg-white dark:bg-gray-800 dark:text-white"
            data-testid="users-create-positions-title"
          >
            <option value="">Select Ambassadorial Title</option>
            {Array.isArray(titles) && titles
              .filter(title => title.isActive)
              .map(title => (
                <option key={title.id} value={title.id}>
                  {title.name}
                </option>
              ))}
          </select>
        </div>

        <div>
          <label htmlFor="positionId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Position
          </label>
          <PositionSearchAutocomplete
            titleId={data.titleId}
            value={data.positionId}
            onChange={(value) => onChange({ ...data, positionId: value })}
            placeholder="Search positions..."
            disabled={!data.titleId}
            currentUserId={currentUserId}
            dataTestId="users-create-positions-search"
          />
          {data.titleId && Array.isArray(displayPositions) && displayPositions.length === 0 && (
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              No positions are associated with the selected ambassadorial title.
            </p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="submit"
          className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
          data-testid="users-create-positions-submit"
        >
          {mode === 'create' ? 'Save & Continue' : 'Update Position'}
        </button>
      </div>
    </form>
  );
}
