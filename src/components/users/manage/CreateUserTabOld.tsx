'use client';

import React, { useState, useRef, useEffect } from 'react';
import { toast } from 'sonner';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  FileText,
  BookOpen,
  IdCard,
  Briefcase,
  Crown
} from 'lucide-react';
import { EnhancedTreatyCreation } from './EnhancedTreatyCreation';

interface Title {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
}

interface Position {
  id: string;
  title: string;
  description: string | null;
  level: number;
  parentId: string | null;
  isActive: boolean;
}

export const CreateUserTab: React.FC = () => {
  const [isBulkUpload, setIsBulkUpload] = useState(false);
  const [activeCreateUserTab, setActiveCreateUserTab] = useState<'user' | 'contact' | 'identification' | 'positions' | 'treaty' | 'ordinance'>('user');
  const [singleUser, setSingleUser] = useState({
    firstName: '',
    surname: '',
    dob: '',
    email: '',
    personalEmail: '',
    nwaEmail: '',
    phone: '',
    mobile: '',
    streetAddress1: '',
    streetAddress2: '',
    town: '',
    city: '',
    country: '',
    postalCode: '',
    regionId: '',
    regionText: '',
    peaceAmbassadorNumber: '',
    bio: '',
    titleId: '',
    positionId: '',
  });

  // New state for multiple treaty numbers
  const [treatyNumbers, setTreatyNumbers] = useState<Array<{
    id: string;
    treatyTypeId: string;
    treatyNumber: string;
  }>>([]);

  // Autocomplete state
  const [countries, setCountries] = useState<Array<{ id: string, name: string }>>([]);
  const [cities, setCities] = useState<Array<{ id: string, name: string }>>([]);
  const [regions, setRegions] = useState<Array<{ id: string, name: string, code: string }>>([]);
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [showCityDropdown, setShowCityDropdown] = useState(false);
  const [showRegionDropdown, setShowRegionDropdown] = useState(false);
  const [countrySearch, setCountrySearch] = useState('');
  const [citySearch, setCitySearch] = useState('');
  
  // Address formatting state
  const [addressFormat, setAddressFormat] = useState<any>(null);
  const [selectedCountryId, setSelectedCountryId] = useState<string>('');

  // Ordinance state
  const [selectedOrdinances, setSelectedOrdinances] = useState<string[]>([]);
  const [availableOrdinances, setAvailableOrdinances] = useState<Array<{ id: string, name: string, description: string }>>([]);
  
  // Treaty state
  const [selectedTreaties, setSelectedTreaties] = useState<string[]>([]);
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [csvFileName, setCsvFileName] = useState('');
  const [bulkDataPreview, setBulkDataPreview] = useState<any[]>([]);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [adminConfirmation, setAdminConfirmation] = useState('');
  const [titles, setTitles] = useState<Title[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [filteredPositions, setFilteredPositions] = useState<Position[]>([]);
  const [treatyTypes, setTreatyTypes] = useState<Array<{ id: string, name: string }>>([]);
  const [loading, setLoading] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch titles, positions, and ordinances on component mount
  useEffect(() => {
    Promise.all([
      fetchTitles(),
      fetchPositions(),
      fetchOrdinances(),
      fetchTreatyTypes()
    ]).finally(() => {
      setLoading(false);
    });
  }, []);

  // Fetch countries when country search changes
  useEffect(() => {
    if (countrySearch.length >= 2) {
      const fetchCountries = async () => {
        try {
          const response = await fetch(`/api/countries?q=${encodeURIComponent(countrySearch)}`);
          if (response.ok) {
            const data = await response.json();
            setCountries(data);
            setShowCountryDropdown(true);
          }
        } catch (error) {
          console.error('Error fetching countries:', error);
        }
      };

      const timeoutId = setTimeout(fetchCountries, 300); // Debounce
      return () => clearTimeout(timeoutId);
    } else {
      setShowCountryDropdown(false);
    }
  }, [countrySearch]);

  // Fetch cities when city search changes and country is selected
  useEffect(() => {
    if (citySearch.length >= 2 && singleUser.country) {
      const fetchCities = async () => {
        try {
          // Find the country ID from the selected country name
          // In a real implementation, you would store the country ID in the state
          const response = await fetch(`/api/cities?q=${encodeURIComponent(citySearch)}&countryId=${encodeURIComponent(singleUser.country)}`);
          if (response.ok) {
            const data = await response.json();
            setCities(data);
            setShowCityDropdown(true);
          }
        } catch (error) {
          console.error('Error fetching cities:', error);
        }
      };

      const timeoutId = setTimeout(fetchCities, 300); // Debounce
      return () => clearTimeout(timeoutId);
    } else {
      setShowCityDropdown(false);
    }
  }, [citySearch, singleUser.country]);

  // Fetch regions when country is selected
  const fetchRegions = async (countryId: string) => {
    try {
      const response = await fetch(`/api/regions?countryId=${encodeURIComponent(countryId)}`);
      if (response.ok) {
        const data = await response.json();
        setRegions(data);
      } else {
        setRegions([]);
      }
    } catch (error) {
      console.error('Error fetching regions:', error);
      setRegions([]);
    }
  };

  // Fetch address format for a country
  const fetchAddressFormat = async (countryId: string) => {
    try {
      const response = await fetch(`/api/countries/${countryId}/address-format`);
      if (response.ok) {
        const data = await response.json();
        setAddressFormat(data);
      } else {
        setAddressFormat(null);
      }
    } catch (error) {
      console.error('Error fetching address format:', error);
      setAddressFormat(null);
    }
  };

  const fetchTitles = async () => {
    try {
      const response = await fetch('/api/positions/titles');

      if (!response.ok) {
        if (response.status === 403) {
          // User doesn't have admin privileges, show appropriate message
          toast.info('Title selection is only available to administrators');
          return;
        }
        throw new Error('Failed to fetch titles');
      }

      const data = await response.json();
      setTitles(data);
    } catch (error) {
      console.error('Error fetching titles:', error);
      toast.error('Failed to load titles');
    }
  };

  const fetchPositions = async () => {
    try {
      const response = await fetch('/api/positions/positions');

      if (!response.ok) {
        if (response.status === 403) {
          // User doesn't have admin privileges, show appropriate message
          toast.info('Position selection is only available to administrators');
          return;
        }
        throw new Error('Failed to fetch positions');
      }

      const data = await response.json();
      // The API returns { positions: [...], pagination: {...} }
      // Extract the positions array from the response
      const positionsArray = data.positions || [];
      setPositions(positionsArray);
    } catch (error) {
      console.error('Error fetching positions:', error);
      toast.error('Failed to load positions');
    }
  };



  const fetchOrdinances = async () => {
    try {
      const response = await fetch('/api/ordinance-types');

      if (!response.ok) {
        if (response.status === 404) {
          // No ordinance types found
          setAvailableOrdinances([]);
          toast.info('No ordinance types are currently available');
          return;
        }
        throw new Error('Failed to fetch ordinance types');
      }

      const data = await response.json();
      setAvailableOrdinances(data.ordinanceTypes || []);
    } catch (error) {
      console.error('Error fetching ordinances:', error);
      setAvailableOrdinances([]);
      toast.error('Failed to load ordinance types');
    }
  };

  const fetchTreatyTypes = async () => {
    try {
      const response = await fetch('/api/treaty-types');

      if (!response.ok) {
        if (response.status === 404) {
          // No treaty types found
          setTreatyTypes([]);
          return;
        }
        throw new Error('Failed to fetch treaty types');
      }

      const data = await response.json();
      setTreatyTypes(data.treatyTypes || []);
    } catch (error) {
      console.error('Error fetching treaty types:', error);
      setTreatyTypes([]);
      toast.error('Failed to load treaty types');
    }
  };

  const handleTreatyToggle = (treatyId: string) => {
    setSelectedTreaties(prev =>
      prev.includes(treatyId)
        ? prev.filter(id => id !== treatyId)
        : [...prev, treatyId]
    );
  };

  const handleOrdinanceToggle = (ordinanceId: string) => {
    setSelectedOrdinances(prev =>
      prev.includes(ordinanceId)
        ? prev.filter(id => id !== ordinanceId)
        : [...prev, ordinanceId]
    );
  };

  // Fetch positions associated with a specific title
  const fetchPositionsForTitle = async (titleId: string) => {
    try {
      const response = await fetch(`/api/positions/positions?titleId=${titleId}`);

      if (!response.ok) {
        if (response.status === 403) {
          // User doesn't have admin privileges, show appropriate message
          toast.info('Position selection is only available to administrators');
          return;
        }
        throw new Error('Failed to fetch positions for title');
      }

      const data = await response.json();
      // The API returns { positions: [...], pagination: {...} }
      // Extract the positions array from the response
      const positionsArray = data.positions || [];
      setFilteredPositions(positionsArray);
      // Reset position selection when title changes
      setSingleUser(prev => ({ ...prev, positionId: '' }));
    } catch (error) {
      console.error('Error fetching positions for title:', error);
      toast.error('Failed to load positions for selected title');
    }
  };

  const handleSingleUserChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // If changing the title, fetch associated positions
    if (name === 'titleId') {
      setSingleUser(prev => ({ ...prev, [name]: value, positionId: '' }));
      if (value) {
        fetchPositionsForTitle(value);
      } else {
        setFilteredPositions([]);
      }
    } else {
      setSingleUser(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleCsvFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Check file type
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        toast.error('Invalid file type. Please upload a CSV file.');
        return;
      }

      // Check file size (10MB limit)
      if (selectedFile.size > 10 * 1024 * 1024) {
        toast.error('File size exceeds 10MB limit.');
        return;
      }

      setCsvFile(selectedFile);
      setCsvFileName(selectedFile.name);

      // In a real implementation, you would parse the CSV file and show a preview
      // For now, we'll just simulate this
      setBulkDataPreview([
        { id: 1, firstName: 'John', surname: 'Doe', email: '<EMAIL>', country: 'USA' },
        { id: 2, firstName: 'Jane', surname: 'Smith', email: '<EMAIL>', country: 'Canada' },
        { id: 3, firstName: 'Bob', surname: 'Johnson', email: '<EMAIL>', country: 'UK' },
      ]);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const removeCsvFile = () => {
    setCsvFile(null);
    setCsvFileName('');
    setBulkDataPreview([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSingleUserSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!singleUser.firstName || !singleUser.surname || !singleUser.email) {
      toast.error('Please fill in all required fields (First Name, Surname, Email).');
      return;
    }

    // Validate country-specific required fields based on address format
    if (addressFormat && addressFormat.format) {
      const missingFields = [];
      
      for (const field of addressFormat.format) {
        if (field.required) {
          const fieldValue = singleUser[field.field as keyof typeof singleUser];
          if (!fieldValue || fieldValue === '') {
            missingFields.push(field.label);
          }
        }
      }
      
      if (missingFields.length > 0) {
        toast.error(`Please fill in the following required fields: ${missingFields.join(', ')}`);
        return;
      }
    }

    try {
      // Prepare user data for API submission
      const userData = {
        firstName: singleUser.firstName,
        surname: singleUser.surname,
        email: singleUser.email,
        personalEmail: singleUser.personalEmail || undefined,
        nwaEmail: singleUser.nwaEmail || undefined,
        dob: singleUser.dob || undefined,
        phone: singleUser.phone || undefined,
        mobile: singleUser.mobile || undefined,
        streetAddress1: singleUser.streetAddress1 || undefined,
        streetAddress2: singleUser.streetAddress2 || undefined,
        town: singleUser.town || undefined,
        country: singleUser.country || undefined,
        city: singleUser.city || undefined,
        postalCode: singleUser.postalCode || undefined,
        regionId: singleUser.regionId || undefined,
        regionText: singleUser.regionText || undefined,
        peaceAmbassadorNumber: singleUser.peaceAmbassadorNumber || undefined,
        // Send treatyNumbers separately for backend processing
        treatyNumbers: treatyNumbers.length > 0 ? treatyNumbers.map(t => ({treatyTypeId: t.treatyTypeId, treatyNumber: t.treatyNumber})) : undefined,
        bio: singleUser.bio || undefined,
        titleId: singleUser.titleId || undefined,
        positionId: singleUser.positionId || undefined,
      };

      // Send data to the API
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create user');
      }

      const result = await response.json();
      toast.success('User created successfully!');

      // Reset form
      setSingleUser({
        firstName: '',
        surname: '',
        dob: '',
        email: '',
        personalEmail: '',
        nwaEmail: '',
        phone: '',
        mobile: '',
        streetAddress1: '',
        streetAddress2: '',
        town: '',
        city: '',
        country: '',
        postalCode: '',
        regionId: '',
        regionText: '',
        peaceAmbassadorNumber: '',
        bio: '',
        titleId: '',
        positionId: '',
      });
      setTreatyNumbers([]); // Reset treaty numbers
      setFilteredPositions([]);
    } catch (error: any) {
      toast.error(error.message || 'Failed to create user');
      console.error('Error creating user:', error);
    }
  };

  const handleBulkUploadSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!csvFile) {
      toast.error('Please select a CSV file to upload.');
      return;
    }

    // Show preview mode
    setIsPreviewMode(true);
  };

  const handleConfirmBulkUpload = async () => {
    if (adminConfirmation.toLowerCase() !== 'insert data') {
      toast.error('Please type "insert data" to confirm.');
      return;
    }

    try {
      // In a real implementation, you would send the bulk data to the API
      // For now, we'll just show a success message
      toast.success('Bulk users uploaded successfully!');

      // Reset form
      setCsvFile(null);
      setCsvFileName('');
      setBulkDataPreview([]);
      setIsPreviewMode(false);
      setAdminConfirmation('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to upload bulk users');
      console.error('Error uploading bulk users:', error);
    }
  };

  const handleCancelBulkUpload = () => {
    setIsPreviewMode(false);
    setAdminConfirmation('');
  };

  // Get positions to display (filtered by title or all positions)
  const displayPositions = singleUser.titleId ? filteredPositions : positions;

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex space-x-4 mb-6">
        <button
          onClick={() => setIsBulkUpload(false)}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${!isBulkUpload
            ? 'bg-slate-700 text-white hover:bg-slate-800'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-slate-700 dark:text-slate-300 dark:hover:bg-slate-600'
            }`}
        >
          Single User
        </button>
        <button
          onClick={() => setIsBulkUpload(true)}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${isBulkUpload
            ? 'bg-slate-700 text-white hover:bg-slate-800'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-slate-700 dark:text-slate-300 dark:hover:bg-slate-600'
            }`}
        >
          Bulk Upload (CSV)
        </button>
      </div>

      {!isBulkUpload ? (
        // Single User Form
        <div>
          {/* Internal Tabs for Create User Section */}
          <div className="border-b border-slate-200 dark:border-slate-700 mb-6">
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveCreateUserTab('user')}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${activeCreateUserTab === 'user'
                  ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                  }`}
              >
                <User className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
                <span>Create User</span>
              </button>
              <button
                onClick={() => setActiveCreateUserTab('contact')}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${activeCreateUserTab === 'contact'
                  ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                  }`}
              >
                <MapPin className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
                <span>Contact Details</span>
              </button>
              <button
                onClick={() => setActiveCreateUserTab('identification')}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${activeCreateUserTab === 'identification'
                  ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                  }`}
              >
                <IdCard className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
                <span>Identification</span>
              </button>
              <button
                onClick={() => setActiveCreateUserTab('positions')}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${activeCreateUserTab === 'positions'
                  ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                  }`}
              >
                <Briefcase className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
                <span>Positions</span>
              </button>
              <button
                onClick={() => setActiveCreateUserTab('treaty')}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${activeCreateUserTab === 'treaty'
                  ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                  }`}
              >
                <FileText className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
                <span>Treaties</span>
              </button>
              <button
                onClick={() => setActiveCreateUserTab('ordinance')}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${activeCreateUserTab === 'ordinance'
                  ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                  }`}
              >
                <BookOpen className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
                <span>Ordinances</span>
              </button>
            </nav>
          </div>

          <form onSubmit={handleSingleUserSubmit} className="space-y-6">
            {activeCreateUserTab === 'user' ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                      First Name <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="h-5 w-5 text-slate-400" />
                      </div>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        value={singleUser.firstName}
                        onChange={handleSingleUserChange}
                        className="w-full pl-10 pr-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="surname" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                      Surname <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="h-5 w-5 text-slate-400" />
                      </div>
                      <input
                        type="text"
                        id="surname"
                        name="surname"
                        value={singleUser.surname}
                        onChange={handleSingleUserChange}
                        className="w-full pl-10 pr-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="dob" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Date of Birth
                    </label>
                    <input
                      type="date"
                      id="dob"
                      name="dob"
                      value={singleUser.dob}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Email <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={singleUser.email}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="personalEmail" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Personal Email
                    </label>
                    <input
                      type="email"
                      id="personalEmail"
                      name="personalEmail"
                      value={singleUser.personalEmail}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <div>
                    <label htmlFor="nwaEmail" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      NWA Email
                    </label>
                    <input
                      type="email"
                      id="nwaEmail"
                      name="nwaEmail"
                      value={singleUser.nwaEmail}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Phone
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={singleUser.phone}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <div>
                    <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Mobile
                    </label>
                    <input
                      type="tel"
                      id="mobile"
                      name="mobile"
                      value={singleUser.mobile}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label htmlFor="bio" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Bio
                    </label>
                    <textarea
                      id="bio"
                      name="bio"
                      value={singleUser.bio}
                      onChange={handleSingleUserChange}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    type="submit"
                    className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                  >
                    Create User
                  </button>
                </div>
              </>
            ) : activeCreateUserTab === 'contact' ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label htmlFor="streetAddress1" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {addressFormat?.format?.find((f: any) => f.field === 'streetAddress1')?.label || 'Street Address'}
                    </label>
                    <input
                      type="text"
                      id="streetAddress1"
                      name="streetAddress1"
                      value={singleUser.streetAddress1}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                      placeholder="123 Main Street"
                      required={addressFormat?.format?.find((f: any) => f.field === 'streetAddress1')?.required || false}
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label htmlFor="streetAddress2" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {addressFormat?.format?.find((f: any) => f.field === 'streetAddress2')?.label || 'Apt/Suite/Unit'} <span className="text-gray-500">(Optional)</span>
                    </label>
                    <input
                      type="text"
                      id="streetAddress2"
                      name="streetAddress2"
                      value={singleUser.streetAddress2}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Apt 4B, Suite 200, Unit 5"
                    />
                  </div>

                  <div>
                    <label htmlFor="town" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {addressFormat?.format?.find((f: any) => f.field === 'town')?.label || 'Town'}
                    </label>
                    <input
                      type="text"
                      id="town"
                      name="town"
                      value={singleUser.town}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                      required={addressFormat?.format?.find((f: any) => f.field === 'town')?.required || false}
                    />
                  </div>

                  <div>
                    <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Country
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        id="country"
                        value={singleUser.country}
                        onChange={(e) => {
                          const value = e.target.value;
                          setSingleUser(prev => ({ ...prev, country: value }));
                          setCountrySearch(value);
                          // Clear city when country changes
                          setSingleUser(prev => ({ ...prev, city: '' }));
                          setCitySearch('');
                        }}
                        onFocus={() => countrySearch.length >= 2 && setShowCountryDropdown(true)}
                        onBlur={() => setTimeout(() => setShowCountryDropdown(false), 200)}
                        placeholder="Search for a country..."
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                        autoComplete="off"
                      />
                      {showCountryDropdown && countries.length > 0 && (
                        <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md py-1 max-h-60 overflow-auto">
                          {countries.map((country) => (
                            <div
                              key={country.id}
                              className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                              onMouseDown={() => {
                                setSingleUser(prev => ({ ...prev, country: country.name, regionId: '', regionText: '' }));
                                setSelectedCountryId(country.id);
                                setCountrySearch('');
                                setShowCountryDropdown(false);
                                // Fetch regions and address format for the selected country
                                fetchRegions(country.id);
                                fetchAddressFormat(country.id);
                              }}
                            >
                              {country.name}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <label htmlFor="city" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      City
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        id="city"
                        value={singleUser.city}
                        onChange={(e) => {
                          const value = e.target.value;
                          setSingleUser(prev => ({ ...prev, city: value }));
                          setCitySearch(value);
                        }}
                        onFocus={() => citySearch.length >= 2 && singleUser.country && setShowCityDropdown(true)}
                        onBlur={() => setTimeout(() => setShowCityDropdown(false), 200)}
                        placeholder="Search for a city..."
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                        autoComplete="off"
                        disabled={!singleUser.country}
                      />
                      {showCityDropdown && cities.length > 0 && (
                        <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md py-1 max-h-60 overflow-auto">
                          {cities.map((city) => (
                            <div
                              key={city.id}
                              className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                              onMouseDown={() => {
                                setSingleUser(prev => ({ ...prev, city: city.name }));
                                setCitySearch('');
                                setShowCityDropdown(false);
                              }}
                            >
                              {city.name}
                            </div>
                          ))}
                        </div>
                      )}
                      {singleUser.country && (
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          Showing cities in {singleUser.country}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label htmlFor="region" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {addressFormat?.format?.find((f: any) => f.field === 'regionText')?.label || 'Region'}
                    </label>
                    {regions.length > 0 ? (
                      <select
                        id="region"
                        name="regionId"
                        value={singleUser.regionId}
                        onChange={handleSingleUserChange}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                        disabled={!selectedCountryId}
                        required={addressFormat?.format?.find((f: any) => f.field === 'regionText')?.required || false}
                      >
                        <option value="">Select a region</option>
                        {regions.map((region) => (
                          <option key={region.id} value={region.id}>
                            {region.name}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <input
                        type="text"
                        id="regionText"
                        name="regionText"
                        value={singleUser.regionText}
                        onChange={handleSingleUserChange}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                        placeholder={`Enter ${addressFormat?.format?.find((f: any) => f.field === 'regionText')?.label?.toLowerCase() || 'region'}`}
                        disabled={!selectedCountryId}
                        required={addressFormat?.format?.find((f: any) => f.field === 'regionText')?.required || false}
                      />
                    )}
                    {selectedCountryId && regions.length === 0 && (
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        No regions available for this country. Please enter manually.
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="postalCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {addressFormat?.format?.find((f: any) => f.field === 'postalCode')?.label || 'Postal Code'}
                    </label>
                    <input
                      type="text"
                      id="postalCode"
                      name="postalCode"
                      value={singleUser.postalCode}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                      required={addressFormat?.format?.find((f: any) => f.field === 'postalCode')?.required || false}
                      placeholder={addressFormat?.example ? `e.g., ${addressFormat.example.split(', ').pop()}` : 'Enter postal code'}
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setActiveCreateUserTab('user')}
                    className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                  >
                    Back to Create User
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      toast.success('Contact information saved!');
                      setActiveCreateUserTab('identification');
                    }}
                    className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                  >
                    Save & Continue
                  </button>
                </div>
              </>
            ) : activeCreateUserTab === 'identification' ? (
              <>
                <div className="space-y-6">
                  {/* Ambassadorial Membership Section */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Ambassadorial Membership</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="peaceAmbassadorNumber" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Peace Ambassador
                        </label>
                        <input
                          type="text"
                          id="peaceAmbassadorNumber"
                          name="peaceAmbassadorNumber"
                          value={singleUser.peaceAmbassadorNumber}
                          onChange={handleSingleUserChange}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                          placeholder="PAMB34344d-e002"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Identification Section */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Identification</h3>
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <select className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white">
                          <option value="">Select ID Type</option>
                          <option value="drivers_license">Drivers License</option>
                          <option value="passport">Passport</option>
                          <option value="national_id">National ID</option>
                        </select>
                        <input
                          type="text"
                          placeholder="Enter ID number"
                          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                        />
                        <button
                          type="button"
                          className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                        >
                          Add
                        </button>
                      </div>
                      
                      {/* Display added IDs */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded-md">
                          <span className="text-sm text-gray-700 dark:text-gray-300">Drivers License - LMD233543s</span>
                          <button className="text-red-500 hover:text-red-700 text-sm">Remove</button>
                        </div>
                        <div className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded-md">
                          <span className="text-sm text-gray-700 dark:text-gray-300">Passport - LMP3434dfw34</span>
                          <button className="text-red-500 hover:text-red-700 text-sm">Remove</button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Treaty Type Section */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Treaty Type</h3>
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <select className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white">
                          <option value="">Select Treaty Type</option>
                          {treatyTypes.map((treatyType) => (
                            <option key={treatyType.id} value={treatyType.id}>
                              {treatyType.name}
                            </option>
                          ))}
                        </select>
                        <input
                          type="text"
                          placeholder="Enter treaty number (e.g. PPT234234wef234)"
                          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                        />
                        <button
                          type="button"
                          className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                        >
                          Add
                        </button>
                      </div>
                      
                      {/* Display added treaty types */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded-md">
                          <span className="text-sm text-gray-700 dark:text-gray-300">Peace & Trade Treaty - PPT234234wef234</span>
                          <button className="text-red-500 hover:text-red-700 text-sm">Remove</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setActiveCreateUserTab('contact')}
                    className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                  >
                    Back to Contact Details
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      toast.success('Identification information saved!');
                      setActiveCreateUserTab('positions');
                    }}
                    className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                  >
                    Save & Continue
                  </button>
                </div>
              </>
            ) : activeCreateUserTab === 'positions' ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="titleId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Ambassadorial Title
                    </label>
                    <select
                      id="titleId"
                      name="titleId"
                      value={singleUser.titleId}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Select Ambassadorial Title</option>
                      {titles
                        .filter(title => title.isActive)
                        .map(title => (
                          <option key={title.id} value={title.id}>
                            {title.name}
                          </option>
                        ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="positionId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Position
                    </label>
                    <select
                      id="positionId"
                      name="positionId"
                      value={singleUser.positionId}
                      onChange={handleSingleUserChange}
                      disabled={!singleUser.titleId}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white disabled:opacity-50"
                    >
                      <option value="">{singleUser.titleId ? 'Select Position' : 'Select an ambassadorial title first'}</option>
                      {displayPositions
                        .filter(position => position.isActive)
                        .map(position => (
                          <option key={position.id} value={position.id}>
                            {position.title}
                          </option>
                        ))}
                    </select>
                    {singleUser.titleId && displayPositions.length === 0 && (
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        No positions are associated with the selected ambassadorial title.
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setActiveCreateUserTab('identification')}
                    className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                  >
                    Back to Identification
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      toast.success('Positions information saved!');
                      setActiveCreateUserTab('treaty');
                    }}
                    className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                  >
                    Save & Continue
                  </button>
                </div>
              </>
            ) : activeCreateUserTab === 'treaty' ? (
              /* Enhanced Treaties Tab Content */
              <EnhancedTreatyCreation
                userId={singleUser.email} // We'll use email as temporary ID until user is created
                userProfile={{
                  treatyNumbers: treatyNumbers, // Pass multiple treaty numbers
                  firstName: singleUser.firstName,
                  lastName: singleUser.surname,
                  email: singleUser.email
                }}
                onTreatyCreated={() => {
                  toast.success('Treaty created successfully!');
                  setActiveCreateUserTab('user');
                }}
              />
            ) : (
              /* Ordinances Tab Content */
              <div className="space-y-6">
                <div className="grid grid-cols-1 gap-6">
                  {availableOrdinances.length === 0 ? (
                    <div className="text-center py-8">
                      <BookOpen className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No ordinance types available</h3>
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        There are currently no ordinance types configured in the system. Please contact an administrator to set up ordinance types.
                      </p>
                    </div>
                  ) : (
                    <div>
                      <label htmlFor="ordinanceSelect" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Select Ordinance
                      </label>
                      <div className="flex space-x-2">
                        <select
                          id="ordinanceSelect"
                          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                          value=""
                          onChange={(e) => {
                            const ordinanceId = e.target.value;
                            if (ordinanceId) {
                              // Check if ordinance is already selected
                              if (!selectedOrdinances.includes(ordinanceId)) {
                                setSelectedOrdinances([...selectedOrdinances, ordinanceId]);
                                toast.success('Ordinance added successfully!');
                              }
                            }
                          }}
                        >
                          <option value="">Select an ordinance</option>
                          {availableOrdinances.map((ordinance) => (
                            <option key={ordinance.id} value={ordinance.id}>
                              {ordinance.name}
                            </option>
                          ))}
                        </select>
                        <button
                          type="button"
                          className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                          onClick={() => {
                            const selectElement = document.getElementById('ordinanceSelect') as HTMLSelectElement;
                            const ordinanceId = selectElement.value;
                            if (ordinanceId) {
                              // Check if ordinance is already selected
                              if (!selectedOrdinances.includes(ordinanceId)) {
                                setSelectedOrdinances([...selectedOrdinances, ordinanceId]);
                                toast.success('Ordinance added successfully!');
                                // Reset the select to default
                                selectElement.value = '';
                              }
                            }
                          }}
                        >
                          Add Ordinance
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Display selected ordinances */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 dark:text-white mb-2">Selected Ordinances</h4>
                    <div className="border border-gray-300 dark:border-gray-600 rounded-md p-4 min-h-32">
                      {selectedOrdinances.length > 0 ? (
                        <ul className="space-y-2">
                          {selectedOrdinances.map((ordinanceId) => {
                            const ordinance = availableOrdinances.find(o => o.id === ordinanceId);
                            return ordinance ? (
                              <li key={ordinanceId} className="flex justify-between items-center bg-slate-100 dark:bg-slate-700 p-2 rounded">
                                <span>{ordinance.name}</span>
                                <button
                                  type="button"
                                  className="text-red-500 hover:text-red-700"
                                  onClick={() => {
                                    setSelectedOrdinances(selectedOrdinances.filter(id => id !== ordinanceId));
                                    toast.success('Ordinance removed!');
                                  }}
                                >
                                  Remove
                                </button>
                              </li>
                            ) : null;
                          })}
                        </ul>
                      ) : (
                        <p className="text-gray-500 dark:text-gray-400 text-sm">No ordinances selected</p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setActiveCreateUserTab('treaty')}
                    className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                  >
                    Back to Treaties
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      // In a real implementation, you would save the ordinance selections to the database
                      toast.success(`Saved ${selectedOrdinances.length} ordinances!`);
                      setActiveCreateUserTab('user');
                    }}
                    className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                  >
                    Save Ordinances
                  </button>
                </div>
              </div>
            )}
          </form>
        </div>
      ) : (
        // Bulk Upload Form
        <div>
          {!isPreviewMode ? (
            <form onSubmit={handleBulkUploadSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  CSV File <span className="text-red-500">*</span>
                </label>
                <div className="flex items-center space-x-4">
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleCsvFileChange}
                    accept=".csv,text/csv"
                    className="hidden"
                  />
                  <button
                    type="button"
                    onClick={triggerFileInput}
                    className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 text-sm transition-colors duration-200"
                  >
                    Choose File
                  </button>
                  {csvFileName && (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400 truncate max-w-xs">
                        {csvFileName}
                      </span>
                      <button
                        type="button"
                        onClick={removeCsvFile}
                        className="text-red-500 hover:text-red-700"
                      >
                        Remove
                      </button>
                    </div>
                  )}
                </div>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  CSV files only. Max 10MB. Required columns: firstName, surname, email
                </p>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                  CSV Format Requirements
                </h3>
                <ul className="text-xs text-blue-700 dark:text-blue-300 list-disc pl-5 space-y-1">
                  <li>Required columns: firstName, surname, email</li>
                  <li>Optional columns: dob, phone, mobile, personalEmail, nwaEmail, streetAddress, town, city, country, postalCode, peaceAmbassadorNumber, bio, titleId, positionId</li>
                  <li>Date format: YYYY-MM-DD</li>
                  <li>Maximum 1000 rows per upload</li>
                </ul>
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={!csvFile}
                  className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 transition-colors duration-200"
                >
                  Preview & Upload
                </button>
              </div>
            </form>
          ) : (
            // Preview Mode
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Bulk Upload Preview</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          First Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Surname
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Email
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Country
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {bulkDataPreview.map((user) => (
                        <tr key={user.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {user.firstName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {user.surname}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {user.email}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {user.country}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Showing {bulkDataPreview.length} of {bulkDataPreview.length} records
                </p>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                  Admin Confirmation Required
                </h3>
                <p className="text-sm text-blue-700 dark:text-blue-300 mb-4">
                  Please type &quot;insert data&quot; in the field below to confirm the bulk upload.
                  This action cannot be undone.
                </p>
                <div>
                  <label htmlFor="adminConfirmation" className="block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                    Confirmation Text
                  </label>
                  <input
                    type="text"
                    id="adminConfirmation"
                    value={adminConfirmation}
                    onChange={(e) => setAdminConfirmation(e.target.value)}
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Type 'insert data' to confirm"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={handleCancelBulkUpload}
                  className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleConfirmBulkUpload}
                  disabled={adminConfirmation.toLowerCase() !== 'insert data'}
                  className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 transition-colors duration-200"
                >
                  Confirm & Insert Data
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};