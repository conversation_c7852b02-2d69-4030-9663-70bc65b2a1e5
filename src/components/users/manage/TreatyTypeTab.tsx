'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { FileText, Edit, Plus, Search, ArrowRight, Users, AlertTriangle, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';

interface TreatyType {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  treatyCount: number;
  activeTreaties: number;
}

interface Treaty {
  id: string;
  treatyTypeId: string;
  title: string;
  status: string;
  notes: string;
  signedDate: string;
  expirationDate: string;
  renewalDate: string;
  createdAt: string;
  updatedAt: string;
}

interface Pagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  pageSize: number;
}

export const TreatyTypeTab: React.FC = () => {
  const [treatyTypes, setTreatyTypes] = useState<TreatyType[]>([]);
  const [treaties, setTreaties] = useState<Treaty[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [editingType, setEditingType] = useState<TreatyType | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showReassignModal, setShowReassignModal] = useState(false);
  const [selectedTreatyType, setSelectedTreatyType] = useState<TreatyType | null>(null);
  const [reassignTreaties, setReassignTreaties] = useState<Treaty[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 10
  });
  const [autocompleteSuggestions, setAutocompleteSuggestions] = useState<string[]>([]);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    treatyId: '',
    selectedTreaty: null as Treaty | null,
  });

  // Autocomplete state
  const [treatySearchQuery, setTreatySearchQuery] = useState('');
  const [treatySuggestions, setTreatySuggestions] = useState<Treaty[]>([]);
  const [showTreatySuggestions, setShowTreatySuggestions] = useState(false);

  // Reassign form state
  const [reassignData, setReassignData] = useState({
    treatyId: '',
    newTreatyTypeId: '',
  });

  const fetchTreatyTypes = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.currentPage.toString(),
        limit: pagination.pageSize.toString(),
        search: searchQuery
      });

      const response = await fetch(`/api/treaty-types?${params}`);
      if (response.ok) {
        const data = await response.json();
        setTreatyTypes(data.treatyTypes);
        setPagination(prev => ({
          ...prev,
          totalPages: Math.ceil(data.totalCount / pagination.pageSize),
          totalCount: data.totalCount
        }));
      }
    } catch (error) {
      console.error('Error fetching treaty types:', error);
      toast.error('Failed to load treaty types');
    } finally {
      setLoading(false);
    }
  }, [pagination.currentPage, pagination.pageSize, searchQuery]);

  const fetchTreaties = useCallback(async () => {
    try {
      const response = await fetch('/api/treaties');
      if (response.ok) {
        const data = await response.json();
        setTreaties(data.treaties);
      }
    } catch (error) {
      console.error('Error fetching treaties:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchTreatyTypes();
  }, [pagination.currentPage, searchQuery, fetchTreatyTypes]);

  useEffect(() => {
    fetchTreaties();

    // Add click outside handler
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.treaty-search-container')) {
        setShowTreatySuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [fetchTreaties]);

  const handleCreateTreatyType = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.treatyId) {
      toast.error('Treaty type and treaty selection are required');
      return;
    }

    try {
      const response = await fetch('/api/treaty-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
        }),
      });

      if (response.ok) {
        const newTreatyType = await response.json();

        // If a treaty was selected, link it to the treaty type
        if (formData.treatyId) {
          await fetch(`/api/treaties?id=${formData.treatyId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              treatyTypeId: newTreatyType.treatyType.id,
            }),
          });
        }

        toast.success('Treaty type assigned successfully');
        setFormData({ name: '', description: '', treatyId: '', selectedTreaty: null });
        setTreatySearchQuery('');
        setShowCreateForm(false);
        fetchTreatyTypes();
        fetchTreaties();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create treaty type');
      }
    } catch (error) {
      console.error('Error creating treaty type:', error);
      toast.error('Failed to create treaty type');
    }
  };

  const handleEditTreatyType = (treatyType: TreatyType) => {
    setEditingType(treatyType);
    // Find the associated treaty by looking at treaties that have this treaty type assigned
    const associatedTreaty = treaties.find(t => t.treatyTypeId === treatyType.id);
    setFormData({
      name: treatyType.name,
      description: treatyType.description || '',
      treatyId: associatedTreaty?.id || '',
      selectedTreaty: associatedTreaty || null,
    });
    setTreatySearchQuery(associatedTreaty?.title || '');
  };

  const handleUpdateTreatyType = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editingType) return;

    try {
      const response = await fetch(`/api/treaty-types?id=${editingType.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
        }),
      });

      if (response.ok) {
        // If a treaty was selected, link it to the treaty type
        if (formData.treatyId) {
          await fetch(`/api/treaties?id=${formData.treatyId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              treatyTypeId: editingType.id,
            }),
          });
        }

        toast.success('Treaty type assignment updated successfully');
        setEditingType(null);
        setFormData({ name: '', description: '', treatyId: '', selectedTreaty: null });
        setTreatySearchQuery('');
        fetchTreatyTypes();
        fetchTreaties();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to update treaty type');
      }
    } catch (error) {
      console.error('Error updating treaty type:', error);
      toast.error('Failed to update treaty type');
    }
  };

  const handleDeleteTreatyType = async (treatyType: TreatyType) => {
    if (treatyType.treatyCount > 0) {
      toast.error('Cannot delete treaty type that has associated treaties');
      return;
    }

    if (!confirm(`Are you sure you want to delete "${treatyType.name}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/treaty-types?id=${treatyType.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success('Treaty type deleted successfully');
        fetchTreatyTypes();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to delete treaty type');
      }
    } catch (error) {
      console.error('Error deleting treaty type:', error);
      toast.error('Failed to delete treaty type');
    }
  };

  const handleReassignTreaty = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!reassignData.treatyId || !reassignData.newTreatyTypeId) {
      toast.error('Please select both treaty and new treaty type');
      return;
    }

    try {
      const response = await fetch(`/api/treaties?id=${reassignData.treatyId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          treatyTypeId: reassignData.newTreatyTypeId,
        }),
      });

      if (response.ok) {
        toast.success('Treaty reassigned successfully');
        setReassignData({ treatyId: '', newTreatyTypeId: '' });
        setShowReassignModal(false);
        fetchTreaties();
        fetchTreatyTypes();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to reassign treaty');
      }
    } catch (error) {
      console.error('Error reassigning treaty:', error);
      toast.error('Failed to reassign treaty');
    }
  };

  const openReassignModal = (treatyType: TreatyType) => {
    setSelectedTreatyType(treatyType);
    // Get treaties that could be reassigned (those with different treaty type or no type)
    const assignableTreaties = treaties.filter(treaty =>
      treaty.treatyTypeId !== treatyType.id
    );
    setReassignTreaties(assignableTreaties);
    setShowReassignModal(true);
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page on search

    // Generate autocomplete suggestions based on existing treaty types
    if (query.length > 0) {
      const suggestions = treatyTypes
        .filter(type =>
          type.name.toLowerCase().includes(query.toLowerCase()) ||
          type.description.toLowerCase().includes(query.toLowerCase())
        )
        .map(type => type.name)
        .slice(0, 5); // Limit to 5 suggestions
      setAutocompleteSuggestions([...new Set(suggestions)]); // Remove duplicates
    } else {
      setAutocompleteSuggestions([]);
    }
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchTreatyTypes();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-700"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-slate-800 dark:text-slate-200">
          Treaty Type Assignments
        </h2>
        <button
          onClick={() => setShowCreateForm(true)}
          className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200 flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          Assign Treaty Type
        </button>
      </div>

      {/* Create/Edit Form */}
      {(showCreateForm || editingType) && (
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">
            {editingType ? 'Edit Treaty Type Assignment' : 'Assign Treaty Type'}
          </h3>

          <form onSubmit={editingType ? handleUpdateTreatyType : handleCreateTreatyType} className="space-y-4">
            {/* Treaty Search/Autocomplete Field */}
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                Treaty *
              </label>
              <div className="relative treaty-search-container">
                <input
                  type="text"
                  value={treatySearchQuery}
                  onChange={(e) => {
                    setTreatySearchQuery(e.target.value);
                    setShowTreatySuggestions(true);
                    // Filter suggestions based on search
                    const filtered = treaties.filter(treaty =>
                      treaty.title.toLowerCase().includes(e.target.value.toLowerCase())
                    );
                    setTreatySuggestions(filtered.slice(0, 5)); // Limit to 5 suggestions
                  }}
                  onFocus={() => setShowTreatySuggestions(true)}
                  className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white ring-1 ring-slate-200 dark:ring-slate-600 placeholder-slate-400 z-20"
                  placeholder="Search for treaty to assign type"
                  required
                />
                {showTreatySuggestions && treatySuggestions.length > 0 && (
                  <div className="absolute z-10 w-full bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-lg mt-1 max-h-40 overflow-y-auto">
                    {treatySuggestions.map((treaty) => (
                      <div
                        key={treaty.id}
                        className="px-3 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 cursor-pointer"
                        onClick={() => {
                          setFormData({
                            ...formData,
                            treatyId: treaty.id,
                            selectedTreaty: treaty
                          });
                          setTreatySearchQuery(treaty.title);
                          setShowTreatySuggestions(false);
                        }}
                      >
                        {treaty.title}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                Treaty Type Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white ring-1 ring-slate-200 dark:ring-slate-600 placeholder-slate-400 z-20"
                placeholder="Enter treaty type name"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white ring-1 ring-slate-200 dark:ring-slate-600 placeholder-slate-400 z-20"
                placeholder="Description of this treaty type..."
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => {
                  setShowCreateForm(false);
                  setEditingType(null);
                  setFormData({ name: '', description: '', treatyId: '', selectedTreaty: null });
                  setTreatySearchQuery('');
                }}
                className="px-4 py-2 border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 rounded-md hover:bg-slate-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
              >
                {editingType ? 'Update Assignment' : 'Assign Treaty Type'}
              </button>
            </div>
          </form>
        </div>
      )}


      {/* Treaties with Types List */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
        <div className="p-6">
          <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">
            Treaty Assignments ({treaties.length})
          </h3>

          {treaties.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="mx-auto h-12 w-12 text-slate-400" />
              <h3 className="mt-2 text-lg font-medium text-slate-800 dark:text-slate-200">
                No treaties found
              </h3>
              <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                Create your first treaty to get started with type assignments.
              </p>
            </div>
          ) : (
            <>
              <div className="space-y-4">
                {treaties.map((treaty) => {
                  const treatyType = treatyTypes.find(type => type.id === treaty.treatyTypeId);
                  return (
                    <div key={treaty.id} className="border border-slate-300 dark:border-slate-600 rounded-md p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h4 className="font-medium text-slate-800 dark:text-slate-200">
                              {treaty.title}
                            </h4>
                            <span className={`px-2 py-1 text-xs rounded ${
                              treaty.status === 'ACTIVE'
                                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                                : treaty.status === 'EXPIRED'
                                  ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
                                  : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                            }`}>
                              {treaty.status}
                            </span>
                          </div>
                          <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                            Type: {treatyType ? treatyType.name : 'No type assigned'}
                          </p>
                          <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">
                            {treaty.notes || 'No description'}
                          </p>
                        </div>

                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => treatyType && handleEditTreatyType(treatyType)}
                            disabled={!treatyType}
                            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Reassign treaty type"
                          >
                            <ArrowRight className="h-4 w-4 mr-1" />
                            Reassign
                          </button>
                          <button
                            onClick={() => {
                              // Find the treaty type for this treaty to edit
                              const typeToEdit = treatyTypes.find(type => type.id === treaty.treatyTypeId);
                              if (typeToEdit) {
                                handleEditTreatyType(typeToEdit);
                              }
                            }}
                            className="px-3 py-1 text-sm bg-slate-600 text-white rounded hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 flex items-center"
                            title="Edit treaty type"
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </button>
                          {!treatyType && (
                            <button
                              onClick={() => {
                                // Remove treaty type assignment
                                fetch(`/api/treaties?id=${treaty.id}`, {
                                  method: 'PUT',
                                  headers: { 'Content-Type': 'application/json' },
                                  body: JSON.stringify({ treatyTypeId: null }),
                                }).then(() => {
                                  fetchTreaties();
                                  toast.success('Treaty type assignment removed');
                                });
                              }}
                              className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 flex items-center"
                              title="Remove treaty type assignment"
                            >
                              <Trash2 className="h-4 w-4 mr-1" />
                              Remove
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Reassign Modal */}
      {showReassignModal && selectedTreatyType && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">
                Reassign Treaty to &ldquo;{selectedTreatyType.name}&rdquo;
              </h3>

              <form onSubmit={handleReassignTreaty} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    Select Treaty to Reassign
                  </label>
                  <select
                    value={reassignData.treatyId}
                    onChange={(e) => setReassignData({ ...reassignData, treatyId: e.target.value })}
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
                    required
                  >
                    <option value="">Select a treaty</option>
                    {reassignTreaties.map((treaty) => (
                      <option key={treaty.id} value={treaty.id}>
                        {treaty.title} (Current: {treatyTypes.find(t => t.id === treaty.treatyTypeId)?.name || 'None'})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => {
                      setShowReassignModal(false);
                      setReassignData({ treatyId: '', newTreatyTypeId: selectedTreatyType.id });
                    }}
                    className="px-4 py-2 border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 rounded-md hover:bg-slate-50 dark:hover:bg-slate-700"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Reassign Treaty
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};