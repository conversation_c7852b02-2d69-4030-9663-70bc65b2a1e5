import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  FileText, 
  Plus, 
  AlertCircle, 
  CheckCircle, 
  DollarSign, 
  Upload,
  X
} from 'lucide-react';

interface TreatyType {
  id: string;
  name: string;
  description: string | null;
  category: string;
  price: number;
  currency: string;
  requiresPayment: boolean;
  isActive: boolean;
}

interface Category {
  id: number;
  name: string;
  description: string | null;
}

interface Subcategory {
  id: number;
  name: string;
  description: string | null;
  categoryId: number;
}

interface Country {
  id: number;
  name: string;
}

interface City {
  id: number;
  name: string;
  countryId: number;
}

interface BusinessDetails {
  businessName: string;
  businessAddress: string;
  businessEmail: string;
  businessPhone: string;
  businessWebsite: string;
  businessDescription: string;
  categoryId: string;
  subcategoryId: string;
  logoFile: File | null;
}

interface TreatyTypeEntry {
  treatyTypeId: string;
  treatyType: TreatyType;
  businessDetails: BusinessDetails;
  completed: boolean;
}

interface TreatyFormData {
  treatyId: string;
  treatyName: string;
  treatyTypes: TreatyTypeEntry[];
  currentCountryId: string;
  currentCityId: string;
  residenceCountryId: string;
  residenceCityId: string;
  residentialAddress: string;
  phoneNumbers: string[];
  email: string;
  attachments: File[];
}

interface EnhancedTreatyCreationProps {
  userId: string;
  userProfile: {
    treatyNumbers?: Array<{
      id: string;
      treatyTypeId: string;
      treatyNumber: string;
    }>;
    firstName: string;
    lastName: string;
    email: string;
  };
  onTreatyCreated: () => void;
}

export const EnhancedTreatyCreation: React.FC<EnhancedTreatyCreationProps> = ({
  userId,
  userProfile,
  onTreatyCreated
}) => {
  const [step, setStep] = useState(1);
  const [treatyTypes, setTreatyTypes] = useState<TreatyType[]>([]);
  const [availableTreaties, setAvailableTreaties] = useState<Array<{id: string, name: string, description: string}>>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [countries, setCountries] = useState<Country[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentTreatyTypeIndex, setCurrentTreatyTypeIndex] = useState(0);

  const [formData, setFormData] = useState<TreatyFormData>({
    treatyId: '',
    treatyName: '',
    treatyTypes: [],
    currentCountryId: '',
    currentCityId: '',
    residenceCountryId: '',
    residenceCityId: '',
    residentialAddress: '',
    phoneNumbers: [''],
    email: userProfile.email,
    attachments: []
  });

  useEffect(() => {
    fetchTreatyTypes();
    fetchAvailableTreaties();
    fetchCategories();
    fetchCountries();
  }, []);

  useEffect(() => {
    const currentTreatyType = formData.treatyTypes[currentTreatyTypeIndex];
    if (currentTreatyType?.businessDetails.categoryId) {
      fetchSubcategories(currentTreatyType.businessDetails.categoryId);
    }
  }, [currentTreatyTypeIndex, formData.treatyTypes]);

  useEffect(() => {
    if (formData.currentCountryId) {
      fetchCities(formData.currentCountryId, 'current');
    }
  }, [formData.currentCountryId]);

  useEffect(() => {
    if (formData.residenceCountryId) {
      fetchCities(formData.residenceCountryId, 'residence');
    }
  }, [formData.residenceCountryId]);

  const hasTreatyNumbers = Boolean(userProfile.treatyNumbers && userProfile.treatyNumbers.length > 0);
  const checkUsershipStatus = () => {
    return true;
  };

  const fetchTreatyTypes = async () => {
    try {
      const response = await fetch('/api/treaty-types');
      if (response.ok) {
        const data = await response.json();
        setTreatyTypes(data.treatyTypes || []);
      }
    } catch (error) {
      console.error('Error fetching treaty types:', error);
      toast.error('Failed to load treaty types');
    }
  };

  const fetchAvailableTreaties = async () => {
    try {
      const response = await fetch('/api/treaties');
      if (response.ok) {
        const data = await response.json();
        // Extract unique treaty names from the response
        const treaties = data.treaties || [];
        const uniqueTreaties = treaties.reduce((acc: Array<{id: string, name: string, description: string}>, treaty: any) => {
          const existingTreaty = acc.find(t => t.name === treaty.name);
          if (!existingTreaty && treaty.name) {
            acc.push({
              id: treaty.id,
              name: treaty.name,
              description: treaty.description || ''
            });
          }
          return acc;
        }, []);
        setAvailableTreaties(uniqueTreaties);
      }
    } catch (error) {
      console.error('Error fetching available treaties:', error);
      toast.error('Failed to load available treaties');
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast.error('Failed to load business categories');
    }
  };

  const fetchSubcategories = async (categoryId: string) => {
    try {
      const response = await fetch(`/api/categories/${categoryId}/subcategories`);
      if (response.ok) {
        const data = await response.json();
        setSubcategories(data.subcategories || []);
      }
    } catch (error) {
      console.error('Error fetching subcategories:', error);
      toast.error('Failed to load subcategories');
    }
  };

  const fetchCountries = async () => {
    try {
      const response = await fetch('/api/countries');
      if (response.ok) {
        const data = await response.json();
        setCountries(data || []);
      }
    } catch (error) {
      console.error('Error fetching countries:', error);
      toast.error('Failed to load countries');
    }
  };

  const fetchCities = async (countryId: string, type: 'current' | 'residence') => {
    try {
      const response = await fetch(`/api/countries/${countryId}/cities`);
      if (response.ok) {
        const data = await response.json();
        setCities(data || []);
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
      toast.error('Failed to load cities');
    }
  };

  const addTreatyType = (treatyTypeId: string) => {
    const treatyType = treatyTypes.find(t => t.id === treatyTypeId);
    if (!treatyType) return;

    // Check if treaty type already added
    const exists = formData.treatyTypes.some(tt => tt.treatyTypeId === treatyTypeId);
    if (exists) {
      toast.error('This treaty type has already been added');
      return;
    }

    const newTreatyTypeEntry: TreatyTypeEntry = {
      treatyTypeId,
      treatyType,
      businessDetails: {
        businessName: '',
        businessAddress: '',
        businessEmail: '',
        businessPhone: '',
        businessWebsite: '',
        businessDescription: '',
        categoryId: '',
        subcategoryId: '',
        logoFile: null
      },
      completed: false
    };

    setFormData(prev => ({
      ...prev,
      treatyTypes: [...prev.treatyTypes, newTreatyTypeEntry]
    }));

    setCurrentTreatyTypeIndex(formData.treatyTypes.length);
    toast.success(`${treatyType.name} added successfully`);
  };

  const removeTreatyType = (index: number) => {
    setFormData(prev => ({
      ...prev,
      treatyTypes: prev.treatyTypes.filter((_, i) => i !== index)
    }));

    if (currentTreatyTypeIndex >= formData.treatyTypes.length - 1) {
      setCurrentTreatyTypeIndex(Math.max(0, formData.treatyTypes.length - 2));
    }
  };

  const handleBusinessDetailsChange = (field: keyof BusinessDetails, value: string | File | null) => {
    if (currentTreatyTypeIndex < 0 || currentTreatyTypeIndex >= formData.treatyTypes.length) return;

    setFormData(prev => ({
      ...prev,
      treatyTypes: prev.treatyTypes.map((tt, index) => 
        index === currentTreatyTypeIndex 
          ? {
              ...tt,
              businessDetails: {
                ...tt.businessDetails,
                [field]: value
              }
            }
          : tt
      )
    }));
  };

  const addPhoneNumber = () => {
    setFormData(prev => ({
      ...prev,
      phoneNumbers: [...prev.phoneNumbers, '']
    }));
  };

  const removePhoneNumber = (index: number) => {
    setFormData(prev => ({
      ...prev,
      phoneNumbers: prev.phoneNumbers.filter((_, i) => i !== index)
    }));
  };

  const updatePhoneNumber = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      phoneNumbers: prev.phoneNumbers.map((phone, i) => i === index ? value : phone)
    }));
  };

  const handleFileUpload = (files: FileList | null, type: 'logo' | 'attachments') => {
    if (!files) return;

    if (type === 'logo' && files[0]) {
      handleBusinessDetailsChange('logoFile', files[0]);
    } else if (type === 'attachments') {
      setFormData(prev => ({
        ...prev,
        attachments: [...prev.attachments, ...Array.from(files)]
      }));
    }
  };

  const removeAttachment = (index: number) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  const validateStep = (stepNumber: number): boolean => {
    switch (stepNumber) {
      case 1:
        if (!checkUsershipStatus()) return false;
        return !!(formData.treatyId && formData.treatyTypes.length > 0);
      case 2:
        // Check if current treaty type business details are complete
        const currentTreatyType = formData.treatyTypes[currentTreatyTypeIndex];
        if (!currentTreatyType) return false;
        const bd = currentTreatyType.businessDetails;
        return !!(bd.businessName && bd.businessAddress && bd.businessEmail && bd.categoryId && bd.subcategoryId);
      case 3:
        return !!(formData.currentCountryId && formData.currentCityId && formData.email);
      case 4:
        // All treaty types must be completed
        return formData.treatyTypes.every(tt => tt.completed);
      default:
        return true;
    }
  };

  const markCurrentTreatyTypeComplete = () => {
    if (currentTreatyTypeIndex < 0 || currentTreatyTypeIndex >= formData.treatyTypes.length) return;

    const currentTreatyType = formData.treatyTypes[currentTreatyTypeIndex];
    const bd = currentTreatyType.businessDetails;
    
    if (bd.businessName && bd.businessAddress && bd.businessEmail && bd.categoryId && bd.subcategoryId) {
      setFormData(prev => ({
        ...prev,
        treatyTypes: prev.treatyTypes.map((tt, index) => 
          index === currentTreatyTypeIndex 
            ? { ...tt, completed: true }
            : tt
        )
      }));
      toast.success(`${currentTreatyType.treatyType.name} business details completed`);
      return true;
    } else {
      toast.error('Please complete all required business details');
      return false;
    }
  };

  const nextStep = () => {
    if (validateStep(step)) {
      setStep(prev => prev + 1);
    } else {
      toast.error('Please fill in all required fields before proceeding');
    }
  };

  const prevStep = () => {
    setStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep(step)) {
      toast.error('Please complete all required fields');
      return;
    }

    setLoading(true);
    try {
      // Create FormData for file uploads
      const submitData = new FormData();
      
      // Add basic treaty data
      submitData.append('treatyName', formData.treatyName);
      submitData.append('email', formData.email);
      submitData.append('currentCountryId', formData.currentCountryId);
      submitData.append('currentCityId', formData.currentCityId);
      submitData.append('residenceCountryId', formData.residenceCountryId);
      submitData.append('residenceCityId', formData.residenceCityId);
      submitData.append('residentialAddress', formData.residentialAddress);
      
      // Add treaty types and their business details
      submitData.append('treatyTypes', JSON.stringify(formData.treatyTypes.map(tt => ({
        treatyTypeId: tt.treatyTypeId,
        businessDetails: {
          businessName: tt.businessDetails.businessName,
          businessAddress: tt.businessDetails.businessAddress,
          businessEmail: tt.businessDetails.businessEmail,
          businessPhone: tt.businessDetails.businessPhone,
          businessWebsite: tt.businessDetails.businessWebsite,
          businessDescription: tt.businessDetails.businessDescription,
          categoryId: tt.businessDetails.categoryId,
          subcategoryId: tt.businessDetails.subcategoryId,
        }
      }))));

      // Add phone numbers
      formData.phoneNumbers.forEach((phone, index) => {
        if (phone.trim()) {
          submitData.append(`phoneNumbers[${index}]`, phone);
        }
      });

      // Add logo files for each treaty type
      formData.treatyTypes.forEach((tt, index) => {
        if (tt.businessDetails.logoFile) {
          submitData.append(`logo_${tt.treatyTypeId}`, tt.businessDetails.logoFile);
        }
      });

      formData.attachments.forEach((file, index) => {
        submitData.append(`attachments`, file);
      });

      const response = await fetch(`/api/users/${userId}/treaties`, {
        method: 'POST',
        body: submitData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create treaty');
      }

      toast.success('Treaty created successfully!');
      onTreatyCreated();
      
      // Reset form
      setStep(1);
      setFormData({
        treatyId: '',
        treatyName: '',
        treatyTypes: [],
        currentCountryId: '',
        currentCityId: '',
        residenceCountryId: '',
        residenceCityId: '',
        residentialAddress: '',
        phoneNumbers: [''],
        email: userProfile.email,
        attachments: []
      });
      setCurrentTreatyTypeIndex(0);

    } catch (error: any) {
      console.error('Error creating treaty:', error);
      toast.error(error.message || 'Failed to create treaty');
    } finally {
      setLoading(false);
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {[1, 2, 3, 4].map((stepNumber) => (
        <div key={stepNumber} className="flex items-center">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            step >= stepNumber 
              ? 'bg-slate-700 text-white' 
              : 'bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
          }`}>
            {stepNumber}
          </div>
          {stepNumber < 4 && (
            <div className={`w-12 h-1 mx-2 ${
              step > stepNumber ? 'bg-slate-700' : 'bg-gray-200 dark:bg-gray-700'
            }`} />
          )}
        </div>
      ))}
    </div>
  );

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">
          Treaty & Treaty Types Selection
        </h3>
        
        {/* Process Overview */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800 mb-6">
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
            📝 Treaty Creation Process
          </h4>
          <ol className="text-xs text-blue-700 dark:text-blue-300 space-y-1 list-decimal list-inside">
            <li><strong>Verify Usership:</strong> Ensure you have a Trade Treaty Number</li>
            <li><strong>Select Treaty:</strong> Choose an existing treaty to join (e.g., &ldquo;Peace & Trade Treaty&rdquo;)</li>
            <li><strong>Select Treaty Types:</strong> Choose one or more treaty types based on your business needs</li>
            <li><strong>Complete Business Details:</strong> Fill out business information for each treaty type</li>
            <li><strong>Provide Contact Info:</strong> Add your location and contact details</li>
            <li><strong>Review & Submit:</strong> Confirm all information and submit your application</li>
          </ol>
        </div>
        
        {/* Usership Status Check */}
        <div className={`p-4 rounded-lg border mb-6 ${
          hasTreatyNumbers
            ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
            : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
        }`}>
          <div className="flex items-center">
            {hasTreatyNumbers ? (
              <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            )}
            <div className="flex-1">
              <p className={`font-medium ${
                hasTreatyNumbers ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'
              }`}>
                {hasTreatyNumbers 
                  ? `✅ Treaty Numbers Present`
                  : '❌ Usership Required'
                }
              </p>
              {hasTreatyNumbers ? (
                <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                  You can create treaties and select treaty types. Each treaty type may have associated costs.
                </p>
              ) : (
                <div className="mt-2">
                  <p className="text-sm text-red-600 dark:text-red-400">
                    You need a Trade Treaty Number to create treaties. Please upgrade your usership first.
                  </p>
                  <p className="text-xs text-red-500 dark:text-red-300 mt-1">
                    Contact an administrator or visit the usership section to upgrade.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Select Treaty <span className="text-red-500">*</span>
          </label>
          <select
            value={formData.treatyId}
            onChange={(e) => {
              const selectedTreaty = availableTreaties.find(t => t.id === e.target.value);
              setFormData(prev => ({ 
                ...prev, 
                treatyId: e.target.value,
                treatyName: selectedTreaty?.name || ''
              }));
            }}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={false}
          >
            <option value="">
              Select a treaty to join
            </option>
            {availableTreaties.map((treaty) => (
              <option key={treaty.id} value={treaty.id}>
                {treaty.name}
              </option>
            ))}
          </select>
          {formData.treatyId && (
            <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                Selected Treaty: {formData.treatyName}
              </h4>
              <p className="text-xs text-blue-700 dark:text-blue-300">
                {availableTreaties.find(t => t.id === formData.treatyId)?.description || 'No description available'}
              </p>
            </div>
          )}
          <p className="text-xs text-gray-500 mt-1">
            📋 Select an existing treaty to join. You&apos;ll then choose specific treaty types that apply to your business.
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Add Treaty Types <span className="text-red-500">*</span>
          </label>
          <div className="flex space-x-2">
            <select
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              onChange={(e) => {
                if (e.target.value) {
                  addTreatyType(e.target.value);
                  e.target.value = '';
                }
              }}
            >
              <option value="">Select a treaty type to add</option>
              {treatyTypes
                .filter(type => !formData.treatyTypes.some(tt => tt.treatyTypeId === type.id))
                .map(type => (
                  <option key={type.id} value={type.id}>
                    {type.name} - {type.price > 0 ? `$${type.price} ${type.currency}` : 'Free'}
                  </option>
                ))}
            </select>
          </div>
          <div className="mt-2 space-y-1">
            <p className="text-xs text-gray-500">
              📋 You can add multiple treaty types. Each will require separate business details.
            </p>
{hasTreatyNumbers && (
              <p className="text-xs text-blue-600 dark:text-blue-400">
                💡 Tip: Start with one treaty type, complete its business details, then add more if needed.
              </p>
            )}
          </div>
        </div>

        {/* Selected Treaty Types */}
        {formData.treatyTypes.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
              Selected Treaty Types ({formData.treatyTypes.length})
            </h4>
            <div className="space-y-3">
              {formData.treatyTypes.map((treatyTypeEntry, index) => (
                <div key={treatyTypeEntry.treatyTypeId} className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        {treatyTypeEntry.completed ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-yellow-500" />
                        )}
                        <div>
                          <h5 className="font-medium text-slate-800 dark:text-slate-200">
                            {treatyTypeEntry.treatyType.name}
                          </h5>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {treatyTypeEntry.treatyType.description}
                          </p>
                          <div className="flex items-center space-x-4 mt-1">
                            <div className="flex items-center">
                              <DollarSign className="h-3 w-3 text-green-500 mr-1" />
                              <span className="text-xs font-medium">
                                {treatyTypeEntry.treatyType.price > 0 
                                  ? `$${treatyTypeEntry.treatyType.price} ${treatyTypeEntry.treatyType.currency}`
                                  : 'Free'
                                }
                              </span>
                            </div>
                            <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">
                              {treatyTypeEntry.treatyType.category}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              treatyTypeEntry.completed 
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                            }`}>
                              {treatyTypeEntry.completed ? 'Completed' : 'Pending'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {!treatyTypeEntry.completed && (
                        <button
                          type="button"
                          onClick={() => setCurrentTreatyTypeIndex(index)}
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium"
                        >
                          {currentTreatyTypeIndex === index ? 'Editing' : 'Edit'}
                        </button>
                      )}
                      <button
                        type="button"
                        onClick={() => removeTreatyType(index)}
                        className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm font-medium"
                      >
                        Remove
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Total Cost:</strong> $
                {formData.treatyTypes.reduce((total, tt) => total + (tt.treatyType.price || 0), 0).toFixed(2)} USD
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderStep2 = () => {
    const currentTreatyType = formData.treatyTypes[currentTreatyTypeIndex];
    
    if (!currentTreatyType) {
      return (
        <div className="text-center py-8">
          <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No Treaty Type Selected</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Please go back and select at least one treaty type.
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-2">
            Business Details for: {currentTreatyType.treatyType.name}
          </h3>
          <p className="text-sm text-blue-800 dark:text-blue-200">
            Complete the business details for this treaty type. You can switch between treaty types to fill different business information for each.
          </p>
        </div>

        {/* Treaty Type Selector */}
        {formData.treatyTypes.length > 1 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Currently Editing Treaty Type
            </label>
            <select
              value={currentTreatyTypeIndex}
              onChange={(e) => setCurrentTreatyTypeIndex(parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
            >
              {formData.treatyTypes.map((tt, index) => (
                <option key={tt.treatyTypeId} value={index}>
                  {tt.treatyType.name} {tt.completed ? '✓' : '(Incomplete)'}
                </option>
              ))}
            </select>
          </div>
        )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Business Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={currentTreatyType.businessDetails.businessName}
            onChange={(e) => handleBusinessDetailsChange('businessName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Business Email <span className="text-red-500">*</span>
          </label>
          <input
            type="email"
            value={currentTreatyType.businessDetails.businessEmail}
            onChange={(e) => handleBusinessDetailsChange('businessEmail', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Business Phone
          </label>
          <input
            type="tel"
            value={currentTreatyType.businessDetails.businessPhone}
            onChange={(e) => handleBusinessDetailsChange('businessPhone', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Business Website
          </label>
          <input
            type="url"
            value={currentTreatyType.businessDetails.businessWebsite}
            onChange={(e) => handleBusinessDetailsChange('businessWebsite', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Business Category <span className="text-red-500">*</span>
          </label>
          <select
            value={currentTreatyType.businessDetails.categoryId}
            onChange={(e) => handleBusinessDetailsChange('categoryId', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="">Select category</option>
            {categories.map(category => (
              <option key={category.id} value={category.id.toString()}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Business Subcategory <span className="text-red-500">*</span>
          </label>
          <select
            value={currentTreatyType.businessDetails.subcategoryId}
            onChange={(e) => handleBusinessDetailsChange('subcategoryId', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
            disabled={!currentTreatyType.businessDetails.categoryId}
          >
            <option value="">Select subcategory</option>
            {subcategories.map(subcategory => (
              <option key={subcategory.id} value={subcategory.id.toString()}>
                {subcategory.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Business Address <span className="text-red-500">*</span>
        </label>
        <textarea
          value={currentTreatyType.businessDetails.businessAddress}
          onChange={(e) => handleBusinessDetailsChange('businessAddress', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Business Description
        </label>
        <textarea
          value={currentTreatyType.businessDetails.businessDescription}
          onChange={(e) => handleBusinessDetailsChange('businessDescription', e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Business Logo
        </label>
        <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md dark:border-gray-600">
          <div className="space-y-1 text-center">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <div className="flex text-sm text-gray-600 dark:text-gray-400">
              <label className="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-slate-600 hover:text-slate-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-slate-500">
                <span>Upload logo</span>
                <input
                  type="file"
                  className="sr-only"
                  accept="image/*"
                  onChange={(e) => handleFileUpload(e.target.files, 'logo')}
                />
              </label>
              <p className="pl-1">or drag and drop</p>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF up to 10MB</p>
            {currentTreatyType.businessDetails.logoFile && (
              <p className="text-sm text-green-600 dark:text-green-400">
                Selected: {currentTreatyType.businessDetails.logoFile.name}
              </p>
            )}
          </div>
        </div>

        {/* Complete Treaty Type Button */}
        <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Treaty Type {currentTreatyTypeIndex + 1} of {formData.treatyTypes.length}
          </div>
          <div className="flex space-x-3">
            {!currentTreatyType.completed && (
              <button
                type="button"
                onClick={markCurrentTreatyTypeComplete}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
              >
                Mark as Complete
              </button>
            )}
            {currentTreatyTypeIndex < formData.treatyTypes.length - 1 && (
              <button
                type="button"
                onClick={() => setCurrentTreatyTypeIndex(currentTreatyTypeIndex + 1)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
              >
                Next Treaty Type
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
    );
  };

  const renderStep3 = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">
        Contact & Location Details
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Email <span className="text-red-500">*</span>
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Current Country <span className="text-red-500">*</span>
          </label>
          <select
            value={formData.currentCountryId}
            onChange={(e) => setFormData(prev => ({ ...prev, currentCountryId: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="">Select country</option>
            {countries.map(country => (
              <option key={country.id} value={country.id.toString()}>
                {country.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Current City <span className="text-red-500">*</span>
          </label>
          <select
            value={formData.currentCityId}
            onChange={(e) => setFormData(prev => ({ ...prev, currentCityId: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
            disabled={!formData.currentCountryId}
          >
            <option value="">Select city</option>
            {cities.map(city => (
              <option key={city.id} value={city.id.toString()}>
                {city.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Residence Country
          </label>
          <select
            value={formData.residenceCountryId}
            onChange={(e) => setFormData(prev => ({ ...prev, residenceCountryId: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="">Select country</option>
            {countries.map(country => (
              <option key={country.id} value={country.id.toString()}>
                {country.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Residential Address
        </label>
        <textarea
          value={formData.residentialAddress}
          onChange={(e) => setFormData(prev => ({ ...prev, residentialAddress: e.target.value }))}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Phone Numbers
        </label>
        {formData.phoneNumbers.map((phone, index) => (
          <div key={index} className="flex items-center space-x-2 mb-2">
            <input
              type="tel"
              value={phone}
              onChange={(e) => updatePhoneNumber(index, e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              placeholder="Phone number"
            />
            {formData.phoneNumbers.length > 1 && (
              <button
                type="button"
                onClick={() => removePhoneNumber(index)}
                className="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
        ))}
        <button
          type="button"
          onClick={addPhoneNumber}
          className="text-sm text-slate-600 hover:text-slate-800 dark:text-slate-400 dark:hover:text-slate-300 flex items-center"
        >
          <Plus className="h-4 w-4 mr-1" />
          Add another phone number
        </button>
      </div>
    </div>
  );

  const renderStep4 = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">
        Review & Submit
      </h3>

      <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg space-y-4">
        <div>
          <h4 className="font-medium text-slate-800 dark:text-slate-200">Treaty Information</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Name: {formData.treatyName}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Treaty Types: {formData.treatyTypes.length}
          </p>
          <p className="text-sm text-green-600 dark:text-green-400 font-medium">
            Total Cost: ${formData.treatyTypes.reduce((total, tt) => total + (tt.treatyType.price || 0), 0).toFixed(2)} USD
          </p>
        </div>

        {/* Treaty Types Summary */}
        <div>
          <h4 className="font-medium text-slate-800 dark:text-slate-200 mb-3">Treaty Types & Business Details</h4>
          <div className="space-y-4">
            {formData.treatyTypes.map((treatyTypeEntry, index) => (
              <div key={treatyTypeEntry.treatyTypeId} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h5 className="font-medium text-slate-700 dark:text-slate-300">
                    {treatyTypeEntry.treatyType.name}
                  </h5>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    treatyTypeEntry.completed 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  }`}>
                    {treatyTypeEntry.completed ? 'Complete' : 'Incomplete'}
                  </span>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <p><strong>Business:</strong> {treatyTypeEntry.businessDetails.businessName || 'Not specified'}</p>
                  <p><strong>Email:</strong> {treatyTypeEntry.businessDetails.businessEmail || 'Not specified'}</p>
                  <p><strong>Cost:</strong> ${treatyTypeEntry.treatyType.price || 0} {treatyTypeEntry.treatyType.currency || 'USD'}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h4 className="font-medium text-slate-800 dark:text-slate-200">Contact Information</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Email: {formData.email}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Phone: {formData.phoneNumbers.filter(p => p.trim()).join(', ')}
          </p>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
          <FileText className="h-4 w-4 mr-2" />
          Additional Attachments
        </label>
        <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md dark:border-gray-600">
          <div className="space-y-1 text-center">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <div className="flex text-sm text-gray-600 dark:text-gray-400">
              <label className="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-slate-600 hover:text-slate-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-slate-500">
                <span>Upload files</span>
                <input
                  type="file"
                  className="sr-only"
                  multiple
                  onChange={(e) => handleFileUpload(e.target.files, 'attachments')}
                />
              </label>
              <p className="pl-1">or drag and drop</p>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Any file type up to 10MB each</p>
          </div>
        </div>
        
        {formData.attachments.length > 0 && (
          <div className="mt-4">
            <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Attached Files:
            </h5>
            <ul className="space-y-2">
              {formData.attachments.map((file, index) => (
                <li key={index} className="flex items-center justify-between bg-white dark:bg-gray-700 p-2 rounded border">
                  <span className="text-sm text-gray-600 dark:text-gray-400">{file.name}</span>
                  <button
                    type="button"
                    onClick={() => removeAttachment(index)}
                    className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {renderStepIndicator()}

      <div className="min-h-96">
        {step === 1 && renderStep1()}
        {step === 2 && renderStep2()}
        {step === 3 && renderStep3()}
        {step === 4 && renderStep4()}
      </div>

      <div className="flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={prevStep}
          disabled={step === 1}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>

        {step < 4 ? (
          <button
            type="button"
            onClick={nextStep}
disabled={false}
            className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        ) : (
          <button
            type="button"
            onClick={handleSubmit}
disabled={loading}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating Treaty...
              </>
            ) : (
              'Create Treaty'
            )}
          </button>
        )}
      </div>
    </div>
  );
};