'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Search, X, Clock } from 'lucide-react';
import { TreatyApplicationManagement } from './treaties/TreatyApplicationManagement';
import { UserAuditLog } from './UserAuditLog';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';

interface Title {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
}

interface Position {
  id: string;
  title: string;
  description: string | null;
  level: number;
  parentId: string | null;
  isActive: boolean;
}

export const UpdateUserTab: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [titles, setTitles] = useState<Title[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [filteredPositions, setFilteredPositions] = useState<Position[]>([]);
  const [treatyTypes, setTreatyTypes] = useState<Array<{ id: string, name: string }>>([]);
  const [initialLoading, setInitialLoading] = useState(true);
  
  // Address-related state
  const [countries, setCountries] = useState<Array<{ id: string, name: string }>>([]);
  const [cities, setCities] = useState<Array<{ id: string, name: string }>>([]);
  const [regions, setRegions] = useState<Array<{ id: string, name: string, code: string }>>([]);
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [showCityDropdown, setShowCityDropdown] = useState(false);
  const [showRegionDropdown, setShowRegionDropdown] = useState(false);
  const [countrySearch, setCountrySearch] = useState('');
  const [citySearch, setCitySearch] = useState('');
  
  // Address formatting state
  const [addressFormat, setAddressFormat] = useState<any>(null);
  const [selectedCountryId, setSelectedCountryId] = useState<string>('');
  const [activeTab, setActiveTab] = useState('basic');

  const formatDate = (value?: string | null): string | null => {
    if (!value) {
      return null;
    }

    const date = new Date(value);

    if (Number.isNaN(date.getTime())) {
      return null;
    }

    return date.toLocaleDateString();
  };

  // Fetch titles and positions on component mount
  useEffect(() => {
    Promise.all([
      fetchTitles(),
      fetchPositions(),
      fetchTreatyTypes()
    ]).finally(() => {
      setInitialLoading(false);
    });
  }, []);

  const fetchTitles = async () => {
    try {
      const response = await fetch('/api/positions/titles');
      
      if (!response.ok) {
        if (response.status === 403) {
          // User doesn't have admin privileges, show appropriate message
          toast.info('Title selection is only available to administrators');
          return;
        }
        throw new Error('Failed to fetch titles');
      }
      
      const data = await response.json();
      setTitles(data);
    } catch (error) {
      console.error('Error fetching titles:', error);
      toast.error('Failed to load titles');
    }
  };

  const fetchPositions = async () => {
    try {
      const response = await fetch('/api/positions/positions');

      if (!response.ok) {
        if (response.status === 403) {
          // User doesn't have admin privileges, show appropriate message
          toast.info('Position selection is only available to administrators');
          return;
        }
        throw new Error('Failed to fetch positions');
      }

      const data = await response.json();
      // The API returns { positions: [...], pagination: {...} }
      // Extract the positions array from the response
      const positionsArray = data.positions || [];
      setPositions(positionsArray);
    } catch (error) {
      console.error('Error fetching positions:', error);
      toast.error('Failed to load positions');
    }
  };

  const fetchTreatyTypes = async () => {
    try {
      const response = await fetch('/api/treaty-types');

      if (!response.ok) {
        if (response.status === 404) {
          // No treaty types found
          setTreatyTypes([]);
          return;
        }
        throw new Error('Failed to fetch treaty types');
      }

      const data = await response.json();
      setTreatyTypes(data.treatyTypes || []);
    } catch (error) {
      console.error('Error fetching treaty types:', error);
      setTreatyTypes([]);
      toast.error('Failed to load treaty types');
    }
  };

  
  // Fetch positions associated with a specific title
  const fetchPositionsForTitle = async (titleId: string) => {
    try {
      const response = await fetch(`/api/positions/positions?titleId=${titleId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch positions for title');
      }

      const data = await response.json();
      // The API returns { positions: [...], pagination: {...} }
      // Extract the positions array from the response
      const positionsArray = data.positions || [];
      setFilteredPositions(positionsArray);
    } catch (error) {
      console.error('Error fetching positions for title:', error);
      toast.error('Failed to load positions for selected title');
    }
  };

  // Fetch regions when country is selected
  const fetchRegions = async (countryId: string) => {
    try {
      const response = await fetch(`/api/regions?countryId=${encodeURIComponent(countryId)}`);
      if (response.ok) {
        const data = await response.json();
        setRegions(data);
      } else {
        setRegions([]);
      }
    } catch (error) {
      console.error('Error fetching regions:', error);
      setRegions([]);
    }
  };

  // Fetch address format for a country
  const fetchAddressFormat = async (countryId: string) => {
    try {
      const response = await fetch(`/api/countries/${countryId}/address-format`);
      if (response.ok) {
        const data = await response.json();
        setAddressFormat(data);
      } else {
        setAddressFormat(null);
      }
    } catch (error) {
      console.error('Error fetching address format:', error);
      setAddressFormat(null);
    }
  };

  const buildSelectedUser = (user: any) => {
    const profile = user?.profile || {};
    const userPositions = Array.isArray(user?.userPositions) ? user.userPositions : [];
    const primaryPosition = userPositions.find((assignment: any) => assignment?.isActive) || userPositions[0];

    return {
      id: user.id,
      firstName: profile.firstName || '',
      surname: profile.lastName || '',
      email: user.email || '',
      position: primaryPosition?.position?.title || profile.title?.name || 'User',
      streetAddress1: profile.streetAddress1 || profile.streetAddress || '',
      streetAddress2: profile.streetAddress2 || '',
      town: profile.town || '',
      country: profile.country?.name || '',
      city: profile.city?.name || '',
      postalCode: profile.postalCode || '',
      regionId: profile.regionId ? String(profile.regionId) : '',
      regionText: profile.regionText || '',
      phone: profile.phone || '',
      mobile: profile.mobile || '',
      dob: profile.dateOfBirth || '',
      peaceAmbassadorNumber: profile.peaceAmbassadorNumber || '',
      bio: profile.bio || '',
      titleId: profile.titleId || '',
      positionId: primaryPosition?.positionId || '',
      userPositions,
    };
  };

  const loadUserDetails = async (userId: string) => {
    try {
      const userResponse = await fetch(`/api/users/${userId}`);

      if (!userResponse.ok) {
        if (userResponse.status === 404) {
          toast.error('User not found');
        } else {
          toast.error('Failed to fetch user details');
        }
        return null;
      }

      const userData = await userResponse.json();
      const user = userData.user;

      if (!user) {
        toast.error('User not found');
        return null;
      }

      const userWithTitles = buildSelectedUser(user);
      setSelectedUser(userWithTitles);
      setActiveTab('contact');

      const countryId = user?.profile?.country?.id ? String(user.profile.country.id) : '';

      if (countryId) {
        setSelectedCountryId(countryId);
        fetchRegions(countryId);
        fetchAddressFormat(countryId);
      } else {
        setSelectedCountryId('');
        setRegions([]);
        setAddressFormat(null);
      }

      if (userWithTitles.titleId) {
        fetchPositionsForTitle(userWithTitles.titleId);
      } else {
        setFilteredPositions([]);
      }

      return userWithTitles;
    } catch (error) {
      console.error('Error loading user details:', error);
      toast.error('Failed to fetch user details');
      return null;
    }
  };

  // Fetch user suggestions from API
  useEffect(() => {
    if (searchQuery.length > 2) {
      const fetchSuggestions = async () => {
        try {
          const response = await fetch(`/api/users?search=${encodeURIComponent(searchQuery)}&limit=5`);

          if (!response.ok) {
            if (response.status === 404) {
              // No users found
              setSuggestions([]);
              setShowSuggestions(false);
              return;
            }
            throw new Error('Failed to fetch user suggestions');
          }

          const data = await response.json();
          const suggestions = (data.users || []).map((user: any) => ({
            id: user.id,
            name: `${user.firstName || ''} ${user.surname || ''}`.trim(),
            email: user.email,
            position: user.position || 'User',
            // Include all the fields needed for form population
            firstName: user.firstName || '',
            surname: user.surname || '',
            streetAddress1: user.streetAddress1 || '',
            streetAddress2: user.streetAddress2 || '',
            town: user.town || '',
            country: user.country || '',
            city: user.city || '',
            postalCode: user.postalCode || '',
            regionId: user.regionId || '',
            regionText: user.regionText || '',
            phone: user.phone || '',
            mobile: user.mobile || '',
            dob: user.dob || '',
            peaceAmbassadorNumber: user.peaceAmbassadorNumber || '',
            bio: user.bio || '',
            titleId: user.titleId || '',
            positionId: user.positionId || '',
          }));

          setSuggestions(suggestions);
          setShowSuggestions(suggestions.length > 0);
        } catch (error) {
          console.error('Error fetching user suggestions:', error);
          setSuggestions([]);
          setShowSuggestions(false);
        }
      };

      const timeoutId = setTimeout(fetchSuggestions, 300); // Debounce
      return () => clearTimeout(timeoutId);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [searchQuery]);

  // Fetch countries when country search changes
  useEffect(() => {
    if (countrySearch.length >= 2) {
      const fetchCountries = async () => {
        try {
          const response = await fetch(`/api/countries?q=${encodeURIComponent(countrySearch)}`);
          if (response.ok) {
            const data = await response.json();
            setCountries(data);
            setShowCountryDropdown(true);
          }
        } catch (error) {
          console.error('Error fetching countries:', error);
        }
      };

      const timeoutId = setTimeout(fetchCountries, 300); // Debounce
      return () => clearTimeout(timeoutId);
    } else {
      setShowCountryDropdown(false);
    }
  }, [countrySearch]);

  // Fetch cities when city search changes and country is selected
  useEffect(() => {
    if (citySearch.length >= 2 && selectedUser?.country) {
      const fetchCities = async () => {
        try {
          const response = await fetch(`/api/cities?q=${encodeURIComponent(citySearch)}&countryId=${encodeURIComponent(selectedUser.country)}`);
          if (response.ok) {
            const data = await response.json();
            setCities(data);
            setShowCityDropdown(true);
          }
        } catch (error) {
          console.error('Error fetching cities:', error);
        }
      };

      const timeoutId = setTimeout(fetchCities, 300); // Debounce
      return () => clearTimeout(timeoutId);
    } else {
      setShowCityDropdown(false);
    }
  }, [citySearch, selectedUser?.country]);

  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    if (activeTab === 'basic') {
      return;
    }

    const selector = `[data-testid="users-update-section-${activeTab}"]`;
    const activeSection = document.querySelector(selector) as HTMLElement | null;
    const scroller = document.querySelector('[data-testid="main-content"]') as HTMLElement | null;

    if (activeSection && scroller) {
      const scrollerRect = scroller.getBoundingClientRect();
      const sectionRect = activeSection.getBoundingClientRect();
      const offset = sectionRect.top - scrollerRect.top + scroller.scrollTop - 96;

      scroller.scrollTo({
        top: Math.max(offset, 0),
        behavior: 'auto',
      });
      scroller.scrollLeft = 0;
    }
  }, [activeTab]);

  const handleSearch = async (searchQuery: string = '') => {
    console.log('handleSearch called with:', searchQuery);
    setSearchQuery(searchQuery);
    setShowSuggestions(false);

    if (searchQuery) {
      setLoading(true);
      try {
        // Find the user from suggestions or fetch directly
        let foundUser = suggestions.find(user =>
          user.name.toLowerCase() === searchQuery.toLowerCase() ||
          user.email.toLowerCase() === searchQuery.toLowerCase()
        );

        if (!foundUser) {
          // If not in suggestions, try to fetch the user directly
          const response = await fetch(`/api/users?search=${encodeURIComponent(searchQuery)}&limit=1`);

          if (response.ok) {
            const data = await response.json();
            if (data.users && data.users.length > 0) {
              const user = data.users[0];
              foundUser = {
                id: user.id,
                name: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
                email: user.email,
                position: user.position || 'User'
              };
            }
          }
        }

        if (foundUser) {
          await loadUserDetails(foundUser.id);
        } else {
          toast.error('User not found');
        }
      } catch (error) {
        console.error('Error searching for user:', error);
        toast.error('Failed to search for user');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSuggestionClick = async (user: any) => {
    setSearchQuery(user.name);
    setShowSuggestions(false);
    setLoading(true);

    try {
      await loadUserDetails(user.id);
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setSearchQuery('');
    setSelectedUser(null);
    setSuggestions([]);
    setShowSuggestions(false);
  };

  const handleUserChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (selectedUser) {
      // If changing the title, fetch associated positions
      if (name === 'titleId') {
        setSelectedUser({
          ...selectedUser,
          [name]: value,
          positionId: ''
        });
        if (value) {
          fetchPositionsForTitle(value);
        } else {
          setFilteredPositions([]);
        }
      } else {
        setSelectedUser({
          ...selectedUser,
          [name]: value
        });
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedUser) {
      toast.error('Please select a user to update.');
      return;
    }
    
    // Validate required fields
    if (!selectedUser.firstName || !selectedUser.surname || !selectedUser.email) {
      toast.error('Please fill in all required fields (First Name, Surname, Email).');
      return;
    }

    // Validate country-specific required fields based on address format
    if (addressFormat && addressFormat.format) {
      const missingFields = [];
      
      for (const field of addressFormat.format) {
        if (field.required) {
          const fieldValue = selectedUser[field.field as keyof typeof selectedUser];
          if (!fieldValue || fieldValue === '') {
            missingFields.push(field.label);
          }
        }
      }
      
      if (missingFields.length > 0) {
        toast.error(`Please fill in the following required fields: ${missingFields.join(', ')}`);
        return;
      }
    }
    
    setUpdating(true);
    
    try {
      // Prepare user data for API submission
      const userData = {
        firstName: selectedUser.firstName,
        surname: selectedUser.surname,
        email: selectedUser.email,
        phone: selectedUser.phone || undefined,
        mobile: selectedUser.mobile || undefined,
        streetAddress1: selectedUser.streetAddress1 || undefined,
        streetAddress2: selectedUser.streetAddress2 || undefined,
        town: selectedUser.town || undefined,
        country: selectedUser.country || undefined,
        city: selectedUser.city || undefined,
        postalCode: selectedUser.postalCode || undefined,
        regionId: selectedUser.regionId || undefined,
        regionText: selectedUser.regionText || undefined,
        dob: selectedUser.dob || undefined,
        peaceAmbassadorNumber: selectedUser.peaceAmbassadorNumber || undefined,
        bio: selectedUser.bio || undefined,
        titleId: selectedUser.titleId || undefined,
        positionId: selectedUser.positionId || undefined,
      };

      // Send data to the API
      const response = await fetch(`/api/users/${selectedUser.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update user');
      }

      const result = await response.json();
      toast.success('User updated successfully!');
    } catch (error: any) {
      toast.error(error.message || 'Failed to update user');
      console.error('Error updating user:', error);
    } finally {
      setUpdating(false);
    }
  };

  const handleSubmitForVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedUser) {
      toast.error('Please select a user to update.');
      return;
    }
    
    try {
      // In a real implementation, you would send the update request for admin verification
      // For now, we'll just show a success message
      toast.success('Update request submitted for admin verification!');
    } catch (error: any) {
      toast.error(error.message || 'Failed to submit update request');
      console.error('Error submitting update request:', error);
    }
  };

  // Get positions to display (filtered by title or all positions)
  const displayPositions = selectedUser?.titleId ? filteredPositions : positions;

  if (initialLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <label htmlFor="userSearch" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Search User
        </label>
        <div className="relative">
          <form
            onSubmit={(e) => { e.preventDefault(); handleSearch(); }}
            className="relative"
            data-testid="users-update-search-form"
          >
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              id="userSearch"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => searchQuery.length > 2 && setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              className="block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              placeholder="Search by name or email..."
              data-testid="users-update-search-input"
            />
            {searchQuery && (
              <button
                type="button"
                onClick={handleClear}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <X className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
              </button>
            )}
          </form>

          {showSuggestions && suggestions.length > 0 && (
            <div
              className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md overflow-hidden"
              data-testid="users-update-suggestions"
            >
              <ul className="py-1">
                {suggestions.map((user) => (
                  <li
                    key={user.id}
                    onMouseDown={() => handleSuggestionClick(user)}
                    className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                    data-testid={`users-update-suggestion-${user.id}`}
                  >
                    <div className="flex justify-between">
                      <span className="font-medium">{user.name}</span>
                      <span className="text-gray-500 dark:text-gray-400">{user.position}</span>
                    </div>
                    <div className="text-gray-500 dark:text-gray-400">{user.email}</div>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      {selectedUser && (
        <div className="space-y-6">
          <div className="border-b border-gray-200 dark:border-gray-700 pb-4" data-testid="users-update-selected-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <Clock className="h-5 w-5 mr-2 text-blue-600" />
              Update User: {selectedUser.firstName} {selectedUser.surname}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              ID: {selectedUser.id} | Position: {selectedUser.position}
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6" data-testid="users-update-form">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-7">
                <TabsTrigger value="basic" data-testid="users-update-tab-basic">Basic Info</TabsTrigger>
                <TabsTrigger value="contact" data-testid="users-update-tab-contact">Contact</TabsTrigger>
                <TabsTrigger value="identification" data-testid="users-update-tab-identification">ID</TabsTrigger>
                <TabsTrigger value="positions" data-testid="users-update-tab-positions">Positions</TabsTrigger>
                <TabsTrigger value="treaties" data-testid="users-update-tab-treaties">Treaties</TabsTrigger>
                <TabsTrigger value="ordinances" data-testid="users-update-tab-ordinances">Ordinances</TabsTrigger>
                <TabsTrigger value="audit" data-testid="users-update-tab-audit">Audit Log</TabsTrigger>
              </TabsList>
            {/* Basic Info Tab */}
            <TabsContent value="basic" className="space-y-4 scroll-mt-24" forceMount data-testid="users-update-section-basic">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    First Name
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={selectedUser.firstName}
                    onChange={handleUserChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                
                <div>
                  <label htmlFor="surname" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Surname
                  </label>
                  <input
                    type="text"
                    id="surname"
                    name="surname"
                    value={selectedUser.surname}
                    onChange={handleUserChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                
                <div>
                  <label htmlFor="dob" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Date of Birth
                  </label>
                  <input
                    type="date"
                    id="dob"
                    name="dob"
                    value={selectedUser.dob}
                    onChange={handleUserChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={selectedUser.email}
                    onChange={handleUserChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="bio" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Bio
                  </label>
                  <textarea
                    id="bio"
                    name="bio"
                    value={selectedUser.bio}
                    onChange={handleUserChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
            </TabsContent>

            {/* Contact Tab */}
            <TabsContent value="contact" className="space-y-4 scroll-mt-24" forceMount data-testid="users-update-section-contact">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Phone
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={selectedUser.phone}
                    onChange={handleUserChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                
                <div>
                  <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Mobile
                  </label>
                  <input
                    type="tel"
                    id="mobile"
                    name="mobile"
                    value={selectedUser.mobile}
                    onChange={handleUserChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                
                <div className="md:col-span-2">
                  <label htmlFor="streetAddress1" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {addressFormat?.format?.find((f: any) => f.field === 'streetAddress1')?.label || 'Street Address'}
                  </label>
                  <input
                    type="text"
                    id="streetAddress1"
                    name="streetAddress1"
                    value={selectedUser.streetAddress1}
                    onChange={handleUserChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    placeholder="123 Main Street"
                    required={addressFormat?.format?.find((f: any) => f.field === 'streetAddress1')?.required || false}
                  />
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="streetAddress2" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {addressFormat?.format?.find((f: any) => f.field === 'streetAddress2')?.label || 'Apt/Suite/Unit'} <span className="text-gray-500">(Optional)</span>
                  </label>
                  <input
                    type="text"
                    id="streetAddress2"
                    name="streetAddress2"
                    value={selectedUser.streetAddress2}
                    onChange={handleUserChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Apt 4B, Suite 200, Unit 5"
                  />
                </div>

                <div>
                  <label htmlFor="town" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {addressFormat?.format?.find((f: any) => f.field === 'town')?.label || 'Town'}
                  </label>
                  <input
                    type="text"
                    id="town"
                    name="town"
                    value={selectedUser.town}
                    onChange={handleUserChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    required={addressFormat?.format?.find((f: any) => f.field === 'town')?.required || false}
                  />
                </div>

                <div>
                  <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Country
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="country"
                      value={selectedUser.country}
                      onChange={(e) => {
                        const value = e.target.value;
                        setSelectedUser((prev: any) => prev ? ({ ...prev, country: value }) : null);
                        setCountrySearch(value);
                        setSelectedUser((prev: any) => prev ? ({ ...prev, city: '' }) : null);
                        setCitySearch('');
                      }}
                      onFocus={() => countrySearch.length >= 2 && setShowCountryDropdown(true)}
                      onBlur={() => setTimeout(() => setShowCountryDropdown(false), 200)}
                      placeholder="Search for a country..."
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                      autoComplete="off"
                    />
                    {showCountryDropdown && countries.length > 0 && (
                      <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md py-1 max-h-60 overflow-auto">
                        {countries.map((country) => (
                          <div
                            key={country.id}
                            className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                            onMouseDown={() => {
                              setSelectedUser((prev: any) => prev ? ({ ...prev, country: country.name, regionId: '', regionText: '' }) : null);
                              setSelectedCountryId(country.id);
                              setCountrySearch('');
                              setShowCountryDropdown(false);
                              fetchRegions(country.id);
                              fetchAddressFormat(country.id);
                            }}
                          >
                            {country.name}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label htmlFor="city" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    City
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="city"
                      value={selectedUser.city}
                      onChange={(e) => {
                        const value = e.target.value;
                        setSelectedUser((prev: any) => prev ? ({ ...prev, city: value }) : null);
                        setCitySearch(value);
                      }}
                      onFocus={() => citySearch.length >= 2 && selectedUser?.country && setShowCityDropdown(true)}
                      onBlur={() => setTimeout(() => setShowCityDropdown(false), 200)}
                      placeholder="Search for a city..."
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                      autoComplete="off"
                      disabled={!selectedUser?.country}
                    />
                    {showCityDropdown && cities.length > 0 && (
                      <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md py-1 max-h-60 overflow-auto">
                        {cities.map((city) => (
                          <div
                            key={city.id}
                            className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                            onMouseDown={() => {
                              setSelectedUser((prev: any) => prev ? ({ ...prev, city: city.name }) : null);
                              setCitySearch('');
                              setShowCityDropdown(false);
                            }}
                          >
                            {city.name}
                          </div>
                        ))}
                      </div>
                    )}
                    {selectedUser?.country && (
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Showing cities in {selectedUser.country}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <label htmlFor="region" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {addressFormat?.format?.find((f: any) => f.field === 'regionText')?.label || 'Region'}
                  </label>
                  {regions.length > 0 ? (
                    <select
                      id="region"
                      name="regionId"
                      value={selectedUser.regionId}
                      onChange={handleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                      disabled={!selectedCountryId}
                      required={addressFormat?.format?.find((f: any) => f.field === 'regionText')?.required || false}
                    >
                      <option value="">Select a region</option>
                      {regions.map((region) => (
                        <option key={region.id} value={region.id}>
                          {region.name}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      id="regionText"
                      name="regionText"
                      value={selectedUser.regionText}
                      onChange={handleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                      placeholder={`Enter ${addressFormat?.format?.find((f: any) => f.field === 'regionText')?.label?.toLowerCase() || 'region'}`}
                      disabled={!selectedCountryId}
                      required={addressFormat?.format?.find((f: any) => f.field === 'regionText')?.required || false}
                    />
                  )}
                  {selectedCountryId && regions.length === 0 && (
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      No regions available for this country. Please enter manually.
                    </p>
                  )}
                </div>

                <div>
                  <label htmlFor="postalCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {addressFormat?.format?.find((f: any) => f.field === 'postalCode')?.label || 'Postal Code'}
                  </label>
                  <input
                    type="text"
                    id="postalCode"
                    name="postalCode"
                    value={selectedUser.postalCode}
                    onChange={handleUserChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    required={addressFormat?.format?.find((f: any) => f.field === 'postalCode')?.required || false}
                    placeholder={addressFormat?.example ? `e.g., ${addressFormat.example.split(', ').pop()}` : 'Enter postal code'}
                  />
                </div>
              </div>
            </TabsContent>

            {/* Identification Tab */}
            <TabsContent value="identification" className="space-y-4 scroll-mt-24" forceMount data-testid="users-update-section-identification">
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label htmlFor="peaceAmbassadorNumber" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Peace Ambassador Number
                  </label>
                  <input
                    type="text"
                    id="peaceAmbassadorNumber"
                    name="peaceAmbassadorNumber"
                    value={selectedUser.peaceAmbassadorNumber}
                    onChange={handleUserChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Identification documents and treaty numbers are managed through the treaties system.
                </div>
              </div>
            </TabsContent>

            {/* Positions Tab */}
            <TabsContent value="positions" className="space-y-4 scroll-mt-24" forceMount data-testid="users-update-section-positions">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="titleId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Title
                  </label>
                  <select
                    id="titleId"
                    name="titleId"
                    value={selectedUser.titleId}
                    onChange={handleUserChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Select Title</option>
                    {titles
                      .filter(title => title.isActive)
                      .map(title => (
                        <option key={title.id} value={title.id}>
                          {title.name}
                        </option>
                      ))}
                  </select>
                </div>
                
                <div>
                  <label htmlFor="positionId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Position
                  </label>
                  <select
                    id="positionId"
                    name="positionId"
                    value={selectedUser.positionId}
                    onChange={handleUserChange}
                    disabled={!selectedUser.titleId}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white disabled:opacity-50"
                  >
                    <option value="">{selectedUser.titleId ? 'Select Position' : 'Select a title first'}</option>
                    {displayPositions
                      .filter(position => position.isActive)
                      .map(position => (
                        <option key={position.id} value={position.id}>
                          {position.title}
                        </option>
                      ))}
                  </select>
                  {selectedUser.titleId && displayPositions.length === 0 && (
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      No positions are associated with the selected title.
                    </p>
                  )}
                </div>
              </div>

              {selectedUser && (
                selectedUser.userPositions?.length ? (
                  <div className="rounded-lg border border-slate-200 bg-white p-4 shadow-sm dark:border-slate-700 dark:bg-slate-800">
                    <h4 className="text-sm font-semibold text-slate-900 dark:text-slate-100">
                      Current Positions
                    </h4>
                    <ul className="mt-3 space-y-3">
                      {selectedUser.userPositions.map((assignment: any) => {
                        const startDate = formatDate(assignment?.startDate);
                        const endDate = formatDate(assignment?.endDate);

                        return (
                          <li
                            key={assignment.id}
                            className="rounded-md bg-slate-50 p-3 text-sm dark:bg-slate-900/60"
                          >
                            <div className="font-medium text-slate-900 dark:text-slate-100">
                              {assignment?.position?.title || 'Position'}
                            </div>
                            <div className="text-xs text-slate-500 dark:text-slate-400">
                              {assignment?.isActive ? 'Active' : 'Inactive'}
                              {startDate ? ` • Since ${startDate}` : ''}
                              {endDate ? ` • Until ${endDate}` : ''}
                            </div>
                            {assignment?.position?.description && (
                              <div className="mt-2 text-xs text-slate-500 dark:text-slate-400">
                                {assignment.position.description}
                              </div>
                            )}
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    No positions currently assigned to this user.
                  </p>
                )
              )}
            </TabsContent>

            {/* Treaties Tab */}
            <TabsContent value="treaties" className="space-y-4 scroll-mt-24" forceMount data-testid="users-update-section-treaties">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Treaty Management
                </label>
                <TreatyApplicationManagement
                  userId={selectedUser.id}
                  userName={`${selectedUser.firstName} ${selectedUser.surname}`}
                  peaceAmbassadorNumber={selectedUser.peaceAmbassadorNumber}
                />
              </div>
            </TabsContent>

            {/* Ordinances Tab */}
            <TabsContent value="ordinances" className="space-y-4 scroll-mt-24" forceMount data-testid="users-update-section-ordinances">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Ordinance assignments are managed through separate ordinance administration interfaces.
              </div>
            </TabsContent>

            {/* Audit Log Tab */}
            <TabsContent value="audit" className="space-y-4 scroll-mt-24" forceMount data-testid="users-update-section-audit">
              <UserAuditLog 
                userId={selectedUser.id} 
                userName={`${selectedUser.firstName} ${selectedUser.surname}`} 
              />
            </TabsContent>
          </Tabs>
          
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleSubmitForVerification}
              className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
              data-testid="users-update-submit-verification"
            >
              Submit for Verification
            </button>
            <button
              type="submit"
              disabled={updating}
              className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 transition-colors duration-200"
              data-testid="users-update-submit"
            >
              {updating ? 'Updating...' : 'Update User'}
            </button>
          </div>
        </form>
        </div>
      )}
    </div>
  );
};
