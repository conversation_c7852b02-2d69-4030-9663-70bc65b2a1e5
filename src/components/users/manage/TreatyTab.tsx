'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { FileText, Edit, Plus, Search, Calendar, File, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';

interface Treaty {
  id: string;
  treatyTypeId: string;
  title: string;
  status: string;
  notes: string;
  signedDate: string;
  expirationDate: string;
  renewalDate: string;
  createdAt: string;
  updatedAt: string;
}

interface Pagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  pageSize: number;
}

export const TreatyTab: React.FC = () => {
  const [treaties, setTreaties] = useState<Treaty[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [editingTreaty, setEditingTreaty] = useState<Treaty | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [pagination, setPagination] = useState<Pagination>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 10
  });
  const [autocompleteSuggestions, setAutocompleteSuggestions] = useState<string[]>([]);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });

  const fetchTreaties = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.currentPage.toString(),
        limit: pagination.pageSize.toString(),
        search: searchQuery
      });

      const response = await fetch(`/api/treaties?${params}`);
      if (response.ok) {
        const data = await response.json();
        setTreaties(data.treaties);
        setPagination(prev => ({
          ...prev,
          totalPages: data.pagination.totalPages,
          totalCount: data.pagination.totalCount
        }));
      }
    } catch (error) {
      console.error('Error fetching treaties:', error);
      toast.error('Failed to load treaties');
    } finally {
      setLoading(false);
    }
  }, [pagination.currentPage, pagination.pageSize, searchQuery]);

  useEffect(() => {
    fetchTreaties();
  }, [pagination.currentPage, searchQuery, fetchTreaties]);

  const handleCreateTreaty = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error('Please enter a treaty name');
      return;
    }

    try {
      const response = await fetch('/api/treaties', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success('Treaty created successfully');
        setFormData({
          name: '',
          description: ''
        });
        setShowCreateForm(false);
        fetchTreaties();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create treaty');
      }
    } catch (error) {
      console.error('Error creating treaty:', error);
      toast.error('Failed to create treaty');
    }
  };

  const handleEditTreaty = (treaty: Treaty) => {
    setEditingTreaty(treaty);
    setFormData({
      name: treaty.title,
      description: treaty.notes || ''
    });
  };

  const handleUpdateTreaty = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editingTreaty) return;

    try {
      const response = await fetch(`/api/treaties?id=${editingTreaty.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success('Treaty updated successfully');
        setEditingTreaty(null);
        setFormData({
          name: '',
          description: ''
        });
        fetchTreaties();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to update treaty');
      }
    } catch (error) {
      console.error('Error updating treaty:', error);
      toast.error('Failed to update treaty');
    }
  };

  const handleDeleteTreaty = async (treaty: Treaty) => {
    if (!confirm(`Are you sure you want to delete "${treaty.title}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/treaties?id=${treaty.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success('Treaty deleted successfully');
        fetchTreaties();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to delete treaty');
      }
    } catch (error) {
      console.error('Error deleting treaty:', error);
      toast.error('Failed to delete treaty');
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page on search

    // Generate autocomplete suggestions based on existing treaties
    if (query.length > 0) {
      const suggestions = treaties
        .filter(treaty =>
          treaty.title.toLowerCase().includes(query.toLowerCase()) ||
          treaty.notes.toLowerCase().includes(query.toLowerCase())
        )
        .map(treaty => treaty.title)
        .slice(0, 5); // Limit to 5 suggestions
      setAutocompleteSuggestions([...new Set(suggestions)]); // Remove duplicates
    } else {
      setAutocompleteSuggestions([]);
    }
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchTreaties();
  };


  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-700"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-slate-800 dark:text-slate-200">
          Treaty Management
        </h2>
        <button
          onClick={() => setShowCreateForm(true)}
          className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200 flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Treaty
        </button>
      </div>

      {/* Create/Edit Form */}
      {(showCreateForm || editingTreaty) && (
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">
            {editingTreaty ? 'Edit Treaty' : 'Create New Treaty'}
          </h3>

          <form onSubmit={editingTreaty ? handleUpdateTreaty : handleCreateTreaty} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                Treaty Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
                placeholder="Enter treaty name..."
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
                placeholder="Enter treaty description..."
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => {
                  setShowCreateForm(false);
                  setEditingTreaty(null);
                  setFormData({
                    name: '',
                    description: ''
                  });
                }}
                className="px-4 py-2 border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 rounded-md hover:bg-slate-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
              >
                {editingTreaty ? 'Update Treaty' : 'Create Treaty'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Search */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
        <form onSubmit={handleSearchSubmit} className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-slate-400" />
          </div>
          <input
            type="text"
            placeholder="Search treaties..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="w-full pl-10 pr-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
          />
          {autocompleteSuggestions.length > 0 && (
            <div className="absolute z-10 w-full bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-md shadow-lg mt-1">
              {autocompleteSuggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className="px-3 py-2 hover:bg-slate-100 dark:hover:bg-slate-600 cursor-pointer"
                  onClick={() => {
                    setSearchQuery(suggestion);
                    setAutocompleteSuggestions([]);
                    fetchTreaties();
                  }}
                >
                  {suggestion}
                </div>
              ))}
            </div>
          )}
        </form>
      </div>

      {/* Treaties List */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
        <div className="p-6">
          <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">
            Treaties ({pagination.totalCount})
          </h3>

          {treaties.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="mx-auto h-12 w-12 text-slate-400" />
              <h3 className="mt-2 text-lg font-medium text-slate-800 dark:text-slate-200">
                No treaties found
              </h3>
              <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                {searchQuery ? 'Try adjusting your search terms.' : 'Create your first treaty to get started.'}
              </p>
            </div>
          ) : (
            <>
              <div className="space-y-4">
                {treaties.map((treaty) => (
                  <div key={treaty.id} className="border border-slate-300 dark:border-slate-600 rounded-md p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-medium text-slate-800 dark:text-slate-200">
                          {treaty.title}
                        </h4>
                        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                          {treaty.notes || 'No description'}
                        </p>

                        <div className="flex items-center space-x-4 mt-2 text-sm text-slate-500 dark:text-slate-400">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            Created: {new Date(treaty.createdAt).toLocaleDateString()}
                          </div>
                          <div className="flex items-center">
                            <File className="h-4 w-4 mr-1" />
                            Status: {treaty.status}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEditTreaty(treaty)}
                          className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center text-sm"
                          title="Edit treaty"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteTreaty(treaty)}
                          className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 flex items-center text-sm"
                          title="Delete treaty"
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm text-slate-600 dark:text-slate-400">
                    Showing {((pagination.currentPage - 1) * pagination.pageSize) + 1} to {Math.min(pagination.currentPage * pagination.pageSize, pagination.totalCount)} of {pagination.totalCount} treaties
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handlePageChange(pagination.currentPage - 1)}
                      disabled={pagination.currentPage === 1}
                      className="px-3 py-1 border border-slate-300 dark:border-slate-600 rounded text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </button>

                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        const pageNumber = Math.max(1, Math.min(pagination.totalPages - 4, pagination.currentPage - 2)) + i;
                        if (pageNumber > pagination.totalPages) return null;

                        return (
                          <button
                            key={pageNumber}
                            onClick={() => handlePageChange(pageNumber)}
                            className={`px-3 py-1 border rounded ${
                              pagination.currentPage === pageNumber
                                ? 'bg-slate-700 text-white border-slate-700'
                                : 'border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-700'
                            }`}
                          >
                            {pageNumber}
                          </button>
                        );
                      })}
                    </div>

                    <button
                      onClick={() => handlePageChange(pagination.currentPage + 1)}
                      disabled={pagination.currentPage === pagination.totalPages}
                      className="px-3 py-1 border border-slate-300 dark:border-slate-600 rounded text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};