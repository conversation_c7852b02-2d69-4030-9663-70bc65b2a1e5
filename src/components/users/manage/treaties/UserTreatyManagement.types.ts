export interface Treaty {
  id: string;
  name: string;
  status: 'ACTIVE' | 'EXPIRED' | 'TERMINATED' | 'PENDING_RENEWAL';
  signedDate: string | null;
  expirationDate: string | null;
  renewalDate: string | null;
  treatyTypes: Array<{
    id: string;
    name: string;
  }>;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
  isTreatyNumber?: boolean;
  treatyNumber?: string;
}

export interface TreatyType {
  id: string;
  name: string;
  description: string | null;
  category: string;
  price: number;
}

export interface TreatyNumberResponse {
  id: string;
  treatyNumber: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  treatyType?: {
    id: string;
    name: string;
  };
}

export interface UserTreatyManagementProps {
  userId: string;
  userName: string;
}