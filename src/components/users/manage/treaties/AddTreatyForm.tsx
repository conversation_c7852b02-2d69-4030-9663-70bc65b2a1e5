import React, { useState, useEffect } from 'react';
import { Treaty, TreatyType } from './UserTreatyManagement.types';
import { toast } from 'sonner';

interface BusinessDetails {
  businessName: string;
  businessAddress: string;
  businessEmail: string;
  businessPhone?: string;
  businessWebsite?: string;
  businessDescription?: string;
  categoryId: string;
  subcategoryId: string;
  businessType: string; // sole trader, small business, church, trust, etc.
}

interface PersonalDetails {
  fullLegalName: string;
  email: string;
  currentCountryId: string;
  currentCityId: string;
  phoneNumbers?: string[];
  nationality?: string[];
  dateOfBirth?: string;
  genderIdentity?: string;
  residentialAddress?: string;
  identificationNumber?: string;
  peaceProtectedPremises?: boolean;
  declarationAccepted?: boolean;
}

interface TreatyTypeBusinessDetails {
  treatyTypeId: string;
  businessDetails: BusinessDetails;
  personalDetails: PersonalDetails;
}

interface NewTreatyFormData {
  selectedTreatyId: string;
  selectedTreatyTypeIds: string[];
  signedDate: string;
  expirationDate: string;
  description: string;
  businessDetailsMap: Record<string, TreatyTypeBusinessDetails>;
}

interface AddTreatyFormProps {
  showAddForm: boolean;
  setShowAddForm: (show: boolean) => void;
  userTreaties: Treaty[];
  availableTreatyTypes: TreatyType[];
  newTreaty: NewTreatyFormData;
  setNewTreaty: (data: NewTreatyFormData) => void;
  handleAddTreaty: (e: React.FormEvent) => void;
  userId?: string;
}

export const AddTreatyForm: React.FC<AddTreatyFormProps> = ({
  showAddForm,
  setShowAddForm,
  userTreaties,
  availableTreatyTypes,
  newTreaty,
  setNewTreaty,
  handleAddTreaty,
  userId,
}) => {
  const [categories, setCategories] = useState<any[]>([]);
  const [subcategories, setSubcategories] = useState<any[]>([]);
  const [countries, setCountries] = useState<any[]>([]);
  const [cities, setCities] = useState<any[]>([]);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    fetchCategories();
    fetchCountries();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(Array.isArray(data) ? data : (data.categories || []));
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchSubcategories = async (categoryId: string) => {
    try {
      const response = await fetch(`/api/subcategories?categoryId=${categoryId}`);
      if (response.ok) {
        const data = await response.json();
        setSubcategories(Array.isArray(data) ? data : (data.subcategories || []));
      }
    } catch (error) {
      console.error('Error fetching subcategories:', error);
    }
  };

  const fetchCountries = async () => {
    try {
      const response = await fetch('/api/countries');
      if (response.ok) {
        const data = await response.json();
        setCountries(Array.isArray(data) ? data : (data.countries || []));
      }
    } catch (error) {
      console.error('Error fetching countries:', error);
    }
  };

  const fetchCities = async (countryId: string) => {
    try {
      const response = await fetch(`/api/cities?countryId=${countryId}`);
      if (response.ok) {
        const data = await response.json();
        setCities(Array.isArray(data) ? data : (data.cities || []));
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
    }
  };

  const handleBusinessDetailChange = (treatyTypeId: string, field: string, value: any) => {
    const updatedBusinessDetailsMap = { ...newTreaty.businessDetailsMap };
    const currentDetails = updatedBusinessDetailsMap[treatyTypeId] || {
      treatyTypeId,
      businessDetails: {
        businessName: '',
        businessAddress: '',
        businessEmail: '',
        categoryId: '',
        subcategoryId: '',
        businessType: ''
      },
      personalDetails: {
        fullLegalName: '',
        email: '',
        currentCountryId: '',
        currentCityId: '',
        phoneNumbers: [],
        nationality: [],
        declarationAccepted: false
      }
    };

    if (field.startsWith('business.')) {
      const businessField = field.replace('business.', '');
      currentDetails.businessDetails = { ...currentDetails.businessDetails, [businessField]: value };
      
      // Handle subcategory fetching when category changes
      if (businessField === 'categoryId' && value) {
        fetchSubcategories(value);
      }
    } else if (field.startsWith('personal.')) {
      const personalField = field.replace('personal.', '');
      currentDetails.personalDetails = { ...currentDetails.personalDetails, [personalField]: value };
      
      // Handle city fetching when country changes
      if (personalField === 'currentCountryId' && value) {
        fetchCities(value);
      }
    }

    updatedBusinessDetailsMap[treatyTypeId] = currentDetails;

    setNewTreaty({
      ...newTreaty,
      businessDetailsMap: updatedBusinessDetailsMap
    });
  };

  const validateBusinessDetails = () => {
    const errors: Record<string, string> = {};
    
    newTreaty.selectedTreatyTypeIds.forEach(treatyTypeId => {
      const details = newTreaty.businessDetailsMap[treatyTypeId];
      if (!details) {
        errors[`treatyType_${treatyTypeId}`] = 'Business details are required for this treaty type';
        return;
      }

      // Validate business details
      const requiredBusinessFields = ['businessName', 'businessAddress', 'businessEmail', 'categoryId', 'subcategoryId', 'businessType'];
      requiredBusinessFields.forEach(field => {
        const key = `business.${treatyTypeId}.${field}`;
        if (!details.businessDetails[field as keyof typeof details.businessDetails]) {
          errors[key] = `${field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} is required`;
        }
      });

      // Validate personal details
      const requiredPersonalFields = ['fullLegalName', 'email', 'currentCountryId', 'currentCityId'];
      requiredPersonalFields.forEach(field => {
        const key = `personal.${treatyTypeId}.${field}`;
        if (!details.personalDetails[field as keyof typeof details.personalDetails]) {
          errors[key] = `${field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} is required`;
        }
      });

      if (!details.personalDetails.declarationAccepted) {
        errors[`personal.${treatyTypeId}.declarationAccepted`] = 'You must accept the declaration';
      }
    });

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  if (!showAddForm) return null;

  return (
    <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
      <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">Add Treaty Types to Your Treaty</h3>
      
      {userTreaties.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            No treaties found. Please add a treaty number in the Identification tab first.
          </p>
          <button
            onClick={() => setShowAddForm(false)}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      ) : (
        <form onSubmit={handleAddTreaty} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Select Treaty <span className="text-red-500">*</span>
            </label>
            <select
              value={newTreaty.selectedTreatyId}
              onChange={(e) => setNewTreaty({ ...newTreaty, selectedTreatyId: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              required
            >
              <option value="">Choose a treaty...</option>
              {userTreaties.map(treaty => (
                <option key={treaty.id} value={treaty.id}>
                  {treaty.name} (Number: {treaty.treatyNumber})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Treaty Types <span className="text-red-500">*</span>
            </label>
            <select
              multiple
              value={newTreaty.selectedTreatyTypeIds}
              onChange={(e) => {
                const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
                setNewTreaty({ ...newTreaty, selectedTreatyTypeIds: selectedOptions });
              }}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              size={6}
              required
            >
              {availableTreatyTypes.map(type => (
                <option key={type.id} value={type.id}>
                  {type.name} - ${type.price.toFixed(2)} ({type.category})
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-500 mt-1">
              Hold Ctrl/Cmd to select multiple treaty types. Each type has an associated cost.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Signed Date
              </label>
              <input
                type="date"
                value={newTreaty.signedDate}
                onChange={(e) => setNewTreaty({ ...newTreaty, signedDate: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Expiration Date
              </label>
              <input
                type="date"
                value={newTreaty.expirationDate}
                onChange={(e) => setNewTreaty({ ...newTreaty, expirationDate: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          {/* Business Details Section for Selected Treaty Types */}
          {newTreaty.selectedTreatyTypeIds.length > 0 && (
            <div className="space-y-6 border-t pt-6">
              <h4 className="text-md font-medium text-slate-800 dark:text-slate-200">Business Details for Treaty Types</h4>
              
              {newTreaty.selectedTreatyTypeIds.map(treatyTypeId => {
                const treatyType = availableTreatyTypes.find(t => t.id === treatyTypeId);
                const details = newTreaty.businessDetailsMap[treatyTypeId];
                
                return (
                  <div key={treatyTypeId} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
                      {treatyType?.name} - Business Information
                    </h5>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Business Name */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Business Name <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          value={details?.businessDetails.businessName || ''}
                          onChange={(e) => handleBusinessDetailChange(treatyTypeId, 'business.businessName', e.target.value)}
                          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white ${
                            validationErrors[`business.${treatyTypeId}.businessName`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                          }`}
                        />
                        {validationErrors[`business.${treatyTypeId}.businessName`] && (
                          <p className="mt-1 text-sm text-red-600">{validationErrors[`business.${treatyTypeId}.businessName`]}</p>
                        )}
                      </div>

                      {/* Business Email */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Business Email <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="email"
                          value={details?.businessDetails.businessEmail || ''}
                          onChange={(e) => handleBusinessDetailChange(treatyTypeId, 'business.businessEmail', e.target.value)}
                          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white ${
                            validationErrors[`business.${treatyTypeId}.businessEmail`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                          }`}
                        />
                        {validationErrors[`business.${treatyTypeId}.businessEmail`] && (
                          <p className="mt-1 text-sm text-red-600">{validationErrors[`business.${treatyTypeId}.businessEmail`]}</p>
                        )}
                      </div>

                      {/* Business Phone */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Business Phone
                        </label>
                        <input
                          type="tel"
                          value={details?.businessDetails.businessPhone || ''}
                          onChange={(e) => handleBusinessDetailChange(treatyTypeId, 'business.businessPhone', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>

                      {/* Business Type */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Business Type <span className="text-red-500">*</span>
                        </label>
                        <select
                          value={details?.businessDetails.businessType || ''}
                          onChange={(e) => handleBusinessDetailChange(treatyTypeId, 'business.businessType', e.target.value)}
                          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white ${
                            validationErrors[`business.${treatyTypeId}.businessType`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                          }`}
                        >
                          <option value="">Select business type...</option>
                          <option value="sole_trader">Sole Trader</option>
                          <option value="small_business">Small Business</option>
                          <option value="medium_business">Medium Business</option>
                          <option value="large_business">Large Business</option>
                          <option value="church">Church</option>
                          <option value="trust">Trust</option>
                          <option value="non_profit">Non-Profit</option>
                          <option value="government">Government</option>
                          <option value="educational">Educational Institution</option>
                        </select>
                        {validationErrors[`business.${treatyTypeId}.businessType`] && (
                          <p className="mt-1 text-sm text-red-600">{validationErrors[`business.${treatyTypeId}.businessType`]}</p>
                        )}
                      </div>

                      {/* Category */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Business Category <span className="text-red-500">*</span>
                        </label>
                        <select
                          value={details?.businessDetails.categoryId || ''}
                          onChange={(e) => handleBusinessDetailChange(treatyTypeId, 'business.categoryId', e.target.value)}
                          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white ${
                            validationErrors[`business.${treatyTypeId}.categoryId`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                          }`}
                        >
                          <option value="">Select category...</option>
                          {categories.map(category => (
                            <option key={category.id} value={category.id}>
                              {category.name}
                            </option>
                          ))}
                        </select>
                        {validationErrors[`business.${treatyTypeId}.categoryId`] && (
                          <p className="mt-1 text-sm text-red-600">{validationErrors[`business.${treatyTypeId}.categoryId`]}</p>
                        )}
                      </div>

                      {/* Subcategory */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Business Subcategory <span className="text-red-500">*</span>
                        </label>
                        <select
                          value={details?.businessDetails.subcategoryId || ''}
                          onChange={(e) => handleBusinessDetailChange(treatyTypeId, 'business.subcategoryId', e.target.value)}
                          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white ${
                            validationErrors[`business.${treatyTypeId}.subcategoryId`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                          }`}
                          disabled={!details?.businessDetails.categoryId}
                        >
                          <option value="">Select subcategory...</option>
                          {subcategories.map(subcategory => (
                            <option key={subcategory.id} value={subcategory.id}>
                              {subcategory.name}
                            </option>
                          ))}
                        </select>
                        {validationErrors[`business.${treatyTypeId}.subcategoryId`] && (
                          <p className="mt-1 text-sm text-red-600">{validationErrors[`business.${treatyTypeId}.subcategoryId`]}</p>
                        )}
                      </div>
                    </div>

                    {/* Business Address */}
                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Business Address <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        value={details?.businessDetails.businessAddress || ''}
                        onChange={(e) => handleBusinessDetailChange(treatyTypeId, 'business.businessAddress', e.target.value)}
                        rows={3}
                        className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white ${
                          validationErrors[`business.${treatyTypeId}.businessAddress`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                        }`}
                      />
                      {validationErrors[`business.${treatyTypeId}.businessAddress`] && (
                        <p className="mt-1 text-sm text-red-600">{validationErrors[`business.${treatyTypeId}.businessAddress`]}</p>
                      )}
                    </div>

                    {/* Personal Details Section */}
                    <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                      <h6 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">Personal Details</h6>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Full Legal Name */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Full Legal Name <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            value={details?.personalDetails.fullLegalName || ''}
                            onChange={(e) => handleBusinessDetailChange(treatyTypeId, 'personal.fullLegalName', e.target.value)}
                            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white ${
                              validationErrors[`personal.${treatyTypeId}.fullLegalName`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                            }`}
                          />
                          {validationErrors[`personal.${treatyTypeId}.fullLegalName`] && (
                            <p className="mt-1 text-sm text-red-600">{validationErrors[`personal.${treatyTypeId}.fullLegalName`]}</p>
                          )}
                        </div>

                        {/* Email */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Email <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="email"
                            value={details?.personalDetails.email || ''}
                            onChange={(e) => handleBusinessDetailChange(treatyTypeId, 'personal.email', e.target.value)}
                            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white ${
                              validationErrors[`personal.${treatyTypeId}.email`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                            }`}
                          />
                          {validationErrors[`personal.${treatyTypeId}.email`] && (
                            <p className="mt-1 text-sm text-red-600">{validationErrors[`personal.${treatyTypeId}.email`]}</p>
                          )}
                        </div>

                        {/* Country */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Country <span className="text-red-500">*</span>
                          </label>
                          <select
                            value={details?.personalDetails.currentCountryId || ''}
                            onChange={(e) => handleBusinessDetailChange(treatyTypeId, 'personal.currentCountryId', e.target.value)}
                            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white ${
                              validationErrors[`personal.${treatyTypeId}.currentCountryId`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                            }`}
                          >
                            <option value="">Select country...</option>
                            {countries.map(country => (
                              <option key={country.id} value={country.id}>
                                {country.name}
                              </option>
                            ))}
                          </select>
                          {validationErrors[`personal.${treatyTypeId}.currentCountryId`] && (
                            <p className="mt-1 text-sm text-red-600">{validationErrors[`personal.${treatyTypeId}.currentCountryId`]}</p>
                          )}
                        </div>

                        {/* City */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            City <span className="text-red-500">*</span>
                          </label>
                          <select
                            value={details?.personalDetails.currentCityId || ''}
                            onChange={(e) => handleBusinessDetailChange(treatyTypeId, 'personal.currentCityId', e.target.value)}
                            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white ${
                              validationErrors[`personal.${treatyTypeId}.currentCityId`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                            }`}
                            disabled={!details?.personalDetails.currentCountryId}
                          >
                            <option value="">Select city...</option>
                            {cities.map(city => (
                              <option key={city.id} value={city.id}>
                                {city.name}
                              </option>
                            ))}
                          </select>
                          {validationErrors[`personal.${treatyTypeId}.currentCityId`] && (
                            <p className="mt-1 text-sm text-red-600">{validationErrors[`personal.${treatyTypeId}.currentCityId`]}</p>
                          )}
                        </div>
                      </div>

                      {/* Declaration */}
                      <div className="mt-4">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={details?.personalDetails.declarationAccepted || false}
                            onChange={(e) => handleBusinessDetailChange(treatyTypeId, 'personal.declarationAccepted', e.target.checked)}
                            className={`rounded border-gray-300 text-slate-600 shadow-sm focus:ring-slate-500 ${
                              validationErrors[`personal.${treatyTypeId}.declarationAccepted`] ? 'border-red-500' : ''
                            }`}
                          />
                          <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            I declare that the information provided is true and accurate <span className="text-red-500">*</span>
                          </span>
                        </label>
                        {validationErrors[`personal.${treatyTypeId}.declarationAccepted`] && (
                          <p className="mt-1 text-sm text-red-600">{validationErrors[`personal.${treatyTypeId}.declarationAccepted`]}</p>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <textarea
              value={newTreaty.description}
              onChange={(e) => setNewTreaty({ ...newTreaty, description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => setShowAddForm(false)}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
            >
              Add Treaty Types
            </button>
          </div>
        </form>
      )}
    </div>
  );
};