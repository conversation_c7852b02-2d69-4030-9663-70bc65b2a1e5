import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { FileText, CheckCircle, Clock, XCircle, AlertCircle, Plus, Eye, CreditCard } from 'lucide-react';
import PaymentSubmissionModal from '@/components/payments/PaymentSubmissionModal';

interface TreatyType {
  id: string;
  name: string;
  description: string | null;
  category: string;
  price: number;
  currency: string;
  requiresPayment: boolean;
  paymentDeadlineDays: number | null;
  isActive: boolean;
}

interface TreatyApplication {
  id: string;
  userId: string;
  treatyTypeId: string;
  status: 'APPLIED' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'ACTIVE';
  paymentStatus: 'NOT_REQUIRED' | 'AWAITING_PAYMENT' | 'PENDING' | 'PAID' | 'FAILED';
  appliedAt: string;
  approvedAt: string | null;
  rejectedAt: string | null;
  rejectionReason: string | null;
  notes: string | null;
  treatyType: TreatyType;
  latestPayment: {
    id: string;
    amount: number;
    currency: string;
    paymentMethod: string;
    status: string;
    paymentDate: string | null;
    notes: string | null;
  } | null;
}

interface TreatyApplicationManagementProps {
  userId: string;
  userName: string;
  peaceAmbassadorNumber: string | null;
}

export const TreatyApplicationManagement: React.FC<TreatyApplicationManagementProps> = ({
  userId,
  userName,
  peaceAmbassadorNumber
}) => {
  const [applications, setApplications] = useState<TreatyApplication[]>([]);
  const [availableTreatyTypes, setAvailableTreatyTypes] = useState<TreatyType[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [selectedTreatyType, setSelectedTreatyType] = useState('');
  const [submissionReason, setSubmissionReason] = useState('');
  const [notes, setNotes] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState<TreatyApplication | null>(null);

  useEffect(() => {
    fetchApplications();
    fetchTreatyTypes();
  }, [userId]);

  const fetchApplications = async () => {
    try {
      const response = await fetch(`/api/treaty-applications?userId=${userId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          setApplications([]);
          return;
        }
        throw new Error('Failed to fetch treaty applications');
      }
      
      const data = await response.json();
      setApplications(data.applications || []);
    } catch (error) {
      console.error('Error fetching treaty applications:', error);
      toast.error('Failed to load treaty applications');
      setApplications([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchTreatyTypes = async () => {
    try {
      const response = await fetch('/api/treaty-types');
      
      if (!response.ok) {
        if (response.status === 404) {
          setAvailableTreatyTypes([]);
          return;
        }
        throw new Error('Failed to fetch treaty types');
      }
      
      const data = await response.json();
      setAvailableTreatyTypes(data.treatyTypes || []);
    } catch (error) {
      console.error('Error fetching treaty types:', error);
      toast.error('Failed to load treaty types');
      setAvailableTreatyTypes([]);
    }
  };

  const handleSubmitApplication = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!peaceAmbassadorNumber) {
      toast.error('User must have Peace Ambassador status to apply for treaties');
      return;
    }

    if (!selectedTreatyType) {
      toast.error('Please select a treaty type');
      return;
    }

    setSubmitting(true);
    
    try {
      const response = await fetch('/api/treaty-applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          treatyTypeId: selectedTreatyType,
          submissionReason,
          notes
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit application');
      }

      const result = await response.json();
      toast.success('Treaty application submitted successfully!');
      
      // Reset form
      setSelectedTreatyType('');
      setSubmissionReason('');
      setNotes('');
      setShowApplicationForm(false);
      
      // Refresh applications
      await fetchApplications();
    } catch (error: any) {
      toast.error(error.message || 'Failed to submit application');
      console.error('Error submitting application:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handlePaymentSubmit = async (paymentData: any) => {
    if (!selectedApplication) return;

    try {
      const formData = new FormData();
      formData.append('userTreatyTypeId', selectedApplication.id);
      formData.append('payerName', paymentData.payerName);
      formData.append('payerEmail', paymentData.payerEmail);
      formData.append('paymentMethod', paymentData.paymentMethod);
      formData.append('amount', paymentData.amount.toString());
      
      if (paymentData.payerPhone) {
        formData.append('payerPhone', paymentData.payerPhone);
      }
      
      if (paymentData.transactionId) {
        formData.append('transactionId', paymentData.transactionId);
      }
      
      if (paymentData.notes) {
        formData.append('notes', paymentData.notes);
      }
      
      if (paymentData.receiptFile) {
        formData.append('receipt', paymentData.receiptFile);
      }

      const response = await fetch('/api/treaty-payments', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit payment');
      }

      toast.success('Payment submitted successfully! It will be reviewed by administrators.');
      await fetchApplications();
    } catch (error: any) {
      toast.error(error.message || 'Failed to submit payment');
      throw error;
    }
  };

  const handlePaymentClick = (application: TreatyApplication) => {
    if (application.status === 'APPROVED' && application.paymentStatus === 'AWAITING_PAYMENT') {
      setSelectedApplication(application);
      setShowPaymentModal(true);
    } else {
      toast.info('Payment can only be submitted for approved applications awaiting payment.');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPLIED':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'UNDER_REVIEW':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'ACTIVE':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'REJECTED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPLIED':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'UNDER_REVIEW':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'APPROVED':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'AWAITING_PAYMENT':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'PENDING':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'FAILED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'NOT_REQUIRED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-slate-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200">
            Treaty Applications
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Manage treaty applications and payments for {userName}
          </p>
          {!peaceAmbassadorNumber && (
            <div className="mt-2 p-2 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
              <p className="text-sm text-amber-800 dark:text-amber-200">
                User must have Peace Ambassador status to apply for treaties
              </p>
            </div>
          )}
        </div>
        
        <button
          onClick={() => setShowApplicationForm(true)}
          disabled={!peaceAmbassadorNumber}
          className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Application
        </button>
      </div>

      {/* Application Form */}
      {showApplicationForm && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
          <h4 className="text-md font-medium text-slate-800 dark:text-slate-200 mb-4">
            Submit New Treaty Application
          </h4>
          
          <form onSubmit={handleSubmitApplication} className="space-y-4">
            <div>
              <label htmlFor="treatyType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Treaty Type *
              </label>
              <select
                id="treatyType"
                value={selectedTreatyType}
                onChange={(e) => setSelectedTreatyType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                required
              >
                <option value="">Select a treaty type...</option>
                {availableTreatyTypes
                  .filter(type => type.isActive)
                  .map(type => (
                    <option key={type.id} value={type.id}>
                      {type.name} - {type.requiresPayment ? `${type.currency} ${type.price}` : 'Free'}
                    </option>
                  ))}
              </select>
            </div>

            <div>
              <label htmlFor="submissionReason" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Reason for Application
              </label>
              <textarea
                id="submissionReason"
                value={submissionReason}
                onChange={(e) => setSubmissionReason(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                placeholder="Please provide a brief reason for this treaty application..."
              />
            </div>

            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Additional Notes
              </label>
              <textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                placeholder="Any additional information..."
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowApplicationForm(false)}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={submitting || !selectedTreatyType}
                className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {submitting ? 'Submitting...' : 'Submit Application'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Applications List */}
      {applications.length === 0 ? (
        <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <FileText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No treaty applications</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            This user has not submitted any treaty applications yet.
          </p>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Treaty Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Application Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Payment Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Applied Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Cost
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {applications.map((application) => (
                  <tr key={application.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {application.treatyType.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {application.treatyType.description}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(application.status)}
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}>
                          {application.status.replace('_', ' ')}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(application.paymentStatus)}`}>
                        {application.paymentStatus.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {formatDate(application.appliedAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {application.treatyType.requiresPayment ? (
                        `${application.treatyType.currency} ${application.treatyType.price}`
                      ) : (
                        'Free'
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => {
                            // View application details - could open a modal or navigate to details page
                            toast.info('Application details view not implemented yet');
                          }}
                          className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-200"
                          title="View Details"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        
                        {application.status === 'APPROVED' && 
                         application.paymentStatus === 'AWAITING_PAYMENT' && 
                         application.treatyType.requiresPayment && (
                          <button
                            onClick={() => handlePaymentClick(application)}
                            className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-200"
                            title="Submit Payment"
                          >
                            <CreditCard className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
      
      {/* Payment Submission Modal */}
      <PaymentSubmissionModal
        application={selectedApplication}
        isOpen={showPaymentModal}
        onClose={() => {
          setShowPaymentModal(false);
          setSelectedApplication(null);
        }}
        onPaymentSubmit={handlePaymentSubmit}
      />
    </div>
  );
};