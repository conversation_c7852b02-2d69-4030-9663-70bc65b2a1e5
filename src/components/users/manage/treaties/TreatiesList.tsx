import React from 'react';
import { FileText, Calendar, CheckCircle, AlertCircle } from 'lucide-react';
import { Treaty } from './UserTreatyManagement.types';

interface TreatiesListProps {
  treaties: Treaty[];
  searchQuery: string;
  statusFilter: string;
  currentPage: number;
  totalPages: number;
  setCurrentPage: (page: number) => void;
  handleRemoveTreaty: (treatyId: string) => void;
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'ACTIVE':
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case 'EXPIRED':
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    case 'PENDING_RENEWAL':
      return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    default:
      return <AlertCircle className="h-4 w-4 text-gray-500" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'ACTIVE':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'EXPIRED':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    case 'PENDING_RENEWAL':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
};

export const TreatiesList: React.FC<TreatiesListProps> = ({
  treaties,
  searchQuery,
  statusFilter,
  currentPage,
  totalPages,
  setCurrentPage,
  handleRemoveTreaty,
}) => {
  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
        {treaties.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No treaties found</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {searchQuery || statusFilter !== 'all' ? 'Try adjusting your search or filter criteria.' : 'This user has no treaties assigned yet.'}
            </p>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {treaties.map((treaty) => (
              <li key={treaty.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(treaty.status)}
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {treaty.name}
                          {treaty.isTreatyNumber && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                              Treaty Number
                            </span>
                          )}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(treaty.status)}`}>
                            {treaty.isTreatyNumber ? 'Number Assigned' : treaty.status.replace('_', ' ')}
                          </span>
                          {treaty.treatyTypes.length > 0 && (
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {treaty.treatyTypes.map(type => type.name).join(', ')}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {(treaty.signedDate || treaty.expirationDate) && (
                      <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                        {treaty.signedDate && (
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            Signed: {new Date(treaty.signedDate).toLocaleDateString()}
                          </div>
                        )}
                        {treaty.expirationDate && (
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            Expires: {new Date(treaty.expirationDate).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleRemoveTreaty(treaty.id)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 text-sm font-medium"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(currentPage - 1, 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(currentPage + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};