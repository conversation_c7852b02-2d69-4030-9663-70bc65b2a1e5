'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Users,
  Shield,
  Search,
  User,
  Settings,
  Key,
  Plus,
  Trash2,
  CheckCircle,
  XCircle,
  RefreshCw,
  Eye,
  EyeOff,
  Crown
} from 'lucide-react'

interface User {
  id: string
  name: string | null
  email: string | null
  profile?: {
    firstName: string | null
    lastName: string | null
  } | null
}



interface LocalUserPermissions {
  userId: string
  user: {
    name: string | null
    email: string | null
    profile?: {
      firstName: string | null
      lastName: string | null
    } | null
  }
  roles: {
    id: string
    name: string
    description: string | null
    permissions: string[]
  }[]
  directPermissions: any[]
  allPermissions: string[]
}

export function LocalPermissionManager() {
  const [activeTab, setActiveTab] = useState<'users' | 'roles' | 'sync'>('users')
  const [users, setUsers] = useState<User[]>([])
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null)
  const [userSearchTerm, setUserSearchTerm] = useState('')
  const [userPermissions, setUserPermissions] = useState<LocalUserPermissions | null>(null)
  const [showPermissionDetails, setShowPermissionDetails] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [syncing, setSyncing] = useState(false)

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    if (selectedUserId) {
      loadUserPermissions(selectedUserId)
    }
  }, [selectedUserId])

  const loadData = async () => {
    try {
      setLoading(true)

      // Load users
      const usersResponse = await fetch('/api/users')

      if (usersResponse.ok) {
        const usersData = await usersResponse.json()
        setUsers(usersData.users || [])
      }

    } catch (error) {
      console.error('Error loading data:', error)
      setError('Failed to load permission data')
    } finally {
      setLoading(false)
    }
  }

  const loadUserPermissions = async (userId: string) => {
    try {
      const response = await fetch(`/api/users/${userId}/local-permissions`)
      if (response.ok) {
        const data = await response.json()
        setUserPermissions(data.data)
      } else {
        setUserPermissions(null)
      }
    } catch (error) {
      console.error('Error loading user permissions:', error)
      setUserPermissions(null)
    }
  }

  const syncCorePermissions = async () => {
    try {
      setSyncing(true)
      const response = await fetch('/api/local-permissions', {
        method: 'POST'
      })

      if (response.ok) {
        const data = await response.json()
        setError(null)
        // Show success message
        console.log('Permissions synced:', data.message)
        // Reload data
        await loadData()
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to sync permissions')
      }
    } catch (error) {
      console.error('Error syncing permissions:', error)
      setError('Failed to sync permissions')
    } finally {
      setSyncing(false)
    }
  }

  const assignRole = async (userId: string, roleId: string) => {
    try {
      const response = await fetch(`/api/users/${userId}/roles`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          serverId: 'local',
          roleName: roleId, // Use roleId as roleName for local roles
          action: 'assign'
        })
      })

      if (response.ok) {
        await loadUserPermissions(userId)
        setError(null)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to assign role')
      }
    } catch (error) {
      console.error('Error assigning role:', error)
      setError('Failed to assign role')
    }
  }

  const removeRole = async (userId: string, roleId: string) => {
    try {
      const response = await fetch(`/api/users/${userId}/roles`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          serverId: 'local',
          roleName: roleId, // Use roleId as roleName for local roles
          action: 'remove'
        })
      })

      if (response.ok) {
        await loadUserPermissions(userId)
        setError(null)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to remove role')
      }
    } catch (error) {
      console.error('Error removing role:', error)
      setError('Failed to remove role')
    }
  }


  const filteredUsers = users.filter(user => {
    if (!userSearchTerm) return true
    
    const searchLower = userSearchTerm.toLowerCase()
    const name = user.name?.toLowerCase() || ''
    const email = user.email?.toLowerCase() || ''
    const firstName = user.profile?.firstName?.toLowerCase() || ''
    const lastName = user.profile?.lastName?.toLowerCase() || ''
    
    return name.includes(searchLower) || 
           email.includes(searchLower) ||
           firstName.includes(searchLower) ||
           lastName.includes(searchLower)
  })

  const getUserDisplayName = (user: User) => {
    if (user.profile?.firstName || user.profile?.lastName) {
      return `${user.profile.firstName || ''} ${user.profile.lastName || ''}`.trim()
    }
    return user.name || user.email || 'Unknown User'
  }

  const selectedUser = users.find(u => u.id === selectedUserId)

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-slate-600">Loading local permission manager...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Main Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-slate-900 flex items-center">
            <Settings className="h-6 w-6 mr-3" />
            Local Permission Manager
          </h1>
          <p className="text-slate-600 mt-1 flex items-center">
            <Crown className="h-4 w-4 mr-2" />
            Manage user permissions for this local server
          </p>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid w-full grid-cols-2 bg-slate-100 dark:bg-slate-800">
          <TabsTrigger value="users" className="flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-900">
            <Users className="h-4 w-4" />
            User Permissions
          </TabsTrigger>
          <TabsTrigger value="sync" className="flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-900">
            <RefreshCw className="h-4 w-4" />
            Sync Permissions
          </TabsTrigger>
        </TabsList>

        {/* User Permissions Tab */}
        <TabsContent value="users" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* User Selection Panel */}
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-3">Select User</h3>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                  <Input
                    placeholder="Search users..."
                    value={userSearchTerm}
                    onChange={(e) => setUserSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="max-h-96 overflow-y-auto border border-slate-200 dark:border-slate-700 rounded-lg">
                {filteredUsers.length === 0 ? (
                  <div className="p-4 text-center text-slate-500">
                    {userSearchTerm ? 'No users found matching search' : 'No users available'}
                  </div>
                ) : (
                  <div className="divide-y divide-slate-200 dark:divide-slate-700">
                    {filteredUsers.map(user => (
                      <button
                        key={user.id}
                        onClick={() => setSelectedUserId(user.id)}
                        className={`w-full p-3 text-left hover:bg-slate-50 dark:hover:bg-slate-800 ${
                          selectedUserId === user.id ? 'bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500' : ''
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <User className="h-4 w-4 text-slate-400" />
                          <div>
                            <div className="font-medium text-sm">{getUserDisplayName(user)}</div>
                            <div className="text-xs text-slate-500">{user.email}</div>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* User Permission Management Panel */}
            <div className="lg:col-span-2">
              {selectedUserId ? (
                <div className="space-y-6">
                  {/* User Info Header */}
                  <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-4 border border-slate-200 dark:border-slate-700">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <User className="h-5 w-5 text-blue-500" />
                        <div>
                          <h3 className="font-semibold">{selectedUser ? getUserDisplayName(selectedUser) : 'Selected User'}</h3>
                          <p className="text-sm text-slate-600">{selectedUser?.email}</p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowPermissionDetails(!showPermissionDetails)}
                      >
                        {showPermissionDetails ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        {showPermissionDetails ? 'Hide' : 'Show'} Details
                      </Button>
                    </div>
                  </div>

                  {/* Permission Details */}
                  {showPermissionDetails && userPermissions && (
                    <div className="space-y-4">
                      {/* Summary */}
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <Crown className="h-5 w-5 text-blue-600" />
                            <span className="font-medium text-blue-800">Roles</span>
                          </div>
                          <div className="text-2xl font-bold text-blue-900">{userPermissions.roles.length}</div>
                        </div>

                        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <Shield className="h-5 w-5 text-green-600" />
                            <span className="font-medium text-green-800">Permissions</span>
                          </div>
                          <div className="text-2xl font-bold text-green-900">{userPermissions.allPermissions.length}</div>
                        </div>

                      </div>

                      {/* Current Roles */}
                      {userPermissions.roles.length > 0 && (
                        <div className="border border-slate-200 rounded-lg p-4">
                          <h4 className="font-medium mb-3">Current Roles</h4>
                          <div className="space-y-2">
                            {userPermissions.roles.map(role => (
                              <div key={role.id} className="flex items-center justify-between bg-slate-50 rounded px-3 py-2">
                                <div className="flex items-center gap-2">
                                  <Crown className="h-4 w-4 text-blue-500" />
                                  <span className="font-medium">{role.name}</span>
                                  <span className="text-sm text-slate-500">({role.permissions.length} permissions)</span>
                                </div>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => removeRole(selectedUserId, role.id)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                    </div>
                  )}

                </div>
              ) : (
                <div className="flex items-center justify-center h-64 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg">
                  <div className="text-center text-slate-500">
                    <Users className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p>Select a user to manage their local permissions</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabsContent>


        {/* Sync Permissions Tab */}
        <TabsContent value="sync" className="space-y-6">
          <div className="max-w-2xl">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-center gap-3 mb-4">
                <RefreshCw className="h-6 w-6 text-blue-600" />
                <div>
                  <h3 className="font-semibold text-blue-900">Sync Core Permissions</h3>
                  <p className="text-sm text-blue-700">
                    Update the database with core NWAPromote permissions
                  </p>
                </div>
              </div>
              
              <div className="space-y-4">
                <p className="text-sm text-slate-600">
                  Since NWAPromote server is not running, this will sync manually defined core permissions 
                  to the database. This includes permissions for user management, profiles, roles, treaties, 
                  documents, and system administration.
                </p>
                
                <Button 
                  onClick={syncCorePermissions}
                  disabled={syncing}
                  className="flex items-center gap-2"
                >
                  {syncing ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4" />
                  )}
                  {syncing ? 'Syncing...' : 'Sync Core Permissions'}
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}