'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Plus,
  Trash2,
  Shield,
  Search,
  Check,
  X,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react'

interface ServerPermission {
  permission_name: string
  permission_description: string | null
}

interface ServerPermissionsManagerProps {
  serverId: string
  serverName: string
}

export function ServerPermissionsManager({ serverId, serverName }: ServerPermissionsManagerProps) {
  const [permissions, setPermissions] = useState<ServerPermission[]>([])
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [showBulkActions, setShowBulkActions] = useState(false)

  useEffect(() => {
    loadPermissions()
  }, [serverId])

  const loadPermissions = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/remote-servers/${serverId}/permissions`)
      if (response.ok) {
        const data = await response.json()
        setPermissions(data || [])
      } else {
        throw new Error('Failed to load server permissions')
      }
    } catch (error) {
      console.error('Error loading permissions:', error)
      setError('Failed to load server permissions')
    } finally {
      setLoading(false)
    }
  }

  const handlePermissionToggle = (permissionName: string) => {
    setSelectedPermissions(prev =>
      prev.includes(permissionName)
        ? prev.filter(p => p !== permissionName)
        : [...prev, permissionName]
    )
  }

  const handleSelectAll = () => {
    const filteredPermissions = permissions.filter(perm =>
      searchTerm === '' ||
      perm.permission_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (perm.permission_description && perm.permission_description.toLowerCase().includes(searchTerm.toLowerCase()))
    )

    const allPermissionNames = filteredPermissions.map(p => p.permission_name)
    setSelectedPermissions(allPermissionNames)
  }

  const handleDeselectAll = () => {
    setSelectedPermissions([])
  }

  const filteredPermissions = permissions.filter(perm =>
    searchTerm === '' ||
    perm.permission_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (perm.permission_description && perm.permission_description.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2 text-slate-600">
          <RefreshCw className="h-4 w-4 animate-spin" />
          Loading server permissions...
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-500" />
            Server Permissions
          </h3>
          <p className="text-sm text-slate-600 mt-1">
            Manage permissions for <span className="font-medium">{serverName}</span>
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="default"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Permission
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={loadPermissions}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription className="flex items-center justify-between">
            <span>{error}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={loadPermissions}
              className="ml-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Search and Bulk Actions */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Search permissions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleSelectAll}
            disabled={filteredPermissions.length === 0}
          >
            Select All
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDeselectAll}
            disabled={selectedPermissions.length === 0}
          >
            Deselect All
          </Button>
        </div>
      </div>

      {/* Permissions List */}
      <div className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden">
        {filteredPermissions.length === 0 ? (
          <div className="text-center py-8">
            <Shield className="h-8 w-8 mx-auto mb-2 text-slate-300" />
            <p className="text-sm text-slate-500">
              {searchTerm ? 'No permissions found matching search' : 'No permissions available'}
            </p>
          </div>
        ) : (
          <div className="max-h-96 overflow-y-auto">
            <div className="sticky top-0 bg-slate-50 dark:bg-slate-800 p-3 border-b border-slate-200 dark:border-slate-600">
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium text-slate-700 dark:text-slate-300">
                  Available Permissions
                </span>
                <span className="text-slate-500">
                  {selectedPermissions.length} of {filteredPermissions.length} selected
                </span>
              </div>
            </div>
            <div className="p-2">
              {filteredPermissions.map(permission => (
                <label
                  key={permission.permission_name}
                  className="flex items-start gap-3 p-3 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700 cursor-pointer transition-colors"
                >
                  <input
                    type="checkbox"
                    checked={selectedPermissions.includes(permission.permission_name)}
                    onChange={() => handlePermissionToggle(permission.permission_name)}
                    className="mt-0.5 h-4 w-4 text-blue-600 rounded border-slate-300 focus:ring-blue-500"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-slate-800 dark:text-slate-200">
                      {permission.permission_name}
                    </div>
                    {permission.permission_description && (
                      <div className="text-xs text-slate-500 mt-1 leading-relaxed">
                        {permission.permission_description}
                      </div>
                    )}
                  </div>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      {selectedPermissions.length > 0 && (
        <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="text-sm text-blue-800 dark:text-blue-200">
            {selectedPermissions.length} permission(s) selected
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedPermissions([])}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              Clear Selection
            </Button>
            <Button
              size="sm"
              className="flex items-center gap-2"
            >
              <Check className="h-4 w-4" />
              Apply Permissions
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}