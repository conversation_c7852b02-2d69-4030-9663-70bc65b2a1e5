export interface RemoteServer {
  id: string
  name: string
  url: string
  description: string | null
  isActive: boolean
}

export interface ServerPermission {
  permission_name: string
  permission_description: string | null
}

export interface UserServerAssignment {
  id: string
  userId: string
  serverId: string
  server: RemoteServer
  grantedAt: string
  grantedBy: {
    id: string
    name: string | null
    email: string | null
  } | null
  notes: string | null
  permissions: {
    permission: string
    grantedAt: string
    expiresAt: string | null
    grantedBy: string | null
  }[]
}

export interface RoleServerAssignment {
  id: string
  roleId: string
  serverId: string
  server: RemoteServer
  autoGrantPermissions: string[]
  createdAt: string
  createdBy: {
    id: string
    name: string | null
    email: string | null
  } | null
}

export type Assignment = UserServerAssignment | RoleServerAssignment

export interface RemoteServerAssignmentProps {
  entityType: 'user' | 'role'
  entityId: string
  entityName?: string
}

export function isUserAssignment(a: Assignment): a is UserServerAssignment {
  return 'permissions' in a
}

export function isRoleAssignment(a: Assignment): a is RoleServerAssignment {
  return 'autoGrantPermissions' in a
}