'use client';

import React, { useState, useEffect } from 'react';
import { Shield, Loader2, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';

interface RemotePermission {
  id: string;
  permission_name: string;
  permission_description: string;
  last_synced_at: string;
  isAssigned?: boolean;
  assignedAt?: Date;
  assignedByRole?: string; // Indicates this permission comes from a role assignment
}

interface RemotePermissionManagementProps {
  serverId: string;
  userId: string;
  permissions: RemotePermission[];
  loading: boolean;
  error?: string;
  onPermissionToggle: (permissionName: string, serverId: string) => Promise<void>;
}

export const RemotePermissionManagement: React.FC<RemotePermissionManagementProps> = ({
  serverId,
  userId,
  permissions,
  loading,
  error,
  onPermissionToggle,
}) => {
  const [localPermissions, setLocalPermissions] = useState<RemotePermission[]>(permissions);
  const [togglingPermissions, setTogglingPermissions] = useState<Record<string, boolean>>({});

  useEffect(() => {
    setLocalPermissions(permissions);
  }, [permissions]);

  const handlePermissionToggle = async (permissionName: string) => {
    if (togglingPermissions[permissionName]) return; // Prevent multiple simultaneous toggles

    setTogglingPermissions(prev => ({ ...prev, [permissionName]: true }));

    try {
      await onPermissionToggle(permissionName, serverId);

      // Update local state optimistically
      setLocalPermissions(prev =>
        prev.map(permission =>
          permission.permission_name === permissionName
            ? { ...permission, isAssigned: !permission.isAssigned, assignedAt: !permission.isAssigned ? new Date() : undefined }
            : permission
        )
      );
    } catch (error) {
      console.error('Failed to toggle permission:', error);
      // Revert optimistic update on error
      setLocalPermissions(permissions);
    } finally {
      setTogglingPermissions(prev => ({ ...prev, [permissionName]: false }));
    }
  };

  if (loading) {
    return (
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
          <Shield className="h-4 w-4 mr-2" />
          Remote Permissions
        </h4>
        <div className="animate-pulse space-y-2">
          {[1, 2, 3].map(i => (
            <div key={i} className="h-12 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
          <Shield className="h-4 w-4 mr-2" />
          Remote Permissions
        </h4>
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-red-400 mr-2" />
            <span className="text-red-800 text-sm">{error}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
        <Shield className="h-4 w-4 mr-2" />
        Remote Permissions
        <span className="ml-2 text-xs text-gray-500">
           ({localPermissions.filter(p => p.isAssigned || p.assignedByRole).length}/{localPermissions.length} assigned)
         </span>
      </h4>

      {localPermissions.length === 0 ? (
        <div className="text-center py-4 border-2 border-dashed border-gray-300 rounded-lg">
          <Shield className="h-6 w-6 mx-auto mb-2 text-gray-300" />
          <p className="text-sm text-gray-600">No permissions available for this server</p>
        </div>
      ) : (
        <div className="space-y-2 max-h-[1200px] overflow-y-auto">
          {localPermissions.map(permission => {
            const isToggling = togglingPermissions[permission.permission_name];

            return (
              <div
                key={permission.id}
                className={`border rounded-lg p-3 transition-colors ${
                  permission.isAssigned || permission.assignedByRole
                    ? 'border-green-200 bg-green-50'
                    : 'border-gray-200 bg-white hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h5 className="text-sm font-medium text-gray-900">
                        {permission.permission_name}
                        {permission.permission_description && (
                          <span className="text-xs text-gray-600 ml-1">
                            - {permission.permission_description}
                          </span>
                        )}
                      </h5>
                      {(permission.isAssigned || permission.assignedByRole) && (
                        <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      )}
                    </div>

                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs text-gray-500">
                        Last synced: {new Date(permission.last_synced_at).toLocaleDateString()}
                      </span>
                    </div>

                    {(permission.isAssigned || permission.assignedByRole) && permission.assignedAt && (
                       <p className="text-xs text-green-600 mt-1">
                         {permission.assignedByRole
                           ? `From role: ${permission.assignedByRole}`
                           : `Assigned ${permission.assignedAt.toLocaleDateString()}`
                         }
                       </p>
                     )}
                  </div>

                  <div className="ml-3 flex items-center">
                    {isToggling ? (
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                    ) : (
                      <Switch
                        checked={permission.isAssigned || !!permission.assignedByRole}
                        onCheckedChange={() => handlePermissionToggle(permission.permission_name)}
                        disabled={isToggling || !!permission.assignedByRole}
                      />
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};