'use client';

import React, { useState, useEffect } from 'react';
import { Shield, Crown, Loader2, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';

interface LocalRole {
  id: string;
  name: string;
  description?: string;
  permissions?: string[];
  isAssigned?: boolean;
  assignedAt?: Date;
}

interface LocalPermission {
  id: string;
  permission_name: string;
  permission_description: string;
  isAssigned?: boolean;
  assignedAt?: Date;
}

interface LocalPermissionManagementProps {
  userId: string;
  localRoles: LocalRole[];
  localPermissions: LocalPermission[];
  rolesLoading: boolean;
  permissionsLoading: boolean;
  rolesError?: string;
  permissionsError?: string;
  onLocalRoleToggle: (roleName: string, userId: string) => Promise<void>;
  onLocalPermissionToggle: (permissionName: string, userId: string) => Promise<void>;
}

export const LocalPermissionManagement: React.FC<LocalPermissionManagementProps> = ({
  userId,
  localRoles,
  localPermissions,
  rolesLoading,
  permissionsLoading,
  rolesError,
  permissionsError,
  onLocalRoleToggle,
  onLocalPermissionToggle,
}) => {
  const [localLocalRoles, setLocalLocalRoles] = useState<LocalRole[]>(localRoles);
  const [localLocalPermissions, setLocalLocalPermissions] = useState<LocalPermission[]>(localPermissions);
  const [togglingRoles, setTogglingRoles] = useState<Record<string, boolean>>({});
  const [togglingPermissions, setTogglingPermissions] = useState<Record<string, boolean>>({});

  useEffect(() => {
    setLocalLocalRoles(localRoles);
  }, [localRoles]);

  useEffect(() => {
    setLocalLocalPermissions(localPermissions);
  }, [localPermissions]);

  const handleLocalRoleToggle = async (roleName: string) => {
    if (togglingRoles[roleName]) return;

    setTogglingRoles(prev => ({ ...prev, [roleName]: true }));

    try {
      await onLocalRoleToggle(roleName, userId);

      setLocalLocalRoles(prev =>
        prev.map(role =>
          role.name === roleName
            ? { ...role, isAssigned: !role.isAssigned, assignedAt: !role.isAssigned ? new Date() : undefined }
            : role
        )
      );
    } catch (error) {
      console.error('Failed to toggle local role:', error);
      setLocalLocalRoles(localRoles);
    } finally {
      setTogglingRoles(prev => ({ ...prev, [roleName]: false }));
    }
  };

  const handleLocalPermissionToggle = async (permissionName: string) => {
    if (togglingPermissions[permissionName]) return;

    setTogglingPermissions(prev => ({ ...prev, [permissionName]: true }));

    try {
      await onLocalPermissionToggle(permissionName, userId);

      setLocalLocalPermissions(prev =>
        prev.map(permission =>
          permission.permission_name === permissionName
            ? { ...permission, isAssigned: !permission.isAssigned, assignedAt: !permission.isAssigned ? new Date() : undefined }
            : permission
        )
      );
    } catch (error) {
      console.error('Failed to toggle local permission:', error);
      setLocalLocalPermissions(localPermissions);
    } finally {
      setTogglingPermissions(prev => ({ ...prev, [permissionName]: false }));
    }
  };

  return (
    <div className="space-y-6">
      {/* Local Roles Section */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
          <Crown className="h-4 w-4 mr-2 text-blue-600" />
          Local Roles
          <span className="ml-2 text-xs text-gray-500">
            ({localLocalRoles.filter(r => r.isAssigned).length}/{localLocalRoles.length} assigned)
          </span>
        </h4>

        {rolesLoading ? (
          <div className="animate-pulse space-y-2">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        ) : rolesError ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <div className="flex items-center">
              <AlertCircle className="h-4 w-4 text-red-400 mr-2" />
              <span className="text-red-800 text-sm">{rolesError}</span>
            </div>
          </div>
        ) : localLocalRoles.length === 0 ? (
          <div className="text-center py-4 border-2 border-dashed border-gray-300 rounded-lg">
            <Crown className="h-6 w-6 mx-auto mb-2 text-gray-300" />
            <p className="text-sm text-gray-600">No local roles available</p>
          </div>
        ) : (
          <div className="space-y-2 max-h-[500px] overflow-y-auto">
            {localLocalRoles.map(role => {
              const isToggling = togglingRoles[role.name];

              return (
                <div
                  key={role.id}
                  className={`border rounded-lg p-3 transition-colors ${
                    role.isAssigned
                      ? 'border-blue-200 bg-blue-50'
                      : 'border-gray-200 bg-white hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h5 className="text-sm font-medium text-gray-900 truncate">
                          {role.name}
                        </h5>
                        {role.isAssigned && (
                          <CheckCircle className="h-4 w-4 text-blue-500 flex-shrink-0" />
                        )}
                      </div>

                      {role.description && (
                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                          {role.description}
                        </p>
                      )}

                      {role.permissions && role.permissions.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {role.permissions.slice(0, 3).map((permission, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {permission}
                            </span>
                          ))}
                          {role.permissions.length > 3 && (
                            <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              +{role.permissions.length - 3} more
                            </span>
                          )}
                        </div>
                      )}

                      {role.isAssigned && role.assignedAt && (
                        <p className="text-xs text-blue-600 mt-1">
                          Assigned {role.assignedAt.toLocaleDateString()}
                        </p>
                      )}
                    </div>

                    <div className="ml-3 flex items-center">
                      {isToggling ? (
                        <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                      ) : (
                        <Switch
                          checked={role.isAssigned || false}
                          onCheckedChange={() => handleLocalRoleToggle(role.name)}
                          disabled={isToggling}
                        />
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Local Permissions Section */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
          <Shield className="h-4 w-4 mr-2 text-green-600" />
          Local Permissions
          <span className="ml-2 text-xs text-gray-500">
            ({localLocalPermissions.filter(p => p.isAssigned).length}/{localLocalPermissions.length} assigned)
          </span>
        </h4>

        {permissionsLoading ? (
          <div className="animate-pulse space-y-2">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        ) : permissionsError ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <div className="flex items-center">
              <AlertCircle className="h-4 w-4 text-red-400 mr-2" />
              <span className="text-red-800 text-sm">{permissionsError}</span>
            </div>
          </div>
        ) : localLocalPermissions.length === 0 ? (
          <div className="text-center py-4 border-2 border-dashed border-gray-300 rounded-lg">
            <Shield className="h-6 w-6 mx-auto mb-2 text-gray-300" />
            <p className="text-sm text-gray-600">No local permissions available</p>
          </div>
        ) : (
          <div className="space-y-2 max-h-[500px] overflow-y-auto">
            {localLocalPermissions.map(permission => {
              const isToggling = togglingPermissions[permission.permission_name];

              return (
                <div
                  key={permission.id}
                  className={`border rounded-lg p-3 transition-colors ${
                    permission.isAssigned
                      ? 'border-green-200 bg-green-50'
                      : 'border-gray-200 bg-white hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h5 className="text-sm font-medium text-gray-900 truncate">
                          {permission.permission_name}
                        </h5>
                        {permission.isAssigned && (
                          <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                        )}
                      </div>

                      {permission.permission_description && (
                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                          {permission.permission_description}
                        </p>
                      )}

                      {permission.isAssigned && permission.assignedAt && (
                        <p className="text-xs text-green-600 mt-1">
                          Assigned {permission.assignedAt.toLocaleDateString()}
                        </p>
                      )}
                    </div>

                    <div className="ml-3 flex items-center">
                      {isToggling ? (
                        <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                      ) : (
                        <Switch
                          checked={permission.isAssigned || false}
                          onCheckedChange={() => handleLocalPermissionToggle(permission.permission_name)}
                          disabled={isToggling}
                        />
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};