'use client'

import React from 'react'
import { Switch } from '@/components/ui/switch'
import { Shield } from 'lucide-react'
import { UserPermission } from './types'

interface PermissionManagementPanelProps {
  permissions: any[]
  userPermissions: UserPermission[]
  selectedUserId: string | null
  serverId: string
  loading: boolean
  onPermissionToggle: (permissionName: string, serverId: string) => void
}

export function PermissionManagementPanel({
  permissions,
  userPermissions,
  selectedUserId,
  serverId,
  loading,
  onPermissionToggle
}: PermissionManagementPanelProps) {
  const isPermissionAssigned = (permissionName: string) => {
    return userPermissions.some(
      p => p.userId === selectedUserId &&
           p.serverId === serverId &&
           p.permissionName === permissionName &&
           p.isAssigned
    )
  }

  return (
    <div>
      <h5 className="font-medium mb-3 flex items-center gap-2">
        <Shield className="h-4 w-4 text-blue-500" />
        Permissions
      </h5>
      <div className="space-y-2">
        {loading ? (
          <div className="text-center py-4 text-slate-500">
            Loading permissions...
          </div>
        ) : permissions.length > 0 ? (
          permissions.map((permission, index) => {
            const isAssigned = isPermissionAssigned(permission.permission_name)

            return (
              <div key={index} className="flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                <div className="flex-1">
                  <div className="font-medium text-sm text-slate-800 dark:text-slate-200">
                    {permission.permission_name}
                  </div>
                  {permission.permission_description && (
                    <div className="text-xs text-slate-600 dark:text-slate-400 mt-1">
                      {permission.permission_description}
                    </div>
                  )}
                </div>
                <Switch
                  checked={isAssigned}
                  onCheckedChange={() => onPermissionToggle(permission.permission_name, serverId)}
                  className={`data-[state=checked]:bg-green-600 data-[state=unchecked]:bg-red-500 transition-all duration-200 ease-in-out ${isAssigned ? 'bg-green-600' : 'bg-red-500'}`}
                />
              </div>
            )
          })
        ) : (
          <div className="text-center py-4 text-slate-500">
            No permissions available for this server
          </div>
        )}
      </div>
    </div>
  )
}