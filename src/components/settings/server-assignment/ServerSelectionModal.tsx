'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Server, Plus } from 'lucide-react'
import { RemoteServer, User } from './types'

interface ServerSelectionModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  availableServers: RemoteServer[]
  selectedUser: User | null
  selectedRole?: any
  onServerSelect: (serverId: string) => void
  getUserDisplayName: (user: User) => string
}

export function ServerSelectionModal({
  isOpen,
  onOpenChange,
  availableServers,
  selectedUser,
  selectedRole,
  onServerSelect,
  getUserDisplayName
}: ServerSelectionModalProps) {
  const isRoleAssignment = !!selectedRole
  const targetName = isRoleAssignment ? selectedRole.name : (selectedUser ? getUserDisplayName(selectedUser) : 'this user')

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Assign Server
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Assign Server to {isRoleAssignment ? 'Role' : 'User'}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <p className="text-sm text-slate-600 dark:text-slate-400">
            Select a server to assign to {targetName}:
          </p>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {availableServers.map(server => (
              <button
                key={server.id}
                onClick={() => {
                  console.log('Server button clicked:', server)
                  onServerSelect(server.id)
                }}
                className="w-full p-3 text-left border border-slate-200 dark:border-slate-700 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <Server className="h-4 w-4 text-slate-400" />
                  <div>
                    <div className="font-medium text-sm">{server.name}</div>
                    <div className="text-xs text-slate-500">{server.url}</div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}