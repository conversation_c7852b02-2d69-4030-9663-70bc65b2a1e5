'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { User, Crown, Server, RefreshCw, XCircle, CheckCircle, Search, Shield } from 'lucide-react'
import { ServerAssignmentState } from './types'
import { UserSelectionPanel } from './UserSelectionPanel'
import { ServerSelectionModal } from './ServerSelectionModal'
import { ExpandableRemoteServerCard } from './ExpandableServerCard'

export function ServerAssignmentManager() {
  // State management
  const [state, setState] = useState<ServerAssignmentState>({
    users: [],
    availableServers: [],
    selectedUserId: null,
    userSearchTerm: '',
    loading: true,
    error: null,
    userServerAssignments: [],
    userPermissions: [],
    userRoles: [],
    showServerSelectionModal: false,
    expandedServerId: null,
    serverRoles: {},
    serverPermissions: {},
    rolesLoading: {},
    syncLoading: {},
    availableRoles: [],
    selectedRoleId: null,
    roleSearchTerm: '',
    roleServerAssignments: [],
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }))

      // Load users
      const usersResponse = await fetch('/api/users')
      if (usersResponse.ok) {
        const usersData = await usersResponse.json()
        const users = usersData.users || (Array.isArray(usersData) ? usersData : [])
        setState(prev => ({ ...prev, users }))
      }

      // Load remote servers
      const serversResponse = await fetch('/api/remote-servers')
      if (serversResponse.ok) {
        const serversData = await serversResponse.json()
        setState(prev => ({ ...prev, availableServers: serversData.remoteServers || [] }))
      } else {
        setState(prev => ({ ...prev, availableServers: [] }))
      }

      // Load available roles
      const rolesResponse = await fetch('/api/admin/roles')
      if (rolesResponse.ok) {
        const rolesData = await rolesResponse.json()
        setState(prev => ({ ...prev, availableRoles: rolesData.roles || [] }))
      } else {
        setState(prev => ({ ...prev, availableRoles: [] }))
      }

    } catch (error) {
      console.error('Error loading data:', error)
      setState(prev => ({ ...prev, error: 'Failed to load users and servers' }))
    } finally {
      setState(prev => ({ ...prev, loading: false }))
    }
  }

  const getUserDisplayName = (user: any) => {
    if (user.profile?.firstName || user.profile?.lastName) {
      return `${user.profile.firstName || ''} ${user.profile.lastName || ''}`.trim()
    }
    return user.name || user.email || 'Unknown User'
  }

  const selectedUser = state.users.find(u => u.id === state.selectedUserId) || null
  const selectedRole = state.availableRoles.find(r => r.id === state.selectedRoleId) || null

  // Event handlers
  const handleUserSearchChange = (term: string) => {
    setState(prev => ({ ...prev, userSearchTerm: term }))
  }

  const handleUserSelect = async (userId: string) => {
    console.log('User selected:', userId)
    setState(prev => ({ ...prev, selectedUserId: userId }))

    // Load user's existing server assignments
    if (userId) {
      console.log('Loading server assignments for user:', userId)
      await loadUserServerAssignments(userId)
    } else {
      // Clear assignments when no user is selected
      console.log('Clearing server assignments - no user selected')
      setState(prev => ({ ...prev, userServerAssignments: [] }))
    }
  }

  const loadUserServerAssignments = async (userId: string) => {
    try {
      console.log('Loading user server assignments for:', userId)
      const response = await fetch(`/api/users/${userId}/remote-servers`)

      console.log('API Response status:', response.status, response.statusText)
      console.log('API Response headers:', Object.fromEntries(response.headers.entries()))

      if (response.ok) {
        const responseData = await response.json()
        console.log('Raw API response data:', responseData)

        // Handle different response formats
        let assignmentsData = []
        if (responseData.data) {
          assignmentsData = Array.isArray(responseData.data) ? responseData.data : []
        } else if (Array.isArray(responseData)) {
          assignmentsData = responseData
        }

        console.log('Assignments data to process:', assignmentsData)
        console.log('Number of assignments found:', assignmentsData.length)

        // Transform the data to match the expected format
        const assignments = assignmentsData.map((access: any) => {
          console.log('Processing assignment:', access)
          return {
            id: access.id,
            userId: access.userId || userId,
            serverId: access.serverId,
            server: access.server,
            assignedAt: new Date(access.grantedAt || access.granted_at || access.createdAt || Date.now()),
            grantedBy: access.grantedBy,
            notes: access.notes,
            permissions: access.permissions || [],
            roles: access.roles || [],
            hasAccess: access.hasAccess !== false // Default to true if not specified
          }
        })

        console.log('Final transformed assignments:', assignments)
        setState(prev => ({ ...prev, userServerAssignments: assignments }))
      } else {
        const errorText = await response.text()
        console.error('Failed to load user server assignments:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        })
        setState(prev => ({ ...prev, userServerAssignments: [] }))
      }
    } catch (error) {
      console.error('Error loading user server assignments:', error)
      setState(prev => ({ ...prev, userServerAssignments: [] }))
    }
  }

  const handleServerSelect = (serverId: string) => {
    console.log('Server selected:', { serverId, selectedUserId: state.selectedUserId })
    if (!state.selectedUserId) {
      console.error('No user selected')
      setState(prev => ({
        ...prev,
        error: 'Please select a user first',
        showServerSelectionModal: false // Close modal even on error
      }))
      return
    }
    handleAssignServer(serverId)
    // Close the modal after attempting server assignment
    setState(prev => ({ ...prev, showServerSelectionModal: false }))
  }

  const handleAssignServer = async (serverId: string) => {
    const server = state.availableServers.find(s => s.id === serverId)
    if (!server || !state.selectedUserId) {
      console.error('Missing server or user ID:', { serverId, selectedUserId: state.selectedUserId })
      setState(prev => ({
        ...prev,
        error: 'Missing server or user information'
      }))
      return
    }

    console.log('Attempting to assign server:', { serverId, userId: state.selectedUserId, server })

    try {
      const requestBody = {
        serverId: serverId,
        permissions: [],
        notes: `Assigned via ServerAssignmentManager`
      }

      console.log('Making API request:', {
        url: `/api/users/${state.selectedUserId}/remote-servers`,
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: requestBody
      })

      const response = await fetch(`/api/users/${state.selectedUserId}/remote-servers`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      })

      console.log('API Response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        url: response.url
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Assignment successful:', result)

        // Create a more complete assignment object
        const newAssignment = {
          id: result.data?.accessId || `assignment_${Date.now()}`,
          userId: state.selectedUserId,
          serverId: serverId,
          server: server,
          assignedAt: new Date(),
          grantedBy: { name: 'Current User' }, // Will be updated by the API response
          notes: `Assigned via ServerAssignmentManager`,
          permissions: result.data?.finalPermissions || [],
          roles: [],
          hasAccess: true
        }

        setState(prev => ({
          ...prev,
          userServerAssignments: [...prev.userServerAssignments, newAssignment],
          showServerSelectionModal: false,
          error: null // Clear any previous errors
        }))

        // Reload user assignments to get the complete data from the server
        if (state.selectedUserId) {
          await loadUserServerAssignments(state.selectedUserId)
        }
      } else {
        try {
          const responseText = await response.text()
          console.error('Failed to assign server - Raw response:', {
            status: response.status,
            statusText: response.statusText,
            body: responseText,
            headers: Object.fromEntries(response.headers.entries())
          })

          // Check if response is completely empty
          if (!responseText || responseText.trim() === '') {
            console.error('API returned completely empty response')
            setState(prev => ({
              ...prev,
              error: `Failed to assign server: Server returned empty response (${response.status} ${response.statusText})`
            }))
            return
          }

          // Try to parse as JSON
          let errorData
          try {
            errorData = JSON.parse(responseText)
          } catch (jsonParseError) {
            console.error('Response is not valid JSON:', jsonParseError)
            setState(prev => ({
              ...prev,
              error: `Failed to assign server: Invalid response from server (${response.status} ${response.statusText})`
            }))
            return
          }

          console.error('Failed to assign server - Parsed error data:', errorData)

          // Handle different error response formats
          let errorMessage = 'Unknown error'
          if (typeof errorData === 'object' && errorData !== null) {
            if (errorData.error) {
              errorMessage = errorData.error
            } else if (errorData.message) {
              errorMessage = errorData.message
            } else if (errorData.details) {
              errorMessage = errorData.details
            } else if (Object.keys(errorData).length === 0) {
              // Empty object
              errorMessage = `HTTP ${response.status}: ${response.statusText} (empty response)`
            } else {
              // Object with unknown structure
              errorMessage = `HTTP ${response.status}: ${response.statusText} (${JSON.stringify(errorData)})`
            }
          } else if (typeof errorData === 'string') {
            errorMessage = errorData
          } else {
            errorMessage = `HTTP ${response.status}: ${response.statusText}`
          }

          setState(prev => ({
            ...prev,
            error: `Failed to assign server: ${errorMessage}`
          }))
        } catch (error) {
          console.error('Error processing error response:', error)
          setState(prev => ({
            ...prev,
            error: `Failed to assign server: ${response.status} ${response.statusText}`
          }))
        }
      }
    } catch (error) {
      console.error('Error assigning server:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error('Full error details:', {
        message: errorMessage,
        error: error,
        stack: error instanceof Error ? error.stack : undefined
      })

      // Show user-friendly error message
      setState(prev => ({
        ...prev,
        error: `Failed to assign server: ${errorMessage}`
      }))
    }
  }

  const handleRemoveServerAssignment = async (assignmentId: string, serverId: string) => {
    if (!state.selectedUserId) return

    try {
      const response = await fetch(`/api/users/${state.selectedUserId}/remote-servers?serverId=${serverId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setState(prev => ({
          ...prev,
          userServerAssignments: prev.userServerAssignments.filter(assignment => assignment.id !== assignmentId)
        }))
      } else {
        const errorData = await response.json()
        console.error('Failed to remove server assignment:', errorData)
      }
    } catch (error) {
      console.error('Error removing server assignment:', error)
    }
  }

  const handlePermissionToggle = async (permissionName: string, serverId: string) => {
    if (!state.selectedUserId) return

    try {
      const response = await fetch(`/api/users/${state.selectedUserId}/permissions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          serverId,
          permissionName,
          action: 'toggle'
        }),
      })

      if (response.ok) {
        const existingPermission = state.userPermissions.find(
          p => p.userId === state.selectedUserId && p.serverId === serverId && p.permissionName === permissionName
        )

        if (existingPermission) {
          setState(prev => ({
            ...prev,
            userPermissions: prev.userPermissions.map(p =>
              p.userId === state.selectedUserId && p.serverId === serverId && p.permissionName === permissionName
                ? { ...p, isAssigned: !p.isAssigned }
                : p
            )
          }))
        } else {
          const newPermission = {
            id: `perm_${Date.now()}_${Math.random()}`,
            userId: state.selectedUserId,
            serverId: serverId,
            permissionName: permissionName,
            isAssigned: true,
            assignedAt: new Date()
          }
          setState(prev => ({
            ...prev,
            userPermissions: [...prev.userPermissions, newPermission]
          }))
        }
      } else {
        const errorText = await response.text()
        console.error('Failed to update permission:', response.status, response.statusText, errorText)
      }
    } catch (error) {
      console.error('Error updating permission:', error)
    }
  }

  const handleRoleToggle = async (roleName: string, serverId: string) => {
    if (!state.selectedUserId) return

    try {
      const response = await fetch(`/api/users/${state.selectedUserId}/roles`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          serverId,
          roleName,
          action: 'toggle'
        }),
      })

      if (response.ok) {
        const existingRole = state.userRoles.find(
          r => r.userId === state.selectedUserId && r.serverId === serverId && r.roleName === roleName
        )

        // Get the role definition to find its permissions
        const serverRoles = state.serverRoles[serverId] || []
        const roleDefinition = serverRoles.find(r => r.name === roleName)
        const rolePermissions = roleDefinition?.permissions || []

        if (existingRole) {
          // Role is being unassigned - remove its permissions
          const isCurrentlyAssigned = existingRole.isAssigned

          setState(prev => ({
            ...prev,
            userRoles: prev.userRoles.map(r =>
              r.userId === state.selectedUserId && r.serverId === serverId && r.roleName === roleName
                ? { ...r, isAssigned: !r.isAssigned }
                : r
            ),
            // If role was assigned and is now being unassigned, remove its permissions
            userPermissions: isCurrentlyAssigned
              ? prev.userPermissions.filter(p =>
                  !(p.userId === state.selectedUserId && p.serverId === serverId && rolePermissions.includes(p.permissionName))
                )
              : [
                  // If role was not assigned and is now being assigned, add its permissions
                  ...prev.userPermissions,
                  ...rolePermissions.map((permissionName: string) => ({
                    id: `role_perm_${Date.now()}_${Math.random()}_${permissionName}`,
                    userId: state.selectedUserId,
                    serverId: serverId,
                    permissionName: permissionName,
                    isAssigned: true,
                    assignedAt: new Date(),
                    assignedByRole: roleName // Mark that this permission comes from a role
                  }))
                ]
          }))
        } else {
          // New role assignment - add the role and its permissions
          const newRole = {
            id: `role_${Date.now()}_${Math.random()}`,
            userId: state.selectedUserId,
            serverId: serverId,
            roleName: roleName,
            isAssigned: true,
            assignedAt: new Date()
          }

          setState(prev => ({
            ...prev,
            userRoles: [...prev.userRoles, newRole],
            // Add the role's permissions to the permissions list
            userPermissions: [
              ...prev.userPermissions,
              ...rolePermissions.map((permissionName: string) => ({
                id: `role_perm_${Date.now()}_${Math.random()}_${permissionName}`,
                userId: state.selectedUserId,
                serverId: serverId,
                permissionName: permissionName,
                isAssigned: true,
                assignedAt: new Date(),
                assignedByRole: roleName // Mark that this permission comes from a role
              }))
            ]
          }))
        }
      } else {
        console.error('Failed to update role:', response.statusText)
      }
    } catch (error) {
      console.error('Error updating role:', error)
    }
  }



  const handleSyncPermissions = async (serverId: string) => {
    console.log('Syncing permissions for server:', serverId)

    // Set loading state
    setState(prev => ({
      ...prev,
      syncLoading: { ...prev.syncLoading, [serverId]: true }
    }))

    try {
      const response = await fetch(`/api/remote-servers/${serverId}/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Sync successful:', result)

        // Reload server roles and permissions after sync
        await loadServerRolesAndPermissions(serverId)

        // Show success message (you might want to add a toast notification here)
        alert(`Permissions synced successfully! Synced ${result.data?.permissionsSynced || 0} permissions and ${result.data?.rolesSynced || 0} roles.`)
      } else {
        const errorData = await response.json()
        console.error('Sync failed:', errorData)
        alert(`Failed to sync permissions: ${errorData.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('Error syncing permissions:', error)
      alert('Failed to sync permissions: Network error')
    } finally {
      // Clear loading state
      setState(prev => ({
        ...prev,
        syncLoading: { ...prev.syncLoading, [serverId]: false }
      }))
    }
  }

  const handleToggleServerExpansion = async (serverId: string) => {
    if (state.expandedServerId === serverId) {
      setState(prev => ({ ...prev, expandedServerId: null }))
    } else {
      setState(prev => ({ ...prev, expandedServerId: serverId }))
      await loadServerRolesAndPermissions(serverId)
    }
  }

  const loadServerRolesAndPermissions = async (serverId: string) => {
    console.log('Loading roles and permissions for server:', serverId)

    if (state.serverRoles[serverId] || state.serverPermissions[serverId]) {
      console.log('Data already loaded for server:', serverId)
      return // Already loaded
    }

    try {
      setState(prev => ({
        ...prev,
        rolesLoading: { ...prev.rolesLoading, [serverId]: true }
      }))

      console.log('Making API calls for server:', serverId)

      // Load roles for this server
      const rolesResponse = await fetch(`/api/remote-servers/${serverId}/roles`)
      console.log('Roles API response:', rolesResponse.status, rolesResponse.statusText)

      if (rolesResponse.ok) {
        const rolesData = await rolesResponse.json()
        console.log('Roles data received:', rolesData)

        // Transform the roles data to match the expected format for RemoteRoleManagement
        const transformedRoles = (rolesData || []).map((role: any) => ({
          id: role.id || `role-${Date.now()}-${Math.random()}`,
          name: role.name,
          description: role.description,
          permissions: (role.permissions || []).map((perm: any) => perm.name || perm), // Extract permission names as strings
          isAssigned: false, // Default to false, will be updated based on user assignments
          assignedAt: undefined
        }))

        console.log('Transformed roles data:', transformedRoles)
        setState(prev => ({
          ...prev,
          serverRoles: { ...prev.serverRoles, [serverId]: transformedRoles }
        }))
      } else {
        console.error('Failed to load roles:', rolesResponse.status, rolesResponse.statusText)
        setState(prev => ({
          ...prev,
          serverRoles: { ...prev.serverRoles, [serverId]: [] }
        }))
      }

      // Load permissions for this server
      const permissionsResponse = await fetch(`/api/remote-servers/${serverId}/permissions`)
      console.log('Permissions API response:', permissionsResponse.status, permissionsResponse.statusText)

      if (permissionsResponse.ok) {
        const permissionsData = await permissionsResponse.json()
        console.log('Permissions data received:', permissionsData)

        // Transform the permissions data to match the expected format for RemotePermissionManagement
        let transformedPermissions: Array<{
          id: string;
          permission_name: string;
          permission_description: string;
          last_synced_at: string;
          isAssigned?: boolean;
          assignedAt?: Date;
        }> = []

        if (permissionsData.success && permissionsData.data) {
          // Extract permissions from the nested data structure
          transformedPermissions = (permissionsData.data.permissions || []).map((permission: any, index: number) => ({
            id: `perm-${index}`,
            permission_name: permission.name,
            permission_description: permission.description || permission.name,
            last_synced_at: new Date().toISOString(),
            isAssigned: false, // Default to false, will be updated based on user assignments
            assignedAt: undefined
          }))
        } else if (Array.isArray(permissionsData)) {
          // Handle direct array response
          transformedPermissions = permissionsData.map((permission: any, index: number) => ({
            id: `perm-${index}`,
            permission_name: permission.name || permission.permission_name,
            permission_description: permission.description || permission.permission_description || permission.name || permission.permission_name,
            last_synced_at: new Date().toISOString(),
            isAssigned: false,
            assignedAt: undefined
          }))
        }

        console.log('Transformed permissions data:', transformedPermissions)
        setState(prev => ({
          ...prev,
          serverPermissions: { ...prev.serverPermissions, [serverId]: transformedPermissions }
        }))
      } else {
        console.error('Failed to load permissions:', permissionsResponse.status, permissionsResponse.statusText)
        setState(prev => ({
          ...prev,
          serverPermissions: { ...prev.serverPermissions, [serverId]: [] }
        }))
      }
    } catch (error) {
      console.error('Error loading server roles and permissions:', error)
    } finally {
      setState(prev => ({
        ...prev,
        rolesLoading: { ...prev.rolesLoading, [serverId]: false }
      }))
    }
  }


  const handleRoleServerSelect = (serverId: string) => {
    console.log('Server selected for role:', { serverId, selectedRoleId: state.selectedRoleId })
    if (!state.selectedRoleId) {
      console.error('No role selected')
      setState(prev => ({
        ...prev,
        error: 'Please select a role first',
        showServerSelectionModal: false
      }))
      return
    }
    handleAssignServerToRole(serverId)
    setState(prev => ({ ...prev, showServerSelectionModal: false }))
  }

  const handleAssignServerToRole = async (serverId: string) => {
    const server = state.availableServers.find(s => s.id === serverId)
    if (!server || !state.selectedRoleId) {
      console.error('Missing server or role ID:', { serverId, selectedRoleId: state.selectedRoleId })
      setState(prev => ({
        ...prev,
        error: 'Missing server or role information'
      }))
      return
    }

    // Check if the role already has access to this server
    const existingAssignment = state.roleServerAssignments.find(
      assignment => assignment.roleId === state.selectedRoleId && assignment.serverId === serverId
    )

    if (existingAssignment) {
      setState(prev => ({
        ...prev,
        error: 'Role already has access to this remote server'
      }))
      return
    }

    console.log('Attempting to assign server to role:', { serverId, roleId: state.selectedRoleId, server })

    try {
      const requestBody = {
        serverId: serverId,
        permissions: [],
        notes: `Assigned via ServerAssignmentManager to role`
      }

      const response = await fetch(`/api/roles/${state.selectedRoleId}/remote-servers`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Role server assignment successful:', result)

        const newAssignment = {
          id: result.data?.accessId || `role_assignment_${Date.now()}`,
          roleId: state.selectedRoleId,
          serverId: serverId,
          server: server,
          assignedAt: new Date(),
          grantedBy: { name: 'Current User' },
          notes: `Assigned via ServerAssignmentManager to role`,
          permissions: result.data?.finalPermissions || [],
          roles: [],
          hasAccess: true
        }

        setState(prev => ({
          ...prev,
          roleServerAssignments: [...prev.roleServerAssignments, newAssignment],
          error: null
        }))

        // Reload role assignments to get the complete data from the server
        if (state.selectedRoleId) {
          await loadRoleServerAssignments(state.selectedRoleId)
        }
      } else {
        const errorText = await response.text()
        console.error('Failed to assign server to role:', response.status, response.statusText, errorText)

        // Try to parse the error response
        let errorMessage = `Failed to assign server to role: ${response.status} ${response.statusText}`
        try {
          const errorData = JSON.parse(errorText)
          if (errorData.error) {
            errorMessage = errorData.error
          } else if (errorData.message) {
            errorMessage = errorData.message
          }
        } catch (parseError) {
          // If we can't parse the JSON, use the raw error text if it's meaningful
          if (errorText && errorText.trim() !== '') {
            errorMessage = errorText
          }
        }

        setState(prev => ({
          ...prev,
          error: errorMessage
        }))
      }
    } catch (error) {
      console.error('Error assigning server to role:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setState(prev => ({
        ...prev,
        error: `Failed to assign server to role: ${errorMessage}`
      }))
    }
  }

  const loadRoleServerAssignments = async (roleId: string) => {
    try {
      console.log('Loading role server assignments for:', roleId)
      const response = await fetch(`/api/roles/${roleId}/remote-servers`)

      if (response.ok) {
        const responseData = await response.json()
        console.log('Role assignments data received:', responseData)

        let assignmentsData = []
        if (responseData.data) {
          assignmentsData = Array.isArray(responseData.data) ? responseData.data : []
        } else if (Array.isArray(responseData)) {
          assignmentsData = responseData
        }

        const assignments = assignmentsData.map((access: any) => ({
          id: access.id,
          roleId: access.roleId || roleId,
          serverId: access.serverId,
          server: access.server,
          assignedAt: new Date(access.grantedAt || access.granted_at || access.createdAt || Date.now()),
          grantedBy: access.grantedBy,
          notes: access.notes,
          permissions: access.permissions || [],
          roles: access.roles || [],
          hasAccess: access.hasAccess !== false
        }))

        console.log('Final role assignments:', assignments)
        setState(prev => ({ ...prev, roleServerAssignments: assignments }))
      } else {
        console.error('Failed to load role server assignments:', response.status, response.statusText)
        setState(prev => ({ ...prev, roleServerAssignments: [] }))
      }
    } catch (error) {
      console.error('Error loading role server assignments:', error)
      setState(prev => ({ ...prev, roleServerAssignments: [] }))
    }
  }

  const handleRemoveRoleServerAssignment = async (assignmentId: string, serverId: string) => {
    if (!state.selectedRoleId) return

    try {
      const response = await fetch(`/api/roles/${state.selectedRoleId}/remote-servers?serverId=${serverId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setState(prev => ({
          ...prev,
          roleServerAssignments: prev.roleServerAssignments.filter(assignment => assignment.id !== assignmentId)
        }))
      } else {
        const errorData = await response.json()
        console.error('Failed to remove role server assignment:', errorData)
      }
    } catch (error) {
      console.error('Error removing role server assignment:', error)
    }
  }

  // Load role assignments when role is selected
  React.useEffect(() => {
    if (state.selectedRoleId) {
      loadRoleServerAssignments(state.selectedRoleId)
    } else {
      setState(prev => ({ ...prev, roleServerAssignments: [] }))
    }
  }, [state.selectedRoleId])

  if (state.loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-slate-700 dark:text-slate-300">Loading server assignment manager...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {state.error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {state.error}
        </div>
      )}

      <Tabs defaultValue="users" className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-emerald-600 border-0 p-1 h-auto rounded-md">
          <TabsTrigger value="users" className="flex items-center gap-2 bg-transparent border-0 text-white data-[state=active]:bg-emerald-700 data-[state=active]:text-white hover:bg-emerald-700/80">
            <User className="h-4 w-4" />
            User Assignments
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2 bg-transparent border-0 text-white data-[state=active]:bg-emerald-700 data-[state=active]:text-white hover:bg-emerald-700/80">
            <Crown className="h-4 w-4" />
            Role Assignments
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
            {/* User Search and Assign Server Button Row */}
            <div className="flex gap-6 items-end">
              {/* Left side: User Search */}
              <div className="flex-1">
                <div>
                  <h3 className="font-medium mb-3">Select User</h3>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                    <Input
                      placeholder="Type to filter users..."
                      value={state.userSearchTerm}
                      onChange={(e) => handleUserSearchChange(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              {/* Right side: Assign Server Button */}
              <div className="w-80">
                <Button
                  onClick={() => setState(prev => ({ ...prev, showServerSelectionModal: true }))}
                  className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 w-full mt-1"
                  disabled={!state.selectedUserId && !state.selectedRoleId}
                >
                  <Server className="h-4 w-4 mr-2" />
                  Assign Server {state.selectedUserId ? 'to User' : state.selectedRoleId ? 'to Role' : ''}
                </Button>
              </div>
            </div>

            {/* User List and Server Assignment Panel Row */}
            <div className="grid grid-cols-3 gap-6">
              {/* Left side: User List (1/3 width) */}
              <div>
                <div className="border border-slate-200 dark:border-slate-700 rounded-lg">
                  {state.users.filter(user => {
                    if (!state.userSearchTerm) return true
                    const searchLower = state.userSearchTerm.toLowerCase()
                    const name = user.name?.toLowerCase() || ''
                    const email = user.email?.toLowerCase() || ''
                    const firstName = user.profile?.firstName?.toLowerCase() || ''
                    const lastName = user.profile?.lastName?.toLowerCase() || ''
                    return name.includes(searchLower) ||
                           email.includes(searchLower) ||
                           firstName.includes(searchLower) ||
                           lastName.includes(searchLower)
                  }).length === 0 ? (
                    <div className="p-4 text-center text-slate-700 dark:text-slate-300">
                      {state.userSearchTerm ? 'No users found matching search' : 'No users available'}
                    </div>
                  ) : (
                    <div className="divide-y divide-slate-200 dark:divide-slate-700">
                      {state.users.filter(user => {
                        if (!state.userSearchTerm) return true
                        const searchLower = state.userSearchTerm.toLowerCase()
                        const name = user.name?.toLowerCase() || ''
                        const email = user.email?.toLowerCase() || ''
                        const firstName = user.profile?.firstName?.toLowerCase() || ''
                        const lastName = user.profile?.lastName?.toLowerCase() || ''
                        return name.includes(searchLower) ||
                               email.includes(searchLower) ||
                               firstName.includes(searchLower) ||
                               lastName.includes(searchLower)
                      }).map(user => (
                        <button
                          key={user.id}
                          onClick={() => handleUserSelect(user.id)}
                          className={`w-full p-3 text-left hover:bg-slate-50 dark:hover:bg-slate-800 ${
                            state.selectedUserId === user.id ? 'bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500' : ''
                          }`}
                        >
                          <div className="flex items-center gap-3">
                            <User className="h-4 w-4 text-slate-400" />
                            <div className="min-w-0 flex-1">
                              <div className="font-medium text-sm truncate">{getUserDisplayName(user)}</div>
                              <div className="text-xs text-slate-600 dark:text-slate-400 truncate" title={user.email || ''}>
                                {user.email && user.email.length > 30
                                  ? `${user.email.substring(0, 27)}...`
                                  : user.email
                                }
                              </div>
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Right side: Server Assignment Panel (2/3 width) */}
              <div className="col-span-2">
                {state.selectedUserId ? (
                  <div className="space-y-3">
                    <h3 className="font-medium">Assigned Servers</h3>
                    {state.userServerAssignments.filter(assignment => assignment.userId === state.selectedUserId).length === 0 ? (
                      <div className="flex items-center justify-center h-32 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg">
                        <div className="text-center text-slate-700 dark:text-slate-300">
                          <Server className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No servers assigned</p>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {state.userServerAssignments.filter(assignment => assignment.userId === state.selectedUserId).map(assignment => (
                          <ExpandableRemoteServerCard
                            key={assignment.id}
                            assignment={assignment}
                            isExpanded={state.expandedServerId === assignment.server.id}
                            isLoading={state.rolesLoading[assignment.server.id] || false}
                            error={state.error || undefined}
                            onToggleExpansion={handleToggleServerExpansion}
                            userId={state.selectedUserId || ''}
                            remoteRoles={state.serverRoles[assignment.server.id] || []}
                            rolesLoading={state.rolesLoading[assignment.server.id] || false}
                            rolesError={state.error || undefined}
                            onRoleToggle={handleRoleToggle}
                            remotePermissions={state.serverPermissions[assignment.server.id] || []}
                            permissionsLoading={state.rolesLoading[assignment.server.id] || false}
                            permissionsError={state.error || undefined}
                            onPermissionToggle={handlePermissionToggle}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg">
                    <div className="text-center text-slate-700 dark:text-slate-300">
                      <User className="h-12 w-12 mx-auto mb-3 opacity-50" />
                      <p>No user selected</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

        <TabsContent value="roles" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Role Selection Panel */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Select Role</h3>
              <div className="space-y-2">
                <input
                  type="text"
                  placeholder="Type to filter roles..."
                  value={state.roleSearchTerm}
                  onChange={(e) => setState(prev => ({ ...prev, roleSearchTerm: e.target.value }))}
                  className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100"
                />
                <div className="space-y-2">
                  {state.availableRoles
                    .filter(role =>
                      state.roleSearchTerm === '' ||
                      role.name.toLowerCase().includes(state.roleSearchTerm.toLowerCase()) ||
                      role.description?.toLowerCase().includes(state.roleSearchTerm.toLowerCase())
                    )
                    .map(role => (
                      <div
                        key={role.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          state.selectedRoleId === role.id
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                            : 'border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800'
                        }`}
                        onClick={() => setState(prev => ({ ...prev, selectedRoleId: role.id }))}
                      >
                        <div className="font-medium text-slate-900 dark:text-slate-100">
                          {role.name}
                        </div>
                        {role.description && (
                          <div className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                            {role.description}
                          </div>
                        )}
                      </div>
                    ))}
                  {state.availableRoles.filter(role =>
                    state.roleSearchTerm !== '' &&
                    (role.name.toLowerCase().includes(state.roleSearchTerm.toLowerCase()) ||
                     role.description?.toLowerCase().includes(state.roleSearchTerm.toLowerCase()))
                  ).length === 0 && state.roleSearchTerm !== '' && (
                    <div className="p-3 text-center text-slate-500 text-sm">
                      No roles match "{state.roleSearchTerm}"
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Role Server Assignments Panel */}
            <div className="lg:col-span-2">
              {state.selectedRoleId ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">
                      Server Access for {state.availableRoles.find(r => r.id === state.selectedRoleId)?.name || 'Selected Role'}
                    </h3>
                    <Button
                      onClick={() => setState(prev => ({ ...prev, showServerSelectionModal: true }))}
                      className="bg-green-600 hover:bg-green-700 text-white px-4 py-2"
                    >
                      <Server className="h-4 w-4 mr-2" />
                      Assign Server to Role
                    </Button>
                  </div>

                  {/* Assigned Servers */}
                  <div className="space-y-3">
                    {state.roleServerAssignments.length === 0 ? (
                      <div className="text-center py-8 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg">
                        <Server className="h-8 w-8 mx-auto mb-3 text-slate-300" />
                        <p className="text-sm text-slate-600 dark:text-slate-400">No servers assigned to this role yet</p>
                      </div>
                    ) : (
                      state.roleServerAssignments.map(assignment => (
                        <div key={assignment.id} className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden">
                          <div className="p-4">
                            <div className="space-y-3">
                              {/* Server Information */}
                              <div className="flex items-center gap-3">
                                <Server className="h-5 w-5 text-slate-400 flex-shrink-0" />
                                <div className="min-w-0 flex-1">
                                  <h4 className="font-medium">{assignment.server.name}</h4>
                                  <p className="text-sm text-slate-600 dark:text-slate-400 break-all">{assignment.server.url}</p>
                                </div>
                              </div>

                              {/* Server URL */}
                              <div className="text-xs text-slate-600 dark:text-slate-400 truncate" title={assignment.server.url}>
                                {assignment.server.url}
                              </div>
                            </div>
                            {assignment.server.description && (
                              <p className="text-sm text-slate-600 dark:text-slate-400 mt-3">
                                {assignment.server.description}
                              </p>
                            )}
                            <div className="flex items-center gap-2 text-xs text-slate-500 mt-3">
                              <CheckCircle className="h-3 w-3 text-green-500" />
                              Assigned {assignment.assignedAt.toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-64 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg">
                  <div className="text-center text-slate-700 dark:text-slate-300">
                    <Crown className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p>Select a role to manage its server assignments</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabsContent>

      </Tabs>

      <ServerSelectionModal
        isOpen={state.showServerSelectionModal}
        onOpenChange={(open) => setState(prev => ({ ...prev, showServerSelectionModal: open }))}
        availableServers={state.availableServers}
        selectedUser={selectedUser}
        selectedRole={selectedRole}
        onServerSelect={state.selectedRoleId ? handleRoleServerSelect : handleServerSelect}
        getUserDisplayName={getUserDisplayName}
      />
    </div>
  )
}