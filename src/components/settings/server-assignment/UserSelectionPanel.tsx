'use client'

import React from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Search, User, Server } from 'lucide-react'
import { User as UserType } from './types'

interface UserSelectionPanelProps {
  users: UserType[]
  selectedUserId: string | null
  userSearchTerm: string
  onUserSearchChange: (term: string) => void
  onUserSelect: (userId: string) => void
  getUserDisplayName: (user: UserType) => string
}

export function UserSelectionPanel({
  users,
  selectedUserId,
  userSearchTerm,
  onUserSearchChange,
  onUserSelect,
  getUserDisplayName
}: UserSelectionPanelProps) {
  // Filter users based on search term
  const filteredUsers = users.filter(user => {
    if (!userSearchTerm) return true

    const searchLower = userSearchTerm.toLowerCase()
    const name = user.name?.toLowerCase() || ''
    const email = user.email?.toLowerCase() || ''
    const firstName = user.profile?.firstName?.toLowerCase() || ''
    const lastName = user.profile?.lastName?.toLowerCase() || ''

    return name.includes(searchLower) ||
           email.includes(searchLower) ||
           firstName.includes(searchLower) ||
           lastName.includes(searchLower)
  })

  return (
    <div className="space-y-4">
      <div>
        <h3 className="font-medium mb-3">Select User</h3>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Type to filter users..."
            value={userSearchTerm}
            onChange={(e) => onUserSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <div className="border border-slate-200 dark:border-slate-700 rounded-lg">
        {filteredUsers.length === 0 ? (
          <div className="p-4 text-center text-slate-700 dark:text-slate-300">
            {userSearchTerm ? 'No users found matching search' : 'No users available'}
          </div>
        ) : (
          <div className="divide-y divide-slate-200 dark:divide-slate-700">
            {filteredUsers.map(user => (
              <button
                key={user.id}
                onClick={() => onUserSelect(user.id)}
                className={`w-full p-3 text-left hover:bg-slate-50 dark:hover:bg-slate-800 ${
                  selectedUserId === user.id ? 'bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500' : ''
                }`}
              >
                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-slate-400" />
                  <div className="min-w-0 flex-1">
                    <div className="font-medium text-sm truncate">{getUserDisplayName(user)}</div>
                    <div className="text-xs text-slate-600 dark:text-slate-400 truncate" title={user.email || ''}>
                      {user.email && user.email.length > 30
                        ? `${user.email.substring(0, 27)}...`
                        : user.email
                      }
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>

    </div>
  )
}