'use client'

import React from 'react'
import { Switch } from '@/components/ui/switch'
import { Crown } from 'lucide-react'
import { UserRole } from './types'

interface RoleManagementPanelProps {
  roles: any[]
  userRoles: UserRole[]
  selectedUserId: string | null
  serverId: string
  loading: boolean
  onRoleToggle: (roleName: string, serverId: string) => void
}

export function RoleManagementPanel({
  roles,
  userRoles,
  selectedUserId,
  serverId,
  loading,
  onRoleToggle
}: RoleManagementPanelProps) {
  const isRoleAssigned = (roleName: string) => {
    return userRoles.some(
      r => r.userId === selectedUserId &&
           r.serverId === serverId &&
           r.roleName === roleName &&
           r.isAssigned
    )
  }

  return (
    <div>
      <h5 className="font-medium mb-3 flex items-center gap-2">
        <Crown className="h-4 w-4 text-amber-500" />
        Roles
      </h5>
      <div className="space-y-2">
        {loading ? (
          <div className="text-center py-4 text-slate-500">
            Loading roles...
          </div>
        ) : roles.length > 0 ? (
          roles.map((role, index) => {
            const isAssigned = isRoleAssigned(role.name)

            return (
              <div key={index} className="flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                <div className="flex-1">
                  <div className="font-medium text-sm text-slate-800 dark:text-slate-200">
                    {role.name}
                  </div>
                  {role.description && (
                    <div className="text-xs text-slate-600 dark:text-slate-400 mt-1">
                      {role.description}
                    </div>
                  )}
                </div>
                <Switch
                  checked={isAssigned}
                  onCheckedChange={() => onRoleToggle(role.name, serverId)}
                  className={`data-[state=checked]:bg-green-600 data-[state=unchecked]:bg-red-500 transition-all duration-200 ease-in-out ${isAssigned ? 'bg-green-600' : 'bg-red-500'}`}
                />
              </div>
            )
          })
        ) : (
          <div className="text-center py-4 text-slate-500">
            No roles available for this server
          </div>
        )}
      </div>
    </div>
  )
}