'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Server, Settings, CheckCircle } from 'lucide-react'
import { UserServerAssignment, User } from './types'
import { PermissionManagementPanel } from './PermissionManagementPanel'
import { RoleManagementPanel } from './RoleManagementPanel'

interface ServerAssignmentPanelProps {
  selectedUser: User | null
  assignedServers: UserServerAssignment[]
  expandedServerId: string | null
  serverRoles: Record<string, any[]>
  serverPermissions: Record<string, any[]>
  userPermissions: any[]
  userRoles: any[]
  selectedUserId: string | null
  rolesLoading: Record<string, boolean>
  onToggleServerExpansion: (serverId: string) => void
  onPermissionToggle: (permissionName: string, serverId: string) => void
  onRoleToggle: (roleName: string, serverId: string) => void
  onAssignServer: () => void
  getUserDisplayName: (user: User) => string
}

export function ServerAssignmentPanel({
  selectedUser,
  assignedServers,
  expandedServerId,
  serverRoles,
  serverPermissions,
  userPermissions,
  userRoles,
  selectedUserId,
  rolesLoading,
  onToggleServerExpansion,
  onPermissionToggle,
  onRoleToggle,
  onAssignServer,
  getUserDisplayName
}: ServerAssignmentPanelProps) {
  if (!selectedUser) {
    return (
      <div className="flex items-center justify-center h-64 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg">
        <div className="text-center text-slate-700 dark:text-slate-300">
          <Server className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p>No user selected</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">
          Server Access for {getUserDisplayName(selectedUser)}
        </h3>
      </div>

      {/* Assigned Servers */}
      <div className="space-y-3">
        {assignedServers.length === 0 ? (
          <div className="text-center py-8 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg">
            <Server className="h-8 w-8 mx-auto mb-3 text-slate-300" />
            <p className="text-sm text-slate-600 dark:text-slate-400">No servers assigned yet</p>
          </div>
        ) : (
          assignedServers.map(assignment => (
            <div key={assignment.id} className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden">
              <div className="p-4">
                <div className="space-y-3 mb-3">
                  {/* Server Information */}
                  <div className="flex items-center gap-3">
                    <Server className="h-5 w-5 text-slate-400 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <h4 className="font-medium">{assignment.server.name}</h4>
                      <p className="text-sm text-slate-600 dark:text-slate-400 break-all">{assignment.server.url}</p>
                    </div>
                  </div>

                </div>
                {assignment.server.description && (
                  <p className="text-sm text-slate-600 dark:text-slate-400 mb-3">
                    {assignment.server.description}
                  </p>
                )}
                <div className="flex items-center gap-2 text-xs text-slate-500">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  Assigned {assignment.assignedAt.toLocaleDateString()}
                </div>
              </div>

              {/* Expandable Permissions Section */}
              {expandedServerId === assignment.server.id && (
                <div className="border-t border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900">
                  <div className="p-4 space-y-6">
                    <PermissionManagementPanel
                      permissions={serverPermissions[assignment.server.id] || []}
                      userPermissions={userPermissions}
                      selectedUserId={selectedUserId}
                      serverId={assignment.server.id}
                      loading={rolesLoading[assignment.server.id] || false}
                      onPermissionToggle={onPermissionToggle}
                    />

                    <RoleManagementPanel
                      roles={serverRoles[assignment.server.id] || []}
                      userRoles={userRoles}
                      selectedUserId={selectedUserId}
                      serverId={assignment.server.id}
                      loading={rolesLoading[assignment.server.id] || false}
                      onRoleToggle={onRoleToggle}
                    />
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  )
}