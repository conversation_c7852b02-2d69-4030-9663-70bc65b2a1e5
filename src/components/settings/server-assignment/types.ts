export interface User {
  id: string
  name: string | null
  email: string | null
  profile?: {
    firstName: string | null
    lastName: string | null
  } | null
}

export interface RemoteServer {
  id: string
  name: string
  url: string
  description: string | null
  isActive: boolean
  permissions?: Array<{
    permission_name: string
    permission_description: string
    last_synced_at: string
  }>
  roles?: any[]
}

export interface UserServerAssignment {
  id: string
  userId: string
  serverId: string
  server: RemoteServer
  assignedAt: Date
}

export interface UserPermission {
  id: string
  userId: string
  serverId: string
  permissionName: string
  isAssigned: boolean
  assignedAt?: Date
}

export interface UserRole {
  id: string
  userId: string
  serverId: string
  roleName: string
  isAssigned: boolean
  assignedAt?: Date
}

export interface ServerAssignmentState {
  users: User[]
  availableServers: RemoteServer[]
  selectedUserId: string | null
  userSearchTerm: string
  loading: boolean
  error: string | null
  userServerAssignments: UserServerAssignment[]
  userPermissions: UserPermission[]
  userRoles: UserRole[]
  showServerSelectionModal: boolean
  expandedServerId: string | null
  serverRoles: Record<string, any[]>
  serverPermissions: Record<string, any[]>
  rolesLoading: Record<string, boolean>
  syncLoading: Record<string, boolean>
  availableRoles: any[]
  selectedRoleId: string | null
  roleSearchTerm: string
  roleServerAssignments: any[]
}

export interface ServerAssignmentProps {
  // Add any props if needed in the future
}