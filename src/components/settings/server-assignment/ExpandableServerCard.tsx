'use client';

import React from 'react';
import { ChevronDown, ChevronUp, AlertTriangle } from 'lucide-react';
import { RemoteRoleManagement } from './RemoteRoleManagement';
import { RemotePermissionManagement } from './RemotePermissionManagement';

interface RemoteServer {
  id: string;
  name: string;
  url: string;
  description: string | null;
  isActive: boolean;
  permissions?: Array<{
    permission_name: string;
    permission_description: string;
    last_synced_at: string;
  }>;
  roles?: any[];
}

interface UserServerAssignment {
  id: string;
  userId: string;
  serverId: string;
  server: RemoteServer;
  assignedAt: Date;
}

interface ExpandableServerCardProps {
  assignment: UserServerAssignment;
  isExpanded: boolean;
  isLoading?: boolean;
  error?: string;
  onToggleExpansion: (serverId: string) => void;
  userId: string;
  remoteRoles?: Array<{
    id: string;
    name: string;
    description?: string;
    permissions?: string[];
    isAssigned?: boolean;
    assignedAt?: Date;
  }>;
  rolesLoading?: boolean;
  rolesError?: string;
  onRoleToggle: (roleName: string, serverId: string) => Promise<void>;
  remotePermissions?: Array<{
    id: string;
    permission_name: string;
    permission_description: string;
    last_synced_at: string;
    isAssigned?: boolean;
    assignedAt?: Date;
  }>;
  permissionsLoading?: boolean;
  permissionsError?: string;
  onPermissionToggle: (permissionName: string, serverId: string) => Promise<void>;
}

export const ExpandableRemoteServerCard: React.FC<ExpandableServerCardProps> = ({
  assignment,
  isExpanded,
  isLoading = false,
  error,
  onToggleExpansion,
  userId,
  remoteRoles = [],
  rolesLoading = false,
  rolesError,
  onRoleToggle,
  remotePermissions = [],
  permissionsLoading = false,
  permissionsError,
  onPermissionToggle,
}) => {
  const { server, assignedAt } = assignment;

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const handleToggle = () => {
    if (onToggleExpansion && typeof onToggleExpansion === 'function') {
      onToggleExpansion(server.id);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-2/3"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-400 mr-2" />
          <span className="text-red-800 text-sm">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {truncateText(server.name, 50)}
              </h3>
              <span
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  server.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                {server.isActive ? 'active' : 'inactive'}
              </span>
            </div>

            <p className="text-sm text-gray-600 mb-2 break-all">
              {truncateText(server.url, 60)}
            </p>

            {server.description && (
              <p className="text-sm text-gray-700 mb-3 line-clamp-2">
                {server.description}
              </p>
            )}

            <div className="flex items-center text-xs text-gray-500">
              <span>Assigned: {formatDate(assignedAt)}</span>
            </div>
          </div>

          <button
            onClick={handleToggle}
            className="ml-4 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors duration-200 flex-shrink-0"
            aria-label={isExpanded ? 'Collapse server details' : 'Expand server details'}
          >
            {isExpanded ? (
              <ChevronUp className="h-5 w-5" />
            ) : (
              <ChevronDown className="h-5 w-5" />
            )}
          </button>
        </div>

        {isExpanded && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="space-y-4">
              <RemoteRoleManagement
                serverId={server.id}
                userId={userId}
                roles={remoteRoles}
                loading={rolesLoading}
                error={rolesError}
                onRoleToggle={onRoleToggle}
              />

              <RemotePermissionManagement
                serverId={server.id}
                userId={userId}
                permissions={remotePermissions}
                loading={permissionsLoading}
                error={permissionsError}
                onPermissionToggle={onPermissionToggle}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};