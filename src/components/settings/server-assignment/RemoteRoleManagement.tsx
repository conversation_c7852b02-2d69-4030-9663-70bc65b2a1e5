'use client';

import React, { useState, useEffect } from 'react';
import { Crown, Loader2, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';

interface RemoteRole {
  id: string;
  name: string;
  description?: string;
  permissions?: string[];
  isAssigned?: boolean;
  assignedAt?: Date;
}

interface RemoteRoleManagementProps {
  serverId: string;
  userId: string;
  roles: RemoteRole[];
  loading: boolean;
  error?: string;
  onRoleToggle: (roleName: string, serverId: string) => Promise<void>;
}

export const RemoteRoleManagement: React.FC<RemoteRoleManagementProps> = ({
  serverId,
  userId,
  roles,
  loading,
  error,
  onRoleToggle,
}) => {
  const [localRoles, setLocalRoles] = useState<RemoteRole[]>(roles);
  const [togglingRoles, setTogglingRoles] = useState<Record<string, boolean>>({});

  useEffect(() => {
    setLocalRoles(roles);
  }, [roles]);

  const handleRoleToggle = async (roleName: string) => {
    if (togglingRoles[roleName]) return; // Prevent multiple simultaneous toggles

    setTogglingRoles(prev => ({ ...prev, [roleName]: true }));

    try {
      await onRoleToggle(roleName, serverId);

      // Update local state optimistically
      setLocalRoles(prev =>
        prev.map(role =>
          role.name === roleName
            ? { ...role, isAssigned: !role.isAssigned, assignedAt: !role.isAssigned ? new Date() : undefined }
            : role
        )
      );
    } catch (error) {
      console.error('Failed to toggle role:', error);
      // Revert optimistic update on error
      setLocalRoles(roles);
    } finally {
      setTogglingRoles(prev => ({ ...prev, [roleName]: false }));
    }
  };

  if (loading) {
    return (
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
          <Crown className="h-4 w-4 mr-2" />
          Remote Roles
        </h4>
        <div className="animate-pulse space-y-2">
          {[1, 2, 3].map(i => (
            <div key={i} className="h-12 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
          <Crown className="h-4 w-4 mr-2" />
          Remote Roles
        </h4>
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-red-400 mr-2" />
            <span className="text-red-800 text-sm">{error}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
        <Crown className="h-4 w-4 mr-2" />
        Remote Roles
        <span className="ml-2 text-xs text-gray-500">
          ({localRoles.filter(r => r.isAssigned).length}/{localRoles.length} assigned)
        </span>
      </h4>

      {localRoles.length === 0 ? (
        <div className="text-center py-4 border-2 border-dashed border-gray-300 rounded-lg">
          <Crown className="h-6 w-6 mx-auto mb-2 text-gray-300" />
          <p className="text-sm text-gray-600">No roles available for this server</p>
        </div>
      ) : (
        <div className="space-y-2 max-h-[500px] overflow-y-auto">
          {localRoles.map(role => {
            const isToggling = togglingRoles[role.name];

            return (
              <div
                key={role.id}
                className={`border rounded-lg p-3 transition-colors ${
                  role.isAssigned
                    ? 'border-blue-200 bg-blue-50'
                    : 'border-gray-200 bg-white hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h5 className="text-sm font-medium text-gray-900 truncate">
                        {role.name}
                      </h5>
                      {role.isAssigned && (
                        <CheckCircle className="h-4 w-4 text-blue-500 flex-shrink-0" />
                      )}
                    </div>

                    {role.description && (
                      <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                        {role.description}
                      </p>
                    )}

                    {role.permissions && role.permissions.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {role.permissions.slice(0, 3).map((permission, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                          >
                            {permission}
                          </span>
                        ))}
                        {role.permissions.length > 3 && (
                          <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            +{role.permissions.length - 3} more
                          </span>
                        )}
                      </div>
                    )}

                    {role.isAssigned && role.assignedAt && (
                      <p className="text-xs text-blue-600 mt-1">
                        Assigned {role.assignedAt.toLocaleDateString()}
                      </p>
                    )}
                  </div>

                  <div className="ml-3 flex items-center">
                    {isToggling ? (
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                    ) : (
                      <Switch
                        checked={role.isAssigned || false}
                        onCheckedChange={() => handleRoleToggle(role.name)}
                        disabled={isToggling}
                      />
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};