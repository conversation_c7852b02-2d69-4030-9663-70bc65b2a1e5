'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Server,
  Trash2,
  Key,
  Eye,
  EyeOff,
  Link,
  RefreshCw,
  AlertCircle
} from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { RemoteServer, ServerCardProps, Permission, Role } from './types'

export function RemoteServerCard({
  server,
  isEditing,
  editData,
  onEdit,
  onSave,
  onCancel,
  onUpdateEditData,
  onFetchPermissions,
  onRemove,
  permissionsLoading,
  serverPermissions,
  showClientSecret,
  showApiKey,
  onToggleClientSecret,
  onToggleApiKey,
  serverStatus
}: ServerCardProps) {
  const [showDetails, setShowDetails] = useState(false)

  const getCallbackUrl = (baseUrl: string) => {
    try {
      const url = new URL(baseUrl)
      return `${url.origin}/api/auth/member-portal`
    } catch {
      return `${baseUrl}/api/auth/member-portal`
    }
  }

  return (
    <div className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden">
      {/* Header */}
      <div className="p-4 hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
        <div className="space-y-3 mb-3">
          {/* Server Information */}
          <div className="flex items-center gap-3">
            <Server className="h-5 w-5 text-slate-400 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2">
                <h4 className="font-medium text-slate-900 dark:text-slate-100 truncate">
                  {typeof server.name === 'string' ? server.name : (server.name ? JSON.stringify(server.name) : 'Unknown Server')}
                </h4>
                {serverStatus && (
                  <div className={`w-2 h-2 rounded-full flex-shrink-0 ${
                    serverStatus.reachable
                      ? 'bg-green-500'
                      : serverStatus.lastChecked
                        ? 'bg-red-500'
                        : 'bg-gray-400'
                  }`} title={
                    serverStatus.reachable
                      ? 'Server is reachable'
                      : serverStatus.error || 'Server connectivity unknown'
                  }></div>
                )}
              </div>
              <div className="flex items-center gap-2 mt-1">
                <div className={`w-2 h-2 rounded-full ${server.isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-xs text-slate-500">
                  {server.isActive ? 'Active' : 'Inactive'}
                </span>
                {serverStatus && serverStatus.lastChecked && (
                  <span className="text-xs text-slate-400">
                    • Last checked: {serverStatus.lastChecked.toLocaleTimeString()}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm" onClick={() => setShowDetails(!showDetails)} className="flex-1 min-w-0">
              {showDetails ? 'Hide' : 'View'} Details
            </Button>
            <Button variant="outline" size="sm" onClick={onFetchPermissions} disabled={permissionsLoading} className="flex-1 min-w-0">
              <RefreshCw className={`w-4 h-4 mr-1 ${permissionsLoading ? 'animate-spin' : ''}`} />
              {permissionsLoading ? 'Syncing...' : 'Sync Permissions & Roles'}
            </Button>
            <Button
              variant={isEditing ? "default" : "outline"}
              size="sm"
              onClick={() => {
                console.log('Edit button clicked for server:', server.id, server.name);
                console.log('Current editing state:', isEditing);
                alert(`Edit button clicked for server: ${server.name} (ID: ${server.id})`);
                try {
                  onEdit();
                  console.log('onEdit function called successfully');
                } catch (error) {
                  console.error('Error calling onEdit function:', error);
                }
              }}
              className="flex-1 min-w-0"
            >
              {isEditing ? 'Editing...' : 'Edit'}
            </Button>
            <Button variant="outline" size="sm" onClick={onRemove} className="flex-1 min-w-0">
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {server.description && typeof server.description === 'string' && (
          <p className="text-sm text-slate-600 dark:text-slate-400 mb-3">{server.description}</p>
        )}

        <div className="text-xs text-slate-500 truncate">
          {typeof server.url === 'string' ? server.url : JSON.stringify(server.url)}
        </div>
      </div>

      {/* Expandable Details */}
      {(showDetails || isEditing) && (
        <div className="border-t border-slate-200 dark:border-slate-700">
          <div className="p-4 space-y-6">
            {isEditing ? (
              <>
                {/* Description */}
                <div>
                  <Label>Description</Label>
                  <Textarea
                    value={typeof editData.description === 'string' ? editData.description : (editData.description ? String(editData.description) : (server.description || ''))}
                    onChange={(e) => onUpdateEditData({ ...editData, description: e.target.value })}
                  />
                </div>

                {/* OAuth Credentials */}
                <div className="pt-4 border-t border-slate-200">
                  <h4 className="text-lg font-medium flex items-center"><Key className="mr-2" />OAuth Credentials</h4>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div>
                      <Label>Client ID</Label>
                      <Input
                        value={typeof editData.clientId === 'string' ? editData.clientId : (editData.clientId ? String(editData.clientId) : (server.clientId || ''))}
                        onChange={(e) => onUpdateEditData({ ...editData, clientId: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label>Client Secret</Label>
                      <div className="flex space-x-2">
                        <Input
                          type={showClientSecret ? "text" : "password"}
                          value={typeof editData.clientSecret === 'string' ? editData.clientSecret : (editData.clientSecret ? String(editData.clientSecret) : (server.clientSecret || ''))}
                          onChange={(e) => onUpdateEditData({ ...editData, clientSecret: e.target.value })}
                          className="flex-1 font-mono text-sm"
                        />
                        <Button variant="outline" size="icon" onClick={onToggleClientSecret}>
                          {showClientSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Frontend Config */}
                <div className="pt-4 border-t border-slate-200">
                  <h4 className="text-lg font-medium flex items-center"><Server className="mr-2 text-green-500" />Frontend Configuration</h4>
                  <Input
                    value={typeof editData.url === 'string' ? editData.url : (editData.url ? String(editData.url) : (server.url || ''))}
                    onChange={(e) => onUpdateEditData({ ...editData, url: e.target.value })}
                  />
                </div>

                {/* Backend/API Config */}
                <div className="pt-4 border-t border-slate-200">
                  <h4 className="text-lg font-medium flex items-center"><Link className="mr-2 text-purple-500" />Backend/API Configuration</h4>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div>
                      <Label>API Endpoint</Label>
                      <Input
                        value={typeof editData.apiEndpoint === 'string' ? editData.apiEndpoint : (editData.apiEndpoint ? String(editData.apiEndpoint) : (server.apiEndpoint || ''))}
                        onChange={(e) => onUpdateEditData({ ...editData, apiEndpoint: e.target.value })}
                        placeholder="https://api.example.com"
                      />
                    </div>
                    <div>
                      <Label>API Key</Label>
                      <div className="flex space-x-2">
                        <Input
                          type={showApiKey ? "text" : "password"}
                          value={typeof editData.apiKey === 'string' ? editData.apiKey : (editData.apiKey ? String(editData.apiKey) : (server.apiKey || ''))}
                          onChange={(e) => onUpdateEditData({ ...editData, apiKey: e.target.value })}
                          className="flex-1 font-mono text-sm"
                        />
                        <Button variant="outline" size="icon" onClick={onToggleApiKey}>
                          {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-slate-50 rounded-md">
                    <Label>Callback URL</Label>
                    <p className="font-mono text-sm break-all">
                      {getCallbackUrl(typeof editData.url === 'string' ? editData.url : (editData.url ? String(editData.url) : (server.url || '')))}
                    </p>
                  </div>
                </div>

                {/* Status */}
                <div className="pt-4 border-t border-slate-200">
                  <Label>Status</Label>
                  <input
                    type="checkbox"
                    checked={editData.isActive ?? server.isActive}
                    onChange={(e) => onUpdateEditData({ ...editData, isActive: e.target.checked })}
                  />
                  {editData.isActive ?? server.isActive ? 'Active' : 'Inactive'}
                </div>

                <div className="flex gap-3 pt-4">
                  <Button onClick={onSave}>Save</Button>
                  <Button variant="outline" onClick={onCancel}>Cancel</Button>
                </div>
              </>
            ) : (
              <>
                <div>
                  <Label>Description</Label>
                  <p>{typeof server.description === 'string' ? server.description : (server.description ? JSON.stringify(server.description) : 'No description')}</p>
                </div>
                <div className="pt-4 border-t"><Label>Client ID</Label><p>{typeof server.clientId === 'string' ? server.clientId : (server.clientId ? JSON.stringify(server.clientId) : 'Not set')}</p></div>
                <div className="pt-4 border-t"><Label>Frontend URL</Label><p>{typeof server.url === 'string' ? server.url : JSON.stringify(server.url)}</p></div>
                <div className="pt-4 border-t"><Label>API Endpoint</Label><p>{typeof server.apiEndpoint === 'string' ? server.apiEndpoint : (server.apiEndpoint ? JSON.stringify(server.apiEndpoint) : 'Using frontend URL')}</p></div>
                <div className="pt-4 border-t"><Label>Status</Label><p>{server.isActive ? 'Active' : 'Inactive'}</p></div>
              </>
            )}

            {/* Server Status */}
            {serverStatus && !serverStatus.reachable && serverStatus.error && (
              <div className="pt-4 border-t">
                <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h5 className="font-medium text-red-800 dark:text-red-200 mb-1">
                        Server Connectivity Issue
                      </h5>
                      <p className="text-sm text-red-700 dark:text-red-300 mb-2">
                        {typeof serverStatus.error === 'string' ? serverStatus.error : JSON.stringify(serverStatus.error)}
                      </p>
                      <p className="text-xs text-red-600 dark:text-red-400">
                        Last checked: {serverStatus.lastChecked?.toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Permissions */}
            <div className="pt-4 border-t">
              <h4 className="text-lg font-medium flex items-center mb-4 text-slate-800 dark:text-slate-100">
                <Key className="mr-2" />Permissions & Roles
              </h4>
              {serverPermissions ? (
                <div className="space-y-8">
                  {/* Summary Stats */}
                  <div className="grid grid-cols-2 gap-4 p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {serverPermissions.roles.length}
                      </div>
                      <div className="text-sm text-slate-600 dark:text-slate-400">Roles</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {serverPermissions.availablePermissions.length}
                      </div>
                      <div className="text-sm text-slate-600 dark:text-slate-400">Available Permissions</div>
                    </div>
                  </div>

                  {/* Available Permissions List */}
                  <div>
                    <h5 className="font-medium text-slate-800 dark:text-slate-100 mb-3">
                      Available Permissions ({serverPermissions.availablePermissions.length})
                    </h5>
                    {serverPermissions.availablePermissions.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {serverPermissions.availablePermissions.map(p => (
                          <div key={typeof p.name === 'string' ? p.name : JSON.stringify(p.name)} className="p-3 bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-md">
                            <div className="font-medium text-slate-900 dark:text-slate-100">{typeof p.name === 'string' ? p.name : JSON.stringify(p.name)}</div>
                            {p.description && typeof p.description === 'string' && (
                              <div className="text-sm text-slate-600 dark:text-slate-400 mt-1">{p.description}</div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-4 bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg">
                        <div className="flex items-start gap-3">
                          <AlertCircle className="h-5 w-5 text-slate-600 dark:text-slate-400 mt-0.5 flex-shrink-0" />
                          <div>
                            <h6 className="font-medium text-slate-800 dark:text-slate-200 mb-1">
                              No Available Permissions
                            </h6>
                            <p className="text-sm text-slate-600 dark:text-slate-400">
                              No individual permissions are available for assignment. All permissions are managed through roles.
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Roles List */}
                  <div>
                    <h5 className="font-medium text-slate-800 dark:text-slate-100 mb-3">
                      Roles ({serverPermissions.roles.length})
                    </h5>
                    {serverPermissions.roles.length > 0 ? (
                      <div className="space-y-3">
                        {serverPermissions.roles.map(r => (
                          <div key={typeof r.name === 'string' ? r.name : JSON.stringify(r.name)} className="p-4 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-600 rounded-md">
                            <div className="flex items-start justify-between mb-2">
                              <div className="font-medium text-slate-900 dark:text-slate-100">{typeof r.name === 'string' ? r.name : JSON.stringify(r.name)}</div>
                              <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">
                                {Array.isArray(r.permissions) ? r.permissions.length : 0} permissions
                              </span>
                            </div>
                            {r.description && typeof r.description === 'string' && (
                              <div className="text-sm text-slate-600 dark:text-slate-400 mb-3">{r.description}</div>
                            )}
                            {r.permissions && Array.isArray(r.permissions) && r.permissions.length > 0 && (
                              <div>
                                <div className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Permissions:</div>
                                <div className="flex flex-wrap gap-1">
                                  {r.permissions.map((permName, idx) => (
                                    <span key={idx} className="text-xs bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 px-2 py-1 rounded">
                                      {typeof permName === 'string' ? permName : JSON.stringify(permName)}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-4 bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg">
                        <div className="flex items-start gap-3">
                          <AlertCircle className="h-5 w-5 text-slate-600 dark:text-slate-400 mt-0.5 flex-shrink-0" />
                          <div>
                            <h6 className="font-medium text-slate-800 dark:text-slate-200 mb-1">
                              No Roles Found
                            </h6>
                            <p className="text-sm text-slate-600 dark:text-slate-400">
                              This server doesn't have any roles configured, or they couldn't be retrieved.
                              Roles are optional and some servers may not use them.
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Key className="mx-auto h-12 w-12 text-slate-300 dark:text-slate-600 mb-3" />
                  <p className="text-sm text-slate-500 dark:text-slate-400">
                    Click "Get Permissions" to load permissions and roles from this server
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}