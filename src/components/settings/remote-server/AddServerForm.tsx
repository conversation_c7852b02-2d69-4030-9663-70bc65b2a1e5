'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Plus, Eye, EyeOff, Server, Link, Key, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { RemoteServer, RemoteServerFormData } from './types'

interface AddServerFormProps {
  onAddServer: (server: RemoteServerFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function AddServerForm({ onAddServer, onCancel, isLoading = false }: AddServerFormProps) {
  const [formData, setFormData] = useState<RemoteServerFormData>({
    name: '',
    description: null,
    url: '',
    apiEndpoint: null,
    clientId: null,
    clientSecret: null,
    apiKey: '',
    isActive: true,
    oauthRedirectUris: null,
    developmentRedirectUris: null,
    redirectUris: null
  })

  const [showClientSecret, setShowClientSecret] = useState(false)
  const [showApiKey, setShowApiKey] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Server name is required'
    }

    if (!formData.url.trim()) {
      newErrors.url = 'Server URL is required'
    } else {
      try {
        new URL(formData.url)
      } catch {
        newErrors.url = 'Please enter a valid URL'
      }
    }

    if (formData.apiEndpoint && formData.apiEndpoint.trim()) {
      try {
        new URL(formData.apiEndpoint)
      } catch {
        newErrors.apiEndpoint = 'Please enter a valid API endpoint URL'
      }
    }

    if (!formData.clientId?.trim()) {
      newErrors.clientId = 'Client ID is required for OAuth'
    }

    if (!formData.clientSecret?.trim()) {
      newErrors.clientSecret = 'Client Secret is required for OAuth'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      await onAddServer(formData)
      // Reset form on success
      setFormData({
        name: '',
        description: null,
        url: '',
        apiEndpoint: null,
        clientId: null,
        clientSecret: null,
        apiKey: '',
        isActive: true,
        oauthRedirectUris: null,
        developmentRedirectUris: null,
        redirectUris: null
      })
      setErrors({})
    } catch (error) {
      console.error('Error adding server:', error)
    }
  }

  const getCallbackUrl = (baseUrl: string | null) => {
    if (!baseUrl) return ''
    try {
      const url = new URL(baseUrl)
      return `${url.origin}/api/auth/member-portal`
    } catch {
      return `${baseUrl}/api/auth/member-portal`
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Plus className="h-5 w-5" />
          Add Remote Server
        </CardTitle>
        <CardDescription>
          Configure a new remote server connection for OAuth authentication and API access.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium flex items-center">
              <Server className="mr-2 text-blue-500" />
              Basic Information
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Server Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="e.g., NWAPromote Server"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="url">Frontend URL *</Label>
                <Input
                  id="url"
                  type="url"
                  value={formData.url}
                  onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                  placeholder="https://nwabusinessalliance.com"
                  className={errors.url ? 'border-red-500' : ''}
                />
                {errors.url && (
                  <p className="text-sm text-red-600">{errors.url}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => setFormData({ ...formData, description: e.target.value || null })}
                placeholder="Optional description of this server..."
                rows={3}
              />
            </div>
          </div>

          {/* OAuth Configuration */}
          <div className="space-y-4 pt-4 border-t">
            <h4 className="text-lg font-medium flex items-center">
              <Key className="mr-2 text-green-500" />
              OAuth Configuration
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="clientId">Client ID *</Label>
                <Input
                  id="clientId"
                  value={formData.clientId || ''}
                  onChange={(e) => setFormData({ ...formData, clientId: e.target.value || null })}
                  placeholder="OAuth client identifier"
                  className={errors.clientId ? 'border-red-500' : ''}
                />
                {errors.clientId && (
                  <p className="text-sm text-red-600">{errors.clientId}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientSecret">Client Secret *</Label>
                <div className="flex space-x-2">
                  <Input
                    id="clientSecret"
                    type={showClientSecret ? "text" : "password"}
                    value={formData.clientSecret || ''}
                    onChange={(e) => setFormData({ ...formData, clientSecret: e.target.value || null })}
                    placeholder="OAuth client secret"
                    className={`flex-1 ${errors.clientSecret ? 'border-red-500' : ''}`}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => setShowClientSecret(!showClientSecret)}
                  >
                    {showClientSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
                {errors.clientSecret && (
                  <p className="text-sm text-red-600">{errors.clientSecret}</p>
                )}
              </div>
            </div>
          </div>

          {/* API Configuration */}
          <div className="space-y-4 pt-4 border-t">
            <h4 className="text-lg font-medium flex items-center">
              <Link className="mr-2 text-purple-500" />
              API Configuration
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="apiEndpoint">API Endpoint</Label>
                <Input
                  id="apiEndpoint"
                  type="url"
                  value={formData.apiEndpoint || ''}
                  onChange={(e) => setFormData({ ...formData, apiEndpoint: e.target.value || null })}
                  placeholder="https://api.nwabusinessalliance.com"
                  className={errors.apiEndpoint ? 'border-red-500' : ''}
                />
                {errors.apiEndpoint && (
                  <p className="text-sm text-red-600">{errors.apiEndpoint}</p>
                )}
                <p className="text-xs text-slate-500">
                  Leave empty to use the frontend URL
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="apiKey">API Key</Label>
                <div className="flex space-x-2">
                  <Input
                    id="apiKey"
                    type={showApiKey ? "text" : "password"}
                    value={formData.apiKey}
                    onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
                    placeholder="Optional API key for additional authentication"
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => setShowApiKey(!showApiKey)}
                  >
                    {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            </div>

            {/* Callback URL Display */}
            {formData.url && (
              <div className="p-3 bg-slate-50 dark:bg-slate-800 rounded-md">
                <Label>Callback URL</Label>
                <p className="font-mono text-sm break-all mt-1">
                  {getCallbackUrl(formData.url)}
                </p>
                <p className="text-xs text-slate-500 mt-1">
                  This URL will be used by the remote server to redirect users back to your application
                </p>
              </div>
            )}
          </div>

          {/* Status Configuration */}
          <div className="space-y-4 pt-4 border-t">
            <h4 className="text-lg font-medium">Status</h4>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isActive"
                checked={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
              />
              <Label htmlFor="isActive">Server is active</Label>
            </div>
          </div>

          {/* Validation Summary */}
          {Object.keys(errors).length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Validation Errors</AlertTitle>
              <AlertDescription>
                Please fix the errors above before submitting the form.
              </AlertDescription>
            </Alert>
          )}

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Adding Server...' : 'Add Server'}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}