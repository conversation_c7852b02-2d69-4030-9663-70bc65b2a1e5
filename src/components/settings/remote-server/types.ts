export interface RemoteServer {
  id: string
  name: string
  url: string
  apiEndpoint: string | null
  oauthRedirectUris: string[] | null
  developmentRedirectUris: string[] | null
  apiKey: string
  description: string | null
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  clientId: string | null
  clientSecret: string | null
  redirectUris: string[] | null
}

export interface Permission {
  name: string
  description: string
}

export interface Role {
  name: string
  description: string
  permissions: string[]
}

export interface ServerPermissions {
  permissions: Permission[]
  roles: Role[]
  availablePermissions: Permission[]
  rolePermissions: Record<string, string[]>
  summary: any
}

export interface ServerStatus {
  reachable: boolean
  lastChecked: Date | null
  error?: string
}

export interface ServerCardProps {
  server: RemoteServer
  isEditing: boolean
  editData: Partial<RemoteServer>
  onEdit: () => void
  onSave: () => void
  onCancel: () => void
  onUpdateEditData: (data: Partial<RemoteServer>) => void
  onFetchPermissions: () => void
  onRemove: () => void
  permissionsLoading: boolean
  serverPermissions: ServerPermissions | undefined
  showClientSecret: boolean
  showApiKey: boolean
  onToggleClientSecret: () => void
  onToggleApiKey: () => void
  serverStatus: ServerStatus | undefined
}

export interface RemoteServerFormData extends Omit<RemoteServer, 'id' | 'createdAt' | 'updatedAt'> {
  // Form-specific fields can be added here
}