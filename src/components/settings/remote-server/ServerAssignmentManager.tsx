'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  Search,
  UserCheck,
  Shield,
  Key,
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw
} from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { RemoteServer, Permission, Role, ServerPermissions } from './types'

interface User {
  id: string
  name: string
  email: string
  roles: string[]
  permissions: string[]
}

interface ServerAssignmentManagerProps {
  server: RemoteServer
  serverPermissions: ServerPermissions | undefined
  onAssignUser: (userId: string, permissions: string[], roles: string[]) => Promise<void>
  onUnassignUser: (userId: string) => Promise<void>
  onUpdateUserPermissions: (userId: string, permissions: string[]) => Promise<void>
  onUpdateUserRoles: (userId: string, roles: string[]) => Promise<void>
  isLoading?: boolean
}

export function ServerAssignmentManager({
  server,
  serverPermissions,
  onAssignUser,
  onUnassignUser,
  onUpdateUserPermissions,
  onUpdateUserRoles,
  isLoading = false
}: ServerAssignmentManagerProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([])
  const [selectedRoles, setSelectedRoles] = useState<string[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  // Mock users data - in real app, this would come from an API
  useEffect(() => {
    const mockUsers: User[] = [
      {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        roles: ['admin'],
        permissions: ['read', 'write', 'delete']
      },
      {
        id: '2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        roles: ['user'],
        permissions: ['read']
      },
      {
        id: '3',
        name: 'Bob Johnson',
        email: '<EMAIL>',
        roles: [],
        permissions: []
      }
    ]
    setUsers(mockUsers)
  }, [])

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleUserSelect = (user: User) => {
    setSelectedUser(user)
    setSelectedPermissions(user.permissions)
    setSelectedRoles(user.roles)
  }

  const handlePermissionToggle = (permissionName: string) => {
    setSelectedPermissions(prev =>
      prev.includes(permissionName)
        ? prev.filter(p => p !== permissionName)
        : [...prev, permissionName]
    )
  }

  const handleRoleToggle = (roleName: string) => {
    setSelectedRoles(prev =>
      prev.includes(roleName)
        ? prev.filter(r => r !== roleName)
        : [...prev, roleName]
    )
  }

  const handleSaveAssignments = async () => {
    if (!selectedUser) return

    setLoading(true)
    try {
      await onUpdateUserPermissions(selectedUser.id, selectedPermissions)
      await onUpdateUserRoles(selectedUser.id, selectedRoles)

      // Update local state
      setUsers(prev => prev.map(user =>
        user.id === selectedUser.id
          ? { ...user, permissions: selectedPermissions, roles: selectedRoles }
          : user
      ))

      setMessage({ type: 'success', text: 'User permissions and roles updated successfully' })
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to update user assignments' })
    } finally {
      setLoading(false)
    }
  }

  const handleUnassignUser = async () => {
    if (!selectedUser) return

    setLoading(true)
    try {
      await onUnassignUser(selectedUser.id)

      // Update local state
      setUsers(prev => prev.map(user =>
        user.id === selectedUser.id
          ? { ...user, permissions: [], roles: [] }
          : user
      ))

      setSelectedUser(null)
      setSelectedPermissions([])
      setSelectedRoles([])
      setMessage({ type: 'success', text: 'User unassigned from server successfully' })
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to unassign user' })
    } finally {
      setLoading(false)
    }
  }

  const getUserStatus = (user: User) => {
    if (user.roles.length > 0 || user.permissions.length > 0) {
      return { status: 'assigned', label: 'Assigned', color: 'bg-green-100 text-green-800' }
    }
    return { status: 'unassigned', label: 'Not Assigned', color: 'bg-gray-100 text-gray-800' }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            User Assignment Manager
          </CardTitle>
          <CardDescription>
            Assign users to {typeof server.name === 'string' ? server.name : 'this server'} and manage their permissions and roles.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* User List */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Search className="h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1"
                />
              </div>

              <div className="space-y-2 max-h-64 overflow-y-auto">
                {filteredUsers.map(user => {
                  const userStatus = getUserStatus(user)
                  return (
                    <div
                      key={user.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedUser?.id === user.id
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800'
                      }`}
                      onClick={() => handleUserSelect(user)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-slate-900 dark:text-slate-100">
                            {user.name}
                          </div>
                          <div className="text-sm text-slate-500">{user.email}</div>
                        </div>
                        <Badge className={userStatus.color}>
                          {userStatus.label}
                        </Badge>
                      </div>
                      {(user.roles.length > 0 || user.permissions.length > 0) && (
                        <div className="mt-2 text-xs text-slate-600 dark:text-slate-400">
                          {user.roles.length > 0 && (
                            <span className="mr-2">
                              <Shield className="inline h-3 w-3 mr-1" />
                              {user.roles.join(', ')}
                            </span>
                          )}
                          {user.permissions.length > 0 && (
                            <span>
                              <Key className="inline h-3 w-3 mr-1" />
                              {user.permissions.length} permissions
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Assignment Panel */}
            <div className="space-y-4">
              {selectedUser ? (
                <>
                  <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <UserCheck className="h-4 w-4" />
                      <span className="font-medium">
                        {selectedUser.name}
                      </span>
                    </div>
                    <div className="text-sm text-slate-600 dark:text-slate-400">
                      {selectedUser.email}
                    </div>
                  </div>

                  {/* Permissions */}
                  {serverPermissions && serverPermissions.availablePermissions.length > 0 && (
                    <div className="space-y-3">
                      <Label className="text-base font-medium flex items-center">
                        <Key className="mr-2 h-4 w-4" />
                        Permissions ({selectedPermissions.length} selected)
                      </Label>
                      <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
                        {serverPermissions.availablePermissions.map(permission => (
                          <div key={typeof permission.name === 'string' ? permission.name : JSON.stringify(permission.name)} className="flex items-center space-x-2">
                            <Switch
                              checked={selectedPermissions.includes(typeof permission.name === 'string' ? permission.name : JSON.stringify(permission.name))}
                              onCheckedChange={() => handlePermissionToggle(typeof permission.name === 'string' ? permission.name : JSON.stringify(permission.name))}
                              className="data-[state=checked]:bg-green-600"
                            />
                            <div className="flex-1">
                              <div className="text-sm font-medium">
                                {typeof permission.name === 'string' ? permission.name : JSON.stringify(permission.name)}
                              </div>
                              {permission.description && typeof permission.description === 'string' && (
                                <div className="text-xs text-slate-500">
                                  {permission.description}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Roles */}
                  {serverPermissions && serverPermissions.roles.length > 0 && (
                    <div className="space-y-3">
                      <Label className="text-base font-medium flex items-center">
                        <Shield className="mr-2 h-4 w-4" />
                        Roles ({selectedRoles.length} selected)
                      </Label>
                      <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
                        {serverPermissions.roles.map(role => (
                          <div key={typeof role.name === 'string' ? role.name : JSON.stringify(role.name)} className="flex items-center space-x-2">
                            <Switch
                              checked={selectedRoles.includes(typeof role.name === 'string' ? role.name : JSON.stringify(role.name))}
                              onCheckedChange={() => handleRoleToggle(typeof role.name === 'string' ? role.name : JSON.stringify(role.name))}
                              className="data-[state=checked]:bg-green-600"
                            />
                            <div className="flex-1">
                              <div className="text-sm font-medium">
                                {typeof role.name === 'string' ? role.name : JSON.stringify(role.name)}
                              </div>
                              {role.description && typeof role.description === 'string' && (
                                <div className="text-xs text-slate-500">
                                  {role.description}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-4">
                    <Button
                      onClick={handleSaveAssignments}
                      disabled={loading}
                      className="flex-1"
                    >
                      {loading ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Save Assignments
                        </>
                      )}
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={handleUnassignUser}
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Unassigning...
                        </>
                      ) : (
                        <>
                          <XCircle className="mr-2 h-4 w-4" />
                          Unassign
                        </>
                      )}
                    </Button>
                  </div>
                </>
              ) : (
                <div className="p-8 text-center text-slate-500">
                  <Users className="mx-auto h-12 w-12 mb-3 opacity-50" />
                  <p>Select a user to manage their permissions and roles</p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status Messages */}
      {message && (
        <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50 dark:bg-green-900/20' : 'border-red-200 bg-red-50 dark:bg-red-900/20'}>
          {message.type === 'success' ? (
            <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
          )}
          <AlertTitle>
            {message.type === 'success' ? 'Success' : 'Error'}
          </AlertTitle>
          <AlertDescription className={message.type === 'success' ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}