'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import {
  Server,
  Settings,
  Users,
  AlertCircle,
  CheckCircle,
  RefreshCw
} from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { RemoteServerCard } from './RemoteServerCard'
import { ServerAssignmentManager } from './ServerAssignmentManager'
import { RemoteServer, ServerPermissions, ServerStatus } from './types'

export function RemoteServerManagement() {
  const [servers, setServers] = useState<RemoteServer[]>([])
  const [loading, setLoading] = useState(false)
  const [editingServer, setEditingServer] = useState<RemoteServer | null>(null)
  const [editData, setEditData] = useState<Partial<RemoteServer>>({})
  const [permissionsLoading, setPermissionsLoading] = useState<Record<string, boolean>>({})
  const [serverPermissions, setServerPermissions] = useState<Record<string, ServerPermissions>>({})
  const [serverStatuses, setServerStatuses] = useState<Record<string, ServerStatus>>({})
  const [showClientSecret, setShowClientSecret] = useState<Record<string, boolean>>({})
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({})
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  // Fetch real server data from API
  useEffect(() => {
    const fetchServers = async () => {
      try {
        setLoading(true)
        console.log('Fetching servers from /api/remote-servers');
        const response = await fetch('/api/remote-servers')
        console.log('Servers API response status:', response.status);
        console.log('Servers API response headers:', Object.fromEntries(response.headers.entries()));

        if (response.ok) {
          const data = await response.json()
          console.log('Servers API response data:', data);
          setServers(data.remoteServers || [])
        } else {
          const errorData = await response.json()
          console.error('Failed to fetch servers:', response.status, response.statusText, errorData)
          setMessage({ type: 'error', text: errorData.message || 'Failed to load servers' })
        }
      } catch (error) {
        console.error('Error fetching servers:', error)
        setMessage({ type: 'error', text: 'Error loading servers' })
      } finally {
        setLoading(false)
      }
    }

    fetchServers()
  }, [])

  const handleEditServer = (server: RemoteServer) => {
    console.log('handleEditServer called with server:', server.id, server.name);
    console.log('Current editingServer state:', editingServer);
    try {
      setEditingServer(server)
      setEditData(server)
      console.log('State updated successfully');
    } catch (error) {
      console.error('Error updating edit state:', error);
    }
  }

  const handleSaveEdit = async () => {
    console.log('handleSaveEdit called');
    console.log('editingServer:', editingServer);
    console.log('editData:', editData);

    if (!editingServer || !editData) {
      console.log('Missing editingServer or editData');
      return
    }

    setLoading(true)
    try {
      console.log('Making API call to update server:', editingServer.id);
      // Make API call to update server
      const response = await fetch(`/api/remote-servers/${editingServer.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editData),
      })

      console.log('API response status:', response.status);
      console.log('API response headers:', Object.fromEntries(response.headers.entries()));

      if (response.ok) {
        const result = await response.json()
        console.log('API response data:', result);

        // Update local state with server response
        setServers(prev => prev.map(server =>
          server.id === editingServer.id
            ? { ...server, ...result.remoteServer }
            : server
        ))
        setEditingServer(null)
        setEditData({})
        setMessage({ type: 'success', text: 'Server updated successfully' })
      } else {
        const errorData = await response.json()
        console.error('API error response:', errorData);
        setMessage({ type: 'error', text: errorData.message || 'Failed to update server' })
      }
    } catch (error) {
      console.error('Error updating server:', error)
      setMessage({ type: 'error', text: 'Failed to update server' })
    } finally {
      setLoading(false)
    }
  }

  const handleCancelEdit = () => {
    setEditingServer(null)
    setEditData({})
  }

  const handleUpdateEditData = (data: Partial<RemoteServer>) => {
    setEditData(prev => ({ ...prev, ...data }))
  }

  const handleFetchPermissions = async (serverId: string) => {
    setPermissionsLoading(prev => ({ ...prev, [serverId]: true }))
    try {
      // Make API call to sync permissions from remote server
      const response = await fetch(`/api/permissions/sync/${serverId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const result = await response.json()

        // Update server permissions with real data
        if (result.permissions) {
          setServerPermissions(prev => ({ ...prev, [serverId]: result.permissions }))
        }

        setMessage({ type: 'success', text: result.message || 'Permissions synced successfully' })
      } else {
        const errorData = await response.json()
        setMessage({ type: 'error', text: errorData.message || 'Failed to sync permissions' })
      }
    } catch (error) {
      console.error('Error syncing permissions:', error)
      setMessage({ type: 'error', text: 'Failed to sync permissions' })
    } finally {
      setPermissionsLoading(prev => ({ ...prev, [serverId]: false }))
    }
  }

  const handleRemoveServer = async (serverId: string) => {
    setLoading(true)
    try {
      // Make API call to delete/deactivate server
      const response = await fetch(`/api/remote-servers/${serverId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const result = await response.json()

        if (result.success) {
          // Remove from local state
          setServers(prev => prev.filter(server => server.id !== serverId))
          setMessage({ type: 'success', text: result.message || 'Server removed successfully' })
        } else {
          setMessage({ type: 'error', text: 'Failed to remove server' })
        }
      } else {
        const errorData = await response.json()
        setMessage({ type: 'error', text: errorData.message || 'Failed to remove server' })
      }
    } catch (error) {
      console.error('Error removing server:', error)
      setMessage({ type: 'error', text: 'Failed to remove server' })
    } finally {
      setLoading(false)
    }
  }

  const handleToggleClientSecret = (serverId: string) => {
    setShowClientSecret(prev => ({ ...prev, [serverId]: !prev[serverId] }))
  }

  const handleToggleApiKey = (serverId: string) => {
    setShowApiKey(prev => ({ ...prev, [serverId]: !prev[serverId] }))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
          Remote Server Management
        </h2>
        <p className="text-slate-600 dark:text-slate-400">
          Configure and manage external applications and API connections
        </p>
      </div>

      {/* Status Messages */}
      {message && (
        <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50 dark:bg-green-900/20' : 'border-red-200 bg-red-50 dark:bg-red-900/20'}>
          {message.type === 'success' ? (
            <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
          )}
          <AlertTitle>
            {message.type === 'success' ? 'Success' : 'Error'}
          </AlertTitle>
          <AlertDescription className={message.type === 'success' ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {/* Servers List */}
      {servers.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Server className="h-5 w-5 text-slate-400" />
            <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100">
              Configured Servers ({servers.length})
            </h3>
          </div>

          <div className="grid gap-4">
            {servers.map(server => (
              <RemoteServerCard
                key={server.id}
                server={server}
                isEditing={editingServer?.id === server.id}
                editData={editData}
                onEdit={() => handleEditServer(server)}
                onSave={handleSaveEdit}
                onCancel={handleCancelEdit}
                onUpdateEditData={handleUpdateEditData}
                onFetchPermissions={() => handleFetchPermissions(server.id)}
                onRemove={() => handleRemoveServer(server.id)}
                permissionsLoading={permissionsLoading[server.id] || false}
                serverPermissions={serverPermissions[server.id]}
                showClientSecret={showClientSecret[server.id] || false}
                showApiKey={showApiKey[server.id] || false}
                onToggleClientSecret={() => handleToggleClientSecret(server.id)}
                onToggleApiKey={() => handleToggleApiKey(server.id)}
                serverStatus={serverStatuses[server.id]}
              />
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {servers.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Server className="h-12 w-12 text-slate-300 dark:text-slate-600 mb-4" />
            <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">
              No Remote Servers Configured
            </h3>
            <p className="text-slate-600 dark:text-slate-400 text-center mb-4">
              No servers are available for configuration. Add servers from the Remote Servers tab first.
            </p>
          </CardContent>
        </Card>
      )}

      {/* User Assignment Tab */}
      {servers.length > 0 && (
        <Tabs defaultValue="servers" className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-emerald-600 border-0 p-1 h-auto rounded-md">
            <TabsTrigger value="servers" className="flex items-center gap-2 bg-transparent border-0 text-white data-[state=active]:bg-emerald-700 data-[state=active]:text-white hover:bg-emerald-700/80">
              <Settings className="h-4 w-4" />
              Server Configuration
            </TabsTrigger>
            <TabsTrigger value="assignments" className="flex items-center gap-2 bg-transparent border-0 text-white data-[state=active]:bg-emerald-700 data-[state=active]:text-white hover:bg-emerald-700/80">
              <Users className="h-4 w-4" />
              User Assignments
            </TabsTrigger>
          </TabsList>

          <TabsContent value="servers" className="mt-6">
            {/* Server configuration content is already above */}
          </TabsContent>

          <TabsContent value="assignments" className="mt-6">
            {servers.length > 0 ? (
              <ServerAssignmentManager
                server={servers[0]} // For now, just show the first server
                serverPermissions={serverPermissions[servers[0].id]}
                onAssignUser={async (userId, permissions, roles) => {
                  // Real API implementation
                  try {
                    const response = await fetch(`/api/users/${userId}/remote-servers`, {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({
                        serverId: servers[0].id,
                        permissions: permissions || [],
                        roles: roles || []
                      })
                    })
                    if (!response.ok) throw new Error('Failed to assign user')
                    return await response.json()
                  } catch (error) {
                    console.error('Error assigning user:', error)
                    throw error
                  }
                }}
                onUnassignUser={async (userId) => {
                  // Real API implementation
                  try {
                    const response = await fetch(`/api/users/${userId}/remote-servers?serverId=${servers[0].id}`, {
                      method: 'DELETE'
                    })
                    if (!response.ok) throw new Error('Failed to unassign user')
                    return await response.json()
                  } catch (error) {
                    console.error('Error unassigning user:', error)
                    throw error
                  }
                }}
                onUpdateUserPermissions={async (userId, permissions) => {
                  // Real API implementation
                  try {
                    const response = await fetch(`/api/users/${userId}/permissions`, {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({
                        serverId: servers[0].id,
                        permissions
                      })
                    })
                    if (!response.ok) throw new Error('Failed to update user permissions')
                    return await response.json()
                  } catch (error) {
                    console.error('Error updating user permissions:', error)
                    throw error
                  }
                }}
                onUpdateUserRoles={async (userId, roles) => {
                  // Real API implementation
                  try {
                    const response = await fetch(`/api/users/${userId}/roles`, {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({
                        serverId: servers[0].id,
                        roles
                      })
                    })
                    if (!response.ok) throw new Error('Failed to update user roles')
                    return await response.json()
                  } catch (error) {
                    console.error('Error updating user roles:', error)
                    throw error
                  }
                }}
                isLoading={loading}
              />
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Users className="h-12 w-12 text-slate-300 dark:text-slate-600 mb-4" />
                  <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">
                    No Servers Available
                  </h3>
                  <p className="text-slate-600 dark:text-slate-400 text-center">
                    Configure a server first to manage user assignments.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}