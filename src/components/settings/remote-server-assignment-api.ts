import { RemoteServerNotificationService } from '@/lib/services/remote-server-notification-service'
import type { Assignment, RemoteServer, ServerPermission } from './remote-server-assignment-types'

export const loadAssignments = async (entityType: 'user' | 'role', entityId: string): Promise<Assignment[]> => {
  const response = await fetch(`/api/${entityType}s/${entityId}/remote-servers`)
  if (response.ok) {
    const data = await response.json()
    return data.data || []
  }
  throw new Error('Failed to load assignments')
}

export const loadAvailableServers = async (): Promise<RemoteServer[]> => {
  const response = await fetch('/api/remote-servers')
  if (response.ok) {
    const data = await response.json()
    return data.remoteServers || []
  }
  throw new Error('Failed to load servers')
}

export const loadServerPermissions = async (serverId: string): Promise<ServerPermission[]> => {
  const response = await fetch(`/api/remote-servers/${serverId}/permissions`)
  if (response.ok) {
    const data = await response.json()
    return data || []
  }
  throw new Error('Failed to load server permissions')
}

export const autoFetchServerPermissions = async (serverId: string, availableServers: RemoteServer[]): Promise<boolean> => {
  try {
    const { fetchRemoteServerPermissions } = await import('@/app/actions/remote-servers')
    const result = await fetchRemoteServerPermissions(serverId)

    if (result.success && result.data) {
      return true
    } else {
      console.warn('Auto-fetch failed:', result.error)
      const notificationService = new RemoteServerNotificationService()
      const server = availableServers.find(s => s.id === serverId)
      if (server) {
        await notificationService.notifyPermissionFetchFailure({
          serverId,
          serverName: server.name,
          error: result.error || 'Unknown error',
          timestamp: new Date().toISOString(),
          context: 'assignment_check'
        })
      }
      return false
    }
  } catch (err) {
    console.error('Error auto-fetching permissions:', err)
    return false
  }
}

export const assignServer = async (
  entityType: 'user' | 'role',
  entityId: string,
  serverId: string,
  permissions: string[],
  notes?: string
): Promise<void> => {
  const payload =
    entityType === 'user'
      ? { serverId, permissions, notes: notes?.trim() || undefined }
      : { serverId, autoGrantPermissions: permissions }

  const response = await fetch(`/api/${entityType}s/${entityId}/remote-servers`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload)
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.error || 'Failed to assign server')
  }
}

export const removeServerAssignment = async (
  entityType: 'user' | 'role',
  entityId: string,
  serverId: string
): Promise<void> => {
  const response = await fetch(`/api/${entityType}s/${entityId}/remote-servers?serverId=${serverId}`, {
    method: 'DELETE'
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.error || 'Failed to remove server assignment')
  }
}

export const formatDate = (dateString: string): string =>
  new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })