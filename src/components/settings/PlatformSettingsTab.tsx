'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import {
  Globe,
  MapPin,
  Folder,
  FolderOpen,
  Plus,
  Edit,
  Trash2,
  Search
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface Country {
  id: number;
  name: string;
  code: string;
}

interface City {
  id: number;
  name: string;
  country: Country;
}

interface Category {
  id: number;
  name: string;
  description: string | null;
  subcategories: Subcategory[];
}

interface Subcategory {
  id: number;
  name: string;
  description: string | null;
  category: Category;
}





export const PlatformSettingsTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'countries' | 'cities' | 'categories' | 'subcategories'>('countries');

  // Countries state
  const [countries, setCountries] = useState<Country[]>([]);
  const [countrySearch, setCountrySearch] = useState('');
  const [newCountry, setNewCountry] = useState({ name: '', code: '' });
  const [loadingCountries, setLoadingCountries] = useState(false);

  // Cities state
  const [cities, setCities] = useState<City[]>([]);
  const [citySearch, setCitySearch] = useState('');
  const [newCity, setNewCity] = useState({ name: '', countryId: '' });
  const [loadingCities, setLoadingCities] = useState(false);

  // Autocomplete for country selection in cities
  const [countryInputValue, setCountryInputValue] = useState('');
  const [selectedCountryId, setSelectedCountryId] = useState<number | null>(null);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [countrySuggestions, setCountrySuggestions] = useState<Country[]>([]);
  const [searchingCountries, setSearchingCountries] = useState(false);

  // Categories state
  const [categories, setCategories] = useState<Category[]>([]);
  const [categorySearch, setCategorySearch] = useState('');
  const [newCategory, setNewCategory] = useState({ name: '', description: '' });
  const [loadingCategories, setLoadingCategories] = useState(false);

  // Subcategories state
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [subcategorySearch, setSubcategorySearch] = useState('');
  const [newSubcategory, setNewSubcategory] = useState({ name: '', description: '', categoryId: '' });
  const [loadingSubcategories, setLoadingSubcategories] = useState(false);

  // Edit states
  const [editingCountry, setEditingCountry] = useState<Country | null>(null);
  const [editingCity, setEditingCity] = useState<City | null>(null);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [editingSubcategory, setEditingSubcategory] = useState<Subcategory | null>(null);
  const [editForm, setEditForm] = useState({
    name: '',
    code: '',
    description: '',
    countryId: ''
  });

  // Pagination states
  const [countriesPage, setCountriesPage] = useState(1);
  const [countriesTotalPages, setCountriesTotalPages] = useState(1);
  const [countriesTotalCount, setCountriesTotalCount] = useState(0);
  const [citiesPage, setCitiesPage] = useState(1);
  const [citiesTotalPages, setCitiesTotalPages] = useState(1);
  const [citiesTotalCount, setCitiesTotalCount] = useState(0);
  const [categoriesPage, setCategoriesPage] = useState(1);
  const [categoriesTotalPages, setCategoriesTotalPages] = useState(1);
  const [categoriesTotalCount, setCategoriesTotalCount] = useState(0);
  const [subcategoriesPage, setSubcategoriesPage] = useState(1);
  const [subcategoriesTotalPages, setSubcategoriesTotalPages] = useState(1);
  const [subcategoriesTotalCount, setSubcategoriesTotalCount] = useState(0);
  const itemsPerPage = 20;

  // Autocomplete for category selection in subcategories
  const [categoryInputValue, setCategoryInputValue] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [showCategorySuggestions, setShowCategorySuggestions] = useState(false);
  const [categorySuggestions, setCategorySuggestions] = useState<Category[]>([]);
  const [searchingCategories, setSearchingCategories] = useState(false);

  // Search countries for autocomplete
  const searchCountries = async (query: string) => {
    if (!query.trim()) {
      setCountrySuggestions([]);
      return;
    }

    setSearchingCountries(true);
    try {
      const response = await fetch(`/api/countries?q=${encodeURIComponent(query)}&limit=50`);
      if (response.ok) {
        const data = await response.json();
        setCountrySuggestions(data.countries);
      }
    } catch (error) {
      console.error('Error searching countries:', error);
    } finally {
      setSearchingCountries(false);
    }
  };

  // Search categories for autocomplete
  const searchCategories = async (query: string) => {
    if (!query.trim()) {
      setCategorySuggestions([]);
      return;
    }

    setSearchingCategories(true);
    try {
      const response = await fetch(`/api/categories?q=${encodeURIComponent(query)}&limit=50`);
      if (response.ok) {
        const data = await response.json();
        setCategorySuggestions(data.categories);
      }
    } catch (error) {
      console.error('Error searching categories:', error);
    } finally {
      setSearchingCategories(false);
    }
  };

  // Countries functions
  const loadCountries = useCallback(async (page = countriesPage) => {
    setLoadingCountries(true);
    try {
      const response = await fetch(`/api/countries?q=${encodeURIComponent(countrySearch)}&limit=${itemsPerPage}`);
      if (response.ok) {
        const data = await response.json();
        // The API returns an array of countries directly, not a paginated response
        setCountries(data);
        setCountriesTotalPages(1); // Single page since no pagination
        setCountriesTotalCount(data.length);
        setCountriesPage(1);
      } else {
        toast.error('Failed to load countries');
      }
    } catch (error) {
      console.error('Error loading countries:', error);
      toast.error('Failed to load countries');
    } finally {
      setLoadingCountries(false);
    }
  }, [countrySearch, itemsPerPage, countriesPage]);

  // Cities functions
  const loadCities = useCallback(async (page = citiesPage) => {
    setLoadingCities(true);
    try {
      const response = await fetch(`/api/cities?all=true&q=${encodeURIComponent(citySearch)}&page=${page}&limit=${itemsPerPage}`);
      if (response.ok) {
        const data = await response.json();
        setCities(data.cities);
        setCitiesTotalPages(data.pagination.totalPages);
        setCitiesTotalCount(data.pagination.totalCount);
        setCitiesPage(data.pagination.currentPage);
      } else {
        toast.error('Failed to load cities');
      }
    } catch (error) {
      console.error('Error loading cities:', error);
      toast.error('Failed to load cities');
    } finally {
      setLoadingCities(false);
    }
  }, [citySearch, itemsPerPage, citiesPage]);

  // Categories functions
  const loadCategories = useCallback(async (page = categoriesPage) => {
    setLoadingCategories(true);
    try {
      const response = await fetch(`/api/categories`);
      if (response.ok) {
        const data = await response.json();
        console.log('Categories API response:', data);
        console.log('Categories data:', data.categories);
        setCategories(data.categories);
        // Categories API doesn't support pagination, set single page
        setCategoriesTotalPages(1);
        setCategoriesTotalCount(data.categories.length);
        setCategoriesPage(1);
        console.log('Categories state set:', data.categories);
      } else {
        console.error('Failed to load categories - response not ok:', response.status);
        toast.error('Failed to load categories');
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      toast.error('Failed to load categories');
    } finally {
      setLoadingCategories(false);
    }
  }, [categorySearch, itemsPerPage, categoriesPage]);

  // Subcategories functions
  const loadSubcategories = useCallback(async (page = subcategoriesPage) => {
    setLoadingSubcategories(true);
    try {
      const response = await fetch(`/api/subcategories?q=${encodeURIComponent(subcategorySearch)}&page=${page}&limit=${itemsPerPage}`);
      if (response.ok) {
        const data = await response.json();
        setSubcategories(data.subcategories);
        setSubcategoriesTotalPages(data.pagination.totalPages);
        setSubcategoriesTotalCount(data.pagination.totalCount);
        setSubcategoriesPage(data.pagination.currentPage);
      } else {
        toast.error('Failed to load subcategories');
      }
    } catch (error) {
      console.error('Error loading subcategories:', error);
      toast.error('Failed to load subcategories');
    } finally {
      setLoadingSubcategories(false);
    }
  }, [subcategorySearch, itemsPerPage, subcategoriesPage]);

  // Debounced search effects
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (countryInputValue && activeTab === 'cities') {
        searchCountries(countryInputValue);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [countryInputValue, activeTab]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (categoryInputValue && activeTab === 'subcategories') {
        searchCategories(categoryInputValue);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [categoryInputValue, activeTab]);

  // Search effects for table filtering
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (activeTab === 'countries') {
        setCountriesPage(1);
        loadCountries(1);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [countrySearch, activeTab, loadCountries]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (activeTab === 'cities') {
        setCitiesPage(1);
        loadCities(1);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [citySearch, activeTab, loadCities]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (activeTab === 'categories') {
        setCategoriesPage(1);
        loadCategories(1);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [categorySearch, activeTab, loadCategories]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (activeTab === 'subcategories') {
        setSubcategoriesPage(1);
        loadSubcategories(1);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [subcategorySearch, activeTab, loadSubcategories]);

  // Load data on component mount and when tabs change
  useEffect(() => {
    if (activeTab === 'countries') {
      loadCountries();
    } else if (activeTab === 'cities') {
      loadCountries();
      loadCities();
    } else if (activeTab === 'categories') {
      loadCategories();
    } else if (activeTab === 'subcategories') {
      loadCategories();
      loadSubcategories();
    }
  }, [activeTab, loadCountries, loadCities, loadCategories, loadSubcategories]);

  const handleCreateCountry = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newCountry.name.trim() || !newCountry.code.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/countries', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newCountry),
      });

      if (response.ok) {
        toast.success('Country created successfully');
        setNewCountry({ name: '', code: '' });
        loadCountries();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create country');
      }
    } catch (error) {
      console.error('Error creating country:', error);
      toast.error('Failed to create country');
    }
  };

  const handleCreateCity = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newCity.name.trim() || !selectedCountryId) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/cities', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: newCity.name,
          countryId: selectedCountryId,
        }),
      });

      if (response.ok) {
        toast.success('City created successfully');
        setNewCity({ name: '', countryId: '' });
        setCountryInputValue('');
        setSelectedCountryId(null);
        loadCities();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create city');
      }
    } catch (error) {
      console.error('Error creating city:', error);
      toast.error('Failed to create city');
    }
  };

  const handleCreateCategory = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newCategory.name.trim()) {
      toast.error('Please fill in the category name');
      return;
    }

    try {
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newCategory),
      });

      if (response.ok) {
        toast.success('Category created successfully');
        setNewCategory({ name: '', description: '' });
        loadCategories();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create category');
      }
    } catch (error) {
      console.error('Error creating category:', error);
      toast.error('Failed to create category');
    }
  };

  const handleCreateSubcategory = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newSubcategory.name.trim() || !selectedCategoryId) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/subcategories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: newSubcategory.name,
          description: newSubcategory.description,
          categoryId: selectedCategoryId,
        }),
      });

      if (response.ok) {
        toast.success('Subcategory created successfully');
        setNewSubcategory({ name: '', description: '', categoryId: '' });
        setCategoryInputValue('');
        setSelectedCategoryId(null);
        loadSubcategories();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create subcategory');
      }
    } catch (error) {
      console.error('Error creating subcategory:', error);
      toast.error('Failed to create subcategory');
    }
  };


  // Edit handlers
  const handleEditCountry = (country: Country) => {
    setEditingCountry(country);
    setEditForm({ name: country.name, code: country.code, description: '', countryId: '' });
  };

  const handleEditCity = (city: City) => {
    setEditingCity(city);
    setEditForm({ name: city.name, code: '', description: '', countryId: city.country.id.toString() });
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setEditForm({ name: category.name, code: '', description: category.description || '', countryId: '' });
  };

  const handleEditSubcategory = (subcategory: Subcategory) => {
    setEditingSubcategory(subcategory);
    setEditForm({
      name: subcategory.name,
      code: '',
      description: subcategory.description || '',
      countryId: subcategory.category.id.toString()
    });
  };

  const handleSaveEdit = async () => {
    if (!editingCountry && !editingCity && !editingCategory && !editingSubcategory) return;

    try {
      let url = '';
      let body: any = {};

      if (editingCountry) {
        url = `/api/countries/${editingCountry.id}`;
        body = { name: editForm.name, code: editForm.code };
      } else if (editingCity) {
        url = `/api/cities/${editingCity.id}`;
        body = { name: editForm.name, countryId: parseInt(editForm.countryId) };
      } else if (editingCategory) {
        url = `/api/categories/${editingCategory.id}`;
        body = { name: editForm.name, description: editForm.description };
      } else if (editingSubcategory) {
        url = `/api/subcategories/${editingSubcategory.id}`;
        body = { name: editForm.name, description: editForm.description, categoryId: parseInt(editForm.countryId) };
      }

      const response = await fetch(url, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        toast.success('Item updated successfully');
        // Reset edit state
        setEditingCountry(null);
        setEditingCity(null);
        setEditingCategory(null);
        setEditingSubcategory(null);
        setEditForm({ name: '', code: '', description: '', countryId: '' });

        // Reload data
        if (editingCountry) loadCountries();
        if (editingCity) loadCities();
        if (editingCategory) loadCategories();
        if (editingSubcategory) loadSubcategories();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to update item');
      }
    } catch (error) {
      console.error('Error updating item:', error);
      toast.error('Failed to update item');
    }
  };

  const handleDeleteItem = async (type: 'country' | 'city' | 'category' | 'subcategory', id: number) => {
    if (!confirm(`Are you sure you want to delete this ${type}?`)) return;

    try {
      let url = '';
      if (type === 'country') url = `/api/countries/${id}`;
      else if (type === 'city') url = `/api/cities/${id}`;
      else if (type === 'category') url = `/api/categories/${id}`;
      else if (type === 'subcategory') url = `/api/subcategories/${id}`;

      const response = await fetch(url, { method: 'DELETE' });

      if (response.ok) {
        toast.success(`${type.charAt(0).toUpperCase() + type.slice(1)} deleted successfully`);
        // Reload data
        if (type === 'country') loadCountries();
        if (type === 'city') loadCities();
        if (type === 'category') loadCategories();
        if (type === 'subcategory') loadSubcategories();
      } else {
        const error = await response.json();
        toast.error(error.error || `Failed to delete ${type}`);
      }
    } catch (error) {
      console.error(`Error deleting ${type}:`, error);
      toast.error(`Failed to delete ${type}`);
    }
  };

  // Countries for autocomplete suggestions (server-side filtered)
  const filteredCountries = countrySuggestions;

  // Edit Modal
  const renderEditModal = () => {
    if (!editingCountry && !editingCity && !editingCategory && !editingSubcategory) return null;

    const isEditingCountry = !!editingCountry;
    const isEditingCity = !!editingCity;
    const isEditingCategory = !!editingCategory;
    const isEditingSubcategory = !!editingSubcategory;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg max-w-md w-full mx-4">
          <h3 className="text-lg font-medium mb-4">
            Edit {isEditingCountry ? 'Country' : isEditingCity ? 'City' : isEditingCategory ? 'Category' : 'Subcategory'}
          </h3>

          <div className="space-y-4">
            <div>
              <Label htmlFor="editName">Name *</Label>
              <Input
                id="editName"
                value={editForm.name}
                onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                required
              />
            </div>

            {isEditingCountry && (
              <div>
                <Label htmlFor="editCode">Code *</Label>
                <Input
                  id="editCode"
                  value={editForm.code}
                  onChange={(e) => setEditForm({ ...editForm, code: e.target.value.toUpperCase() })}
                  maxLength={3}
                  required
                />
              </div>
            )}

            {isEditingCity && (
              <div>
                <Label htmlFor="editCountry">Country *</Label>
                <Input
                  id="editCountry"
                  value={editForm.countryId}
                  onChange={(e) => setEditForm({ ...editForm, countryId: e.target.value })}
                  placeholder="Country ID"
                  required
                />
              </div>
            )}

            {(isEditingCategory || isEditingSubcategory) && (
              <div>
                <Label htmlFor="editDescription">Description</Label>
                <Textarea
                  id="editDescription"
                  value={editForm.description}
                  onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
                  rows={3}
                />
              </div>
            )}

            {isEditingSubcategory && (
              <div>
                <Label htmlFor="editCategory">Category *</Label>
                <Input
                  id="editCategory"
                  value={editForm.countryId}
                  onChange={(e) => setEditForm({ ...editForm, countryId: e.target.value })}
                  placeholder="Category ID"
                  required
                />
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-2 mt-6">
            <Button
              variant="outline"
              onClick={() => {
                setEditingCountry(null);
                setEditingCity(null);
                setEditingCategory(null);
                setEditingSubcategory(null);
                setEditForm({ name: '', code: '', description: '', countryId: '' });
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleSaveEdit}>
              Save Changes
            </Button>
          </div>
        </div>
      </div>
    );
  };

  // Pagination component
  const renderPagination = (
    currentPage: number,
    totalPages: number,
    totalCount: number,
    onPageChange: (page: number) => void
  ) => {
    if (totalPages <= 1) return null;

    return (
      <div className="flex items-center justify-between mt-4">
        <div className="text-sm text-gray-700 dark:text-gray-300">
          Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalCount)} of {totalCount} entries
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage <= 1}
          >
            Previous
          </Button>
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    );
  };

  const tabs = [
    { id: 'countries', label: 'Countries', icon: <Globe className="h-4 w-4" /> },
    { id: 'cities', label: 'Cities', icon: <MapPin className="h-4 w-4" /> },
    { id: 'categories', label: 'Categories', icon: <Folder className="h-4 w-4" /> },
    { id: 'subcategories', label: 'Subcategories', icon: <FolderOpen className="h-4 w-4" /> },
  ];


  return (
    <div className="space-y-6">
      {renderEditModal()}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400'
              }`}
            >
              {tab.icon}
              <span className="ml-2">{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Countries Tab */}
      {activeTab === 'countries' && (
        <div className="space-y-6">
          <div className="border rounded-lg bg-white dark:bg-gray-800 shadow-sm">
            <div className="px-6 py-4 border-b">
              <div className="flex items-center">
                <Globe className="h-5 w-5 mr-2" />
                <h4 className="text-lg font-semibold">Countries Management</h4>
              </div>
            </div>
            <div className="p-6">
              {/* Create Country Form */}
              <form onSubmit={handleCreateCountry} className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 className="text-lg font-medium mb-4">Add New Country</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="countryName">Country Name *</Label>
                    <Input
                      id="countryName"
                      value={newCountry.name}
                      onChange={(e) => setNewCountry({ ...newCountry, name: e.target.value })}
                      placeholder="Enter country name"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="countryCode">Country Code *</Label>
                    <Input
                      id="countryCode"
                      value={newCountry.code}
                      onChange={(e) => setNewCountry({ ...newCountry, code: e.target.value.toUpperCase() })}
                      placeholder="e.g., USA, GBR"
                      maxLength={3}
                      required
                    />
                  </div>
                </div>
                <Button type="submit" className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Country
                </Button>
              </form>

              {/* Countries List */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">Existing Countries</h3>
                  <div className="flex items-center space-x-2">
                    <Search className="h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search countries..."
                      value={countrySearch}
                      onChange={(e) => setCountrySearch(e.target.value)}
                      className="w-64"
                    />
                  </div>
                </div>
                {loadingCountries ? (
                  <div className="text-center py-8">Loading countries...</div>
                ) : (
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Code
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {countries.map((country) => (
                        <tr key={country.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {country.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {country.code}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <Button
                              variant="outline"
                              size="sm"
                              className="mr-2"
                              onClick={() => handleEditCountry(country)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              onClick={() => handleDeleteItem('country', country.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
                {renderPagination(countriesPage, countriesTotalPages, countriesTotalCount, (page) => {
                  setCountriesPage(page);
                  loadCountries(page);
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Cities Tab */}
      {activeTab === 'cities' && (
        <div className="space-y-6">
          <div className="border rounded-lg bg-white dark:bg-gray-800 shadow-sm">
            <div className="px-6 py-4 border-b">
              <div className="flex items-center">
                <MapPin className="h-5 w-5 mr-2" />
                <h4 className="text-lg font-semibold">Cities Management</h4>
              </div>
            </div>
            <div className="p-6">
              {/* Create City Form */}
              <form onSubmit={handleCreateCity} className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 className="text-lg font-medium mb-4">Add New City</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="cityCountry">Country *</Label>
                    <div className="relative">
                      <Input
                        id="cityCountry"
                        value={countryInputValue}
                        onChange={(e) => {
                          const value = e.target.value;
                          setCountryInputValue(value);
                          setSelectedCountryId(null);
                          if (value.trim()) {
                            setShowSuggestions(true);
                          } else {
                            setShowSuggestions(false);
                            setCountrySuggestions([]);
                          }
                        }}
                        onFocus={() => {
                          if (countryInputValue.trim()) {
                            setShowSuggestions(true);
                          }
                        }}
                        onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                        placeholder="Search for a country"
                        required
                      />
                      {showSuggestions && (
                        <div className="absolute z-10 w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-40 overflow-y-auto">
                          {searchingCountries ? (
                            <div className="px-3 py-2 text-gray-500">Searching...</div>
                          ) : filteredCountries.length > 0 ? (
                            filteredCountries.map((country) => (
                              <div
                                key={country.id}
                                className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
                                onClick={() => {
                                  setCountryInputValue(country.name);
                                  setSelectedCountryId(country.id);
                                  setShowSuggestions(false);
                                }}
                              >
                                {country.name}
                              </div>
                            ))
                          ) : countryInputValue.trim() ? (
                            <div className="px-3 py-2 text-gray-500">No countries found</div>
                          ) : null}
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="cityName">City Name *</Label>
                    <Input
                      id="cityName"
                      value={newCity.name}
                      onChange={(e) => setNewCity({ ...newCity, name: e.target.value })}
                      placeholder="Enter city name"
                      required
                    />
                  </div>
                </div>
                <Button type="submit" className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Add City
                </Button>
              </form>

              {/* Cities List */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">Existing Cities</h3>
                  <div className="flex items-center space-x-2">
                    <Search className="h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search cities..."
                      value={citySearch}
                      onChange={(e) => setCitySearch(e.target.value)}
                      className="w-64"
                    />
                  </div>
                </div>
                {loadingCities ? (
                  <div className="text-center py-8">Loading cities...</div>
                ) : (
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          City Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Country
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {cities.map((city) => (
                        <tr key={city.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {city.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {city.country.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <Button
                              variant="outline"
                              size="sm"
                              className="mr-2"
                              onClick={() => handleEditCity(city)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              onClick={() => handleDeleteItem('city', city.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
                {renderPagination(citiesPage, citiesTotalPages, citiesTotalCount, (page) => {
                  setCitiesPage(page);
                  loadCities(page);
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Categories Tab */}
      {activeTab === 'categories' && (
        <div className="space-y-6">
          <div className="border rounded-lg bg-white dark:bg-gray-800 shadow-sm">
            <div className="px-6 py-4 border-b">
              <div className="flex items-center">
                <Folder className="h-5 w-5 mr-2" />
                <h4 className="text-lg font-semibold">Categories Management</h4>
              </div>
            </div>
            <div className="p-6">
              {/* Create Category Form */}
              <form onSubmit={handleCreateCategory} className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 className="text-lg font-medium mb-4">Add New Category</h3>
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label htmlFor="categoryName">Category Name *</Label>
                    <Input
                      id="categoryName"
                      value={newCategory.name}
                      onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                      placeholder="Enter category name"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="categoryDescription">Description</Label>
                    <Textarea
                      id="categoryDescription"
                      value={newCategory.description}
                      onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
                      placeholder="Enter category description"
                      rows={3}
                    />
                  </div>
                </div>
                <Button type="submit" className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Category
                </Button>
              </form>

              {/* Categories List */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">Existing Categories</h3>
                  <div className="flex items-center space-x-2">
                    <Search className="h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search categories..."
                      value={categorySearch}
                      onChange={(e) => setCategorySearch(e.target.value)}
                      className="w-64"
                    />
                  </div>
                </div>
                {loadingCategories ? (
                  <div className="text-center py-8">Loading categories...</div>
                ) : categories.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No categories found. Try refreshing or check if data exists in the database.
                  </div>
                ) : (
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Description
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Subcategories
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {(() => {
                        console.log('Rendering categories table, categories length:', categories.length);
                        return categories.map((category) => {
                          console.log('Rendering category:', category);
                          return (
                            <tr key={category.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {category.name}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {category.description || '-'}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {category.subcategories?.length || 0}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="mr-2"
                                  onClick={() => handleEditCategory(category)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="text-red-600 hover:text-red-700"
                                  onClick={() => handleDeleteItem('category', category.id)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </td>
                            </tr>
                          );
                        });
                      })()}
                    </tbody>
                  </table>
                )}
                {renderPagination(categoriesPage, categoriesTotalPages, categoriesTotalCount, (page) => {
                  setCategoriesPage(page);
                  loadCategories(page);
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Subcategories Tab */}
      {activeTab === 'subcategories' && (
        <div className="space-y-6">
          <div className="border rounded-lg bg-white dark:bg-gray-800 shadow-sm">
            <div className="px-6 py-4 border-b">
              <div className="flex items-center">
                <FolderOpen className="h-5 w-5 mr-2" />
                <h4 className="text-lg font-semibold">Subcategories Management</h4>
              </div>
            </div>
            <div className="p-6">
              {/* Create Subcategory Form */}
              <form onSubmit={handleCreateSubcategory} className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 className="text-lg font-medium mb-4">Add New Subcategory</h3>
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label htmlFor="subcategoryCategory">Category *</Label>
                    <div className="relative">
                      <Input
                        id="subcategoryCategory"
                        value={categoryInputValue}
                        onChange={(e) => {
                          const value = e.target.value;
                          setCategoryInputValue(value);
                          setSelectedCategoryId(null);
                          if (value.trim()) {
                            setShowCategorySuggestions(true);
                          } else {
                            setShowCategorySuggestions(false);
                            setCategorySuggestions([]);
                          }
                        }}
                        onFocus={() => {
                          if (categoryInputValue.trim()) {
                            setShowCategorySuggestions(true);
                          }
                        }}
                        onBlur={() => setTimeout(() => setShowCategorySuggestions(false), 200)}
                        placeholder="Search for a category"
                        required
                      />
                      {showCategorySuggestions && (
                        <div className="absolute z-10 w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-40 overflow-y-auto">
                          {searchingCategories ? (
                            <div className="px-3 py-2 text-gray-500">Searching...</div>
                          ) : categorySuggestions.length > 0 ? (
                            categorySuggestions.map((category) => (
                              <div
                                key={category.id}
                                className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
                                onClick={() => {
                                  setCategoryInputValue(category.name);
                                  setSelectedCategoryId(category.id);
                                  setShowCategorySuggestions(false);
                                }}
                              >
                                {category.name}
                              </div>
                            ))
                          ) : categoryInputValue.trim() ? (
                            <div className="px-3 py-2 text-gray-500">No categories found</div>
                          ) : null}
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="subcategoryName">Subcategory Name *</Label>
                    <Input
                      id="subcategoryName"
                      value={newSubcategory.name}
                      onChange={(e) => setNewSubcategory({ ...newSubcategory, name: e.target.value })}
                      placeholder="Enter subcategory name"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="subcategoryDescription">Description</Label>
                    <Textarea
                      id="subcategoryDescription"
                      value={newSubcategory.description}
                      onChange={(e) => setNewSubcategory({ ...newSubcategory, description: e.target.value })}
                      placeholder="Enter subcategory description"
                      rows={3}
                    />
                  </div>
                </div>
                <Button type="submit" className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Subcategory
                </Button>
              </form>

              {/* Subcategories List */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">Existing Subcategories</h3>
                  <div className="flex items-center space-x-2">
                    <Search className="h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search subcategories..."
                      value={subcategorySearch}
                      onChange={(e) => setSubcategorySearch(e.target.value)}
                      className="w-64"
                    />
                  </div>
                </div>
                {loadingSubcategories ? (
                  <div className="text-center py-8">Loading subcategories...</div>
                ) : (
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Subcategory Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Category
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Description
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {subcategories.map((subcategory) => (
                        <tr key={subcategory.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {subcategory.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {subcategory.category.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {subcategory.description || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <Button
                              variant="outline"
                              size="sm"
                              className="mr-2"
                              onClick={() => handleEditSubcategory(subcategory)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              onClick={() => handleDeleteItem('subcategory', subcategory.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
                {renderPagination(subcategoriesPage, subcategoriesTotalPages, subcategoriesTotalCount, (page) => {
                  setSubcategoriesPage(page);
                  loadSubcategories(page);
                })}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};