'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  DollarSign, 
  Users, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Search,
  Download,
  Eye,
  Filter
} from 'lucide-react'
import { format } from 'date-fns'

// Types for treaty auditing data
interface TreatyApplication {
  id: string
  userId: string
  userName: string
  userEmail: string
  treatyTypeId: string
  treatyTypeName: string
  status: 'APPLIED' | 'UNDER_REVIEW' | 'APPROVED' | 'AWAITING_PAYMENT' | 'PAID' | 'ACTIVE' | 'REJECTED'
  paymentStatus: 'AWAITING_PAYMENT' | 'PENDING' | 'PAID' | 'FAILED' | null
  appliedAt: Date
  approvedAt?: Date | null
  rejectedAt?: Date | null
  rejectionReason?: string | null
  latestPayment?: {
    id: string
    amount: number
    currency: string
    status: string
    paymentDate: Date
    paymentMethod: string
  } | null
}

interface Payment {
  id: string
  userTreatyTypeId: string
  amount: number
  currency: string
  paymentMethod: string
  status: 'AWAITING_PAYMENT' | 'PENDING' | 'PAID' | 'FAILED'
  paymentDate: Date
  notes?: string | null
  application: {
    id: string
    user: {
      name: string
      email: string
    }
    treatyType: {
      name: string
    }
  }
}

interface AuditLog {
  id: string
  userId: string
  userName: string
  action: string
  resource: string
  resourceId: string
  success: boolean
  ipAddress: string
  createdAt: Date
  oldValues?: any
  newValues?: any
}

interface TreatyStats {
  totalApplications: number
  activeTreaties: number
  pendingApplications: number
  totalRevenue: number
  pendingPayments: number
  rejectedApplications: number
}

export function TreatyAuditTab() {
  const [applications, setApplications] = useState<TreatyApplication[]>([])
  const [payments, setPayments] = useState<Payment[]>([])
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([])
  const [stats, setStats] = useState<TreatyStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeView, setActiveView] = useState<'applications' | 'payments' | 'audit'>('applications')
  const [filters, setFilters] = useState({
    status: '',
    paymentStatus: '',
    dateFrom: '',
    dateTo: '',
    search: ''
  })

  useEffect(() => {
    fetchAuditData()
  }, [])

  const fetchAuditData = async () => {
    try {
      setLoading(true)
      
      // Fetch all data in parallel
      const [applicationsRes, paymentsRes, auditLogsRes, statsRes] = await Promise.all([
        fetch('/api/admin/treaty-applications'),
        fetch('/api/admin/treaty-payments'),
        fetch('/api/audit/log'),
        fetch('/api/treaty-types/stats')
      ])

      if (applicationsRes.ok) {
        const appsData = await applicationsRes.json()
        setApplications(appsData.applications || [])
      }

      if (paymentsRes.ok) {
        const paymentsData = await paymentsRes.json()
        setPayments(paymentsData.payments || [])
      }

      if (auditLogsRes.ok) {
        const logsData = await auditLogsRes.json()
        // Filter for treaty-related audit logs
        const treatyLogs = (logsData.logs || []).filter((log: AuditLog) => 
          log.resource.includes('Treaty') || 
          log.resource.includes('Payment') ||
          log.action.includes('TREATY') ||
          log.action.includes('PAYMENT')
        )
        setAuditLogs(treatyLogs)
      }

      if (statsRes.ok) {
        const statsData = await statsRes.json()
        setStats(statsData)
      }
    } catch (error) {
      console.error('Error fetching audit data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'APPLIED': { color: 'bg-blue-100 text-blue-800', icon: Clock, label: 'Applied' },
      'UNDER_REVIEW': { color: 'bg-yellow-100 text-yellow-800', icon: Eye, label: 'Under Review' },
      'APPROVED': { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Approved' },
      'AWAITING_PAYMENT': { color: 'bg-orange-100 text-orange-800', icon: DollarSign, label: 'Awaiting Payment' },
      'PAID': { color: 'bg-purple-100 text-purple-800', icon: DollarSign, label: 'Paid' },
      'ACTIVE': { color: 'bg-emerald-100 text-emerald-800', icon: CheckCircle, label: 'Active' },
      'REJECTED': { color: 'bg-red-100 text-red-800', icon: XCircle, label: 'Rejected' },
      'PENDING': { color: 'bg-yellow-100 text-yellow-800', icon: Clock, label: 'Pending' },
      'FAILED': { color: 'bg-red-100 text-red-800', icon: XCircle, label: 'Failed' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || 
      { color: 'bg-gray-100 text-gray-800', icon: AlertCircle, label: status }

    const Icon = config.icon
    return (
      <Badge variant="secondary" className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    )
  }

  const filteredApplications = applications.filter(app => {
    if (filters.status && app.status !== filters.status) return false
    if (filters.paymentStatus && app.paymentStatus !== filters.paymentStatus) return false
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      return (
        app.userName.toLowerCase().includes(searchLower) ||
        app.userEmail.toLowerCase().includes(searchLower) ||
        app.treatyTypeName.toLowerCase().includes(searchLower)
      )
    }
    return true
  })

  const filteredPayments = payments.filter(payment => {
    if (filters.status && payment.status !== filters.status) return false
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      return (
        payment.application.user.name.toLowerCase().includes(searchLower) ||
        payment.application.user.email.toLowerCase().includes(searchLower) ||
        payment.application.treatyType.name.toLowerCase().includes(searchLower)
      )
    }
    return true
  })

  const exportData = () => {
    const data = activeView === 'applications' ? filteredApplications : filteredPayments
    const csv = convertToCSV(data)
    const blob = new Blob([csv], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `treaty-${activeView}-export-${format(new Date(), 'yyyy-MM-dd')}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const convertToCSV = (data: any[]) => {
    if (data.length === 0) return ''
    
    const headers = Object.keys(data[0])
    const csvHeaders = headers.join(',')
    const csvRows = data.map(row => 
      headers.map(header => {
        const value = row[header]
        // Handle nested objects and arrays
        if (typeof value === 'object' && value !== null) {
          return `"${JSON.stringify(value).replace(/"/g, '""')}"`
        }
        return `"${value}"`
      }).join(',')
    )
    
    return [csvHeaders, ...csvRows].join('\n')
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400">Loading treaty audit data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
      {/* View Navigation */}
      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div className="flex space-x-2">
          <Button
            variant={activeView === 'applications' ? 'default' : 'outline'}
            onClick={() => setActiveView('applications')}
          >
            <FileText className="w-4 h-4 mr-2" />
            Applications
          </Button>
          <Button
            variant={activeView === 'payments' ? 'default' : 'outline'}
            onClick={() => setActiveView('payments')}
          >
            <DollarSign className="w-4 h-4 mr-2" />
            Payments
          </Button>
          <Button
            variant={activeView === 'audit' ? 'default' : 'outline'}
            onClick={() => setActiveView('audit')}
          >
            <Eye className="w-4 h-4 mr-2" />
            Audit Logs
          </Button>
        </div>

        <Button onClick={exportData} variant="outline">
          <Download className="w-4 h-4 mr-2" />
          Export CSV
        </Button>
      </div>

      {/* Statistics Summary */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-slate-50 dark:bg-slate-900/30 p-4 rounded-lg">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-slate-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-slate-600 dark:text-slate-300">Total Applications</p>
                <p className="text-2xl font-semibold">{stats.totalApplications}</p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-green-600 dark:text-green-300">Active Treaties</p>
                <p className="text-2xl font-semibold">{stats.activeTreaties}</p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/30 p-4 rounded-lg">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-yellow-600 dark:text-yellow-300">Pending Review</p>
                <p className="text-2xl font-semibold">{stats.pendingApplications}</p>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/30 p-4 rounded-lg">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-purple-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-purple-600 dark:text-purple-300">Total Revenue</p>
                <p className="text-2xl font-semibold">${stats.totalRevenue?.toFixed(2) || '0.00'}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filter Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div>
          <Label htmlFor="search">Search</Label>
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
            <Input
              id="search"
              placeholder="Search users or treaties..."
              className="pl-10"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            />
          </div>
        </div>

        {activeView === 'applications' && (
          <>
            <div>
              <Label htmlFor="status">Application Status</Label>
              <select
                id="status"
                className="w-full rounded-md border border-slate-300 bg-white py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-slate-500"
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              >
                <option value="">All Statuses</option>
                <option value="APPLIED">Applied</option>
                <option value="UNDER_REVIEW">Under Review</option>
                <option value="APPROVED">Approved</option>
                <option value="AWAITING_PAYMENT">Awaiting Payment</option>
                <option value="PAID">Paid</option>
                <option value="ACTIVE">Active</option>
                <option value="REJECTED">Rejected</option>
              </select>
            </div>

            <div>
              <Label htmlFor="paymentStatus">Payment Status</Label>
              <select
                id="paymentStatus"
                className="w-full rounded-md border border-slate-300 bg-white py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-slate-500"
                value={filters.paymentStatus}
                onChange={(e) => setFilters(prev => ({ ...prev, paymentStatus: e.target.value }))}
              >
                <option value="">All Payment Statuses</option>
                <option value="AWAITING_PAYMENT">Awaiting Payment</option>
                <option value="PENDING">Pending</option>
                <option value="PAID">Paid</option>
                <option value="FAILED">Failed</option>
              </select>
            </div>
          </>
        )}

        {activeView === 'payments' && (
          <div>
            <Label htmlFor="paymentStatus">Payment Status</Label>
            <select
              id="paymentStatus"
              className="w-full rounded-md border border-slate-300 bg-white py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-slate-500"
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            >
              <option value="">All Statuses</option>
              <option value="AWAITING_PAYMENT">Awaiting Payment</option>
              <option value="PENDING">Pending</option>
              <option value="PAID">Paid</option>
              <option value="FAILED">Failed</option>
            </select>
          </div>
        )}
      </div>

      {/* Data Tables */}
      {activeView === 'applications' && (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
            <thead className="bg-slate-50 dark:bg-slate-800">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">User</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Treaty Type</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Status</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Payment Status</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Applied Date</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Latest Payment</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
              {filteredApplications.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-slate-500 dark:text-slate-400">
                    No applications found matching your filters
                  </td>
                </tr>
              ) : (
                filteredApplications.map((app) => (
                  <tr key={app.id} className="hover:bg-slate-50 dark:hover:bg-slate-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-slate-900 dark:text-slate-100">{app.userName}</div>
                      <div className="text-sm text-slate-500 dark:text-slate-400">{app.userEmail}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-slate-100">
                      {app.treatyTypeName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(app.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {app.paymentStatus ? getStatusBadge(app.paymentStatus) : <span className="text-sm text-slate-500">-</span>}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                      {format(new Date(app.appliedAt), 'MMM dd, yyyy')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {app.latestPayment ? (
                        <div className="text-sm">
                          <div className="text-slate-900 dark:text-slate-100">${app.latestPayment.amount} {app.latestPayment.currency}</div>
                          <div className="text-slate-500">{getStatusBadge(app.latestPayment.status)}</div>
                        </div>
                      ) : (
                        <span className="text-sm text-slate-500">-</span>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}

      {activeView === 'payments' && (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
            <thead className="bg-slate-50 dark:bg-slate-800">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">User</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Treaty Type</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Amount</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Method</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Status</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Payment Date</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
              {filteredPayments.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-slate-500 dark:text-slate-400">
                    No payments found matching your filters
                  </td>
                </tr>
              ) : (
                filteredPayments.map((payment) => (
                  <tr key={payment.id} className="hover:bg-slate-50 dark:hover:bg-slate-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-slate-900 dark:text-slate-100">{payment.application.user.name}</div>
                      <div className="text-sm text-slate-500 dark:text-slate-400">{payment.application.user.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-slate-100">
                      {payment.application.treatyType.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-slate-100">
                      ${payment.amount} {payment.currency}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-slate-100">
                      {payment.paymentMethod}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(payment.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                      {format(new Date(payment.paymentDate), 'MMM dd, yyyy')}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}

      {activeView === 'audit' && (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
            <thead className="bg-slate-50 dark:bg-slate-800">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Timestamp</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">User</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Action</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Resource</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Status</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">IP Address</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
              {auditLogs.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-slate-500 dark:text-slate-400">
                    No audit logs found matching your filters
                  </td>
                </tr>
              ) : (
                auditLogs.map((log) => (
                  <tr key={log.id} className="hover:bg-slate-50 dark:hover:bg-slate-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                      {format(new Date(log.createdAt), 'MMM dd, yyyy HH:mm')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-slate-100">
                      {log.userName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-slate-100">
                      {log.action}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-slate-100">
                      {log.resource}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {log.success ? (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Success
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="bg-red-100 text-red-800">
                          <XCircle className="w-3 h-3 mr-1" />
                          Failed
                        </Badge>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                      {log.ipAddress}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}