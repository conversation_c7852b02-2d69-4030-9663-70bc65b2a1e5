'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  LogOut,
  RefreshCw,
  User,
  Globe,
  Monitor,
  ChevronDown,
  ChevronRight,
  MapPin,
  Smartphone
} from 'lucide-react';
import { toast } from 'sonner';

interface UserSession {
  id: string;
  sessionToken: string;
  userId: string;
  user: {
    name: string | null;
    email: string | null;
  };
  ipAddress: string | null;
  userAgent: string | null;
  lastActive: Date;
  createdAt: Date;
  expires: Date;
  location?: {
    country?: string;
    city?: string;
    region?: string;
  };
  deviceInfo?: {
    type: 'desktop' | 'mobile' | 'tablet';
    browser: string;
    os: string;
  };
  isRemote?: boolean;
}

export function SessionManagementTab() {
  const [sessions, setSessions] = useState<UserSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [terminating, setTerminating] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [expandedUser, setExpandedUser] = useState<string | null>(null);
  const [expandedSessionDetail, setExpandedSessionDetail] = useState<string | null>(null);

  // Group sessions by user
  const groupedSessions = sessions.reduce((acc, session) => {
    const userKey = session.user.email || session.userId;
    if (!acc[userKey]) {
      acc[userKey] = [];
    }
    acc[userKey].push(session);
    return acc;
  }, {} as Record<string, UserSession[]>);

  const fetchSessions = async () => {
    try {
      setLoading(true);

      // Fetch sessions from the admin API
      const response = await fetch('/api/admin/sessions');

      if (!response.ok) {
        throw new Error('Failed to fetch sessions');
      }

      const data = await response.json();
      setSessions(data.sessions || []);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Error fetching sessions:', error);
      toast.error('Failed to load sessions');
      // Fallback: show empty array
      setSessions([]);
      setLastUpdate(new Date());
    } finally {
      setLoading(false);
    }
  };

  const terminateSession = async (sessionId: string) => {
    try {
      setTerminating(sessionId);
      const response = await fetch(`/api/admin/sessions/${sessionId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to terminate session');
      }

      toast.success('Session terminated successfully');
      fetchSessions(); // Refresh the list
    } catch (error) {
      console.error('Error terminating session:', error);
      toast.error('Failed to terminate session');
    } finally {
      setTerminating(null);
    }
  };

  const terminateAllSessions = async () => {
    if (!confirm('Are you sure you want to terminate all sessions? This will log out all users.')) {
      return;
    }

    try {
      const response = await fetch('/api/admin/sessions', {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to terminate all sessions');
      }

      toast.success('All sessions terminated successfully');
      fetchSessions(); // Refresh the list
    } catch (error) {
      console.error('Error terminating all sessions:', error);
      toast.error('Failed to terminate all sessions');
    }
  };

  useEffect(() => {
    fetchSessions();
    
    // Set up periodic refresh every 30 seconds if auto-refresh is enabled
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(fetchSessions, 30000);
    }
    
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [autoRefresh]);

  const getSessionStatus = (expires: Date) => {
    return new Date() > new Date(expires) ? 'expired' : 'active';
  };

  const isRemoteSession = (session: UserSession) => {
    // For now, we'll consider any session with an IP address as potentially remote
    // In a real implementation, you'd compare against known local IP ranges
    return session.ipAddress && session.ipAddress !== '127.0.0.1' && session.ipAddress !== '::1';
  };

  const getDeviceType = (userAgent: string | null) => {
    if (!userAgent) return 'unknown';

    const ua = userAgent.toLowerCase();
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      return 'mobile';
    }
    if (ua.includes('tablet') || ua.includes('ipad')) {
      return 'tablet';
    }
    return 'desktop';
  };

  const getBrowser = (userAgent: string | null) => {
    if (!userAgent) return 'Unknown';

    const ua = userAgent.toLowerCase();
    if (ua.includes('chrome')) return 'Chrome';
    if (ua.includes('firefox')) return 'Firefox';
    if (ua.includes('safari')) return 'Safari';
    if (ua.includes('edge')) return 'Edge';
    return 'Other';
  };

  const formatUserAgent = (userAgent: string | null) => {
    if (!userAgent) return 'Unknown';
    
    // Simple user agent parsing
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    if (userAgent.includes('Mobile')) return 'Mobile';
    
    return 'Other Browser';
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - new Date(date).getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  const formatLastUpdate = () => {
    if (!lastUpdate) return 'Never';
    return formatTimeAgo(lastUpdate);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
        <span className="ml-2 text-gray-500">Loading sessions...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">

      <div className="border-2 border-slate-700 rounded-xl bg-white dark:bg-gray-800 shadow-lg overflow-hidden">
        {/* Header Section with Navy Blue Background */}
        <div className="bg-slate-800 px-6 py-4" style={{ backgroundColor: '#1e293b' }}>
          <div className="flex items-center">
            <Monitor className="h-5 w-5 mr-2 text-green-400" />
            <h4 className="text-lg font-semibold text-white" style={{ color: '#ffffff' }}>Session Overview</h4>
          </div>
          <p className="text-sm mt-1" style={{ color: '#e2e8f0' }}>
            <span className="font-semibold" style={{ color: '#ffffff' }}>{sessions.length}</span> active session{sessions.length !== 1 ? 's' : ''}
            {sessions.length > 0 && (
              <>
                <span className="mx-2" style={{ color: '#94a3b8' }}>•</span>
                {(() => {
                  const localSessions = sessions.filter(s => !isRemoteSession(s)).length;
                  const remoteSessions = sessions.filter(s => isRemoteSession(s)).length;
                  return (
                    <>
                      <span className="font-medium" style={{ color: '#10b981' }}>{localSessions} local</span>
                      {remoteSessions > 0 && (
                        <>
                          <span className="mx-1" style={{ color: '#94a3b8' }}>•</span>
                          <span className="font-medium" style={{ color: '#ef4444' }}>{remoteSessions} remote</span>
                        </>
                      )}
                    </>
                  );
                })()}
              </>
            )}
            {lastUpdate && (
              <span className="ml-2 text-xs" style={{ color: '#94a3b8' }}>
                • Last updated: {formatLastUpdate()}
              </span>
            )}
          </p>
        </div>
        <div className="p-6">
          {sessions.length === 0 ? (
            <div className="text-center py-8">
              <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <Monitor className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  No Active Sessions
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  There are currently no active user sessions in the system.
                </p>
                <div className="bg-gray-100 dark:bg-gray-800 rounded p-3 text-sm text-gray-700 dark:text-gray-300">
                  <p className="font-medium mb-1">Session Information:</p>
                  <p>• Sessions are tracked in the database for admin monitoring</p>
                  <p>• JWT tokens are used for authentication</p>
                  <p>• Sessions will appear here when users log in</p>
                </div>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow className="rounded-t-xl hover:bg-slate-800 [&>th:first-child]:rounded-tl-xl [&>th:last-child]:rounded-tr-xl" style={{ backgroundColor: '#1e293b' }}>
                  <TableHead className="font-semibold rounded-tl-xl" style={{ color: '#cbd5e1' }}>User</TableHead>
                  <TableHead className="font-semibold" style={{ color: '#cbd5e1' }}>IP Address</TableHead>
                  <TableHead className="font-semibold" style={{ color: '#cbd5e1' }}>Last Active</TableHead>
                  <TableHead className="font-semibold" style={{ color: '#cbd5e1' }}>Status</TableHead>
                  <TableHead className="text-right font-semibold rounded-tr-xl" style={{ color: '#cbd5e1' }}>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Object.entries(groupedSessions).map(([userKey, userSessions], groupIndex) => {
                  const firstSession = userSessions[0];
                  const isExpanded = expandedUser === userKey;
                  const isEvenRow = groupIndex % 2 === 0;
                  const sessionCount = userSessions.length;
                  const localCount = userSessions.filter(s => !isRemoteSession(s)).length;
                  const remoteCount = userSessions.filter(s => isRemoteSession(s)).length;

                  return (
                    <React.Fragment key={userKey}>
                      {/* User Group Summary Row */}
                      <TableRow
                        className={`cursor-pointer hover:bg-transparent ${isEvenRow
                          ? 'bg-white dark:bg-gray-800'
                          : 'bg-gray-50 dark:bg-gray-700'
                        }`}
                        onClick={() => setExpandedUser(isExpanded ? null : userKey)}
                      >
                        <TableCell>
                          <div className="flex items-center">
                            <div className="mr-2">
                              {isExpanded ? (
                                <ChevronDown className="h-4 w-4 text-gray-400" />
                              ) : (
                                <ChevronRight className="h-4 w-4 text-gray-400" />
                              )}
                            </div>
                            <User className="h-4 w-4 mr-2 text-gray-400" />
                            <div>
                              <div className="font-medium">
                                {firstSession.user.name || 'Unknown User'}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                {firstSession.user.email}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {sessionCount} session{sessionCount !== 1 ? 's' : ''}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {localCount} local, {remoteCount} remote
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            {sessionCount} Active
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Terminate all sessions for this user
                              userSessions.forEach(session => terminateSession(session.id));
                            }}
                          >
                            <LogOut className="h-3 w-3 mr-1" />
                            Terminate All
                          </Button>
                        </TableCell>
                      </TableRow>

                      {/* Individual Sessions */}
                      {isExpanded && userSessions.map((session, sessionIndex) => {
                        const status = getSessionStatus(session.expires);
                        const isRemote = isRemoteSession(session);
                        const isSessionEven = (groupIndex * userSessions.length + sessionIndex) % 2 === 0;
                        const isSessionDetailExpanded = expandedSessionDetail === session.id;

                        return (
                          <React.Fragment key={session.id}>
                            <TableRow
                              className={`cursor-pointer hover:bg-transparent pl-8 ${isSessionEven
                                ? 'bg-white dark:bg-gray-800'
                                : 'bg-gray-50 dark:bg-gray-700'
                              }`}
                              onClick={() => setExpandedSessionDetail(isSessionDetailExpanded ? null : session.id)}
                            >
                              <TableCell>
                                <div className="flex items-center ml-6">
                                  <div className="w-4 h-4 border-l-2 border-b-2 border-gray-300 ml-2"></div>
                                  <div className="mr-2">
                                    {isSessionDetailExpanded ? (
                                      <ChevronDown className="h-4 w-4 text-gray-400" />
                                    ) : (
                                      <ChevronRight className="h-4 w-4 text-gray-400" />
                                    )}
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">
                                      Session {sessionIndex + 1}
                                    </div>
                                    <div className="text-xs text-gray-500 dark:text-gray-400">
                                      ID: {session.id.slice(0, 8)}...
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  {session.ipAddress || 'Unknown'}
                                </div>
                              </TableCell>
                              <TableCell>{formatTimeAgo(session.lastActive)}</TableCell>
                              <TableCell>
                                <Badge
                                  variant={status === 'active' ? 'default' : 'secondary'}
                                  className={`flex items-center gap-1 ${status === 'active'
                                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                    : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
                                  }`}
                                >
                                  {status === 'active' ? (
                                    <CheckCircle className="h-3 w-3" />
                                  ) : (
                                    <Clock className="h-3 w-3" />
                                  )}
                                  {status === 'active' ? 'Active' : 'Expired'}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    terminateSession(session.id);
                                  }}
                                  disabled={terminating === session.id}
                                >
                                  {terminating === session.id ? (
                                    <RefreshCw className="h-3 w-3 animate-spin" />
                                  ) : (
                                    <LogOut className="h-3 w-3" />
                                  )}
                                  Terminate
                                </Button>
                              </TableCell>
                            </TableRow>

                            {/* Session Detail Expansion */}
                            {isSessionDetailExpanded && (
                              <TableRow className={`hover:bg-transparent ${isSessionEven
                                ? 'bg-white dark:bg-gray-800'
                                : 'bg-gray-50 dark:bg-gray-700'
                              }`}>
                                <TableCell colSpan={5}>
                                  <div className="p-4 space-y-3 ml-12">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                      <div>
                                        <h5 className="font-medium text-sm mb-2 flex items-center">
                                          <MapPin className="h-4 w-4 mr-1" />
                                          Location Details
                                        </h5>
                                        <div className="space-y-1 text-sm">
                                          <div><span className="text-gray-500">IP Address:</span> {session.ipAddress || 'Unknown'}</div>
                                          <div><span className="text-gray-500">Type:</span>
                                            <Badge
                                              variant={isRemote ? 'destructive' : 'default'}
                                              className={`ml-2 text-xs ${isRemote
                                                ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                                : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                              }`}
                                            >
                                              {isRemote ? 'Remote' : 'Local'}
                                            </Badge>
                                          </div>
                                          {session.location?.city && (
                                            <div><span className="text-gray-500">City:</span> {session.location.city}</div>
                                          )}
                                          {session.location?.country && (
                                            <div><span className="text-gray-500">Country:</span> {session.location.country}</div>
                                          )}
                                          {session.location?.region && (
                                            <div><span className="text-gray-500">Region:</span> {session.location.region}</div>
                                          )}
                                        </div>
                                      </div>

                                      <div>
                                        <h5 className="font-medium text-sm mb-2 flex items-center">
                                          <Smartphone className="h-4 w-4 mr-1" />
                                          Device Details
                                        </h5>
                                        <div className="space-y-1 text-sm">
                                          <div><span className="text-gray-500">User Agent:</span> {session.userAgent || 'Unknown'}</div>
                                          <div><span className="text-gray-500">Device Type:</span> {getDeviceType(session.userAgent)}</div>
                                          <div><span className="text-gray-500">Browser:</span> {getBrowser(session.userAgent)}</div>
                                        </div>
                                      </div>
                                    </div>

                                    <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                                      <div className="grid grid-cols-2 gap-4 text-sm">
                                        <div><span className="text-gray-500">Session Created:</span> {formatTimeAgo(session.createdAt)}</div>
                                        <div><span className="text-gray-500">Expires:</span> {formatTimeAgo(session.expires)}</div>
                                      </div>
                                    </div>
                                  </div>
                                </TableCell>
                              </TableRow>
                            )}
                          </React.Fragment>
                        );
                      })}
                    </React.Fragment>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </div>
      </div>

    </div>
  );
}