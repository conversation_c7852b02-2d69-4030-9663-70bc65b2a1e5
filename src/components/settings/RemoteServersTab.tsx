'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Plus,
  Server,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff
} from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
  getAllRemoteServers,
  deleteRemoteServerById,
  createRemoteServer
} from '@/app/actions/remote-servers-oauth'
import { ServerPermissions } from './remote-server/types'
import { RemoteServerCard } from './remote-server/RemoteServerCard'
import { RemoteServer } from './remote-server/types'

export function RemoteServersTab() {
  const [servers, setServers] = useState<RemoteServer[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)
  const [syncLoading, setSyncLoading] = useState<Record<string, boolean>>({})
  const [serverPermissions, setServerPermissions] = useState<Record<string, ServerPermissions>>({})

  // Edit functionality state
  const [editingServer, setEditingServer] = useState<RemoteServer | null>(null)
  const [editData, setEditData] = useState<Partial<RemoteServer>>({})
  const [showClientSecret, setShowClientSecret] = useState<Record<string, boolean>>({})
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({})

  // Add server form state
  const [newServer, setNewServer] = useState<Omit<RemoteServer, 'id' | 'isActive' | 'createdAt' | 'updatedAt' | 'redirectUris'>>({
    name: '',
    url: '',
    apiEndpoint: '',
    oauthRedirectUris: [],
    developmentRedirectUris: [],
    apiKey: '',
    description: '',
    clientId: '',
    clientSecret: ''
  })

  useEffect(() => { fetchServers() }, [])

  // Fetch permissions for all servers when servers are loaded
  useEffect(() => {
    if (servers.length > 0) {
      servers.forEach(server => {
        fetchServerPermissions(server.id)
      })
    }
  }, [servers])

  const fetchServers = async () => {
    try {
      setLoading(true)
      const result = await getAllRemoteServers()
      if (result.success && result.data) {
        const serversWithDates = result.data.map((s: any) => ({
          ...s,
          createdAt: new Date(s.createdAt),
          updatedAt: new Date(s.updatedAt),
          name: typeof s.name === 'string' ? s.name : (s.name ? String(s.name) : 'Unknown Server'),
          url: typeof s.url === 'string' ? s.url : (s.url ? String(s.url) : ''),
          description: typeof s.description === 'string' ? s.description : (s.description ? String(s.description) : null),
          apiEndpoint: typeof s.apiEndpoint === 'string' ? s.apiEndpoint : (s.apiEndpoint ? String(s.apiEndpoint) : null),
          clientId: typeof s.clientId === 'string' ? s.clientId : (s.clientId ? String(s.clientId) : null),
          clientSecret: typeof s.clientSecret === 'string' ? s.clientSecret : (s.clientSecret ? String(s.clientSecret) : null),
          apiKey: typeof s.apiKey === 'string' ? s.apiKey : (s.apiKey ? String(s.apiKey) : ''),
          oauthRedirectUris: Array.isArray(s.oauthRedirectUris) ? s.oauthRedirectUris : (s.oauthRedirectUris ? [String(s.oauthRedirectUris)] : null),
          developmentRedirectUris: Array.isArray(s.developmentRedirectUris) ? s.developmentRedirectUris : (s.developmentRedirectUris ? [String(s.developmentRedirectUris)] : null),
          redirectUris: Array.isArray(s.redirectUris) ? s.redirectUris : (s.redirectUris ? [String(s.redirectUris)] : null)
        }))
        setServers(serversWithDates)
        setError(null)
      } else {
        setError(result.error || 'Failed to fetch servers')
      }
    } catch {
      setError('Unexpected error')
    } finally {
      setLoading(false)
    }
  }

  const handleAddServer = async () => {
    if (!newServer.name || !newServer.url) {
      setError('Please fill in required fields')
      return
    }
    try {
      const result = await createRemoteServer({
        name: newServer.name,
        url: newServer.url,
        apiEndpoint: newServer.apiEndpoint || undefined,
        oauthRedirectUris: newServer.oauthRedirectUris || undefined,
        developmentRedirectUris: newServer.developmentRedirectUris || undefined,
        description: newServer.description
      })
      if (result.success) {
        setNewServer({ name: '', url: '', apiEndpoint: '', oauthRedirectUris: [], developmentRedirectUris: [], apiKey: '', description: '', clientId: '', clientSecret: '' })
        setError(null)
        await fetchServers()
        setMessage({ type: 'success', text: 'Server added successfully' })
      } else setError(result.error || 'Failed to create server')
    } catch { setError('Unexpected error') }
  }

  const fetchServerPermissions = async (serverId: string) => {
    try {
      const response = await fetch(`/api/remote-servers/${serverId}/permissions`)
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setServerPermissions(prev => ({
            ...prev,
            [serverId]: result.data
          }))
        }
      }
    } catch (error) {
      console.error('Error fetching server permissions:', error)
    }
  }

  const handleSyncPermissions = async (serverId: string) => {
    console.log('Syncing permissions for server:', serverId)

    // Set loading state
    setSyncLoading(prev => ({ ...prev, [serverId]: true }))

    try {
      const response = await fetch(`/api/remote-servers/${serverId}/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Sync successful:', result)

        // Show success message
        setMessage({
          type: 'success',
          text: `Permissions synced successfully! Synced ${result.data?.permissionsSynced || 0} permissions and ${result.data?.rolesSynced || 0} roles.`
        })

        // Clear any previous errors
        setError(null)

        // Refresh permissions display
        await fetchServerPermissions(serverId)
      } else {
        const errorData = await response.json()
        console.error('Sync failed:', errorData)
        setError(`Failed to sync permissions: ${errorData.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('Error syncing permissions:', error)
      setError('Failed to sync permissions: Network error')
    } finally {
      // Clear loading state
      setSyncLoading(prev => ({ ...prev, [serverId]: false }))
    }
  }

  const handleRemoveServer = async (id: string) => {
    try {
      const result = await deleteRemoteServerById(id)
      if (result.success) {
        await fetchServers()
        setMessage({ type: 'success', text: 'Server removed successfully' })
      } else setError(result.error || 'Failed to delete server')
    } catch { setError('Unexpected error') }
  }

  const getCallbackUrl = (baseUrl: string) => {
    try {
      const url = new URL(baseUrl)
      return `${url.origin}/api/auth/member-portal`
    } catch {
      return `${baseUrl}/api/auth/member-portal`
    }
  }

  // Edit functionality handlers
  const handleEditServer = (server: RemoteServer) => {
    setEditingServer(server)
    setEditData(server)
  }

  const handleSaveEdit = async () => {
    if (!editingServer || !editData) {
      return
    }

    try {
      const response = await fetch(`/api/remote-servers/${editingServer.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editData),
      })

      if (response.ok) {
        const result = await response.json()

        // Update local state with server response
        setServers(prev => prev.map(server =>
          server.id === editingServer.id
            ? { ...server, ...result.remoteServer }
            : server
        ))
        setEditingServer(null)
        setEditData({})
        setMessage({ type: 'success', text: 'Server updated successfully' })
      } else {
        const errorData = await response.json()
        setMessage({ type: 'error', text: errorData.message || 'Failed to update server' })
      }
    } catch (error) {
      console.error('Error updating server:', error)
      setMessage({ type: 'error', text: 'Failed to update server' })
    }
  }

  const handleCancelEdit = () => {
    setEditingServer(null)
    setEditData({})
  }

  const handleUpdateEditData = (data: Partial<RemoteServer>) => {
    setEditData(prev => ({ ...prev, ...data }))
  }

  const handleToggleClientSecret = (serverId: string) => {
    setShowClientSecret(prev => ({ ...prev, [serverId]: !prev[serverId] }))
  }

  const handleToggleApiKey = (serverId: string) => {
    setShowApiKey(prev => ({ ...prev, [serverId]: !prev[serverId] }))
  }

  if (loading) return <p>Loading servers…</p>

  return (
    <div className="space-y-6">
      {/* Status Messages */}
      {message && (
        <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50 dark:bg-green-900/20' : 'border-red-200 bg-red-50 dark:bg-red-900/20'}>
          {message.type === 'success' ? (
            <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
          )}
          <AlertTitle>
            {message.type === 'success' ? 'Success' : 'Error'}
          </AlertTitle>
          <AlertDescription className={message.type === 'success' ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {error && <Alert variant="destructive"><AlertCircle /><AlertTitle>Error</AlertTitle><AlertDescription>{error}</AlertDescription></Alert>}

      {/* Add New Server Form */}
      <Card className="w-full bg-white dark:bg-slate-900">
        <CardHeader className="bg-white dark:bg-slate-900">
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add Remote Server
          </CardTitle>
          <CardDescription>
            Configure a new remote server connection for OAuth authentication and API access.
          </CardDescription>
        </CardHeader>
        <CardContent className="bg-white dark:bg-slate-900">
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h4 className="text-lg font-medium flex items-center">
                <Server className="mr-2 text-blue-500" />
                Basic Information
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Server Name *</Label>
                  <Input
                    id="name"
                    value={newServer.name}
                    onChange={e => setNewServer({ ...newServer, name: e.target.value })}
                    placeholder="e.g., NWAPromote Server"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="url">Frontend URL *</Label>
                  <Input
                    id="url"
                    type="url"
                    value={newServer.url}
                    onChange={e => setNewServer({ ...newServer, url: e.target.value })}
                    placeholder="https://nwabusinessalliance.com"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newServer.description || ''}
                  onChange={e => setNewServer({ ...newServer, description: e.target.value })}
                  placeholder="Optional description of this server..."
                  rows={3}
                />
              </div>
            </div>

            {/* OAuth Configuration */}
            <div className="space-y-4 pt-4 border-t">
              <h4 className="text-lg font-medium flex items-center">
                <Server className="mr-2 text-green-500" />
                OAuth Configuration
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="clientId">Client ID</Label>
                  <Input
                    id="clientId"
                    value={newServer.clientId || ''}
                    onChange={e => setNewServer({ ...newServer, clientId: e.target.value })}
                    placeholder="OAuth client identifier"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="clientSecret">Client Secret</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="clientSecret"
                      type={showClientSecret ? "text" : "password"}
                      value={newServer.clientSecret || ''}
                      onChange={e => setNewServer({ ...newServer, clientSecret: e.target.value })}
                      placeholder="OAuth client secret"
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => setShowClientSecret(prev => ({ ...prev, [newServer.name || 'new']: !prev[newServer.name || 'new'] }))}
                    >
                      {showClientSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* API Configuration */}
            <div className="space-y-4 pt-4 border-t">
              <h4 className="text-lg font-medium flex items-center">
                <RefreshCw className="mr-2 text-purple-500" />
                API Configuration
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="apiEndpoint">API Endpoint</Label>
                  <Input
                    id="apiEndpoint"
                    type="url"
                    value={newServer.apiEndpoint || ''}
                    onChange={e => setNewServer({ ...newServer, apiEndpoint: e.target.value })}
                    placeholder="https://api.nwabusinessalliance.com"
                  />
                  <p className="text-xs text-slate-500">
                    Leave empty to use the frontend URL
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="apiKey">API Key</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="apiKey"
                      type={showApiKey ? "text" : "password"}
                      value={newServer.apiKey}
                      onChange={e => setNewServer({ ...newServer, apiKey: e.target.value })}
                      placeholder="Optional API key for additional authentication"
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => setShowApiKey(prev => ({ ...prev, [newServer.name || 'new']: !prev[newServer.name || 'new'] }))}
                    >
                      {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Callback URL Display */}
              {newServer.url && (
                <div className="p-3 border border-slate-200 dark:border-slate-700 rounded-md">
                  <Label>Callback URL</Label>
                  <p className="font-mono text-sm break-all mt-1">
                    {getCallbackUrl(newServer.url)}
                  </p>
                  <p className="text-xs text-slate-500 mt-1">
                    This URL will be used by the remote server to redirect users back to your application
                  </p>
                </div>
              )}
            </div>

            {/* Form Actions */}
            <div className="flex gap-3 pt-4">
              <Button onClick={handleAddServer} disabled={!newServer.name || !newServer.url}>
                Add Server
              </Button>
              <Button
                variant="outline"
                onClick={() => setNewServer({
                  name: '',
                  url: '',
                  apiEndpoint: '',
                  oauthRedirectUris: [],
                  developmentRedirectUris: [],
                  apiKey: '',
                  description: '',
                  clientId: '',
                  clientSecret: ''
                })}
              >
                Clear Form
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configured Servers */}
      <div>
        <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-4">Configured Servers</h3>
        {servers.length === 0 ? (
          <Card className="bg-white dark:bg-slate-900">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Server className="h-12 w-12 text-slate-300 dark:text-slate-600 mb-4" />
              <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">
                No Remote Servers Configured
              </h3>
              <p className="text-slate-600 dark:text-slate-400 text-center mb-4">
                Get started by adding your first remote server connection above.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {servers.map((server) => (
              <RemoteServerCard
                key={server.id}
                server={server}
                isEditing={editingServer?.id === server.id}
                editData={editData}
                onEdit={() => handleEditServer(server)}
                onSave={handleSaveEdit}
                onCancel={handleCancelEdit}
                onUpdateEditData={handleUpdateEditData}
                onFetchPermissions={() => handleSyncPermissions(server.id)}
                onRemove={() => handleRemoveServer(server.id)}
                permissionsLoading={syncLoading[server.id] || false}
                serverPermissions={serverPermissions[server.id]}
                showClientSecret={showClientSecret[server.id] || false}
                showApiKey={showApiKey[server.id] || false}
                onToggleClientSecret={() => handleToggleClientSecret(server.id)}
                onToggleApiKey={() => handleToggleApiKey(server.id)}
                serverStatus={undefined}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
