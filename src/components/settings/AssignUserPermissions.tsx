'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  Shield,
  Search,
  User,
  Settings,
  Key,
  Plus,
  Trash2,
  CheckCircle,
  XCircle,
  RefreshCw,
  Eye,
  EyeOff,
  Crown,
  Loader2,
  ChevronDown,
  ChevronRight,
  UserCheck,
  AlertCircle,
  ArrowRight,
  FileText,
  Building2,
  UserCog,
  Lock
} from 'lucide-react'

// TypeScript interfaces
interface User {
  id: string
  name: string | null
  email: string | null
  profile?: {
    firstName: string | null
    lastName: string | null
  } | null
}

interface Role {
  id: string
  name: string
  description: string | null
  permissions: string[]
}

interface UserPermissions {
  userId: string
  user: {
    name: string | null
    email: string | null
    profile?: {
      firstName: string | null
      lastName: string | null
    } | null
  }
  roles: RoleWithDetails[]
  directPermissions: string[]
  allPermissions: string[]
}

interface PermissionCategoryGroup {
  id: string
  name: string
  icon: React.ReactNode
  permissions: Permission[]
}

interface Permission {
  id: string
  name: string
  resource: string
  action: string
  description: string | null
  createdAt: string
}

interface RoleWithDetails {
  id: string
  name: string
  description: string | null
  isSystem: boolean
  userCount: number
  permissionCount: number
  createdAt: string
  updatedAt: string
}

interface PermissionCategory {
  id: string
  name: string
  icon: React.ReactNode
  permissions: Permission[]
}

interface UserPermissionAssignment {
  id: string
  userId: string
  permissionId: string
  permission: Permission
  isAssigned: boolean
  assignedAt?: Date
  source: 'role' | 'individual' // Whether it comes from a role or individual assignment
}

export default function AssignUserPermissions() {
  // Authentication
  const { data: session, status } = useSession()

  // State management
  const [users, setUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<RoleWithDetails[]>([])
  const [allRoles, setAllRoles] = useState<RoleWithDetails[]>([])
  const [allPermissions, setAllPermissions] = useState<Permission[]>([])
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null)
  const [userSearchTerm, setUserSearchTerm] = useState('')
  const [userPermissions, setUserPermissions] = useState<UserPermissions | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [syncing, setSyncing] = useState(false)
  const [userPermissionsLoading, setUserPermissionsLoading] = useState(false)
  const [allDataLoading, setAllDataLoading] = useState(false)
  const [permissionSearchTerm, setPermissionSearchTerm] = useState('')
  const [roleSearchTerm, setRoleSearchTerm] = useState('')
  const [showAdvancedPermissions, setShowAdvancedPermissions] = useState(false)
  const [expandedRoleId, setExpandedRoleId] = useState<string | null>(null)
  const [assigningRoleId, setAssigningRoleId] = useState<string | null>(null)
  const [assigningPermissionId, setAssigningPermissionId] = useState<string | null>(null)

  // Helper function to estimate permission count based on role name
  const getEstimatedPermissionCount = (roleName: string) => {
    const name = roleName.toLowerCase()
    if (name.includes('admin')) return 27
    if (name.includes('envoy') || name.includes('plenipotentiary')) return 3
    if (name.includes('user')) return 4
    if (name.includes('contributor')) return 13
    if (name.includes('moderator')) return 8
    if (name.includes('member')) return 3
    return 5 // default
  }

  // Computed values
  const filteredUsers = users.filter(user => {
    if (!userSearchTerm) return true

    const searchLower = userSearchTerm.toLowerCase()
    const name = user.name?.toLowerCase() || ''
    const email = user.email?.toLowerCase() || ''
    const firstName = user.profile?.firstName?.toLowerCase() || ''
    const lastName = user.profile?.lastName?.toLowerCase() || ''

    return name.includes(searchLower) ||
           email.includes(searchLower) ||
           firstName.includes(searchLower) ||
           lastName.includes(searchLower)
  })

  const getUserDisplayName = (user: User) => {
    if (user.profile?.firstName || user.profile?.lastName) {
      return `${user.profile.firstName || ''} ${user.profile.lastName || ''}`.trim()
    }
    return user.name || user.email || 'Unknown User'
  }

  const selectedUser = users.find(u => u.id === selectedUserId)

  // Group permissions by category
  const getPermissionCategories = (permissions: Permission[]): PermissionCategoryGroup[] => {
    const categories: { [key: string]: PermissionCategoryGroup } = {
      treaties: {
        id: 'treaties',
        name: 'Treaties',
        icon: <FileText className="h-4 w-4" />,
        permissions: []
      },
      positions: {
        id: 'positions',
        name: 'Positions',
        icon: <Building2 className="h-4 w-4" />,
        permissions: []
      },
      users: {
        id: 'users',
        name: 'User Management',
        icon: <UserCog className="h-4 w-4" />,
        permissions: []
      },
      system: {
        id: 'system',
        name: 'System',
        icon: <Settings className="h-4 w-4" />,
        permissions: []
      },
      admin: {
        id: 'admin',
        name: 'Administration',
        icon: <Shield className="h-4 w-4" />,
        permissions: []
      }
    }

    permissions.forEach(permission => {
      if (permission.resource.includes('treaty')) {
        categories.treaties.permissions.push(permission)
      } else if (permission.resource.includes('position')) {
        categories.positions.permissions.push(permission)
      } else if (permission.resource.includes('user') || permission.resource.includes('admin')) {
        categories.users.permissions.push(permission)
      } else if (permission.resource.includes('system') || permission.resource.includes('audit')) {
        categories.system.permissions.push(permission)
      } else {
        categories.admin.permissions.push(permission)
      }
    })

    return Object.values(categories).filter(category => category.permissions.length > 0)
  }

  // Check if a permission is assigned to the user (either directly or through roles)
  const isPermissionAssigned = (permissionName: string): boolean => {
    if (!userPermissions) return false

    // Check direct permissions
    if (userPermissions.directPermissions.includes(permissionName)) {
      return true
    }

    // Check permissions from roles
    // Note: This would need to be implemented based on how role permissions are stored
    // For now, we'll assume role permissions are not directly accessible in this component
    return false
  }

  // Get permission source (role or individual)
  const getPermissionSource = (permissionName: string): 'role' | 'individual' | null => {
    if (!userPermissions) return null

    if (userPermissions.directPermissions.includes(permissionName)) {
      return 'individual'
    }

    // Check if permission comes from any role
    // Note: This would need to be implemented based on how role permissions are stored
    return null
  }

  // Computed values for filtering
  const filteredPermissions = allPermissions.filter(permission => {
    if (!permissionSearchTerm) return true

    const searchLower = permissionSearchTerm.toLowerCase()
    return permission.name.toLowerCase().includes(searchLower) ||
           permission.resource.toLowerCase().includes(searchLower) ||
           permission.action.toLowerCase().includes(searchLower) ||
           (permission.description && permission.description.toLowerCase().includes(searchLower))
  })

  const filteredRoles = allRoles.filter(role => {
    if (!roleSearchTerm) return true

    const searchLower = roleSearchTerm.toLowerCase()
    return role.name.toLowerCase().includes(searchLower) ||
           (role.description && role.description.toLowerCase().includes(searchLower))
  })

  // Group permissions by resource
  const permissionsByResource = filteredPermissions.reduce((acc, permission) => {
    if (!acc[permission.resource]) {
      acc[permission.resource] = []
    }
    acc[permission.resource].push(permission)
    return acc
  }, {} as Record<string, Permission[]>)

  // Data loading
  useEffect(() => {
    loadData()
    loadAllPermissionsAndRoles()
  }, [])

  useEffect(() => {
    if (selectedUserId) {
      console.log('Loading permissions for user:', selectedUserId)
      loadUserPermissions(selectedUserId)
    } else {
      console.log('No user selected, clearing permissions')
      setUserPermissions(null)
    }
  }, [selectedUserId])

  const loadData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Load users
      const usersResponse = await fetch('/api/users')
      if (usersResponse.ok) {
        const usersData = await usersResponse.json()
        setUsers(usersData.users || [])
      } else {
        throw new Error('Failed to load users')
      }

      // Load roles
      const rolesResponse = await fetch('/api/admin/roles')
      if (rolesResponse.ok) {
        const rolesData = await rolesResponse.json()
        const loadedRoles = rolesData.roles || []
        console.log('Loaded roles:', loadedRoles.map((r: any) => ({ id: r.id, name: r.name, permissionCount: r.permissionCount })))
        setRoles(loadedRoles)
      } else {
        throw new Error('Failed to load roles')
      }

    } catch (error) {
      console.error('Error loading data:', error)
      setError('Failed to load permission data')
    } finally {
      setLoading(false)
    }
  }

  const loadAllPermissionsAndRoles = async () => {
    try {
      setAllDataLoading(true)
      setError(null)

      // Load all permissions
      const permissionsResponse = await fetch('/api/admin/permissions')
      if (permissionsResponse.ok) {
        const permissionsData = await permissionsResponse.json()
        setAllPermissions(permissionsData.permissions || [])
      } else {
        throw new Error('Failed to load permissions')
      }

      // Load all roles with details
      const allRolesResponse = await fetch('/api/admin/roles')
      if (allRolesResponse.ok) {
        const allRolesData = await allRolesResponse.json()
        setAllRoles(allRolesData.roles || [])
      } else {
        throw new Error('Failed to load all roles')
      }

    } catch (error) {
      console.error('Error loading all permissions and roles:', error)
      setError('Failed to load permissions and roles data')
    } finally {
      setAllDataLoading(false)
    }
  }

  const loadUserPermissions = async (userId: string) => {
    try {
      setUserPermissionsLoading(true)
      setError(null)

      // Load user's local roles
      const rolesResponse = await fetch(`/api/users/${userId}/roles?includeLocal=true`)
      let userRoles = []
      if (rolesResponse.ok) {
        const rolesData = await rolesResponse.json()
        const rawRoles = rolesData.localRoles || []
        // Transform API response to match expected format
        userRoles = rawRoles.map((r: any) => ({
          id: r.roleId,        // Use roleId as the main ID
          name: r.roleName,    // Use roleName as the name
          description: null,   // API doesn't provide description
          isSystem: false,     // Default to false
          userCount: 0,        // API doesn't provide user count
          permissionCount: 0,  // API doesn't provide permission count
          createdAt: r.assignedAt || new Date().toISOString(),
          updatedAt: r.assignedAt || new Date().toISOString()
        }))
        console.log('User local roles loaded from API:', userRoles.map((r: any) => ({ id: r.id, name: r.name })))
      } else {
        console.error('Failed to load user roles:', rolesResponse.status, rolesResponse.statusText)
      }

      // Load user's local permissions
      const localPermissionsResponse = await fetch(`/api/users/${userId}/local-permissions`)
      let localPermissions = []
      if (localPermissionsResponse.ok) {
        const permissionsData = await localPermissionsResponse.json()
        localPermissions = permissionsData.permissions || []
      }

      // For now, just use direct permissions as all permissions
      // In a full implementation, we'd combine role permissions here too
      const allUserPermissions = [...new Set([...localPermissions])]

      // Transform to UserPermissions format
      const transformedPermissions: UserPermissions = {
        userId: userId,
        user: {
          name: null,
          email: null
        },
        roles: userRoles,
        directPermissions: localPermissions,
        allPermissions: allUserPermissions
      }
      setUserPermissions(transformedPermissions)
    } catch (error) {
      console.error('Error loading user permissions:', error)
      setError('Failed to load user permissions')
      setUserPermissions(null)
    } finally {
      setUserPermissionsLoading(false)
    }
  }

  const assignPermission = async (userId: string, permissionName: string) => {
    try {
      setError(null)
      setAssigningPermissionId(permissionName)
      console.log('Assigning individual permission:', { userId, permissionName })

      // Update UI state immediately for better UX
      setUserPermissions(prev => {
        if (!prev) return prev
        return {
          ...prev,
          directPermissions: [...(prev.directPermissions || []), permissionName],
          allPermissions: [...new Set([...(prev.allPermissions || []), permissionName])]
        }
      })

      const response = await fetch('/api/local-permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: userId,
          permissionName: permissionName,
          action: 'assign'
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Permission assignment successful:', result)
        await loadUserPermissions(userId) // Refresh to get updated data
        setError(null)
      } else {
        let errorData
        try {
          errorData = await response.json()
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
          errorData = { error: `HTTP ${response.status}: ${response.statusText}` }
        }
        console.error('Permission assignment failed:', errorData)
        // Revert UI state on failure
        setUserPermissions(prev => {
          if (!prev) return prev
          return {
            ...prev,
            directPermissions: prev.directPermissions?.filter(p => p !== permissionName) || [],
            allPermissions: prev.allPermissions?.filter(p => p !== permissionName) || []
          }
        })
        setError(errorData.error || `Failed to assign permission: ${response.status} ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error assigning permission:', error)
      // Revert UI state on error
      setUserPermissions(prev => {
        if (!prev) return prev
        return {
          ...prev,
          directPermissions: prev.directPermissions?.filter(p => p !== permissionName) || [],
          allPermissions: prev.allPermissions?.filter(p => p !== permissionName) || []
        }
      })
      setError('Failed to assign permission')
    } finally {
      setAssigningPermissionId(null)
    }
  }

  const removePermission = async (userId: string, permissionName: string) => {
    try {
      setError(null)
      setAssigningPermissionId(permissionName)
      console.log('Removing individual permission:', { userId, permissionName })

      // Update UI state immediately for better UX
      setUserPermissions(prev => {
        if (!prev) return prev
        return {
          ...prev,
          directPermissions: prev.directPermissions?.filter(p => p !== permissionName) || [],
          allPermissions: prev.allPermissions?.filter(p => p !== permissionName) || []
        }
      })

      const response = await fetch('/api/local-permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: userId,
          permissionName: permissionName,
          action: 'remove'
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Permission removal successful:', result)
        await loadUserPermissions(userId) // Refresh to get updated data
        setError(null)
      } else {
        let errorData
        try {
          errorData = await response.json()
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
          errorData = { error: `HTTP ${response.status}: ${response.statusText}` }
        }
        console.error('Permission removal failed:', errorData)
        // Revert UI state on failure
        setUserPermissions(prev => {
          if (!prev) return prev
          return {
            ...prev,
            directPermissions: [...(prev.directPermissions || []), permissionName],
            allPermissions: [...(prev.allPermissions || []), permissionName]
          }
        })
        setError(errorData.error || `Failed to remove permission: ${response.status} ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error removing permission:', error)
      // Revert UI state on error
      setUserPermissions(prev => {
        if (!prev) return prev
        return {
          ...prev,
          directPermissions: [...(prev.directPermissions || []), permissionName],
          allPermissions: [...(prev.allPermissions || []), permissionName]
        }
      })
      setError('Failed to remove permission')
    } finally {
      setAssigningPermissionId(null)
    }
  }

  // Authentication check
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2 text-slate-600">
          <Loader2 className="h-4 w-4 animate-spin" />
          Checking authentication...
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <h1 className="text-xl font-semibold text-slate-900 mb-2">Not authenticated</h1>
          <p className="text-slate-600">Please log in to manage permissions.</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2 text-slate-600">
          <Loader2 className="h-4 w-4 animate-spin" />
          Loading permission data...
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-slate-900">Assign User Permissions</h1>
          <p className="text-slate-600 mt-1">Select a user to manage their individual permissions</p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={loadData}
          disabled={loading}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription className="flex items-center justify-between">
            <span>{error}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setError(null)
                loadData()
              }}
              className="ml-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User Selection Panel */}
        <div className="space-y-4">
          <div>
            <h3 className="font-medium mb-3">Select User</h3>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search users..."
                value={userSearchTerm}
                onChange={(e) => setUserSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="border border-slate-200 dark:border-slate-700 rounded-lg">
            {filteredUsers.length === 0 ? (
              <div className="p-4 text-center text-slate-700 dark:text-slate-300">
                {userSearchTerm ? 'No users found matching search' : 'No users available'}
              </div>
            ) : (
              <div className="divide-y divide-slate-200 dark:divide-slate-700">
                {filteredUsers.map(user => (
                  <button
                    key={user.id}
                    onClick={() => setSelectedUserId(user.id)}
                    className={`w-full p-3 text-left ${
                      selectedUserId === user.id ? 'border-r-2 border-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-slate-400" />
                      <div className="min-w-0 flex-1">
                        <div className="font-medium text-sm truncate">{getUserDisplayName(user)}</div>
                        <div className="text-xs text-slate-600 dark:text-slate-400 truncate" title={user.email || ''}>
                          {user.email && user.email.length > 25
                            ? `${user.email.substring(0, 22)}...`
                            : user.email
                          }
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Permission Assignment Panel */}
        <div className="lg:col-span-2">
          {selectedUserId ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">
                  Permissions for {selectedUser ? getUserDisplayName(selectedUser) : 'User'}
                </h3>
              </div>

              {/* Permission Loading State */}
              {userPermissionsLoading && (
                <div className="flex items-center justify-center p-8">
                  <div className="flex items-center gap-2 text-slate-600">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Loading user permissions...
                  </div>
                </div>
              )}

              {/* Permission Error State */}
              {error && error.includes('permissions') && !userPermissionsLoading && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="flex items-center justify-between">
                    <span>Failed to load user permissions</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => selectedUserId && loadUserPermissions(selectedUserId)}
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Retry
                    </Button>
                  </AlertDescription>
                </Alert>
              )}

              {/* Permission Summary */}
              {userPermissions && !userPermissionsLoading && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Crown className="h-5 w-5 text-blue-600" />
                      <span className="font-medium text-blue-800">Roles</span>
                    </div>
                    <div className="text-2xl font-bold text-blue-900">{userPermissions.roles?.length || 0}</div>
                  </div>

                  <div className="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Shield className="h-5 w-5 text-green-600" />
                      <span className="font-medium text-green-800">Total Permissions</span>
                    </div>
                    <div className="text-2xl font-bold text-green-900">{allPermissions.length}</div>
                  </div>

                  <div className="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Key className="h-5 w-5 text-purple-600" />
                      <span className="font-medium text-purple-800">Direct Permissions</span>
                    </div>
                    <div className="text-2xl font-bold text-purple-900">{userPermissions.directPermissions?.length || 0}</div>
                  </div>

                </div>
              )}

              {/* Assigned Roles Display */}
              {userPermissions && userPermissions.roles && userPermissions.roles.length > 0 && (
                <div className="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Crown className="h-5 w-5" />
                    Assigned Roles
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {userPermissions.roles.map(role => (
                      <Badge key={role.id} variant="secondary" className="flex items-center gap-1">
                        <Crown className="h-3 w-3" />
                        {role.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Individual Permissions */}
              <div className="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  Individual Permissions
                </h4>

                {userPermissionsLoading ? (
                  <div className="flex items-center justify-center p-8">
                    <div className="flex items-center gap-2 text-slate-600">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Loading user permissions...
                    </div>
                  </div>
                ) : !userPermissions ? (
                  <div className="flex items-center justify-center p-8">
                    <div className="text-center">
                      <AlertCircle className="h-8 w-8 mx-auto mb-3 text-slate-400" />
                      <p className="text-sm text-slate-600 mb-3">User permissions not loaded</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => selectedUserId && loadUserPermissions(selectedUserId)}
                        className="flex items-center gap-2"
                      >
                        <RefreshCw className="h-4 w-4" />
                        Load Permissions
                      </Button>
                    </div>
                  </div>
                ) : allPermissions.length === 0 ? (
                  <p className="text-sm text-slate-500 text-center py-4">No permissions available</p>
                ) : (
                  <div className="space-y-6">
                    {getPermissionCategories(allPermissions).map(category => (
                      <div key={category.id}>
                        <h5 className="font-medium mb-3 flex items-center gap-2">
                          {category.icon}
                          {category.name}
                        </h5>
                        <div className="space-y-2">
                          {category.permissions.map(permission => {
                            const isAssigned = isPermissionAssigned(permission.name)
                            const source = getPermissionSource(permission.name)

                            return (
                              <div key={permission.id} className="flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg">
                                <div className="flex-1">
                                  <div className="font-medium text-sm text-slate-800 dark:text-slate-200">
                                    {permission.name}
                                  </div>
                                  {permission.description && (
                                    <div className="text-xs text-slate-600 dark:text-slate-400 mt-1">
                                      {permission.description}
                                    </div>
                                  )}
                                  {source && (
                                    <div className="flex items-center gap-2 mt-1">
                                      <span className={`text-xs px-2 py-1 rounded ${
                                        source === 'individual'
                                          ? 'bg-green-100 text-green-800'
                                          : 'bg-blue-100 text-blue-800'
                                      }`}>
                                        {source === 'individual' ? 'Direct Assignment' : 'From Role'}
                                      </span>
                                    </div>
                                  )}
                                </div>
                                <Button
                                  size="sm"
                                  disabled={assigningPermissionId === permission.name}
                                  onClick={() => {
                                    console.log(`Button clicked for permission ${permission.name}:`, {
                                      permissionName: permission.name,
                                      isAssigned: isAssigned,
                                      selectedUserId: selectedUserId
                                    })
                                    if (isAssigned) {
                                      removePermission(selectedUserId!, permission.name)
                                    } else {
                                      assignPermission(selectedUserId!, permission.name)
                                    }
                                  }}
                                  className={`flex-shrink-0 ml-4 ${
                                    isAssigned
                                      ? 'bg-red-600 hover:bg-red-700'
                                      : 'bg-green-600 hover:bg-green-700'
                                  }`}
                                >
                                  {assigningPermissionId === permission.name ? (
                                    <>
                                      <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                                      {isAssigned ? 'Removing...' : 'Assigning...'}
                                    </>
                                  ) : isAssigned ? (
                                    <>
                                      <Trash2 className="h-4 w-4 mr-1" />
                                      Remove
                                    </>
                                  ) : (
                                    <>
                                      <Plus className="h-4 w-4 mr-1" />
                                      Assign
                                    </>
                                  )}
                                </Button>
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-64 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg">
              <div className="text-center text-slate-700 dark:text-slate-300">
                <Users className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>Select a user to manage their individual permissions</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}