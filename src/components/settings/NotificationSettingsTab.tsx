'use client';

import React, { useState, useEffect } from 'react';
import { Bell, Mail, MessageSquare, Smartphone, Users, FileText, Server, Settings, Shield, Moon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  inAppNotifications: boolean;
  notificationTypes: {
    userManagement: boolean;
    treaty: boolean;
    remoteServer: boolean;
    system: boolean;
    security: boolean;
  };
  priorityFilters: {
    urgent: boolean;
    high: boolean;
    normal: boolean;
    low: boolean;
  };
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

export function NotificationSettingsTab() {
  const [settings, setSettings] = useState<NotificationSettings>({
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    inAppNotifications: true,
    notificationTypes: {
      userManagement: true,
      treaty: true,
      remoteServer: true,
      system: true,
      security: true,
    },
    priorityFilters: {
      urgent: true,
      high: true,
      normal: true,
      low: false,
    },
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '08:00',
    },
  });

  const [loading, setLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Load settings from API
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const response = await fetch('/api/user/notification-settings', {
          credentials: 'include',
        });

        if (response.ok) {
          const data = await response.json();
          setSettings(prev => ({ ...prev, ...data }));
        }
      } catch (error) {
        console.error('Error loading notification settings:', error);
      }
    };

    loadSettings();
  }, []);

  // Handle setting changes
  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => {
      const newSettings = { ...prev };

      // Handle nested objects
      if (key.includes('.')) {
        const [parent, child] = key.split('.');
        const parentKey = parent as keyof NotificationSettings;
        const parentValue = newSettings[parentKey];

        if (parentValue && typeof parentValue === 'object') {
          (newSettings[parentKey] as any) = {
            ...parentValue,
            [child]: value,
          };
        }
      } else {
        (newSettings as any)[key] = value;
      }

      return newSettings;
    });
    setHasChanges(true);
  };

  // Save settings
  const saveSettings = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/notification-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
        credentials: 'include',
      });

      if (response.ok) {
        setHasChanges(false);
        toast.success('Notification settings saved successfully');
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (error) {
      toast.error('Failed to save notification settings');
      console.error('Error saving notification settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const getNotificationTypeDescription = (type: string) => {
    switch (type) {
      case 'userManagement': return 'User creation, updates, and management events';
      case 'treaty': return 'Treaty applications, approvals, and renewals';
      case 'remoteServer': return 'Remote server connections and API events';
      case 'system': return 'System maintenance, backups, and updates';
      case 'security': return 'Security alerts and authentication events';
      default: return '';
    }
  };

  const getPriorityDescription = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'Critical system issues requiring immediate attention';
      case 'high': return 'Important events that need prompt action';
      case 'normal': return 'Standard notifications for regular activities';
      case 'low': return 'Informational messages and routine updates';
      default: return '';
    }
  };

  const getNotificationTypeIcon = (type: string) => {
    switch (type) {
      case 'userManagement': return <Users className="h-4 w-4 text-emerald-500" />;
      case 'treaty': return <FileText className="h-4 w-4 text-emerald-500" />;
      case 'remoteServer': return <Server className="h-4 w-4 text-emerald-500" />;
      case 'system': return <Settings className="h-4 w-4 text-emerald-500" />;
      case 'security': return <Shield className="h-4 w-4 text-emerald-500" />;
      default: return <Bell className="h-4 w-4 text-emerald-500" />;
    }
  };

  return (
    <div className="space-y-6 bg-white dark:bg-slate-800">

      {/* Notification Methods */}
      <Card className="bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700">
        <CardHeader className="bg-slate-800 text-white rounded-t-lg">
          <CardTitle className="flex items-center text-white">
            <MessageSquare className="h-5 w-5 mr-2 text-emerald-400" />
            Notification Methods
          </CardTitle>
          <CardDescription className="text-slate-200">
            Choose how you want to receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 bg-white dark:bg-slate-800">
          <div className="flex items-center justify-between p-3 rounded-md bg-slate-50 dark:bg-slate-800/50">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <Mail className="h-5 w-5 text-emerald-500" />
              <div className="flex-1 min-w-0">
                <div className="font-medium">Email Notifications</div>
                <div className="text-sm text-gray-500 mt-1">Receive notifications via email</div>
              </div>
            </div>
            <Switch
              checked={settings.emailNotifications}
              onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between p-3 rounded-md bg-white dark:bg-slate-900/30">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <Bell className="h-5 w-5 text-emerald-500" />
              <div className="flex-1 min-w-0">
                <div className="font-medium">Push Notifications</div>
                <div className="text-sm text-gray-500 mt-1">Browser push notifications</div>
              </div>
            </div>
            <Switch
              checked={settings.pushNotifications}
              onCheckedChange={(checked) => handleSettingChange('pushNotifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between p-3 rounded-md bg-slate-50 dark:bg-slate-800/50">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <Smartphone className="h-5 w-5 text-emerald-500" />
              <div className="flex-1 min-w-0">
                <div className="font-medium">SMS Notifications</div>
                <div className="text-sm text-gray-500 mt-1">Text message notifications</div>
              </div>
            </div>
            <Switch
              checked={settings.smsNotifications}
              onCheckedChange={(checked) => handleSettingChange('smsNotifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between p-3 rounded-md bg-white dark:bg-slate-900/30">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <MessageSquare className="h-5 w-5 text-emerald-500" />
              <div className="flex-1 min-w-0">
                <div className="font-medium">In-App Notifications</div>
                <div className="text-sm text-gray-500 mt-1">Show notifications within the application</div>
              </div>
            </div>
            <Switch
              checked={settings.inAppNotifications}
              onCheckedChange={(checked) => handleSettingChange('inAppNotifications', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Notification Types */}
      <Card className="bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700">
        <CardHeader className="bg-slate-800 text-white rounded-t-lg">
          <CardTitle className="flex items-center text-white">
            <Bell className="h-5 w-5 mr-2 text-emerald-400" />
            Notification Categories
          </CardTitle>
          <CardDescription className="text-slate-200">
            Choose which types of notifications you want to receive
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 bg-white dark:bg-slate-800">
          {Object.entries(settings.notificationTypes).map(([key, value], index) => (
            <div key={key} className={`flex items-center justify-between p-3 rounded-md ${
              index % 2 === 0
                ? 'bg-slate-50 dark:bg-slate-800/50'
                : 'bg-white dark:bg-slate-900/30'
            }`}>
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                {getNotificationTypeIcon(key)}
                <div className="flex-1 min-w-0">
                  <div className="font-medium capitalize text-sm">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {getNotificationTypeDescription(key)}
                  </div>
                </div>
              </div>
              <Switch
                checked={value}
                onCheckedChange={(checked) => handleSettingChange(`notificationTypes.${key}`, checked)}
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Priority Filters */}
      <Card className="bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700">
        <CardHeader className="bg-slate-800 text-white rounded-t-lg">
          <CardTitle className="flex items-center text-white">
            <MessageSquare className="h-5 w-5 mr-2 text-emerald-400" />
            Priority Filters
          </CardTitle>
          <CardDescription className="text-slate-200">
            Set minimum priority level for notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 bg-white dark:bg-slate-800">
          {Object.entries(settings.priorityFilters).map(([key, value], index) => (
            <div key={key} className={`flex items-center justify-between p-3 rounded-md ${
              index % 2 === 0
                ? 'bg-slate-50 dark:bg-slate-800/50'
                : 'bg-white dark:bg-slate-900/30'
            }`}>
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <Badge
                  className={
                    key === 'urgent' ? 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-200 dark:border-red-800' :
                    key === 'high' ? 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900 dark:text-orange-200 dark:border-orange-800' :
                    key === 'normal' ? 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:border-blue-800' :
                    'bg-slate-100 text-slate-800 border-slate-200 dark:bg-slate-700 dark:text-slate-300 dark:border-slate-600'
                  }
                >
                  {key.charAt(0).toUpperCase() + key.slice(1)}
                </Badge>
                <div className="flex-1 min-w-0">
                  <div className="text-sm text-gray-500">
                    {getPriorityDescription(key)}
                  </div>
                </div>
              </div>
              <Switch
                checked={value}
                onCheckedChange={(checked) => handleSettingChange(`priorityFilters.${key}`, checked)}
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Quiet Hours */}
      <Card className="bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700">
        <CardHeader className="bg-slate-800 text-white rounded-t-lg">
          <CardTitle className="flex items-center text-white">
            <Bell className="h-5 w-5 mr-2 text-emerald-400" />
            Quiet Hours
          </CardTitle>
          <CardDescription className="text-slate-200">
            Set times when you don't want to receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 bg-white dark:bg-slate-800">
          <div className="flex items-center justify-between p-3 rounded-md bg-slate-50 dark:bg-slate-800/50">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <Moon className="h-5 w-5 text-emerald-500" />
              <div className="flex-1 min-w-0">
                <div className="font-medium">Enable Quiet Hours</div>
                <div className="text-sm text-gray-500 mt-1">
                  Pause notifications during specified hours
                </div>
              </div>
            </div>
            <Switch
              checked={settings.quietHours.enabled}
              onCheckedChange={(checked) => handleSettingChange('quietHours.enabled', checked)}
            />
          </div>

          {settings.quietHours.enabled && (
            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Start Time
                </label>
                <input
                  type="time"
                  value={settings.quietHours.start}
                  onChange={(e) => handleSettingChange('quietHours.start', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  End Time
                </label>
                <input
                  type="time"
                  value={settings.quietHours.end}
                  onChange={(e) => handleSettingChange('quietHours.end', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-sm"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Save button at bottom if there are changes */}
      {hasChanges && (
        <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-slate-700">
          <Button
            onClick={saveSettings}
            disabled={loading}
            className="bg-emerald-600 hover:bg-emerald-700 text-white"
          >
            {loading ? 'Saving...' : 'Save All Changes'}
          </Button>
        </div>
      )}
    </div>
  );
}