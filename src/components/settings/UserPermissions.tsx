'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Users, 
  Key,
  Save,
  RefreshCw,
  AlertCircle
} from 'lucide-react'
import { 
  fetchRemoteServerPermissions,
  assignUserPermissions,
  removeUserPermissions,
  getUserPermissions
} from '@/app/actions/remote-servers'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

interface Permission {
  id: string
  name: string
  description: string
  category: string
  isActive: boolean
  createdAt: Date
}

interface Role {
  name: string
  description: string
  permissions: string[]
}

interface User {
  id: string
  name: string | null
  email: string | null
  profile: {
    nwaEmail: string | null
  } | null
}

export function UserPermissions({ serverId, userId }: { serverId: string; userId: string }) {
  const [user, setUser] = useState<User | null>(null)
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [userPermissions, setUserPermissions] = useState<string[]>([])
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [permissionsLoading, setPermissionsLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchPermissions = useCallback(async () => {
    try {
      setPermissionsLoading(true)
      const result = await fetchRemoteServerPermissions(serverId)
      if (result.success && result.data) {
        setPermissions(result.data.permissions as unknown as Permission[])
        setRoles(result.data.roles)
      } else {
        setError(result.error || 'Failed to fetch permissions')
      }
    } catch (err) {
      setError('An unexpected error occurred while fetching permissions')
    } finally {
      setPermissionsLoading(false)
    }
  }, [serverId])

  const fetchUserPermissions = useCallback(async () => {
    try {
      const result = await getUserPermissions(userId, serverId)
      if (result.success && result.data) {
        const permissionNames = result.data.map(p => p.name).filter((name): name is string => name !== null)
        setUserPermissions(permissionNames)
        setSelectedPermissions(permissionNames)
      } else {
        setError(result.error || 'Failed to fetch user permissions')
      }
    } catch (err) {
      setError('An unexpected error occurred while fetching user permissions')
    }
  }, [userId, serverId])

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      // In a real implementation, we would fetch the user and their current permissions
      // For now, we'll simulate this with mock data
      const mockUser: User = {
        id: userId,
        name: 'John Doe',
        email: '<EMAIL>',
        profile: {
          nwaEmail: '<EMAIL>'
        }
      }
      
      setUser(mockUser)
      
      // Fetch permissions from remote server
      await fetchPermissions()
      
      // Fetch user's current permissions
      await fetchUserPermissions()
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }, [fetchPermissions, fetchUserPermissions, userId])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  const handleSavePermissions = async () => {
    try {
      setSaving(true)
      setError(null)
      
      // Determine which permissions to add and which to remove
      const permissionsToAdd = selectedPermissions.filter(p => !userPermissions.includes(p))
      const permissionsToRemove = userPermissions.filter(p => !selectedPermissions.includes(p))
      
      // Add new permissions
      if (permissionsToAdd.length > 0) {
        const result = await assignUserPermissions(userId, serverId, permissionsToAdd)
        if (!result.success) {
          throw new Error(result.error || 'Failed to assign permissions')
        }
      }
      
      // Remove old permissions
      if (permissionsToRemove.length > 0) {
        const result = await removeUserPermissions(userId, serverId, permissionsToRemove)
        if (!result.success) {
          throw new Error(result.error || 'Failed to remove permissions')
        }
      }
      
      // Update local state
      setUserPermissions(selectedPermissions)
      
      alert('Permissions saved successfully')
    } catch (err) {
      setError('An unexpected error occurred while saving permissions')
    } finally {
      setSaving(false)
    }
  }

  const handlePermissionChange = (permissionName: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions(prev => [...prev, permissionName])
    } else {
      setSelectedPermissions(prev => prev.filter(p => p !== permissionName))
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading user permissions...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="w-5 h-5 mr-2 text-blue-500" />
            User Permissions
          </CardTitle>
          <p className="text-sm text-gray-500">
            Manage permissions for user {user?.name || user?.email || userId} on this remote server.
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div>
              <h3 className="text-lg font-medium">Available Permissions</h3>
              <p className="text-sm text-gray-500">
                Select the permissions to grant to this user
              </p>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={fetchPermissions}
              disabled={permissionsLoading}
            >
              <RefreshCw className={`w-4 h-4 mr-1 ${permissionsLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <h4 className="font-medium mb-3">Individual Permissions</h4>
              <div className="space-y-2 max-h-64 overflow-y-auto p-2 border border-gray-200 dark:border-gray-700 rounded">
                {permissions.map((permission) => (
                  <div key={permission.name} className="flex items-center space-x-2 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded">
                    <Checkbox
                      id={`permission-${permission.name}`}
                      checked={selectedPermissions.includes(permission.name)}
                      onCheckedChange={(checked) => handlePermissionChange(permission.name, !!checked)}
                    />
                    <Label 
                      htmlFor={`permission-${permission.name}`} 
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      <span className="font-mono text-xs bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded mr-2">
                        {permission.name}
                      </span>
                      <span className="text-gray-500">{permission.description}</span>
                    </Label>
                  </div>
                ))}
                {permissions.length === 0 && (
                  <p className="text-gray-500 text-sm py-4 text-center">
                    No permissions available. Click &quot;Refresh&quot; to fetch permissions.
                  </p>
                )}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-3">Predefined Roles</h4>
              <div className="space-y-3">
                {roles.map((role) => (
                  <div 
                    key={role.name} 
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
                    onClick={() => {
                      // Select all permissions in this role
                      const rolePermissions = role.permissions
                      setSelectedPermissions(prev => {
                        const newPermissions = [...prev]
                        rolePermissions.forEach(rp => {
                          if (!newPermissions.includes(rp)) {
                            newPermissions.push(rp)
                          }
                        })
                        return newPermissions
                      })
                    }}
                  >
                    <div className="font-medium">{role.name}</div>
                    <div className="text-sm text-gray-500">{role.description}</div>
                    <div className="text-xs mt-1">
                      <span className="text-gray-500">Includes:</span>
                      <span className="ml-1 text-gray-600 dark:text-gray-400">
                        {role.permissions.join(', ')}
                      </span>
                    </div>
                  </div>
                ))}
                {roles.length === 0 && (
                  <p className="text-gray-500 text-sm py-4 text-center">
                    No roles available. Click &quot;Refresh&quot; to fetch roles.
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button 
              onClick={handleSavePermissions}
              disabled={saving}
            >
              <Save className="w-4 h-4 mr-2" />
              {saving ? 'Saving...' : 'Save Permissions'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}