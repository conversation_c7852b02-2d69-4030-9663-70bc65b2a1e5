import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Server, ChevronDown, ChevronRight, Trash2, Eye, EyeOff } from 'lucide-react'
import { PermissionInheritanceViewer } from '@/components/permissions/PermissionInheritanceViewer'
import { removeServerAssignment, formatDate } from './remote-server-assignment-api'
import { isUserAssignment } from './remote-server-assignment-types'
import type { Assignment } from './remote-server-assignment-types'

interface RemoteServerAssignmentListProps {
  entityType: 'user' | 'role'
  entityId: string
  assignments: Assignment[]
  onRefresh: () => void
}

export function RemoteServerAssignmentList({
  entityType,
  entityId,
  assignments,
  onRefresh
}: RemoteServerAssignmentListProps) {
  const [expandedAssignments, setExpandedAssignments] = useState<Set<string>>(new Set())
  const [showPermissionViewer, setShowPermissionViewer] = useState<string | null>(null)

  const toggleAssignmentExpanded = (assignmentId: string) => {
    setExpandedAssignments(prev => {
      const newSet = new Set(prev)
      newSet.has(assignmentId) ? newSet.delete(assignmentId) : newSet.add(assignmentId)
      return newSet
    })
  }

  const handleRemoveAssignment = async (serverId: string) => {
    const assignment = assignments.find(a => a.serverId === serverId)
    const serverName = assignment?.server?.name || 'Unknown Server'
    if (!confirm(`Remove access to "${serverName}"?`)) return

    try {
      await removeServerAssignment(entityType, entityId, serverId)
      onRefresh()
    } catch (error) {
      console.error('Error removing assignment:', error)
      // Error handling should be done by parent component
    }
  }

  if (assignments.length === 0) {
    return (
      <div className="text-center py-12 border-2 border-dashed rounded-lg">
        <Server className="h-12 w-12 mx-auto mb-4 text-slate-300" />
        <h4 className="font-medium">No servers assigned</h4>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      {assignments.map(assignment => {
        const isExpanded = expandedAssignments.has(assignment.id)
        return (
          <div key={assignment.id} className="border rounded-lg">
            <div
              className="p-4 cursor-pointer flex justify-between"
              onClick={() => toggleAssignmentExpanded(assignment.id)}
            >
              <div className="flex gap-2">
                {isExpanded ? <ChevronDown /> : <ChevronRight />}
                <Server className="text-blue-500" />
                <span>{assignment.server.name}</span>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowPermissionViewer(showPermissionViewer === assignment.id ? null : assignment.id)
                  }}
                >
                  {showPermissionViewer === assignment.id ? <EyeOff /> : <Eye />}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleRemoveAssignment(assignment.serverId)
                  }}
                >
                  <Trash2 />
                </Button>
              </div>
            </div>

            {isExpanded && (
              <div className="p-4 border-t">
                <div className="text-sm">
                  {isUserAssignment(assignment) && (
                    <>
                      Granted: {formatDate(assignment.grantedAt)} <br />
                      {assignment.notes && <>Notes: {assignment.notes}</>}
                    </>
                  )}
                  {!isUserAssignment(assignment) && (
                    <>Created: {formatDate(assignment.createdAt)}</>
                  )}
                </div>
              </div>
            )}

            {showPermissionViewer === assignment.id && isUserAssignment(assignment) && (
              <div className="p-6 border-t">
                <PermissionInheritanceViewer
                  userId={entityId}
                  serverId={assignment.serverId}
                  serverName={assignment.server.name}
                  onRefresh={onRefresh}
                />
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}