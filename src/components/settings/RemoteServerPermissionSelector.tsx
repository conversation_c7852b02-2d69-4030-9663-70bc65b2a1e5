import React from 'react'
import { Label } from '@/components/ui/label'
import type { ServerPermission } from './remote-server-assignment-types'

interface PermissionSelectorProps {
  permissions: ServerPermission[]
  selectedPermissions: string[]
  onPermissionsChange: (permissions: string[]) => void
  entityType: 'user' | 'role'
}

export function PermissionSelector({
  permissions,
  selectedPermissions,
  onPermissionsChange,
  entityType
}: PermissionSelectorProps) {
  const handlePermissionToggle = (permissionName: string, checked: boolean) => {
    if (checked) {
      onPermissionsChange([...selectedPermissions, permissionName])
    } else {
      onPermissionsChange(selectedPermissions.filter(p => p !== permissionName))
    }
  }

  if (permissions.length === 0) {
    return (
      <div>
        <Label>{entityType === 'user' ? 'Permissions' : 'Auto-Grant Permissions'}</Label>
        <p className="text-sm text-slate-500">No permissions available</p>
      </div>
    )
  }

  return (
    <div>
      <Label>{entityType === 'user' ? 'Permissions' : 'Auto-Grant Permissions'}</Label>
      <div className="space-y-2 mt-2">
        {permissions.map(permission => (
          <label key={permission.permission_name} className="flex gap-2">
            <input
              type="checkbox"
              checked={selectedPermissions.includes(permission.permission_name)}
              onChange={(e) => handlePermissionToggle(permission.permission_name, e.target.checked)}
            />
            {permission.permission_name}
          </label>
        ))}
      </div>
    </div>
  )
}