'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import AssignUserRoles from './AssignUserRoles'
import AssignUserPermissions from './AssignUserPermissions'
import {
  Users,
  Shield,
  Search,
  User,
  Settings,
  Key,
  Plus,
  Trash2,
  CheckCircle,
  XCircle,
  RefreshCw,
  Eye,
  EyeOff,
  Crown,
  Loader2,
  ChevronDown,
  ChevronRight,
  UserCheck,
  AlertCircle,
  ArrowRight,
  FileText,
  Building2,
  UserCog,
  Lock
} from 'lucide-react'

// TypeScript interfaces
interface User {
  id: string
  name: string | null
  email: string | null
  profile?: {
    firstName: string | null
    lastName: string | null
  } | null
}

interface Role {
  id: string
  name: string
  description: string | null
  permissions: string[]
}

interface UserPermissions {
  userId: string
  user: {
    name: string | null
    email: string | null
    profile?: {
      firstName: string | null
      lastName: string | null
    } | null
  }
  roles: RoleWithDetails[]
  directPermissions: string[]
  allPermissions: string[]
}

interface PermissionCategoryGroup {
  id: string
  name: string
  icon: React.ReactNode
  permissions: Permission[]
}

interface Permission {
  id: string
  name: string
  resource: string
  action: string
  description: string | null
  createdAt: string
}

interface RoleWithDetails {
  id: string
  name: string
  description: string | null
  isSystem: boolean
  userCount: number
  permissionCount: number
  createdAt: string
  updatedAt: string
}

interface PermissionCategory {
  id: string
  name: string
  icon: React.ReactNode
  permissions: Permission[]
}

interface UserPermissionAssignment {
  id: string
  userId: string
  permissionId: string
  permission: Permission
  isAssigned: boolean
  assignedAt?: Date
  source: 'role' | 'individual' // Whether it comes from a role or individual assignment
}

export default function PermissionManager() {
  // Authentication
  const { data: session, status } = useSession()

  // State management
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Data loading
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Load users
      const usersResponse = await fetch('/api/users')
      if (usersResponse.ok) {
        const usersData = await usersResponse.json()
        setUsers(usersData.users || [])
      } else {
        throw new Error('Failed to load users')
      }

    } catch (error) {
      console.error('Error loading data:', error)
      setError('Failed to load permission data')
    } finally {
      setLoading(false)
    }
  }

  // Authentication check
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2 text-slate-600">
          <Loader2 className="h-4 w-4 animate-spin" />
          Checking authentication...
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <h1 className="text-xl font-semibold text-slate-900 mb-2">Not authenticated</h1>
          <p className="text-slate-600">Please log in to manage permissions.</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2 text-slate-600">
          <Loader2 className="h-4 w-4 animate-spin" />
          Loading permission data...
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription className="flex items-center justify-between">
            <span>{error}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setError(null)
                loadData()
              }}
              className="ml-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="roles" className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-emerald-600 border-0 p-1 h-auto rounded-md">
          <TabsTrigger value="roles" className="flex items-center gap-2 bg-transparent border-0 text-white data-[state=active]:bg-emerald-700 data-[state=active]:text-white hover:bg-emerald-700/80">
            <Crown className="h-4 w-4 text-white" />
            Assign Roles
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2 bg-transparent border-0 text-white data-[state=active]:bg-emerald-700 data-[state=active]:text-white hover:bg-emerald-700/80">
            <Key className="h-4 w-4 text-white" />
            Assign Permissions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="mt-6">
          <AssignUserRoles />
        </TabsContent>

        <TabsContent value="permissions" className="mt-6">
          <AssignUserPermissions />
        </TabsContent>
      </Tabs>
    </div>
  )
}