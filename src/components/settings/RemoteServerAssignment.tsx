'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Plus } from 'lucide-react'
import { AddServerForm } from './RemoteServerAssignmentForm'
import { RemoteServerAssignmentList } from './RemoteServerAssignmentList'
import { loadAssignments, loadAvailableServers } from './remote-server-assignment-api'
import type { RemoteServerAssignmentProps, Assignment, RemoteServer } from './remote-server-assignment-types'

export function RemoteServerAssignment({ entityType, entityId }: RemoteServerAssignmentProps) {
  const [assignments, setAssignments] = useState<Assignment[]>([])
  const [availableServers, setAvailableServers] = useState<RemoteServer[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)

  useEffect(() => {
    loadData()
  }, [entityType, entityId])

  const loadData = async () => {
    try {
      setLoading(true)
      setError(null)

      const [assignmentsData, serversData] = await Promise.all([
        loadAssignments(entityType, entityId),
        loadAvailableServers()
      ])

      setAssignments(assignmentsData)
      setAvailableServers(serversData)
    } catch (err) {
      console.error('Error loading data:', err)
      setError('Failed to load server assignments')
    } finally {
      setLoading(false)
    }
  }

  const getAvailableServersForAssignment = () => {
    const assignedServerIds = new Set(assignments.map(a => a.serverId))
    return availableServers.filter(server => !assignedServerIds.has(server.id) && server.isActive)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-slate-600">Loading server assignments...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex gap-2">
          <Button
            onClick={() => setShowAddForm(true)}
            disabled={getAvailableServersForAssignment().length === 0}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Assign Server
          </Button>
        </div>
        {assignments.length > 0 && (
          <div className="text-sm text-slate-500">{assignments.length} server(s) assigned</div>
        )}
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Add Server Form */}
      {showAddForm && (
        <AddServerForm
          entityType={entityType}
          entityId={entityId}
          availableServers={availableServers}
          onSuccess={() => {
            loadData()
            setShowAddForm(false)
          }}
          onCancel={() => setShowAddForm(false)}
        />
      )}

      {/* Current Assignments */}
      <RemoteServerAssignmentList
        entityType={entityType}
        entityId={entityId}
        assignments={assignments}
        onRefresh={loadData}
      />
    </div>
  )
}
