import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Plus, X } from 'lucide-react'
import { PermissionSelector } from './RemoteServerPermissionSelector'
import { loadServerPermissions, autoFetchServerPermissions, assignServer } from './remote-server-assignment-api'
import type { RemoteServer, ServerPermission } from './remote-server-assignment-types'

interface AddServerFormProps {
  entityType: 'user' | 'role'
  entityId: string
  availableServers: RemoteServer[]
  onSuccess: () => void
  onCancel: () => void
}

export function AddServerForm({
  entityType,
  entityId,
  availableServers,
  onSuccess,
  onCancel
}: AddServerFormProps) {
  const [selectedServerId, setSelectedServerId] = useState('')
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([])
  const [notes, setNotes] = useState('')
  const [serverPermissions, setServerPermissions] = useState<Record<string, ServerPermission[]>>({})
  const [loading, setLoading] = useState(false)

  const handleServerChange = async (serverId: string) => {
    setSelectedServerId(serverId)
    if (serverId) {
      await loadServerPermissions(serverId)
      if ((serverPermissions[serverId] || []).length === 0) {
        await autoFetchServerPermissions(serverId, availableServers)
      }
    }
  }

  const handleSubmit = async () => {
    if (!selectedServerId) return

    setLoading(true)
    try {
      await assignServer(entityType, entityId, selectedServerId, selectedPermissions, notes)
      onSuccess()
    } catch (error) {
      console.error('Error assigning server:', error)
      // Error handling should be done by parent component
    } finally {
      setLoading(false)
    }
  }

  const getAvailableServersForAssignment = () => {
    return availableServers.filter(server => server.isActive)
  }

  return (
    <div className="border rounded-lg p-6 bg-white dark:bg-slate-800 shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Plus className="h-5 w-5 text-blue-500" />
          Assign Remote Server
        </h3>
        <Button variant="ghost" size="sm" onClick={onCancel}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="space-y-6">
        <div>
          <Label htmlFor="server-select">Select Server</Label>
          <select
            id="server-select"
            value={selectedServerId}
            onChange={(e) => handleServerChange(e.target.value)}
            className="w-full mt-2 border rounded-md"
          >
            <option value="">Choose a remote server...</option>
            {getAvailableServersForAssignment().map(server => (
              <option key={server.id} value={server.id}>
                {server.name} ({server.url})
              </option>
            ))}
          </select>
        </div>

        {selectedServerId && serverPermissions[selectedServerId] && (
          <PermissionSelector
            permissions={serverPermissions[selectedServerId]}
            selectedPermissions={selectedPermissions}
            onPermissionsChange={setSelectedPermissions}
            entityType={entityType}
          />
        )}

        {entityType === 'user' && (
          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea id="notes" value={notes} onChange={(e) => setNotes(e.target.value)} />
          </div>
        )}

        <div className="flex gap-3 pt-4">
          <Button onClick={handleSubmit} disabled={!selectedServerId || loading}>
            <Plus className="h-4 w-4" /> Assign Server
          </Button>
          <Button variant="outline" onClick={onCancel} disabled={loading}>
            Cancel
          </Button>
        </div>
      </div>
    </div>
  )
}