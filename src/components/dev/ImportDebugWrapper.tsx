'use client';

import React from 'react';
import { hasUnusedImports, getHighlightClasses } from '@/lib/dev-utils/unused-import-highlighter';

interface ImportDebugWrapperProps {
  filePath: string;
  children: React.ReactNode;
  className?: string;
}

/**
 * Development wrapper to visually highlight components with unused imports
 * Usage:
 * <ImportDebugWrapper filePath="/src/components/MyComponent.tsx">
 *   <MyComponent />
 * </ImportDebugWrapper>
 */
export const ImportDebugWrapper: React.FC<ImportDebugWrapperProps> = ({
  filePath,
  children,
  className = '',
}) => {
  const hasUnused = hasUnusedImports(filePath);
  const highlightClasses = getHighlightClasses(hasUnused);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return <>{children}</>;
  }

  return (
    <div
      className={`relative ${className} ${highlightClasses}`}
      title={hasUnused ? `⚠️ This file has unused imports - ${filePath}` : undefined}
    >
      {hasUnused && (
        <div className="absolute -top-2 -right-2 z-50">
          <span className="inline-flex items-center justify-center w-6 h-6 bg-orange-500 text-white text-xs font-bold rounded-full animate-pulse">
            !
          </span>
        </div>
      )}
      {children}

      {hasUnused && (
        <div className="absolute bottom-0 left-0 right-0 bg-orange-500/10 text-orange-600 text-xs p-1 text-center">
          Unused imports detected
        </div>
      )}
    </div>
  );
};

// HOC for easier wrapping
export const withImportDebug = <P extends object>(
  Component: React.ComponentType<P>,
  filePath: string
) => {
  return (props: P) => (
    <ImportDebugWrapper filePath={filePath}>
      <Component {...props} />
    </ImportDebugWrapper>
  );
};