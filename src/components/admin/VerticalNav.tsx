'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { usePermissions } from '@/lib/contexts/PermissionContext';
import { Shield, Users, Settings, FileText, Database, Key } from 'lucide-react';

interface NavItem {
  label: string;
  href: string;
  requiredPermissions?: string[];
  icon?: React.ReactNode;
  description?: string;
}

interface NavCategory {
  category: string;
  icon: React.ReactNode;
  items: NavItem[];
}

export const VerticalNav: React.FC = () => {
  const pathname = usePathname();
  const { hasAnyPermission, hasPermission, loading } = usePermissions();

  const navItems: NavCategory[] = [
    {
      category: 'User Management',
      icon: <Users className="h-4 w-4" />,
      items: [
        {
          label: 'Manage Users',
          href: '/admin/manage/users',
          requiredPermissions: ['users.manage', 'admin.users'],
          icon: <Users className="h-4 w-4" />,
          description: 'View and manage user accounts'
        },
        {
          label: 'Update User',
          href: '/admin/manage/updateuser',
          requiredPermissions: ['users.update', 'admin.users'],
          icon: <Users className="h-4 w-4" />,
          description: 'Update user information'
        },
        {
          label: 'Bulk Upload',
          href: '/admin/manage/users/bulk',
          requiredPermissions: ['users.bulk_upload', 'admin.users'],
          icon: <FileText className="h-4 w-4" />,
          description: 'Bulk upload user data'
        },
        {
          label: 'Search Users',
          href: '/admin/manage/searchuser',
          requiredPermissions: ['users.search', 'admin.users'],
          icon: <Users className="h-4 w-4" />,
          description: 'Search and filter users'
        },
      ]
    },
    {
      category: 'System Administration',
      icon: <Settings className="h-4 w-4" />,
      items: [
        {
          label: 'System Settings',
          href: '/admin/system/settings',
          requiredPermissions: ['system.settings', 'admin.system'],
          icon: <Settings className="h-4 w-4" />,
          description: 'Configure system settings'
        },
        {
          label: 'Database Management',
          href: '/admin/system/database',
          requiredPermissions: ['system.database', 'admin.system'],
          icon: <Database className="h-4 w-4" />,
          description: 'Database operations and maintenance'
        },
        {
          label: 'Permission Management',
          href: '/admin/system/permissions',
          requiredPermissions: ['permissions.manage', 'admin.permissions'],
          icon: <Key className="h-4 w-4" />,
          description: 'Manage user permissions and roles'
        },
      ]
    },
    {
      category: 'Security & Audit',
      icon: <Shield className="h-4 w-4" />,
      items: [
        {
          label: 'Security Audit',
          href: '/admin/security/audit',
          requiredPermissions: ['security.audit', 'admin.security'],
          icon: <Shield className="h-4 w-4" />,
          description: 'View security audit logs'
        },
        {
          label: 'Access Control',
          href: '/admin/security/access',
          requiredPermissions: ['security.access_control', 'admin.security'],
          icon: <Key className="h-4 w-4" />,
          description: 'Manage access control policies'
        },
      ]
    }
  ];

  // Filter navigation items based on permissions
  const filterNavItems = (items: NavItem[]): NavItem[] => {
    return items.filter(item => {
      if (!item.requiredPermissions) return true; // Show if no permissions required
      // Convert string array to object array format expected by hasAnyPermission
      const permissionObjects = item.requiredPermissions.map(perm => {
        const [resource, action] = perm.split('.');
        return { resource, action };
      });
      return hasAnyPermission(permissionObjects);
    });
  };

  const filteredNavItems = navItems.map(category => ({
    ...category,
    items: filterNavItems(category.items)
  })).filter(category => category.items.length > 0); // Only show categories with visible items

  if (loading) {
    return (
      <nav className="p-4">
        <div className="animate-pulse space-y-6">
          <div className="h-4 bg-slate-700 rounded w-32"></div>
          <div className="space-y-2">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-8 bg-slate-700 rounded"></div>
            ))}
          </div>
        </div>
      </nav>
    );
  }

  return (
    <nav className="p-4">
      <div className="space-y-6">
        {filteredNavItems.map((section) => (
          <div key={section.category}>
            <h3 className="text-sm font-semibold text-slate-300 uppercase tracking-wider mb-3 flex items-center gap-2">
              {section.icon}
              {section.category}
            </h3>
            <ul className="space-y-1">
              {section.items.map((item) => {
                const isActive = pathname === item.href;
                return (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className={`group block px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                        isActive
                          ? 'bg-slate-700 text-white border-l-2 border-blue-400 shadow-sm'
                          : 'text-slate-300 hover:bg-slate-700 hover:text-white hover:translate-x-1'
                      }`}
                      title={item.description}
                    >
                      <div className="flex items-center gap-3">
                        {item.icon && (
                          <span className={`transition-colors duration-200 ${
                            isActive ? 'text-blue-400' : 'text-slate-400 group-hover:text-slate-300'
                          }`}>
                            {item.icon}
                          </span>
                        )}
                        <span className="flex-1">{item.label}</span>
                      </div>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>
        ))}

        {filteredNavItems.length === 0 && (
          <div className="text-center py-8">
            <Shield className="h-8 w-8 mx-auto mb-3 text-slate-500" />
            <p className="text-sm text-slate-400">No accessible navigation items</p>
            <p className="text-xs text-slate-500 mt-1">You may not have the required permissions</p>
          </div>
        )}
      </div>
    </nav>
  );
};