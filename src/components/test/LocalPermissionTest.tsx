'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { LocalPermissionManager } from '@/components/settings/LocalPermissionManager'
import { 
  TestTube, 
  Key, 
  CheckCircle, 
  XCircle, 
  Users,
  Crown,
  Briefcase,
  RefreshCw,
  Database
} from 'lucide-react'

export function LocalPermissionTest() {
  const [testResults, setTestResults] = useState<any[]>([])
  const [testing, setTesting] = useState(false)
  const [showFullManager, setShowFullManager] = useState(false)

  const runLocalPermissionTests = async () => {
    setTesting(true)
    setTestResults([])
    
    const results: any[] = []

    try {
      // Test 1: Load local permissions
      const permissionsResponse = await fetch('/api/local-permissions')
      if (permissionsResponse.ok) {
        const data = await permissionsResponse.json()
        results.push({
          name: 'Load Local Permissions',
          status: 'success',
          message: `Loaded ${data.data?.length || 0} permission definitions`,
          data: data.data
        })
      } else {
        throw new Error('Failed to load local permissions')
      }

      // Test 2: Load local roles
      const rolesResponse = await fetch('/api/local-roles')
      if (rolesResponse.ok) {
        const data = await rolesResponse.json()
        results.push({
          name: 'Load Local Roles',
          status: 'success',
          message: `Loaded ${data.data?.length || 0} roles`,
          data: data.data
        })
      } else {
        throw new Error('Failed to load local roles')
      }

      // Test 3: Load local projects
      const projectsResponse = await fetch('/api/local-projects')
      if (projectsResponse.ok) {
        const data = await projectsResponse.json()
        results.push({
          name: 'Load Local Projects',
          status: 'success',
          message: `Loaded ${data.data?.length || 0} projects`,
          data: data.data
        })
      } else {
        throw new Error('Failed to load local projects')
      }

      // Test 4: Test user permissions API
      const usersResponse = await fetch('/api/users')
      if (usersResponse.ok) {
        const usersData = await usersResponse.json()
        if (usersData.users && usersData.users.length > 0) {
          const firstUserId = usersData.users[0].id
          const userPermissionsResponse = await fetch(`/api/users/${firstUserId}/local-permissions`)
          
          if (userPermissionsResponse.ok) {
            const userPermissionsData = await userPermissionsResponse.json()
            results.push({
              name: 'User Local Permissions API',
              status: 'success',
              message: `User has ${userPermissionsData.data?.roles?.length || 0} roles and ${userPermissionsData.data?.allPermissions?.length || 0} permissions`,
              data: userPermissionsData.data
            })
          } else {
            throw new Error('User permissions API failed')
          }
        } else {
          results.push({
            name: 'User Local Permissions API',
            status: 'warning',
            message: 'No users available for testing',
            data: null
          })
        }
      }

      // Test 5: Test sync permissions
      const syncResponse = await fetch('/api/local-permissions', {
        method: 'POST'
      })

      if (syncResponse.ok) {
        const syncData = await syncResponse.json()
        results.push({
          name: 'Sync Core Permissions',
          status: 'success',
          message: syncData.message || 'Permissions synced successfully',
          data: syncData.data
        })
      } else {
        const syncError = await syncResponse.json()
        results.push({
          name: 'Sync Core Permissions',
          status: 'error',
          message: syncError.error || 'Sync failed',
          data: syncError
        })
      }

    } catch (error) {
      results.push({
        name: 'Local Permission Tests',
        status: 'error',
        message: `Test failed: ${error}`,
        data: null
      })
    }

    setTestResults(results)
    setTesting(false)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning':
        return <TestTube className="h-5 w-5 text-yellow-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <TestTube className="h-5 w-5 text-blue-500" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Key className="h-6 w-6" />
        <div>
          <h2 className="text-xl font-semibold">Local Permission System Test</h2>
          <p className="text-sm text-slate-600">
            Test the NWAPromote local permission management system.
          </p>
        </div>
      </div>

      {/* Test Controls */}
      <div className="flex gap-3">
        <Button onClick={runLocalPermissionTests} disabled={testing}>
          {testing ? 'Running Tests...' : 'Run Local Permission Tests'}
        </Button>
        <Button 
          variant="outline" 
          onClick={() => setShowFullManager(!showFullManager)}
        >
          {showFullManager ? 'Hide' : 'Show'} Full Manager
        </Button>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="space-y-3">
          <h3 className="font-medium">Test Results</h3>
          <div className="space-y-2">
            {testResults.map((result, index) => (
              <div 
                key={index}
                className={`flex items-start gap-3 p-3 rounded-lg border ${
                  result.status === 'success' 
                    ? 'border-green-200 bg-green-50' 
                    : result.status === 'warning'
                    ? 'border-yellow-200 bg-yellow-50'
                    : 'border-red-200 bg-red-50'
                }`}
              >
                {getStatusIcon(result.status)}
                <div className="flex-1">
                  <div className="font-medium">{result.name}</div>
                  <div className="text-sm text-slate-600">{result.message}</div>
                  {result.data && (
                    <details className="mt-2">
                      <summary className="text-xs text-slate-500 cursor-pointer">Show data</summary>
                      <pre className="text-xs bg-white p-2 rounded border mt-1 overflow-auto max-h-40">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          {/* Summary */}
          <Alert>
            <AlertDescription>
              Tests completed: {testResults.filter(r => r.status === 'success').length} passed, {testResults.filter(r => r.status === 'error').length} failed
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* Feature Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <Crown className="h-4 w-4 text-blue-500" />
            Role Management
          </h4>
          <p className="text-sm text-slate-600 mb-3">
            Assign local NWAPromote roles (peace, member, moderator, admin) to users.
          </p>
          <ul className="text-xs text-slate-500 space-y-1">
            <li>• View user's current roles</li>
            <li>• Assign/remove roles</li>
            <li>• Role permission inheritance</li>
          </ul>
        </div>

        <div className="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <Briefcase className="h-4 w-4 text-purple-500" />
            Project Permissions
          </h4>
          <p className="text-sm text-slate-600 mb-3">
            Assign project-specific permissions and scopes within NWAPromote.
          </p>
          <ul className="text-xs text-slate-500 space-y-1">
            <li>• Project scope assignment</li>
            <li>• Granular project access</li>
            <li>• Multi-project support</li>
          </ul>
        </div>

        <div className="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <Database className="h-4 w-4 text-green-500" />
            Permission Sync
          </h4>
          <p className="text-sm text-slate-600 mb-3">
            Sync core NWAPromote permissions to database (since server isn't running).
          </p>
          <ul className="text-xs text-slate-500 space-y-1">
            <li>• Manual permission sync</li>
            <li>• Core permission definitions</li>
            <li>• Database population</li>
          </ul>
        </div>
      </div>

      {/* Full Manager */}
      {showFullManager && (
        <div className="border-t pt-6">
          <h3 className="font-medium mb-4 flex items-center gap-2">
            <Key className="h-5 w-5" />
            Full Local Permission Manager
          </h3>
          <LocalPermissionManager />
        </div>
      )}
    </div>
  )
}