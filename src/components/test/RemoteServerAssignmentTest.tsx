'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ServerAssignmentManager } from '@/components/settings/ServerAssignmentManager'
import { RemoteServerAssignment } from '@/components/settings/RemoteServerAssignment'
import { CreateTestRemoteServer } from './CreateTestRemoteServer'
import { PermissionInheritanceTest } from './PermissionInheritanceTest'
import { 
  TestTube, 
  CheckCircle, 
  XCircle, 
  User,
  Shield,
  Server
} from 'lucide-react'

interface TestResult {
  name: string
  status: 'success' | 'error' | 'pending'
  message: string
}

export function RemoteServerAssignmentTest() {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [testing, setTesting] = useState(false)
  const [showFullManager, setShowFullManager] = useState(false)

  const runTests = async () => {
    setTesting(true)
    setTestResults([])
    
    const results: TestResult[] = []

    // Test 1: Load users
    try {
      const response = await fetch('/api/users')
      if (response.ok) {
        const data = await response.json()
        results.push({
          name: 'Load Users',
          status: 'success',
          message: `Loaded ${data.users?.length || 0} users`
        })
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      results.push({
        name: 'Load Users',
        status: 'error',
        message: `Failed to load users: ${error}`
      })
    }

    // Test 2: Load roles
    try {
      const response = await fetch('/api/admin/roles')
      if (response.ok) {
        const data = await response.json()
        results.push({
          name: 'Load Roles',
          status: 'success',
          message: `Loaded ${data.roles?.length || data.data?.length || 0} roles`
        })
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      results.push({
        name: 'Load Roles',
        status: 'error',
        message: `Failed to load roles: ${error}`
      })
    }

    // Test 3: Load remote servers
    try {
      const response = await fetch('/api/remote-servers')
      if (response.ok) {
        const data = await response.json()
        results.push({
          name: 'Load Remote Servers',
          status: 'success',
          message: `Loaded ${data.remoteServers?.length || 0} remote servers`
        })
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      results.push({
        name: 'Load Remote Servers',
        status: 'error',
        message: `Failed to load remote servers: ${error}`
      })
    }

    // Test 4: Test user remote server API endpoint
    try {
      // Try to get remote servers for the first user (assuming test user exists)
      const usersResponse = await fetch('/api/users')
      if (usersResponse.ok) {
        const usersData = await usersResponse.json()
        if (usersData.users && usersData.users.length > 0) {
          const firstUserId = usersData.users[0].id
          const userServersResponse = await fetch(`/api/users/${firstUserId}/remote-servers`)
          
          if (userServersResponse.ok) {
            const userServersData = await userServersResponse.json()
            results.push({
              name: 'User Remote Servers API',
              status: 'success',
              message: `API working - user has ${userServersData.data?.length || 0} server assignments`
            })
          } else {
            throw new Error(`HTTP ${userServersResponse.status}`)
          }
        } else {
          results.push({
            name: 'User Remote Servers API',
            status: 'error',
            message: 'No users available for testing'
          })
        }
      }
    } catch (error) {
      results.push({
        name: 'User Remote Servers API',
        status: 'error',
        message: `API test failed: ${error}`
      })
    }

    // Test 5: Test role remote server API endpoint
    try {
      const rolesResponse = await fetch('/api/admin/roles')
      if (rolesResponse.ok) {
        const rolesData = await rolesResponse.json()
        if (rolesData.roles && rolesData.roles.length > 0) {
          const firstRoleId = rolesData.roles[0].id
          const roleServersResponse = await fetch(`/api/roles/${firstRoleId}/remote-servers`)
          
          if (roleServersResponse.ok) {
            const roleServersData = await roleServersResponse.json()
            results.push({
              name: 'Role Remote Servers API',
              status: 'success',
              message: `API working - role has ${roleServersData.data?.length || 0} server assignments`
            })
          } else {
            throw new Error(`HTTP ${roleServersResponse.status}`)
          }
        } else {
          results.push({
            name: 'Role Remote Servers API',
            status: 'error',
            message: 'No roles available for testing'
          })
        }
      }
    } catch (error) {
      results.push({
        name: 'Role Remote Servers API',
        status: 'error',
        message: `API test failed: ${error}`
      })
    }

    setTestResults(results)
    setTesting(false)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <TestTube className="h-5 w-5 text-blue-500" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <TestTube className="h-6 w-6" />
        <div>
          <h2 className="text-xl font-semibold">Remote Server Assignment Test</h2>
          <p className="text-sm text-slate-600">
            Test the remote server assignment system functionality.
          </p>
        </div>
      </div>

      {/* Test Controls */}
      <div className="flex gap-3">
        <Button onClick={runTests} disabled={testing}>
          {testing ? 'Running Tests...' : 'Run Tests'}
        </Button>
        <Button 
          variant="outline" 
          onClick={() => setShowFullManager(!showFullManager)}
        >
          {showFullManager ? 'Hide' : 'Show'} Full Manager
        </Button>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="space-y-3">
          <h3 className="font-medium">Test Results</h3>
          <div className="space-y-2">
            {testResults.map((result, index) => (
              <div 
                key={index}
                className={`flex items-center gap-3 p-3 rounded-lg border ${
                  result.status === 'success' 
                    ? 'border-green-200 bg-green-50' 
                    : 'border-red-200 bg-red-50'
                }`}
              >
                {getStatusIcon(result.status)}
                <div>
                  <div className="font-medium">{result.name}</div>
                  <div className="text-sm text-slate-600">{result.message}</div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Summary */}
          <Alert>
            <AlertDescription>
              Tests completed: {testResults.filter(r => r.status === 'success').length} passed, {testResults.filter(r => r.status === 'error').length} failed
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* Create Test Servers */}
      <div className="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
        <CreateTestRemoteServer />
      </div>

      {/* Quick Test Components */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <User className="h-4 w-4" />
            Test User Assignment (First User)
          </h4>
          <div className="text-sm text-slate-600 mb-3">
            This will show remote server assignments for the first user in the system.
          </div>
          {testResults.length > 0 && testResults[0].status === 'success' && (
            <RemoteServerAssignment 
              entityType="user" 
              entityId="test-user-id" 
              entityName="Test User"
            />
          )}
        </div>

        <div className="border border-slate-200 dark:border-slate-700 rounded-lg p-4">
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Test Role Assignment (First Role)
          </h4>
          <div className="text-sm text-slate-600 mb-3">
            This will show remote server assignments for the first role in the system.
          </div>
          {testResults.length > 0 && testResults[1].status === 'success' && (
            <RemoteServerAssignment 
              entityType="role" 
              entityId="test-role-id" 
              entityName="Test Role"
            />
          )}
        </div>
      </div>

      {/* Permission Inheritance Test */}
      <div className="border-t pt-6">
        <PermissionInheritanceTest />
      </div>

      {/* Full Manager */}
      {showFullManager && (
        <div className="border-t pt-6">
          <h3 className="font-medium mb-4 flex items-center gap-2">
            <Server className="h-5 w-5" />
            Full Server Assignment Manager
          </h3>
          <ServerAssignmentManager />
        </div>
      )}
    </div>
  )
}