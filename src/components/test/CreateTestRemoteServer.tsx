'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Plus, Server, CheckCircle, XCircle } from 'lucide-react'

export function CreateTestRemoteServer() {
  const [creating, setCreating] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)
  const [serverUrl, setServerUrl] = useState('https://example-remote-server.com')

  const createTestServer = async () => {
    setCreating(true)
    setResult(null)

    try {
      // First try creating via API (this will fail for external URLs, but will create a database entry)
      const response = await fetch('/api/remote-servers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          url: serverUrl
        })
      })

      if (response.ok) {
        setResult({ 
          success: true, 
          message: 'Test remote server created successfully!' 
        })
      } else {
        // If API creation fails, let's create a manual entry using direct Prisma
        const manualResponse = await fetch('/api/remote-servers/test-create', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: 'Test Remote Server',
            url: serverUrl,
            description: 'Test server for development purposes'
          })
        })

        if (manualResponse.ok) {
          setResult({ 
            success: true, 
            message: 'Test remote server created manually!' 
          })
        } else {
          throw new Error('Failed to create server')
        }
      }
    } catch (error) {
      setResult({ 
        success: false, 
        message: `Failed to create server: ${error}` 
      })
    } finally {
      setCreating(false)
    }
  }

  const createMultipleTestServers = async () => {
    setCreating(true)
    setResult(null)

    const testServers = [
      { name: 'Development Server', url: 'https://dev-server.example.com' },
      { name: 'Staging Server', url: 'https://staging-server.example.com' },
      { name: 'Production Server', url: 'https://prod-server.example.com' }
    ]

    try {
      let created = 0
      for (const server of testServers) {
        try {
          const response = await fetch('/api/remote-servers/test-create', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: server.name,
              url: server.url,
              description: `Test ${server.name.toLowerCase()} for development purposes`
            })
          })
          if (response.ok) created++
        } catch (error) {
          console.error(`Failed to create ${server.name}:`, error)
        }
      }

      setResult({ 
        success: created > 0, 
        message: `Created ${created} out of ${testServers.length} test servers` 
      })
    } catch (error) {
      setResult({ 
        success: false, 
        message: `Failed to create servers: ${error}` 
      })
    } finally {
      setCreating(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3">
        <Server className="h-5 w-5" />
        <h4 className="font-medium">Create Test Remote Servers</h4>
      </div>
      
      <p className="text-sm text-slate-600">
        If no remote servers are available for assignment, use this to create test servers.
      </p>

      {result && (
        <Alert variant={result.success ? "default" : "destructive"}>
          <div className="flex items-center gap-2">
            {result.success ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <XCircle className="h-4 w-4 text-red-500" />
            )}
            <AlertDescription>{result.message}</AlertDescription>
          </div>
        </Alert>
      )}

      <div className="space-y-3">
        <div className="flex gap-2">
          <Input
            value={serverUrl}
            onChange={(e) => setServerUrl(e.target.value)}
            placeholder="https://example-server.com"
            className="flex-1"
          />
          <Button 
            onClick={createTestServer}
            disabled={creating || !serverUrl}
            size="sm"
          >
            <Plus className="h-4 w-4 mr-1" />
            Create Single
          </Button>
        </div>

        <Button 
          onClick={createMultipleTestServers}
          disabled={creating}
          variant="outline"
          className="w-full"
        >
          {creating ? 'Creating...' : 'Create 3 Test Servers'}
        </Button>
      </div>
    </div>
  )
}