'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { PermissionInheritanceViewer } from '@/components/permissions/PermissionInheritanceViewer'
import { 
  TestTube, 
  Shield, 
  CheckCircle, 
  XCircle, 
  Users,
  Server,
  User,
  Crown
} from 'lucide-react'

export function PermissionInheritanceTest() {
  const [users, setUsers] = useState<any[]>([])
  const [servers, setServers] = useState<any[]>([])
  const [selectedUserId, setSelectedUserId] = useState<string>('')
  const [selectedServerId, setSelectedServerId] = useState<string>('')
  const [testResults, setTestResults] = useState<any[]>([])
  const [testing, setTesting] = useState(false)

  useEffect(() => {
    loadTestData()
  }, [])

  const loadTestData = async () => {
    try {
      // Load users
      const usersResponse = await fetch('/api/users')
      if (usersResponse.ok) {
        const usersData = await usersResponse.json()
        setUsers(usersData.users || [])
        if (usersData.users?.length > 0) {
          setSelectedUserId(usersData.users[0].id)
        }
      }

      // Load servers
      const serversResponse = await fetch('/api/remote-servers')
      if (serversResponse.ok) {
        const serversData = await serversResponse.json()
        setServers(serversData.remoteServers || [])
        if (serversData.remoteServers?.length > 0) {
          setSelectedServerId(serversData.remoteServers[0].id)
        }
      }
    } catch (error) {
      console.error('Error loading test data:', error)
    }
  }

  const runPermissionTests = async () => {
    if (!selectedUserId || !selectedServerId) return

    setTesting(true)
    setTestResults([])

    const results: any[] = []

    try {
      // Test 1: Calculate user permissions
      const permissionsResponse = await fetch(`/api/permissions/calculate?userId=${selectedUserId}&serverId=${selectedServerId}`)
      if (permissionsResponse.ok) {
        const data = await permissionsResponse.json()
        results.push({
          name: 'Calculate User Permissions',
          status: 'success',
          message: `User has ${data.data?.permissions?.length || 0} permissions from ${data.data?.roles?.length || 0} roles`,
          data: data.data
        })
      } else {
        throw new Error('Failed to calculate permissions')
      }

      // Test 2: Validate sample permissions
      const samplePermissions = ['read:documents', 'write:documents', 'invalid:permission']
      const validationResponse = await fetch('/api/permissions/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          serverId: selectedServerId,
          permissions: samplePermissions
        })
      })

      if (validationResponse.ok) {
        const validation = await validationResponse.json()
        results.push({
          name: 'Validate Permissions',
          status: validation.data.valid ? 'success' : 'warning',
          message: validation.data.valid 
            ? 'All permissions are valid'
            : `Invalid permissions: ${validation.data.invalidPermissions.join(', ')}`,
          data: validation.data
        })
      }

      // Test 3: Sync server permissions
      const syncResponse = await fetch(`/api/permissions/sync/${selectedServerId}`, {
        method: 'POST'
      })

      if (syncResponse.ok) {
        const syncData = await syncResponse.json()
        results.push({
          name: 'Sync Server Permissions',
          status: 'success',
          message: `Synced ${syncData.data?.synced || 0} permissions`,
          data: syncData.data
        })
      } else {
        const syncError = await syncResponse.json()
        results.push({
          name: 'Sync Server Permissions',
          status: 'error',
          message: syncError.error || 'Sync failed',
          data: syncError
        })
      }

    } catch (error) {
      results.push({
        name: 'Permission Tests',
        status: 'error',
        message: `Test failed: ${error}`,
        data: null
      })
    }

    setTestResults(results)
    setTesting(false)
  }

  const selectedUser = users.find(u => u.id === selectedUserId)
  const selectedServer = servers.find(s => s.id === selectedServerId)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning':
        return <TestTube className="h-5 w-5 text-yellow-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <TestTube className="h-5 w-5 text-blue-500" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Shield className="h-6 w-6" />
        <div>
          <h2 className="text-xl font-semibold">Permission Inheritance System Test</h2>
          <p className="text-sm text-slate-600">
            Test the permission inheritance logic and validation.
          </p>
        </div>
      </div>

      {/* Test Configuration */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">Test User</label>
          <select
            value={selectedUserId}
            onChange={(e) => setSelectedUserId(e.target.value)}
            className="w-full px-3 py-2 border border-slate-300 rounded-md"
          >
            <option value="">Select a user...</option>
            {users.map(user => (
              <option key={user.id} value={user.id}>
                {user.name || user.email} 
                {user.profile?.firstName && ` (${user.profile.firstName} ${user.profile.lastName})`}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Test Server</label>
          <select
            value={selectedServerId}
            onChange={(e) => setSelectedServerId(e.target.value)}
            className="w-full px-3 py-2 border border-slate-300 rounded-md"
          >
            <option value="">Select a server...</option>
            {servers.map(server => (
              <option key={server.id} value={server.id}>
                {server.name} ({server.url})
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Test Controls */}
      <div className="flex gap-3">
        <Button 
          onClick={runPermissionTests}
          disabled={testing || !selectedUserId || !selectedServerId}
        >
          {testing ? 'Running Tests...' : 'Run Permission Tests'}
        </Button>
        <Button variant="outline" onClick={loadTestData}>
          Refresh Data
        </Button>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="space-y-3">
          <h3 className="font-medium">Test Results</h3>
          <div className="space-y-2">
            {testResults.map((result, index) => (
              <div 
                key={index}
                className={`flex items-start gap-3 p-3 rounded-lg border ${
                  result.status === 'success' 
                    ? 'border-green-200 bg-green-50' 
                    : result.status === 'warning'
                    ? 'border-yellow-200 bg-yellow-50'
                    : 'border-red-200 bg-red-50'
                }`}
              >
                {getStatusIcon(result.status)}
                <div className="flex-1">
                  <div className="font-medium">{result.name}</div>
                  <div className="text-sm text-slate-600">{result.message}</div>
                  {result.data && (
                    <details className="mt-2">
                      <summary className="text-xs text-slate-500 cursor-pointer">Show data</summary>
                      <pre className="text-xs bg-white p-2 rounded border mt-1 overflow-auto">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Permission Inheritance Viewer */}
      {selectedUserId && selectedServerId && (
        <div className="border border-slate-200 rounded-lg p-6">
          <h3 className="font-medium mb-4 flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Live Permission Inheritance View
          </h3>
          <p className="text-sm text-slate-600 mb-4">
            Real-time view of how permissions are inherited for {selectedUser?.name || 'selected user'} 
            on {selectedServer?.name || 'selected server'}.
          </p>
          <PermissionInheritanceViewer
            userId={selectedUserId}
            serverId={selectedServerId}
            serverName={selectedServer?.name}
          />
        </div>
      )}
    </div>
  )
}