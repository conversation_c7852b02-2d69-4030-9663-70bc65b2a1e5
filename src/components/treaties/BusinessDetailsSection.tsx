'use client';

import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Plus, X } from 'lucide-react';
import { TreatyTypeDetailsData } from '@/types/user-treaty-assignment';
import { FileUpload } from './FileUpload';

interface BusinessDetailsSectionProps {
  formData: TreatyTypeDetailsData;
  onChange: (data: Partial<TreatyTypeDetailsData>) => void;
  errors: Record<string, string>;
}

interface BusinessDetail {
  id: string;
  businessName: string;
  treatyType: string;
  businessAddress: string;
  category: string;
  subcategory: string;
  ownershipStatus: string;
  licensingStatus: string;
  logoPath?: string;
}

export const BusinessDetailsSection: React.FC<BusinessDetailsSectionProps> = ({
  formData,
  onChange,
  errors
}) => {
  const [businesses, setBusinesses] = useState<BusinessDetail[]>(
    (formData.businessDetails as BusinessDetail[]) || []
  );

  const handleInputChange = (field: keyof TreatyTypeDetailsData, value: any) => {
    onChange({ [field]: value });
  };

  const handleBusinessChange = (index: number, field: keyof BusinessDetail, value: any) => {
    const updatedBusinesses = [...businesses];
    updatedBusinesses[index] = { ...updatedBusinesses[index], [field]: value };
    setBusinesses(updatedBusinesses);
    onChange({ businessDetails: updatedBusinesses });
  };

  const addBusiness = () => {
    const newBusiness: BusinessDetail = {
      id: Date.now().toString(),
      businessName: '',
      treatyType: '',
      businessAddress: '',
      category: '',
      subcategory: '',
      ownershipStatus: '',
      licensingStatus: ''
    };
    const updatedBusinesses = [...businesses, newBusiness];
    setBusinesses(updatedBusinesses);
    onChange({ businessDetails: updatedBusinesses });
  };

  const removeBusiness = (index: number) => {
    const updatedBusinesses = businesses.filter((_, i) => i !== index);
    setBusinesses(updatedBusinesses);
    onChange({ businessDetails: updatedBusinesses });
  };

  const handleLogoUpload = (index: number, file: File) => {
    handleBusinessChange(index, 'logoPath', file.name);
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Business / Organization Details
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          Please provide information about your business or organization.
        </p>
      </div>

      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Label className="text-sm font-medium">Businesses/Organizations</Label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addBusiness}
            className="flex items-center space-x-1"
          >
            <Plus className="w-4 h-4" />
            <span>Add Business</span>
          </Button>
        </div>

        {businesses.map((business, index) => (
          <div key={business.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 space-y-4">
            <div className="flex justify-between items-center">
              <h4 className="font-medium text-gray-900 dark:text-white">Business {index + 1}</h4>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => removeBusiness(index)}
                className="text-red-600 hover:text-red-700"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Business Name */}
            <div className="space-y-2">
              <Label htmlFor={`businessName-${index}`} className="text-sm font-medium">
                Business Name *
              </Label>
              <Input
                id={`businessName-${index}`}
                type="text"
                value={business.businessName}
                onChange={(e) => handleBusinessChange(index, 'businessName', e.target.value)}
                placeholder="Enter business name"
              />
            </div>

            {/* Treaty Type */}
            <div className="space-y-2">
              <Label htmlFor={`treatyType-${index}`} className="text-sm font-medium">
                Treaty Type
              </Label>
              <Select
                value={business.treatyType}
                onValueChange={(value) => handleBusinessChange(index, 'treatyType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select treaty type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="business-protection">Business Protection</SelectItem>
                  <SelectItem value="intellectual-property">Intellectual Property</SelectItem>
                  <SelectItem value="trade-protection">Trade Protection</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Business Address */}
            <div className="space-y-2">
              <Label htmlFor={`businessAddress-${index}`} className="text-sm font-medium">
                Business Address
              </Label>
              <Textarea
                id={`businessAddress-${index}`}
                value={business.businessAddress}
                onChange={(e) => handleBusinessChange(index, 'businessAddress', e.target.value)}
                placeholder="Enter complete business address"
                rows={3}
              />
            </div>

            {/* Category and Subcategory */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor={`category-${index}`} className="text-sm font-medium">
                  Category
                </Label>
                <Select
                  value={business.category}
                  onValueChange={(value) => handleBusinessChange(index, 'category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="retail">Retail</SelectItem>
                    <SelectItem value="manufacturing">Manufacturing</SelectItem>
                    <SelectItem value="services">Services</SelectItem>
                    <SelectItem value="technology">Technology</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor={`subcategory-${index}`} className="text-sm font-medium">
                  Subcategory
                </Label>
                <Input
                  id={`subcategory-${index}`}
                  type="text"
                  value={business.subcategory}
                  onChange={(e) => handleBusinessChange(index, 'subcategory', e.target.value)}
                  placeholder="Enter subcategory"
                />
              </div>
            </div>

            {/* Ownership Status */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Ownership Status</Label>
              <RadioGroup
                value={business.ownershipStatus}
                onValueChange={(value) => handleBusinessChange(index, 'ownershipStatus', value)}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="sole-proprietor" id={`sole-${index}`} />
                  <Label htmlFor={`sole-${index}`}>Sole Proprietor</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="partnership" id={`partnership-${index}`} />
                  <Label htmlFor={`partnership-${index}`}>Partnership</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="corporation" id={`corporation-${index}`} />
                  <Label htmlFor={`corporation-${index}`}>Corporation</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="llc" id={`llc-${index}`} />
                  <Label htmlFor={`llc-${index}`}>LLC</Label>
                </div>
              </RadioGroup>
            </div>

            {/* Licensing Status */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Licensing Status</Label>
              <RadioGroup
                value={business.licensingStatus}
                onValueChange={(value) => handleBusinessChange(index, 'licensingStatus', value)}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="licensed" id={`licensed-${index}`} />
                  <Label htmlFor={`licensed-${index}`}>Licensed</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="pending" id={`pending-${index}`} />
                  <Label htmlFor={`pending-${index}`}>Pending</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="not-required" id={`not-required-${index}`} />
                  <Label htmlFor={`not-required-${index}`}>Not Required</Label>
                </div>
              </RadioGroup>
            </div>

            {/* Logo Upload */}
            <FileUpload
              value={business.logoPath ? [business.logoPath] : []}
              onChange={(files) => handleBusinessChange(index, 'logoPath', files[0] || '')}
              accept="image/*"
              maxFiles={1}
              maxSize={5}
              label="Business Logo"
              description="Upload your business logo"
              showPreview={true}
              allowMultiple={false}
            />
          </div>
        ))}

        {businesses.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p>No businesses added yet. Click &ldquo;Add Business&rdquo; to get started.</p>
          </div>
        )}
      </div>
    </div>
  );
};