'use client';

import React, { useState } from 'react';
import { Plus, X, Save, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface TreatyType {
  id: string;
  name: string;
  description: string | null;
  category: string;
  isActive: boolean;
}

interface CreateTreatyData {
  name: string;
  description: string;
}

interface CreateApplicationData {
  treatyId: string;
  treatyTypeId: string;
}

interface TreatyManagementProps {
  treatyTypes: TreatyType[];
  onCreateTreaty: (data: CreateTreatyData) => Promise<void>;
  onCreateApplication: (data: CreateApplicationData) => Promise<void>;
  isLoading?: boolean;
}

export const TreatyManagement: React.FC<TreatyManagementProps> = ({
  treatyTypes,
  onCreateTreaty,
  onCreateApplication,
  isLoading = false
}) => {
  const [isCreateTreatyOpen, setIsCreateTreatyOpen] = useState(false);
  const [isCreateApplicationOpen, setIsCreateApplicationOpen] = useState(false);
  const [selectedTreatyId, setSelectedTreatyId] = useState<string>('');

  const [treatyForm, setTreatyForm] = useState<CreateTreatyData>({
    name: '',
    description: ''
  });

  const [applicationForm, setApplicationForm] = useState<CreateApplicationData>({
    treatyId: '',
    treatyTypeId: ''
  });

  const handleCreateTreaty = async () => {
    if (!treatyForm.name.trim()) return;

    try {
      await onCreateTreaty(treatyForm);
      setTreatyForm({ name: '', description: '' });
      setIsCreateTreatyOpen(false);
    } catch (error) {
      console.error('Error creating treaty:', error);
    }
  };

  const handleCreateApplication = async () => {
    if (!applicationForm.treatyId || !applicationForm.treatyTypeId) return;

    try {
      await onCreateApplication(applicationForm);
      setApplicationForm({ treatyId: '', treatyTypeId: '' });
      setIsCreateApplicationOpen(false);
    } catch (error) {
      console.error('Error creating application:', error);
    }
  };

  const openCreateApplication = (treatyId: string) => {
    setSelectedTreatyId(treatyId);
    setApplicationForm({ treatyId, treatyTypeId: '' });
    setIsCreateApplicationOpen(true);
  };

  const activeTreatyTypes = treatyTypes.filter(type => type.isActive);

  return (
    <div className="space-y-4">
      {/* Action Buttons */}
      <div className="flex space-x-4">
        <Dialog open={isCreateTreatyOpen} onOpenChange={setIsCreateTreatyOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create New Treaty
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Treaty</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="treaty-name">Treaty Name *</Label>
                <Input
                  id="treaty-name"
                  value={treatyForm.name}
                  onChange={(e) => setTreatyForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter treaty name"
                />
              </div>
              <div>
                <Label htmlFor="treaty-description">Description</Label>
                <Textarea
                  id="treaty-description"
                  value={treatyForm.description}
                  onChange={(e) => setTreatyForm(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter treaty description (optional)"
                  rows={3}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsCreateTreatyOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateTreaty}
                  disabled={!treatyForm.name.trim() || isLoading}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Create Treaty
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        <Dialog open={isCreateApplicationOpen} onOpenChange={setIsCreateApplicationOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Treaty Type Application</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="treaty-type">Treaty Type *</Label>
                <select
                  id="treaty-type"
                  value={applicationForm.treatyTypeId}
                  onChange={(e) => setApplicationForm(prev => ({ ...prev, treatyTypeId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <option value="">Select a treaty type</option>
                  {activeTreatyTypes.map((type) => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsCreateApplicationOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateApplication}
                  disabled={!applicationForm.treatyTypeId || isLoading}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Application
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Treaty Types Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Available Treaty Types
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {activeTreatyTypes.map((type) => (
            <div
              key={type.id}
              className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-blue-300 dark:hover:border-blue-600 transition-colors"
            >
              <div className="flex items-start space-x-3">
                <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {type.name}
                  </h4>
                  {type.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                      {type.description}
                    </p>
                  )}
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mt-2">
                    {type.category}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {activeTreatyTypes.length === 0 && (
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No treaty types available</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Treaty types need to be configured by an administrator.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

// Export the openCreateApplication function for external use
export const useTreatyManagement = () => {
  const [isCreateApplicationOpen, setIsCreateApplicationOpen] = useState(false);
  const [selectedTreatyId, setSelectedTreatyId] = useState<string>('');

  const openCreateApplication = (treatyId: string) => {
    setSelectedTreatyId(treatyId);
    setIsCreateApplicationOpen(true);
  };

  return {
    isCreateApplicationOpen,
    setIsCreateApplicationOpen,
    selectedTreatyId,
    openCreateApplication
  };
};