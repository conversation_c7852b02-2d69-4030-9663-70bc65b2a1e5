'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { Loader2, Save, ArrowLeft, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { NationTreatyStatus } from '@/lib/validation/nation-treaty';

interface FormSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
}

const FormSection: React.FC<FormSectionProps> = ({ title, description, children }) => (
  <section className="relative border-2 border-slate-300 dark:border-slate-600 rounded-xl bg-white dark:bg-slate-900 px-6 pt-8 pb-6 shadow-sm">
    <span className="absolute -top-3 left-6 bg-white dark:bg-slate-900 px-3 text-sm font-semibold uppercase tracking-wide text-slate-600 dark:text-slate-300">
      {title}
    </span>
    {description && (
      <p className="mb-6 text-sm text-slate-600 dark:text-slate-300">
        {description}
      </p>
    )}
    <div className="space-y-4">
      {children}
    </div>
  </section>
);

interface UserOption {
  id: string;
  name?: string | null;
  email?: string | null;
  nwaEmail?: string | null;
  phone?: string | null;
  profile?: {
    firstName?: string | null;
    lastName?: string | null;
    nwaEmail?: string | null;
    phone?: string | null;
    mobile?: string | null;
    personalEmail?: string | null;
  } | null;
}

interface CreateNationTreatyFormData {
  name: string;
  officialName: string;
  status: keyof typeof NationTreatyStatus;
  description: string;
  contactEmail: string;
  contactPhone: string;
  contactAddress: string;
  website: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactEmail: string;
  notes: string;
}

interface EnvoyOfficeDetails {
  streetAddress: string;
  townRegion: string;
  city: string;
  country: string;
}

export interface CreateNationTreatyTabProps {
  onCreateSuccess?: () => void;
  onCancel?: () => void;
}

export const CreateNationTreatyTab: React.FC<CreateNationTreatyTabProps> = ({ onCreateSuccess, onCancel }) => {
  const [formData, setFormData] = useState<CreateNationTreatyFormData>({
    name: '',
    officialName: '',
    status: 'ACTIVE',
    description: '',
    contactEmail: '',
    contactPhone: '',
    contactAddress: '',
    website: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactEmail: '',
    notes: ''
  });

  const [envoyOffice, setEnvoyOffice] = useState<EnvoyOfficeDetails>({
    streetAddress: '',
    townRegion: '',
    city: '',
    country: ''
  });

  const [selectedSpecialEnvoys, setSelectedSpecialEnvoys] = useState<UserOption[]>([]);
  const [specialEnvoySearchTerm, setSpecialEnvoySearchTerm] = useState('');
  const [specialEnvoyOptions, setSpecialEnvoyOptions] = useState<UserOption[]>([]);
  const [specialEnvoyLoading, setSpecialEnvoyLoading] = useState(false);
  const [specialEnvoyError, setSpecialEnvoyError] = useState<string | null>(null);

  const [selectedEmergencyContact, setSelectedEmergencyContact] = useState<UserOption | null>(null);
  const [emergencyContactSearchTerm, setEmergencyContactSearchTerm] = useState('');
  const [emergencyContactOptions, setEmergencyContactOptions] = useState<UserOption[]>([]);
  const [emergencyContactLoading, setEmergencyContactLoading] = useState(false);
  const [emergencyContactError, setEmergencyContactError] = useState<string | null>(null);

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const formatUserDisplay = useCallback((user: UserOption) => {
    const names = [user.profile?.firstName, user.profile?.lastName].filter(Boolean).join(' ');
    if (names) {
      return names;
    }
    if (user.name) {
      return user.name;
    }
    if (user.email) {
      return user.email;
    }
    if (user.nwaEmail) {
      return user.nwaEmail;
    }
    return 'Unnamed User';
  }, []);

  const formatUserSubtitle = useCallback((user: UserOption) => {
    if (user.email) {
      return user.email;
    }
    if (user.profile?.personalEmail) {
      return user.profile.personalEmail;
    }
    if (user.nwaEmail) {
      return user.nwaEmail;
    }
    return null;
  }, []);

  const fetchUsers = useCallback(async (query: string) => {
    const trimmed = query.trim();
    if (trimmed.length < 2) {
      return [] as UserOption[];
    }

    const response = await fetch(`/api/users/search?q=${encodeURIComponent(trimmed)}&limit=5`);
    if (!response.ok) {
      throw new Error('Failed to search users');
    }

    const data = await response.json();
    if (Array.isArray(data?.users)) {
      return data.users as UserOption[];
    }
    return [] as UserOption[];
  }, []);

  const handleInputChange = useCallback((field: keyof CreateNationTreatyFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setErrors(prev => {
      if (!prev[field]) {
        return prev;
      }
      const { [field]: _removed, ...rest } = prev;
      return rest;
    });
  }, []);

  useEffect(() => {
    if (specialEnvoySearchTerm.trim().length < 2) {
      setSpecialEnvoyOptions([]);
      setSpecialEnvoyLoading(false);
      setSpecialEnvoyError(null);
      return;
    }

    let isActive = true;
    setSpecialEnvoyLoading(true);
    setSpecialEnvoyError(null);

    const debounce = setTimeout(async () => {
      try {
        const users = await fetchUsers(specialEnvoySearchTerm);
        if (isActive) {
          setSpecialEnvoyOptions(users);
        }
      } catch (error) {
        if (isActive) {
          setSpecialEnvoyError('Unable to search envoys right now.');
          setSpecialEnvoyOptions([]);
        }
      } finally {
        if (isActive) {
          setSpecialEnvoyLoading(false);
        }
      }
    }, 300);

    return () => {
      isActive = false;
      clearTimeout(debounce);
    };
  }, [fetchUsers, specialEnvoySearchTerm]);

  useEffect(() => {
    if (emergencyContactSearchTerm.trim().length < 2) {
      setEmergencyContactOptions([]);
      setEmergencyContactLoading(false);
      setEmergencyContactError(null);
      return;
    }

    let isActive = true;
    setEmergencyContactLoading(true);
    setEmergencyContactError(null);

    const debounce = setTimeout(async () => {
      try {
        const users = await fetchUsers(emergencyContactSearchTerm);
        if (isActive) {
          setEmergencyContactOptions(users);
        }
      } catch (error) {
        if (isActive) {
          setEmergencyContactError('Unable to search members right now.');
          setEmergencyContactOptions([]);
        }
      } finally {
        if (isActive) {
          setEmergencyContactLoading(false);
        }
      }
    }, 300);

    return () => {
      isActive = false;
      clearTimeout(debounce);
    };
  }, [emergencyContactSearchTerm, fetchUsers]);

  const handleAddSpecialEnvoy = useCallback((user: UserOption) => {
    setSelectedSpecialEnvoys(prev => {
      if (prev.some(envoy => envoy.id === user.id)) {
        return prev;
      }
      return [...prev, user];
    });
    setSpecialEnvoySearchTerm('');
    setSpecialEnvoyOptions([]);
  }, []);

  const handleRemoveSpecialEnvoy = useCallback((userId: string) => {
    setSelectedSpecialEnvoys(prev => prev.filter(envoy => envoy.id !== userId));
  }, []);

  const handleSelectEmergencyContact = useCallback((user: UserOption) => {
    setSelectedEmergencyContact(user);
    const displayName = formatUserDisplay(user);
    handleInputChange('emergencyContactName', displayName);
    const emailCandidate = user.email || user.profile?.personalEmail || user.nwaEmail || '';
    handleInputChange('emergencyContactEmail', emailCandidate);
    const phoneCandidate = user.phone || user.profile?.phone || user.profile?.mobile || '';
    handleInputChange('emergencyContactPhone', phoneCandidate);
    setEmergencyContactSearchTerm('');
    setEmergencyContactOptions([]);
  }, [formatUserDisplay, handleInputChange]);

  const handleClearEmergencyContact = useCallback(() => {
    setSelectedEmergencyContact(null);
    handleInputChange('emergencyContactName', '');
    handleInputChange('emergencyContactPhone', '');
    handleInputChange('emergencyContactEmail', '');
  }, [handleInputChange]);

  const handleEnvoyOfficeChange = useCallback(
    (field: keyof EnvoyOfficeDetails, value: string) => {
      setEnvoyOffice(prev => ({ ...prev, [field]: value }));
    },
    []
  );

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nation treaty name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    } else if (formData.name.length > 100) {
      newErrors.name = 'Name cannot exceed 100 characters';
    }

    if (!formData.officialName.trim()) {
      newErrors.officialName = 'Official name is required';
    } else if (formData.officialName.length < 2) {
      newErrors.officialName = 'Official name must be at least 2 characters';
    } else if (formData.officialName.length > 200) {
      newErrors.officialName = 'Official name cannot exceed 200 characters';
    }

    if (formData.description.length > 1000) {
      newErrors.description = 'Description cannot exceed 1000 characters';
    }

    if (formData.contactEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactEmail)) {
      newErrors.contactEmail = 'Invalid email format';
    }

    if (formData.contactPhone && !/^[+]?\d{1,16}$/.test(formData.contactPhone.replace(/[\s\-()]/g, ''))) {
      newErrors.contactPhone = 'Invalid phone number format';
    }

    const combinedContactAddress = [
      envoyOffice.streetAddress,
      envoyOffice.townRegion,
      envoyOffice.city,
      envoyOffice.country
    ].filter(Boolean).join(', ');

    if (combinedContactAddress.length > 500) {
      newErrors.contactAddress = 'Address cannot exceed 500 characters';
    }

    if (formData.website && !/^https?:\/\//i.test(formData.website)) {
      newErrors.website = 'Website must start with http:// or https://';
    }

    if (formData.notes.length > 2000) {
      newErrors.notes = 'Notes cannot exceed 2000 characters';
    }

    if (formData.emergencyContactName && !formData.emergencyContactPhone) {
      newErrors.emergencyContactPhone = 'Emergency contact phone is required when name is provided';
    }

    if (formData.emergencyContactPhone && !/^[+]?\d{1,16}$/.test(formData.emergencyContactPhone.replace(/[\s\-()]/g, ''))) {
      newErrors.emergencyContactPhone = 'Invalid emergency contact phone number';
    }

    if (formData.emergencyContactEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.emergencyContactEmail)) {
      newErrors.emergencyContactEmail = 'Invalid emergency contact email';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors(prev => ({ ...prev, submit: '' }));

    try {
      const combinedContactAddress = [
        envoyOffice.streetAddress,
        envoyOffice.townRegion,
        envoyOffice.city,
        envoyOffice.country
      ].filter(Boolean).join(', ');

      const metadata: Record<string, unknown> = {};
      if (envoyOffice.streetAddress || envoyOffice.townRegion || envoyOffice.city || envoyOffice.country) {
        metadata.envoyOffice = {
          streetAddress: envoyOffice.streetAddress || null,
          townRegion: envoyOffice.townRegion || null,
          city: envoyOffice.city || null,
          country: envoyOffice.country || null
        };
      }

      if (selectedSpecialEnvoys.length > 0) {
        metadata.specialEnvoyUserIds = selectedSpecialEnvoys.map(envoy => envoy.id);
      }

      if (selectedEmergencyContact) {
        metadata.emergencyContactUserId = selectedEmergencyContact.id;
      }

      const response = await fetch('/api/nation-treaties', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          officialName: formData.officialName.trim(),
          status: formData.status,
          description: formData.description.trim() || null,
          contactEmail: formData.contactEmail.trim() || null,
          contactPhone: formData.contactPhone.trim() || null,
          contactAddress: combinedContactAddress || null,
          website: formData.website.trim() || null,
          emergencyContactName: formData.emergencyContactName.trim() || null,
          emergencyContactPhone: formData.emergencyContactPhone.trim() || null,
          emergencyContactEmail: formData.emergencyContactEmail.trim() || null,
          notes: formData.notes.trim() || null,
          metadata: Object.keys(metadata).length ? metadata : undefined
        })
      });

      const result = await response.json();
      if (!response.ok || !result?.success) {
        throw new Error(result?.error || 'Failed to create nation treaty');
      }

      setSuccess(true);
      setFormData({
        name: '',
        officialName: '',
        status: 'ACTIVE',
        description: '',
        contactEmail: '',
        contactPhone: '',
        contactAddress: '',
        website: '',
        emergencyContactName: '',
        emergencyContactPhone: '',
        emergencyContactEmail: '',
        notes: ''
      });
      setEnvoyOffice({ streetAddress: '', townRegion: '', city: '', country: '' });
      setSelectedSpecialEnvoys([]);
      setSelectedEmergencyContact(null);
      setSpecialEnvoySearchTerm('');
      setSpecialEnvoyOptions([]);
      setSpecialEnvoyError(null);
      setEmergencyContactSearchTerm('');
      setEmergencyContactOptions([]);
      setEmergencyContactError(null);

      if (onCreateSuccess) {
        onCreateSuccess();
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to create nation treaty';
      setErrors(prev => ({ ...prev, submit: message }));
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-6">
        <div className="text-center py-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
            <Plus className="h-8 w-8 text-green-600 dark:text-green-300" />
          </div>
          <h3 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-2">
            Treaty Entity Created Successfully!
          </h3>
          <p className="text-slate-600 dark:text-slate-300 mb-6">
            The new nation treaty entity has been saved and is ready for management.
          </p>
          <div className="flex gap-4 justify-center">
            <Button onClick={() => setSuccess(false)} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Another
            </Button>
            {onCancel && (
              <Button variant="outline" onClick={onCancel} className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to List
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
          Create New Treaty Entity
        </h2>
        <p className="text-slate-600 dark:text-slate-300">
          Provide detailed information to register a nation treaty
        </p>
      </div>

      {errors.submit && (
        <Alert variant="destructive">
          <AlertDescription>{errors.submit}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <FormSection
          title="Nation / Tribal Information"
          description="Core identity details for this treaty entity."
        >
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="name" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                Nation / Tribal Treaty *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(event) => handleInputChange('name', event.target.value)}
                placeholder="Enter treaty name"
                className="mt-1"
              />
              {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
            </div>
            <div>
              <Label htmlFor="officialName" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                Official Nation Name *
              </Label>
              <Input
                id="officialName"
                value={formData.officialName}
                onChange={(event) => handleInputChange('officialName', event.target.value)}
                placeholder="Enter official nation or tribal name"
                className="mt-1"
              />
              {errors.officialName && <p className="mt-1 text-sm text-red-500">{errors.officialName}</p>}
            </div>
            <div>
              <Label htmlFor="status" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                Treaty Status
              </Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value as keyof typeof NationTreatyStatus)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(NationTreatyStatus).map((statusValue) => (
                    <SelectItem key={statusValue} value={statusValue}>
                      {statusValue.charAt(0) + statusValue.slice(1).toLowerCase()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="description" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                Treaty Description
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(event) => handleInputChange('description', event.target.value)}
                placeholder="Describe the treaty's mission, scope, and any distinguishing details"
                rows={4}
                className="mt-1 resize-none"
              />
              {errors.description && <p className="mt-1 text-sm text-red-500">{errors.description}</p>}
            </div>
          </div>
        </FormSection>

        <FormSection
          title="Envoy Office"
          description="Primary envoy office details used for direct outreach."
        >
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="md:col-span-2">
              <Label htmlFor="envoyStreetAddress" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                Street Address
              </Label>
              <Input
                id="envoyStreetAddress"
                value={envoyOffice.streetAddress}
                onChange={(event) => handleEnvoyOfficeChange('streetAddress', event.target.value)}
                placeholder="123 Alliance Row"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="envoyTownRegion" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                Town / Region
              </Label>
              <Input
                id="envoyTownRegion"
                value={envoyOffice.townRegion}
                onChange={(event) => handleEnvoyOfficeChange('townRegion', event.target.value)}
                placeholder="Riverbend Territory"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="envoyCity" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                City
              </Label>
              <Input
                id="envoyCity"
                value={envoyOffice.city}
                onChange={(event) => handleEnvoyOfficeChange('city', event.target.value)}
                placeholder="Phoenix"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="envoyCountry" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                Country
              </Label>
              <Input
                id="envoyCountry"
                value={envoyOffice.country}
                onChange={(event) => handleEnvoyOfficeChange('country', event.target.value)}
                placeholder="United States"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="contactEmail" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                Office Email
              </Label>
              <Input
                id="contactEmail"
                type="email"
                value={formData.contactEmail}
                onChange={(event) => handleInputChange('contactEmail', event.target.value)}
                placeholder="<EMAIL>"
                className="mt-1"
              />
              {errors.contactEmail && <p className="mt-1 text-sm text-red-500">{errors.contactEmail}</p>}
            </div>
            <div>
              <Label htmlFor="contactPhone" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                Office Phone
              </Label>
              <Input
                id="contactPhone"
                value={formData.contactPhone}
                onChange={(event) => handleInputChange('contactPhone', event.target.value)}
                placeholder="****** 567 8900"
                className="mt-1"
              />
              {errors.contactPhone && <p className="mt-1 text-sm text-red-500">{errors.contactPhone}</p>}
            </div>
          </div>
          {errors.contactAddress && <p className="text-sm text-red-500">{errors.contactAddress}</p>}
        </FormSection>

        <FormSection
          title="Special Envoys"
          description="Assign special envoys linked to this treaty. Search for existing users and add them to the roster."
        >
          <div className="space-y-4">
            <div>
              <Label htmlFor="specialEnvoySearch" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                Search Envoys
              </Label>
              <Input
                id="specialEnvoySearch"
                value={specialEnvoySearchTerm}
                onChange={(event) => setSpecialEnvoySearchTerm(event.target.value)}
                placeholder="Search by name, email, or NWA email"
                className="mt-1"
              />
              {specialEnvoyError && <p className="mt-1 text-sm text-red-500">{specialEnvoyError}</p>}
              {specialEnvoyLoading && (
                <p className="mt-1 text-sm text-slate-500 dark:text-slate-300">Searching envoys...</p>
              )}
              {specialEnvoyOptions.length > 0 && (
                <div className="mt-2 divide-y divide-slate-200 overflow-hidden rounded-lg border border-slate-200 dark:divide-slate-700 dark:border-slate-700">
                  {specialEnvoyOptions.map((envoy) => (
                    <button
                      type="button"
                      key={envoy.id}
                      onClick={() => handleAddSpecialEnvoy(envoy)}
                      className="flex w-full flex-col gap-0 px-4 py-2 text-left hover:bg-slate-50 dark:hover:bg-slate-800"
                    >
                      <span className="text-sm font-medium text-slate-800 dark:text-slate-100">
                        {formatUserDisplay(envoy)}
                      </span>
                      {formatUserSubtitle(envoy) && (
                        <span className="text-xs text-slate-500 dark:text-slate-300">
                          {formatUserSubtitle(envoy)}
                        </span>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
            {selectedSpecialEnvoys.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {selectedSpecialEnvoys.map((envoy) => (
                  <span
                    key={envoy.id}
                    className="inline-flex items-center gap-2 rounded-full border border-slate-300 bg-slate-100 px-3 py-1 text-sm font-medium text-slate-700 dark:border-slate-600 dark:bg-slate-800 dark:text-slate-100"
                  >
                    {formatUserDisplay(envoy)}
                    <button
                      type="button"
                      onClick={() => handleRemoveSpecialEnvoy(envoy.id)}
                      className="text-xs text-slate-500 hover:text-red-500 dark:text-slate-300 dark:hover:text-red-400"
                      aria-label={`Remove ${formatUserDisplay(envoy)} from special envoys`}
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>
        </FormSection>

        <FormSection
          title="Emergency Contact"
          description="Select a member to serve as the emergency point of contact, then confirm their details."
        >
          <div className="space-y-4">
            <div>
              <Label htmlFor="emergencyContactSearch" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                Search Members
              </Label>
              <Input
                id="emergencyContactSearch"
                value={emergencyContactSearchTerm}
                onChange={(event) => setEmergencyContactSearchTerm(event.target.value)}
                placeholder="Search by name, email, or NWA email"
                className="mt-1"
              />
              {emergencyContactError && <p className="mt-1 text-sm text-red-500">{emergencyContactError}</p>}
              {emergencyContactLoading && (
                <p className="mt-1 text-sm text-slate-500 dark:text-slate-300">Searching members...</p>
              )}
              {emergencyContactOptions.length > 0 && (
                <div className="mt-2 divide-y divide-slate-200 overflow-hidden rounded-lg border border-slate-200 dark:divide-slate-700 dark:border-slate-700">
                  {emergencyContactOptions.map((user) => (
                    <button
                      type="button"
                      key={user.id}
                      onClick={() => handleSelectEmergencyContact(user)}
                      className="flex w-full flex-col gap-0 px-4 py-2 text-left hover:bg-slate-50 dark:hover:bg-slate-800"
                    >
                      <span className="text-sm font-medium text-slate-800 dark:text-slate-100">
                        {formatUserDisplay(user)}
                      </span>
                      {formatUserSubtitle(user) && (
                        <span className="text-xs text-slate-500 dark:text-slate-300">
                          {formatUserSubtitle(user)}
                        </span>
                      )}
                    </button>
                  ))}
                </div>
              )}
              {selectedEmergencyContact && (
                <div className="mt-3 flex items-center justify-between rounded-lg border border-slate-200 bg-slate-50 px-4 py-3 text-sm dark:border-slate-700 dark:bg-slate-800 dark:text-slate-100">
                  <div>
                    <p className="font-medium">{formatUserDisplay(selectedEmergencyContact)}</p>
                    {formatUserSubtitle(selectedEmergencyContact) && (
                      <p className="text-xs text-slate-500 dark:text-slate-300">
                        {formatUserSubtitle(selectedEmergencyContact)}
                      </p>
                    )}
                  </div>
                  <Button type="button" variant="outline" size="sm" onClick={handleClearEmergencyContact}>
                    Clear
                  </Button>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="md:col-span-2">
                <Label htmlFor="emergencyContactName" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                  Contact Name
                </Label>
                <Input
                  id="emergencyContactName"
                  value={formData.emergencyContactName}
                  onChange={(event) => handleInputChange('emergencyContactName', event.target.value)}
                  placeholder="Emergency contact full name"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="emergencyContactPhone" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                  Contact Phone
                </Label>
                <Input
                  id="emergencyContactPhone"
                  value={formData.emergencyContactPhone}
                  onChange={(event) => handleInputChange('emergencyContactPhone', event.target.value)}
                  placeholder="****** 567 8900"
                  className="mt-1"
                />
                {errors.emergencyContactPhone && (
                  <p className="mt-1 text-sm text-red-500">{errors.emergencyContactPhone}</p>
                )}
              </div>
              <div>
                <Label htmlFor="emergencyContactEmail" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
                  Contact Email
                </Label>
                <Input
                  id="emergencyContactEmail"
                  type="email"
                  value={formData.emergencyContactEmail}
                  onChange={(event) => handleInputChange('emergencyContactEmail', event.target.value)}
                  placeholder="<EMAIL>"
                  className="mt-1"
                />
                {errors.emergencyContactEmail && (
                  <p className="mt-1 text-sm text-red-500">{errors.emergencyContactEmail}</p>
                )}
              </div>
            </div>
          </div>
        </FormSection>

        <FormSection
          title="Additional Notes"
          description="Internal-only notes for alliance staff."
        >
          <div>
            <Label htmlFor="notes" className="text-sm font-semibold text-slate-700 dark:text-slate-200">
              Notes
            </Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(event) => handleInputChange('notes', event.target.value)}
              placeholder="Record contextual information, reminders, or follow-up actions"
              rows={4}
              className="mt-1 resize-none"
            />
            {errors.notes && <p className="mt-1 text-sm text-red-500">{errors.notes}</p>}
          </div>
        </FormSection>

        <div className="flex justify-end gap-4 border-t border-slate-200 pt-6 dark:border-slate-700">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Cancel
            </Button>
          <Button type="submit" disabled={isLoading} className="flex items-center gap-2">
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                Create Treaty Entity
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
