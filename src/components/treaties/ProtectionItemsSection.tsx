'use client';

import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, X } from 'lucide-react';
import { TreatyTypeDetailsData } from '@/types/user-treaty-assignment';
import { CountryCityAutocomplete } from './CountryCityAutocomplete';
import { FileUpload } from './FileUpload';

interface ProtectionItemsSectionProps {
  formData: TreatyTypeDetailsData;
  onChange: (data: Partial<TreatyTypeDetailsData>) => void;
  errors: Record<string, string>;
}

interface ProtectionItem {
  id: string;
  itemType: string;
  itemDescription: string;
  itemLocation: string;
  countryId?: number;
  cityId?: number;
  proofImages: string[];
}

export const ProtectionItemsSection: React.FC<ProtectionItemsSectionProps> = ({
  formData,
  onChange,
  errors
}) => {
  const [items, setItems] = useState<ProtectionItem[]>(
    (formData.protectionItems as ProtectionItem[]) || []
  );

  const handleInputChange = (field: keyof TreatyTypeDetailsData, value: any) => {
    onChange({ [field]: value });
  };

  const handleItemChange = (index: number, field: keyof ProtectionItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    setItems(updatedItems);
    onChange({ protectionItems: updatedItems });
  };

  const addItem = () => {
    const newItem: ProtectionItem = {
      id: Date.now().toString(),
      itemType: '',
      itemDescription: '',
      itemLocation: '',
      proofImages: []
    };
    const updatedItems = [...items, newItem];
    setItems(updatedItems);
    onChange({ protectionItems: updatedItems });
  };

  const removeItem = (index: number) => {
    const updatedItems = items.filter((_, i) => i !== index);
    setItems(updatedItems);
    onChange({ protectionItems: updatedItems });
  };

  const handleFileUpload = (index: number, files: FileList) => {
    const fileNames = Array.from(files).map(file => file.name);
    const currentImages = items[index].proofImages || [];
    const updatedImages = [...currentImages, ...fileNames];
    handleItemChange(index, 'proofImages', updatedImages);
  };

  const removeImage = (itemIndex: number, imageIndex: number) => {
    const updatedImages = items[itemIndex].proofImages.filter((_, i) => i !== imageIndex);
    handleItemChange(itemIndex, 'proofImages', updatedImages);
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Items for Protection Recognition
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          Please provide information about items requiring treaty protection.
        </p>
      </div>

      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Label className="text-sm font-medium">Protection Items</Label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addItem}
            className="flex items-center space-x-1"
          >
            <Plus className="w-4 h-4" />
            <span>Add Item</span>
          </Button>
        </div>

        {items.map((item, index) => (
          <div key={item.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 space-y-4">
            <div className="flex justify-between items-center">
              <h4 className="font-medium text-gray-900 dark:text-white">Item {index + 1}</h4>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => removeItem(index)}
                className="text-red-600 hover:text-red-700"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Item Type */}
            <div className="space-y-2">
              <Label htmlFor={`itemType-${index}`} className="text-sm font-medium">
                Item Type *
              </Label>
              <Select
                value={item.itemType}
                onValueChange={(value) => handleItemChange(index, 'itemType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select item type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="artwork">Artwork</SelectItem>
                  <SelectItem value="antique">Antique</SelectItem>
                  <SelectItem value="jewelry">Jewelry</SelectItem>
                  <SelectItem value="collectible">Collectible</SelectItem>
                  <SelectItem value="document">Document</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Item Description */}
            <div className="space-y-2">
              <Label htmlFor={`itemDescription-${index}`} className="text-sm font-medium">
                Item Description *
              </Label>
              <Textarea
                id={`itemDescription-${index}`}
                value={item.itemDescription}
                onChange={(e) => handleItemChange(index, 'itemDescription', e.target.value)}
                placeholder="Provide detailed description of the item"
                rows={3}
              />
            </div>

            {/* Item Location */}
            <div className="space-y-2">
              <Label htmlFor={`itemLocation-${index}`} className="text-sm font-medium">
                Current Location
              </Label>
              <Input
                id={`itemLocation-${index}`}
                type="text"
                value={item.itemLocation}
                onChange={(e) => handleItemChange(index, 'itemLocation', e.target.value)}
                placeholder="Where is the item currently located?"
              />
            </div>

            {/* Country/City Autocomplete */}
            <CountryCityAutocomplete
              countryId={item.countryId}
              cityId={item.cityId}
              onCountryChange={(countryId) => handleItemChange(index, 'countryId', countryId)}
              onCityChange={(cityId) => handleItemChange(index, 'cityId', cityId)}
              countryLabel="Item Country"
              cityLabel="Item City"
              required={false}
            />

            {/* Proof Images Upload */}
            <FileUpload
              value={item.proofImages || []}
              onChange={(files) => handleItemChange(index, 'proofImages', files)}
              accept="image/*"
              maxFiles={10}
              maxSize={5}
              label="Proof Images"
              description="Upload multiple images showing the item from different angles"
              showPreview={true}
              allowMultiple={true}
            />
          </div>
        ))}

        {items.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p>No protection items added yet. Click &ldquo;Add Item&rdquo; to get started.</p>
          </div>
        )}
      </div>
    </div>
  );
};