'use client';

import React from 'react';

interface ProgressIndicatorProps {
  currentSection: number;
  totalSections: number;
  sectionNames?: string[];
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  currentSection,
  totalSections,
  sectionNames = []
}) => {
  const progressPercentage = (currentSection / totalSections) * 100;

  const getSectionTitle = (index: number): string => {
    if (sectionNames[index]) {
      return sectionNames[index].charAt(0).toUpperCase() + sectionNames[index].slice(1);
    }
    return `Section ${index + 1}`;
  };

  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          {getSectionTitle(currentSection - 1)}
        </h2>
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {currentSection} of {totalSections}
        </span>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4">
        <div
          className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-in-out"
          style={{ width: `${progressPercentage}%` }}
        />
      </div>

      {/* Section Indicators */}
      <div className="flex justify-between">
        {Array.from({ length: totalSections }, (_, index) => {
          const sectionNumber = index + 1;
          const isCompleted = sectionNumber < currentSection;
          const isCurrent = sectionNumber === currentSection;

          return (
            <div key={index} className="flex flex-col items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium mb-2 transition-colors duration-200 ${
                  isCompleted
                    ? 'bg-green-600 text-white'
                    : isCurrent
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
                }`}
              >
                {isCompleted ? '✓' : sectionNumber}
              </div>
              <span className={`text-xs text-center max-w-20 ${
                isCurrent
                  ? 'text-blue-600 dark:text-blue-400 font-medium'
                  : 'text-gray-500 dark:text-gray-400'
              }`}>
                {getSectionTitle(index)}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};