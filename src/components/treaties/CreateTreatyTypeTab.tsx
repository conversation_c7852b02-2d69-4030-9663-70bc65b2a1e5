'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Edit } from 'lucide-react';

interface TreatyType {
    id?: string;
    name: string;
    description: string;
}

interface CreateTreatyTypeTabProps {
    onCreateSuccess?: () => void;
    onUpdateSuccess?: () => void;
    editingTreatyType?: TreatyType | null;
}

export const CreateTreatyTypeTab: React.FC<CreateTreatyTypeTabProps> = ({
    onCreateSuccess,
    onUpdateSuccess,
    editingTreatyType
}) => {
    const [name, setName] = useState(editingTreatyType?.name || '');
    const [description, setDescription] = useState(editingTreatyType?.description || '');
    const [creating, setCreating] = useState(false);
    const [updating, setUpdating] = useState(false);

    // Update form fields when editingTreatyType changes
    useEffect(() => {
        if (editingTreatyType) {
            setName(editingTreatyType.name || '');
            setDescription(editingTreatyType.description || '');
        }
    }, [editingTreatyType]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!name.trim()) {
            toast.error('Please enter a name for the treaty type.');
            return;
        }

        if (editingTreatyType?.id) {
            // Update existing treaty type
            setUpdating(true);
            try {
                const response = await fetch(`/api/treaty-types/${editingTreatyType.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: name.trim(),
                        description: description.trim(),
                    }),
                });

                if (response.ok) {
                    toast.success('Treaty type updated successfully!');
                    setName('');
                    setDescription('');
                    onUpdateSuccess?.();
                } else {
                    const errorData = await response.json();
                    toast.error(errorData.error || 'Failed to update treaty type');
                }
            } catch (error) {
                console.error('Error updating treaty type:', error);
                toast.error('Failed to update treaty type');
            } finally {
                setUpdating(false);
            }
        } else {
            // Create new treaty type
            setCreating(true);
            try {
                const response = await fetch('/api/treaty-types', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: name.trim(),
                        description: description.trim(),
                    }),
                });

                if (response.ok) {
                    toast.success('Treaty type created successfully!');
                    setName('');
                    setDescription('');
                    onCreateSuccess?.();
                } else {
                    const errorData = await response.json();
                    toast.error(errorData.error || 'Failed to create treaty type');
                }
            } catch (error) {
                console.error('Error creating treaty type:', error);
                toast.error('Failed to create treaty type');
            } finally {
                setCreating(false);
            }
        }
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-4">
            <div>
                <Label htmlFor="treatyTypeName" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    Treaty Type Name *
                </Label>
                <Input
                    id="treatyTypeName"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Enter treaty type name (e.g., Peace Covenant, Trade Agreement)"
                    required
                    className="w-full"
                />
            </div>



            <div>
                <Label htmlFor="treatyTypeDescription" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    Description
                </Label>
                <Textarea
                    id="treatyTypeDescription"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Enter a detailed description of this treaty type"
                    rows={4}
                    className="w-full"
                />
            </div>

            <div className="flex justify-end">
                <Button
                    type="submit"
                    disabled={creating || updating}
                    className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                >
                    {creating || updating ? (
                        <div className="flex items-center">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            {editingTreatyType ? 'Updating...' : 'Creating...'}
                        </div>
                    ) : (
                        <>
                            {editingTreatyType ? (
                                <>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Update Treaty Type
                                </>
                            ) : (
                                <>
                                    <Plus className="h-4 w-4 mr-2" />
                                    Create Treaty Type
                                </>
                            )}
                        </>
                    )}
                </Button>
            </div>
        </form>
    );
};