'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Combobox, ComboboxInput, ComboboxOptions, ComboboxOption } from '@headlessui/react';

interface NationTreaty {
  id: string;
  name: string;
  officialName: string;
  status: string;
  description?: string;
}

interface NationTreatySearchAutocompleteProps {
  value: string;
  onChange: (treatyId: string) => void;
  onTreatySelect?: (treaty: NationTreaty) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  statusFilter?: string[];
}

export function NationTreatySearchAutocomplete({
  value,
  onChange,
  onTreatySelect,
  disabled = false,
  placeholder = 'Search nation/tribe treaties...',
  className = '',
  statusFilter = []
}: NationTreatySearchAutocompleteProps) {
  const [query, setQuery] = useState('');
  const [treaties, setTreaties] = useState<NationTreaty[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Debounced search function
  const searchTreaties = useCallback(async (searchQuery: string) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        search: searchQuery,
        limit: '20'
      });

      if (statusFilter.length > 0) {
        params.append('status', statusFilter.join(','));
      }

      const response = await fetch(`/api/nation-treaties?${params}`, {
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Handle response format
      const treatiesData = data.data || data.treaties || [];
      setTreaties(Array.isArray(treatiesData) ? treatiesData : []);
    } catch (err) {
      if (err instanceof Error && err.name !== 'AbortError') {
        setError(err.message);
        console.error('Error searching treaties:', err);
      }
    } finally {
      setLoading(false);
    }
  }, [statusFilter]);

  // Effect for debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query.trim().length >= 2) {
        searchTreaties(query);
      } else {
        setTreaties([]);
      }
    }, 300); // 300ms debounce

    return () => {
      clearTimeout(timeoutId);
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [query, searchTreaties]);

  // Get selected treaty
  const selectedTreaty = treaties.find(t => t.id === value);

  // Filter treaties based on query (client-side filtering)
  const filteredTreaties = query.trim() === '' 
    ? treaties 
    : treaties.filter(treaty =>
        treaty.name.toLowerCase().includes(query.toLowerCase()) ||
        treaty.officialName.toLowerCase().includes(query.toLowerCase()) ||
        (treaty.description && treaty.description.toLowerCase().includes(query.toLowerCase()))
      );

  return (
    <div className={`relative ${className}`}>
      <Combobox value={value} onChange={onChange} disabled={disabled}>
        <div className="relative">
          <ComboboxInput
            displayValue={(treatyId: string) => {
              const treaty = treaties.find(t => t.id === treatyId);
              return treaty ? treaty.name : '';
            }}
            onChange={(event) => setQuery(event.target.value)}
            placeholder={disabled ? 'Search disabled' : placeholder}
            disabled={disabled}
            className={`
              w-full px-3 py-2 border border-gray-300 dark:border-gray-600 
              rounded-md shadow-sm focus:outline-none focus:ring-slate-500 
              focus:border-slate-500 dark:bg-gray-700 dark:text-white
              disabled:opacity-50 disabled:cursor-not-allowed
              ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
            `}
          />
          
          {/* Loading indicator */}
          {loading && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <div className="animate-spin h-4 w-4 border-2 border-slate-500 border-t-transparent rounded-full"></div>
            </div>
          )}
        </div>

        <ComboboxOptions
          className={`
            absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 
            border border-gray-300 dark:border-gray-600 rounded-md 
            shadow-lg max-h-60 overflow-auto
            ${filteredTreaties.length === 0 && !loading && !error ? 'hidden' : ''}
          `}
        >
          {error && (
            <div className="px-3 py-2 text-sm text-red-600 dark:text-red-400 border-b border-gray-200 dark:border-gray-600">
              Error: {error}
            </div>
          )}
          
          {loading && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              Searching...
            </div>
          )}
          
          {!loading && !error && filteredTreaties.length === 0 && query.trim().length >= 2 && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              No treaties found
            </div>
          )}
          
          {!loading && !error && query.trim().length < 2 && query.trim().length > 0 && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              Type at least 2 characters to search
            </div>
          )}
          
          {filteredTreaties.map((treaty) => (
            <ComboboxOption
              key={treaty.id}
              value={treaty.id}
              className={({ active }) => `
                px-3 py-2 text-sm cursor-pointer transition-colors duration-200
                ${active 
                  ? 'bg-slate-100 dark:bg-slate-600 text-slate-900 dark:text-white' 
                  : 'text-gray-900 dark:text-gray-100'
                }
              `}
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="font-medium">{treaty.name}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {treaty.officialName}
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    <span className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${
                      treaty.status === 'ACTIVE'
                        ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                        : treaty.status === 'PENDING'
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                        : treaty.status === 'SUSPENDED'
                        ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                    }`}>
                      {treaty.status}
                    </span>
                  </div>
                </div>
              </div>
            </ComboboxOption>
          ))}
        </ComboboxOptions>
      </Combobox>
      
      {/* Error message display */}
      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}

      {/* Selected treaty info */}
      {selectedTreaty && (
        <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-md text-sm">
          <div className="font-medium text-gray-900 dark:text-white">
            {selectedTreaty.name}
          </div>
          <div className="text-gray-600 dark:text-gray-400">
            {selectedTreaty.officialName}
          </div>
        </div>
      )}
    </div>
  );
}