'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { TreatyTypeDetailsData } from '@/types/user-treaty-assignment';
import { DigitalSignature } from './DigitalSignature';
import { FileUpload } from './FileUpload';

interface FinalConfirmationSectionProps {
  formData: TreatyTypeDetailsData;
  onChange: (data: Partial<TreatyTypeDetailsData>) => void;
  onSubmit: () => void;
  onSave: () => void;
  errors: Record<string, string>;
  isSubmitting: boolean;
}

export const FinalConfirmationSection: React.FC<FinalConfirmationSectionProps> = ({
  formData,
  onChange,
  onSubmit,
  onSave,
  errors,
  isSubmitting
}) => {
  const handleDeclarationChange = (checked: boolean) => {
    onChange({ declarationAccepted: checked });
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Final Confirmation
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          Please review and confirm your treaty application details.
        </p>
      </div>

      {/* Declaration Statement */}
      <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <h4 className="font-medium text-gray-900 dark:text-white mb-3">
          Declaration Statement
        </h4>
        <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
          I hereby declare that all information provided in this treaty application is true and accurate to the best of my knowledge. I understand that providing false information may result in the rejection of this application and potential legal consequences. I agree to abide by the terms and conditions of the NWA Alliance treaty protection program.
        </p>
      </div>

      {/* Declaration Acceptance */}
      <div className="space-y-3">
        <div className="flex items-start space-x-3">
          <Checkbox
            id="declarationAccepted"
            checked={formData.declarationAccepted || false}
            onCheckedChange={handleDeclarationChange}
            className={errors.declarationAccepted ? 'border-red-500' : ''}
          />
          <div className="grid gap-1.5 leading-none">
            <Label
              htmlFor="declarationAccepted"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              I accept the declaration statement *
            </Label>
            {errors.declarationAccepted && (
              <p className="text-red-500 text-sm">{errors.declarationAccepted}</p>
            )}
          </div>
        </div>
      </div>

      {/* Digital Signature */}
      <DigitalSignature
        value={formData.signatureData}
        onChange={(signatureData) => onChange({ signatureData })}
      />

      {/* File Attachments */}
      <FileUpload
        value={formData.attachmentPaths || []}
        onChange={(files) => onChange({ attachmentPaths: files })}
        accept="image/*,.pdf,.doc,.docx"
        maxFiles={10}
        maxSize={10}
        label="Optional Attachments"
        description="Upload supporting documents (identity proof, business registration, residence proof, etc.)"
        showPreview={true}
        allowMultiple={true}
      />

      {/* Usership Identification Clause */}
      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
        <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
          Usership Identification Clause
        </h4>
        <p className="text-sm text-blue-800 dark:text-blue-200 leading-relaxed">
          Upon approval, you will receive a usership identification card containing your personal information, treaty details, and protection status. This card serves as official documentation of your NWA Alliance usership and treaty protections.
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onSave}
          disabled={isSubmitting}
        >
          Save Draft
        </Button>
        <Button
          type="button"
          onClick={onSubmit}
          disabled={isSubmitting || !formData.declarationAccepted}
          className="bg-green-600 hover:bg-green-700"
        >
          {isSubmitting ? 'Submitting...' : 'Submit Application'}
        </Button>
      </div>
    </div>
  );
};