'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, X } from 'lucide-react';
import { TreatyTypeDetailsData } from '@/types/user-treaty-assignment';
import { FileUpload } from './FileUpload';

interface ApplicantInformationSectionProps {
  formData: TreatyTypeDetailsData;
  onChange: (data: Partial<TreatyTypeDetailsData>) => void;
  errors: Record<string, string>;
}

export const ApplicantInformationSection: React.FC<ApplicantInformationSectionProps> = ({
  formData,
  onChange,
  errors
}) => {
  const handleInputChange = (field: keyof TreatyTypeDetailsData, value: any) => {
    onChange({ [field]: value });
  };

  const handlePhoneChange = (index: number, field: string, value: any) => {
    const phones = [...(formData.phoneNumbers || [])];
    if (!phones[index]) {
      phones[index] = { number: '', type: 'mobile', primary: false };
    }
    phones[index] = { ...phones[index], [field]: value };
    onChange({ phoneNumbers: phones });
  };

  const addPhone = () => {
    const phones = [...(formData.phoneNumbers || [])];
    phones.push({ number: '', type: 'mobile', primary: false });
    onChange({ phoneNumbers: phones });
  };

  const removePhone = (index: number) => {
    const phones = [...(formData.phoneNumbers || [])];
    phones.splice(index, 1);
    onChange({ phoneNumbers: phones });
  };

  const setPrimaryPhone = (index: number) => {
    const phones = [...(formData.phoneNumbers || [])];
    phones.forEach((phone, i) => {
      phone.primary = i === index;
    });
    onChange({ phoneNumbers: phones });
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Applicant Information
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          Please provide your personal information and identification details.
        </p>
      </div>

      {/* Full Legal Name */}
      <div className="space-y-2">
        <Label htmlFor="fullLegalName" className="text-sm font-medium">
          Full Legal Name *
        </Label>
        <Input
          id="fullLegalName"
          type="text"
          value={formData.fullLegalName || ''}
          onChange={(e) => handleInputChange('fullLegalName', e.target.value)}
          placeholder="Enter your full legal name"
          className={errors.fullLegalName ? 'border-red-500' : ''}
        />
        {errors.fullLegalName && (
          <p className="text-red-500 text-sm">{errors.fullLegalName}</p>
        )}
      </div>

      {/* Date of Birth */}
      <div className="space-y-2">
        <Label htmlFor="dateOfBirth" className="text-sm font-medium">
          Date of Birth
        </Label>
        <Input
          id="dateOfBirth"
          type="date"
          value={formData.dateOfBirth || ''}
          onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
        />
      </div>

      {/* Gender/Identity */}
      <div className="space-y-2">
        <Label htmlFor="genderIdentity" className="text-sm font-medium">
          Gender / Identity
        </Label>
        <Select
          value={formData.genderIdentity || ''}
          onValueChange={(value) => handleInputChange('genderIdentity', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select gender/identity" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="male">Male</SelectItem>
            <SelectItem value="female">Female</SelectItem>
            <SelectItem value="non-binary">Non-binary</SelectItem>
            <SelectItem value="other">Other</SelectItem>
            <SelectItem value="prefer-not-to-say">Prefer not to say</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Phone Numbers */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Label className="text-sm font-medium">Phone Number(s)</Label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addPhone}
            className="flex items-center space-x-1"
          >
            <Plus className="w-4 h-4" />
            <span>Add Phone</span>
          </Button>
        </div>

        {(formData.phoneNumbers || []).map((phone, index) => (
          <div key={index} className="flex space-x-2 items-end">
            <div className="flex-1 space-y-2">
              <Label className="text-xs text-gray-600">Phone {index + 1}</Label>
              <Input
                type="tel"
                value={phone.number}
                onChange={(e) => handlePhoneChange(index, 'number', e.target.value)}
                placeholder="Enter phone number"
              />
            </div>
            <div className="w-32 space-y-2">
              <Label className="text-xs text-gray-600">Type</Label>
              <Select
                value={phone.type}
                onValueChange={(value) => handlePhoneChange(index, 'type', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="mobile">Mobile</SelectItem>
                  <SelectItem value="home">Home</SelectItem>
                  <SelectItem value="work">Work</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex space-x-1">
              <Button
                type="button"
                variant={phone.primary ? "default" : "outline"}
                size="sm"
                onClick={() => setPrimaryPhone(index)}
              >
                Primary
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => removePhone(index)}
                className="text-red-600 hover:text-red-700"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        ))}

        {(formData.phoneNumbers || []).length === 0 && (
          <p className="text-gray-500 text-sm italic">No phone numbers added yet.</p>
        )}
      </div>

      {/* Email Address */}
      <div className="space-y-2">
        <Label htmlFor="email" className="text-sm font-medium">
          Email Address *
        </Label>
        <Input
          id="email"
          type="email"
          value={formData.email || ''}
          onChange={(e) => handleInputChange('email', e.target.value)}
          placeholder="Enter your email address"
          className={errors.email ? 'border-red-500' : ''}
        />
        {errors.email && (
          <p className="text-red-500 text-sm">{errors.email}</p>
        )}
      </div>

      {/* Identification Number */}
      <div className="space-y-2">
        <Label htmlFor="identificationNumber" className="text-sm font-medium">
          Identification (Passport/ID #)
        </Label>
        <Input
          id="identificationNumber"
          type="text"
          value={formData.identificationNumber || ''}
          onChange={(e) => handleInputChange('identificationNumber', e.target.value)}
          placeholder="Enter passport or ID number"
        />
      </div>

      {/* Photograph Upload */}
      <FileUpload
        value={formData.photographPath ? [formData.photographPath] : []}
        onChange={(files) => handleInputChange('photographPath', files[0] || '')}
        accept="image/*"
        maxFiles={1}
        maxSize={5}
        label="Photograph (for Usership ID)"
        description="Upload a clear photo for your usership ID"
        showPreview={true}
        allowMultiple={false}
      />
    </div>
  );
};