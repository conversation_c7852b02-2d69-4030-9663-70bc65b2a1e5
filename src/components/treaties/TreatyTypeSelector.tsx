'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, Search, X, Plus } from 'lucide-react';

interface TreatyType {
  id: string;
  name: string;
  description: string;
  category: string;
  status: string;
  isActive: boolean;
}

interface TreatyTypeSelectorProps {
  selectedTypes: string[];
  onTypesChange: (typeIds: string[]) => void;
  disabled?: boolean;
}

export const TreatyTypeSelector: React.FC<TreatyTypeSelectorProps> = ({
  selectedTypes,
  onTypesChange,
  disabled = false
}) => {
  const [treatyTypes, setTreatyTypes] = useState<TreatyType[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [filteredTypes, setFilteredTypes] = useState<TreatyType[]>([]);
  const [customTypeInput, setCustomTypeInput] = useState('');
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const treatyTypesRef = useRef<TreatyType[]>([]);

  useEffect(() => {
    fetchTreatyTypes();
  }, []);

  useEffect(() => {
    const searchTreatyTypes = async () => {
      if (searchQuery.trim()) {
        setSearchLoading(true);
        try {
          console.log('Searching treaty types with query:', searchQuery);
          const response = await fetch(`/api/treaty-types?status=APPROVED&isActive=true&search=${encodeURIComponent(searchQuery)}`);
          if (response.ok) {
            const data = await response.json();
            console.log('Search results:', data.treatyTypes || []);
            const searchResults = data.treatyTypes || [];
            setFilteredTypes(searchResults);
            
            // Merge search results into main treatyTypes array to avoid "Unknown" labels
            setTreatyTypes(prevTypes => {
              const existingIds = new Set(prevTypes.map((t: TreatyType) => t.id));
              const newTypes = searchResults.filter((t: TreatyType) => !existingIds.has(t.id));
              const updatedTypes = [...prevTypes, ...newTypes];
              treatyTypesRef.current = updatedTypes;
              return updatedTypes;
            });
          } else {
            console.error('Failed to search treaty types:', response.status);
            setFilteredTypes([]);
          }
        } catch (error) {
          console.error('Error searching treaty types:', error);
          setFilteredTypes([]);
        } finally {
          setSearchLoading(false);
        }
      } else {
        // When no search query, show the current treatyTypes from ref
        setFilteredTypes(treatyTypesRef.current);
      }
    };

    const timeoutId = setTimeout(searchTreatyTypes, 300); // Debounce search
    return () => clearTimeout(timeoutId);
  }, [searchQuery]); // Only depend on searchQuery

  const fetchTreatyTypes = async () => {
    try {
      console.log('Fetching treaty types from API...');
      const response = await fetch('/api/treaty-types?status=APPROVED&isActive=true');
      if (response.ok) {
        const data = await response.json();
        console.log('Loaded treaty types:', data.treatyTypes || []);
        const types = data.treatyTypes || [];
        setTreatyTypes(types);
        setFilteredTypes(types);
        treatyTypesRef.current = types;
      } else {
        console.error('Failed to fetch treaty types:', response.status);
        setTreatyTypes([]);
        setFilteredTypes([]);
        treatyTypesRef.current = [];
      }
    } catch (error) {
      console.error('Error fetching treaty types:', error);
      setTreatyTypes([]);
      setFilteredTypes([]);
      treatyTypesRef.current = [];
    } finally {
      setLoading(false);
      console.log('Treaty types loading complete');
    }
  };

  const handleTypeToggle = (typeId: string) => {
    if (selectedTypes.includes(typeId)) {
      onTypesChange(selectedTypes.filter(id => id !== typeId));
    } else {
      onTypesChange([...selectedTypes, typeId]);
    }
  };

  const removeType = (typeId: string) => {
    onTypesChange(selectedTypes.filter(id => id !== typeId));
  };

  const handleAddCustomType = () => {
    if (customTypeInput.trim()) {
      // Create a temporary ID for the custom type
      const customTypeId = `custom-${Date.now()}`;
      onTypesChange([...selectedTypes, customTypeId]);
      setCustomTypeInput('');
      setShowCustomInput(false);
    }
  };

  const getSelectedTypeNames = () => {
    console.log('Getting selected type names:', {
      selectedTypes,
      treatyTypes: treatyTypes.map(t => ({id: t.id, name: t.name})),
      treatyTypesLoaded: treatyTypes.length > 0
    });
    
    return selectedTypes.map(id => {
      const type = treatyTypes.find(t => t.id === id);
      if (!type) {
        console.log('Unknown treaty type ID:', id, 'Available types:', treatyTypes.map(t => ({id: t.id, name: t.name})));
      }
      return type ? type.name : id.startsWith('custom-') ? id.replace('custom-', 'Custom: ') : 'Unknown';
    });
  };

  if (loading) {
    return (
      <div className="border border-gray-300 dark:border-gray-600 rounded-md p-3 bg-gray-50 dark:bg-gray-700">
        <div className="animate-pulse flex space-x-4">
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  // If no treaty types are available, show text input for custom types
  if (treatyTypes.length === 0 && !showCustomInput) {
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
          Treaty Types
        </label>
        <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
          No treaty types available. You can add a custom treaty type:
        </div>
        <button
          type="button"
          onClick={() => setShowCustomInput(true)}
          disabled={disabled}
          className="px-3 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 text-sm"
        >
          Add Custom Treaty Type
        </button>
      </div>
    );
  }

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
        Treaty Types
      </label>
      
      {/* Selected types display */}
      {selectedTypes.length > 0 && (
        <div className="mb-2 flex flex-wrap gap-2">
          {getSelectedTypeNames().map((name, index) => (
            <span
              key={selectedTypes[index]}
              className="inline-flex items-center px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-800 dark:text-slate-200 text-sm rounded-md"
            >
              {name}
              <button
                type="button"
                onClick={() => removeType(selectedTypes[index])}
                className="ml-1 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300"
                disabled={disabled}
              >
                <X className="h-3 w-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      {/* Custom treaty type input */}
      {showCustomInput && (
        <div className="mb-3 p-3 border border-slate-300 dark:border-slate-600 rounded-md bg-slate-50 dark:bg-slate-800">
          <div className="flex items-end space-x-2">
            <div className="flex-1">
              <label className="block text-xs font-medium text-slate-600 dark:text-slate-400 mb-1">
                Custom Treaty Type Name
              </label>
              <input
                type="text"
                value={customTypeInput}
                onChange={(e) => setCustomTypeInput(e.target.value)}
                placeholder="Enter custom treaty type name"
                className="w-full px-2 py-1 border border-slate-300 dark:border-slate-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-slate-500 dark:bg-slate-700 dark:text-white"
                autoFocus
              />
            </div>
            <button
              type="button"
              onClick={handleAddCustomType}
              disabled={!customTypeInput.trim() || disabled}
              className="px-3 py-1 bg-slate-700 text-white rounded text-sm hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50"
            >
              Add
            </button>
            <button
              type="button"
              onClick={() => setShowCustomInput(false)}
              className="px-3 py-1 border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 rounded text-sm hover:bg-slate-100 dark:hover:bg-slate-700"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Dropdown trigger */}
      <div className="flex space-x-2">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          disabled={disabled}
          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white flex items-center justify-between"
        >
          <span className="text-left">
            {selectedTypes.length === 0 ? 'Select treaty types...' : `${selectedTypes.length} type(s) selected`}
          </span>
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </button>
        <button
          type="button"
          onClick={() => setShowCustomInput(true)}
          disabled={disabled}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-slate-700 dark:text-slate-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
          title="Add custom treaty type"
        >
          <Plus className="h-4 w-4" />
        </button>
      </div>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-slate-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto">
          {/* Search input */}
          <div className="sticky top-0 bg-white dark:bg-slate-800 p-2 border-b border-gray-300 dark:border-gray-600">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search treaty types..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
              />
            </div>
          </div>

          {/* Type list */}
          <div className="p-1">
            {searchLoading ? (
              <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-slate-600 mr-2"></div>
                Searching...
              </div>
            ) : filteredTypes.length === 0 ? (
              <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                {searchQuery ? 'No matching treaty types found' : 'No treaty types available'}
              </div>
            ) : (
              filteredTypes.map((type) => (
                <label
                  key={type.id}
                  className="flex items-center px-3 py-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={selectedTypes.includes(type.id)}
                    onChange={() => handleTypeToggle(type.id)}
                    className="rounded border-gray-300 text-slate-600 focus:ring-slate-500"
                  />
                  <div className="ml-3 flex-1">
                    <div className="text-sm font-medium text-slate-800 dark:text-slate-200">
                      {type.name}
                    </div>
                    <div className="text-xs text-slate-500 dark:text-slate-400">
                      {type.category} • {type.description}
                    </div>
                  </div>
                </label>
              ))
            )}
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};