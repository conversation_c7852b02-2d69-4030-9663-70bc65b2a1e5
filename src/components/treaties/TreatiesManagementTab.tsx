'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  FileText,
  Tag,
  Plus,
  Edit,
  Trash2,
  Search,
  X,
  ChevronUp,
  ChevronDown,
  ChevronsUpDown
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
// Note: Do NOT import the Nation treaty creator here.
// This tab manages generic/user treaties via /api/treaties.
// The nation treaty creator lives in components/treaties/CreateNationTreatyTab
// and posts to /api/nation-treaties, so importing it here caused type and domain mismatches.
import { CreateTreatyTypeTab } from './CreateTreatyTypeTab';

interface Treaty {
  id: string;
  title: string;
  notes: string;
  treatyTypeId?: string;
  treatyTypeIds?: string[];
  treatyTypes?: Array<{ id: string; name: string }>;
  treatyTypeName?: string;
  status: string;
  signedDate: string;
  expirationDate: string;
  renewalDate: string;
  createdAt: string;
  updatedAt: string;
}

interface TreatyType {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  treatyCount: number;
  activeTreaties: number;
  totalAssigned?: number;
  userTreatyNumbersCount?: number;
  assignedUsersCount?: number;
}

export const TreatiesManagementTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'treaties' | 'types' | 'assign' | 'reassign'>('treaties');

  // Treaties state
  const [treaties, setTreaties] = useState<Treaty[]>([]);
  const [treatiesSearch, setTreatiesSearch] = useState('');
  const [loadingTreaties, setLoadingTreaties] = useState(false);

  // Treaty Types state
  const [treatyTypes, setTreatyTypes] = useState<TreatyType[]>([]);
  const [treatyTypesSearch, setTreatyTypesSearch] = useState('');
  const [newTreatyType, setNewTreatyType] = useState({ name: '', description: '' });
  const [loadingTreatyTypes, setLoadingTreatyTypes] = useState(false);

  // Sorting state for treaty types
  const [treatyTypesSortBy, setTreatyTypesSortBy] = useState<'name' | 'treatyCount' | null>(null);
  const [treatyTypesSortOrder, setTreatyTypesSortOrder] = useState<'asc' | 'desc'>('asc');

  // Pagination states
  const [treatiesPage, setTreatiesPage] = useState(1);
  const [treatiesTotalPages, setTreatiesTotalPages] = useState(1);
  const [treatiesTotalCount, setTreatiesTotalCount] = useState(0);
  const [treatyTypesPage, setTreatyTypesPage] = useState(1);
  const [treatyTypesTotalPages, setTreatyTypesTotalPages] = useState(1);
  const [treatyTypesTotalCount, setTreatyTypesTotalCount] = useState(0);
  const itemsPerPage = 10;

  // Edit state
  const [editingTreaty, setEditingTreaty] = useState<{
    id: string;
    name: string;
    description: string;
    treatyTypeIds?: string[];
  } | null>(null);

  // Local form state for editing/creating generic treaties (/api/treaties)
  const [editForm, setEditForm] = useState<{ name: string; description: string }>({ name: '', description: '' });
  const [createForm, setCreateForm] = useState<{ name: string; description: string }>({ name: '', description: '' });
  const [isSavingEdit, setIsSavingEdit] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const [editingTreatyType, setEditingTreatyType] = useState<TreatyType | null>(null);

  // Assign/Reassign state
  const [selectedTreatyForAssignment, setSelectedTreatyForAssignment] = useState<Treaty | null>(null);
  const [availableTypesForAssignment, setAvailableTypesForAssignment] = useState<TreatyType[]>([]);
  const [selectedTypesToAssign, setSelectedTypesToAssign] = useState<string[]>([]);
  const [assigningTypes, setAssigningTypes] = useState(false);
  
  const [selectedTreatyForReassignment, setSelectedTreatyForReassignment] = useState<Treaty | null>(null);
  const [currentAssignedTypes, setCurrentAssignedTypes] = useState<TreatyType[]>([]);
  const [selectedTypesToRemove, setSelectedTypesToRemove] = useState<string[]>([]);
  const [reassigningTypes, setReassigningTypes] = useState(false);

  // Treaties functions
  const loadTreaties = useCallback(async (page?: number, search?: string) => {
    const currentPage = page ?? treatiesPage;
    const currentSearch = search ?? treatiesSearch;

    setLoadingTreaties(true);
    try {
      const response = await fetch(`/api/treaties?page=${currentPage}&limit=${itemsPerPage}&search=${encodeURIComponent(currentSearch)}`);
      if (response.ok) {
        const data = await response.json();
        setTreaties(data.treaties || []);
        setTreatiesTotalPages(data.pagination?.totalPages || 1);
        setTreatiesTotalCount(data.pagination?.totalCount || 0);
        setTreatiesPage(data.pagination?.currentPage || currentPage);
      } else {
        console.error('Failed to load treaties - Server returned:', response.status, response.statusText);
        // Set empty state instead of logging error
        setTreaties([]);
        setTreatiesTotalPages(1);
        setTreatiesTotalCount(0);
      }
    } catch (error) {
      console.error('Error loading treaties:', error);
      // Set empty state on network error
      setTreaties([]);
      setTreatiesTotalPages(1);
      setTreatiesTotalCount(0);
    } finally {
      setLoadingTreaties(false);
    }
  }, [treatiesPage, treatiesSearch]);


  // Treaty Types functions
  const loadTreatyTypes = useCallback(async (page?: number, search?: string, sortBy?: string | null, sortOrder?: string) => {
    const currentPage = page ?? treatyTypesPage;
    const currentSearch = search ?? treatyTypesSearch;
    const currentSortBy = sortBy ?? treatyTypesSortBy;
    const currentSortOrder = sortOrder ?? treatyTypesSortOrder;

    setLoadingTreatyTypes(true);
    try {
      let url = `/api/treaty-types?page=${currentPage}&limit=${itemsPerPage}&search=${encodeURIComponent(currentSearch)}`;
      if (currentSortBy) {
        url += `&sortBy=${currentSortBy}&sortOrder=${currentSortOrder}`;
      }

      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setTreatyTypes(data.treatyTypes || []);
        setTreatyTypesTotalPages(data.pagination?.totalPages || 1);
        setTreatyTypesTotalCount(data.pagination?.totalCount || 0);
        setTreatyTypesPage(data.pagination?.currentPage || currentPage);
      } else {
        console.error('Failed to load treaty types');
      }
    } catch (error) {
      console.error('Error loading treaty types:', error);
    } finally {
      setLoadingTreatyTypes(false);
    }
  }, [treatyTypesPage, treatyTypesSearch, treatyTypesSortBy, treatyTypesSortOrder]);

  const handleCreateTreatyType = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newTreatyType.name.trim()) {
      alert('Please fill in the treaty type name');
      return;
    }

    try {
      const response = await fetch('/api/treaty-types', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newTreatyType),
      });

      if (response.ok) {
        alert('Treaty type created successfully');
        setNewTreatyType({ name: '', description: '' });
        loadTreatyTypes(1, '', treatyTypesSortBy || undefined, treatyTypesSortOrder);
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to create treaty type');
      }
    } catch (error) {
      console.error('Error creating treaty type:', error);
      alert('Failed to create treaty type');
    }
  };

  // Search effects
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (activeTab === 'treaties') {
        setTreatiesPage(1);
        loadTreaties(1, treatiesSearch);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [treatiesSearch, activeTab, loadTreaties]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (activeTab === 'types') {
        setTreatyTypesPage(1);
        loadTreatyTypes(1, treatyTypesSearch, treatyTypesSortBy || undefined, treatyTypesSortOrder);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [treatyTypesSearch, activeTab, loadTreatyTypes, treatyTypesSortBy, treatyTypesSortOrder]);

  // Load data on component mount and when tabs change
  useEffect(() => {
    if (activeTab === 'treaties') {
      loadTreaties(treatiesPage, treatiesSearch);
    } else if (activeTab === 'types') {
      loadTreatyTypes(treatyTypesPage, treatyTypesSearch, treatyTypesSortBy || undefined, treatyTypesSortOrder);
    }
  }, [activeTab, loadTreaties, loadTreatyTypes, treatiesPage, treatiesSearch, treatyTypesPage, treatyTypesSearch, treatyTypesSortBy, treatyTypesSortOrder]);

  const handleDeleteItem = async (type: 'treaty' | 'treatyType', id: string) => {
    if (!confirm(`Are you sure you want to delete this ${type}?`)) return;

    try {
      let url = '';
      if (type === 'treaty') url = `/api/treaties?id=${id}`;
      else if (type === 'treatyType') url = `/api/treaty-types?id=${id}`;

      const response = await fetch(url, { method: 'DELETE' });

      if (response.ok) {
        alert(`${type.charAt(0).toUpperCase() + type.slice(1)} deleted successfully`);
        if (type === 'treaty') loadTreaties(treatiesPage, treatiesSearch);
        if (type === 'treatyType') loadTreatyTypes(treatyTypesPage, treatyTypesSearch, treatyTypesSortBy || undefined, treatyTypesSortOrder);
      } else {
        const error = await response.json();
        alert(error.error || `Failed to delete ${type}`);
      }
    } catch (error) {
      console.error(`Error deleting ${type}:`, error);
      alert(`Failed to delete ${type}`);
    }
  };

  const handleForceDeleteTreatyType = async (id: string) => {
    if (!confirm('⚠️ FORCE DELETE: This will permanently remove the treaty type and ALL its associations (treaties, applications, details). This action cannot be undone. Are you sure?')) return;

    try {
      const response = await fetch(`/api/treaty-types/${id}/force-delete`, {
        method: 'DELETE',
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Treaty type force deleted successfully!\nRemoved:\n- ${result.deletedAssociations.treatyAssociations} treaty associations\n- ${result.deletedAssociations.detailsRecords} detail records\n- ${result.deletedAssociations.userTreatyTypes} user treaty types\n- ${result.deletedAssociations.userTreatyNumbers} user treaty numbers`);
        loadTreatyTypes(treatyTypesPage, treatyTypesSearch, treatyTypesSortBy || undefined, treatyTypesSortOrder);
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to force delete treaty type');
        console.error('Force delete error details:', error.details);
      }
    } catch (error) {
      console.error('Error force deleting treaty type:', error);
      alert('Failed to force delete treaty type');
    }
  };

  const handleEditTreaty = (treaty: Treaty) => {
    // Extract name and description from notes field
    // The notes field is stored as: "Name\n\nDescription"
    const notes = treaty.notes || '';
    const lines = notes.split('\n\n');
    const name = lines[0] || '';
    const description = lines.length > 1 ? lines.slice(1).join('\n\n') : '';

    setEditingTreaty({
      id: treaty.id,
      name: name,
      description: description,
      treatyTypeIds: treaty.treatyTypes ? treaty.treatyTypes.map(type => type.id) : treaty.treatyTypeIds || [],
    });
    setEditForm({ name, description });
  };

  const handleCancelEdit = () => {
    setEditingTreaty(null);
    setEditForm({ name: '', description: '' });
  };

  const handleUpdateSuccess = () => {
    setEditingTreaty(null);
    loadTreaties(treatiesPage, treatiesSearch);
  };

  const handleSubmitEdit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingTreaty) return;
    if (!editForm.name.trim()) {
      alert('Please provide a treaty name');
      return;
    }
    setIsSavingEdit(true);
    try {
      const response = await fetch(`/api/treaties?id=${editingTreaty.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: editForm.name.trim(), description: editForm.description.trim() })
      });
      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.error || 'Failed to update treaty');
      }
      handleUpdateSuccess();
    } catch (err: any) {
      console.error('Error updating treaty:', err);
      alert(err?.message || 'Failed to update treaty');
    } finally {
      setIsSavingEdit(false);
    }
  };

  const handleCreateTreaty = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!createForm.name.trim()) {
      alert('Please provide a treaty name');
      return;
    }
    setIsCreating(true);
    try {
      const response = await fetch('/api/treaties', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: createForm.name.trim(), description: createForm.description.trim() })
      });
      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.error || 'Failed to create treaty');
      }
      setCreateForm({ name: '', description: '' });
      loadTreaties(1, treatiesSearch);
    } catch (err: any) {
      console.error('Error creating treaty:', err);
      alert(err?.message || 'Failed to create treaty');
    } finally {
      setIsCreating(false);
    }
  };

  const handleEditTreatyType = (treatyType: TreatyType) => {
    setEditingTreatyType(treatyType);
    setNewTreatyType({
      name: treatyType.name,
      description: treatyType.description || ''
    });
  };

  const handleCancelEditTreatyType = () => {
    setEditingTreatyType(null);
    setNewTreatyType({ name: '', description: '' });
  };

  const handleUpdateTreatyType = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editingTreatyType) return;

    setLoadingTreatyTypes(true);
    try {
      const response = await fetch(`/api/treaty-types/${editingTreatyType.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newTreatyType.name,
          description: newTreatyType.description,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update treaty type');
      }

      // Reset form and reload
      setEditingTreatyType(null);
      setNewTreatyType({ name: '', description: '' });
      loadTreatyTypes(treatyTypesPage, treatyTypesSearch, treatyTypesSortBy || undefined, treatyTypesSortOrder);

    } catch (error: any) {
      console.error('Error updating treaty type:', error);
      alert(error.message || 'Failed to update treaty type');
    } finally {
      setLoadingTreatyTypes(false);
    }
  };

  // Sorting handler for treaty types
  const handleTreatyTypeSort = (column: 'name' | 'treatyCount') => {
    let newSortOrder: 'asc' | 'desc' = 'asc';

    if (treatyTypesSortBy === column) {
      // If clicking the same column, toggle sort order
      newSortOrder = treatyTypesSortOrder === 'asc' ? 'desc' : 'asc';
    }

    setTreatyTypesSortBy(column);
    setTreatyTypesSortOrder(newSortOrder);
    setTreatyTypesPage(1); // Reset to first page when sorting
    loadTreatyTypes(1, treatyTypesSearch, column, newSortOrder);
  };

  // Assignment functions
  const loadCurrentAssignments = async (treatyId: string) => {
    try {
      const response = await fetch(`/api/treaties/${treatyId}/types`);
      if (response.ok) {
        const data = await response.json();
        return data.treatyTypes || [];
      }
      return [];
    } catch (error) {
      console.error('Error loading current assignments:', error);
      return [];
    }
  };

  const loadAvailableTypesForAssignment = async (treatyId: string) => {
    try {
      // Get all treaty types
      const allTypesResponse = await fetch('/api/treaty-types?limit=100');
      if (!allTypesResponse.ok) return [];
      
      const allTypesData = await allTypesResponse.json();
      const allTypes = allTypesData.treatyTypes || [];

      // Get currently assigned types
      const assignedTypes = await loadCurrentAssignments(treatyId);
      const assignedTypeIds = assignedTypes.map((type: any) => type.id);

      // Filter out already assigned types
      return allTypes.filter((type: TreatyType) => !assignedTypeIds.includes(type.id));
    } catch (error) {
      console.error('Error loading available types:', error);
      return [];
    }
  };

  const handleTreatySelectionForAssignment = async (treaty: Treaty) => {
    setSelectedTreatyForAssignment(treaty);
    setSelectedTypesToAssign([]);
    
    // Load available types for this treaty
    const availableTypes = await loadAvailableTypesForAssignment(treaty.id);
    setAvailableTypesForAssignment(availableTypes);
  };

  const handleAssignTreatyTypes = async () => {
    if (!selectedTreatyForAssignment || selectedTypesToAssign.length === 0) {
      alert('Please select a treaty and at least one treaty type to assign');
      return;
    }

    setAssigningTypes(true);
    try {
      // Get current treaty type IDs
      const currentAssignments = await loadCurrentAssignments(selectedTreatyForAssignment.id);
      const currentTypeIds = currentAssignments.map((type: any) => type.id);
      
      // Combine current and new type IDs
      const updatedTypeIds = [...currentTypeIds, ...selectedTypesToAssign];

      const response = await fetch(`/api/treaties?id=${selectedTreatyForAssignment.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ treatyTypeIds: updatedTypeIds }),
      });

      if (response.ok) {
        alert('Treaty types assigned successfully');
        setSelectedTreatyForAssignment(null);
        setSelectedTypesToAssign([]);
        setAvailableTypesForAssignment([]);
        // Refresh treaties list
        loadTreaties(treatiesPage, treatiesSearch);
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to assign treaty types');
      }
    } catch (error) {
      console.error('Error assigning treaty types:', error);
      alert('Failed to assign treaty types');
    } finally {
      setAssigningTypes(false);
    }
  };

  const handleTreatySelectionForReassignment = async (treaty: Treaty) => {
    setSelectedTreatyForReassignment(treaty);
    setSelectedTypesToRemove([]);
    
    // Load current assignments for this treaty
    const assignedTypes = await loadCurrentAssignments(treaty.id);
    setCurrentAssignedTypes(assignedTypes);
  };

  const handleRemoveTreatyTypes = async () => {
    if (!selectedTreatyForReassignment || selectedTypesToRemove.length === 0) {
      alert('Please select a treaty and at least one treaty type to remove');
      return;
    }

    setReassigningTypes(true);
    try {
      // Get current treaty type IDs and filter out the ones to remove
      const currentTypeIds = currentAssignedTypes.map(type => type.id);
      const updatedTypeIds = currentTypeIds.filter(id => !selectedTypesToRemove.includes(id));

      const response = await fetch(`/api/treaties?id=${selectedTreatyForReassignment.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ treatyTypeIds: updatedTypeIds }),
      });

      if (response.ok) {
        alert('Treaty types removed successfully');
        // Refresh the current assignments
        const updatedAssignments = await loadCurrentAssignments(selectedTreatyForReassignment.id);
        setCurrentAssignedTypes(updatedAssignments);
        setSelectedTypesToRemove([]);
        // Refresh treaties list
        loadTreaties(treatiesPage, treatiesSearch);
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to remove treaty types');
      }
    } catch (error) {
      console.error('Error removing treaty types:', error);
      alert('Failed to remove treaty types');
    } finally {
      setReassigningTypes(false);
    }
  };

  const tabs = [
    { id: 'treaties', label: 'Treaties', icon: <FileText className="h-4 w-4" /> },
    { id: 'types', label: 'Treaty Types', icon: <Tag className="h-4 w-4" /> },
    { id: 'assign', label: 'Assign Treaty Types', icon: <Plus className="h-4 w-4" /> },
    { id: 'reassign', label: 'Reassign Treaty Types', icon: <Edit className="h-4 w-4" /> },
  ];

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm gap-2 ${activeTab === tab.id
                ? 'border-emerald-600 text-emerald-700 dark:text-emerald-300 dark:border-emerald-400'
                : 'border-transparent text-emerald-600 hover:text-emerald-700 hover:border-emerald-400 dark:text-emerald-400 dark:hover:text-emerald-300'
                }`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Blue instruction box - positioned after tabs */}
      {(activeTab === 'treaties' || activeTab === 'types') && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Manage User Treaties</h3>
          <p className="text-xs text-blue-600 dark:text-blue-300">
            View and manage existing user treaty assignments and their current status.
          </p>
        </div>
      )}

      {/* Treaties Tab */}
      {activeTab === 'treaties' && (
        <div className="space-y-6">
          {/* Create/Edit Treaty Form */}
          <div className="p-4 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800">
            {editingTreaty ? (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200">Edit Treaty</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCancelEdit}
                    className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <form onSubmit={handleSubmitEdit} className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="edit-treaty-name" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        Nation / Tribal Treaty *
                      </Label>
                      <Input
                        id="edit-treaty-name"
                        value={editForm.name}
                        onChange={(e) => setEditForm((prev) => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter treaty name"
                        className="w-full"
                      />
                    </div>

                    <div>
                      <Label htmlFor="edit-treaty-description" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        Description *
                      </Label>
                      <Textarea
                        id="edit-treaty-description"
                        value={editForm.description}
                        onChange={(e) => setEditForm((prev) => ({ ...prev, description: e.target.value }))}
                        rows={4}
                        placeholder="Enter treaty description"
                        className="w-full"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button type="button" variant="outline" onClick={handleCancelEdit}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSavingEdit}>
                      {isSavingEdit ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </div>
                </form>
              </div>
            ) : (
              <div>
                <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">Create Treaty</h3>
                <form onSubmit={handleCreateTreaty} className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="create-treaty-name" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        Nation / Tribal Treaty *
                      </Label>
                      <Input
                        id="create-treaty-name"
                        value={createForm.name}
                        onChange={(e) => setCreateForm((prev) => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter treaty name"
                        className="w-full"
                      />
                    </div>

                    <div>
                      <Label htmlFor="create-treaty-description" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        Description *
                      </Label>
                      <Textarea
                        id="create-treaty-description"
                        value={createForm.description}
                        onChange={(e) => setCreateForm((prev) => ({ ...prev, description: e.target.value }))}
                        rows={4}
                        placeholder="Enter treaty description"
                        className="w-full"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <Button type="submit" disabled={isCreating}>
                      {isCreating ? 'Creating...' : 'Create Treaty'}
                    </Button>
                  </div>
                </form>
              </div>
            )}
          </div>

          {/* Treaties List */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200">Existing Treaties</h3>
              <div className="flex items-center space-x-2">
                <Search className="h-4 w-4 text-slate-400 dark:text-slate-400" />
                <Input
                  placeholder="Search treaties..."
                  value={treatiesSearch}
                  onChange={(e) => setTreatiesSearch(e.target.value)}
                  className="w-64"
                />
              </div>
            </div>
            {loadingTreaties ? (
              <div className="text-center py-8 text-slate-500 dark:text-slate-400">Loading treaties...</div>
            ) : treaties.length === 0 ? (
              <div className="text-center py-8 text-slate-500 dark:text-slate-400">
                No treaties found.
              </div>
            ) : (
              <div className="space-y-4">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-slate-300 dark:divide-slate-600">
                    <thead className="bg-slate-50 dark:bg-slate-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                          Treaty Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-slate-800 divide-y divide-slate-300 dark:divide-slate-600">
                      {treaties.map((treaty) => (
                        <tr key={treaty.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-white">
                            {treaty.title}
                          </td>
                          <td className="px-6 py-4 text-sm text-slate-900 dark:text-white max-w-xs">
                            {treaty.treatyTypes && treaty.treatyTypes.length > 0 ? (
                              <div className="space-y-1">
                                {treaty.treatyTypes.length <= 3 ? (
                                  // Show all types if 3 or fewer
                                  treaty.treatyTypes.map((type, index) => (
                                    <span key={type.id} className="inline-block bg-gray-100 dark:bg-gray-700 text-xs px-2 py-1 rounded mr-1 mb-1">
                                      {type.name}
                                    </span>
                                  ))
                                ) : (
                                  // Show first 2 types + "and X more"
                                  <>
                                    {treaty.treatyTypes.slice(0, 2).map((type, index) => (
                                      <span key={type.id} className="inline-block bg-gray-100 dark:bg-gray-700 text-xs px-2 py-1 rounded mr-1 mb-1">
                                        {type.name}
                                      </span>
                                    ))}
                                    <span className="inline-block bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100 text-xs px-2 py-1 rounded mr-1 mb-1">
                                      +{treaty.treatyTypes.length - 2} more
                                    </span>
                                  </>
                                )}
                              </div>
                            ) : (
                              <span className="text-gray-500">{treaty.treatyTypeName || 'N/A'}</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-white">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${treaty.status === 'ACTIVE'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100'
                              : treaty.status === 'EXPIRED'
                                ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100'
                                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100'
                              }`}>
                              {treaty.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <Button
                              variant="outline"
                              size="sm"
                              className="mr-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300 border-slate-300 dark:border-slate-600"
                              onClick={() => handleEditTreaty(treaty)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-500 hover:text-red-700 border-slate-300 dark:border-slate-600"
                              onClick={() => handleDeleteItem('treaty', treaty.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Treaties Pagination */}
                {treatiesTotalPages > 1 && (
                  <div className="flex items-center justify-between px-4 py-3 bg-white dark:bg-slate-800 border-t border-slate-300 dark:border-slate-600">
                    <div className="flex items-center text-sm text-slate-700 dark:text-slate-300">
                      <span>
                        Showing {((treatiesPage - 1) * itemsPerPage) + 1} to {Math.min(treatiesPage * itemsPerPage, treatiesTotalCount)} of {treatiesTotalCount} treaties
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newPage = treatiesPage - 1;
                          setTreatiesPage(newPage);
                          loadTreaties(newPage, treatiesSearch);
                        }}
                        disabled={treatiesPage <= 1}
                        className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300"
                      >
                        Previous
                      </Button>

                      <div className="flex items-center space-x-1">
                        {Array.from({ length: Math.min(5, treatiesTotalPages) }, (_, i) => {
                          let pageNum;
                          if (treatiesTotalPages <= 5) {
                            pageNum = i + 1;
                          } else if (treatiesPage <= 3) {
                            pageNum = i + 1;
                          } else if (treatiesPage >= treatiesTotalPages - 2) {
                            pageNum = treatiesTotalPages - 4 + i;
                          } else {
                            pageNum = treatiesPage - 2 + i;
                          }

                          return (
                            <Button
                              key={pageNum}
                              variant={treatiesPage === pageNum ? "default" : "outline"}
                              size="sm"
                              onClick={() => {
                                setTreatiesPage(pageNum);
                                loadTreaties(pageNum, treatiesSearch);
                              }}
                              className={treatiesPage === pageNum
                                ? "bg-slate-700 text-white dark:bg-slate-300 dark:text-slate-800"
                                : "text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300"
                              }
                            >
                              {pageNum}
                            </Button>
                          );
                        })}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newPage = treatiesPage + 1;
                          setTreatiesPage(newPage);
                          loadTreaties(newPage, treatiesSearch);
                        }}
                        disabled={treatiesPage >= treatiesTotalPages}
                        className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300"
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Treaty Types Tab */}
      {activeTab === 'types' && (
        <div className="space-y-6">
          {/* Create/Edit Treaty Type Form */}
          <div className="p-4 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800">
            {editingTreatyType ? (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200">Edit Treaty Type</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCancelEditTreatyType}
                    className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <CreateTreatyTypeTab
                  editingTreatyType={editingTreatyType}
                  onUpdateSuccess={() => {
                    setEditingTreatyType(null);
                    loadTreatyTypes(treatyTypesPage, treatyTypesSearch, treatyTypesSortBy || undefined, treatyTypesSortOrder);
                  }}
                />
              </div>
            ) : (
              <div>
                <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">Create Treaty Type</h3>
                <CreateTreatyTypeTab onCreateSuccess={() => loadTreatyTypes(1, treatyTypesSearch, treatyTypesSortBy || undefined, treatyTypesSortOrder)} />
              </div>
            )}
          </div>

          {/* Treaty Types List */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200">Existing Treaty Types</h3>
              <div className="flex items-center space-x-2">
                <Search className="h-4 w-4 text-slate-400 dark:text-slate-400" />
                <Input
                  placeholder="Search treaty types..."
                  value={treatyTypesSearch}
                  onChange={(e) => setTreatyTypesSearch(e.target.value)}
                  className="w-64"
                />
              </div>
            </div>
            {loadingTreatyTypes ? (
              <div className="text-center py-8 text-slate-500 dark:text-slate-400">Loading treaty types...</div>
            ) : treatyTypes.length === 0 ? (
              <div className="text-center py-8 text-slate-500 dark:text-slate-400">
                No treaty types found.
              </div>
            ) : (
              <div className="space-y-4">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-slate-300 dark:divide-slate-600">
                    <thead className="bg-slate-50 dark:bg-slate-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                          <button
                            onClick={() => handleTreatyTypeSort('name')}
                            className="flex items-center space-x-1 hover:text-slate-900 dark:hover:text-slate-100 transition-colors"
                          >
                            <span>Treaty Type</span>
                            {treatyTypesSortBy === 'name' ? (
                              treatyTypesSortOrder === 'asc' ? (
                                <ChevronUp className="h-4 w-4" />
                              ) : (
                                <ChevronDown className="h-4 w-4" />
                              )
                            ) : (
                              <ChevronsUpDown className="h-4 w-4 opacity-50" />
                            )}
                          </button>
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                          Description
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                          <button
                            onClick={() => handleTreatyTypeSort('treatyCount')}
                            className="flex items-center space-x-1 hover:text-slate-900 dark:hover:text-slate-100 transition-colors"
                          >
                            <span>Treaties</span>
                            {treatyTypesSortBy === 'treatyCount' ? (
                              treatyTypesSortOrder === 'asc' ? (
                                <ChevronUp className="h-4 w-4" />
                              ) : (
                                <ChevronDown className="h-4 w-4" />
                              )
                            ) : (
                              <ChevronsUpDown className="h-4 w-4 opacity-50" />
                            )}
                          </button>
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-slate-800 divide-y divide-slate-300 dark:divide-slate-600">
                      {treatyTypes.map((type) => (
                        <tr key={type.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-white">
                            {type.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-white">
                            {type.description || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-white">
                            <div className="space-y-1">
                              <div>{type.totalAssigned ?? type.treatyCount} treaties ({type.activeTreaties} active)</div>
                              {((type.assignedUsersCount ?? 0) > 0) && (
                                <div className="text-xs text-amber-600 dark:text-amber-400">
                                  {type.assignedUsersCount ?? 0} user{(type.assignedUsersCount ?? 0) !== 1 ? 's' : ''} with treaty numbers
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <Button
                              variant="outline"
                              size="sm"
                              className="mr-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300 border-slate-300 dark:border-slate-600"
                              onClick={() => handleEditTreatyType(type)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-500 hover:text-red-700 border-slate-300 dark:border-slate-600"
                              onClick={() => handleDeleteItem('treatyType', type.id)}
                              disabled={type.treatyCount > 0 || (type.assignedUsersCount ?? 0) > 0}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                            {(type.treatyCount > 0 || (type.assignedUsersCount ?? 0) > 0) && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-red-600 hover:text-red-800 border-red-300 hover:border-red-500 ml-1"
                                onClick={() => handleForceDeleteTreatyType(type.id)}
                                title={`Force delete (removes ${type.treatyCount} treaty assignments and ${type.assignedUsersCount ?? 0} user treaty numbers)`}
                              >
                                <Trash2 className="h-3 w-3" />
                                <span className="text-xs ml-1">Force</span>
                              </Button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Treaty Types Pagination */}
                {treatyTypesTotalPages > 1 && (
                  <div className="flex items-center justify-between px-4 py-3 bg-white dark:bg-slate-800 border-t border-slate-300 dark:border-slate-600">
                    <div className="flex items-center text-sm text-slate-700 dark:text-slate-300">
                      <span>
                        Showing {((treatyTypesPage - 1) * itemsPerPage) + 1} to {Math.min(treatyTypesPage * itemsPerPage, treatyTypesTotalCount)} of {treatyTypesTotalCount} treaty types
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newPage = treatyTypesPage - 1;
                          setTreatyTypesPage(newPage);
                          loadTreatyTypes(newPage, treatyTypesSearch, treatyTypesSortBy || undefined, treatyTypesSortOrder);
                        }}
                        disabled={treatyTypesPage <= 1}
                        className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300"
                      >
                        Previous
                      </Button>

                      <div className="flex items-center space-x-1">
                        {Array.from({ length: Math.min(5, treatyTypesTotalPages) }, (_, i) => {
                          let pageNum;
                          if (treatyTypesTotalPages <= 5) {
                            pageNum = i + 1;
                          } else if (treatyTypesPage <= 3) {
                            pageNum = i + 1;
                          } else if (treatyTypesPage >= treatyTypesTotalPages - 2) {
                            pageNum = treatyTypesTotalPages - 4 + i;
                          } else {
                            pageNum = treatyTypesPage - 2 + i;
                          }

                          return (
                            <Button
                              key={pageNum}
                              variant={treatyTypesPage === pageNum ? "default" : "outline"}
                              size="sm"
                              onClick={() => {
                                setTreatyTypesPage(pageNum);
                                loadTreatyTypes(pageNum, treatyTypesSearch, treatyTypesSortBy || undefined, treatyTypesSortOrder);
                              }}
                              className={treatyTypesPage === pageNum
                                ? "bg-slate-700 text-white dark:bg-slate-300 dark:text-slate-800"
                                : "text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300"
                              }
                            >
                              {pageNum}
                            </Button>
                          );
                        })}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newPage = treatyTypesPage + 1;
                          setTreatyTypesPage(newPage);
                          loadTreatyTypes(newPage, treatyTypesSearch, treatyTypesSortBy || undefined, treatyTypesSortOrder);
                        }}
                        disabled={treatyTypesPage >= treatyTypesTotalPages}
                        className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300"
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Assign Treaty Types Tab */}
      {activeTab === 'assign' && (
        <div className="space-y-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Assign Treaty Types</h3>
            <p className="text-xs text-blue-600 dark:text-blue-300">
              Select a treaty and assign one or more treaty types to it.
            </p>
          </div>

          <div className="p-4 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 space-y-4">
            <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200">Select Treaty to Assign Types</h3>
            
            {/* Treaty Selection */}
            <div>
              <Label htmlFor="treaty-select" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Choose Treaty
              </Label>
              <select
                id="treaty-select"
                className="w-full p-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                value={selectedTreatyForAssignment?.id || ''}
                onChange={(e) => {
                  const treatyId = e.target.value;
                  if (treatyId) {
                    const treaty = treaties.find(t => t.id === treatyId);
                    if (treaty) handleTreatySelectionForAssignment(treaty);
                  } else {
                    setSelectedTreatyForAssignment(null);
                    setAvailableTypesForAssignment([]);
                    setSelectedTypesToAssign([]);
                  }
                }}
              >
                <option value="">Select a treaty...</option>
                {treaties.map((treaty) => (
                  <option key={treaty.id} value={treaty.id}>
                    {treaty.title}
                  </option>
                ))}
              </select>
            </div>

            {/* Available Treaty Types (shown when treaty is selected) */}
            {selectedTreatyForAssignment && (
              <div>
                <Label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Available Treaty Types to Assign
                </Label>
                {availableTypesForAssignment.length === 0 ? (
                  <p className="text-sm text-slate-500 dark:text-slate-400 italic">
                    All available treaty types are already assigned to this treaty.
                  </p>
                ) : (
                  <div className="space-y-2 max-h-64 overflow-y-auto border border-slate-200 dark:border-slate-600 rounded p-3">
                    {availableTypesForAssignment.map((type) => (
                      <label key={type.id} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedTypesToAssign.includes(type.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedTypesToAssign([...selectedTypesToAssign, type.id]);
                            } else {
                              setSelectedTypesToAssign(selectedTypesToAssign.filter(id => id !== type.id));
                            }
                          }}
                          className="rounded border-slate-300 dark:border-slate-600"
                        />
                        <span className="text-sm text-slate-700 dark:text-slate-300">{type.name}</span>
                        {type.description && (
                          <span className="text-xs text-slate-500 dark:text-slate-400">- {type.description}</span>
                        )}
                      </label>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Assign Button */}
            {selectedTreatyForAssignment && selectedTypesToAssign.length > 0 && (
              <div className="flex justify-end">
                <Button
                  onClick={handleAssignTreatyTypes}
                  disabled={assigningTypes}
                  className="bg-emerald-600 hover:bg-emerald-700"
                >
                  {assigningTypes ? 'Assigning...' : `Assign ${selectedTypesToAssign.length} Treaty Type${selectedTypesToAssign.length !== 1 ? 's' : ''}`}
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Reassign Treaty Types Tab */}
      {activeTab === 'reassign' && (
        <div className="space-y-6">
          <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-4">
            <h3 className="text-sm font-medium text-orange-800 dark:text-orange-200 mb-2">Reassign Treaty Types</h3>
            <p className="text-xs text-orange-600 dark:text-orange-300">
              Select a treaty and remove treaty types that are currently assigned to it.
            </p>
          </div>

          <div className="p-4 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 space-y-4">
            <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200">Select Treaty to Remove Types</h3>
            
            {/* Treaty Selection */}
            <div>
              <Label htmlFor="treaty-reassign-select" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Choose Treaty
              </Label>
              <select
                id="treaty-reassign-select"
                className="w-full p-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                value={selectedTreatyForReassignment?.id || ''}
                onChange={(e) => {
                  const treatyId = e.target.value;
                  if (treatyId) {
                    const treaty = treaties.find(t => t.id === treatyId);
                    if (treaty) handleTreatySelectionForReassignment(treaty);
                  } else {
                    setSelectedTreatyForReassignment(null);
                    setCurrentAssignedTypes([]);
                    setSelectedTypesToRemove([]);
                  }
                }}
              >
                <option value="">Select a treaty...</option>
                {treaties.map((treaty) => (
                  <option key={treaty.id} value={treaty.id}>
                    {treaty.title}
                  </option>
                ))}
              </select>
            </div>

            {/* Currently Assigned Treaty Types (shown when treaty is selected) */}
            {selectedTreatyForReassignment && (
              <div>
                <Label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Currently Assigned Treaty Types
                </Label>
                {currentAssignedTypes.length === 0 ? (
                  <p className="text-sm text-slate-500 dark:text-slate-400 italic">
                    No treaty types are currently assigned to this treaty.
                  </p>
                ) : (
                  <div className="space-y-2 max-h-64 overflow-y-auto border border-slate-200 dark:border-slate-600 rounded p-3">
                    {currentAssignedTypes.map((type) => (
                      <label key={type.id} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedTypesToRemove.includes(type.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedTypesToRemove([...selectedTypesToRemove, type.id]);
                            } else {
                              setSelectedTypesToRemove(selectedTypesToRemove.filter(id => id !== type.id));
                            }
                          }}
                          className="rounded border-slate-300 dark:border-slate-600"
                        />
                        <span className="text-sm text-slate-700 dark:text-slate-300">{type.name}</span>
                        {type.description && (
                          <span className="text-xs text-slate-500 dark:text-slate-400">- {type.description}</span>
                        )}
                      </label>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Remove Button */}
            {selectedTreatyForReassignment && selectedTypesToRemove.length > 0 && (
              <div className="flex justify-end">
                <Button
                  onClick={handleRemoveTreatyTypes}
                  disabled={reassigningTypes}
                  variant="destructive"
                >
                  {reassigningTypes ? 'Removing...' : `Remove ${selectedTypesToRemove.length} Treaty Type${selectedTypesToRemove.length !== 1 ? 's' : ''}`}
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
