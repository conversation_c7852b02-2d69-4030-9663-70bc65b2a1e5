'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { TreatyTypeDetailsData } from '@/types/user-treaty-assignment';
import { CountryCityAutocomplete } from './CountryCityAutocomplete';

interface ResidentialDetailsSectionProps {
  formData: TreatyTypeDetailsData;
  onChange: (data: Partial<TreatyTypeDetailsData>) => void;
  errors: Record<string, string>;
}

export const ResidentialDetailsSection: React.FC<ResidentialDetailsSectionProps> = ({
  formData,
  onChange,
  errors
}) => {
  const handleInputChange = (field: keyof TreatyTypeDetailsData, value: any) => {
    onChange({ [field]: value });
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Residential Details
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          Please provide information about your residential property.
        </p>
      </div>

      {/* Residential Address */}
      <div className="space-y-2">
        <Label htmlFor="residentialAddress" className="text-sm font-medium">
          Residential Address *
        </Label>
        <Textarea
          id="residentialAddress"
          value={formData.residentialAddress || ''}
          onChange={(e) => handleInputChange('residentialAddress', e.target.value)}
          placeholder="Enter your complete residential address"
          rows={3}
          className={errors.residentialAddress ? 'border-red-500' : ''}
        />
        {errors.residentialAddress && (
          <p className="text-red-500 text-sm">{errors.residentialAddress}</p>
        )}
      </div>

      {/* Status of Residence */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Status of Residence</Label>
        <RadioGroup
          value={formData.residenceStatus || ''}
          onValueChange={(value) => handleInputChange('residenceStatus', value)}
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="owned" id="owned" />
            <Label htmlFor="owned">Owned</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="rented" id="rented" />
            <Label htmlFor="rented">Rented</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="family-owned" id="family-owned" />
            <Label htmlFor="family-owned">Family-owned</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="other" id="other" />
            <Label htmlFor="other">Other</Label>
          </div>
        </RadioGroup>

        {formData.residenceStatus === 'other' && (
          <Input
            placeholder="Please specify"
            value={formData.residenceStatusOther || ''}
            onChange={(e) => handleInputChange('residenceStatusOther', e.target.value)}
          />
        )}
      </div>

      {/* Residence Use Designation */}
      <div className="space-y-2">
        <Label htmlFor="residenceUseDesignation" className="text-sm font-medium">
          Residence Use Designation
        </Label>
        <Input
          id="residenceUseDesignation"
          type="text"
          value={formData.residenceUseDesignation || ''}
          onChange={(e) => handleInputChange('residenceUseDesignation', e.target.value)}
          placeholder="e.g., Primary Residence, Vacation Home"
        />
      </div>

      {/* Peace Protection Request */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="peaceProtected"
          checked={formData.peaceProtectedPremises || false}
          onCheckedChange={(checked) => handleInputChange('peaceProtectedPremises', checked)}
        />
        <Label htmlFor="peaceProtected" className="text-sm">
          Request NWA recognition as peace-protected premises
        </Label>
      </div>

      {/* Country/City Autocomplete */}
      <CountryCityAutocomplete
        countryId={formData.residenceCountryId}
        cityId={formData.residenceCityId}
        onCountryChange={(countryId) => handleInputChange('residenceCountryId', countryId)}
        onCityChange={(cityId) => handleInputChange('residenceCityId', cityId)}
        countryLabel="Residence Country"
        cityLabel="Residence City"
        required={false}
      />
    </div>
  );
};