'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Globe,
  Users,
  UserPlus,
  Edit,
  Trash2,
  Search,
  Plus,
  Crown,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Activity,
  Ban,
  CheckCircle,
  AlertTriangle,
  Building
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { NationTreatyMemberModal } from './modals/NationTreatyMemberModal';
import { NationTreatyEnvoyModal } from './modals/NationTreatyEnvoyModal';
import { NationTreatyOfficeModal, NationTreatyOfficeList } from './modals/NationTreatyOfficeModal';
import { CreateNationTreatyModal } from './modals/CreateNationTreatyModal';
import { EditNationTreatyModal } from './modals/EditNationTreatyModal';

interface NationTreaty {
  id: string;
  name: string;
  officialName: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'TERMINATED';
  description?: string;
  contactEmail?: string;
  contactPhone?: string;
  contactAddress?: string;
  website?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactEmail?: string;
  notes?: string;
  documentPath?: string;
  createdAt: string;
  updatedAt: string;
  _count: {
    members: number;
    envoys: number;
  };
  createdBy?: {
    id: string;
    name: string;
    email: string;
  };
}

interface NationTreatyMember {
  id: string;
  userId: string;
  nationTreatyId: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  joinedAt: string;
  notes?: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
}

interface NationTreatyEnvoy {
  id: string;
  userId: string;
  nationTreatyId: string;
  title: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  appointedAt: string;
  notes?: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
}

interface NationTreatyOffice {
  id: string;
  nationTreatyId: string;
  officeType: 'ENVOY_OFFICE' | 'CONSULATE' | 'EMBASSY';
  status: 'ACTIVE' | 'INACTIVE' | 'CLOSED';
  city: string;
  country: string;
  address: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  website?: string;
  operatingHours?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface NationTreatyStats {
  totalTreaties: number;
  activeTreaties: number;
  totalMembers: number;
  activeMembers: number;
  totalEnvoys: number;
  activeEnvoys: number;
  statusBreakdown: Record<string, number>;
}

export const NationTreatyManagement: React.FC = () => {
  const [treaties, setTreaties] = useState<NationTreaty[]>([]);
  const [selectedTreaty, setSelectedTreaty] = useState<NationTreaty | null>(null);
  const [members, setMembers] = useState<NationTreatyMember[]>([]);
  const [envoys, setEnvoys] = useState<NationTreatyEnvoy[]>([]);
  const [offices, setOffices] = useState<NationTreatyOffice[]>([]);
  const [stats, setStats] = useState<NationTreatyStats | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showMemberModal, setShowMemberModal] = useState(false);
  const [showEnvoyModal, setShowEnvoyModal] = useState(false);
  
  const itemsPerPage = 10;

  const loadTreaties = useCallback(async (currentPage = page, currentSearch = search) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
        ...(currentSearch && { search: currentSearch })
      });
      
      const response = await fetch(`/api/nation-treaties?${params}`);
      if (response.ok) {
        const data = await response.json();
        setTreaties(data.data || []);
        setTotalPages(data.pagination?.totalPages || 1);
        setTotalCount(data.pagination?.total || 0);
        setPage(data.pagination?.page || currentPage);
      }
    } catch (error) {
      console.error('Error loading nation treaties:', error);
    } finally {
      setLoading(false);
    }
  }, [page, search]);

  const loadStats = useCallback(async () => {
    try {
      const response = await fetch('/api/nation-treaties/statistics');
      if (response.ok) {
        const data = await response.json();
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error loading nation treaty statistics:', error);
    }
  }, []);

  const loadTreatyDetails = useCallback(async (treatyId: string) => {
    try {
      const [treatyResponse, membersResponse, envoysResponse, officesResponse] = await Promise.all([
        fetch(`/api/nation-treaties/${treatyId}`),
        fetch(`/api/nation-treaties/${treatyId}/members`),
        fetch(`/api/nation-treaties/${treatyId}/envoys`),
        fetch(`/api/nation-treaties/${treatyId}/offices`)
      ]);

      if (treatyResponse.ok) {
        const treatyData = await treatyResponse.json();
        setSelectedTreaty(treatyData.data);
      }

      if (membersResponse.ok) {
        const membersData = await membersResponse.json();
        setMembers(membersData.data || []);
      }

      if (envoysResponse.ok) {
        const envoysData = await envoysResponse.json();
        setEnvoys(envoysData.data || []);
      }

      if (officesResponse.ok) {
        const officesData = await officesResponse.json();
        setOffices(officesData.data || []);
      }
    } catch (error) {
      console.error('Error loading treaty details:', error);
    }
  }, []);

  const handleDeleteTreaty = async (treatyId: string) => {
    if (!confirm('Are you sure you want to delete this nation treaty?')) return;

    try {
      const response = await fetch(`/api/nation-treaties/${treatyId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        if (selectedTreaty?.id === treatyId) {
          setSelectedTreaty(null);
          setMembers([]);
          setEnvoys([]);
          setOffices([]);
        }
        loadTreaties(page, search);
        loadStats();
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to delete nation treaty');
      }
    } catch (error) {
      console.error('Error deleting nation treaty:', error);
      alert('Failed to delete nation treaty');
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    try {
      const response = await fetch(`/api/nation-treaties/${selectedTreaty?.id}/members`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ memberId })
      });

      if (response.ok) {
        if (selectedTreaty) {
          loadTreatyDetails(selectedTreaty.id);
        }
        loadStats();
      }
    } catch (error) {
      console.error('Error removing member:', error);
    }
  };

  const handleRemoveEnvoy = async (envoyId: string) => {
    try {
      const response = await fetch(`/api/nation-treaties/${selectedTreaty?.id}/envoys`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ envoyId })
      });

      if (response.ok) {
        if (selectedTreaty) {
          loadTreatyDetails(selectedTreaty.id);
        }
        loadStats();
      }
    } catch (error) {
      console.error('Error removing envoy:', error);
    }
  };

  useEffect(() => {
    loadTreaties();
    loadStats();
  }, []);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setPage(1);
      loadTreaties(1, search);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [search]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100';
      case 'INACTIVE': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-100';
      case 'SUSPENDED': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100';
      case 'TERMINATED': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-100';
    }
  };

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Treaties</CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalTreaties}</div>
              <p className="text-xs text-muted-foreground">
                {stats.activeTreaties} active
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Members</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalMembers}</div>
              <p className="text-xs text-muted-foreground">
                {stats.activeMembers} active
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Envoys</CardTitle>
              <Crown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalEnvoys}</div>
              <p className="text-xs text-muted-foreground">
                {stats.activeEnvoys} active
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Status Breakdown</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeTreaties}</div>
              <p className="text-xs text-muted-foreground">
                Currently active
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200">Nation Treaties</h2>
          <p className="text-slate-600 dark:text-slate-400">Manage diplomatic treaties and national representatives</p>
        </div>
        <Button onClick={() => setShowCreateModal(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Nation Treaty
        </Button>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Search nation treaties..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-9"
          />
        </div>
      </div>

      {/* Treaty List */}
      {loading ? (
        <div className="text-center py-8">Loading nation treaties...</div>
      ) : treaties.length === 0 ? (
        <div className="text-center py-8 text-slate-500">
          {search ? 'No nation treaties found matching your search.' : 'No nation treaties found.'}
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Treaty List */}
          <div className="lg:col-span-1 space-y-4">
            {treaties.map((treaty) => (
              <Card 
                key={treaty.id} 
                className={`cursor-pointer transition-colors ${
                  selectedTreaty?.id === treaty.id 
                    ? 'ring-2 ring-slate-500 bg-slate-50 dark:bg-slate-800' 
                    : 'hover:bg-slate-50 dark:hover:bg-slate-800'
                }`}
                onClick={() => loadTreatyDetails(treaty.id)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{treaty.name}</CardTitle>
                    <Badge className={getStatusColor(treaty.status)}>
                      {treaty.status}
                    </Badge>
                  </div>
                  <CardDescription className="text-sm">
                    {treaty.officialName}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm text-slate-600 dark:text-slate-400">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4" />
                      <span>{treaty._count.members} members</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Crown className="h-4 w-4" />
                      <span>{treaty._count.envoys} envoys</span>
                    </div>
                    {treaty.contactEmail && (
                      <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4" />
                        <span className="truncate">{treaty.contactEmail}</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(treaty.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Treaty Details */}
          <div className="lg:col-span-2">
            {selectedTreaty ? (
              <div className="space-y-6">
                {/* Treaty Header */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl">{selectedTreaty.name}</CardTitle>
                        <CardDescription>{selectedTreaty.officialName}</CardDescription>
                      </div>
                      <div className="flex items-center space-x-2">
                        <EditNationTreatyModal
                          treaty={selectedTreaty}
                          onTreatyUpdated={() => {
                            loadTreaties();
                            loadTreatyDetails(selectedTreaty.id);
                            loadStats();
                          }}
                        >
                          <Button
                            variant="outline"
                            size="sm"
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                        </EditNationTreatyModal>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteTreaty(selectedTreaty.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      {selectedTreaty.description && (
                        <div className="md:col-span-2">
                          <strong className="text-slate-700 dark:text-slate-300">Description:</strong>
                          <p className="mt-1 text-slate-600 dark:text-slate-400">{selectedTreaty.description}</p>
                        </div>
                      )}
                      {selectedTreaty.contactEmail && (
                        <div>
                          <strong className="text-slate-700 dark:text-slate-300">Contact Email:</strong>
                          <p className="mt-1 text-slate-600 dark:text-slate-400">{selectedTreaty.contactEmail}</p>
                        </div>
                      )}
                      {selectedTreaty.contactPhone && (
                        <div>
                          <strong className="text-slate-700 dark:text-slate-300">Contact Phone:</strong>
                          <p className="mt-1 text-slate-600 dark:text-slate-400">{selectedTreaty.contactPhone}</p>
                        </div>
                      )}
                      {selectedTreaty.contactAddress && (
                        <div className="md:col-span-2">
                          <strong className="text-slate-700 dark:text-slate-300">Contact Address:</strong>
                          <p className="mt-1 text-slate-600 dark:text-slate-400">{selectedTreaty.contactAddress}</p>
                        </div>
                      )}
                      {selectedTreaty.emergencyContactName && (
                        <div>
                          <strong className="text-slate-700 dark:text-slate-300">Emergency Contact:</strong>
                          <p className="mt-1 text-slate-600 dark:text-slate-400">
                            {selectedTreaty.emergencyContactName}
                            {selectedTreaty.emergencyContactPhone && ` - ${selectedTreaty.emergencyContactPhone}`}
                          </p>
                        </div>
                      )}
                      {selectedTreaty.website && (
                        <div>
                          <strong className="text-slate-700 dark:text-slate-300">Website:</strong>
                          <p className="mt-1 text-slate-600 dark:text-slate-400">
                            <a href={selectedTreaty.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                              {selectedTreaty.website}
                            </a>
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Members and Envoys Tabs */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Management</CardTitle>
                      <div className="flex space-x-2">
                        <NationTreatyMemberModal
                          treatyId={selectedTreaty.id}
                          onMemberAdded={() => {
                            loadTreatyDetails(selectedTreaty.id);
                            loadStats();
                          }}
                        >
                          <Button
                            variant="outline"
                            size="sm"
                          >
                            <UserPlus className="h-4 w-4 mr-2" />
                            Add Member
                          </Button>
                        </NationTreatyMemberModal>
                        <NationTreatyEnvoyModal
                          treatyId={selectedTreaty.id}
                          onEnvoyAdded={() => {
                            loadTreatyDetails(selectedTreaty.id);
                            loadStats();
                          }}
                        >
                          <Button
                            variant="outline"
                            size="sm"
                          >
                            <Crown className="h-4 w-4 mr-2" />
                            Add Envoy
                          </Button>
                        </NationTreatyEnvoyModal>
                        <NationTreatyOfficeModal
                          treatyId={selectedTreaty.id}
                          onOfficeAdded={() => {
                            loadTreatyDetails(selectedTreaty.id);
                          }}
                        >
                          <Button
                            variant="outline"
                            size="sm"
                          >
                            <Building className="h-4 w-4 mr-2" />
                            Add Office
                          </Button>
                        </NationTreatyOfficeModal>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Tabs defaultValue="members" className="w-full">
                      <TabsList>
                        <TabsTrigger value="members">Members ({members.length})</TabsTrigger>
                        <TabsTrigger value="envoys">Envoys ({envoys.length})</TabsTrigger>
                        <TabsTrigger value="offices">Offices ({offices.length})</TabsTrigger>
                      </TabsList>
                      
                      <TabsContent value="members" className="space-y-4">
                        {members.length === 0 ? (
                          <div className="text-center py-8 text-slate-500">
                            No members found for this nation treaty.
                          </div>
                        ) : (
                          <div className="space-y-3">
                            {members.map((member) => (
                              <div key={member.id} className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                <div className="flex items-center space-x-3">
                                  <Avatar>
                                    <AvatarFallback>
                                      {member.user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <p className="font-medium">{member.user.name}</p>
                                    <p className="text-sm text-slate-600 dark:text-slate-400">{member.user.email}</p>
                                    <p className="text-xs text-slate-500">
                                      Joined {new Date(member.joinedAt).toLocaleDateString()}
                                    </p>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Badge className={getStatusColor(member.status)}>
                                    {member.status}
                                  </Badge>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleRemoveMember(member.id)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </TabsContent>
                      
                      <TabsContent value="envoys" className="space-y-4">
                        {envoys.length === 0 ? (
                          <div className="text-center py-8 text-slate-500">
                            No envoys found for this nation treaty.
                          </div>
                        ) : (
                          <div className="space-y-3">
                            {envoys.map((envoy) => (
                              <div key={envoy.id} className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                <div className="flex items-center space-x-3">
                                  <Avatar>
                                    <AvatarFallback>
                                      {envoy.user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <p className="font-medium">{envoy.user.name}</p>
                                    <p className="text-sm text-slate-600 dark:text-slate-400">{envoy.user.email}</p>
                                    <div className="flex items-center space-x-2 mt-1">
                                      <Badge variant="outline">{envoy.title}</Badge>
                                      <Badge className={getStatusColor(envoy.status)}>
                                        {envoy.status}
                                      </Badge>
                                    </div>
                                    <p className="text-xs text-slate-500">
                                      Appointed {new Date(envoy.appointedAt).toLocaleDateString()}
                                    </p>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleRemoveEnvoy(envoy.id)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </TabsContent>
                      
                      <TabsContent value="offices" className="space-y-4">
                        <NationTreatyOfficeList
                          treatyId={selectedTreaty.id}
                          offices={offices}
                          onOfficeRemoved={() => {
                            loadTreatyDetails(selectedTreaty.id);
                          }}
                        />
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center py-16">
                  <div className="text-center text-slate-500">
                    <Globe className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-lg font-medium mb-2">Select a Nation Treaty</p>
                    <p className="text-sm">Choose a treaty from the list to view and manage its details</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}

      {/* Create Modal */}
      <CreateNationTreatyModal
        onTreatyCreated={() => {
          loadTreaties();
          loadStats();
        }}
      />
    </div>
  );
};