'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { X, Upload, File, Image, AlertCircle, CheckCircle } from 'lucide-react';

interface FileUploadProps {
  value?: string[];
  onChange: (files: string[]) => void;
  accept?: string;
  maxFiles?: number;
  maxSize?: number; // in MB
  required?: boolean;
  disabled?: boolean;
  className?: string;
  label?: string;
  description?: string;
  showPreview?: boolean;
  allowMultiple?: boolean;
}

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url?: string;
  status: 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  value = [],
  onChange,
  accept = '*/*',
  maxFiles = 5,
  maxSize = 10, // 10MB default
  required = false,
  disabled = false,
  className = '',
  label = 'Upload Files',
  description = 'Drag and drop files here or click to browse',
  showPreview = true,
  allowMultiple = true
}) => {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Convert existing file URLs to UploadedFile objects
  React.useEffect(() => {
    if (value && value.length > 0) {
      const existingFiles: UploadedFile[] = value.map((url, index) => ({
        id: `existing-${index}`,
        name: url.split('/').pop() || 'Unknown file',
        size: 0,
        type: 'unknown',
        url,
        status: 'success',
        progress: 100
      }));
      setFiles(existingFiles);
    }
  }, [value]);

  const validateFile = React.useCallback((file: File): { valid: boolean; error?: string } => {
    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      return { valid: false, error: `File size must be less than ${maxSize}MB` };
    }

    // Check file type
    if (accept !== '*/*') {
      const acceptedTypes = accept.split(',').map(type => type.trim());
      const fileType = file.type.toLowerCase();
      const fileName = file.name.toLowerCase();

      const isAccepted = acceptedTypes.some(acceptedType => {
        if (acceptedType.startsWith('.')) {
          return fileName.endsWith(acceptedType);
        }
        return fileType === acceptedType || fileType.startsWith(acceptedType.replace('*', ''));
      });

      if (!isAccepted) {
        return { valid: false, error: `File type not accepted. Accepted types: ${accept}` };
      }
    }

    return { valid: true };
  }, [maxSize, accept]);

  const processFiles = useCallback(async (fileList: FileList) => {
    const newFiles: UploadedFile[] = [];
    const currentFileCount = files.length;

    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];

      // Check if we've reached the max file limit
      if (currentFileCount + newFiles.length >= maxFiles) {
        break;
      }

      const validation = validateFile(file);
      if (!validation.valid) {
        newFiles.push({
          id: `error-${Date.now()}-${i}`,
          name: file.name,
          size: file.size,
          type: file.type,
          status: 'error',
          progress: 0,
          error: validation.error
        });
        continue;
      }

      const uploadedFile: UploadedFile = {
        id: `upload-${Date.now()}-${i}`,
        name: file.name,
        size: file.size,
        type: file.type,
        status: 'uploading',
        progress: 0
      };

      newFiles.push(uploadedFile);

      // Simulate file upload
      try {
        await simulateUpload(uploadedFile);
        uploadedFile.status = 'success';
        uploadedFile.progress = 100;
        uploadedFile.url = `/api/files/${uploadedFile.name}`; // Mock URL
      } catch (error) {
        uploadedFile.status = 'error';
        uploadedFile.error = 'Upload failed';
      }
    }

    const updatedFiles = [...files, ...newFiles];
    setFiles(updatedFiles);

    // Update parent with successful file URLs
    const successfulFiles = updatedFiles
      .filter(f => f.status === 'success' && f.url)
      .map(f => f.url!);

    onChange(successfulFiles);
  }, [files, maxFiles, onChange, validateFile]);

  const simulateUpload = (file: UploadedFile): Promise<void> => {
    return new Promise((resolve, reject) => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);
          resolve();
        }
        file.progress = progress;
        setFiles(prev => [...prev]); // Trigger re-render
      }, 200);
    });
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (disabled) return;

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      processFiles(droppedFiles);
    }
  }, [disabled, processFiles]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      processFiles(e.target.files);
    }
  };

  const removeFile = (fileId: string) => {
    const updatedFiles = files.filter(f => f.id !== fileId);
    setFiles(updatedFiles);

    const successfulFiles = updatedFiles
      .filter(f => f.status === 'success' && f.url)
      .map(f => f.url!);

    onChange(successfulFiles);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <Image className="w-4 h-4" aria-label="Image file" />;
    }
    return <File className="w-4 h-4" aria-label="File" />;
  };

  const canAddMoreFiles = files.length < maxFiles;

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="space-y-2">
        <Label className="text-sm font-medium">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
        {description && (
          <p className="text-sm text-gray-600 dark:text-gray-400">{description}</p>
        )}
      </div>

      {/* Upload Area */}
      {canAddMoreFiles && (
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            isDragOver
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => !disabled && fileInputRef.current?.click()}
        >
          <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
            {isDragOver ? 'Drop files here' : 'Drag & drop files here'}
          </p>
          <p className="text-xs text-gray-500">
            or <span className="text-blue-600 hover:text-blue-700">browse files</span>
          </p>
          <p className="text-xs text-gray-400 mt-2">
            Max {maxFiles} files, {maxSize}MB each
          </p>
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={allowMultiple && canAddMoreFiles}
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-2">
          <Label className="text-sm font-medium">Uploaded Files</Label>
          <div className="space-y-2">
            {files.map((file) => (
              <div
                key={file.id}
                className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
              >
                <div className="flex-shrink-0">
                  {file.status === 'success' && getFileIcon(file.type)}
                  {file.status === 'uploading' && (
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                  )}
                  {file.status === 'error' && (
                    <AlertCircle className="w-4 h-4 text-red-500" />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(file.size)}
                  </p>

                  {file.status === 'uploading' && (
                    <div className="mt-1">
                      <div className="w-full bg-gray-200 rounded-full h-1">
                        <div
                          className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${file.progress}%` }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">{Math.round(file.progress)}%</p>
                    </div>
                  )}

                  {file.status === 'error' && file.error && (
                    <p className="text-xs text-red-500 mt-1">{file.error}</p>
                  )}
                </div>

                {file.status === 'success' && (
                  <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                )}

                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(file.id)}
                  className="flex-shrink-0 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* File Count Indicator */}
      <div className="text-xs text-gray-500">
        {files.length} of {maxFiles} files uploaded
      </div>
    </div>
  );
};