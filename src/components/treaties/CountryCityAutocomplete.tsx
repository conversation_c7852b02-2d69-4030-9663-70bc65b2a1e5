'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, MapPin, Search } from 'lucide-react';

interface Country {
  id: number;
  name: string;
  code: string;
}

interface City {
  id: number;
  name: string;
  countryId: number;
}

interface CountryCityAutocompleteProps {
  countryId?: number;
  cityId?: number;
  onCountryChange: (countryId: number | undefined) => void;
  onCityChange: (cityId: number | undefined) => void;
  countryLabel?: string;
  cityLabel?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
}

export const CountryCityAutocomplete: React.FC<CountryCityAutocompleteProps> = ({
  countryId,
  cityId,
  onCountryChange,
  onCityChange,
  countryLabel = "Country",
  cityLabel = "City",
  required = false,
  disabled = false,
  className = ""
}) => {
  const [countries, setCountries] = useState<Country[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [countrySearch, setCountrySearch] = useState('');
  const [citySearch, setCitySearch] = useState('');
  const [isLoadingCountries, setIsLoadingCountries] = useState(false);
  const [isLoadingCities, setIsLoadingCities] = useState(false);
  const [countryError, setCountryError] = useState<string | null>(null);
  const [cityError, setCityError] = useState<string | null>(null);



  const loadCountries = useCallback(async () => {
    setIsLoadingCountries(true);
    setCountryError(null);

    try {
      // Fetch countries from API with search query
      const queryParams = new URLSearchParams();
      if (countrySearch) {
        queryParams.set('q', countrySearch);
      }
      queryParams.set('limit', '50'); // Get more results for better search

      const response = await fetch(`/api/countries?${queryParams}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch countries: ${response.status}`);
      }

      const data = await response.json();
      setCountries(data.countries || []);
    } catch (error) {
      setCountryError('Failed to load countries');
      console.error('Error loading countries:', error);
    } finally {
      setIsLoadingCountries(false);
    }
  }, [countrySearch]);

  const loadCities = useCallback(async (selectedCountryId: number) => {
    setIsLoadingCities(true);
    setCityError(null);

    try {
      // Fetch cities from API for the selected country with search query
      const queryParams = new URLSearchParams();
      queryParams.set('countryId', selectedCountryId.toString());
      if (citySearch) {
        queryParams.set('q', citySearch);
      }
      queryParams.set('limit', '50'); // Get more results for better search

      const response = await fetch(`/api/cities?${queryParams}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch cities: ${response.status}`);
      }

      const data = await response.json();
      setCities(data.cities || []);
    } catch (error) {
      setCityError('Failed to load cities');
      console.error('Error loading cities:', error);
    } finally {
      setIsLoadingCities(false);
    }
  }, [citySearch]);

  const handleCountrySearch = (value: string) => {
    setCountrySearch(value);
    loadCountries();
  };

  const handleCitySearch = (value: string) => {
    setCitySearch(value);
    if (countryId) {
      loadCities(countryId);
    }
  };

  const handleCountrySelect = (selectedCountryId: string) => {
    const id = parseInt(selectedCountryId);
    onCountryChange(id);
    setCitySearch(''); // Clear city search when country changes
  };

  const handleCitySelect = (selectedCityId: string) => {
    const id = parseInt(selectedCityId);
    onCityChange(id);
  };

  // Load countries on component mount
  useEffect(() => {
    loadCountries();
  }, [loadCountries]);

  // Load cities when country changes
  useEffect(() => {
    if (countryId) {
      loadCities(countryId);
    } else {
      setCities([]);
      onCityChange(undefined);
    }
  }, [countryId, loadCities, onCityChange]);

  const selectedCountry = countries.find(c => c.id === countryId);
  const selectedCity = cities.find(c => c.id === cityId);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Country Selection */}
      <div className="space-y-2">
        <Label htmlFor="country-select" className="text-sm font-medium">
          {countryLabel} {required && <span className="text-red-500">*</span>}
        </Label>
        <div className="relative">
          <Select
            value={countryId?.toString() || ''}
            onValueChange={handleCountrySelect}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder={`Search ${countryLabel.toLowerCase()}...`} />
            </SelectTrigger>
            <SelectContent>
              {isLoadingCountries ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="ml-2">Loading countries...</span>
                </div>
              ) : countries.length === 0 ? (
                <div className="py-4 text-center text-gray-500">
                  No countries found
                </div>
              ) : (
                countries.map((country) => (
                  <SelectItem key={country.id} value={country.id.toString()}>
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4" />
                      <span>{country.name}</span>
                      <span className="text-xs text-gray-500">({country.code})</span>
                    </div>
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>
        {countryError && (
          <p className="text-red-500 text-sm">{countryError}</p>
        )}
      </div>

      {/* City Selection */}
      <div className="space-y-2">
        <Label htmlFor="city-select" className="text-sm font-medium">
          {cityLabel} {required && <span className="text-red-500">*</span>}
        </Label>
        <div className="relative">
          <Select
            value={cityId?.toString() || ''}
            onValueChange={handleCitySelect}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder={countryId ? `Search ${cityLabel.toLowerCase()}...` : `Select ${countryLabel.toLowerCase()} first`} />
            </SelectTrigger>
            <SelectContent>
              {isLoadingCities ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="ml-2">Loading cities...</span>
                </div>
              ) : cities.length === 0 ? (
                <div className="py-4 text-center text-gray-500">
                  {countryId ? 'No cities found' : `Select ${countryLabel.toLowerCase()} first`}
                </div>
              ) : (
                cities.map((city) => (
                  <SelectItem key={city.id} value={city.id.toString()}>
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4" />
                      <span>{city.name}</span>
                    </div>
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>
        {cityError && (
          <p className="text-red-500 text-sm">{cityError}</p>
        )}
      </div>

      {/* Selected Location Display */}
      {selectedCountry && selectedCity && (
        <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="flex items-center space-x-2 text-sm">
            <MapPin className="w-4 h-4 text-green-600" />
            <span className="font-medium">
              {selectedCity.name}, {selectedCountry.name}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};