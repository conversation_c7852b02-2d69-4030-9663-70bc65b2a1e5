'use client';

import React from 'react';
import { FileText, Download, Eye, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

interface Treaty {
  id: string;
  title: string;
  description: string | null;
  status: string;
  signedDate: string | null;
  expirationDate: string | null;
  documentPath: string | null;
}

interface TreatyListProps {
  treaties: Treaty[];
  showPagination?: boolean;
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
  };
  onPageChange?: (page: number) => void;
  onViewDetails?: (id: string) => void;
  onDelete?: (id: string) => void;
}

export const TreatyList: React.FC<TreatyListProps> = ({
  treaties,
  showPagination = false,
  pagination,
  onPageChange,
  onViewDetails,
  onDelete
}) => {
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'pending_renewal':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'expired':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'terminated':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  return (
    <div>
      {treaties.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No treaties found</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {showPagination ? 'Try adjusting your search or filter criteria.' : 'Get started by uploading a new treaty.'}
          </p>
        </div>
      ) : (
        <>
          <div className="overflow-hidden bg-white dark:bg-gray-800 shadow sm:rounded-md">
            <ul className="divide-y divide-gray-200 dark:divide-gray-700">
              {treaties.map((treaty) => (
                <li key={treaty.id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <FileText className="h-5 w-5 text-gray-400 dark:text-gray-500 mr-2" />
                        <p className="text-sm font-medium text-slate-700 dark:text-slate-300 truncate">
                          {treaty.title}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(treaty.status)}`}>
                          {treaty.status.replace('_', ' ')}
                        </span>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        {treaty.description && (
                          <p className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            {treaty.description}
                          </p>
                        )}
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400 sm:mt-0">
                        <p>
                          Signed: {formatDate(treaty.signedDate)} | Expires: {formatDate(treaty.expirationDate)}
                        </p>
                      </div>
                    </div>
                    <div className="mt-3 flex space-x-2">
                      <button
                        type="button"
                        className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 flex items-center"
                        onClick={() => onViewDetails && onViewDetails(treaty.id)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Details
                      </button>
                      {treaty.documentPath && (
                        <button
                          type="button"
                          className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 flex items-center"
                          onClick={() => window.open(treaty.documentPath!, '_blank')}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </button>
                      )}
                      <button
                        type="button"
                        className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 flex items-center"
                        onClick={async () => {
                          if (window.confirm('Are you sure you want to delete this treaty? This action cannot be undone.')) {
                            try {
                              const response = await fetch(`/api/treaties?id=${treaty.id}`, {
                                method: 'DELETE',
                              });
                              
                              if (!response.ok) {
                                const errorData = await response.json();
                                throw new Error(errorData.error || 'Failed to delete treaty');
                              }
                              
                              toast.success('Treaty deleted successfully');
                              onDelete && onDelete(treaty.id);
                            } catch (error: any) {
                              toast.error(error.message || 'Failed to delete treaty');
                              console.error('Error deleting treaty:', error);
                            }
                          }
                        }}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </button>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>

          {showPagination && pagination && onPageChange && (
            <div className="mt-6 flex items-center justify-between">
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  Showing <span className="font-medium">{((pagination.currentPage || 1) - 1) * 10 + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min((pagination.currentPage || 1) * 10, pagination.totalCount || 0)}
                  </span>{' '}
                  of <span className="font-medium">{pagination.totalCount || 0}</span> results
                </div>
                <div className="flex space-x-2">
                  <button
                    type="button"
                    className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50"
                    onClick={() => onPageChange((pagination.currentPage || 1) - 1)}
                    disabled={(pagination.currentPage || 1) === 1}
                  >
                    Previous
                  </button>
                  <button
                    type="button"
                    className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50"
                    onClick={() => onPageChange((pagination.currentPage || 1) + 1)}
                    disabled={(pagination.currentPage || 1) === (pagination.totalPages || 1)}
                  >
                    Next
                  </button>
                </div>
              </div>
          )}
        </>
      )}
    </div>
  );
};