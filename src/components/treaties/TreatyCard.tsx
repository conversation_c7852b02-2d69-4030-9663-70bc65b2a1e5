'use client';

import React from 'react';
import { Eye, Edit, Archive, FileText, Download, Calendar, User, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';

export interface TreatyCardData {
  id: string;
  userId: string;
  treatyTypeId: string;
  treatyTypeName: string;
  status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'archived';
  createdAt: string;
  updatedAt: string;
  submittedAt?: string;
  applicantName?: string;
  businessName?: string;
  country?: string;
  city?: string;
  hasSignature?: boolean;
  hasAttachments?: boolean;
}

interface TreatyCardProps {
  treaty: TreatyCardData;
  onView: (treaty: TreatyCardData) => void;
  onEdit: (treaty: TreatyCardData) => void;
  onArchive: (treaty: TreatyCardData) => void;
  onDownloadPDF: (treaty: TreatyCardData) => void;
}

export const TreatyCard: React.FC<TreatyCardProps> = ({
  treaty,
  onView,
  onEdit,
  onArchive,
  onDownloadPDF
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      case 'submitted':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'archived':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Edit className="h-3 w-3" />;
      case 'submitted':
        return <FileText className="h-3 w-3" />;
      case 'approved':
        return <FileText className="h-3 w-3" />;
      case 'rejected':
        return <FileText className="h-3 w-3" />;
      case 'archived':
        return <Archive className="h-3 w-3" />;
      default:
        return <FileText className="h-3 w-3" />;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-shadow duration-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
              {treaty.treatyTypeName}
            </h3>
            <div className="flex items-center mt-1 space-x-2">
              <span
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(treaty.status)}`}
              >
                {getStatusIcon(treaty.status)}
                <span className="ml-1 capitalize">{treaty.status}</span>
              </span>
              {treaty.hasSignature && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border border-gray-300 dark:border-gray-600">
                  <FileText className="h-3 w-3 mr-1" />
                  Signed
                </span>
              )}
              {treaty.hasAttachments && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border border-gray-300 dark:border-gray-600">
                  <FileText className="h-3 w-3 mr-1" />
                  Attachments
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 space-y-3">
        {/* Applicant Info */}
        {treaty.applicantName && (
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <User className="h-4 w-4 mr-2 flex-shrink-0" />
            <span className="truncate">{treaty.applicantName}</span>
          </div>
        )}

        {/* Business Info */}
        {treaty.businessName && (
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <FileText className="h-4 w-4 mr-2 flex-shrink-0" />
            <span className="truncate">{treaty.businessName}</span>
          </div>
        )}

        {/* Location Info */}
        {(treaty.country || treaty.city) && (
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
            <span className="truncate">
              {[treaty.city, treaty.country].filter(Boolean).join(', ')}
            </span>
          </div>
        )}

        {/* Dates */}
        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
          <Calendar className="h-4 w-4 mr-2 flex-shrink-0" />
          <span>
            {treaty.status === 'submitted' && treaty.submittedAt
              ? `Submitted: ${formatDate(treaty.submittedAt)}`
              : `Created: ${formatDate(treaty.createdAt)}`
            }
          </span>
        </div>
      </div>

      {/* Actions */}
      <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onView(treaty)}
            className="flex-1"
          >
            <Eye className="h-4 w-4 mr-1" />
            View
          </Button>

          {treaty.status === 'draft' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(treaty)}
              className="flex-1"
            >
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={() => onDownloadPDF(treaty)}
            className="flex-1"
          >
            <Download className="h-4 w-4 mr-1" />
            PDF
          </Button>

          {treaty.status !== 'archived' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onArchive(treaty)}
              className="flex-1 text-orange-600 hover:text-orange-700"
            >
              <Archive className="h-4 w-4 mr-1" />
              Archive
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};