'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Edit, Save, X } from 'lucide-react';
import { toast } from 'sonner';
import { NationTreatyStatus } from '@/lib/validation/nation-treaty';

interface EditNationTreatyModalProps {
  treaty: any;
  onTreatyUpdated: () => void;
  children: React.ReactNode;
}

interface FormData {
  name: string;
  officialName: string;
  status: keyof typeof NationTreatyStatus;
  description: string;
  contactEmail: string;
  contactPhone: string;
  contactAddress: string;
  website: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  notes: string;
}

export const EditNationTreatyModal: React.FC<EditNationTreatyModalProps> = ({ 
  treaty, 
  onTreatyUpdated, 
  children 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    officialName: '',
    status: NationTreatyStatus.ACTIVE,
    description: '',
    contactEmail: '',
    contactPhone: '',
    contactAddress: '',
    website: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    notes: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (treaty) {
      setFormData({
        name: treaty.name || '',
        officialName: treaty.officialName || '',
        status: treaty.status || NationTreatyStatus.ACTIVE,
        description: treaty.description || '',
        contactEmail: treaty.contactEmail || '',
        contactPhone: treaty.contactPhone || '',
        contactAddress: treaty.contactAddress || '',
        website: treaty.website || '',
        emergencyContactName: treaty.emergencyContactName || '',
        emergencyContactPhone: treaty.emergencyContactPhone || '',
        notes: treaty.notes || '',
      });
    }
  }, [treaty]);

  const handleInputChange = (field: keyof FormData, value: string | keyof typeof NationTreatyStatus) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/nation-treaties/${treaty.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Failed to update nation treaty');
      }

      toast.success('Nation treaty updated successfully');
      setIsOpen(false);
      onTreatyUpdated();
    } catch (error) {
      console.error('Error updating nation treaty:', error);
      toast.error('Failed to update nation treaty');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
              <Edit className="h-5 w-5 mr-2" />
              Edit Nation Treaty: {treaty?.name}
            </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Treaty Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="United Tribes of Aotearoa"
                required
              />
            </div>
            <div>
              <Label htmlFor="officialName">Official Name *</Label>
              <Input
                id="officialName"
                value={formData.officialName}
                onChange={(e) => handleInputChange('officialName', e.target.value)}
                placeholder="United Tribes of Aotearoa Treaty"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="status">Status</Label>
            <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value as keyof typeof NationTreatyStatus)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={NationTreatyStatus.ACTIVE}>Active</SelectItem>
                <SelectItem value={NationTreatyStatus.SUSPENDED}>Suspended</SelectItem>
                  <SelectItem value={NationTreatyStatus.PENDING}>Pending</SelectItem>
                              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Treaty description..."
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <h3 className="text-sm font-medium">Contact Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="contactEmail">Email</Label>
                <Input
                  id="contactEmail"
                  type="email"
                  value={formData.contactEmail}
                  onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="contactPhone">Phone</Label>
                <Input
                  id="contactPhone"
                  value={formData.contactPhone}
                  onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                  placeholder="+64 9 123 4567"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="contactAddress">Address</Label>
              <Textarea
                id="contactAddress"
                value={formData.contactAddress}
                onChange={(e) => handleInputChange('contactAddress', e.target.value)}
                placeholder="Full address..."
                rows={2}
              />
            </div>
            <div>
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                type="url"
                value={formData.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                placeholder="https://nation.gov"
              />
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-sm font-medium">Emergency Contact</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="emergencyContactName">Contact Name</Label>
                <Input
                  id="emergencyContactName"
                  value={formData.emergencyContactName}
                  onChange={(e) => handleInputChange('emergencyContactName', e.target.value)}
                  placeholder="John Doe"
                />
              </div>
              <div>
                <Label htmlFor="emergencyContactPhone">Contact Phone</Label>
                <Input
                  id="emergencyContactPhone"
                  value={formData.emergencyContactPhone}
                  onChange={(e) => handleInputChange('emergencyContactPhone', e.target.value)}
                  placeholder="+64 21 123 4567"
                />
              </div>
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes..."
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              <Save className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Updating...' : 'Update Treaty'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};