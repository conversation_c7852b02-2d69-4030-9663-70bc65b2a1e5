'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { UserCheck, Save, X, Send } from 'lucide-react';
import { toast } from 'sonner';
import { NationTreatyEnvoyRole } from '@/lib/validation/nation-treaty';

interface NationTreatyEnvoyModalProps {
  treatyId: string;
  onEnvoyAdded: () => void;
  children: React.ReactNode;
}

interface UserData {
  id: string;
  name: string;
  email: string;
}

interface FormData {
  userId: string;
  role: keyof typeof NationTreatyEnvoyRole;
  title: string;
  department: string;
  contactPhone: string;
  contactEmail: string;
  jurisdiction: string;
  authorityLevel: string;
  notes: string;
}

export const NationTreatyEnvoyModal: React.FC<NationTreatyEnvoyModalProps> = ({ 
  treatyId, 
  onEnvoyAdded, 
  children 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [users, setUsers] = useState<UserData[]>([]);
  const [formData, setFormData] = useState<FormData>({
    userId: '',
    role: 'SPECIAL_ENVOY',
    title: '',
    department: '',
    contactPhone: '',
    contactEmail: '',
    jurisdiction: '',
    authorityLevel: '',
    notes: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadUsers();
    }
  }, [isOpen]);

  const loadUsers = async () => {
    setLoadingUsers(true);
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      }
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoadingUsers(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string | keyof typeof NationTreatyEnvoyRole) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/nation-treaties/${treatyId}/envoys`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Failed to add envoy');
      }

      toast.success('Envoy added successfully');
      setFormData({
        userId: '',
        role: 'SPECIAL_ENVOY',
        title: '',
        department: '',
        contactPhone: '',
        contactEmail: '',
        jurisdiction: '',
        authorityLevel: '',
        notes: '',
      });
      setIsOpen(false);
      onEnvoyAdded();
    } catch (error) {
      console.error('Error adding envoy:', error);
      toast.error('Failed to add envoy');
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedUser = users.find(user => user.id === formData.userId);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Send className="h-5 w-5 mr-2" />
            Add Nation Treaty Envoy
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="userId">Select User *</Label>
            <Select value={formData.userId} onValueChange={(value) => handleInputChange('userId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a user..." />
              </SelectTrigger>
              <SelectContent>
                {loadingUsers ? (
                  <SelectItem value="" data-disabled>Loading users...</SelectItem>
                ) : users.length === 0 ? (
                  <SelectItem value="" data-disabled>No users available</SelectItem>
                ) : (
                  users.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.name} ({user.email})
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
            {selectedUser && (
              <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                Selected: {selectedUser.name} - {selectedUser.email}
              </p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="role">Role *</Label>
              <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value as keyof typeof NationTreatyEnvoyRole)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={'SPECIAL_ENVOY'}>Special Envoy</SelectItem>
                  <SelectItem value={'AMBASSADOR'}>Ambassador</SelectItem>
                  <SelectItem value={'CONSUL'}>Consul</SelectItem>
                  <SelectItem value={'DIPLOMAT'}>Diplomat</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="title">Title/Position</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Special Envoy, Ambassador, etc."
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="department">Department</Label>
              <Input
                id="department"
                value={formData.department}
                onChange={(e) => handleInputChange('department', e.target.value)}
                placeholder="Foreign Affairs, Trade, etc."
              />
            </div>
            <div>
              <Label htmlFor="jurisdiction">Jurisdiction</Label>
              <Input
                id="jurisdiction"
                value={formData.jurisdiction}
                onChange={(e) => handleInputChange('jurisdiction', e.target.value)}
                placeholder="New Zealand, Pacific Region, etc."
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="contactPhone">Contact Phone</Label>
              <Input
                id="contactPhone"
                value={formData.contactPhone}
                onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                placeholder="+64 21 123 4567"
              />
            </div>
            <div>
              <Label htmlFor="contactEmail">Contact Email</Label>
              <Input
                id="contactEmail"
                type="email"
                value={formData.contactEmail}
                onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="authorityLevel">Authority Level</Label>
            <Input
              id="authorityLevel"
              value={formData.authorityLevel}
              onChange={(e) => handleInputChange('authorityLevel', e.target.value)}
              placeholder="Full Authority, Limited Authority, etc."
            />
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes about this envoy..."
              rows={2}
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || !formData.userId}>
              <UserCheck className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Adding...' : 'Add Envoy'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};