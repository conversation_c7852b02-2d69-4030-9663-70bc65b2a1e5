'use client';

import React, { useState, useEffect } from 'react';
import { MapPin, Phone, Mail, Globe, Building, Edit, Trash2, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface NationTreatyOffice {
  id: string;
  nationTreatyId: string;
  officeType: 'ENVOY_OFFICE' | 'CONSULATE' | 'EMBASSY';
  status: 'ACTIVE' | 'INACTIVE' | 'CLOSED';
  city: string;
  country: string;
  address: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  website?: string;
  operatingHours?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface CreateOfficeData {
  officeType: 'ENVOY_OFFICE' | 'CONSULATE' | 'EMBASSY';
  city: string;
  country: string;
  address: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  website?: string;
  operatingHours?: string;
  notes?: string;
}

interface NationTreatyOfficeModalProps {
  treatyId: string;
  children: React.ReactNode;
  onOfficeAdded?: () => void;
}

export function NationTreatyOfficeModal({ treatyId, children, onOfficeAdded }: NationTreatyOfficeModalProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CreateOfficeData>({
    officeType: 'ENVOY_OFFICE',
    city: '',
    country: '',
    address: '',
    postalCode: '',
    phone: '',
    email: '',
    website: '',
    operatingHours: '',
    notes: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch(`/api/nation-treaties/${treatyId}/offices`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setFormData({
          officeType: 'ENVOY_OFFICE',
          city: '',
          country: '',
          address: '',
          postalCode: '',
          phone: '',
          email: '',
          website: '',
          operatingHours: '',
          notes: ''
        });
        setOpen(false);
        onOfficeAdded?.();
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to add office');
      }
    } catch (error) {
      console.error('Error adding office:', error);
      alert('Failed to add office');
    } finally {
      setLoading(false);
    }
  };

  const getOfficeTypeLabel = (type: string) => {
    switch (type) {
      case 'ENVOY_OFFICE': return 'Envoy Office';
      case 'CONSULATE': return 'Consulate';
      case 'EMBASSY': return 'Embassy';
      default: return type;
    }
  };

  const getOfficeTypeColor = (type: string) => {
    switch (type) {
      case 'ENVOY_OFFICE': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100';
      case 'CONSULATE': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100';
      case 'EMBASSY': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-100';
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Nation Treaty Office</DialogTitle>
          <DialogDescription>
            Create a new office location for this nation treaty.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="officeType">Office Type</Label>
              <Select 
                value={formData.officeType} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, officeType: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select office type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ENVOY_OFFICE">Envoy Office</SelectItem>
                  <SelectItem value="CONSULATE">Consulate</SelectItem>
                  <SelectItem value="EMBASSY">Embassy</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="city">City *</Label>
              <Input
                id="city"
                value={formData.city}
                onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="country">Country *</Label>
              <Input
                id="country"
                value={formData.country}
                onChange={(e) => setFormData(prev => ({ ...prev, country: e.target.value }))}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="postalCode">Postal Code</Label>
              <Input
                id="postalCode"
                value={formData.postalCode}
                onChange={(e) => setFormData(prev => ({ ...prev, postalCode: e.target.value }))}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">Address *</Label>
            <Textarea
              id="address"
              value={formData.address}
              onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
              required
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="website">Website</Label>
            <Input
              id="website"
              type="url"
              value={formData.website}
              onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="operatingHours">Operating Hours</Label>
            <Input
              id="operatingHours"
              value={formData.operatingHours}
              onChange={(e) => setFormData(prev => ({ ...prev, operatingHours: e.target.value }))}
              placeholder="e.g., Mon-Fri 9:00 AM - 5:00 PM"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Adding...' : 'Add Office'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

interface NationTreatyOfficeListProps {
  treatyId: string;
  offices: NationTreatyOffice[];
  onOfficeRemoved?: () => void;
}

export function NationTreatyOfficeList({ treatyId, offices, onOfficeRemoved }: NationTreatyOfficeListProps) {
  const handleRemoveOffice = async (officeId: string) => {
    if (!confirm('Are you sure you want to remove this office?')) return;

    try {
      const response = await fetch(`/api/nation-treaties/${treatyId}/offices/${officeId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        onOfficeRemoved?.();
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to remove office');
      }
    } catch (error) {
      console.error('Error removing office:', error);
      alert('Failed to remove office');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100';
      case 'INACTIVE': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-100';
      case 'CLOSED': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-100';
    }
  };

  const getOfficeTypeLabel = (type: string) => {
    switch (type) {
      case 'ENVOY_OFFICE': return 'Envoy Office';
      case 'CONSULATE': return 'Consulate';
      case 'EMBASSY': return 'Embassy';
      default: return type;
    }
  };

  const getOfficeTypeColor = (type: string) => {
    switch (type) {
      case 'ENVOY_OFFICE': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100';
      case 'CONSULATE': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100';
      case 'EMBASSY': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-100';
    }
  };

  if (offices.length === 0) {
    return (
      <div className="text-center py-8 text-slate-500">
        <Building className="h-12 w-12 mx-auto mb-4 opacity-50" />
        <p className="text-lg font-medium mb-2">No Offices Found</p>
        <p className="text-sm">This nation treaty doesn't have any office locations yet.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {offices.map((office) => (
        <div key={office.id} className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-slate-200 dark:bg-slate-700 rounded-lg">
                <Building className="h-5 w-5 text-slate-600 dark:text-slate-400" />
              </div>
              <div>
                <h4 className="font-medium text-slate-900 dark:text-slate-100">
                  {office.city}, {office.country}
                </h4>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge className={getOfficeTypeColor(office.officeType)}>
                    {getOfficeTypeLabel(office.officeType)}
                  </Badge>
                  <Badge className={getStatusColor(office.status)}>
                    {office.status}
                  </Badge>
                </div>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleRemoveOffice(office.id)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-slate-600 dark:text-slate-400">
            {office.address && (
              <div className="md:col-span-2">
                <div className="flex items-start space-x-2">
                  <MapPin className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>{office.address}</span>
                </div>
              </div>
            )}
            
            {office.postalCode && (
              <div>
                <strong className="text-slate-700 dark:text-slate-300">Postal Code:</strong>
                <p className="mt-1">{office.postalCode}</p>
              </div>
            )}

            {office.phone && (
              <div>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4" />
                  <span>{office.phone}</span>
                </div>
              </div>
            )}

            {office.email && (
              <div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4" />
                  <span>{office.email}</span>
                </div>
              </div>
            )}

            {office.website && (
              <div className="md:col-span-2">
                <div className="flex items-center space-x-2">
                  <Globe className="h-4 w-4" />
                  <a 
                    href={office.website} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    {office.website}
                  </a>
                </div>
              </div>
            )}

            {office.operatingHours && (
              <div className="md:col-span-2">
                <strong className="text-slate-700 dark:text-slate-300">Hours:</strong>
                <p className="mt-1">{office.operatingHours}</p>
              </div>
            )}

            {office.notes && (
              <div className="md:col-span-2">
                <strong className="text-slate-700 dark:text-slate-300">Notes:</strong>
                <p className="mt-1">{office.notes}</p>
              </div>
            )}
          </div>

          <div className="mt-3 text-xs text-slate-500">
            Created {new Date(office.createdAt).toLocaleDateString()}
          </div>
        </div>
      ))}
    </div>
  );
}