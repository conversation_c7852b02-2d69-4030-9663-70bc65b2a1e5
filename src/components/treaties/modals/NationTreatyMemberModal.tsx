'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { UserPlus, Save, X, Users } from 'lucide-react';
import { toast } from 'sonner';
import { NationTreatyMemberRole } from '@/lib/validation/nation-treaty';

interface NationTreatyMemberModalProps {
  treatyId: string;
  onMemberAdded: () => void;
  children: React.ReactNode;
}

interface UserData {
  id: string;
  name: string;
  email: string;
}

interface FormData {
  userId: string;
  role: keyof typeof NationTreatyMemberRole;
  title: string;
  responsibilities: string;
  notes: string;
}

export const NationTreatyMemberModal: React.FC<NationTreatyMemberModalProps> = ({ 
  treatyId, 
  onMemberAdded, 
  children 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [users, setUsers] = useState<UserData[]>([]);
  const [formData, setFormData] = useState<FormData>({
    userId: '',
    role: 'MEMBER',
    title: '',
    responsibilities: '',
    notes: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadUsers();
    }
  }, [isOpen]);

  const loadUsers = async () => {
    setLoadingUsers(true);
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      }
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoadingUsers(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string | keyof typeof NationTreatyMemberRole) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/nation-treaties/${treatyId}/members`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Failed to add member');
      }

      toast.success('Member added successfully');
      setFormData({
        userId: '',
        role: 'MEMBER',
        title: '',
        responsibilities: '',
        notes: '',
      });
      setIsOpen(false);
      onMemberAdded();
    } catch (error) {
      console.error('Error adding member:', error);
      toast.error('Failed to add member');
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedUser = users.find(user => user.id === formData.userId);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Add Nation Treaty Member
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="userId">Select User *</Label>
            <Select value={formData.userId} onValueChange={(value) => handleInputChange('userId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a user..." />
              </SelectTrigger>
              <SelectContent>
                {loadingUsers ? (
                  <SelectItem value="" data-disabled>Loading users...</SelectItem>
                ) : users.length === 0 ? (
                  <SelectItem value="" data-disabled>No users available</SelectItem>
                ) : (
                  users.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.name} ({user.email})
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
            {selectedUser && (
              <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                Selected: {selectedUser.name} - {selectedUser.email}
              </p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="role">Role *</Label>
              <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value as keyof typeof NationTreatyMemberRole)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='ADMIN'>Admin</SelectItem>
                  <SelectItem value='ENVOY'>Envoy</SelectItem>
                  <SelectItem value='MEMBER'>Member</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="title">Title/Position</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Chief Representative, Ambassador, etc."
              />
            </div>
          </div>

          <div>
            <Label htmlFor="responsibilities">Responsibilities</Label>
            <Textarea
              id="responsibilities"
              value={formData.responsibilities}
              onChange={(e) => handleInputChange('responsibilities', e.target.value)}
              placeholder="Key responsibilities and duties..."
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes about this member..."
              rows={2}
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || !formData.userId}>
              <UserPlus className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Adding...' : 'Add Member'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};