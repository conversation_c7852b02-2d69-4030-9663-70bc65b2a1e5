'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  DollarSign, 
  Users, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Search,
  Download,
  Eye,
  Filter,
  Globe,
  Shield,
  TrendingUp,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Crown
} from 'lucide-react';
import { format } from 'date-fns';

// Types for comprehensive treaty auditing
interface NationTreatyAudit {
  id: string;
  name: string;
  officialName: string;
  status: 'ACTIVE' | 'SUSPENDED' | 'TERMINATED';
  contactEmail?: string;
  contactPhone?: string;
  contactAddress?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  membersCount: number;
  envoysCount: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: {
    name: string;
    email: string;
  };
}

interface TradeTreatyAudit {
  id: string;
  title: string;
  status: 'ACTIVE' | 'EXPIRED' | 'SUSPENDED';
  treatyTypes: Array<{ id: string; name: string }>;
  signedDate: Date;
  expirationDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  applicationsCount: number;
  activeMembersCount: number;
  totalRevenue: number;
}

interface TreatyApplicationAudit {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  treatyTypeName: string;
  status: 'APPLIED' | 'UNDER_REVIEW' | 'APPROVED' | 'AWAITING_PAYMENT' | 'PAID' | 'ACTIVE' | 'REJECTED';
  paymentStatus: 'AWAITING_PAYMENT' | 'PENDING' | 'PAID' | 'FAILED' | null;
  appliedAt: Date;
  approvedAt?: Date;
  latestPayment?: {
    amount: number;
    currency: string;
    status: string;
    paymentDate: Date;
  };
}

interface TreatyMemberAudit {
  id: string;
  userName: string;
  userEmail: string;
  nationTreatyName: string;
  role: 'LEADER' | 'REPRESENTATIVE' | 'MEMBER';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  joinedAt: Date;
  contactInfo?: {
    phone?: string;
    address?: string;
  };
}

interface TreatyEnvoyAudit {
  id: string;
  userName: string;
  userEmail: string;
  nationTreatyName: string;
  title: string;
  role: 'SPECIAL_ENVOY' | 'AMBASSADOR' | 'CONSUL' | 'DIPLOMAT';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  appointedAt: Date;
  contactInfo?: {
    phone?: string;
    email?: string;
    jurisdiction?: string;
  };
}

interface AuditLog {
  id: string;
  userName: string;
  action: string;
  resource: string;
  resourceId: string;
  success: boolean;
  ipAddress: string;
  createdAt: Date;
  details?: string;
}

interface TreatyAuditStats {
  // Nation Treaties Stats
  totalNationTreaties: number;
  activeNationTreaties: number;
  suspendedNationTreaties: number;
  totalNationMembers: number;
  totalNationEnvoys: number;
  
  // Trade Treaties Stats
  totalTradeTreaties: number;
  activeTradeTreaties: number;
  totalTradeApplications: number;
  activeTradeMembers: number;
  
  // Combined Stats
  totalRevenue: number;
  pendingApplications: number;
  rejectedApplications: number;
  totalAuditLogs: number;
}

export function TreatyAuditTab() {
  const [nationTreaties, setNationTreaties] = useState<NationTreatyAudit[]>([]);
  const [tradeTreaties, setTradeTreaties] = useState<TradeTreatyAudit[]>([]);
  const [applications, setApplications] = useState<TreatyApplicationAudit[]>([]);
  const [members, setMembers] = useState<TreatyMemberAudit[]>([]);
  const [envoys, setEnvoys] = useState<TreatyEnvoyAudit[]>([]);
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [stats, setStats] = useState<TreatyAuditStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'nation-treaties' | 'trade-treaties' | 'applications' | 'members' | 'envoys' | 'audit-logs'>('overview');
  const [filters, setFilters] = useState({
    status: '',
    search: '',
    dateFrom: '',
    dateTo: '',
    treatyType: ''
  });

  useEffect(() => {
    fetchAuditData();
  }, []);

  const fetchAuditData = async () => {
    try {
      setLoading(true);
      
      // Fetch data from multiple APIs
      const [
        nationTreatiesRes, 
        tradeTreatiesRes, 
        applicationsRes, 
        auditLogsRes,
        nationMembersRes,
        nationEnvoysRes
      ] = await Promise.all([
        fetch('/api/nation-treaties'),
        fetch('/api/treaties'),
        fetch('/api/admin/treaty-applications'),
        fetch('/api/audit/log'),
        fetch('/api/nation-treaties/members/all'),
        fetch('/api/nation-treaties/envoys/all')
      ]);

      // Process nation treaties data
      if (nationTreatiesRes.ok) {
        const data = await nationTreatiesRes.json();
        setNationTreaties(data.data || []);
      }

      // Process trade treaties data
      if (tradeTreatiesRes.ok) {
        const data = await tradeTreatiesRes.json();
        setTradeTreaties(data.treaties || []);
      }

      // Process applications data
      if (applicationsRes.ok) {
        const data = await applicationsRes.json();
        setApplications(data.applications || []);
      }

      // Process audit logs
      if (auditLogsRes.ok) {
        const data = await auditLogsRes.json();
        const treatyLogs = (data.logs || []).filter((log: AuditLog) => 
          log.resource.toLowerCase().includes('treaty') ||
          log.action.toLowerCase().includes('treaty')
        );
        setAuditLogs(treatyLogs);
      }

      // Calculate statistics
      calculateStats();

    } catch (error) {
      console.error('Error fetching audit data:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = () => {
    const activeNation = nationTreaties.filter(t => t.status === 'ACTIVE').length;
    const suspendedNation = nationTreaties.filter(t => t.status === 'SUSPENDED').length;
    const totalNationMembers = nationTreaties.reduce((sum, t) => sum + t.membersCount, 0);
    const totalNationEnvoys = nationTreaties.reduce((sum, t) => sum + t.envoysCount, 0);

    const activeTrade = tradeTreaties.filter(t => t.status === 'ACTIVE').length;
    const pendingApps = applications.filter(a => a.status === 'APPLIED' || a.status === 'UNDER_REVIEW').length;
    const rejectedApps = applications.filter(a => a.status === 'REJECTED').length;
    const activeTradeMembers = tradeTreaties.reduce((sum, t) => sum + t.activeMembersCount, 0);
    const totalRevenue = tradeTreaties.reduce((sum, t) => sum + (t.totalRevenue || 0), 0);

    setStats({
      totalNationTreaties: nationTreaties.length,
      activeNationTreaties: activeNation,
      suspendedNationTreaties: suspendedNation,
      totalNationMembers,
      totalNationEnvoys,
      totalTradeTreaties: tradeTreaties.length,
      activeTradeTreaties: activeTrade,
      totalTradeApplications: applications.length,
      activeTradeMembers,
      totalRevenue,
      pendingApplications: pendingApps,
      rejectedApplications: rejectedApps,
      totalAuditLogs: auditLogs.length
    });
  };

  const getStatusBadge = (status: string, type: 'treaty' | 'application' | 'member' = 'treaty') => {
    const statusConfig = {
      // Treaty statuses
      'ACTIVE': { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Active' },
      'SUSPENDED': { color: 'bg-yellow-100 text-yellow-800', icon: Clock, label: 'Suspended' },
      'TERMINATED': { color: 'bg-red-100 text-red-800', icon: XCircle, label: 'Terminated' },
      'EXPIRED': { color: 'bg-red-100 text-red-800', icon: XCircle, label: 'Expired' },
      
      // Application statuses
      'APPLIED': { color: 'bg-blue-100 text-blue-800', icon: FileText, label: 'Applied' },
      'UNDER_REVIEW': { color: 'bg-yellow-100 text-yellow-800', icon: Eye, label: 'Under Review' },
      'APPROVED': { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Approved' },
      'AWAITING_PAYMENT': { color: 'bg-orange-100 text-orange-800', icon: DollarSign, label: 'Awaiting Payment' },
      'PAID': { color: 'bg-purple-100 text-purple-800', icon: DollarSign, label: 'Paid' },
      'REJECTED': { color: 'bg-red-100 text-red-800', icon: XCircle, label: 'Rejected' },
      'PENDING': { color: 'bg-yellow-100 text-yellow-800', icon: Clock, label: 'Pending' },
      'FAILED': { color: 'bg-red-100 text-red-800', icon: XCircle, label: 'Failed' },
      
      // Member statuses
      'INACTIVE': { color: 'bg-gray-100 text-gray-800', icon: Clock, label: 'Inactive' },
      
      // Default
      'default': { color: 'bg-gray-100 text-gray-800', icon: AlertCircle, label: status }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.default;
    const Icon = config.icon;
    
    return (
      <Badge variant="secondary" className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      'LEADER': { color: 'bg-purple-100 text-purple-800', label: 'Leader' },
      'REPRESENTATIVE': { color: 'bg-blue-100 text-blue-800', label: 'Representative' },
      'MEMBER': { color: 'bg-green-100 text-green-800', label: 'Member' },
      'SPECIAL_ENVOY': { color: 'bg-orange-100 text-orange-800', label: 'Special Envoy' },
      'AMBASSADOR': { color: 'bg-red-100 text-red-800', label: 'Ambassador' },
      'CONSUL': { color: 'bg-indigo-100 text-indigo-800', label: 'Consul' },
      'DIPLOMAT': { color: 'bg-teal-100 text-teal-800', label: 'Diplomat' }
    };

    const config = roleConfig[role as keyof typeof roleConfig] || { color: 'bg-gray-100 text-gray-800', label: role };
    
    return (
      <Badge variant="secondary" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const exportData = () => {
    let data: any[] = [];
    let filename = '';

    switch (activeTab) {
      case 'nation-treaties':
        data = nationTreaties;
        filename = 'nation-treaties-audit';
        break;
      case 'trade-treaties':
        data = tradeTreaties;
        filename = 'trade-treaties-audit';
        break;
      case 'applications':
        data = applications;
        filename = 'treaty-applications-audit';
        break;
      case 'members':
        data = members;
        filename = 'treaty-members-audit';
        break;
      case 'envoys':
        data = envoys;
        filename = 'treaty-envoys-audit';
        break;
      case 'audit-logs':
        data = auditLogs;
        filename = 'treaty-audit-logs';
        break;
      default:
        return;
    }

    const csv = convertToCSV(data);
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${filename}-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const convertToCSV = (data: any[]) => {
    if (data.length === 0) return '';
    
    const headers = Object.keys(data[0]).filter(key => !key.includes('password'));
    const csvHeaders = headers.join(',');
    const csvRows = data.map(row => 
      headers.map(header => {
        const value = row[header];
        if (typeof value === 'object' && value !== null) {
          return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
        }
        return `"${value}"`;
      }).join(',')
    );
    
    return [csvHeaders, ...csvRows].join('\n');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400">Loading treaty audit data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
      {/* Header */}
      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div>
          <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200">Treaty Audit</h2>
          <p className="text-slate-600 dark:text-slate-400">Comprehensive audit of nation treaties, trade treaties, and related activities</p>
        </div>
        <Button onClick={exportData} variant="outline">
          <Download className="w-4 h-4 mr-2" />
          Export CSV
        </Button>
      </div>

      {/* Statistics Summary */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          {/* Nation Treaties */}
          <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg">
            <div className="flex items-center">
              <Globe className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-blue-600 dark:text-blue-300">Nation Treaties</p>
                <p className="text-2xl font-semibold">{stats.totalNationTreaties}</p>
                <p className="text-xs text-blue-500">{stats.activeNationTreaties} active</p>
              </div>
            </div>
          </div>

          {/* Trade Treaties */}
          <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-green-600 dark:text-green-300">Trade Treaties</p>
                <p className="text-2xl font-semibold">{stats.totalTradeTreaties}</p>
                <p className="text-xs text-green-500">{stats.activeTradeTreaties} active</p>
              </div>
            </div>
          </div>

          {/* Total Members */}
          <div className="bg-purple-50 dark:bg-purple-900/30 p-4 rounded-lg">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-purple-600 dark:text-purple-300">Total Members</p>
                <p className="text-2xl font-semibold">{stats.totalNationMembers + stats.activeTradeMembers}</p>
                <p className="text-xs text-purple-500">{stats.totalNationMembers} nation + {stats.activeTradeMembers} trade</p>
              </div>
            </div>
          </div>

          {/* Revenue */}
          <div className="bg-emerald-50 dark:bg-emerald-900/30 p-4 rounded-lg">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-emerald-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-emerald-600 dark:text-emerald-300">Total Revenue</p>
                <p className="text-2xl font-semibold">${stats.totalRevenue.toFixed(2)}</p>
                <p className="text-xs text-emerald-500">{stats.pendingApplications} pending</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="nation-treaties">Nation Treaties</TabsTrigger>
          <TabsTrigger value="trade-treaties">Trade Treaties</TabsTrigger>
          <TabsTrigger value="applications">Applications</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="envoys">Envoys</TabsTrigger>
          <TabsTrigger value="audit-logs">Audit Logs</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Nation Treaties Overview */}
            <div className="bg-slate-50 dark:bg-slate-900/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Globe className="h-5 w-5 mr-2" />
                Nation Treaties Overview
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span>Total Nation Treaties:</span>
                  <span className="font-semibold">{stats?.totalNationTreaties}</span>
                </div>
                <div className="flex justify-between">
                  <span>Active:</span>
                  <span className="text-green-600 font-semibold">{stats?.activeNationTreaties}</span>
                </div>
                <div className="flex justify-between">
                  <span>Suspended:</span>
                  <span className="text-yellow-600 font-semibold">{stats?.suspendedNationTreaties}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Members:</span>
                  <span className="font-semibold">{stats?.totalNationMembers}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Envoys:</span>
                  <span className="font-semibold">{stats?.totalNationEnvoys}</span>
                </div>
              </div>
            </div>

            {/* Trade Treaties Overview */}
            <div className="bg-slate-50 dark:bg-slate-900/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Trade Treaties Overview
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span>Total Trade Treaties:</span>
                  <span className="font-semibold">{stats?.totalTradeTreaties}</span>
                </div>
                <div className="flex justify-between">
                  <span>Active:</span>
                  <span className="text-green-600 font-semibold">{stats?.activeTradeTreaties}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Applications:</span>
                  <span className="font-semibold">{stats?.totalTradeApplications}</span>
                </div>
                <div className="flex justify-between">
                  <span>Active Members:</span>
                  <span className="font-semibold">{stats?.activeTradeMembers}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Revenue:</span>
                  <span className="text-green-600 font-semibold">${stats?.totalRevenue?.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Nation Treaties Tab */}
        <TabsContent value="nation-treaties">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
              <thead className="bg-slate-50 dark:bg-slate-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Treaty Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Members</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Envoys</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Contact</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Created</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                {nationTreaties.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-slate-500 dark:text-slate-400">
                      No nation treaties found
                    </td>
                  </tr>
                ) : (
                  nationTreaties.map((treaty) => (
                    <tr key={treaty.id} className="hover:bg-slate-50 dark:hover:bg-slate-700">
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm font-medium text-slate-900 dark:text-slate-100">{treaty.name}</div>
                          <div className="text-sm text-slate-500 dark:text-slate-400">{treaty.officialName}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        {getStatusBadge(treaty.status)}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {treaty.membersCount}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {treaty.envoysCount}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {treaty.contactEmail && (
                          <div className="flex items-center space-x-1">
                            <Mail className="h-3 w-3" />
                            <span>{treaty.contactEmail}</span>
                          </div>
                        )}
                        {treaty.contactPhone && (
                          <div className="flex items-center space-x-1">
                            <Phone className="h-3 w-3" />
                            <span>{treaty.contactPhone}</span>
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-500 dark:text-slate-400">
                        {format(new Date(treaty.createdAt), 'MMM dd, yyyy')}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>

        {/* Trade Treaties Tab */}
        <TabsContent value="trade-treaties">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
              <thead className="bg-slate-50 dark:bg-slate-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Treaty Title</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Treaty Types</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Applications</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Active Members</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Revenue</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                {tradeTreaties.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-slate-500 dark:text-slate-400">
                      No trade treaties found
                    </td>
                  </tr>
                ) : (
                  tradeTreaties.map((treaty) => (
                    <tr key={treaty.id} className="hover:bg-slate-50 dark:hover:bg-slate-700">
                      <td className="px-6 py-4">
                        <div className="text-sm font-medium text-slate-900 dark:text-slate-100">{treaty.title}</div>
                      </td>
                      <td className="px-6 py-4">
                        {getStatusBadge(treaty.status)}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {treaty.treatyTypes?.map(type => type.name).join(', ') || 'N/A'}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {treaty.applicationsCount}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {treaty.activeMembersCount}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        ${treaty.totalRevenue?.toFixed(2) || '0.00'}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>

        {/* Applications Tab */}
        <TabsContent value="applications">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
              <thead className="bg-slate-50 dark:bg-slate-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">User</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Treaty Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Payment Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Applied Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Latest Payment</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                {applications.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-slate-500 dark:text-slate-400">
                      No applications found
                    </td>
                  </tr>
                ) : (
                  applications.map((app) => (
                    <tr key={app.id} className="hover:bg-slate-50 dark:hover:bg-slate-700">
                      <td className="px-6 py-4">
                        <div className="text-sm font-medium text-slate-900 dark:text-slate-100">{app.userName}</div>
                        <div className="text-sm text-slate-500 dark:text-slate-400">{app.userEmail}</div>
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {app.treatyTypeName}
                      </td>
                      <td className="px-6 py-4">
                        {getStatusBadge(app.status, 'application')}
                      </td>
                      <td className="px-6 py-4">
                        {app.paymentStatus ? getStatusBadge(app.paymentStatus, 'application') : <span className="text-sm text-slate-500">-</span>}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-500 dark:text-slate-400">
                        {format(new Date(app.appliedAt), 'MMM dd, yyyy')}
                      </td>
                      <td className="px-6 py-4">
                        {app.latestPayment ? (
                          <div className="text-sm">
                            <div className="text-slate-900 dark:text-slate-100">${app.latestPayment.amount} {app.latestPayment.currency}</div>
                            <div>{getStatusBadge(app.latestPayment.status, 'application')}</div>
                          </div>
                        ) : (
                          <span className="text-sm text-slate-500">-</span>
                        )}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>

        {/* Members Tab */}
        <TabsContent value="members">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
              <thead className="bg-slate-50 dark:bg-slate-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Member</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Nation Treaty</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Role</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Joined Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Contact</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                {members.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-slate-500 dark:text-slate-400">
                      No members found
                    </td>
                  </tr>
                ) : (
                  members.map((member) => (
                    <tr key={member.id} className="hover:bg-slate-50 dark:hover:bg-slate-700">
                      <td className="px-6 py-4">
                        <div className="text-sm font-medium text-slate-900 dark:text-slate-100">{member.userName}</div>
                        <div className="text-sm text-slate-500 dark:text-slate-400">{member.userEmail}</div>
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {member.nationTreatyName}
                      </td>
                      <td className="px-6 py-4">
                        {getRoleBadge(member.role)}
                      </td>
                      <td className="px-6 py-4">
                        {getStatusBadge(member.status, 'member')}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-500 dark:text-slate-400">
                        {format(new Date(member.joinedAt), 'MMM dd, yyyy')}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {member.contactInfo?.phone && (
                          <div className="flex items-center space-x-1">
                            <Phone className="h-3 w-3" />
                            <span>{member.contactInfo.phone}</span>
                          </div>
                        )}
                        {member.contactInfo?.address && (
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3" />
                            <span className="truncate">{member.contactInfo.address}</span>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>

        {/* Envoys Tab */}
        <TabsContent value="envoys">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
              <thead className="bg-slate-50 dark:bg-slate-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Envoy</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Nation Treaty</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Title</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Role</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Jurisdiction</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                {envoys.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-slate-500 dark:text-slate-400">
                      No envoys found
                    </td>
                  </tr>
                ) : (
                  envoys.map((envoy) => (
                    <tr key={envoy.id} className="hover:bg-slate-50 dark:hover:bg-slate-700">
                      <td className="px-6 py-4">
                        <div className="text-sm font-medium text-slate-900 dark:text-slate-100">{envoy.userName}</div>
                        <div className="text-sm text-slate-500 dark:text-slate-400">{envoy.userEmail}</div>
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {envoy.nationTreatyName}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {envoy.title}
                      </td>
                      <td className="px-6 py-4">
                        {getRoleBadge(envoy.role)}
                      </td>
                      <td className="px-6 py-4">
                        {getStatusBadge(envoy.status, 'member')}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {envoy.contactInfo?.jurisdiction || 'N/A'}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>

        {/* Audit Logs Tab */}
        <TabsContent value="audit-logs">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
              <thead className="bg-slate-50 dark:bg-slate-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Timestamp</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">User</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Action</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Resource</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">IP Address</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                {auditLogs.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-slate-500 dark:text-slate-400">
                      No audit logs found
                    </td>
                  </tr>
                ) : (
                  auditLogs.map((log) => (
                    <tr key={log.id} className="hover:bg-slate-50 dark:hover:bg-slate-700">
                      <td className="px-6 py-4 text-sm text-slate-500 dark:text-slate-400">
                        {format(new Date(log.createdAt), 'MMM dd, yyyy HH:mm')}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {log.userName}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {log.action}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                        {log.resource}
                      </td>
                      <td className="px-6 py-4">
                        {log.success ? (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Success
                          </Badge>
                        ) : (
                          <Badge variant="secondary" className="bg-red-100 text-red-800">
                            <XCircle className="w-3 h-3 mr-1" />
                            Failed
                          </Badge>
                        )}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-500 dark:text-slate-400">
                        {log.ipAddress}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}