'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Plus, FileText, Eye, Edit, Archive, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { TreatyCardData } from './TreatyCard';

interface TreatyGroup {
  treatyId: string;
  treatyName: string;
  treatyTypeApplications: TreatyCardData[];
  createdAt: string;
  status: 'active' | 'completed' | 'archived';
}

interface TreatyGroupingProps {
  treatyGroups: TreatyGroup[];
  onViewApplication: (application: TreatyCardData) => void;
  onEditApplication: (application: TreatyCardData) => void;
  onArchiveApplication: (application: TreatyCardData) => void;
  onDownloadPDF: (application: TreatyCardData) => void;
  onCreateNewApplication: (treatyId: string) => void;
  onCreateNewTreaty: () => void;
}

export const TreatyGrouping: React.FC<TreatyGroupingProps> = ({
  treatyGroups,
  onViewApplication,
  onEditApplication,
  onArchiveApplication,
  onDownloadPDF,
  onCreateNewApplication,
  onCreateNewTreaty
}) => {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  const toggleGroup = (treatyId: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(treatyId)) {
      newExpanded.delete(treatyId);
    } else {
      newExpanded.add(treatyId);
    }
    setExpandedGroups(newExpanded);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'archived':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const getApplicationStatusSummary = (applications: TreatyCardData[]) => {
    const statusCounts = applications.reduce((acc, app) => {
      acc[app.status] = (acc[app.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(statusCounts)
      .map(([status, count]) => `${count} ${status}`)
      .join(', ');
  };

  if (treatyGroups.length === 0) {
    return (
      <div className="text-center py-12">
        <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No treaties found</h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Get started by creating your first treaty.
        </p>
        <div className="mt-6">
          <Button onClick={onCreateNewTreaty}>
            <Plus className="h-4 w-4 mr-2" />
            Create New Treaty
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with Create New Treaty button */}
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Treaty Applications
        </h2>
        <Button onClick={onCreateNewTreaty} variant="outline">
          <Plus className="h-4 w-4 mr-2" />
          New Treaty
        </Button>
      </div>

      {/* Treaty Groups */}
      <div className="space-y-2">
        {treatyGroups.map((group) => (
          <div
            key={group.treatyId}
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden"
          >
            {/* Treaty Header */}
            <div
              className="px-4 py-3 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              onClick={() => toggleGroup(group.treatyId)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {expandedGroups.has(group.treatyId) ? (
                    <ChevronDown className="h-5 w-5 text-gray-400" />
                  ) : (
                    <ChevronRight className="h-5 w-5 text-gray-400" />
                  )}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {group.treatyName}
                    </h3>
                    <div className="flex items-center space-x-4 mt-1">
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {group.treatyTypeApplications.length} application{group.treatyTypeApplications.length !== 1 ? 's' : ''}
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(group.status)}`}>
                        {group.status}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {getApplicationStatusSummary(group.treatyTypeApplications)}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onCreateNewApplication(group.treatyId);
                    }}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Application
                  </Button>
                </div>
              </div>
            </div>

            {/* Treaty Applications */}
            {expandedGroups.has(group.treatyId) && (
              <div className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {group.treatyTypeApplications.map((application) => (
                    <div
                      key={application.id}
                      className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
                    >
                      {/* Application Header */}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {application.treatyTypeName}
                          </h4>
                          <div className="flex items-center mt-1 space-x-2">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}>
                              {application.status}
                            </span>
                            {application.hasSignature && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border border-gray-300 dark:border-gray-600">
                                Signed
                              </span>
                            )}
                            {application.hasAttachments && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border border-gray-300 dark:border-gray-600">
                                Attachments
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Application Details */}
                      <div className="space-y-2 mb-3">
                        {application.applicantName && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <span className="font-medium">Applicant:</span> {application.applicantName}
                          </p>
                        )}
                        {application.businessName && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <span className="font-medium">Business:</span> {application.businessName}
                          </p>
                        )}
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium">Created:</span>{' '}
                          {new Date(application.createdAt).toLocaleDateString()}
                        </p>
                      </div>

                      {/* Application Actions */}
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onViewApplication(application)}
                          className="flex-1"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>

                        {application.status === 'draft' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onEditApplication(application)}
                            className="flex-1"
                          >
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                        )}

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onDownloadPDF(application)}
                          className="flex-1"
                        >
                          <Download className="h-3 w-3 mr-1" />
                          PDF
                        </Button>

                        {application.status !== 'archived' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onArchiveApplication(application)}
                            className="flex-1 text-orange-600 hover:text-orange-700"
                          >
                            <Archive className="h-3 w-3 mr-1" />
                            Archive
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};