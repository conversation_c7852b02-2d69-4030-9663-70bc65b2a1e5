'use client';

import React from 'react';

interface VerticalMenuItem {
  id: string;
  label: string;
  href: string;
}

interface VerticalMenuProps {
  items: VerticalMenuItem[];
  activeItem: string;
  onItemSelect: (itemId: string) => void;
  className?: string;
  title?: string;
}

export const VerticalMenu: React.FC<VerticalMenuProps> = ({
  items,
  activeItem,
  onItemSelect,
  className,
  title
}) => {
  const menuTitle = title || 'Treaty Management';
  
  return (
    <div className={`w-64 bg-slate-800 text-white shadow-lg flex-shrink-0 rounded-lg ${className || ''}`}>
      <nav className="p-4">
        <div className="space-y-6">
          <div>
            <h3 className="text-sm font-semibold text-slate-300 uppercase tracking-wider mb-3">
              {menuTitle}
            </h3>
            <ul className="space-y-1">
              {items.map((item) => {
                const isActive = activeItem === item.id;
                return (
                  <li key={item.id}>
                    <button
                      onClick={() => onItemSelect(item.id)}
                      className={`block w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                        isActive
                          ? 'bg-slate-700 text-white border-l-2 border-blue-400'
                          : 'text-slate-300 hover:bg-slate-700 hover:text-white'
                      }`}
                    >
                      {item.label}
                    </button>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      </nav>
    </div>
  );
};