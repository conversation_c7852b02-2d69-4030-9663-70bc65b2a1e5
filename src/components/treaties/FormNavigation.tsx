'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Save, X } from 'lucide-react';

interface FormNavigationProps {
  onPrevious: () => void;
  onNext: () => void;
  onCancel: () => void;
  canGoNext: boolean;
  canGoPrevious: boolean;
  isLastSection: boolean;
  isSubmitting?: boolean;
}

export const FormNavigation: React.FC<FormNavigationProps> = ({
  onPrevious,
  onNext,
  onCancel,
  canGoNext,
  canGoPrevious,
  isLastSection,
  isSubmitting = false
}) => {
  return (
    <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
      <div className="flex space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
          className="flex items-center space-x-2"
        >
          <X className="w-4 h-4" />
          <span>Cancel</span>
        </Button>

        {canGoPrevious && (
          <Button
            type="button"
            variant="outline"
            onClick={onPrevious}
            disabled={isSubmitting}
            className="flex items-center space-x-2"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>Previous</span>
          </Button>
        )}
      </div>

      <div className="flex space-x-3">
        {canGoNext && !isLastSection && (
          <Button
            type="button"
            onClick={onNext}
            disabled={isSubmitting}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700"
          >
            <span>Next</span>
            <ChevronRight className="w-4 h-4" />
          </Button>
        )}

        {isLastSection && (
          <div className="flex space-x-3">
            <Button
              type="button"
              variant="outline"
              disabled={isSubmitting}
              className="flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>{isSubmitting ? 'Saving...' : 'Save Draft'}</span>
            </Button>

            <Button
              type="button"
              disabled={isSubmitting}
              className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
            >
              <span>{isSubmitting ? 'Submitting...' : 'Submit Form'}</span>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};