'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { 
  Plus, 
  Edit, 
  Trash2, 
  DollarSign, 
  Settings, 
  Save, 
  X,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { toast } from 'sonner'

interface TreatyType {
  id: string
  name: string
  description: string | null
  category: string
  price: number
  currency: string
  requiresPayment: boolean
  paymentDeadlineDays: number | null
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

interface TreatyFormData {
  name: string
  description: string
  category: string
  price: number
  currency: string
  requiresPayment: boolean
  paymentDeadlineDays: number
  isActive: boolean
}

const CATEGORIES = [
  'Micro Organizations',
  'Small Organizations', 
  'Medium Organizations',
  'Large Organizations',
  'NGOs and Charitable Trusts',
  'Educational Institutions',
  'Government Organizations',
  'International Organizations'
]

const CURRENCIES = [
  'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'CHF', 'JPY', 'CNY'
]

interface TreatyTypesManagementProps {
  onTreatyTypeUpdated?: () => void
}

export function TreatyTypesManagement({ onTreatyTypeUpdated }: TreatyTypesManagementProps) {
  const [treatyTypes, setTreatyTypes] = useState<TreatyType[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingTreatyType, setEditingTreatyType] = useState<TreatyType | null>(null)
  const [submitting, setSubmitting] = useState(false)

  const [formData, setFormData] = useState<TreatyFormData>({
    name: '',
    description: '',
    category: '',
    price: 0,
    currency: 'USD',
    requiresPayment: true,
    paymentDeadlineDays: 30,
    isActive: true
  })

  const fetchTreatyTypes = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/treaty-types')
      if (response.ok) {
        const data = await response.json()
        setTreatyTypes(data.treatyTypes || [])
      } else {
        toast.error('Failed to fetch treaty types')
      }
    } catch (error) {
      console.error('Error fetching treaty types:', error)
      toast.error('Failed to fetch treaty types')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTreatyTypes()
  }, [])

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      category: '',
      price: 0,
      currency: 'USD',
      requiresPayment: true,
      paymentDeadlineDays: 30,
      isActive: true
    })
  }

  const handleCreate = async () => {
    if (!formData.name.trim() || !formData.category) {
      toast.error('Name and category are required')
      return
    }

    setSubmitting(true)
    try {
      const response = await fetch('/api/treaty-types', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        toast.success('Treaty type created successfully')
        setIsCreateDialogOpen(false)
        resetForm()
        fetchTreatyTypes()
        onTreatyTypeUpdated?.()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to create treaty type')
      }
    } catch (error) {
      console.error('Error creating treaty type:', error)
      toast.error('Failed to create treaty type')
    } finally {
      setSubmitting(false)
    }
  }

  const handleUpdate = async () => {
    if (!editingTreatyType || !formData.name.trim() || !formData.category) {
      toast.error('Name and category are required')
      return
    }

    setSubmitting(true)
    try {
      const response = await fetch(`/api/treaty-types/${editingTreatyType.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        toast.success('Treaty type updated successfully')
        setIsEditDialogOpen(false)
        setEditingTreatyType(null)
        resetForm()
        fetchTreatyTypes()
        onTreatyTypeUpdated?.()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to update treaty type')
      }
    } catch (error) {
      console.error('Error updating treaty type:', error)
      toast.error('Failed to update treaty type')
    } finally {
      setSubmitting(false)
    }
  }

  const handleDelete = async (treatyType: TreatyType) => {
    if (!confirm(`Are you sure you want to delete "${treatyType.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/treaty-types/${treatyType.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast.success('Treaty type deleted successfully')
        fetchTreatyTypes()
        onTreatyTypeUpdated?.()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to delete treaty type')
      }
    } catch (error) {
      console.error('Error deleting treaty type:', error)
      toast.error('Failed to delete treaty type')
    }
  }

  const openEditDialog = (treatyType: TreatyType) => {
    setEditingTreatyType(treatyType)
    setFormData({
      name: treatyType.name,
      description: treatyType.description || '',
      category: treatyType.category,
      price: treatyType.price,
      currency: treatyType.currency,
      requiresPayment: treatyType.requiresPayment,
      paymentDeadlineDays: treatyType.paymentDeadlineDays || 30,
      isActive: treatyType.isActive
    })
    setIsEditDialogOpen(true)
  }

  const closeCreateDialog = () => {
    setIsCreateDialogOpen(false)
    resetForm()
  }

  const closeEditDialog = () => {
    setIsEditDialogOpen(false)
    setEditingTreatyType(null)
    resetForm()
  }

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="secondary" className="bg-green-100 text-green-800">
        <CheckCircle className="w-3 h-3 mr-1" />
        Active
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-800">
        <XCircle className="w-3 h-3 mr-1" />
        Inactive
      </Badge>
    )
  }

  const TreatyForm = ({ onSubmit, onCancel, isEdit = false }: {
    onSubmit: () => void
    onCancel: () => void
    isEdit?: boolean
  }) => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="name">Treaty Type Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Enter treaty type name"
          />
        </div>
        <div>
          <Label htmlFor="category">Category *</Label>
          <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {CATEGORIES.map(category => (
                <SelectItem key={category} value={category}>{category}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Enter treaty type description"
          rows={3}
        />
      </div>

      <div className="space-y-4">
        <h4 className="font-medium">Pricing Configuration</h4>
        
        <div className="flex items-center space-x-4">
          <Switch
            id="requiresPayment"
            checked={formData.requiresPayment}
            onCheckedChange={(checked) => setFormData(prev => ({ 
              ...prev, 
              requiresPayment: checked,
              price: checked ? prev.price : 0
            }))}
          />
          <Label htmlFor="requiresPayment">Requires Payment</Label>
        </div>

        {formData.requiresPayment && (
          <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <Label htmlFor="price">Price *</Label>
              <Input
                id="price"
                type="number"
                min="0"
                step="0.01"
                value={formData.price}
                onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                placeholder="0.00"
              />
            </div>
            <div>
              <Label htmlFor="currency">Currency *</Label>
              <Select value={formData.currency} onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {CURRENCIES.map(currency => (
                    <SelectItem key={currency} value={currency}>{currency}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="paymentDeadlineDays">Payment Deadline (days)</Label>
              <Input
                id="paymentDeadlineDays"
                type="number"
                min="1"
                max="365"
                value={formData.paymentDeadlineDays}
                onChange={(e) => setFormData(prev => ({ ...prev, paymentDeadlineDays: parseInt(e.target.value) || 30 }))}
              />
            </div>
          </div>
        )}

        {!formData.requiresPayment && (
          <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <AlertCircle className="w-4 h-4 inline mr-2" />
              This treaty type will be free of charge. Users can apply without payment.
            </p>
          </div>
        )}
      </div>

      <div className="flex items-center space-x-4">
        <Switch
          id="isActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
        />
        <Label htmlFor="isActive">Active</Label>
        <span className="text-sm text-gray-500">
          {formData.isActive ? 'Users can apply for this treaty type' : 'Users cannot apply for this treaty type'}
        </span>
      </div>
    </div>
  )

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <Loader2 className="animate-spin h-8 w-8 text-blue-500 mx-auto mb-4" />
          <p className="text-gray-600">Loading treaty types...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Treaty Types Management</h2>
          <p className="text-gray-600">Configure treaty types, pricing, and availability</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create Treaty Type
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Treaty Type</DialogTitle>
              <DialogDescription>
                Configure a new treaty type with pricing and settings
              </DialogDescription>
            </DialogHeader>
            <TreatyForm onSubmit={handleCreate} onCancel={closeCreateDialog} />
            <DialogFooter>
              <Button variant="outline" onClick={closeCreateDialog}>
                Cancel
              </Button>
              <Button onClick={handleCreate} disabled={submitting}>
                {submitting ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
                <Save className="w-4 h-4 mr-2" />
                Create
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Types</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{treatyTypes.length}</div>
            <p className="text-xs text-muted-foreground">Configured treaty types</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Types</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{treatyTypes.filter(t => t.isActive).length}</div>
            <p className="text-xs text-muted-foreground">Available for applications</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid Types</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{treatyTypes.filter(t => t.requiresPayment).length}</div>
            <p className="text-xs text-muted-foreground">Require payment</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Free Types</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{treatyTypes.filter(t => !t.requiresPayment).length}</div>
            <p className="text-xs text-muted-foreground">No payment required</p>
          </CardContent>
        </Card>
      </div>

      {/* Treaty Types Table */}
      <Card>
        <CardHeader>
          <CardTitle>Treaty Types</CardTitle>
          <CardDescription>
            View and manage all treaty types with their pricing configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Pricing</TableHead>
                <TableHead>Payment Deadline</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {treatyTypes.map((treatyType) => (
                <TableRow key={treatyType.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{treatyType.name}</div>
                      {treatyType.description && (
                        <div className="text-sm text-gray-500 mt-1">{treatyType.description}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{treatyType.category}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <DollarSign className="w-4 h-4 text-green-500" />
                      <span className="font-medium">
                        {treatyType.requiresPayment 
                          ? formatCurrency(treatyType.price, treatyType.currency)
                          : 'Free'
                        }
                      </span>
                      {!treatyType.requiresPayment && (
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          No Payment
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {treatyType.paymentDeadlineDays 
                      ? `${treatyType.paymentDeadlineDays} days`
                      : 'No deadline'
                    }
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(treatyType.isActive)}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Dialog open={isEditDialogOpen && editingTreatyType?.id === treatyType.id} onOpenChange={(open) => {
                        if (!open) closeEditDialog()
                      }}>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(treatyType)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Edit Treaty Type</DialogTitle>
                            <DialogDescription>
                              Update treaty type configuration and pricing
                            </DialogDescription>
                          </DialogHeader>
                          <TreatyForm onSubmit={handleUpdate} onCancel={closeEditDialog} isEdit />
                          <DialogFooter>
                            <Button variant="outline" onClick={closeEditDialog}>
                              Cancel
                            </Button>
                            <Button onClick={handleUpdate} disabled={submitting}>
                              {submitting ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
                              <Save className="w-4 h-4 mr-2" />
                              Update
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(treatyType)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {treatyTypes.length === 0 && (
            <div className="text-center py-8">
              <Settings className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No treaty types found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating your first treaty type.
              </p>
              <div className="mt-6">
                <Button onClick={() => setIsCreateDialogOpen(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Treaty Type
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}