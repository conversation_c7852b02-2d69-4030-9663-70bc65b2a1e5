'use client';

import React, { forwardRef } from 'react';
import { Button, ButtonProps } from '@/components/ui/button';
import { useButtonPermission } from '@/hooks/usePermissionUI';
import { cn } from '@/lib/utils';

export interface PermissionButtonProps extends ButtonProps {
  permissions?: string[];
  allPermissions?: string[];
  serverId?: string;
  requireAuth?: boolean;
  disabledReason?: string;
  showSpinner?: boolean;
}

export const PermissionButton = forwardRef<HTMLButtonElement, PermissionButtonProps>(
  ({
    permissions,
    allPermissions,
    serverId,
    requireAuth = true,
    disabledReason,
    showSpinner = true,
    className,
    disabled,
    children,
    ...buttonProps
  }, ref) => {
    const buttonState = useButtonPermission({
      permissions,
      allPermissions,
      serverId,
      requireAuth
    });

    const isDisabled = disabled || !buttonState.enabled;
    const displayReason = disabledReason || buttonState.disabledReason;

    return (
      <div className="relative inline-block">
        <Button
          {...buttonProps}
          ref={ref}
          disabled={isDisabled}
          className={cn(
            className,
            isDisabled && 'cursor-not-allowed opacity-60'
          )}
        >
          {children}
        </Button>

        {isDisabled && displayReason && showSpinner && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin opacity-50" />
          </div>
        )}

        {isDisabled && displayReason && (
          <div className="absolute top-full left-0 mt-1 px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg z-10 whitespace-nowrap">
            {displayReason}
            <div className="absolute top-0 left-2 transform -translate-y-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-800"></div>
          </div>
        )}
      </div>
    );
  }
);

PermissionButton.displayName = 'PermissionButton';