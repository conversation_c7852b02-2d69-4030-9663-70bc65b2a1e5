'use client';

import React, { createContext, useContext } from 'react';

interface RadioGroupContextType {
  value: string;
  onValueChange: (value: string) => void;
}

const RadioGroupContext = createContext<RadioGroupContextType | undefined>(undefined);

interface RadioGroupProps {
  value: string;
  onValueChange: (value: string) => void;
  children: React.ReactNode;
}

export const RadioGroup: React.FC<RadioGroupProps> = ({ value, onValueChange, children }) => {
  return (
    <RadioGroupContext.Provider value={{ value, onValueChange }}>
      <div className="space-y-2">
        {children}
      </div>
    </RadioGroupContext.Provider>
  );
};

interface RadioGroupItemProps {
  value: string;
  id: string;
}

export const RadioGroupItem: React.FC<RadioGroupItemProps> = ({ value, id }) => {
  const context = useContext(RadioGroupContext);
  if (!context) throw new Error('RadioGroupItem must be used within RadioGroup');

  return (
    <input
      type="radio"
      id={id}
      value={value}
      checked={context.value === value}
      onChange={(e) => context.onValueChange(e.target.value)}
      className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
    />
  );
};