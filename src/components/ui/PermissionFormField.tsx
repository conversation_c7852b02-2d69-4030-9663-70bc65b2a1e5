'use client'

import React, { ReactNode, forwardRef } from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useFormFieldPermission } from '@/hooks/usePermissionUI'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'

export interface PermissionFormFieldProps {
  name: string
  label?: string
  permissions?: string[]
  allPermissions?: string[]
  serverId?: string
  requireAuth?: boolean
  disabledReason?: string
  showLabel?: boolean
  required?: boolean
  error?: string
  helperText?: string
  className?: string
}

export interface PermissionInputProps extends PermissionFormFieldProps, Omit<React.ComponentProps<'input'>, 'name'> {}
export interface PermissionTextareaProps extends PermissionFormFieldProps, Omit<React.ComponentProps<'textarea'>, 'name'> {}
export interface PermissionSelectProps extends PermissionFormFieldProps, Omit<React.ComponentProps<'select'>, 'name'> {}
export interface PermissionCheckboxProps extends PermissionFormFieldProps, Omit<React.ComponentProps<'input'>, 'name'> {}

export const PermissionInput = forwardRef<HTMLInputElement, PermissionInputProps>(
  ({ name, label, permissions, allPermissions, serverId, requireAuth = true, disabledReason, showLabel = true, required, error, helperText, className, ...inputProps }, ref) => {
    const fieldState = useFormFieldPermission({
      permissions,
      allPermissions,
      serverId,
      requireAuth
    })

    const isDisabled = !fieldState.editable
    const displayReason = disabledReason || fieldState.disabledReason
    const isRequired = required !== undefined ? required : fieldState.required

    return (
      <div className={cn('space-y-2', className)}>
        {showLabel && label && (
          <Label
            htmlFor={name}
            className={cn(
              'text-sm font-medium',
              isDisabled && 'text-gray-400'
            )}
          >
            {label}
            {isRequired && <span className="text-red-500 ml-1">*</span>}
          </Label>
        )}

        <div className="relative">
          <Input
            {...inputProps}
            ref={ref}
            id={name}
            name={name}
            disabled={isDisabled}
            required={isRequired}
            className={cn(
              error && 'border-red-500 focus:border-red-500',
              isDisabled && 'bg-gray-100 cursor-not-allowed'
            )}
          />

          {isDisabled && displayReason && (
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
              <div className="w-4 h-4 border-2 border-gray-300 border-t-transparent rounded-full animate-spin opacity-50" />
            </div>
          )}
        </div>

        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}

        {helperText && !error && (
          <p className="text-sm text-gray-500">{helperText}</p>
        )}

        {isDisabled && displayReason && (
          <p className="text-xs text-gray-400 italic">{displayReason}</p>
        )}
      </div>
    )
  }
)

PermissionInput.displayName = 'PermissionInput'

export const PermissionTextarea = forwardRef<HTMLTextAreaElement, PermissionTextareaProps>(
  ({ name, label, permissions, allPermissions, serverId, requireAuth = true, disabledReason, showLabel = true, required, error, helperText, className, ...textareaProps }, ref) => {
    const fieldState = useFormFieldPermission({
      permissions,
      allPermissions,
      serverId,
      requireAuth
    })

    const isDisabled = !fieldState.editable
    const displayReason = disabledReason || fieldState.disabledReason
    const isRequired = required !== undefined ? required : fieldState.required

    return (
      <div className={cn('space-y-2', className)}>
        {showLabel && label && (
          <Label
            htmlFor={name}
            className={cn(
              'text-sm font-medium',
              isDisabled && 'text-gray-400'
            )}
          >
            {label}
            {isRequired && <span className="text-red-500 ml-1">*</span>}
          </Label>
        )}

        <div className="relative">
          <Textarea
            {...textareaProps}
            ref={ref}
            id={name}
            name={name}
            disabled={isDisabled}
            required={isRequired}
            className={cn(
              error && 'border-red-500 focus:border-red-500',
              isDisabled && 'bg-gray-100 cursor-not-allowed'
            )}
          />

          {isDisabled && displayReason && (
            <div className="absolute right-2 top-2">
              <div className="w-4 h-4 border-2 border-gray-300 border-t-transparent rounded-full animate-spin opacity-50" />
            </div>
          )}
        </div>

        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}

        {helperText && !error && (
          <p className="text-sm text-gray-500">{helperText}</p>
        )}

        {isDisabled && displayReason && (
          <p className="text-xs text-gray-400 italic">{displayReason}</p>
        )}
      </div>
    )
  }
)

PermissionTextarea.displayName = 'PermissionTextarea'

export const PermissionSelect = forwardRef<HTMLSelectElement, PermissionSelectProps>(
  ({ name, label, permissions, allPermissions, serverId, requireAuth = true, disabledReason, showLabel = true, required, error, helperText, className, children, ...selectProps }, ref) => {
    const fieldState = useFormFieldPermission({
      permissions,
      allPermissions,
      serverId,
      requireAuth
    })

    const isDisabled = !fieldState.editable
    const displayReason = disabledReason || fieldState.disabledReason
    const isRequired = required !== undefined ? required : fieldState.required

    return (
      <div className={cn('space-y-2', className)}>
        {showLabel && label && (
          <Label
            htmlFor={name}
            className={cn(
              'text-sm font-medium',
              isDisabled && 'text-gray-400'
            )}
          >
            {label}
            {isRequired && <span className="text-red-500 ml-1">*</span>}
          </Label>
        )}

        <div className="relative">
          <select
            {...selectProps}
            ref={ref}
            id={name}
            name={name}
            disabled={isDisabled}
            required={isRequired}
            className={cn(
              'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
              error && 'border-red-500 focus:border-red-500',
              isDisabled && 'bg-gray-100 cursor-not-allowed'
            )}
          >
            {children}
          </select>

          {isDisabled && displayReason && (
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
              <div className="w-4 h-4 border-2 border-gray-300 border-t-transparent rounded-full animate-spin opacity-50" />
            </div>
          )}
        </div>

        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}

        {helperText && !error && (
          <p className="text-sm text-gray-500">{helperText}</p>
        )}

        {isDisabled && displayReason && (
          <p className="text-xs text-gray-400 italic">{displayReason}</p>
        )}
      </div>
    )
  }
)

PermissionSelect.displayName = 'PermissionSelect'

export const PermissionCheckbox = forwardRef<HTMLInputElement, PermissionCheckboxProps>(
  ({ name, label, permissions, allPermissions, serverId, requireAuth = true, disabledReason, showLabel = true, error, helperText, className, ...checkboxProps }, ref) => {
    const fieldState = useFormFieldPermission({
      permissions,
      allPermissions,
      serverId,
      requireAuth
    })

    const isDisabled = !fieldState.editable
    const displayReason = disabledReason || fieldState.disabledReason

    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <Checkbox
          {...checkboxProps}
          ref={ref}
          id={name}
          name={name}
          disabled={isDisabled}
          className={cn(
            error && 'border-red-500',
            isDisabled && 'opacity-50 cursor-not-allowed'
          )}
        />

        {showLabel && label && (
          <Label
            htmlFor={name}
            className={cn(
              'text-sm font-medium cursor-pointer',
              isDisabled && 'text-gray-400 cursor-not-allowed'
            )}
          >
            {label}
          </Label>
        )}

        {isDisabled && displayReason && (
          <div className="w-4 h-4 border-2 border-gray-300 border-t-transparent rounded-full animate-spin opacity-50" />
        )}

        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}

        {helperText && !error && (
          <p className="text-sm text-gray-500">{helperText}</p>
        )}

        {isDisabled && displayReason && (
          <p className="text-xs text-gray-400 italic">{displayReason}</p>
        )}
      </div>
    )
  }
)

PermissionCheckbox.displayName = 'PermissionCheckbox'