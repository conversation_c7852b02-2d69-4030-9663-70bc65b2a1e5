'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { formatAddressForDisplay, buildFormattedAddress } from '@/lib/utils/address-format-utils';

interface UserData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  nwaEmail?: string;
  phone?: string;
  mobile?: string;
  streetAddress1?: string;
  streetAddress2?: string;
  town?: string;
  city?: string;
  country?: string;
  postalCode?: string;
  regionId?: string;
  regionText?: string;
  dob?: string;
  peaceAmbassadorNumber?: string;
  bio?: string;
  titleId?: string;
  positionId?: string;
  // Legacy support
  streetAddress?: string;
}

interface CountryAddressFormat {
  id: number | null;
  countryId: number;
  postalCodeLabel: string;
  postalCodeFormat: string;
  postalCodeRequired: boolean;
  regionLabel: string;
  regionRequired: boolean;
  townLabel: string;
  townRequired: boolean;
  addressTemplate: string;
  createdAt: string | null;
  updatedAt: string | null;
}

interface Region {
  id: string;
  name: string;
  code: string;
}

export default function UserProfile() {
  const { data: session, status } = useSession();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [addressFormat, setAddressFormat] = useState<CountryAddressFormat | null>(null);
  const [regionName, setRegionName] = useState<string>('');
  const [loading, setLoading] = useState(true);

  const fetchRegionName = useCallback(async (regionId: string) => {
    try {
      // We need to get the region name from the region ID
      // This is a bit tricky since regions API requires countryId
      // For now, we'll use the regionText as fallback
      setRegionName(userData?.regionText || '');
    } catch (error) {
      console.error('Error fetching region name:', error);
    }
  }, [userData?.regionText]);

  useEffect(() => {
    const fetchUserData = async () => {
      if ((session?.user as any)?.id) {
        try {
          const response = await fetch(`/api/users/${(session!.user as any).id}`);
          if (response.ok) {
            const data = await response.json();
            setUserData(data.user);
            
            // Fetch address format if user has a country
            if (data.user.country) {
              await fetchAddressFormat(data.user.country);
            }
            
            // Fetch region name if user has regionId
            if (data.user.regionId) {
              await fetchRegionName(data.user.regionId);
            }
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
        }
      }
      setLoading(false);
    };

    if (status !== 'loading') {
      fetchUserData();
    }
  }, [session, status, fetchRegionName]);

  const fetchAddressFormat = async (country: string) => {
    try {
      // First, get the country ID by searching
      const countryResponse = await fetch(`/api/countries?q=${encodeURIComponent(country)}`);
      if (countryResponse.ok) {
        const countries = await countryResponse.json();
        const foundCountry = countries.find((c: any) => c.name === country);
        
        if (foundCountry) {
          const formatResponse = await fetch(`/api/countries/${foundCountry.id}/address-format`);
          if (formatResponse.ok) {
            const formatData = await formatResponse.json();
            setAddressFormat(formatData.addressFormat);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching address format:', error);
    }
  };

  const formatAddress = (user: UserData): string => {
    if (!addressFormat) {
      // Fallback to basic formatting
      const parts = [];
      if (user.streetAddress1 || user.streetAddress) {
        parts.push(user.streetAddress1 || user.streetAddress);
      }
      if (user.streetAddress2) {
        parts.push(user.streetAddress2);
      }
      if (user.town) parts.push(user.town);
      if (user.regionText || regionName) {
        parts.push(user.regionText || regionName);
      }
      if (user.postalCode) parts.push(user.postalCode);
      if (user.country) parts.push(user.country);
      
      return parts.filter(Boolean).join(', ') || 'No address information';
    }

    // Use address formatting utilities with country template
    const addressData = {
      streetAddress1: user.streetAddress1 || user.streetAddress || '',
      streetAddress2: user.streetAddress2 || '',
      town: user.town || '',
      region: user.regionText || regionName || '',
      postalCode: user.postalCode || '',
      country: user.country || ''
    };

    const formatted = formatAddressForDisplay(addressData, addressFormat.addressTemplate);
    return formatted || 'No address information';
  };

  if (status === "loading" || loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  if (status === "unauthenticated") {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="text-center py-8">
          <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">Not logged in</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Please log in to view your profile.
          </p>
        </div>
      </div>
    );
  }

  const user = userData || {
    firstName: session?.user?.name?.split(' ')[0] || '',
    lastName: session?.user?.name?.split(' ').slice(1).join(' ') || '',
    email: session?.user?.email || '',
    nwaEmail: (session?.user as any)?.nwaEmail || '',
  } as UserData;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      {/* Header Section */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          <div className="h-16 w-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <span className="text-xl font-semibold text-white">
              {(user.firstName?.[0] || '').toUpperCase()}{(user.lastName?.[0] || '').toUpperCase()}
            </span>
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
              {user.firstName} {user.lastName}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {user.email}
            </p>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Personal Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
              Personal Information
            </h3>
            
            <div className="grid grid-cols-1 gap-3">
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Email
                </label>
                <p className="text-gray-900 dark:text-white">
                  {user.email || 'N/A'}
                </p>
              </div>
              
              {user.nwaEmail && (
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    NWA Email
                  </label>
                  <p className="text-gray-900 dark:text-white">
                    {user.nwaEmail}
                  </p>
                </div>
              )}
              
              {user.phone && (
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Phone
                  </label>
                  <p className="text-gray-900 dark:text-white">
                    {user.phone}
                  </p>
                </div>
              )}
              
              {user.mobile && (
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Mobile
                  </label>
                  <p className="text-gray-900 dark:text-white">
                    {user.mobile}
                  </p>
                </div>
              )}
              
              {user.dob && (
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Date of Birth
                  </label>
                  <p className="text-gray-900 dark:text-white">
                    {new Date(user.dob).toLocaleDateString()}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Address Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
              Address
            </h3>
            
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400 block mb-2">
                Address
              </label>
              <div className="text-gray-900 dark:text-white whitespace-pre-line">
                {formatAddress(user)}
              </div>
            </div>
          </div>
        </div>

        {/* Identification Information */}
        {user.peaceAmbassadorNumber && (
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Identification
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {user.peaceAmbassadorNumber && (
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Peace Ambassador Number
                  </label>
                  <p className="text-gray-900 dark:text-white">
                    {user.peaceAmbassadorNumber}
                  </p>
                </div>
              )}
            </div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              For detailed identification management, please use the administration panel.
            </p>
          </div>
        )}

        {/* Bio Section */}
        {user.bio && (
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Biography
            </h3>
            <div className="text-gray-900 dark:text-white whitespace-pre-wrap">
              {user.bio}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
