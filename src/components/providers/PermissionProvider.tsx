'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

// Types for permissions
export interface UserPermissions {
  [serverId: string]: string[];
}

export interface PermissionData {
  id: string;
  name: string;
  resource: string;
  action: string;
  description: string;
}

export interface RoleData {
  id: string;
  name: string;
  description: string;
  permissions: PermissionData[];
}

export interface PermissionContextType {
  permissions: UserPermissions;
  loading: boolean;
  error: string | null;
  userId: string | null;
  hasPermission: (resource: string, action: string, serverId?: string) => boolean;
  hasAnyPermission: (permissions: Array<{ resource: string; action: string }>, serverId?: string) => boolean;
  hasAllPermissions: (permissions: Array<{ resource: string; action: string }>, serverId?: string) => boolean;
  hasRole: (roleName: string) => boolean;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  refreshPermissions: () => Promise<void>;
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined);

interface PermissionProviderProps {
  children: ReactNode;
}

export const PermissionProvider: React.FC<PermissionProviderProps> = ({ children }) => {
  const [permissions, setPermissions] = useState<UserPermissions>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [userRoles, setUserRoles] = useState<RoleData[]>([]);
  const [isAdminUser, setIsAdminUser] = useState(false);
  const [isSuperAdminUser, setIsSuperAdminUser] = useState(false);

  const fetchPermissions = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('/api/user/permissions');
      if (!response.ok) {
        throw new Error('Failed to fetch permissions');
      }
      const data = await response.json();

      // Convert API response to our format
      const userPermissions: UserPermissions = {};
      const permissionStrings: string[] = [];

      // Process permissions from API response
      if (data.permissions && Array.isArray(data.permissions)) {
        data.permissions.forEach((perm: PermissionData) => {
          permissionStrings.push(`${perm.resource}.${perm.action}`);
        });
      }

      // Store permissions (using 'default' as serverId for now)
      userPermissions['default'] = permissionStrings;

      setPermissions(userPermissions);
      setUserId(data.userId || data.id);
      setUserRoles(data.roles || []);

      // Determine admin status
      const adminRole = data.roles?.find((role: RoleData) => role.name === 'ADMIN' || role.name === 'SUPER_ADMIN');
      const superAdminRole = data.roles?.find((role: RoleData) => role.name === 'SUPER_ADMIN');

      setIsAdminUser(!!adminRole);
      setIsSuperAdminUser(!!superAdminRole);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPermissions();
  }, []);

  const hasPermission = (resource: string, action: string, serverId?: string): boolean => {
    const serverPermissions = permissions[serverId || 'default'] || [];
    return serverPermissions.includes(`${resource}.${action}`);
  };

  const hasAnyPermission = (permissionsToCheck: Array<{ resource: string; action: string }>, serverId?: string): boolean => {
    return permissionsToCheck.some(perm => hasPermission(perm.resource, perm.action, serverId));
  };

  const hasAllPermissions = (permissionsToCheck: Array<{ resource: string; action: string }>, serverId?: string): boolean => {
    return permissionsToCheck.every(perm => hasPermission(perm.resource, perm.action, serverId));
  };

  const hasRole = (roleName: string): boolean => {
    return userRoles.some(role => role.name === roleName);
  };

  const refreshPermissions = async () => {
    await fetchPermissions();
  };

  const value: PermissionContextType = {
    permissions,
    loading,
    error,
    userId,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    isAdmin: isAdminUser,
    isSuperAdmin: isSuperAdminUser,
    refreshPermissions,
  };

  return (
    <PermissionContext.Provider value={value}>
      {children}
    </PermissionContext.Provider>
  );
};

export const usePermissions = (): PermissionContextType => {
  const context = useContext(PermissionContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionProvider');
  }
  return context;
};

export const useHasPermission = (resource: string, action: string, serverId?: string): boolean => {
  const { hasPermission } = usePermissions();
  return hasPermission(resource, action, serverId);
};

export const useHasAnyPermission = (permissions: Array<{ resource: string; action: string }>, serverId?: string): boolean => {
  const { hasAnyPermission } = usePermissions();
  return hasAnyPermission(permissions, serverId);
};

export const useHasAllPermissions = (permissions: Array<{ resource: string; action: string }>, serverId?: string): boolean => {
  const { hasAllPermissions } = usePermissions();
  return hasAllPermissions(permissions, serverId);
};

export const useHasRole = (roleName: string): boolean => {
  const { hasRole } = usePermissions();
  return hasRole(roleName);
};

export const useIsAdmin = (): boolean => {
  const { isAdmin } = usePermissions();
  return isAdmin;
};

export const useIsSuperAdmin = (): boolean => {
  const { isSuperAdmin } = usePermissions();
  return isSuperAdmin;
};