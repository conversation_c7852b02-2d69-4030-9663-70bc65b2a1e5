'use client';

import React, { ReactNode } from 'react';
import { usePermissions } from '@/lib/contexts/PermissionContext';

interface PermissionWrapperProps {
  children: ReactNode;
  resource: string;
  action: string;
  fallback?: ReactNode;
  requireAll?: boolean;
  permissions?: Array<{ resource: string; action: string }>;
  roles?: string[];
  adminOnly?: boolean;
  superAdminOnly?: boolean;
}

export function PermissionWrapper({
  children,
  resource,
  action,
  fallback = null,
  requireAll = false,
  permissions = [],
  roles = [],
  adminOnly = false,
  superAdminOnly = false,
}: PermissionWrapperProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, hasRole, isAdmin, isSuperAdmin } = usePermissions();

  // Check admin/super admin requirements first
  if (superAdminOnly && !isSuperAdmin) {
    return <>{fallback}</>;
  }

  if (adminOnly && !isAdmin) {
    return <>{fallback}</>;
  }

  // Check role requirements
  if (roles.length > 0) {
    const hasRequiredRole = roles.some(role => hasRole(role));
    if (!hasRequiredRole) {
      return <>{fallback}</>;
    }
  }

  // Check permission requirements
  if (permissions.length > 0) {
    const hasRequiredPermissions = requireAll
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);

    if (!hasRequiredPermissions) {
      return <>{fallback}</>;
    }
  }

  // Check single permission
  if (resource && action) {
    const hasSinglePermission = hasPermission(resource, action);
    if (!hasSinglePermission) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
}

interface ConditionalRenderProps {
  children: ReactNode;
  condition: boolean;
  fallback?: ReactNode;
}

export function ConditionalRender({ children, condition, fallback = null }: ConditionalRenderProps) {
  return condition ? <>{children}</> : <>{fallback}</>;
}