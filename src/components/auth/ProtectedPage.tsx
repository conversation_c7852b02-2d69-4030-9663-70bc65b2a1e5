'use client';

import React, { ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { usePermissions } from '@/lib/contexts/PermissionContext';

interface ProtectedPageProps {
  children: ReactNode;
  resource?: string;
  action?: string;
  fallback?: ReactNode;
  redirectTo?: string;
  requireAll?: boolean;
  permissions?: Array<{ resource: string; action: string }>;
  roles?: string[];
  adminOnly?: boolean;
  superAdminOnly?: boolean;
  requireAuth?: boolean;
}

export function ProtectedPage({
  children,
  resource,
  action,
  fallback = null,
  redirectTo = '/unauthorized',
  requireAll = false,
  permissions = [],
  roles = [],
  adminOnly = false,
  superAdminOnly = false,
  requireAuth = true,
}: ProtectedPageProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, hasRole, isAdmin, isSuperAdmin, loading } = usePermissions();
  const router = useRouter();

  // Show loading state while permissions are being fetched
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Check authentication requirement
  if (requireAuth && !hasPermission('AUTHENTICATION', 'ACCESS')) {
    // Use setTimeout to avoid calling router.push during render
    setTimeout(() => {
      router.push('/login');
    }, 0);
    return null;
  }

  // Check admin/super admin requirements first
  if (superAdminOnly && !isSuperAdmin) {
    if (redirectTo) {
      setTimeout(() => {
        router.push(redirectTo);
      }, 0);
      return null;
    }
    return <>{fallback}</>;
  }

  if (adminOnly && !isAdmin) {
    if (redirectTo) {
      setTimeout(() => {
        router.push(redirectTo);
      }, 0);
      return null;
    }
    return <>{fallback}</>;
  }

  // Check role requirements
  if (roles.length > 0) {
    const hasRequiredRole = roles.some(role => hasRole(role));
    if (!hasRequiredRole) {
      if (redirectTo) {
        setTimeout(() => {
          router.push(redirectTo);
        }, 0);
        return null;
      }
      return <>{fallback}</>;
    }
  }

  // Check permission requirements
  if (permissions.length > 0) {
    console.log('ProtectedPage: Checking permissions:', permissions);
    console.log('ProtectedPage: requireAll:', requireAll);
    const hasRequiredPermissions = requireAll
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
    console.log('ProtectedPage: hasRequiredPermissions:', hasRequiredPermissions);

    if (!hasRequiredPermissions) {
      console.log('ProtectedPage: Access denied, redirecting to:', redirectTo);
      if (redirectTo) {
        setTimeout(() => {
          router.push(redirectTo);
        }, 0);
        return null;
      }
      return <>{fallback}</>;
    }
  }

  // Check single permission
  if (resource && action) {
    const hasSinglePermission = hasPermission(resource, action);
    if (!hasSinglePermission) {
      if (redirectTo) {
        setTimeout(() => {
          router.push(redirectTo);
        }, 0);
        return null;
      }
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
}

// Convenience components for common protection patterns
export function AdminOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <ProtectedPage
      adminOnly={true}
      fallback={fallback}
      redirectTo="/unauthorized"
    >
      {children}
    </ProtectedPage>
  );
}

export function SuperAdminOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <ProtectedPage
      superAdminOnly={true}
      fallback={fallback}
      redirectTo="/unauthorized"
    >
      {children}
    </ProtectedPage>
  );
}

export function AuthenticatedOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <ProtectedPage
      requireAuth={true}
      fallback={fallback}
      redirectTo="/login"
    >
      {children}
    </ProtectedPage>
  );
}

// AdminPage component that accepts requiredPermissions array
interface AdminPageProps {
  children: ReactNode;
  requiredPermissions: string[];
  fallback?: ReactNode;
  redirectTo?: string;
  requireAll?: boolean;
}

export function AdminPage({
  children,
  requiredPermissions,
  fallback = null,
  redirectTo = '/unauthorized',
  requireAll = false
}: AdminPageProps) {
  // Convert string permissions to the format expected by ProtectedPage
  const permissions = requiredPermissions.map(permission => {
    // Split permission into resource and action (e.g., 'users.manage' -> resource: 'users', action: 'manage')
    const [resource, action] = permission.split('.');
    return { resource, action };
  });

  return (
    <ProtectedPage
      permissions={permissions}
      requireAll={requireAll}
      fallback={fallback}
      redirectTo={redirectTo}
    >
      {children}
    </ProtectedPage>
  );
}