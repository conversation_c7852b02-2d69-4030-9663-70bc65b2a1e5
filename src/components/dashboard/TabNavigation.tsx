import React from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { PERMISSIONS } from '@/lib/constants/permissions';
import {
  LayoutDashboard,
  Bell,
  Server,
  BarChart3,
  Shield,
  Settings
} from 'lucide-react';

interface Tab {
  id: string;
  label: string;
  icon: string;
  requiredPermissions?: string[];
  roles?: string[];
}

interface TabNavigationProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
  userPermissions?: string[];
  userRole?: string;
}

export function TabNavigation({
  tabs,
  activeTab,
  onTabChange,
  className,
  userPermissions = [],
  userRole = 'PEACE'
}: TabNavigationProps) {
  // Icon mapping
  const getIcon = (iconName: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      LayoutDashboard: <LayoutDashboard className="h-4 w-4" />,
      Bell: <Bell className="h-4 w-4" />,
      Server: <Server className="h-4 w-4" />,
      BarChart3: <BarChart3 className="h-4 w-4" />,
      Shield: <Shield className="h-4 w-4" />,
      Settings: <Settings className="h-4 w-4" />,
    };
    return iconMap[iconName] || null;
  };

  // Filter tabs based on permissions and roles
  const visibleTabs = tabs.filter((tab) => {
    // If no permissions required, tab is visible to all
    if (!tab.requiredPermissions && !tab.roles) {
      return true;
    }

    // Check role-based access
    if (tab.roles && tab.roles.includes(userRole)) {
      return true;
    }

    // Check permission-based access
    if (tab.requiredPermissions) {
      return tab.requiredPermissions.some(permission =>
        userPermissions.includes(permission)
      );
    }

    return false;
  });

  return (
    <div className={cn("mb-6", className)}>
      <nav className="flex space-x-8 overflow-x-auto border-b border-emerald-200" aria-label="Tabs">
        {visibleTabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={cn(
              "whitespace-nowrap py-2 px-1 font-medium text-sm transition-all duration-200 flex-shrink-0 flex items-center gap-2 min-w-0 border-b-2",
              activeTab === tab.id
                ? "border-emerald-500 text-emerald-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            )}
            aria-current={activeTab === tab.id ? "page" : undefined}
            data-testid={`tab-${tab.id}`}
          >
            {getIcon(tab.icon)}
            <span>{tab.label}</span>
          </button>
        ))}
      </nav>
    </div>
  );
}
