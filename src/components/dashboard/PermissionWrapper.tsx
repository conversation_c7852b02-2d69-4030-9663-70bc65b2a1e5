import React from 'react';
import { PERMISSIONS } from '@/lib/constants/permissions';

interface PermissionWrapperProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  fallback?: React.ReactNode;
  userPermissions?: string[];
  userRole?: string;
  loadingComponent?: React.ReactNode;
  permissionsLoading?: boolean;
}

export function PermissionWrapper({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  fallback = null,
  userPermissions = [],
  userRole = 'PEACE',
  loadingComponent = null,
  permissionsLoading = false
}: PermissionWrapperProps) {
  // Show loading state while permissions are being fetched
  if (permissionsLoading) {
    return loadingComponent ? (
      <>{loadingComponent}</>
    ) : (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600 dark:text-gray-400">Loading...</span>
      </div>
    );
  }

  // Check if user has any of the required roles
  const hasRequiredRole = requiredRoles.length === 0 || requiredRoles.includes(userRole);

  // Check if user has any of the required permissions
  const hasRequiredPermissions = requiredPermissions.length === 0 ||
    requiredPermissions.some(permission => userPermissions.includes(permission));

  // User has access if they meet role OR permission requirements
  const hasAccess = hasRequiredRole && hasRequiredPermissions;

  // If user doesn't have access, show fallback or null
  if (!hasAccess) {
    return fallback ? <>{fallback}</> : null;
  }

  // User has access, render children
  return <>{children}</>;
}

// Convenience wrapper for admin-only content
export function AdminOnly({
  children,
  fallback,
  userPermissions = [],
  userRole = 'PEACE',
  loadingComponent,
  permissionsLoading = false
}: Omit<PermissionWrapperProps, 'requiredPermissions' | 'requiredRoles'>) {
  return (
    <PermissionWrapper
      requiredRoles={['ADMIN']}
      fallback={fallback}
      userPermissions={userPermissions}
      userRole={userRole}
      loadingComponent={loadingComponent}
      permissionsLoading={permissionsLoading}
    >
      {children}
    </PermissionWrapper>
  );
}

// Convenience wrapper for member-only content
export function MemberOnly({
  children,
  fallback,
  userPermissions = [],
  userRole = 'PEACE',
  loadingComponent,
  permissionsLoading = false
}: Omit<PermissionWrapperProps, 'requiredPermissions' | 'requiredRoles'>) {
  return (
    <PermissionWrapper
      requiredRoles={['MEMBER', 'ADMIN']}
      fallback={fallback}
      userPermissions={userPermissions}
      userRole={userRole}
      loadingComponent={loadingComponent}
      permissionsLoading={permissionsLoading}
    >
      {children}
    </PermissionWrapper>
  );
}

// Convenience wrapper for server access content
export function ServerAccess({
  children,
  fallback,
  userPermissions = [],
  userRole = 'PEACE',
  loadingComponent,
  permissionsLoading = false
}: Omit<PermissionWrapperProps, 'requiredPermissions' | 'requiredRoles'>) {
  return (
    <PermissionWrapper
      requiredPermissions={[PERMISSIONS.REMOTE_SERVER_READ]}
      fallback={fallback}
      userPermissions={userPermissions}
      userRole={userRole}
      loadingComponent={loadingComponent}
      permissionsLoading={permissionsLoading}
    >
      {children}
    </PermissionWrapper>
  );
}

// Convenience wrapper for analytics access content
export function AnalyticsAccess({
  children,
  fallback,
  userPermissions = [],
  userRole = 'PEACE',
  loadingComponent,
  permissionsLoading = false
}: Omit<PermissionWrapperProps, 'requiredPermissions' | 'requiredRoles'>) {
  return (
    <PermissionWrapper
      requiredPermissions={[PERMISSIONS.ADMIN_PANEL_ACCESS]}
      fallback={fallback}
      userPermissions={userPermissions}
      userRole={userRole}
      loadingComponent={loadingComponent}
      permissionsLoading={permissionsLoading}
    >
      {children}
    </PermissionWrapper>
  );
}