'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, Activity } from 'lucide-react';

interface ServerData {
  servers: Array<{
    id: string;
    name: string;
    status: 'online' | 'offline' | 'maintenance';
    cpuUsage: number;
    memoryUsage: number;
    totalMemory: number;
    diskUsage: number;
    totalDisk: number;
    location: string;
    type: 'local' | 'remote';
    url?: string;
    uptime?: string;
    loadAverage?: string[];
    network?: {
      inbound: number;
      outbound: number;
    };
    lastHealthCheck?: string;
  }>;
  stats: {
    totalServers: number;
    onlineServers: number;
    offlineServers: number;
    maintenanceServers: number;
    averageCpuUsage: number;
    averageMemoryUsage: number;
  };
  localServer?: {
    cpuUsage: number;
    memoryUsage: number;
    totalMemory: number;
    diskUsage: number;
    totalDisk: number;
    uptime: string;
    loadAverage: string[];
    network: {
      inbound: number;
      outbound: number;
    };
    hostname: string;
    platform: string;
    arch: string;
    nodeVersion: string;
  };
}

export function ServersTab() {
  const [data, setData] = useState<ServerData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [healthCheckLoading, setHealthCheckLoading] = useState(false);
  const [lastHealthCheck, setLastHealthCheck] = useState<Date | null>(null);

  useEffect(() => {
    const fetchServerData = async () => {
      try {
        const response = await fetch('/api/dashboard/servers');
        if (!response.ok) {
          throw new Error('Failed to fetch server data');
        }
        const serverData = await response.json();
        setData(serverData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchServerData();
  }, []);

  // Function to check health of a single server
  const checkServerHealth = async (serverId: string) => {
    try {
      const response = await fetch('/api/dashboard/servers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          serverId,
          action: 'get_health'
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to check server health');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error(`Health check failed for server ${serverId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Health check failed'
      };
    }
  };

  // Function to check health of all servers
  const checkAllServersHealth = async () => {
    if (!data || !data.servers.length) return;

    setHealthCheckLoading(true);
    try {
      const healthCheckPromises = data.servers.map(server =>
        checkServerHealth(server.id)
      );

      const results = await Promise.all(healthCheckPromises);

      // Update server statuses based on health check results
      setData(prevData => {
        if (!prevData) return prevData;

        const updatedServers = prevData.servers.map((server, index) => {
          const result = results[index];
          if (result.success && result.health) {
            const newStatus: 'online' | 'offline' | 'maintenance' =
              result.health.status === 'healthy' ? 'online' : 'offline';
            return {
              ...server,
              status: newStatus,
              uptime: result.health.uptime || server.uptime,
              cpuUsage: result.health.metrics?.cpu || server.cpuUsage,
              memoryUsage: result.health.metrics?.memory || server.memoryUsage,
              diskUsage: result.health.metrics?.disk || server.diskUsage,
              lastHealthCheck: new Date().toISOString(), // Update the last health check timestamp
            };
          }
          return server;
        });

        // Update statistics
        const onlineServers = updatedServers.filter(s => s.status === 'online').length;
        const offlineServers = updatedServers.filter(s => s.status === 'offline').length;

        return {
          ...prevData,
          servers: updatedServers,
          stats: {
            ...prevData.stats,
            onlineServers: onlineServers + 1, // +1 for local server
            offlineServers: offlineServers,
          }
        };
      });

      setLastHealthCheck(new Date());
    } catch (error) {
      setError('Failed to check server health');
    } finally {
      setHealthCheckLoading(false);
    }
  };

  // Periodic health checks every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (!healthCheckLoading) {
        checkAllServersHealth();
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [data, healthCheckLoading]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'offline':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return 'Online';
      case 'maintenance':
        return 'Maintenance';
      case 'offline':
        return 'Offline';
      default:
        return 'Unknown';
    }
  };

  if (loading) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-600/50 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-slate-600/50 rounded w-1/2 mb-6"></div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="dashboard-card">
              <CardHeader>
                <div className="h-6 bg-slate-200 rounded w-3/4 mb-2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[...Array(3)].map((_, j) => (
                    <div key={j} className="flex justify-between">
                      <div className="h-4 bg-slate-200 rounded w-1/2"></div>
                      <div className="h-4 bg-slate-200 rounded w-1/4"></div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="text-center">
          <p className="text-white">Error loading servers: {error}</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="text-center">
          <p className="text-white">No server data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
      <div className="mb-4 md:mb-6">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-xl md:text-2xl font-bold text-white">
            Server Management
          </h2>
          <div className="flex items-center gap-2">
            {lastHealthCheck && (
              <span className="text-xs text-slate-400 flex items-center gap-1">
                <Activity className="w-3 h-3" />
                Last check: {lastHealthCheck.toLocaleTimeString()}
              </span>
            )}
            <Button
              onClick={checkAllServersHealth}
              disabled={healthCheckLoading}
              variant="outline"
              size="sm"
              className="bg-slate-800 border-slate-600 text-white hover:bg-slate-700 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${healthCheckLoading ? 'animate-spin' : ''}`} />
              {healthCheckLoading ? 'Checking...' : 'Refresh Health'}
            </Button>
          </div>
        </div>
        <p className="text-sm md:text-base text-slate-300">
          Monitor and manage your local and remote servers. Health checks run automatically every 30 seconds.
        </p>
      </div>

      {/* Server Statistics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Total Servers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{data.stats.totalServers}</div>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Online</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{data.stats.onlineServers}</div>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Maintenance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{data.stats.maintenanceServers}</div>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Offline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{data.stats.offlineServers}</div>
          </CardContent>
        </Card>
      </div>

      {/* Local Server */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-white mb-4">Local Server</h3>
        <Card className="dashboard-card text-slate-900">
          <CardHeader>
            <CardTitle className="flex items-center justify-between text-slate-900">
              Local Development Server
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 border border-emerald-200">
                Online
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">CPU Usage:</span>
                <span className="font-medium text-slate-900">{data.localServer?.cpuUsage || 0}%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Memory:</span>
                <span className="font-medium text-slate-900">{data.localServer?.memoryUsage || 0}% ({data.localServer?.totalMemory || 0}GB total)</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Disk Usage:</span>
                <span className="font-medium text-slate-900">{data.localServer?.diskUsage || 0}% ({data.localServer?.totalDisk || 0}GB total)</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Uptime:</span>
                <span className="font-medium text-slate-900">{data.localServer?.uptime || 'N/A'}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Load Average:</span>
                <span className="font-medium text-slate-900">{data.localServer?.loadAverage?.join(', ') || 'N/A'}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Network:</span>
                <span className="font-medium text-slate-900">
                  {data.localServer?.network ? `${data.localServer.network.inbound}↓/${data.localServer.network.outbound}↑ Mbps` : 'N/A'}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Platform:</span>
                <span className="font-medium text-slate-900">{data.localServer?.platform || 'N/A'} ({data.localServer?.arch || 'N/A'})</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Remote Servers */}
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Remote Servers</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
          {data.servers.map((server) => (
            <Card key={server.id} className="dashboard-card text-slate-900">
              <CardHeader>
                <CardTitle className="flex items-center justify-between text-slate-900">
                  {server.name}
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(server.status)}`}>
                    {getStatusText(server.status)}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-600">CPU Usage:</span>
                    <span className="font-medium text-slate-900">{server.cpuUsage}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-600">Memory:</span>
                    <span className="font-medium text-slate-900">{server.memoryUsage}% ({server.totalMemory}GB total)</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-600">Disk Usage:</span>
                    <span className="font-medium text-slate-900">{server.diskUsage}% ({server.totalDisk}GB total)</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-600">Location:</span>
                    <span className="font-medium text-slate-900">{server.location}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-600">Uptime:</span>
                    <span className="font-medium text-slate-900">{server.uptime || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-600">Load Average:</span>
                    <span className="font-medium text-slate-900">{server.loadAverage?.join(', ') || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-600">Network:</span>
                    <span className="font-medium text-slate-900">
                      {server.network ? `${server.network.inbound}↓/${server.network.outbound}↑ Mbps` : 'N/A'}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-600">Type:</span>
                    <span className="font-medium text-slate-900 capitalize">{server.type}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-600">Last Health Check:</span>
                    <span className="font-medium text-slate-900">
                      {server.lastHealthCheck ? new Date(server.lastHealthCheck).toLocaleTimeString() : 'Never'}
                    </span>
                  </div>
                  {server.url && (
                    <button className="w-full mt-4 bg-emerald-600 text-white py-2 px-4 rounded-md hover:bg-emerald-700 transition-colors">
                      Connect
                    </button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}