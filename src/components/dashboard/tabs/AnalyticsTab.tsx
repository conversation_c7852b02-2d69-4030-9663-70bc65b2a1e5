import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface AnalyticsData {
  summary: {
    totalUsers: number;
    totalTreaties: number;
    totalNationTreaties: number;
    totalOrdinances: number;
    activeUsers: number;
    newUsersThisMonth: number;
    period: string;
  };
  charts: {
    userGrowth: Array<{ date: string; newUsers: number; totalUsers: number }>;
    treatyGrowth: Array<{ date: string; newTreaties: number; totalTreaties: number }>;
    revenueData: Array<{ date: string; revenue: number; transactions: number }>;
    geographicData: Array<{ country: string; users: number; percentage: number }>;
  };
  metrics: {
    treatyStats: {
      total: number;
      active: number;
      expired: number;
      pending: number;
      activePercentage: number;
      expiredPercentage: number;
      pendingPercentage: number;
    };
    systemMetrics: {
      averageResponseTime: number;
      uptimePercentage: number;
      errorRate: number;
      throughput: number;
      databaseConnections: number;
      cacheHitRate: number;
    };
  };
  adminMetrics?: any;
  securityMetrics?: any;
  financialMetrics?: any;
}

export function AnalyticsTab() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/dashboard/analytics');
      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }
      const data = await response.json();
      setAnalyticsData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-600/50 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-slate-600/50 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="dashboard-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-slate-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-slate-200 rounded w-1/3 mb-2"></div>
                <div className="h-3 bg-slate-200 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="text-center">
          <p className="text-white">Error loading analytics: {error}</p>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="text-center">
          <p className="text-white">No analytics data available</p>
        </div>
      </div>
    );
  }

  const { summary, charts, metrics } = analyticsData;

  return (
    <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
      <div className="mb-4 md:mb-6">
        <h2 className="text-xl md:text-2xl font-bold text-white mb-2">
          Analytics Dashboard
        </h2>
        <p className="text-sm md:text-base text-slate-300">
          Business intelligence and performance metrics.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-4 md:mb-6">
        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{summary.totalUsers.toLocaleString()}</div>
            <p className="text-xs text-slate-600">
              +{summary.newUsersThisMonth} new this month
            </p>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Active Treaties</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{summary.totalTreaties}</div>
            <p className="text-xs text-slate-600">
              {metrics.treatyStats.activePercentage.toFixed(1)}% active
            </p>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Active Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{summary.activeUsers}</div>
            <p className="text-xs text-slate-600">
              Currently online
            </p>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">System Health</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{metrics.systemMetrics.uptimePercentage.toFixed(2)}%</div>
            <p className="text-xs text-slate-600">
              {metrics.systemMetrics.averageResponseTime}ms avg response
            </p>
          </CardContent>
        </Card>
      </div>

      <Card className="dashboard-card text-slate-900">
        <CardHeader>
          <CardTitle className="text-slate-900">Geographic Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {charts.geographicData.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-4 h-4 rounded mr-3 ${
                    index === 0 ? 'bg-blue-500' :
                    index === 1 ? 'bg-green-500' :
                    index === 2 ? 'bg-yellow-500' :
                    'bg-purple-500'
                  }`}></div>
                  <span className="text-sm font-medium text-slate-900">{item.country}</span>
                </div>
                <span className="text-sm text-slate-600">{item.percentage}%</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}