import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';

interface SecurityData {
  summary: {
    totalEvents: number;
    failedLogins: number;
    period: string;
    timeRange: {
      start: string;
      end: string;
    };
  };
  events: Array<{
    id: string;
    type: string;
    severity: string;
    description: string;
    source: string;
    ipAddress: string;
    userAgent: string;
    detectedAt: string;
    resolvedAt: string | null;
    status: string;
    assignedTo: string | null;
    resolution: string | null;
    metadata: any;
  }>;
  statistics: {
    bySeverity: Array<{ severity: string; count: number; percentage: number }>;
    byType: Array<{ type: string; count: number; percentage: number }>;
    topSources: Array<{ source: string; count: number }>;
    suspiciousPatterns: Array<{
      pattern: string;
      count: number;
      riskLevel: string;
      description: string;
    }>;
  };
  systemHealth: {
    overall: string;
    components: {
      firewall: { status: string; lastUpdate: Date; rulesCount: number };
      intrusionDetection: { status: string; threatsDetected: number; lastScan: Date };
      accessControl: { status: string; activePolicies: number; violations: number };
      encryption: { status: string; algorithms: string[]; lastRotation: Date };
    };
    recommendations: string[];
  };
  adminData?: any;
  threatIntelligence?: any;
}

export function SecurityTab() {
  const [securityData, setSecurityData] = useState<SecurityData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSecurityData();
  }, []);

  const fetchSecurityData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/dashboard/security');
      if (!response.ok) {
        throw new Error('Failed to fetch security data');
      }
      const data = await response.json();
      setSecurityData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-600/50 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-slate-600/50 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="dashboard-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-slate-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-slate-200 rounded w-1/3 mb-2"></div>
                <div className="h-3 bg-slate-200 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="text-center">
          <p className="text-white">Error loading security data: {error}</p>
        </div>
      </div>
    );
  }

  if (!securityData) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="text-center">
          <p className="text-white">No security data available</p>
        </div>
      </div>
    );
  }

  const { summary, events, statistics, systemHealth } = securityData;

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'resolved': return 'text-green-600 dark:text-green-400';
      case 'open': return 'text-red-600 dark:text-red-400';
      case 'investigating': return 'text-yellow-600 dark:text-yellow-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
      <div className="mb-4 md:mb-6">
        <h2 className="text-xl md:text-2xl font-bold text-white mb-2">
          Security Dashboard
        </h2>
        <p className="text-sm md:text-base text-slate-300">
          Monitor security events and system alerts.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-4 md:mb-6">
        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Security Events</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{summary.totalEvents}</div>
            <p className="text-xs text-slate-600">
              Last {summary.period}
            </p>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Failed Logins</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{summary.failedLogins}</div>
            <p className="text-xs text-slate-600">
              Last {summary.period}
            </p>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">System Health</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600 capitalize">
              {systemHealth.overall}
            </div>
            <p className="text-xs text-slate-600">
              All systems operational
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
        <Card className="dashboard-card text-slate-900">
          <CardHeader>
            <CardTitle className="text-slate-900">Recent Security Events</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {events.slice(0, 5).map((event) => (
                <div key={event.id} className="flex items-center justify-between p-3 border border-slate-200 rounded-lg bg-slate-50">
                  <div className="flex items-center">
                    <div className={`w-2 h-2 rounded-full mr-3 ${getSeverityColor(event.severity)}`}></div>
                    <div>
                      <p className="text-sm font-medium text-slate-900">{event.description}</p>
                      <p className="text-xs text-slate-600">{event.source}</p>
                    </div>
                  </div>
                  <span className={`text-xs ${getStatusColor(event.status)}`}>
                    {new Date(event.detectedAt).toLocaleTimeString()}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader>
            <CardTitle className="text-slate-900">Suspicious Patterns</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {statistics.suspiciousPatterns.map((pattern, index) => (
                <div key={index} className="p-3 border border-slate-200 rounded-lg bg-slate-50">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-slate-900">{pattern.pattern}</h4>
                    <span className={`text-xs px-2 py-1 rounded ${
                      pattern.riskLevel === 'high' ? 'bg-red-100 text-red-800' :
                      pattern.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {pattern.riskLevel}
                    </span>
                  </div>
                  <p className="text-xs text-slate-600 mb-2">{pattern.description}</p>
                  <div className="text-sm font-medium text-slate-900">{pattern.count} occurrences</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}