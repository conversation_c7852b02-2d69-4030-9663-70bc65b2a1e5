'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface DashboardData {
  user: {
    id: string;
    name: string;
    email: string;
    treaties: number;
    nationTreaties: number;
    createdAt: string;
    profileCompletion: number;
  };
  stats: {
    totalUsers: number;
    totalTreaties: number;
    totalNationTreaties: number;
    totalServers: number;
    activeSessions: number;
    failedLogins: number;
    totalOrdinances?: number;
    pendingTreaties?: number;
  };
  recentActivity: {
    newUsers: number;
    newTreaties: number;
    period: string;
  };
  quickActions: Array<{
    id: string;
    label: string;
    icon: string;
    href: string;
  }>;
  adminStats?: {
    pendingTreaties: number;
    systemHealth: {
      database: string;
      cache: string;
      storage: string;
    };
    securityMetrics: {
      failedLogins: number;
      suspiciousActivity: number;
    };
  };
}

export function OverviewTab() {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const response = await fetch('/api/dashboard/overview');
        if (!response.ok) {
          throw new Error('Failed to fetch dashboard data');
        }
        const dashboardData = await response.json();
        setData(dashboardData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6 space-y-4 md:space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-600/50 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-slate-600/50 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="dashboard-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-slate-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-slate-200 rounded w-1/3 mb-2"></div>
                <div className="h-3 bg-slate-200 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="text-center">
          <p className="text-white">Error loading dashboard: {error}</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="text-center">
          <p className="text-white">No data available</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6 space-y-4 md:space-y-6"
      data-testid="dashboard-overview"
    >
      <div>
        <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">
          Dashboard Overview
        </h1>
        <p className="text-sm md:text-base text-slate-300">
          Welcome to your NWA Alliance dashboard. Here's what's happening with your account.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        <Card
          className="dashboard-card text-slate-900"
          data-testid="dashboard-card-total-notifications"
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Total Notifications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">
              {data.stats.failedLogins || 0}
            </div>
            <p className="text-xs text-slate-600">
              Failed login attempts
            </p>
          </CardContent>
        </Card>

        <Card
          className="dashboard-card text-slate-900"
          data-testid="dashboard-card-active-servers"
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Active Servers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">
              {data.stats.totalServers || 0}
            </div>
            <p className="text-xs text-slate-600">
              {data.stats.activeSessions || 0} active sessions
            </p>
          </CardContent>
        </Card>

        <Card
          className="dashboard-card text-slate-900"
          data-testid="dashboard-card-active-treaties"
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Active Treaties</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">
              {data.user.treaties + data.user.nationTreaties}
            </div>
            <p className="text-xs text-slate-600">
              {data.recentActivity.newTreaties} new this month
            </p>
          </CardContent>
        </Card>

        <Card
          className="dashboard-card text-slate-900"
          data-testid="dashboard-card-profile-status"
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Profile Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">
              {isNaN(data.user.profileCompletion) ? '0' : data.user.profileCompletion}%
            </div>
            <p className="text-xs text-slate-600">
              {isNaN(data.user.profileCompletion) || data.user.profileCompletion === 100
                ? 'Profile complete'
                : `${Math.round((data.user.profileCompletion / 100) * 5)} of 5 required fields completed`
              }
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-6">
        <Card className="dashboard-card text-slate-900">
          <CardHeader>
            <CardTitle className="text-slate-900">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-slate-900">Active sessions: {data.stats.activeSessions}</p>
                  <p className="text-xs text-slate-600">Currently online</p>
                </div>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-slate-900">New treaties: {data.recentActivity.newTreaties}</p>
                  <p className="text-xs text-slate-600">Last {data.recentActivity.period}</p>
                </div>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-slate-900">Total users: {data.stats.totalUsers}</p>
                  <p className="text-xs text-slate-600">Platform members</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader>
            <CardTitle className="text-slate-900">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.quickActions.map((action) => (
                <button
                  key={action.id}
                  className="w-full text-left p-3 rounded-lg bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-[1.02]"
                >
                  <div className="font-medium text-white">{action.label}</div>
                  <div className="text-sm text-emerald-100">
                    {action.id === 'view-profile' && 'Manage your personal information'}
                    {action.id === 'my-treaties' && 'View and manage your treaties'}
                    {action.id === 'notifications' && 'Check your notifications'}
                    {action.id === 'settings' && 'Update your settings'}
                    {action.id === 'create-user' && 'Add new users to the system'}
                    {action.id === 'manage-treaties' && 'Manage all treaties'}
                    {action.id === 'system-settings' && 'Configure system settings'}
                    {action.id === 'security-audit' && 'Review security logs'}
                  </div>
                </button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
