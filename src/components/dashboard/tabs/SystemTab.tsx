import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';

interface SystemData {
  systemHealth: {
    overall: string;
    status: string;
    uptime: string;
    lastIncident: Date;
    components: {
      web: { status: string; responseTime: number; uptime: string };
      api: { status: string; responseTime: number; uptime: string };
      database: { status: string; connections: number; uptime: string };
      cache: { status: string; hitRate: number; uptime: string };
    };
  };
  resourceUsage: {
    cpu: { usage: number; cores: number; temperature: number };
    memory: { used: number; total: number; usage: number; swap: { used: number; total: number } };
    disk: { used: number; total: number; usage: number; readSpeed: number; writeSpeed: number };
    network: { inbound: number; outbound: number; connections: number };
  };
  serviceStatus: Array<{
    name: string;
    status: string;
    uptime: string;
    lastRestart: Date;
    version: string;
  }>;
  databaseMetrics: {
    connections: { active: number; idle: number; total: number };
    queries: { perSecond: number; slowQueries: number; failedQueries: number };
    storage: { tables: number; indexes: number; size: number };
    performance: { readLatency: number; writeLatency: number; throughput: number };
  };
  cacheMetrics: {
    hits: number;
    misses: number;
    hitRate: number;
    keys: number;
    memoryUsed: number;
    evictions: number;
    operations: { get: number; set: number; delete: number };
  };
  recentEvents: Array<{
    id: string;
    type: string;
    message: string;
    timestamp: Date;
    source: string;
  }>;
  performanceMetrics: {
    responseTime: { average: number; p95: number; p99: number };
    throughput: { requestsPerMinute: number; requestsPerSecond: number; concurrentUsers: number };
    errorRate: { overall: number; byEndpoint: Array<{ endpoint: string; rate: number }> };
    availability: { uptime: number; downtime: number };
  };
  adminData?: any;
  maintenanceInfo?: any;
  backupInfo?: any;
}

export function SystemTab() {
  const [systemData, setSystemData] = useState<SystemData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSystemData();
  }, []);

  const fetchSystemData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/dashboard/system');
      if (!response.ok) {
        throw new Error('Failed to fetch system data');
      }
      const data = await response.json();
      setSystemData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-600/50 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-slate-600/50 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="dashboard-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-slate-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-slate-200 rounded w-1/3 mb-2"></div>
                <div className="h-3 bg-slate-200 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="text-center">
          <p className="text-white">Error loading system data: {error}</p>
        </div>
      </div>
    );
  }

  if (!systemData) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="text-center">
          <p className="text-white">No system data available</p>
        </div>
      </div>
    );
  }

  const { systemHealth, resourceUsage, serviceStatus, recentEvents } = systemData;

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
      case 'running':
        return 'text-green-600 dark:text-green-400';
      case 'warning':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'error':
      case 'down':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusDotColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
      case 'running':
        return 'bg-green-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'error':
      case 'down':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
      <div className="mb-4 md:mb-6">
        <h2 className="text-xl md:text-2xl font-bold text-white mb-2">
          System Health
        </h2>
        <p className="text-sm md:text-base text-slate-300">
          Monitor system performance and infrastructure status.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-4 md:mb-6">
        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">CPU Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{resourceUsage.cpu.usage}%</div>
            <div className="w-full bg-slate-200 rounded-full h-2 mt-2">
              <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${resourceUsage.cpu.usage}%` }}></div>
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Memory Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{resourceUsage.memory.usage}%</div>
            <div className="w-full bg-slate-200 rounded-full h-2 mt-2">
              <div className="bg-green-600 h-2 rounded-full" style={{ width: `${resourceUsage.memory.usage}%` }}></div>
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">Disk Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{resourceUsage.disk.usage}%</div>
            <div className="w-full bg-slate-200 rounded-full h-2 mt-2">
              <div className="bg-yellow-600 h-2 rounded-full" style={{ width: `${resourceUsage.disk.usage}%` }}></div>
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-900">System Health</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600 capitalize">
              {systemHealth.overall}
            </div>
            <p className="text-xs text-slate-600">
              {systemHealth.uptime} uptime
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-6">
        <Card className="dashboard-card text-slate-900">
          <CardHeader>
            <CardTitle className="text-slate-900">Service Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {serviceStatus.map((service, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${getStatusDotColor(service.status)}`}></div>
                    <div>
                      <span className="text-sm font-medium text-slate-900">{service.name}</span>
                      <p className="text-xs text-slate-600">{service.version}</p>
                    </div>
                  </div>
                  <span className={`text-sm ${getStatusColor(service.status)}`}>
                    {service.status}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card text-slate-900">
          <CardHeader>
            <CardTitle className="text-slate-900">Recent System Events</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentEvents.slice(0, 5).map((event) => (
                <div key={event.id} className="flex items-start space-x-3 p-3 border border-slate-200 rounded-lg bg-slate-50">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    event.type === 'info' ? 'bg-blue-500' :
                    event.type === 'warning' ? 'bg-yellow-500' :
                    event.type === 'error' ? 'bg-red-500' :
                    'bg-gray-500'
                  }`}></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-slate-900">{event.message}</p>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-xs text-slate-600">{event.source}</span>
                      <span className="text-xs text-slate-600">
                        {new Date(event.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}