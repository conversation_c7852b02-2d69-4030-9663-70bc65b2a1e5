'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

interface NotificationData {
  notifications: Array<{
    id: string;
    type: string;
    message: string;
    priority: string;
    category: string;
    createdAt: string;
    isRead: boolean;
  }>;
  stats: {
    total: number;
    unread: number;
  };
}

export function NotificationsTab() {
  const [data, setData] = useState<NotificationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchNotificationData = async () => {
      try {
        const response = await fetch('/api/dashboard/notifications');
        if (!response.ok) {
          throw new Error('Failed to fetch notification data');
        }
        const notificationData = await response.json();
        setData(notificationData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchNotificationData();
  }, []);

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'info':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} days ago`;
  };

  if (loading) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-600/50 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-slate-600/50 rounded w-1/2 mb-6"></div>
        </div>
        <Card className="dashboard-card">
          <CardHeader>
            <div className="h-6 bg-slate-200 rounded w-1/3 mb-2"></div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="p-4 border border-slate-200 rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                      <div className="h-3 bg-slate-200 rounded w-full"></div>
                      <div className="h-3 bg-slate-200 rounded w-1/2"></div>
                    </div>
                    <div className="h-6 bg-slate-200 rounded w-16"></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="text-center">
          <p className="text-white">Error loading notifications: {error}</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
        <div className="text-center">
          <p className="text-white">No notification data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 p-4 md:p-6">
      <div className="mb-4 md:mb-6">
        <h2 className="text-xl md:text-2xl font-bold text-white mb-2">
          Notifications
        </h2>
        <p className="text-sm md:text-base text-slate-300">
          Manage your notifications and alerts.
        </p>
      </div>

      <Card className="dashboard-card text-slate-900">
        <CardHeader>
          <CardTitle className="text-slate-900">Recent Notifications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.notifications.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-slate-600">No notifications found</p>
              </div>
            ) : (
              data.notifications.map((notification) => (
                <div key={notification.id} className="p-4 border border-slate-200 rounded-lg bg-slate-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-slate-900">
                        {notification.message}
                      </h4>
                      <p className="text-sm text-slate-600 mt-1">
                        {notification.type} • {notification.category}
                      </p>
                      <p className="text-xs text-slate-500 mt-2">
                        {formatTimeAgo(notification.createdAt)}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(notification.priority)}`}>
                        {notification.priority}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}