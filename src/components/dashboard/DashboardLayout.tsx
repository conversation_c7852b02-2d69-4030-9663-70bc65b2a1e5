import React from 'react';

interface DashboardLayoutProps {
  children: React.ReactNode;
  className?: string;
  'data-testid'?: string;
}

export function DashboardLayout({
  children,
  className = '',
  'data-testid': testId = 'dashboard-layout'
}: DashboardLayoutProps) {
  return (
    <div
      data-testid={testId}
      className={`flex flex-col h-screen bg-white dark:bg-gray-900 overflow-hidden ${className}`}
    >
      <div className="flex-1 flex flex-col min-h-0">
        {children}
      </div>
    </div>
  );
}