'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Upload,
  FileText,
  DollarSign,
  Calendar,
  AlertCircle,
  CheckCircle,
  Info,
} from 'lucide-react';
import { format } from 'date-fns';

interface UserTreatyType {
  id: string;
  status: string;
  paymentStatus: string;
  treatyType: {
    id: string;
    name: string;
    description: string | null;
    category: string;
    price: number;
    currency: string;
    requiresPayment: boolean;
    paymentDeadlineDays: number | null;
    isActive: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

interface TreatyApplication {
  id: string;
  status: string;
  paymentStatus: string;
  treatyType: {
    id: string;
    name: string;
    description: string | null;
    category: string;
    price: number;
    currency: string;
    requiresPayment: boolean;
    paymentDeadlineDays: number | null;
    isActive: boolean;
  };
  appliedAt: string;
}

interface PaymentSubmissionModalProps {
  application: TreatyApplication | null;
  isOpen: boolean;
  onClose: () => void;
  onPaymentSubmit: (paymentData: PaymentFormData) => Promise<void>;
}

interface PaymentFormData {
  payerName: string;
  payerEmail: string;
  payerPhone?: string;
  paymentMethod: string;
  amount: number;
  transactionId?: string;
  notes?: string;
  receiptFile?: File;
}

const PaymentSubmissionModal: React.FC<PaymentSubmissionModalProps> = ({
  application,
  isOpen,
  onClose,
  onPaymentSubmit,
}) => {
  const [formData, setFormData] = useState<PaymentFormData>({
    payerName: '',
    payerEmail: '',
    payerPhone: '',
    paymentMethod: '',
    amount: 0,
    transactionId: '',
    notes: '',
  });
  const [receiptFile, setReceiptFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string>('');

  useEffect(() => {
    if (application) {
      setFormData(prev => ({
        ...prev,
        amount: application.treatyType.price,
      }));
    }
  }, [application]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setReceiptFile(file);
      
      // Create preview for images
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setPreviewUrl(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      } else {
        setPreviewUrl('');
      }
    }
  };

  const handleSubmit = async () => {
    if (!application) return;
    
    setIsSubmitting(true);
    try {
      await onPaymentSubmit({
        ...formData,
        receiptFile: receiptFile || undefined,
      });
      
      // Reset form
      setFormData({
        payerName: '',
        payerEmail: '',
        payerPhone: '',
        paymentMethod: '',
        amount: 0,
        transactionId: '',
        notes: '',
      });
      setReceiptFile(null);
      setPreviewUrl('');
      onClose();
    } catch (error) {
      console.error('Error submitting payment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!application) return null;

  const isPaymentRequired = application.treatyType.requiresPayment;
  const canSubmitPayment = application.status === 'APPROVED' && 
                          application.paymentStatus === 'AWAITING_PAYMENT';

  const getDeadlineDate = () => {
    const createdDate = new Date(application.appliedAt);
    const deadlineDays = application.treatyType.paymentDeadlineDays || 30; // Default to 30 days if null
    const deadlineDate = new Date(createdDate.getTime() + 
      (deadlineDays * 24 * 60 * 60 * 1000));
    return deadlineDate;
  };

  const isOverdue = getDeadlineDate() < new Date();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Submit Payment</DialogTitle>
          <DialogDescription>
            Submit payment information for {application.treatyType.name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Application Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Application Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs text-gray-600">Treaty Type</Label>
                  <p className="font-medium">{application.treatyType.name}</p>
                  <p className="text-sm text-gray-500">{application.treatyType.category}</p>
                </div>
                <div>
                  <Label className="text-xs text-gray-600">Application Status</Label>
                  <Badge className={
                    application.status === 'APPROVED' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }>
                    {application.status}
                  </Badge>
                </div>
              </div>
              
              {isPaymentRequired && (
                <div className="border-t pt-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Payment Amount</span>
                    <span className="text-lg font-semibold text-green-600">
                      {application.treatyType.currency} {application.treatyType.price.toLocaleString()}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4" />
                    <span>Payment Deadline: {format(getDeadlineDate(), 'MMM dd, yyyy')}</span>
                    {isOverdue && (
                      <Badge className="bg-red-100 text-red-800">
                        Overdue
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {!canSubmitPayment && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-yellow-800">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">
                    {application.status !== 'APPROVED' 
                      ? 'Application must be approved before payment can be submitted.'
                      : 'Payment has already been submitted or is not required.'
                    }
                  </span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Form */}
          {canSubmitPayment && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="payer-name">Payer Name *</Label>
                  <Input
                    id="payer-name"
                    value={formData.payerName}
                    onChange={(e) => setFormData(prev => ({ ...prev, payerName: e.target.value }))}
                    placeholder="Enter payer's full name"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="payer-email">Payer Email *</Label>
                  <Input
                    id="payer-email"
                    type="email"
                    value={formData.payerEmail}
                    onChange={(e) => setFormData(prev => ({ ...prev, payerEmail: e.target.value }))}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="payer-phone">Payer Phone (Optional)</Label>
                <Input
                  id="payer-phone"
                  type="tel"
                  value={formData.payerPhone}
                  onChange={(e) => setFormData(prev => ({ ...prev, payerPhone: e.target.value }))}
                  placeholder="+****************"
                />
              </div>

              <div>
                <Label htmlFor="payment-method">Payment Method *</Label>
                <Select value={formData.paymentMethod} onValueChange={(value) => setFormData(prev => ({ ...prev, paymentMethod: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                    <SelectItem value="CASH">Cash</SelectItem>
                    <SelectItem value="CHECK">Check</SelectItem>
                    <SelectItem value="MONEY_ORDER">Money Order</SelectItem>
                    <SelectItem value="OTHER">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="amount">Amount *</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    value={formData.amount}
                    onChange={(e) => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                    placeholder="0.00"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Expected: {application.treatyType.currency} {application.treatyType.price.toLocaleString()}
                  </p>
                </div>
                <div>
                  <Label htmlFor="transaction-id">Transaction ID (Optional)</Label>
                  <Input
                    id="transaction-id"
                    value={formData.transactionId}
                    onChange={(e) => setFormData(prev => ({ ...prev, transactionId: e.target.value }))}
                    placeholder="Bank reference or receipt number"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="receipt">Payment Receipt (Optional)</Label>
                <div className="mt-2">
                  <div className="flex items-center justify-center w-full">
                    <label
                      htmlFor="receipt-upload"
                      className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
                    >
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className="w-8 h-8 mb-4 text-gray-500" />
                        <p className="mb-2 text-sm text-gray-500">
                          <span className="font-semibold">Click to upload</span> or drag and drop
                        </p>
                        <p className="text-xs text-gray-500">
                          PNG, JPG, PDF up to 10MB
                        </p>
                      </div>
                      <input
                        id="receipt-upload"
                        type="file"
                        className="hidden"
                        accept="image/*,.pdf"
                        onChange={handleFileChange}
                      />
                    </label>
                  </div>
                  
                  {receiptFile && (
                    <div className="mt-2 p-2 bg-gray-50 rounded">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">
                          <FileText className="inline w-4 h-4 mr-1" />
                          {receiptFile.name}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setReceiptFile(null);
                            setPreviewUrl('');
                          }}
                        >
                          Remove
                        </Button>
                      </div>
                      {previewUrl && (
                        <div className="mt-2">
                          <img
                            src={previewUrl}
                            alt="Receipt preview"
                            className="max-w-full h-32 object-contain rounded"
                          />
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="notes">Additional Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Add any additional information about this payment..."
                  rows={3}
                />
              </div>
            </div>
          )}

          {/* Information Box */}
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">Payment Verification Process:</p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Submit payment information and receipt (if available)</li>
                    <li>Administrators will review and verify your payment</li>
                    <li>Once verified, your treaty application will be activated</li>
                    <li>You'll receive email notifications at each step</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          {canSubmitPayment && (
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !formData.payerName || !formData.payerEmail || !formData.paymentMethod}
              className="bg-green-600 hover:bg-green-700"
            >
              {isSubmitting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <CheckCircle className="h-4 w-4 mr-2" />
              )}
              Submit Payment
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentSubmissionModal;