'use client';

import React, { useState, useRef, useEffect } from 'react';
import { toast } from 'sonner';
import { X, FileText } from 'lucide-react';

interface OrdinanceUploadFormProps {
  onUploadSuccess?: () => void;
}

interface UploadedFile {
  id: string;
  file: File;
  fileName: string;
  fileSize: number;
  fileType: string;
  status: 'pending' | 'uploading' | 'uploaded' | 'error';
  error?: string;
  uploadedFileId?: string; // The ID returned from the server
}

export const OrdinanceUploadForm: React.FC<OrdinanceUploadFormProps> = ({ onUploadSuccess }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [creatingOrdinance, setCreatingOrdinance] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const uploadNewFiles = async (filesToUpload: UploadedFile[]): Promise<void> => {
    for (const fileData of filesToUpload) {
      try {
        setUploadedFiles(prev => prev.map(f => 
          f.id === fileData.id ? { ...f, status: 'uploading' } : f
        ));
        
        const formData = new FormData();
        formData.append('document', fileData.file);
        
        const response = await fetch('/api/ordinances/temp-upload', {
          method: 'POST',
          body: formData,
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to upload file');
        }
        
        const result = await response.json();
        
        setUploadedFiles(prev => prev.map(f => 
          f.id === fileData.id ? { 
            ...f, 
            status: 'uploaded',
            uploadedFileId: result.fileId 
          } : f
        ));
        
      } catch (error: any) {
        setUploadedFiles(prev => prev.map(f => 
          f.id === fileData.id ? { ...f, status: 'error', error: error.message } : f
        ));
        throw error;
      }
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const newFiles: UploadedFile[] = [];
    
    for (let i = 0; i < selectedFiles.length; i++) {
      const file = selectedFiles[i];
      
      // Check file type - allow PDF, DOC, and DOCX files
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];
      
      if (!allowedTypes.includes(file.type)) {
        toast.error(`Invalid file type for ${file.name}. Please upload a PDF, DOC, or DOCX file.`);
        continue;
      }
      
      // Check file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        toast.error(`File size exceeds 5MB limit for ${file.name}.`);
        continue;
      }
      
      newFiles.push({
        id: Math.random().toString(36).substring(2, 9),
        file,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        status: 'pending'
      });
    }
    
    if (newFiles.length > 0) {
      setUploadedFiles(prev => [...prev, ...newFiles]);
      
      // Auto-upload files immediately after selection
      setTimeout(async () => {
        try {
          setUploading(true);
          await uploadNewFiles(newFiles);
        } catch (error) {
          console.error('Auto-upload failed:', error);
        } finally {
          setUploading(false);
        }
      }, 100);
    }
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) {
      toast.error('Please enter a title for the ordinance.');
      return;
    }
    
    if (uploadedFiles.length === 0) {
      toast.error('Please upload at least one document.');
      return;
    }

    const uploadedFileIds = uploadedFiles
      .filter(f => f.status === 'uploaded' && f.uploadedFileId)
      .map(f => f.uploadedFileId!);
    
    if (uploadedFileIds.length === 0) {
      toast.error('No files were successfully uploaded');
      return;
    }

    setCreatingOrdinance(true);
    
    try {
      console.log('Creating ordinance with data:', {
        notes: description,
        title: title,
        fileIds: uploadedFileIds
      });

      const ordinanceResponse = await fetch('/api/ordinances', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes: description,
          title: title,
          fileIds: uploadedFileIds
        }),
      });
      
      if (!ordinanceResponse.ok) {
        const errorText = await ordinanceResponse.text();
        console.error('Ordinance creation failed:', errorText);
        let errorMessage = 'Failed to create ordinance record';
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          errorMessage = errorText || errorMessage;
        }
        throw new Error(errorMessage);
      }
      
      const result = await ordinanceResponse.json();
      console.log('Ordinance created successfully:', result);
      
      // Success - show success message
      toast.success('Ordinance created successfully!');
      
      // Reset form
      setTitle('');
      setDescription('');
      setUploadedFiles([]);
      // Keep the selected ordinance type for convenience
      
      // Notify parent component
      if (onUploadSuccess) {
        onUploadSuccess();
      }
      
    } catch (error: any) {
      console.error('Error creating ordinance:', error);
      toast.error(error.message || 'An unexpected error occurred. Please try again.');
    } finally {
      setCreatingOrdinance(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
          Ordinance Title
        </label>
        <input
          type="text"
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
          required
        />
      </div>
      
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
          Description
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
          Documents
        </label>
        
        {/* File Input */}
        <div className="flex items-center space-x-4 mb-4">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            className="hidden"
            multiple
          />
          <button
            type="button"
            onClick={triggerFileInput}
            disabled={uploading}
            className="px-4 py-2 bg-blue-800 text-white rounded-md hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 text-sm transition-colors duration-200 disabled:opacity-50"
          >
            {uploading ? 'Uploading...' : 'Select & Upload Files'}
          </button>
        </div>
        
        {/* Uploaded Files List */}
        {uploadedFiles.length > 0 && (
          <div className="space-y-2 mb-4">
            <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Selected Files ({uploadedFiles.length})
            </h4>
            {uploadedFiles.map((fileData) => (
              <div
                key={fileData.id}
                className={`flex items-center justify-between p-3 border rounded-md ${
                  fileData.status === 'uploaded'
                    ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                    : fileData.status === 'error'
                    ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
                    : fileData.status === 'uploading'
                    ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20'
                    : 'border-gray-200 bg-gray-50 dark:border-gray-600 dark:bg-gray-700'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <FileText className="h-5 w-5 text-slate-500 dark:text-slate-400" />
                  <div className="min-w-0">
                    <p className="text-sm font-medium text-slate-700 dark:text-slate-300 truncate max-w-xs">
                      {fileData.fileName}
                    </p>
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      {formatFileSize(fileData.fileSize)}
                    </p>
                    {fileData.error && (
                      <p className="text-xs text-red-500 dark:text-red-400">
                        {fileData.error}
                      </p>
                    )}
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => removeFile(fileData.id)}
                  className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                  disabled={fileData.status === 'uploading'}
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        )}
        
        <p className="mt-1 text-xs text-slate-500 dark:text-slate-400">
          PDF, DOC, or DOCX files only. Max 5MB per file. Select multiple files to upload.
        </p>
      </div>
      
      <div className="flex justify-end space-x-3">
        <button
          type="submit"
          disabled={uploading || creatingOrdinance || uploadedFiles.length === 0 || uploadedFiles.some(f => f.status !== 'uploaded')}
          className="px-6 py-2 bg-blue-800 text-white rounded-md hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 transition-colors duration-200"
        >
          {creatingOrdinance ? 'Creating...' : uploading ? 'Uploading Files...' : 'Create Ordinance'}
        </button>
      </div>
      
      {uploadedFiles.length > 0 && uploadedFiles.some(f => f.status !== 'uploaded') && (
        <div className="mt-2 text-sm text-blue-600 dark:text-blue-400">
          Files are being uploaded automatically. Please wait for all files to turn green before creating the ordinance.
        </div>
      )}
    </form>
  );
};