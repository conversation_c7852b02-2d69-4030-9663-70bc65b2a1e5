'use client';

import React, { useState, useEffect } from 'react';
import {
  Bell,
  Search,
  Filter,
  Check,
  Check<PERSON>he<PERSON>,
  Trash2,
  Download,
  Eye,
  EyeOff
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';

interface Notification {
  id: string;
  type: string;
  message: string;
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  category: string;
  createdAt: string;
  isRead: boolean;
  data?: any;
  trigger?: string;
  triggeredBy?: string;
}

interface NotificationStats {
  total: number;
  unread: number;
  byPriority: Record<string, number>;
  byCategory: Record<string, number>;
}

export const UserNotificationCenter: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const [stats, setStats] = useState<NotificationStats>({
    total: 0,
    unread: 0,
    byPriority: {},
    byCategory: {}
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('ALL');
  const [categoryFilter, setCategoryFilter] = useState('ALL');
  const [readStatusFilter, setReadStatusFilter] = useState('ALL');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);

  const { data: session } = useSession();

  // Fetch notifications
  const fetchNotifications = async () => {
    if (!session) return;

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: '1',
        limit: '100',
      });

      const response = await fetch(`/api/admin/notifications?${params}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        if (response.status === 403) {
          throw new Error('Admin access required to view notifications');
        }
        throw new Error(`Failed to fetch notifications (HTTP ${response.status})`);
      }

      const data = await response.json();
      setNotifications(data.notifications || []);
      setStats({
        total: data.pagination?.totalCount || data.notifications?.length || 0,
        unread: data.unreadCount || data.notifications?.filter((n: Notification) => !n.isRead).length || 0,
        byPriority: data.stats?.byPriority || {},
        byCategory: data.stats?.byCategory || {}
      });
    } catch (error) {
      setError('Failed to load notifications');
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // Filter notifications based on search and filters
  useEffect(() => {
    let filtered = notifications;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(notification =>
        notification.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Priority filter
    if (priorityFilter !== 'ALL') {
      filtered = filtered.filter(notification => notification.priority === priorityFilter);
    }

    // Category filter
    if (categoryFilter !== 'ALL') {
      filtered = filtered.filter(notification => notification.category === categoryFilter);
    }

    // Read status filter
    if (readStatusFilter !== 'ALL') {
      filtered = filtered.filter(notification =>
        readStatusFilter === 'READ' ? notification.isRead : !notification.isRead
      );
    }

    setFilteredNotifications(filtered);
  }, [notifications, searchTerm, priorityFilter, categoryFilter, readStatusFilter]);

  // Initial load
  useEffect(() => {
    fetchNotifications();
  }, [session]);

  // Mark notifications as read
  const markAsRead = async (notificationIds: string[]) => {
    if (notificationIds.length === 0) {
      toast.error('Please select notifications to mark as read');
      return;
    }

    try {
      const response = await fetch('/api/admin/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notificationIds,
          markAll: false
        }),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to mark notifications as read');
      }

      setNotifications(prev =>
        prev.map(n =>
          notificationIds.includes(n.id) ? { ...n, isRead: true } : n
        )
      );
      setSelectedNotifications([]);
      toast.success(`Marked ${notificationIds.length} notifications as read`);
    } catch (error) {
      toast.error('Failed to mark notifications as read');
      console.error('Error marking notifications as read:', error);
    }
  };

  // Mark all as read
  const markAllAsRead = async () => {
    try {
      const response = await fetch('/api/admin/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notificationIds: [],
          markAll: true
        }),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read');
      }

      setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      toast.success('Marked all notifications as read');
    } catch (error) {
      toast.error('Failed to mark all notifications as read');
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Delete notifications
  const deleteNotifications = async (notificationIds: string[]) => {
    if (notificationIds.length === 0) {
      toast.error('Please select notifications to delete');
      return;
    }

    if (!confirm(`Are you sure you want to delete ${notificationIds.length} notifications?`)) {
      return;
    }

    try {
      const deletePromises = notificationIds.map(id =>
        fetch(`/api/admin/notifications/${id}`, {
          method: 'DELETE',
          credentials: 'include',
        })
      );

      const results = await Promise.all(deletePromises);

      if (results.some(r => !r.ok)) {
        throw new Error('Failed to delete some notifications');
      }

      setNotifications(prev => prev.filter(n => !notificationIds.includes(n.id)));
      setSelectedNotifications([]);
      toast.success(`Deleted ${notificationIds.length} notifications`);
    } catch (error) {
      toast.error('Failed to delete notifications');
      console.error('Error deleting notifications:', error);
    }
  };

  // Export notifications
  const exportNotifications = () => {
    const csvContent = [
      ['ID', 'Type', 'Message', 'Priority', 'Category', 'Read', 'Created At', 'Trigger', 'Triggered By'].join(','),
      ...filteredNotifications.map(n => [
        n.id,
        n.type,
        `"${n.message.replace(/"/g, '""')}"`,
        n.priority,
        n.category,
        n.isRead ? 'Yes' : 'No',
        new Date(n.createdAt).toLocaleString(),
        n.trigger || '',
        n.triggeredBy || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `notifications-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'NORMAL': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'LOW': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'USER_MANAGEMENT': return '👤';
      case 'TREATY': return '📜';
      case 'REMOTE_SERVER': return '🔗';
      case 'SYSTEM': return '⚙️';
      case 'SECURITY': return '🔒';
      default: return '📢';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8" data-testid="notifications-loading">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        <p className="ml-4 text-gray-600 dark:text-gray-400">Loading notifications...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8" data-testid="notifications-error">
        <div className="text-red-500 mb-4">❌</div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Error Loading Notifications</h3>
        <p className="text-gray-500 dark:text-gray-400 mb-4">{error}</p>
        {error.includes('Admin access required') && (
          <p className="text-sm text-gray-400 dark:text-gray-500 mb-4">
            This feature is only available to administrators. Please contact your system administrator if you need access.
          </p>
        )}
        <Button onClick={fetchNotifications} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6" data-testid="notifications-center">
      {/* Header with stats */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6" data-testid="notifications-header">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Bell className="h-8 w-8 text-blue-500 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Notifications</h1>
              <p className="text-gray-600 dark:text-gray-400">Manage your notifications and stay updated</p>
            </div>
          </div>
          <div className="flex items-center space-x-4" data-testid="notifications-header-actions">
            <Button
              onClick={markAllAsRead}
              variant="outline"
              disabled={stats.unread === 0}
              className="flex items-center"
              data-testid="notifications-mark-all-read"
            >
              <CheckCheck className="h-4 w-4 mr-2" />
              Mark All Read
            </Button>
            <Button
              onClick={exportNotifications}
              variant="outline"
              className="flex items-center"
              data-testid="notifications-export"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Stats cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4" data-testid="notifications-stats">
          <div className="bg-blue-100 dark:bg-blue-900/30 rounded-lg p-4" data-testid="notifications-stat-total">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{stats.total}</div>
            <div className="text-sm text-blue-600 dark:text-blue-400">Total Notifications</div>
          </div>
          <div className="bg-orange-100 dark:bg-orange-900/30 rounded-lg p-4" data-testid="notifications-stat-unread">
            <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">{stats.unread}</div>
            <div className="text-sm text-orange-600 dark:text-orange-400">Unread</div>
          </div>
          <div className="bg-green-100 dark:bg-green-900/30 rounded-lg p-4" data-testid="notifications-stat-read">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {stats.total - stats.unread}
            </div>
            <div className="text-sm text-green-600 dark:text-green-400">Read</div>
          </div>
          <div className="bg-purple-100 dark:bg-purple-900/30 rounded-lg p-4" data-testid="notifications-stat-categories">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {Object.keys(stats.byCategory).length}
            </div>
            <div className="text-sm text-purple-600 dark:text-purple-400">Categories</div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search notifications..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
                data-testid="notifications-search"
              />
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-sm"
              data-testid="notifications-filter-priority"
            >
              <option value="ALL">All Priorities</option>
              <option value="URGENT">Urgent</option>
              <option value="HIGH">High</option>
              <option value="NORMAL">Normal</option>
              <option value="LOW">Low</option>
            </select>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-sm"
              data-testid="notifications-filter-category"
            >
              <option value="ALL">All Categories</option>
              <option value="USER_MANAGEMENT">User Management</option>
              <option value="TREATY">Treaty</option>
              <option value="REMOTE_SERVER">Remote Server</option>
              <option value="SYSTEM">System</option>
              <option value="SECURITY">Security</option>
            </select>
            <select
              value={readStatusFilter}
              onChange={(e) => setReadStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-sm"
              data-testid="notifications-filter-status"
            >
              <option value="ALL">All Status</option>
              <option value="UNREAD">Unread</option>
              <option value="READ">Read</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bulk actions */}
      {selectedNotifications.length > 0 && (
        <div
          className="bg-blue-100 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4"
          data-testid="notifications-bulk-actions"
        >
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700 dark:text-blue-300">
              {selectedNotifications.length} notification{selectedNotifications.length !== 1 ? 's' : ''} selected
            </span>
            <div className="flex space-x-2">
              <Button
                onClick={() => markAsRead(selectedNotifications)}
                size="sm"
                variant="outline"
                className="text-blue-600 border-blue-300 hover:bg-blue-100"
                data-testid="notifications-bulk-mark-read"
              >
                <Check className="h-4 w-4 mr-1" />
                Mark Read
              </Button>
              <Button
                onClick={() => deleteNotifications(selectedNotifications)}
                size="sm"
                variant="outline"
                className="text-red-600 border-red-300 hover:bg-red-100"
                data-testid="notifications-bulk-delete"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Notifications list */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow overflow-hidden">
        {filteredNotifications.length === 0 ? (
          <div className="text-center p-12">
            <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {searchTerm || priorityFilter !== 'ALL' || categoryFilter !== 'ALL' || readStatusFilter !== 'ALL'
                ? 'No notifications match your search criteria.'
                : 'No notifications found.'}
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm || priorityFilter !== 'ALL' || categoryFilter !== 'ALL' || readStatusFilter !== 'ALL'
                ? 'Try adjusting your filters to see more notifications.'
                : 'You\'re all caught up!'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto" data-testid="notifications-table-container">
            <table
              className="min-w-full divide-y divide-gray-200 dark:divide-slate-700"
              data-testid="notifications-table"
            >
              <thead className="bg-slate-100 dark:bg-slate-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    <input
                      type="checkbox"
                      checked={selectedNotifications.length === filteredNotifications.length && filteredNotifications.length > 0}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedNotifications(filteredNotifications.map(n => n.id));
                        } else {
                          setSelectedNotifications([]);
                        }
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Message
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
                {filteredNotifications.map((notification) => (
                  <tr
                    key={notification.id}
                    className="hover:bg-slate-100 dark:hover:bg-slate-700"
                    data-testid={`notification-row-${notification.id}`}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedNotifications.includes(notification.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedNotifications([...selectedNotifications, notification.id]);
                          } else {
                            setSelectedNotifications(selectedNotifications.filter(id => id !== notification.id));
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {notification.isRead ? (
                        <Eye className="w-4 h-4 text-green-500" />
                      ) : (
                        <EyeOff className="w-4 h-4 text-blue-500" />
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {notification.type}
                      </div>
                      {notification.trigger && (
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {notification.trigger}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 dark:text-white max-w-xs truncate">
                        {notification.message}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge className={getPriorityColor(notification.priority)}>
                        {notification.priority}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900 dark:text-white">
                        <span className="mr-2">{getCategoryIcon(notification.category)}</span>
                        {notification.category}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(notification.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                       {!notification.isRead && (
                          <Button
                            onClick={() => markAsRead([notification.id])}
                            variant="ghost"
                            size="sm"
                            className="text-blue-600 hover:text-blue-800"
                            data-testid="notification-mark-read"
                          >
                            <Check className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          onClick={() => deleteNotifications([notification.id])}
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-800"
                          data-testid="notification-delete"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};
