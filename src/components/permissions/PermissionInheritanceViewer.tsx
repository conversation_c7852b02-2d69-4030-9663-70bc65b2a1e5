'use client'

import React from 'react'
import { useUserServerPermissions } from '@/hooks/usePermissionInheritance'
import { 
  Shield, 
  User, 
  Crown, 
  Clock, 
  CheckCircle,
  AlertCircle,
  Users,
  Server,
  RefreshCw
} from 'lucide-react'

interface PermissionInheritanceViewerProps {
  userId: string
  serverId: string
  serverName?: string
  onRefresh?: () => void
}

export function PermissionInheritanceViewer({ 
  userId, 
  serverId, 
  serverName,
  onRefresh 
}: PermissionInheritanceViewerProps) {
  const { permissions, loading, error, refetch } = useUserServerPermissions(userId, serverId)

  const handleRefresh = () => {
    refetch()
    onRefresh?.()
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
        <span className="ml-2 text-slate-600">Loading permissions...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center gap-2 text-red-800 mb-2">
          <AlertCircle className="h-5 w-5" />
          <span className="font-medium">Error Loading Permissions</span>
        </div>
        <p className="text-red-700 text-sm">{error}</p>
        <button 
          onClick={handleRefresh}
          className="mt-2 text-red-600 hover:text-red-800 text-sm underline"
        >
          Try again
        </button>
      </div>
    )
  }

  if (!permissions) {
    return (
      <div className="text-center py-8 text-slate-500">
        <Server className="h-12 w-12 mx-auto mb-3 opacity-50" />
        <p>No permission data available</p>
      </div>
    )
  }

  if (!permissions.hasAccess) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center gap-2 text-yellow-800 mb-2">
          <AlertCircle className="h-5 w-5" />
          <span className="font-medium">No Server Access</span>
        </div>
        <p className="text-yellow-700 text-sm">
          This user does not have access to {serverName || 'this server'}.
        </p>
      </div>
    )
  }

  const rolePermissions = permissions.permissions.filter(p => p.source === 'role')
  const individualPermissions = permissions.permissions.filter(p => p.source === 'individual')

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Shield className="h-6 w-6 text-blue-500" />
          <div>
            <h3 className="font-semibold text-lg">Permission Inheritance</h3>
            <p className="text-sm text-slate-600">
              {serverName || 'Remote Server'} • {permissions.permissions.length} total permissions
            </p>
          </div>
        </div>
        <button
          onClick={handleRefresh}
          className="flex items-center gap-2 px-3 py-1 text-sm text-slate-600 hover:text-slate-800 border border-slate-300 rounded-md hover:bg-slate-50"
        >
          <RefreshCw className="h-4 w-4" />
          Refresh
        </button>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Crown className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-800">Role-Based</span>
          </div>
          <div className="text-2xl font-bold text-blue-900">{rolePermissions.length}</div>
          <div className="text-sm text-blue-700">From {permissions.roles.length} roles</div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <User className="h-5 w-5 text-green-600" />
            <span className="font-medium text-green-800">Individual</span>
          </div>
          <div className="text-2xl font-bold text-green-900">{individualPermissions.length}</div>
          <div className="text-sm text-green-700">Direct assignments</div>
        </div>

        <div className="bg-slate-50 border border-slate-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <CheckCircle className="h-5 w-5 text-slate-600" />
            <span className="font-medium text-slate-800">Total</span>
          </div>
          <div className="text-2xl font-bold text-slate-900">{permissions.permissions.length}</div>
          <div className="text-sm text-slate-700">Effective permissions</div>
        </div>
      </div>

      {/* Role-Based Permissions */}
      {permissions.roles.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-medium text-lg flex items-center gap-2">
            <Crown className="h-5 w-5 text-blue-500" />
            Role-Based Permissions
          </h4>
          <div className="space-y-3">
            {permissions.roles.map(role => {
              const rolePerms = rolePermissions.filter(p => p.roleId === role.roleId)
              return (
                <div key={role.roleId} className="border border-blue-200 rounded-lg p-4 bg-blue-50">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <Crown className="h-4 w-4 text-blue-600" />
                      <span className="font-medium text-blue-900">{role.roleName}</span>
                    </div>
                    <div className="text-sm text-blue-700">
                      {rolePerms.length} permissions
                    </div>
                  </div>
                  
                  {rolePerms.length > 0 && (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {rolePerms.map((perm, idx) => (
                        <div 
                          key={idx}
                          className="flex items-center gap-2 text-sm bg-white rounded px-3 py-2 border border-blue-100"
                        >
                          <CheckCircle className="h-3 w-3 text-blue-500" />
                          <span className="font-medium">{perm.name}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>
      )}

      {/* Individual Permissions */}
      {individualPermissions.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-medium text-lg flex items-center gap-2">
            <User className="h-5 w-5 text-green-500" />
            Individual Permissions
          </h4>
          <div className="border border-green-200 rounded-lg p-4 bg-green-50">
            <div className="space-y-3">
              {individualPermissions.map((perm, idx) => (
                <div key={idx} className="flex items-center justify-between bg-white rounded px-3 py-2 border border-green-100">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="font-medium">{perm.name}</span>
                  </div>
                  <div className="text-xs text-slate-500">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatDate(perm.grantedAt)}
                    </div>
                    {perm.grantedBy && (
                      <div className="mt-1">by {perm.grantedBy}</div>
                    )}
                    {perm.expiresAt && (
                      <div className="mt-1 text-orange-600">
                        expires {formatDate(perm.expiresAt)}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {permissions.permissions.length === 0 && (
        <div className="text-center py-8 border-2 border-dashed border-slate-200 rounded-lg">
          <Shield className="h-12 w-12 mx-auto mb-3 text-slate-300" />
          <h5 className="font-medium text-slate-700 mb-2">No Permissions Assigned</h5>
          <p className="text-sm text-slate-500">
            This user has server access but no specific permissions assigned.
          </p>
        </div>
      )}
    </div>
  )
}