'use client';

import React, { ReactNode } from 'react';
import { usePermissions } from '@/components/providers/PermissionProvider';

export interface PermissionWrapperProps {
  children: ReactNode;
  permissions?: string[];
  allPermissions?: string[];
  anyPermission?: string[];
  serverId?: string;
  requireAuth?: boolean;
  fallback?: ReactNode;
  loadingComponent?: ReactNode;
}

export const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  children,
  permissions,
  allPermissions,
  anyPermission,
  serverId,
  requireAuth = true,
  fallback = null,
  loadingComponent = <div>Loading...</div>
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading, userId } = usePermissions();

  if (loading) {
    return <>{loadingComponent}</>;
  }

  // Check authentication requirement
  if (requireAuth && !userId) {
    return <>{fallback}</>;
  }

  // Helper function to convert string array to object array
  const convertPermissionsToObjects = (perms: string[]) => {
    return perms.map(perm => {
      const [resource, action] = perm.split('.');
      return { resource, action };
    });
  };

  // Check permissions
  let hasAccess = true;

  if (permissions && permissions.length > 0) {
    const permissionObjects = convertPermissionsToObjects(permissions);
    hasAccess = hasAnyPermission(permissionObjects, serverId);
  }

  if (allPermissions && allPermissions.length > 0) {
    const permissionObjects = convertPermissionsToObjects(allPermissions);
    hasAccess = hasAccess && hasAllPermissions(permissionObjects, serverId);
  }

  if (anyPermission && anyPermission.length > 0) {
    const permissionObjects = convertPermissionsToObjects(anyPermission);
    hasAccess = hasAnyPermission(permissionObjects, serverId);
  }

  // If no specific permissions required, show content (but still respect auth requirement)
  if (!permissions && !allPermissions && !anyPermission) {
    hasAccess = true;
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
};

export interface HasPermissionProps {
  children: ReactNode;
  permission: string;
  serverId?: string;
  fallback?: ReactNode;
  loadingComponent?: ReactNode;
}

export const HasPermission: React.FC<HasPermissionProps> = ({
  children,
  permission,
  serverId,
  fallback = null,
  loadingComponent
}) => {
  return (
    <PermissionWrapper
      permissions={[permission]}
      serverId={serverId}
      fallback={fallback}
      loadingComponent={loadingComponent}
    >
      {children}
    </PermissionWrapper>
  );
};

export interface HasAnyPermissionProps {
  children: ReactNode;
  permissions: string[];
  serverId?: string;
  fallback?: ReactNode;
  loadingComponent?: ReactNode;
}

export const HasAnyPermission: React.FC<HasAnyPermissionProps> = ({
  children,
  permissions,
  serverId,
  fallback = null,
  loadingComponent
}) => {
  return (
    <PermissionWrapper
      anyPermission={permissions}
      serverId={serverId}
      fallback={fallback}
      loadingComponent={loadingComponent}
    >
      {children}
    </PermissionWrapper>
  );
};

export interface HasAllPermissionsProps {
  children: ReactNode;
  permissions: string[];
  serverId?: string;
  fallback?: ReactNode;
  loadingComponent?: ReactNode;
}

export const HasAllPermissions: React.FC<HasAllPermissionsProps> = ({
  children,
  permissions,
  serverId,
  fallback = null,
  loadingComponent
}) => {
  return (
    <PermissionWrapper
      allPermissions={permissions}
      serverId={serverId}
      fallback={fallback}
      loadingComponent={loadingComponent}
    >
      {children}
    </PermissionWrapper>
  );
};

export interface AuthenticatedOnlyProps {
  children: ReactNode;
  fallback?: ReactNode;
  loadingComponent?: ReactNode;
}

export const AuthenticatedOnly: React.FC<AuthenticatedOnlyProps> = ({
  children,
  fallback = null,
  loadingComponent
}) => {
  return (
    <PermissionWrapper
      requireAuth={true}
      fallback={fallback}
      loadingComponent={loadingComponent}
    >
      {children}
    </PermissionWrapper>
  );
};

export interface PermissionGuardProps {
  children: ReactNode;
  permissions?: string[];
  allPermissions?: string[];
  anyPermission?: string[];
  serverId?: string;
  requireAuth?: boolean;
  fallback?: ReactNode;
  loadingComponent?: ReactNode;
  onAccessDenied?: () => void;
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permissions,
  allPermissions,
  anyPermission,
  serverId,
  requireAuth = true,
  fallback = null,
  loadingComponent,
  onAccessDenied
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading, userId } = usePermissions();

  if (loading) {
    return <>{loadingComponent}</>;
  }

  // Check authentication requirement
  if (requireAuth && !userId) {
    onAccessDenied?.();
    return <>{fallback}</>;
  }

  // Helper function to convert string array to object array
  const convertPermissionsToObjects = (perms: string[]) => {
    return perms.map(perm => {
      const [resource, action] = perm.split('.');
      return { resource, action };
    });
  };

  // Check permissions
  let hasAccess = true;

  if (permissions && permissions.length > 0) {
    const permissionObjects = convertPermissionsToObjects(permissions);
    hasAccess = hasAnyPermission(permissionObjects, serverId);
  }

  if (allPermissions && allPermissions.length > 0) {
    const permissionObjects = convertPermissionsToObjects(allPermissions);
    hasAccess = hasAccess && hasAllPermissions(permissionObjects, serverId);
  }

  if (anyPermission && anyPermission.length > 0) {
    const permissionObjects = convertPermissionsToObjects(anyPermission);
    hasAccess = hasAnyPermission(permissionObjects, serverId);
  }

  // If no specific permissions required, show content (but still respect auth requirement)
  if (!permissions && !allPermissions && !anyPermission) {
    hasAccess = true;
  }

  if (!hasAccess) {
    onAccessDenied?.();
    return <>{fallback}</>;
  }

  return <>{children}</>;
};