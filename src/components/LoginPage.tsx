"use client";

import React, { useEffect, useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Eye, EyeOff, Globe2 } from "lucide-react";
import { signIn } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";

type PublicStats = {
  totalUsers: number;
  totalActions: number;
  totalCountries: number;
};

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [resetLoading, setResetLoading] = useState(false);
  const [resetMessage, setResetMessage] = useState('');
  const [stats, setStats] = useState<PublicStats | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  // Add null check for searchParams
  const callbackUrl = searchParams ? searchParams.get('callbackUrl') || '/dashboard' : '/dashboard';

  useEffect(() => {
    let isMounted = true;

    const fetchStats = async () => {
      try {
        const response = await fetch('/api/public/stats', { cache: 'no-store' });
        if (!response.ok) {
          throw new Error('Failed to load statistics');
        }

        const data = await response.json();
        if (isMounted) {
          setStats({
            totalUsers: data.totalUsers ?? 0,
            totalActions: data.totalActions ?? 0,
            totalCountries: data.totalCountries ?? 0
          });
        }
      } catch (error) {
        if (isMounted) {
          setStats(null);
        }
      }
    };

    fetchStats();

    return () => {
      isMounted = false;
    };
  }, []);

  const formatStat = (value?: number) => {
    if (typeof value !== 'number' || Number.isNaN(value)) {
      return '—';
    }

    if (value >= 1_000_000) {
      return `${parseFloat((value / 1_000_000).toFixed(1)).toString()}M+`;
    }

    if (value >= 1_000) {
      return `${Math.round(value / 1_000)}K+`;
    }

    return value.toLocaleString();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const result = await signIn("credentials", {
        email,
        password,
        rememberMe,
        redirect: false,
      });

      if (result?.error) {
        setError('Invalid email or password. Please try again.');
      } else {
        // Use the callbackUrl parameter if present and valid, otherwise default to dashboard
        const safeCallbackUrl = callbackUrl && typeof callbackUrl === 'string' && callbackUrl.trim() !== '' ? callbackUrl : '/dashboard';

        // Check if this is an OAuth callback (contains /api/oauth/authorize)
        // If so, we need to redirect back to complete the OAuth flow
        if (safeCallbackUrl.includes('/api/oauth/authorize')) {
          // For OAuth flows, we need to do a full page redirect (not client-side)
          // to ensure the authorization code gets generated properly
          window.location.href = safeCallbackUrl;
        } else {
          // Regular dashboard redirect
          router.push(safeCallbackUrl);
        }
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setResetLoading(true);
    setResetMessage('');

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: resetEmail }),
      });

      const data = await response.json();

      if (response.ok) {
        setResetMessage('Password reset instructions have been sent to your email.');
      } else {
        setResetMessage(data.message || 'Failed to send reset instructions. Please try again.');
      }
    } catch (err) {
      setResetMessage('An unexpected error occurred. Please try again.');
    } finally {
      setResetLoading(false);
    }
  };


  return (
    <div className="min-h-screen flex w-full" role="main" aria-labelledby="welcome-heading">
      {/* Left Panel */}
      <div className="relative flex-1 overflow-hidden">
        <Image
          src="https://images.unsplash.com/photo-1539512110726-6d89c892f117?auto=format&fit=crop&w=1400&q=85"
          alt="Global community"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-slate-950/70 backdrop-blur-sm" />

        <div className="relative z-10 flex h-full flex-col justify-between p-12">
          <div className="flex items-center gap-4 text-white">
            <div className="rounded-2xl bg-white/20 p-3 backdrop-blur-sm">
              <Globe2 className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">New World Alliance</h1>
              <p className="text-emerald-100 text-sm">Member Portal</p>
            </div>
          </div>

          <div className="max-w-xl space-y-4 text-white">
            <h2 id="welcome-heading" className="text-4xl font-bold leading-tight whitespace-nowrap">
              Welcome to the <span className="text-emerald-300">Global Community</span>
            </h2>
            <p className="text-lg text-emerald-100/90">
              Connect with members worldwide and build a future centered on peace, love, and prosperity.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-6 text-white sm:grid-cols-3">
            {[
              { label: 'Users', value: stats?.totalUsers },
              { label: 'Actions', value: stats?.totalActions },
              { label: 'Countries', value: stats?.totalCountries }
            ].map((item) => (
              <div
                key={item.label}
                className="rounded-2xl bg-white/10 p-6 backdrop-blur-md"
              >
                <div className="text-3xl font-semibold">
                  {formatStat(item.value)}
                </div>
                <div className="text-sm text-emerald-100">{item.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Right Panel - Dark */}
        <div className="flex-1 bg-slate-900 flex items-center justify-center p-8 relative" aria-label="Member portal sign-in">
        {/* Background Elements */}
        <div className="absolute top-8 right-8">
          <div className="w-24 h-16 bg-slate-800 rounded-lg opacity-50"></div>
        </div>
        
        {/* Login Form */}
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <h3 id="login-heading" className="text-2xl font-bold text-white mb-2">Welcome back</h3>
            <p className="text-slate-400">Sign in to access your user dashboard</p>
          </div>

          {error && (
            <div
              className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg text-red-200 text-sm"
              data-testid="login-error"
            >
              {error}
            </div>
          )}

          {!showForgotPassword ? (
            <form
              onSubmit={handleSubmit}
              className="space-y-6"
              data-testid="login-form"
              aria-labelledby="login-heading"
            >
              <div className="space-y-2">
                <Label htmlFor="email" className="text-slate-300 text-sm">
                  Email address
                </Label>
                <div className="relative">
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  className="h-12 bg-white border-slate-300 text-slate-900 placeholder:text-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg"
                  placeholder="Enter your email"
                  required
                  data-testid="login-email"
                />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-slate-300 text-sm">
                  Password
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  className="h-12 bg-white border-slate-300 text-slate-900 placeholder:text-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg pr-10"
                  placeholder="Enter your password"
                  required
                  data-testid="login-password"
                />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-500 hover:text-slate-300"
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember"
                    name="remember"
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-slate-300 rounded"
                    data-testid="login-remember"
                  />
                  <Label htmlFor="remember" className="ml-2 text-sm text-slate-300">
                    Remember me
                  </Label>
                </div>
                <button
                  type="button"
                  onClick={() => setShowForgotPassword(true)}
                  className="text-sm text-emerald-300 hover:text-emerald-100 underline"
                >
                  Forgot password?
                </button>
              </div>

              <Button
                type="submit"
                disabled={loading}
                className="w-full h-12 bg-emerald-600 hover:bg-emerald-700 text-white font-semibold rounded-lg transition-colors disabled:opacity-50"
                data-testid="login-submit"
              >
                {loading ? 'Signing in...' : 'Sign in to Dashboard'}
              </Button>
            </form>
          ) : (
            <form
              onSubmit={handleForgotPassword}
              className="space-y-6"
              aria-labelledby="forgot-password-heading"
            >
              <div className="space-y-2">
                <Label id="forgot-password-heading" htmlFor="resetEmail" className="text-slate-300 text-sm">
                  Email address
                </Label>
                <Input
                  id="resetEmail"
                  type="email"
                  value={resetEmail}
                  onChange={(e) => setResetEmail(e.target.value)}
                  className="h-12 bg-white border-slate-300 text-slate-900 placeholder:text-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg"
                  placeholder="Enter your email to reset password"
                  required
                />
              </div>

              {resetMessage && (
                <div className={`p-3 rounded-lg text-sm ${
                  resetMessage.includes('sent')
                    ? 'bg-green-900/50 border border-green-700 text-green-200'
                    : 'bg-red-900/50 border border-red-700 text-red-200'
                }`}>
                  {resetMessage}
                </div>
              )}

              <div className="space-y-3">
                <Button
                  type="submit"
                  disabled={resetLoading}
                  className="w-full h-12 bg-emerald-600 hover:bg-emerald-700 text-white font-semibold rounded-lg transition-colors disabled:opacity-50"
                >
                  {resetLoading ? 'Sending...' : 'Send Reset Instructions'}
                </Button>

                <Button
                  type="button"
                  onClick={() => {
                    setShowForgotPassword(false);
                    setResetMessage('');
                    setResetEmail('');
                  }}
                  className="w-full h-12 bg-slate-700 hover:bg-slate-600 text-white font-semibold rounded-lg transition-colors"
                >
                  Back to Login
                </Button>
              </div>
            </form>
          )}


          <p className="text-xs text-slate-200 text-center mt-6 leading-relaxed">
            By signing in, you agree to our mission of peace, love, and prosperity for all humanity.
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
