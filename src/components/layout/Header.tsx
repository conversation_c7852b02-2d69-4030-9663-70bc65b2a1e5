'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Search
} from 'lucide-react';
import { signOut } from 'next-auth/react';
import { NotificationDropdown } from '@/components/notifications/NotificationDropdown';

interface HeaderProps {
}

export const Header: React.FC<HeaderProps> = () => {
  const handleLogout = async () => {
    await signOut({ callbackUrl: '/login' });
  };

  return (
    <header
      data-testid="header"
      className="bg-slate-800 text-white shadow-lg z-10 w-full"
    >
      {/* Skip navigation links */}
      <div className="sr-only focus-within:not-sr-only focus-within:absolute focus-within:top-0 focus-within:left-0 bg-slate-800 text-white p-2 z-50">
        <a href="#main-content" className="underline hover:no-underline">
          Skip to main content
        </a>
      </div>

      <div className="flex items-center justify-between p-4">
        {/* Left side - Dove logo with olive branch */}
        <div className="flex items-center">
          <img
            src="/images/logo.png"
            alt="NWA Peace Dove Logo"
            className="h-14 w-14"
          />
          <span className="ml-2 text-xl font-bold text-white">
            NWA Users Portal
          </span>
        </div>

        {/* Right side - Search and Notifications */}
        <div className="flex items-center space-x-4">
          {/* Search bar */}
          <div className="flex items-center bg-white rounded-lg px-3 py-2 w-64">
            <Search className="h-4 w-4 text-slate-800 mr-2" />
            <input
              type="text"
              placeholder="Search..."
              className="bg-transparent border-none focus:outline-none w-full text-sm text-slate-800 placeholder:text-slate-500"
            />
          </div>

          <NotificationDropdown />
        </div>
      </div>
    </header>
  );
};