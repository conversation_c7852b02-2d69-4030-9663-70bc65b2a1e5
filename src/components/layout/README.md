# Dashboard Layout Components

This directory contains the components for the NWA User Portal dashboard layout.

## Components

### DashboardLayout
The main layout component that provides the overall structure with header, sidebar, and main content area.

Usage:
```tsx
import { DashboardLayout } from '@/components/layout/DashboardLayout';

export default function MyPage() {
  return (
    <DashboardLayout>
      <div>Your page content here</div>
    </DashboardLayout>
  );
}
```

### Header
The top navigation bar with menu toggle, search, notifications, and user menu.

### Sidebar
The left navigation menu with collapsible behavior and navigation items.

### MainContent
The main content area with responsive padding and max-width container.

## Features

- Responsive design that works on mobile, tablet, and desktop
- Collapsible sidebar that reusers user preference in localStorage
- Dark mode support
- Accessible navigation with proper ARIA attributes
- Consistent styling with Tailwind CSS

## Customization

The components use Tailwind CSS for styling and can be easily customized by modifying the class names.