'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  LayoutDashboard,
  User,
  Users,
  FileText,
  BookOpen,
  Settings,
  Briefcase,
  LogOut,
  Menu,
  X,
  UserCircle,
  Globe,
  Bell
} from 'lucide-react';
import { signOut, useSession } from 'next-auth/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { usePermissions } from '@/lib/hooks/usePermissions';

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, onToggle }) => {
  const { data: session } = useSession();
  const pathname = usePathname();
  const { hasPermission, hasRole } = usePermissions();

  // Define navigation items with role-based visibility
  const allNavItems = [
    { icon: LayoutDashboard, label: 'Dashboard', href: '/dashboard', roles: [], permissions: [] },
    { icon: User, label: 'User Profile', href: '/profile', roles: [], permissions: [] },
    { icon: Bell, label: 'Notifications', href: '/notifications', roles: [], permissions: [] },
    { icon: Users, label: 'User Management', href: '/admin/manage/users', roles: ['admin', 'super_admin', 'SUPER_ADMIN'], permissions: ['user:write'] },
    { icon: Briefcase, label: 'Positions', href: '/positions', roles: ['admin', 'super_admin', 'SUPER_ADMIN'], permissions: ['roles:read'] },
    { icon: FileText, label: 'Documents', href: '/documents', roles: ['admin', 'super_admin', 'SUPER_ADMIN'], permissions: ['files:read'] },
    { icon: Globe, label: 'Treaties', href: '/treaty-management', roles: ['admin', 'super_admin', 'SUPER_ADMIN'], permissions: ['treaty:read'] },
    { icon: Settings, label: 'System Settings', href: '/settings', roles: ['admin', 'super_admin', 'SUPER_ADMIN'], permissions: ['admin:access'] },
  ];

  // Filter navigation items based on user role and permissions
  const navItems = allNavItems.filter(item => {
    // If no roles or permissions specified, item is visible to all authenticated users
    if (item.roles.length === 0 && item.permissions.length === 0) {
      return true;
    }

    // Check if user has any of the required roles
    const hasRequiredRole = item.roles.length === 0 || item.roles.some(role => hasRole(role));

    // Check if user has any of the required permissions
    const hasRequiredPermission = item.permissions.length === 0 || item.permissions.some(permission => hasPermission(permission));

    // User must have at least one required role OR one required permission
    return hasRequiredRole || hasRequiredPermission;
  });

  const handleLogout = async () => {
    await signOut({ callbackUrl: '/login' });
  };

  return (
    <aside
      data-testid="sidebar"
      className={`bg-emerald-600 text-white shadow-lg transition-all duration-300 ease-in-out
                 ${isCollapsed ? 'w-20' : 'w-64'}
                 flex flex-col`}
    >
      {/* Sidebar header with user info and toggle */}
      <div className={`flex items-center p-4 border-b border-emerald-700
                      ${isCollapsed ? 'justify-center' : 'justify-between'}`}>
        {!isCollapsed && session?.user?.name && (
          <div className="flex items-center">
            <UserCircle className="h-8 w-8 flex-shrink-0 text-white mr-2" />
            <span className="font-medium text-emerald-100 truncate max-w-[120px]">
              {session.user.name}
            </span>
          </div>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggle}
          className="text-white hover:bg-emerald-700 ml-auto"
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCollapsed ? <Menu className="h-5 w-5" /> : <X className="h-5 w-5" />}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 py-4">
        <ul className="space-y-1 px-2">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;
            return (
              <li key={item.label}>
                <Link
                  href={item.href}
                  className={`flex items-center p-3 rounded-lg transition-colors duration-200
                             ${isActive
                               ? 'bg-emerald-700 text-white shadow-md'
                               : 'text-emerald-100 hover:bg-emerald-700 hover:text-white'
                             }
                             ${isCollapsed ? 'justify-center' : ''}`}
                >
                  <Icon className="h-5 w-5 flex-shrink-0" />
                  {!isCollapsed && (
                    <span className="ml-3 font-medium">{item.label}</span>
                  )}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Sidebar footer with logout button */}
      <div className="p-4 border-t border-emerald-700">
        <button
          onClick={handleLogout}
          className={`flex items-center p-3 rounded-lg w-full text-left transition-colors duration-200
                     bg-emerald-700 text-white hover:bg-emerald-800
                     ${isCollapsed ? 'justify-center' : ''}`}
          data-testid="sidebar-logout"
        >
          <LogOut className="h-5 w-5 flex-shrink-0 text-red-300" />
          {!isCollapsed && (
            <span className="ml-3 font-medium">Logout</span>
          )}
        </button>
      </div>
    </aside>
  );
};
