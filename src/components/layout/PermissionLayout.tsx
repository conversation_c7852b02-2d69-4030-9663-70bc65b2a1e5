'use client';

import React, { ReactNode } from 'react';
import { PermissionProvider } from '@/lib/contexts/PermissionContext';
import { PermissionNavigation, adminNavigationItems, userNavigationItems } from '@/components/navigation/PermissionNavigation';
import { PermissionErrorHandler } from '@/components/auth/PermissionErrorBoundary';

interface PermissionLayoutProps {
  children: ReactNode;
  navigation?: 'admin' | 'user' | 'none';
  showSidebar?: boolean;
  sidebarClassName?: string;
}

export function PermissionLayout({
  children,
  navigation = 'admin',
  showSidebar = true,
  sidebarClassName = '',
}: PermissionLayoutProps) {
  const getNavigationItems = () => {
    switch (navigation) {
      case 'admin':
        return adminNavigationItems;
      case 'user':
        return userNavigationItems;
      default:
        return [];
    }
  };

  return (
    <PermissionProvider>
      <PermissionErrorHandler>
        <div className="flex h-screen bg-gray-100">
          {showSidebar && navigation !== 'none' && (
            <div className={`w-64 bg-white shadow-lg ${sidebarClassName}`}>
              <div className="p-4">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  {navigation === 'admin' ? 'Admin Panel' : 'Navigation'}
                </h2>
                <PermissionNavigation items={getNavigationItems()} />
              </div>
            </div>
          )}

          <div className="flex-1 flex flex-col overflow-hidden">
            <main className="flex-1 overflow-auto">
              {children}
            </main>
          </div>
        </div>
      </PermissionErrorHandler>
    </PermissionProvider>
  );
}

// Specialized layout components
export function AdminLayout({ children }: { children: ReactNode }) {
  return (
    <PermissionLayout navigation="admin" showSidebar={true}>
      {children}
    </PermissionLayout>
  );
}

export function UserLayout({ children }: { children: ReactNode }) {
  return (
    <PermissionLayout navigation="user" showSidebar={true}>
      {children}
    </PermissionLayout>
  );
}

export function SimpleLayout({ children }: { children: ReactNode }) {
  return (
    <PermissionLayout navigation="none" showSidebar={false}>
      {children}
    </PermissionLayout>
  );
}