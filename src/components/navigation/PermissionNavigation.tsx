'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { usePermissions } from '@/lib/contexts/PermissionContext';

interface NavigationItem {
  name: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  permissions?: Array<{ resource: string; action: string }>;
  roles?: string[];
  adminOnly?: boolean;
  superAdminOnly?: boolean;
  children?: NavigationItem[];
}

interface PermissionNavigationProps {
  items: NavigationItem[];
  className?: string;
}

export function PermissionNavigation({ items, className = '' }: PermissionNavigationProps) {
  const { hasAnyPermission, hasRole, isAdmin, isSuperAdmin } = usePermissions();
  const pathname = usePathname();

  const canAccessItem = (item: NavigationItem): boolean => {
    // Check admin/super admin requirements first
    if (item.superAdminOnly && !isSuperAdmin) {
      return false;
    }

    if (item.adminOnly && !isAdmin) {
      return false;
    }

    // Check role requirements
    if (item.roles && item.roles.length > 0) {
      const hasRequiredRole = item.roles.some(role => hasRole(role));
      if (!hasRequiredRole) {
        return false;
      }
    }

    // Check permission requirements
    if (item.permissions && item.permissions.length > 0) {
      const hasRequiredPermissions = hasAnyPermission(item.permissions);
      if (!hasRequiredPermissions) {
        return false;
      }
    }

    return true;
  };

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    if (!canAccessItem(item)) {
      return null;
    }

    const isActive = pathname && (pathname === item.href || pathname.startsWith(item.href + '/'));
    const Icon = item.icon;

    return (
      <div key={item.name}>
        <Link
          href={item.href}
          className={`
            flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
            ${isActive
              ? 'bg-primary-100 text-primary-900'
              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
            }
            ${level > 0 ? 'ml-6' : ''}
          `}
        >
          {Icon && <Icon className="mr-3 h-5 w-5" />}
          {item.name}
        </Link>

        {item.children && (
          <div className="mt-1 space-y-1">
            {item.children.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <nav className={`space-y-1 ${className}`}>
      {items.map(item => renderNavigationItem(item))}
    </nav>
  );
}

// Predefined navigation items for common use cases
export const adminNavigationItems: NavigationItem[] = [
  {
    name: 'Dashboard',
    href: '/admin',
    permissions: [{ resource: 'ADMIN_PANEL', action: 'ACCESS' }],
  },
  {
    name: 'User Management',
    href: '/admin/users',
    permissions: [{ resource: 'USER_MANAGEMENT', action: 'READ' }],
    children: [
      {
        name: 'All Users',
        href: '/admin/users',
        permissions: [{ resource: 'USER_MANAGEMENT', action: 'READ' }],
      },
      {
        name: 'Create User',
        href: '/admin/users/create',
        permissions: [{ resource: 'USER_MANAGEMENT', action: 'CREATE' }],
      },
      {
        name: 'Bulk Operations',
        href: '/admin/users/bulk',
        permissions: [{ resource: 'USER_MANAGEMENT', action: 'UPDATE' }],
      },
    ],
  },
  {
    name: 'Security',
    href: '/admin/security',
    permissions: [{ resource: 'SECURITY_MANAGEMENT', action: 'READ' }],
    adminOnly: true,
  },
  {
    name: 'System Settings',
    href: '/admin/settings',
    permissions: [{ resource: 'SYSTEM_SETTINGS', action: 'READ' }],
    adminOnly: true,
  },
];

export const userNavigationItems: NavigationItem[] = [
  {
    name: 'Profile',
    href: '/profile',
  },
  {
    name: 'Notifications',
    href: '/notifications',
    permissions: [{ resource: 'NOTIFICATIONS', action: 'READ' }],
  },
  {
    name: 'Security Settings',
    href: '/profile/security',
    permissions: [{ resource: 'USER_PROFILE', action: 'UPDATE' }],
  },
];