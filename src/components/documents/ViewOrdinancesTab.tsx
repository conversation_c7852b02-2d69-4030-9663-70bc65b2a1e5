'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { OrdinanceList } from '@/components/ordinances/OrdinanceList';
import { OrdinanceSearch } from '@/components/ordinances/OrdinanceSearch';
import { Search } from 'lucide-react';

export const ViewOrdinancesTab: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [ordinances, setOrdinances] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 10
  });

  const fetchOrdinances = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/documents/ordinances?search=${encodeURIComponent(searchQuery)}&page=${pagination.currentPage}&limit=${pagination.pageSize}`
      );
      
      if (!response.ok) {
        throw new Error('Failed to fetch ordinances');
      }
      
      const data = await response.json();
      setOrdinances(data.ordinances);
      setPagination(prev => ({
        ...prev,
        totalPages: data.totalPages,
        totalCount: data.totalCount
      }));
    } catch (error) {
      console.error('Error fetching ordinances:', error);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, pagination.currentPage, pagination.pageSize]);

  // Fetch ordinances when component mounts or when search/pagination changes
  useEffect(() => {
    fetchOrdinances();
  }, [fetchOrdinances]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page on new search
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center">
          <Search className="h-4 w-4 mr-2" />
          View Ordinances
        </h3>
        <p className="text-xs text-blue-600 dark:text-blue-300">
          Search and view all ordinances in the system. You can filter by name or description.
        </p>
      </div>

      <div>
        <OrdinanceSearch onSearch={handleSearch} />
        {loading ? (
          <div className="animate-pulse space-y-4 mt-6">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        ) : (
          <OrdinanceList 
            ordinances={ordinances} 
            showPagination={true}
            pagination={pagination}
            onPageChange={handlePageChange}
            onViewDetails={(id) => console.log('View ordinance:', id)}
            onDelete={() => fetchOrdinances()}
          />
        )}
      </div>
    </div>
  );
};
