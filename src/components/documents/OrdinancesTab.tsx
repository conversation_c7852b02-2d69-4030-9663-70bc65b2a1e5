'use client';

import React, { useState } from 'react';
import { CreateOrdinanceTab } from './CreateOrdinanceTab';
import { ViewOrdinancesTab } from './ViewOrdinancesTab';
import { FilePlus, Eye } from 'lucide-react';

export const OrdinancesTab: React.FC = () => {
  const [activeSubTab, setActiveSubTab] = useState<'create' | 'view'>('create');

  return (
    <div className="space-y-6">
      {/* Horizontal Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveSubTab('create')}
            className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm gap-2 transition-colors ${
              activeSubTab === 'create'
                ? 'border-emerald-600 text-emerald-700 dark:text-emerald-300 dark:border-emerald-400'
                : 'border-transparent text-emerald-600 hover:text-emerald-700 hover:border-emerald-400 dark:text-emerald-400 dark:hover:text-emerald-300'
            }`}
          >
            <FilePlus className="h-4 w-4" />
            <span>Create Ordinance</span>
          </button>
          <button
            onClick={() => setActiveSubTab('view')}
            className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm gap-2 transition-colors ${
              activeSubTab === 'view'
                ? 'border-emerald-600 text-emerald-700 dark:text-emerald-300 dark:border-emerald-400'
                : 'border-transparent text-emerald-600 hover:text-emerald-700 hover:border-emerald-400 dark:text-emerald-400 dark:hover:text-emerald-300'
            }`}
          >
            <Eye className="h-4 w-4" />
            <span>View Ordinances</span>
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeSubTab === 'create' ? (
          <CreateOrdinanceTab />
        ) : (
          <ViewOrdinancesTab />
        )}
      </div>
    </div>
  );
};
