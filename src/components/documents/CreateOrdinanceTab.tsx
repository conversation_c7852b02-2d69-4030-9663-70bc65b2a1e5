'use client';

import React, { useState, useRef } from 'react';
import { toast } from 'sonner';
import { Upload, X, FileText, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface FileUpload {
  file: File;
  id: string;
}

export const CreateOrdinanceTab: React.FC = () => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [files, setFiles] = useState<FileUpload[]>([]);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    
    selectedFiles.forEach((selectedFile) => {
      // Check file type - allow PDF, DOC, and DOCX files
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];
      if (!allowedTypes.includes(selectedFile.type)) {
        toast.error(`Invalid file type for ${selectedFile.name}. Please upload PDF, DOC, or DOCX files only.`);
        return;
      }
      
      // Check file size (5MB limit)
      if (selectedFile.size > 5 * 1024 * 1024) {
        toast.error(`File ${selectedFile.name} exceeds 5MB limit.`);
        return;
      }
      
      // Add file to the list
      const fileUpload: FileUpload = {
        file: selectedFile,
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      };
      
      setFiles(prev => [...prev, fileUpload]);
    });

    // Reset the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      toast.error('Please enter an ordinance name.');
      return;
    }
    
    if (!description.trim()) {
      toast.error('Please enter a description.');
      return;
    }
    
    if (files.length === 0) {
      toast.error('Please upload at least one document.');
      return;
    }
    
    setUploading(true);
    
    try {
      // Create FormData for the ordinance
      const formData = new FormData();
      formData.append('name', name.trim());
      formData.append('description', description.trim());
      formData.append('type', 'ordinance');
      
      // Append all files
      files.forEach((fileUpload, index) => {
        formData.append(`documents`, fileUpload.file);
      });
      
      const response = await fetch('/api/documents/ordinances', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = 'Failed to create ordinance';
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          errorMessage = errorText || errorMessage;
        }
        toast.error(errorMessage);
        return;
      }
      
      // Success
      toast.success('Ordinance created successfully!');
      
      // Reset form
      setName('');
      setDescription('');
      setFiles([]);
      
    } catch (error: any) {
      console.error('Error creating ordinance:', error);
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Create New Ordinance</h3>
        <p className="text-xs text-blue-600 dark:text-blue-300">
          Fill in the ordinance details and upload the supporting documents. You can upload multiple files at once.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Ordinance Name */}
        <div className="space-y-2">
          <Label htmlFor="ordinance-name" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Ordinance Name *
          </Label>
          <Input
            id="ordinance-name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Enter ordinance name"
            className="w-full"
            required
          />
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label htmlFor="ordinance-description" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Description *
          </Label>
          <Textarea
            id="ordinance-description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Enter a detailed description of the ordinance"
            rows={4}
            className="w-full"
            required
          />
        </div>

        {/* File Upload */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Documents * (PDF, DOC, DOCX - Max 5MB each)
          </Label>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                multiple
                className="hidden"
              />
              <Button
                type="button"
                onClick={triggerFileInput}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Documents
              </Button>
            </div>

            {/* File List */}
            {files.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Selected Files ({files.length}):
                </p>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {files.map((fileUpload) => (
                    <div
                      key={fileUpload.id}
                      className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
                    >
                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        <FileText className="h-4 w-4 text-slate-500 flex-shrink-0" />
                        <span className="text-sm text-gray-700 dark:text-gray-300 truncate">
                          {fileUpload.file.name}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0">
                          ({(fileUpload.file.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      <Button
                        type="button"
                        onClick={() => removeFile(fileUpload.id)}
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            type="submit"
            disabled={uploading || !name.trim() || !description.trim() || files.length === 0}
            className="flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700 focus:ring-emerald-500 text-white"
          >
            <Upload className="h-4 w-4" />
            {uploading ? 'Creating Ordinance...' : 'Create Ordinance'}
          </Button>
        </div>
      </form>
    </div>
  );
};
