'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Search, ScrollText } from 'lucide-react';

export const ViewDirectivesTab: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [directives, setDirectives] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 10
  });

  const fetchDirectives = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/documents/directives?search=${encodeURIComponent(searchQuery)}&page=${pagination.currentPage}&limit=${pagination.pageSize}`
      );
      
      if (!response.ok) {
        throw new Error('Failed to fetch directives');
      }
      
      const data = await response.json();
      setDirectives(data.directives || []);
      setPagination(prev => ({
        ...prev,
        totalPages: data.totalPages || 1,
        totalCount: data.totalCount || 0
      }));
    } catch (error) {
      console.error('Error fetching directives:', error);
      // For now, set empty array if API doesn't exist yet
      setDirectives([]);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, pagination.currentPage, pagination.pageSize]);

  // Fetch directives when component mounts or when search/pagination changes
  useEffect(() => {
    fetchDirectives();
  }, [fetchDirectives]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page on new search
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center">
          <ScrollText className="h-4 w-4 mr-2" />
          View Directives
        </h3>
        <p className="text-xs text-blue-600 dark:text-blue-300">
          Search and view all directives in the system. You can filter by name or description.
        </p>
      </div>

      {/* Search Input */}
      <div className="flex gap-4 items-center">
        <div className="flex-1 relative">
          <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search directives..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-slate-500 focus:border-slate-500"
          />
        </div>
      </div>

      <div>
        {loading ? (
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        ) : directives.length === 0 ? (
          <div className="text-center py-8">
            <ScrollText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              No Directives Found
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {searchQuery 
                ? `No directives match your search "${searchQuery}"`
                : 'No directives have been created yet. Use the "Create Directive" tab to add your first directive.'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {directives.map((directive: any) => (
              <div
                key={directive.id}
                className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                      {directive.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {directive.description}
                    </p>
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <span>Created: {new Date(directive.createdAt).toLocaleDateString()}</span>
                      {directive.documents && directive.documents.length > 0 && (
                        <span className="ml-4">
                          {directive.documents.length} document{directive.documents.length !== 1 ? 's' : ''}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2 ml-4">
                    <button
                      onClick={() => console.log('View directive:', directive.id)}
                      className="px-3 py-1 text-xs bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-200 rounded hover:bg-slate-200 dark:hover:bg-slate-800 transition-colors"
                    >
                      View
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Pagination - if needed in the future */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center space-x-2 mt-6">
          {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-3 py-1 rounded text-sm ${
                page === pagination.currentPage
                  ? 'bg-slate-700 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {page}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
