import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { globalSecurityMiddleware, isPublicEndpoint } from '@/lib/middleware/global-security';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';
import { require2FAMiddleware } from '@/lib/middleware/require-2fa';

/**
 * Global middleware that runs on every request
 */
export async function middleware(request: NextRequest) {
  const { pathname } = new URL(request.url);
  
  // Apply global security (headers, rate limiting, etc.)
  const securityResponse = await globalSecurityMiddleware(request);
  
  // If security middleware returned a response (like rate limit), return it
  if (securityResponse.status !== 200) {
    return securityResponse;
  }
  
  // Skip authentication for public endpoints
  if (isPublicEndpoint(pathname)) {
    return securityResponse;
  }

  // Apply 2FA enforcement
  const twoFAResponse = await require2FAMiddleware(request);

  // If 2FA middleware returned a redirect, merge security headers and return it
  if (twoFAResponse.status === 307 || twoFAResponse.status === 308) {
    securityResponse.headers.forEach((value, key) => {
      twoFAResponse.headers.set(key, value);
    });
    return twoFAResponse;
  }

  // Merge security headers into the continued response
  securityResponse.headers.forEach((value, key) => {
    twoFAResponse.headers.set(key, value);
  });

  return twoFAResponse;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\.(?:svg|png|jpg|jpeg|gif|webp|ico|txt|xml|css|js)$).*)',
  ],
  runtime: 'nodejs', // Force Node.js runtime to avoid Edge Runtime issues
};
