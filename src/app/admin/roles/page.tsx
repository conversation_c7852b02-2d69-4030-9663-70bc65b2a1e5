import React from 'react';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { redirect } from 'next/navigation';
import AdminRolesClient from './AdminRolesClient';

async function checkAdminAccess() {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect('/login');
  }

  // Check if user has admin role
  const user = await prisma.user.findUnique({
    where: { id: (session.user as any).id },
    include: { userRoles: { include: { role: true } } }
  });

  const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');

  if (!isAdmin) {
    redirect('/dashboard');
  }

  return { session, user };
}

export default async function AdminRolesPage() {
  await checkAdminAccess();
  return <AdminRolesClient />;
}