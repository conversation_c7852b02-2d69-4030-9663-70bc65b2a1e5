'use client';

import React from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { VerticalNav } from '@/components/admin/VerticalNav';
import SecurityDashboard from '@/components/security/SecurityDashboard';
import { AdminOnly } from '@/components/auth/ProtectedPage';

export default function SecurityPage() {
  return (
    <AdminOnly>
      <DashboardLayout>
        <div className="flex h-full">
          {/* Admin Vertical Navigation */}
          <div className="w-64 bg-slate-800 text-white shadow-lg flex-shrink-0 rounded-lg">
            <VerticalNav />
          </div>

          {/* Security Dashboard Content */}
          <div className="flex-1 p-6 bg-gray-50 dark:bg-gray-900">
            <SecurityDashboard />
          </div>
        </div>
      </DashboardLayout>
    </AdminOnly>
  );
}