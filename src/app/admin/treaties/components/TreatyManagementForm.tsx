'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { 
  FileText, 
  Plus, 
  AlertCircle, 
  CheckCircle, 
  DollarSign, 
  Upload,
  X,
  User,
  Mail,
  Phone,
  MapPin,
  Building,
  Globe,
  Save
} from 'lucide-react';

interface TreatyType {
  id: string;
  name: string;
  description: string | null;
  category: string;
  price: number;
  currency: string;
  requiresPayment: boolean;
  isActive: boolean;
}

interface Category {
  id: number;
  name: string;
  description: string | null;
}

interface Subcategory {
  id: number;
  name: string;
  description: string | null;
  categoryId: number;
}

interface Country {
  id: number;
  name: string;
}

interface City {
  id: number;
  name: string;
  countryId: number;
}

interface BusinessDetails {
  businessName: string;
  businessAddress: string;
  businessEmail: string;
  businessPhone: string;
  businessWebsite: string;
  businessDescription: string;
  categoryId: string;
  subcategoryId: string;
  logoFile: File | null;
}

interface TreatyTypeEntry {
  treatyTypeId: string;
  treatyType: TreatyType;
  businessDetails: BusinessDetails;
  completed: boolean;
}

interface TreatyFormData {
  treatyId: string;
  treatyName: string;
  treatyTypes: TreatyTypeEntry[];
  currentCountryId: string;
  currentCityId: string;
  residenceCountryId: string;
  residenceCityId: string;
  residentialAddress: string;
  phoneNumbers: string[];
  email: string;
  attachments: File[];
}

interface TreatyManagementFormProps {
  userId?: string;
  initialData?: TreatyFormData;
  onTreatyCreated?: () => void;
  onSave?: (data: TreatyFormData) => void;
  mode?: 'create' | 'edit';
}

export const TreatyManagementForm: React.FC<TreatyManagementFormProps> = ({
  userId,
  initialData,
  onTreatyCreated,
  onSave,
  mode = 'create'
}) => {
  const [step, setStep] = useState(1);
  const [treatyTypes, setTreatyTypes] = useState<TreatyType[]>([]);
  const [availableTreaties, setAvailableTreaties] = useState<Array<{id: string, name: string, description: string}>>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [countries, setCountries] = useState<Country[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentTreatyTypeIndex, setCurrentTreatyTypeIndex] = useState(0);
  const [userProfile, setUserProfile] = useState<{
    firstName: string;
    lastName: string;
    email: string;
  }>({
    firstName: '',
    lastName: '',
    email: ''
  });

  const [formData, setFormData] = useState<TreatyFormData>(
    initialData || {
      treatyId: '',
      treatyName: '',
      treatyTypes: [],
      currentCountryId: '',
      currentCityId: '',
      residenceCountryId: '',
      residenceCityId: '',
      residentialAddress: '',
      phoneNumbers: [''],
      email: '',
      attachments: []
    }
  );

  const fetchInitialData = async () => {
    await Promise.all([
      fetchTreatyTypes(),
      fetchAvailableTreaties(),
      fetchCategories(),
      fetchCountries(),
      userId && fetchUserProfile(userId)
    ]);
  };

  useEffect(() => {
    fetchInitialData();
  }, [userId]);

  useEffect(() => {
    const currentTreatyType = formData.treatyTypes[currentTreatyTypeIndex];
    if (currentTreatyType?.businessDetails.categoryId) {
      fetchSubcategories(currentTreatyType.businessDetails.categoryId);
    }
  }, [currentTreatyTypeIndex, formData.treatyTypes]);

  useEffect(() => {
    if (formData.currentCountryId) {
      fetchCities(formData.currentCountryId, 'current');
    }
  }, [formData.currentCountryId]);

  useEffect(() => {
    if (formData.residenceCountryId) {
      fetchCities(formData.residenceCountryId, 'residence');
    }
  }, [formData.residenceCountryId]);

  const fetchUserProfile = async (userId: string) => {
    try {
      const response = await fetch(`/api/users/${userId}/profile`);
      if (response.ok) {
        const profile = await response.json();
        setUserProfile({
          firstName: profile.firstName || '',
          lastName: profile.lastName || '',
          email: profile.email || ''
        });
        setFormData(prev => ({ ...prev, email: profile.email || '' }));
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
    }
  };

  const checkUsershipStatus = () => {
    return true;
  };

  const fetchTreatyTypes = async () => {
    try {
      const response = await fetch('/api/treaty-types');
      if (response.ok) {
        const data = await response.json();
        setTreatyTypes(data.treatyTypes || []);
      }
    } catch (error) {
      console.error('Error fetching treaty types:', error);
      toast.error('Failed to load treaty types');
    }
  };

  const fetchAvailableTreaties = async () => {
    try {
      const response = await fetch('/api/treaties');
      if (response.ok) {
        const data = await response.json();
        const treaties = data.treaties || [];
        const uniqueTreaties = treaties.reduce((acc: Array<{id: string, name: string, description: string}>, treaty: any) => {
          const existingTreaty = acc.find(t => t.name === treaty.name);
          if (!existingTreaty && treaty.name) {
            acc.push({
              id: treaty.id,
              name: treaty.name,
              description: treaty.description || ''
            });
          }
          return acc;
        }, []);
        setAvailableTreaties(uniqueTreaties);
      }
    } catch (error) {
      console.error('Error fetching available treaties:', error);
      toast.error('Failed to load available treaties');
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast.error('Failed to load business categories');
    }
  };

  const fetchSubcategories = async (categoryId: string) => {
    try {
      const response = await fetch(`/api/categories/${categoryId}/subcategories`);
      if (response.ok) {
        const data = await response.json();
        setSubcategories(data.subcategories || []);
      }
    } catch (error) {
      console.error('Error fetching subcategories:', error);
      toast.error('Failed to load subcategories');
    }
  };

  const fetchCountries = async () => {
    try {
      const response = await fetch('/api/countries');
      if (response.ok) {
        const data = await response.json();
        setCountries(data || []);
      }
    } catch (error) {
      console.error('Error fetching countries:', error);
      toast.error('Failed to load countries');
    }
  };

  const fetchCities = async (countryId: string, type: 'current' | 'residence') => {
    try {
      const response = await fetch(`/api/countries/${countryId}/cities`);
      if (response.ok) {
        const data = await response.json();
        setCities(data || []);
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
      toast.error('Failed to load cities');
    }
  };

  const addTreatyType = (treatyTypeId: string) => {
    const treatyType = treatyTypes.find(t => t.id === treatyTypeId);
    if (!treatyType) return;

    const exists = formData.treatyTypes.some(tt => tt.treatyTypeId === treatyTypeId);
    if (exists) {
      toast.error('This treaty type has already been added');
      return;
    }

    const newTreatyTypeEntry: TreatyTypeEntry = {
      treatyTypeId,
      treatyType,
      businessDetails: {
        businessName: '',
        businessAddress: '',
        businessEmail: '',
        businessPhone: '',
        businessWebsite: '',
        businessDescription: '',
        categoryId: '',
        subcategoryId: '',
        logoFile: null
      },
      completed: false
    };

    setFormData(prev => ({
      ...prev,
      treatyTypes: [...prev.treatyTypes, newTreatyTypeEntry]
    }));

    setCurrentTreatyTypeIndex(formData.treatyTypes.length);
    toast.success(`${treatyType.name} added successfully`);
  };

  const removeTreatyType = (index: number) => {
    setFormData(prev => ({
      ...prev,
      treatyTypes: prev.treatyTypes.filter((_, i) => i !== index)
    }));

    if (currentTreatyTypeIndex >= formData.treatyTypes.length - 1) {
      setCurrentTreatyTypeIndex(Math.max(0, formData.treatyTypes.length - 2));
    }
  };

  const handleBusinessDetailsChange = (field: keyof BusinessDetails, value: string | File | null) => {
    if (currentTreatyTypeIndex < 0 || currentTreatyTypeIndex >= formData.treatyTypes.length) return;

    setFormData(prev => ({
      ...prev,
      treatyTypes: prev.treatyTypes.map((tt, index) => 
        index === currentTreatyTypeIndex 
          ? {
              ...tt,
              businessDetails: {
                ...tt.businessDetails,
                [field]: value
              }
            }
          : tt
      )
    }));
  };

  const addPhoneNumber = () => {
    setFormData(prev => ({
      ...prev,
      phoneNumbers: [...prev.phoneNumbers, '']
    }));
  };

  const removePhoneNumber = (index: number) => {
    setFormData(prev => ({
      ...prev,
      phoneNumbers: prev.phoneNumbers.filter((_, i) => i !== index)
    }));
  };

  const updatePhoneNumber = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      phoneNumbers: prev.phoneNumbers.map((phone, i) => i === index ? value : phone)
    }));
  };

  const handleFileUpload = (files: FileList | null, type: 'logo' | 'attachments') => {
    if (!files) return;

    if (type === 'logo' && files[0]) {
      handleBusinessDetailsChange('logoFile', files[0]);
    } else if (type === 'attachments') {
      setFormData(prev => ({
        ...prev,
        attachments: [...prev.attachments, ...Array.from(files)]
      }));
    }
  };

  const removeAttachment = (index: number) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  const validateStep = (stepNumber: number): boolean => {
    switch (stepNumber) {
      case 1:
        if (!checkUsershipStatus()) return false;
        return !!(formData.treatyId && formData.treatyTypes.length > 0);
      case 2:
        const currentTreatyType = formData.treatyTypes[currentTreatyTypeIndex];
        if (!currentTreatyType) return false;
        const bd = currentTreatyType.businessDetails;
        return !!(bd.businessName && bd.businessAddress && bd.businessEmail && bd.categoryId && bd.subcategoryId);
      case 3:
        return !!(formData.currentCountryId && formData.currentCityId && formData.email);
      case 4:
        return formData.treatyTypes.every(tt => tt.completed);
      default:
        return true;
    }
  };

  const markCurrentTreatyTypeComplete = () => {
    if (currentTreatyTypeIndex < 0 || currentTreatyTypeIndex >= formData.treatyTypes.length) return;

    const currentTreatyType = formData.treatyTypes[currentTreatyTypeIndex];
    const bd = currentTreatyType.businessDetails;
    
    if (bd.businessName && bd.businessAddress && bd.businessEmail && bd.categoryId && bd.subcategoryId) {
      setFormData(prev => ({
        ...prev,
        treatyTypes: prev.treatyTypes.map((tt, index) => 
          index === currentTreatyTypeIndex 
            ? { ...tt, completed: true }
            : tt
        )
      }));
      toast.success(`${currentTreatyType.treatyType.name} business details completed`);
      return true;
    } else {
      toast.error('Please complete all required business details');
      return false;
    }
  };

  const nextStep = () => {
    if (validateStep(step)) {
      setStep(prev => prev + 1);
    } else {
      toast.error('Please fill in all required fields before proceeding');
    }
  };

  const prevStep = () => {
    setStep(prev => prev - 1);
  };

  const handleSave = () => {
    if (onSave) {
      onSave(formData);
      toast.success('Treaty data saved successfully');
    }
  };

  const handleSubmit = async () => {
    if (!userId) {
      toast.error('User ID is required');
      return;
    }

    if (!validateStep(4)) {
      toast.error('Please complete all required information');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/users/${userId}/treaties`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          treatyName: formData.treatyName,
          treatyTypes: formData.treatyTypes.map(tt => ({
            treatyTypeId: tt.treatyTypeId,
            businessDetails: tt.businessDetails
          })),
          email: formData.email,
          currentCountryId: formData.currentCountryId,
          currentCityId: formData.currentCityId,
          residenceCountryId: formData.residenceCountryId,
          residenceCityId: formData.residenceCityId,
          residentialAddress: formData.residentialAddress,
          phoneNumbers: formData.phoneNumbers.filter(p => p.trim())
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create treaty');
      }

      const result = await response.json();
      toast.success('Treaty created successfully!');
      
      if (onTreatyCreated) {
        onTreatyCreated();
      }
    } catch (error) {
      console.error('Error creating treaty:', error);
      toast.error('Failed to create treaty');
    } finally {
      setLoading(false);
    }
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4 flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          Treaty & Treaty Types Selection
        </h3>
        
        {/* Process Overview */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800 mb-6">
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
            📝 Treaty Creation Process
          </h4>
          <ol className="text-xs text-blue-700 dark:text-blue-300 space-y-1 list-decimal list-inside">
            <li><strong>Verify Usership:</strong> Ensure you have a Trade Treaty Number</li>
            <li><strong>Select Treaty:</strong> Choose an existing treaty to join</li>
            <li><strong>Select Treaty Types:</strong> Choose one or more treaty types based on your business needs</li>
            <li><strong>Complete Business Details:</strong> Fill out business information for each treaty type <Upload className="inline h-3 w-3 ml-1" /></li>
            <li><strong>Provide Contact Info:</strong> Add your location and contact details</li>
            <li><strong>Review & Submit:</strong> Confirm all information and submit your application</li>
          </ol>
        </div>
        

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Globe className="inline h-4 w-4 mr-1" />
            Select Treaty <span className="text-red-500">*</span>
          </label>
          <select
            value={formData.treatyId}
            onChange={(e) => {
              const selectedTreaty = availableTreaties.find(t => t.id === e.target.value);
              setFormData(prev => ({ 
                ...prev, 
                treatyId: e.target.value,
                treatyName: selectedTreaty?.name || ''
              }));
            }}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="">Select a treaty to join</option>
            {availableTreaties.map(treaty => (
              <option key={treaty.id} value={treaty.id}>
                {treaty.name}
              </option>
            ))}
          </select>
          {formData.treatyId && (
            <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                Selected Treaty: {formData.treatyName}
              </h4>
              <p className="text-xs text-blue-700 dark:text-blue-300">
                {availableTreaties.find(t => t.id === formData.treatyId)?.description || 'No description available'}
              </p>
            </div>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Plus className="inline h-4 w-4 mr-1" />
            Add Treaty Types <span className="text-red-500">*</span>
          </label>
          <div className="flex space-x-2">
            <select
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              onChange={(e) => {
                if (e.target.value) {
                  addTreatyType(e.target.value);
                  e.target.value = '';
                }
              }}
            >
              <option value="">Select a treaty type to add</option>
              {treatyTypes
                .filter(type => !formData.treatyTypes.some(tt => tt.treatyTypeId === type.id))
                .map(type => (
                  <option key={type.id} value={type.id}>
                    {type.name} - {type.price > 0 ? `$${type.price} ${type.currency}` : 'Free'}
                  </option>
                ))}
            </select>
          </div>
        </div>

        {/* Selected Treaty Types */}
        {formData.treatyTypes.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
              Selected Treaty Types ({formData.treatyTypes.length})
            </h4>
            <div className="space-y-3">
              {formData.treatyTypes.map((treatyTypeEntry, index) => (
                <div key={treatyTypeEntry.treatyTypeId} className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        {treatyTypeEntry.completed ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-yellow-500" />
                        )}
                        <div>
                          <h5 className="font-medium text-slate-800 dark:text-slate-200">
                            {treatyTypeEntry.treatyType.name}
                          </h5>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {treatyTypeEntry.treatyType.description}
                          </p>
                          <div className="flex items-center space-x-4 mt-1">
                            <div className="flex items-center">
                              <DollarSign className="h-3 w-3 text-green-500 mr-1" />
                              <span className="text-xs font-medium">
                                {treatyTypeEntry.treatyType.price > 0 
                                  ? `$${treatyTypeEntry.treatyType.price} ${treatyTypeEntry.treatyType.currency}`
                                  : 'Free'
                                }
                              </span>
                            </div>
                            <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">
                              {treatyTypeEntry.treatyType.category}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        type="button"
                        onClick={() => removeTreatyType(index)}
                        className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm font-medium flex items-center"
                      >
                        <X className="h-4 w-4 mr-1" />
                        Remove
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Total Cost:</strong> $
                {formData.treatyTypes.reduce((total, tt) => total + (tt.treatyType.price || 0), 0).toFixed(2)} USD
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {mode === 'create' ? 'Create Treaty' : 'Edit Treaty'}
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Manage treaty information, types, and business details
        </p>
      </div>

      {/* Step Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {[1, 2, 3, 4].map((stepNum) => (
            <div key={stepNum} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                step >= stepNum 
                  ? 'bg-slate-600 text-white' 
                  : 'bg-gray-200 text-gray-600 dark:bg-gray-600 dark:text-gray-300'
              }`}>
                {stepNum}
              </div>
              {stepNum < 4 && (
                <div className={`w-16 h-1 mx-2 ${
                  step > stepNum ? 'bg-slate-600' : 'bg-gray-200 dark:bg-gray-600'
                }`} />
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-between mt-2 text-xs text-gray-600 dark:text-gray-400">
          <span>Treaty Selection</span>
          <span>Business Details</span>
          <span>Contact Info</span>
          <span>Review</span>
        </div>
      </div>

      {/* Step Content */}
      {step === 1 && renderStep1()}

      {/* Placeholder for Contact Information Step */}
      {step === 3 && (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4 flex items-center">
              <User className="h-5 w-5 mr-2" />
              Contact Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Mail className="inline h-4 w-4 mr-1" />
                  Email Address
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Phone className="inline h-4 w-4 mr-1" />
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={formData.phoneNumbers[0] || ''}
                  onChange={(e) => updatePhoneNumber(0, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  placeholder="****** 567 8900"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <MapPin className="inline h-4 w-4 mr-1" />
                  Residential Address
                </label>
                <textarea
                  value={formData.residentialAddress}
                  onChange={(e) => setFormData(prev => ({ ...prev, residentialAddress: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter your residential address"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Building className="inline h-4 w-4 mr-1" />
                  Business Location
                </label>
                <select
                  value={formData.currentCountryId}
                  onChange={(e) => setFormData(prev => ({ ...prev, currentCountryId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Select Country</option>
                  {countries.map(country => (
                    <option key={country.id} value={country.id}>
                      {country.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Add other step renders here - step 2, 4 */}

      {/* Navigation Buttons */}
      <div className="flex justify-between mt-8">
        <div>
          {step > 1 && (
            <button
              onClick={prevStep}
              className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
            >
              Previous
            </button>
          )}
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleSave}
            className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 flex items-center"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Draft
          </button>
          {step < 4 ? (
            <button
              onClick={nextStep}
              className="px-6 py-2 bg-slate-600 text-white rounded-md hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
            >
              Next
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Create Treaty'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
