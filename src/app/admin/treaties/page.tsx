'use client';

import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { VerticalNav } from '@/components/admin/VerticalNav';
import { TreatyManagementForm } from './components/TreatyManagementForm';
import { TreatyTypesManagement } from '@/components/treaties/TreatyTypesManagement';
import { Plus, FileText, Users, Search, Settings } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';

type TabType = 'manage' | 'create' | 'search' | 'types';

export default function TreatiesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<TabType>('manage');
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check admin permissions via API
  useEffect(() => {
    const verifyAdmin = async () => {
      if (status === 'loading') return;

      if (!session) {
        router.push('/unauthorized');
        return;
      }

      try {
        const response = await fetch('/api/admin/check-permissions');
        const data = await response.json();

        if (!response.ok || !data.isAdmin) {
          router.push('/unauthorized');
          return;
        }

        setIsAdmin(true);
      } catch (error) {
        console.error('Error verifying admin permissions:', error);
        router.push('/unauthorized');
        return;
      } finally {
        setIsLoading(false);
      }
    };

    verifyAdmin();
  }, [session, status, router]);

  const handleTreatyCreated = () => {
    setActiveTab('manage');
    // Refresh treaty list or show success message
  };

  const handleSaveDraft = (data: any) => {
    // Save draft functionality
    console.log('Saving draft:', data);
  };

  const tabs = [
    { id: 'manage' as TabType, label: 'Manage Treaties' },
    { id: 'create' as TabType, label: 'Create Treaty' },
    { id: 'search' as TabType, label: 'Search Treaties' },
    { id: 'types' as TabType, label: 'Treaty Types' },
  ];

  if (status === 'loading' || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAdmin) {
    return null; // Will redirect
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'create':
        return (
          <div className="space-y-6">
            {/* User Selection for Create View */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Select User (Optional)
              </label>
              <input
                type="text"
                value={selectedUserId}
                onChange={(e) => setSelectedUserId(e.target.value)}
                placeholder="Enter user ID or leave empty for current user"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                If left empty, the treaty will be created for the current authenticated user.
              </p>
            </div>
            
            <TreatyManagementForm
              userId={selectedUserId}
              mode="create"
              onTreatyCreated={handleTreatyCreated}
              onSave={handleSaveDraft}
            />
          </div>
        );
      case 'search':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Search Treaties
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Search functionality will be implemented here.
            </p>
          </div>
        );
      case 'types':
        return <TreatyTypesManagement />;
      default:
        return (
          <div className="space-y-6">

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <FileText className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Total Treaties
                    </p>
                    <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                      24
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Users className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Active Treaties
                    </p>
                    <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                      18
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <FileText className="h-8 w-8 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Pending Review
                    </p>
                    <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                      6
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Treaties List */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                  Recent Treaties
                </h2>
              </div>
              <div className="p-6">
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                    No treaties found
                  </h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Get started by creating a new treaty.
                  </p>
                  <div className="mt-6">
                    <button
                      onClick={() => setActiveTab('create')}
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Create Treaty
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Key Features Info */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
              <h3 className="text-lg font-medium text-blue-900 dark:text-blue-200 mb-4">
                🏛️ Treaty Management Features
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800 dark:text-blue-300">
                <div>
                  <h4 className="font-medium mb-2">✅ Key Features:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Trade Treaty Number validation</li>
                    <li>Multiple treaty type selection</li>
                    <li>Business details management</li>
                    <li>Geographic location tracking</li>
                    <li>File attachment support</li>
                    <li>Multi-step form validation</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">📋 Treaty Categories:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Micro Organizations</li>
                    <li>Small Organizations</li>
                    <li>Medium Organizations</li>
                    <li>Large Organizations</li>
                    <li>NGOs and Charitable Trusts</li>
                    <li>Educational Institutions</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <DashboardLayout>
      <div className="flex h-full">
        {/* Admin Vertical Navigation */}
        <div className="w-64 bg-slate-800 text-white shadow-lg flex-shrink-0 rounded-lg">
          <VerticalNav />
        </div>

        {/* Admin Content */}
        <div className="flex-1 p-6 bg-gray-50 dark:bg-gray-900">
          <div className="space-y-6">
            {/* Page Header */}
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Treaties Management</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                Create, manage, and track treaty applications, types, and pricing.
              </p>
            </div>

            {/* Tabs */}
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="-mb-px flex space-x-8">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-slate-700 text-slate-700 dark:text-slate-300'
                        : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="bg-white dark:bg-slate-800 shadow rounded-lg p-6">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
