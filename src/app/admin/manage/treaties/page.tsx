'use client';

import React, { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { EnhancedTreatyCreation } from '../components/EnhancedTreatyCreation';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
import { FileText, Plus, Users, Activity } from 'lucide-react';

export default function TreatiesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'create' | 'manage' | 'types' | 'status'>('create');
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [userProfile, setUserProfile] = useState<{
    firstName: string;
    lastName: string;
    email: string;
  } | null>(null);
  const [users, setUsers] = useState<Array<{
    id: string;
    name: string;
    email: string;
    profile?: {
      firstName?: string;
      lastName?: string;
    };
  }>>([]);

  // Check admin permissions via API
  useEffect(() => {
    const verifyAdmin = async () => {
      if (status === 'loading') return;

      if (!session) {
        router.push('/unauthorized');
        return;
      }

      try {
        const response = await fetch('/api/admin/check-permissions');
        const data = await response.json();

        if (!response.ok || !data.isAdmin) {
          router.push('/unauthorized');
          return;
        }

        setIsAdmin(true);
        fetchUsers();
      } catch (error) {
        console.error('Error verifying admin permissions:', error);
        router.push('/unauthorized');
        return;
      } finally {
        setIsLoading(false);
      }
    };

    verifyAdmin();
  }, [session, status, router]);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/admin/users?limit=100');
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      } else {
        console.error('Failed to fetch users');
        toast.error('Failed to fetch users list');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Error loading users');
    }
  };

  const handleUserSelection = (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (user) {
      setSelectedUserId(userId);
      setUserProfile({
        firstName: user.profile?.firstName || user.name?.split(' ')[0] || '',
        lastName: user.profile?.lastName || user.name?.split(' ').slice(1).join(' ') || '',
        email: user.email
      });
    }
  };

  if (status === 'loading' || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-800 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAdmin) {
    return null; // Will redirect
  }

  const treatySections = [
    {
      id: 'create',
      title: 'Create Treaty',
      description: 'Create new treaties for users',
      icon: <Plus className="h-5 w-5" />,
      component: (
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Create New Treaty
          </h2>

          {/* User Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Select User
            </label>
            <select
              value={selectedUserId}
              onChange={(e) => handleUserSelection(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">Select a user to create treaty for...</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>
                  {user.name} ({user.email})
                </option>
              ))}
            </select>
          </div>

          {/* Treaty Creation Form */}
          {selectedUserId && userProfile && (
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800 mb-6">
                <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                  Creating Treaty for: {userProfile.firstName} {userProfile.lastName}
                </h3>
                <p className="text-xs text-blue-700 dark:text-blue-300">
                  Email: {userProfile.email}
                </p>
              </div>

              <EnhancedTreatyCreation
                userId={selectedUserId}
                userProfile={userProfile}
                onTreatyCreated={() => {
                  toast.success('Treaty created successfully!');
                  setSelectedUserId('');
                  setUserProfile(null);
                }}
              />
            </div>
          )}

          {!selectedUserId && (
            <div className="text-center py-12">
              <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Select a User
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                Choose a user from the dropdown above to create a treaty for them.
              </p>
            </div>
          )}
        </div>
      )
    },
    {
      id: 'manage',
      title: 'Manage Treaties',
      description: 'View and manage existing user treaties',
      icon: <FileText className="h-5 w-5" />,
      component: (
        <div className="text-center py-12">
          <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Treaty Management
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            View and manage existing user treaties, their status, and associated documents.
          </p>
          <div className="text-sm text-gray-400 dark:text-gray-500">
            Features to be implemented:
            <ul className="mt-2 space-y-1">
              <li>• View all user treaties</li>
              <li>• Edit treaty details</li>
              <li>• Manage treaty approvals</li>
              <li>• Treaty document management</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 'types',
      title: 'Treaty Types',
      description: 'Configure available treaty types',
      icon: <Activity className="h-5 w-5" />,
      component: (
        <div className="text-center py-12">
          <Activity className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Treaty Types Management
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            Configure available treaty types, their requirements, and pricing.
          </p>
          <div className="text-sm text-gray-400 dark:text-gray-500">
            Features to be implemented:
            <ul className="mt-2 space-y-1">
              <li>• Create new treaty types</li>
              <li>• Edit existing treaty types</li>
              <li>• Set pricing and requirements</li>
              <li>• Manage categories and descriptions</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 'status',
      title: 'Treaty Status',
      description: 'Monitor treaty statuses and approvals',
      icon: <Users className="h-5 w-5" />,
      component: (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Treaty Status Overview
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            Monitor treaty statuses, approvals, and expiration dates across all users.
          </p>
          <div className="text-sm text-gray-400 dark:text-gray-500">
            Features to be implemented:
            <ul className="mt-2 space-y-1">
              <li>• Treaty status dashboard</li>
              <li>• Approval workflow tracking</li>
              <li>• Expiration monitoring</li>
              <li>• Bulk status updates</li>
            </ul>
          </div>
        </div>
      )
    }
  ];

  const activeSection = treatySections.find(section => section.id === activeTab) || treatySections[0];

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-100 mb-6">Treaties Management</h1>

          <div className="flex flex-col lg:flex-row gap-6">
            {/* Sidebar Navigation */}
            <div className="w-full lg:w-64">
              <nav className="space-y-1 bg-slate-800 p-2 rounded-lg">
                {treatySections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveTab(section.id as 'create' | 'manage' | 'types' | 'status')}
                    className={`flex items-center p-3 rounded transition-colors duration-200 text-sm font-medium w-full text-left text-white ${
                      activeTab === section.id
                        ? 'bg-slate-700'
                        : 'hover:bg-slate-700'
                    }`}
                  >
                    <div className="mr-3 text-white">
                      {section.icon}
                    </div>
                    {section.title}
                  </button>
                ))}
              </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-2 flex items-center text-slate-800 dark:text-slate-100">
                  <div className="mr-3 text-slate-600 dark:text-slate-400">
                    {activeSection.icon}
                  </div>
                  {activeSection.title}
                </h2>
                <p className="text-slate-600 dark:text-slate-400 mb-6 text-sm">
                  {activeSection.description}
                </p>
                {activeSection.component}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
