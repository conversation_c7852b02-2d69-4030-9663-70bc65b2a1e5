'use client';

import React, { useState, useRef } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { VerticalNav } from '@/components/admin/VerticalNav';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Download, Settings, Eye, CheckCircle, XCircle, AlertCircle, FileText } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

interface HeaderAnalysis {
  exactMatches: Array<{ csvColumn: string; dbField: string; suggestedMapping: string }>;
  partialMatches: Array<{ csvColumn: string; dbField: string; suggestedMapping: string; confidence: number }>;
  unmatchedColumns: string[];
  requiredFields: string[];
}

interface ValidationResults {
  validRows: number;
  warnings: string[];
  errors: string[];
  requiredColumns: string[];
}

interface UploadResults {
  results: {
    processedUsers: number;
    successfulUsers: number;
    failedUsers: number;
    warnings: string[];
  };
}

export default function BulkUserUploadPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState<'upload' | 'mapping' | 'validation' | 'processing'>('upload');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadMode, setUploadMode] = useState<'smart' | 'strict' | 'custom'>('smart');
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [headerAnalysis, setHeaderAnalysis] = useState<HeaderAnalysis | null>(null);
  const [validationResults, setValidationResults] = useState<ValidationResults | null>(null);
  const [uploadResults, setUploadResults] = useState<UploadResults | null>(null);

  // Check admin status
  React.useEffect(() => {
    const checkAdmin = async () => {
      if (status === 'authenticated') {
        try {
          const response = await fetch('/api/admin/check-admin');
          if (response.ok) {
            setIsAdmin(true);
          } else {
            router.push('/unauthorized');
          }
        } catch (error) {
          console.error('Error checking admin status:', error);
          router.push('/unauthorized');
        }
      } else if (status === 'unauthenticated') {
        router.push('/login');
      }
      setLoading(false);
    };

    checkAdmin();
  }, [status, router]);

  const downloadTemplate = async () => {
    try {
      const response = await fetch('/api/admin/users/bulk/template');
      if (!response.ok) throw new Error('Failed to download template');
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'user_bulk_upload_template.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading template:', error);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && (file.type === 'text/csv' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel')) {
      setSelectedFile(file);
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && (file.type === 'text/csv' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel')) {
      setSelectedFile(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const removeFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const validateFile = async () => {
    if (!selectedFile) return;
    
    setUploading(true);
    setUploadProgress(0);
    
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('mode', uploadMode);
      
      const response = await fetch('/api/admin/users/bulk/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) throw new Error('Validation failed');
      
      const data = await response.json();
      setHeaderAnalysis(data.headerAnalysis);
      setValidationResults(data.validationResults);
      setCurrentStep('validation');
    } catch (error) {
      console.error('Error validating file:', error);
    } finally {
      setUploading(false);
    }
  };

  const processUpload = async () => {
    if (!selectedFile || !headerAnalysis) return;
    
    setUploading(true);
    setUploadProgress(0);
    
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('mode', uploadMode);
      formData.append('mappings', JSON.stringify(headerAnalysis));
      
      const response = await fetch('/api/admin/users/bulk/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) throw new Error('Upload failed');
      
      const data = await response.json();
      setUploadResults(data);
      setCurrentStep('processing');
    } catch (error) {
      console.error('Error processing upload:', error);
    } finally {
      setUploading(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAdmin) {
    return null; // Will redirect
  }

  return (
    <DashboardLayout>
      <div className="flex h-full">
        {/* Admin Vertical Navigation */}
        <div className="w-64 bg-slate-800 text-white shadow-lg flex-shrink-0 rounded-lg">
          <VerticalNav />
        </div>
        
        {/* Main Content */}
        <div className="flex-1 p-6 bg-gray-50 dark:bg-gray-900">
          <div className="max-w-6xl mx-auto space-y-6">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
                Bulk User Upload
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Upload multiple users at once using a CSV or Excel file. Download the template to get started.
              </p>
            </div>

            {/* Progress Steps */}
            <div className="flex items-center justify-between">
              {[
                { key: 'upload', label: 'Upload', icon: Upload },
                { key: 'mapping', label: 'Mapping', icon: Settings },
                { key: 'validation', label: 'Validation', icon: Eye },
                { key: 'processing', label: 'Processing', icon: CheckCircle }
              ].map((step, index) => {
                const Icon = step.icon;
                return (
                  <div key={step.key} className="flex flex-col items-center">
                    <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
                      currentStep === step.key 
                        ? 'bg-blue-600 text-white' 
                        : index < ['upload', 'mapping', 'validation', 'processing'].indexOf(currentStep)
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-200 text-gray-500'
                    }`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{step.label}</span>
                  </div>
                );
              })}
            </div>

            {/* Template Download Section */}
            <div className="bg-white dark:bg-slate-800 shadow rounded-lg">
              <div className="p-6 space-y-4">
                <div className="flex items-center gap-2">
                  <Download className="h-5 w-5 text-blue-600" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Download Template</h3>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Download the CSV template with the correct column headers and example data.
                </p>
                <Button 
                  onClick={downloadTemplate}
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white border-0"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download CSV Template
                </Button>
              </div>
            </div>

            {/* File Upload Section */}
            {currentStep === 'upload' && (
              <div className="bg-white dark:bg-slate-800 shadow rounded-lg">
                <div className="p-6 space-y-4">
                  <div className="flex items-center gap-2">
                    <Upload className="h-5 w-5 text-green-600" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Upload File</h3>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Select your CSV or Excel file and choose the processing mode.
                  </p>
                  
                  {/* Upload Mode Selection */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Processing Mode</Label>
                    <Select value={uploadMode} onValueChange={(value: string) => setUploadMode(value as 'smart' | 'strict' | 'custom')}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="smart">
                          <div>
                            <div className="font-medium">Smart Mapping</div>
                            <div className="text-sm text-gray-500">Automatically suggests field mappings</div>
                          </div>
                        </SelectItem>
                        <SelectItem value="strict">
                          <div>
                            <div className="font-medium">Strict Mode</div>
                            <div className="text-sm text-gray-500">Headers must match exactly</div>
                          </div>
                        </SelectItem>
                        <SelectItem value="custom">
                          <div>
                            <div className="font-medium">Custom Mapping</div>
                            <div className="text-sm text-gray-500">Manually map all fields</div>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div
                    className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer"
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                    onClick={() => document.getElementById('file-input')?.click()}
                  >
                    {selectedFile ? (
                      <div className="space-y-2">
                        <FileText className="h-12 w-12 text-green-600 mx-auto" />
                        <p className="text-lg font-medium text-gray-900 dark:text-white">{selectedFile.name}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                        <Badge variant="secondary" className="mt-2">
                          {uploadMode} mode
                        </Badge>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Upload className="h-12 w-12 text-gray-400 mx-auto" />
                        <p className="text-lg font-medium text-gray-600 dark:text-gray-400">
                          Drag and drop your file here, or click to browse
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-500">
                          Supports CSV and Excel files up to 10MB
                        </p>
                      </div>
                    )}

                    <Input
                      id="file-input"
                      type="file"
                      accept=".csv,.xlsx,.xls"
                      onChange={handleFileSelect}
                      className="hidden"
                    />

                    {selectedFile && (
                      <div className="space-y-4">
                        {uploading && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between text-sm">
                              <span>Validating file...</span>
                              <span>{uploadProgress}%</span>
                            </div>
                            <Progress value={uploadProgress} className="w-full" />
                          </div>
                        )}
                        
                        <div className="flex justify-center space-x-3">
                          <Button
                            onClick={removeFile}
                            variant="outline"
                            disabled={uploading}
                          >
                            Remove File
                          </Button>
                          <Button
                            onClick={validateFile}
                            disabled={uploading}
                            className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white border-0"
                          >
                            {uploading ? (
                              'Validating...'
                            ) : (
                              <>
                                <Eye className="h-4 w-4 mr-2" />
                                Validate File
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Results sections would go here */}
            {currentStep === 'validation' && validationResults && (
              <div className="bg-white dark:bg-slate-800 shadow rounded-lg">
                <div className="p-6 space-y-4">
                  <div className="flex items-center gap-2">
                    <Eye className="h-5 w-5 text-blue-600" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Validation Results</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                      <p className="text-green-800 font-medium text-sm">Valid Rows</p>
                      <p className="text-green-600 text-lg font-bold">{validationResults.validRows}</p>
                    </div>
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <p className="text-yellow-800 font-medium text-sm">Warnings</p>
                      <p className="text-yellow-600 text-lg font-bold">{validationResults.warnings?.length || 0}</p>
                    </div>
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                      <p className="text-red-800 font-medium text-sm">Errors</p>
                      <p className="text-red-600 text-lg font-bold">{validationResults.errors?.length || 0}</p>
                    </div>
                  </div>

                  {validationResults.warnings?.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-yellow-800">Warnings</h4>
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-h-40 overflow-y-auto">
                        <ul className="space-y-1">
                          {validationResults.warnings.map((warning: string, index: number) => (
                            <li key={index} className="text-sm text-yellow-700">• {warning}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}

                  {validationResults.errors?.length > 0 && (
                    <Alert className="border-red-200 bg-red-50">
                      <XCircle className="h-4 w-4 text-red-600" />
                      <AlertDescription className="text-red-800">
                        <div className="space-y-2">
                          <h4 className="font-medium text-red-800">Errors Found</h4>
                          <ul className="space-y-1">
                            {validationResults.errors.map((error: string, index: number) => (
                              <li key={index} className="text-sm text-red-700">• {error}</li>
                            ))}
                          </ul>
                        </div>
                      </AlertDescription>
                    </Alert>
                  )}

                  <div className="flex justify-end space-x-3">
                    <Button
                      onClick={() => {
                        setCurrentStep('upload');
                        removeFile();
                      }}
                      variant="outline"
                    >
                      Upload Different File
                    </Button>
                    {validationResults.errors.length === 0 && (
                      <Button
                        onClick={processUpload}
                        disabled={uploading}
                        className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white border-0"
                      >
                        {uploading ? 'Processing...' : 'Process Upload'}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            )}

            {currentStep === 'processing' && uploadResults && (
              <div className="bg-white dark:bg-slate-800 shadow rounded-lg">
                <div className="p-6 space-y-4">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Upload Complete</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                      <p className="text-green-800 font-medium text-sm">Processed</p>
                      <p className="text-green-600 text-lg font-bold">{uploadResults.results?.processedUsers || 0}</p>
                    </div>
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <p className="text-yellow-800 font-medium text-sm">Warnings</p>
                      <p className="text-yellow-600 text-lg font-bold">{uploadResults.results?.warnings?.length || 0}</p>
                    </div>
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                      <p className="text-red-800 font-medium text-sm">Failed</p>
                      <p className="text-red-600 text-lg font-bold">{uploadResults.results?.failedUsers || 0}</p>
                    </div>
                  </div>

                  {uploadResults.results?.warnings?.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-yellow-800">Upload Warnings</h4>
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-h-40 overflow-y-auto">
                        <ul className="space-y-1">
                          {uploadResults.results.warnings.map((warning: string, index: number) => (
                            <li key={index} className="text-sm text-yellow-700">• {warning}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end space-x-3">
                    <Button
                      onClick={() => {
                        setCurrentStep('upload');
                        removeFile();
                        setUploadResults(null);
                      }}
                      className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white border-0"
                    >
                      Upload Another File
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}