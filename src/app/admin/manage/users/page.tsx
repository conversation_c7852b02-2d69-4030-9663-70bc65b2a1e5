'use client';

import React from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { VerticalNav } from '@/components/admin/VerticalNav';
import { CreateUserTab } from '@/components/users/manage/CreateUserTab';
import { AdminPage } from '@/components/auth/ProtectedPage';

export default function ManageUsersPage() {
  return (
    <AdminPage requiredPermissions={['users.manage', 'admin.users']}>
      <DashboardLayout>
        <div className="flex h-full">
          {/* Admin Vertical Navigation */}
          <div className="w-64 bg-slate-800 text-white shadow-lg flex-shrink-0 rounded-lg">
            <VerticalNav />
          </div>

          {/* Admin Content */}
          <div className="flex-1 p-6 bg-gray-50 dark:bg-gray-900">
            <div className="space-y-6">
              {/* Page Header */}
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Manage Users</h1>
                <p className="mt-2 text-gray-600 dark:text-gray-300">
                  Create and manage user accounts with comprehensive profile information
                </p>
              </div>

              {/* CreateUserTab Component with internal tabs */}
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <CreateUserTab />
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </AdminPage>
  );
}