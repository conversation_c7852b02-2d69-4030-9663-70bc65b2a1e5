'use client';

import React, { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { VerticalNav } from '@/components/admin/VerticalNav';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
import { Briefcase } from 'lucide-react';
import { fetchTitles, fetchPositions, type Title, type Position } from '@/lib/admin-shared-utils';

interface PositionFormData {
  titleId: string;
  positionId: string;
}

export default function PositionsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Position form state
  const [positionData, setPositionData] = useState<PositionFormData>({
    titleId: '',
    positionId: '',
  });
  
  const [titles, setTitles] = useState<Title[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [filteredPositions, setFilteredPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Check admin permissions via API
  useEffect(() => {
    const verifyAdmin = async () => {
      if (status === 'loading') return;

      if (!session) {
        router.push('/unauthorized');
        return;
      }

      try {
        const response = await fetch('/api/admin/check-permissions');
        const data = await response.json();

        if (!response.ok || !data.isAdmin) {
          router.push('/unauthorized');
          return;
        }

        setIsAdmin(true);
      } catch (error) {
        console.error('Error verifying admin permissions:', error);
        router.push('/unauthorized');
        return;
      } finally {
        setIsLoading(false);
      }
    };

    verifyAdmin();
  }, [session, status, router]);

  // Fetch titles and positions on component mount
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const [titlesData, positionsData] = await Promise.all([
          fetchTitles(),
          fetchPositions()
        ]);
        setTitles(titlesData);
        setPositions(positionsData);
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load titles and positions');
      } finally {
        setLoading(false);
      }
    };

    if (isAdmin) {
      loadData();
    }
  }, [isAdmin]);

  // Filter positions when title changes
  useEffect(() => {
    if (positionData.titleId && positions.length > 0) {
      // Find positions associated with the selected title
      const titlePositions = positions.filter(position => {
        // You might need to adjust this logic based on your actual data structure
        // This assumes positions have a relationship with titles
        return position.isActive;
      });
      setFilteredPositions(titlePositions);
      // Reset position selection when title changes
      setPositionData(prev => ({ ...prev, positionId: '' }));
    } else {
      setFilteredPositions([]);
      setPositionData(prev => ({ ...prev, positionId: '' }));
    }
  }, [positionData.titleId, positions]);

  const handlePositionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setPositionData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    
    if (!positionData.titleId) {
      toast.error('Please select an ambassadorial title');
      setSaving(false);
      return;
    }
    
    try {
      // Here you would typically send the position data to your API
      // For now, we'll just simulate a save
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Position assignment saved successfully!');
    } catch (error) {
      toast.error('Failed to save position assignment');
      console.error('Error saving position assignment:', error);
    } finally {
      setSaving(false);
    }
  };

  // Get positions to display (filtered by title or all positions)
  const displayPositions = positionData.titleId ? filteredPositions : positions;

  if (status === 'loading' || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAdmin) {
    return null; // Will redirect
  }

  return (
    <DashboardLayout>
      <div className="flex h-full">
        {/* Admin Vertical Navigation */}
        <div className="w-64 bg-slate-800 text-white shadow-lg flex-shrink-0 rounded-lg">
          <VerticalNav />
        </div>

        {/* Admin Content */}
        <div className="flex-1 p-6 bg-gray-50 dark:bg-gray-900">
          <div className="space-y-6">
            {/* Page Header */}
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Positions Management</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                Manage user positions, titles, and organizational hierarchy.
              </p>
            </div>

            {/* Info Message */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
              <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center">
                <Briefcase className="h-4 w-4 mr-2" />
                Position Assignment Form
              </h3>
              <p className="text-xs text-blue-600 dark:text-blue-300">
                Assign user positions and ambassadorial titles within the organizational hierarchy. Select a title first to see available positions.
              </p>
            </div>

            {/* Position Form */}
            {loading ? (
              <div className="bg-white dark:bg-slate-800 shadow rounded-lg p-6">
                <div className="flex justify-center items-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  <p className="ml-3 text-gray-600 dark:text-gray-400">Loading titles and positions...</p>
                </div>
              </div>
            ) : (
              <div className="bg-white dark:bg-slate-800 shadow rounded-lg p-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="titleId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Ambassadorial Title <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="titleId"
                        name="titleId"
                        value={positionData.titleId}
                        onChange={handlePositionChange}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        required
                      >
                        <option value="">Select Ambassadorial Title</option>
                        {titles
                          .filter(title => title.isActive)
                          .map(title => (
                            <option key={title.id} value={title.id}>
                              {title.name}
                            </option>
                          ))}
                      </select>
                      {titles.length === 0 && (
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          No titles available. Please contact an administrator.
                        </p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="positionId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Position
                      </label>
                      <select
                        id="positionId"
                        name="positionId"
                        value={positionData.positionId}
                        onChange={handlePositionChange}
                        disabled={!positionData.titleId}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white disabled:opacity-50"
                      >
                        <option value="">{positionData.titleId ? 'Select Position' : 'Select an ambassadorial title first'}</option>
                        {displayPositions
                          .filter(position => position.isActive)
                          .map(position => (
                            <option key={position.id} value={position.id}>
                              {position.title}
                            </option>
                          ))}
                      </select>
                      {positionData.titleId && displayPositions.length === 0 && (
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          No positions are associated with the selected ambassadorial title.
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-green-800 dark:text-green-200 mb-2">Organizational Hierarchy</h4>
                    <p className="text-xs text-green-700 dark:text-green-300">
                      Titles and positions form the organizational hierarchy. Selecting a title will filter available positions. Users can hold multiple positions if needed.
                    </p>
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => {
                        setPositionData({
                          titleId: '',
                          positionId: '',
                        });
                        toast.success('Form cleared!');
                      }}
                      className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Clear Form
                    </button>
                    <button
                      type="submit"
                      disabled={saving || !positionData.titleId}
                      className="px-6 py-2 bg-blue-800 text-white rounded-md hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 transition-colors duration-200"
                    >
                      {saving ? 'Saving...' : 'Save Position Assignment'}
                    </button>
                  </div>
                </form>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
