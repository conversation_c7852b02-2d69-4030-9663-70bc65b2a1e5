'use client';

import React, { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { VerticalNav } from '@/components/admin/VerticalNav';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
import { MapPin, CheckCircle } from 'lucide-react';
import { ContactDetailsForm } from '@/components/users/manage/shared/ContactDetailsForm';
import Link from 'next/link';

export default function ContactDetailsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);
  const [contactSaved, setContactSaved] = useState(false);

  // Get userId from URL query parameters
  useEffect(() => {
    if (!searchParams) return; // Add null check
    const userIdParam = searchParams.get('userId');
    if (userIdParam) {
      setUserId(userIdParam);
    }
  }, [searchParams]);

  // Check admin permissions via API
  useEffect(() => {
    const verifyAdmin = async () => {
      if (status === 'loading') return;

      if (!session) {
        router.push('/unauthorized');
        return;
      }

      try {
        const response = await fetch('/api/admin/check-permissions');
        const data = await response.json();

        if (!response.ok || !data.isAdmin) {
          router.push('/unauthorized');
          return;
        }

        setIsAdmin(true);
      } catch (error) {
        console.error('Error verifying admin permissions:', error);
        router.push('/unauthorized');
        return;
      } finally {
        setIsLoading(false);
      }
    };

    verifyAdmin();
  }, [session, status, router]);

  // Handle contact form submission
  const handleContactSubmit = async (contactData: any) => {
    if (!userId) {
      toast.error('No user specified for contact details');
      return;
    }

    try {
      // Transform contact data to match UserContactData format
      const userContactData = {
        streetAddress1: contactData.streetName || '',
        streetAddress2: contactData.aptSuiteUnit || '',
        town: contactData.townCity || '',
        city: '', // ContactDetailsForm doesn't have a separate city field
        country: contactData.country || '',
        postalCode: contactData.postcodeZip || '',
        regionId: '',
        regionText: contactData.stateProvince || '',
      };

      const response = await fetch(`/api/users/${userId}/contact`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userContactData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save contact details');
      }

      setContactSaved(true);
      toast.success('Contact details saved successfully!');
    } catch (error) {
      console.error('Error saving contact details:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save contact details');
    }
  };

  if (status === 'loading' || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAdmin) {
    return null; // Will redirect
  }

  // Show success state if contact was saved
  if (contactSaved) {
    return (
      <DashboardLayout>
        <div className="flex h-full">
          {/* Admin Vertical Navigation */}
          <div className="w-64 bg-slate-800 text-white shadow-lg flex-shrink-0 rounded-lg">
            <VerticalNav />
          </div>

          {/* Admin Content */}
          <div className="flex-1 p-6 bg-gray-50 dark:bg-gray-900">
            <div className="max-w-2xl mx-auto">
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6 text-center">
                <CheckCircle className="mx-auto h-12 w-12 text-green-500 mb-4" />
                <h3 className="text-lg font-medium text-green-900 dark:text-green-100 mb-2">
                  Contact Details Saved Successfully!
                </h3>
                <p className="text-green-700 dark:text-green-300 mb-6">
                  The contact details have been saved for the user.
                </p>
                
                <div className="space-y-3">
                  <Link
                    href={`/admin/manage/identification?userId=${userId}`}
                    className="inline-block px-6 py-2 bg-blue-800 text-white rounded-md hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                  >
                    Add Identification Details
                  </Link>
                  
                  <div>
                    <Link
                      href={`/admin/manage/updateuser?userId=${userId}`}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      Back to User Management
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!userId) {
    return (
      <DashboardLayout>
        <div className="flex h-full">
          {/* Admin Vertical Navigation */}
          <div className="w-64 bg-slate-800 text-white shadow-lg flex-shrink-0 rounded-lg">
            <VerticalNav />
          </div>

          {/* Admin Content */}
          <div className="flex-1 p-6 bg-gray-50 dark:bg-gray-900">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">No User Specified</h1>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Please access this page from the user management section by clicking &quot;Add Contact Details&quot; for a specific user.
              </p>
              <Link
                href="/admin/manage/createuser"
                className="px-6 py-2 bg-blue-800 text-white rounded-md hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              >
                Create New User
              </Link>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="flex h-full">
        {/* Admin Vertical Navigation */}
        <div className="w-64 bg-slate-800 text-white shadow-lg flex-shrink-0 rounded-lg">
          <VerticalNav />
        </div>

        {/* Admin Content */}
        <div className="flex-1 p-6 bg-gray-50 dark:bg-gray-900">
          <div className="space-y-6">
            {/* Page Header */}
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Add Contact Details</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                Add contact information for the selected user.
              </p>
            </div>

            {/* Info Message */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
              <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center">
                <MapPin className="h-4 w-4 mr-2" />
                Contact Details Form
              </h3>
              <p className="text-xs text-blue-600 dark:text-blue-300">
                Enter the user&apos;s address information. Street number and name are separated for better data organization.
              </p>
            </div>

            {/* Contact Form */}
            <div className="bg-white dark:bg-slate-800 shadow rounded-lg p-6">
              <ContactDetailsForm
                mode="create"
                onSubmit={handleContactSubmit}
                className="max-w-2xl"
              />
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
