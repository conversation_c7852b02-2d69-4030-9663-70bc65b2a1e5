'use client';

import React, { useState } from 'react';
import { toast } from 'sonner';
import {
  User,
  Mail,
  Phone,
  Calendar,
  CheckCircle,
  UserPlus,
  ArrowRight,
  ExternalLink
} from 'lucide-react';
import Link from 'next/link';
import { EmailInput } from '@/components/users/manage/shared/EmailInput';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

// Form validation schema
const createUserSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Please enter a valid email address'),
  personalEmail: z.string().email('Please enter a valid email address').optional().or(z.literal('')),
  nwaEmail: z.string().email('Please enter a valid email address').optional().or(z.literal('')),
  phone: z.string().optional(),
  mobile: z.string().optional(),
  dateOfBirth: z.string().optional(),
  bio: z.string().optional(),
});

type CreateUserData = z.infer<typeof createUserSchema>;

export const CreateUserTab: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [createdUserId, setCreatedUserId] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<CreateUserData>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      personalEmail: '',
      nwaEmail: '',
      phone: '',
      mobile: '',
      dateOfBirth: '',
      bio: ''
    }
  });

  const onSubmit = async (data: CreateUserData) => {
    setIsSubmitting(true);
    try {
      // Prepare user data for API submission
      const userData = {
        firstName: data.firstName.trim(),
        lastName: data.lastName.trim(),
        email: data.email.trim(),
        personalEmail: data.personalEmail?.trim() || undefined,
        nwaEmail: data.nwaEmail?.trim() || undefined,
        phone: data.phone?.trim() || undefined,
        mobile: data.mobile?.trim() || undefined,
        dateOfBirth: data.dateOfBirth || undefined,
        bio: data.bio?.trim() || undefined,
      };

      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create user');
      }

      const result = await response.json();
      setCreatedUserId(result.user.id);
      
      toast.success('User created successfully!');
      
      // Reset form using react-hook-form's reset method
      // Note: This will be handled by the form's reset functionality

    } catch (error: any) {
      console.error('Error creating user:', error);
      toast.error(error.message || 'Failed to create user');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (createdUserId) {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6 text-center">
          <CheckCircle className="mx-auto h-12 w-12 text-green-500 mb-4" />
          <h3 className="text-lg font-medium text-green-900 dark:text-green-100 mb-2">
            User Created Successfully!
          </h3>
          <p className="text-green-700 dark:text-green-300 mb-6">
            The user has been created with ID: <code className="bg-green-100 dark:bg-green-800 px-2 py-1 rounded">{createdUserId}</code>
          </p>
          
          {/* Next Steps */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-3">
              Next Steps
            </h4>
            <div className="space-y-3 text-sm">
              <Link
                href={`/admin/manage/contact?userId=${createdUserId}`}
                className="flex items-center justify-between p-3 bg-white dark:bg-slate-700 border border-gray-200 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-slate-600 transition-colors"
              >
                <span className="text-gray-900 dark:text-white">Add Contact Details</span>
                <ExternalLink className="h-4 w-4 text-gray-400" />
              </Link>
              
              <Link
                href={`/admin/manage/identification?userId=${createdUserId}`}
                className="flex items-center justify-between p-3 bg-white dark:bg-slate-700 border border-gray-200 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-slate-600 transition-colors"
              >
                <span className="text-gray-900 dark:text-white">Add Identification Details</span>
                <ExternalLink className="h-4 w-4 text-gray-400" />
              </Link>
              
              <Link
                href={`/admin/manage/positions?userId=${createdUserId}`}
                className="flex items-center justify-between p-3 bg-white dark:bg-slate-700 border border-gray-200 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-slate-600 transition-colors"
              >
                <span className="text-gray-900 dark:text-white">Assign Positions</span>
                <ExternalLink className="h-4 w-4 text-gray-400" />
              </Link>
              
              <Link
                href={`/admin/manage/treaties?userId=${createdUserId}`}
                className="flex items-center justify-between p-3 bg-white dark:bg-slate-700 border border-gray-200 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-slate-600 transition-colors"
              >
                <span className="text-gray-900 dark:text-white">Create Treaties</span>
                <ExternalLink className="h-4 w-4 text-gray-400" />
              </Link>
              
              <Link
                href={`/admin/manage/ordinances?userId=${createdUserId}`}
                className="flex items-center justify-between p-3 bg-white dark:bg-slate-700 border border-gray-200 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-slate-600 transition-colors"
              >
                <span className="text-gray-900 dark:text-white">Assign Ordinances</span>
                <ExternalLink className="h-4 w-4 text-gray-400" />
              </Link>
            </div>
          </div>
          
          <button
            onClick={() => {
              setCreatedUserId(null);
            }}
            className="px-6 py-2 bg-blue-800 text-white rounded-md hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            Create Another User
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 flex items-center">
          <UserPlus className="h-6 w-6 mr-2" />
          Create New User
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Create a new user account with basic information. Additional details can be added after creation.
        </p>
      </div>

      {/* Info Banner */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
          Streamlined User Creation
        </h3>
        <p className="text-xs text-blue-700 dark:text-blue-300">
          This form creates the core user account. Use the dedicated sections in the navigation to add:
          Contact Details, Identification, Positions, Treaties, and Ordinances.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <User className="h-5 w-5 mr-2" />
            Basic Information
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                First Name <span className="text-red-500">*</span>
              </label>
              <input
                {...register('firstName')}
                type="text"
                id="firstName"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Enter first name"
              />
              {errors.firstName && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.firstName.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Last Name <span className="text-red-500">*</span>
              </label>
              <input
                {...register('lastName')}
                type="text"
                id="lastName"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Enter last name"
              />
              {errors.lastName && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.lastName.message}</p>
              )}
            </div>
          </div>

          <div className="mt-6">
            <label htmlFor="dateOfBirth" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              <Calendar className="inline h-4 w-4 mr-1" />
              Date of Birth
            </label>
            <input
              {...register('dateOfBirth')}
              type="date"
              id="dateOfBirth"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div className="mt-6">
            <label htmlFor="bio" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Biography
            </label>
            <textarea
              {...register('bio')}
              id="bio"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Brief biography or description..."
            />
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <Mail className="h-5 w-5 mr-2" />
            Contact Information
          </h3>
          
          <div className="space-y-6">
            <div>
              <EmailInput
                name="email"
                label="Primary Email"
                placeholder="Enter email address"
                required={true}
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                This will be used for login and official communications
              </p>
            </div>

            <div>
              <EmailInput
                name="personalEmail"
                label="Personal Email"
                placeholder="Enter personal email address"
                required={false}
              />
            </div>

            <div>
              <EmailInput
                name="nwaEmail"
                label="NWA Email"
                placeholder="Enter NWA email address"
                required={false}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Phone className="inline h-4 w-4 mr-1" />
                  Phone Number
                </label>
                <input
                  {...register('phone')}
                  type="tel"
                  id="phone"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="+****************"
                />
              </div>

              <div>
                <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  <Phone className="inline h-4 w-4 mr-1" />
                  Mobile Number
                </label>
                <input
                  {...register('mobile')}
                  type="tel"
                  id="mobile"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="+****************"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 bg-blue-800 text-white rounded-md hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating User...
              </>
            ) : (
              <>
                <UserPlus className="h-4 w-4 mr-2" />
                Create User
                <ArrowRight className="h-4 w-4 ml-2" />
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};
