import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { FileText, Plus, Search, Calendar, AlertCircle, CheckCircle, Eye, ChevronDown, ChevronUp } from 'lucide-react';

interface Treaty {
  id: string;
  name: string;
  status: 'ACTIVE' | 'EXPIRED' | 'TERMINATED' | 'PENDING_RENEWAL';
  signedDate: string | null;
  expirationDate: string | null;
  renewalDate: string | null;
  treatyTypes: Array<{
    id: string;
    name: string;
  }>;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}

interface TreatyType {
  id: string;
  name: string;
  description: string | null;
  category: string;
}

interface UserTreatyManagementProps {
  userId: string;
  userName: string;
}

export const UserTreatyManagement: React.FC<UserTreatyManagementProps> = ({ userId, userName }) => {
  const [treaties, setTreaties] = useState<Treaty[]>([]);
  const [availableTreatyTypes, setAvailableTreatyTypes] = useState<TreatyType[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showAddForm, setShowAddForm] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [expandedTreaties, setExpandedTreaties] = useState<Set<string>>(new Set());
  
  // Form state for adding new treaty
  const [newTreaty, setNewTreaty] = useState({
    name: '',
    description: '',
    treatyTypeIds: [] as string[],
    signedDate: '',
    expirationDate: '',
    renewalDate: '',
    notes: ''
  });



  const fetchTreaties = useCallback(async () => {
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(searchQuery && { search: searchQuery }),
        ...(statusFilter !== 'all' && { status: statusFilter })
      });

      const response = await fetch(`/api/users/${userId}/treaties?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch treaties');
      }
      
      const data = await response.json();
      setTreaties(data.treaties || []);
      setTotalPages(data.pagination?.totalPages || 1);
    } catch (error) {
      console.error('Error fetching treaties:', error);
      toast.error('Failed to load treaties');
    } finally {
      setLoading(false);
    }
  }, [userId, currentPage, searchQuery, statusFilter]);

  const fetchTreatyTypes = async () => {
    try {
      const response = await fetch('/api/treaty-types');
      
      if (!response.ok) {
        throw new Error('Failed to fetch treaty types');
      }
      
      const data = await response.json();
      setAvailableTreatyTypes(data.treatyTypes || []);
    } catch (error) {
      console.error('Error fetching treaty types:', error);
      toast.error('Failed to load treaty types');
    }
  };

  useEffect(() => {
    fetchTreaties();
    fetchTreatyTypes();
  }, [fetchTreaties]);

  const handleAddTreaty = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newTreaty.name.trim()) {
      toast.error('Treaty name is required');
      return;
    }

    try {
      const response = await fetch(`/api/users/${userId}/treaties`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newTreaty),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add treaty');
      }

      toast.success('Treaty added successfully');
      setShowAddForm(false);
      setNewTreaty({
        name: '',
        description: '',
        treatyTypeIds: [],
        signedDate: '',
        expirationDate: '',
        renewalDate: '',
        notes: ''
      });
      fetchTreaties();
    } catch (error: any) {
      console.error('Error adding treaty:', error);
      toast.error(error.message || 'Failed to add treaty');
    }
  };

  const handleRemoveTreaty = async (treatyId: string) => {
    if (!window.confirm('Are you sure you want to remove this treaty from the user?')) {
      return;
    }

    try {
      const response = await fetch(`/api/users/${userId}/treaties/${treatyId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove treaty');
      }

      toast.success('Treaty removed successfully');
      fetchTreaties();
    } catch (error: any) {
      console.error('Error removing treaty:', error);
      toast.error(error.message || 'Failed to remove treaty');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'EXPIRED':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'PENDING_RENEWAL':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'EXPIRED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'PENDING_RENEWAL':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const toggleTreatyExpansion = (treatyId: string) => {
    setExpandedTreaties(prev => {
      const newSet = new Set(prev);
      if (newSet.has(treatyId)) {
        newSet.delete(treatyId);
      } else {
        newSet.add(treatyId);
      }
      return newSet;
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search treaties..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
        >
          <option value="all">All Status</option>
          <option value="ACTIVE">Active</option>
          <option value="EXPIRED">Expired</option>
          <option value="PENDING_RENEWAL">Pending Renewal</option>
          <option value="TERMINATED">Terminated</option>
        </select>
      </div>

      {/* Add Treaty Button */}
      {!showAddForm && (
        <div className="flex justify-end">
          <button
            onClick={() => setShowAddForm(true)}
            className="px-4 py-2 bg-slate-600 text-white rounded-md hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 transition-colors duration-200 flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Treaty
          </button>
        </div>
      )}

      {/* Add Treaty Form */}
      {showAddForm && (
        <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4 flex items-center">
            <ChevronDown className="h-5 w-5 mr-2" />
            Add New Treaty
          </h3>
          
          <form onSubmit={handleAddTreaty} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Treaty Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={newTreaty.name}
                  onChange={(e) => setNewTreaty({ ...newTreaty, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Treaty Types
                </label>
                <select
                  multiple
                  value={newTreaty.treatyTypeIds}
                  onChange={(e) => {
                    const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
                    setNewTreaty({ ...newTreaty, treatyTypeIds: selectedOptions });
                  }}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                  size={4}
                >
                  {availableTreatyTypes.map(type => (
                    <option key={type.id} value={type.id}>
                      {type.name} ({type.category})
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">Hold Ctrl/Cmd to select multiple</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Signed Date
                </label>
                <input
                  type="date"
                  value={newTreaty.signedDate}
                  onChange={(e) => setNewTreaty({ ...newTreaty, signedDate: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Expiration Date
                </label>
                <input
                  type="date"
                  value={newTreaty.expirationDate}
                  onChange={(e) => setNewTreaty({ ...newTreaty, expirationDate: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                value={newTreaty.description}
                onChange={(e) => setNewTreaty({ ...newTreaty, description: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
              >
                Add Treaty
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Treaties List */}
      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
        {treaties.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No treaties found</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {searchQuery || statusFilter !== 'all' 
                ? 'Try adjusting your search or filter criteria.'
                : 'This user has no treaties assigned yet.'
              }
            </p>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {treaties.map((treaty) => (
              <li key={treaty.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(treaty.status)}
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {treaty.name}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(treaty.status)}`}>
                            {treaty.status.replace('_', ' ')}
                          </span>
                          {treaty.treatyTypes.length > 0 && (
                            <div className="flex items-center space-x-1">
                              {treaty.treatyTypes.length <= 3 ? (
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  {treaty.treatyTypes.map(type => type.name).join(', ')}
                                </span>
                              ) : (
                                <>
                                  <span className="text-xs text-gray-500 dark:text-gray-400">
                                    {treaty.treatyTypes.slice(0, 2).map(type => type.name).join(', ')}
                                  </span>
                                  <button
                                    onClick={() => toggleTreatyExpansion(treaty.id)}
                                    className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center space-x-1"
                                    title="View all treaty types"
                                  >
                                    <Eye className="h-3 w-3" />
                                    <span>+{treaty.treatyTypes.length - 2} more</span>
                                  </button>
                                </>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {(treaty.signedDate || treaty.expirationDate) && (
                      <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                        {treaty.signedDate && (
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            Signed: {new Date(treaty.signedDate).toLocaleDateString()}
                          </div>
                        )}
                        {treaty.expirationDate && (
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            Expires: {new Date(treaty.expirationDate).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Expandable Treaty Types Details */}
                    {expandedTreaties.has(treaty.id) && treaty.treatyTypes.length > 3 && (
                      <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border">
                        <div className="flex items-center justify-end mb-2">
                          <button
                            onClick={() => toggleTreatyExpansion(treaty.id)}
                            className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
                          >
                            <ChevronUp className="h-4 w-4" />
                          </button>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {treaty.treatyTypes.map((type, index) => (
                            <span
                              key={type.id}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                            >
                              {type.name}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleRemoveTreaty(treaty.id)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 text-sm font-medium"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};