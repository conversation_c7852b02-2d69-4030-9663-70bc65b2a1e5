'use client';

import React, { useState, useRef } from 'react';
import { toast } from 'sonner';
import { Upload, FileText, Download, AlertCircle } from 'lucide-react';

export const BulkUserTab: React.FC = () => {
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [csvFileName, setCsvFileName] = useState('');
  const [bulkDataPreview, setBulkDataPreview] = useState<any[]>([]);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [adminConfirmation, setAdminConfirmation] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleCsvFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Check file type
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        toast.error('Invalid file type. Please upload a CSV file.');
        return;
      }

      // Check file size (10MB limit)
      if (selectedFile.size > 10 * 1024 * 1024) {
        toast.error('File size exceeds 10MB limit.');
        return;
      }

      setCsvFile(selectedFile);
      setCsvFileName(selectedFile.name);

      // In a real implementation, you would parse the CSV file and show a preview
      // For now, we'll just simulate this
      setBulkDataPreview([
        { id: 1, firstName: 'John', surname: 'Doe', email: '<EMAIL>', country: 'USA' },
        { id: 2, firstName: 'Jane', surname: 'Smith', email: '<EMAIL>', country: 'Canada' },
        { id: 3, firstName: 'Bob', surname: 'Johnson', email: '<EMAIL>', country: 'UK' },
      ]);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const removeCsvFile = () => {
    setCsvFile(null);
    setCsvFileName('');
    setBulkDataPreview([]);
    setIsPreviewMode(false);
    setAdminConfirmation('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleBulkUploadSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!csvFile) {
      toast.error('Please select a CSV file to upload.');
      return;
    }

    // Show preview mode
    setIsPreviewMode(true);
  };

  const handleConfirmBulkUpload = async () => {
    if (adminConfirmation.toLowerCase() !== 'insert data') {
      toast.error('Please type "insert data" to confirm.');
      return;
    }

    try {
      // In a real implementation, you would send the bulk data to the API
      // For now, we'll just show a success message
      toast.success('Bulk users uploaded successfully!');

      // Reset form
      setCsvFile(null);
      setCsvFileName('');
      setBulkDataPreview([]);
      setIsPreviewMode(false);
      setAdminConfirmation('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to upload bulk users');
      console.error('Error uploading bulk users:', error);
    }
  };

  const handleCancelBulkUpload = () => {
    setIsPreviewMode(false);
    setAdminConfirmation('');
  };

  const downloadTemplate = () => {
    // Create a sample CSV template
    const csvContent = `firstName,surname,email,personalEmail,nwaEmail,dob,phone,mobile,country,city,bio
John,Doe,<EMAIL>,<EMAIL>,<EMAIL>,1985-06-15,******-123-4567,******-987-6543,USA,New York,Sample bio
Jane,Smith,<EMAIL>,<EMAIL>,<EMAIL>,1990-08-22,******-234-5678,******-876-5432,Canada,Toronto,Sample bio`;

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'bulk_users_template.csv';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    toast.success('CSV template downloaded successfully!');
  };

  return (
    <div className="space-y-6">
      {/* Info Message */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center">
          <Upload className="h-4 w-4 mr-2" />
          Bulk User Upload
        </h3>
        <p className="text-xs text-blue-600 dark:text-blue-300">
          Upload multiple users at once using a CSV file. Download the template below to see the required format and column headers.
        </p>
      </div>

      {!isPreviewMode ? (
        /* Upload Form */
        <div className="bg-white dark:bg-slate-800 shadow rounded-lg">
          <form onSubmit={handleBulkUploadSubmit} className="p-6 space-y-6">
          {/* Download Template Button */}
          <div className="flex justify-center">
            <button
              type="button"
              onClick={downloadTemplate}
              className="flex items-center px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm text-sm font-medium text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
            >
              <Download className="h-4 w-4 mr-2" />
              Download CSV Template
            </button>
          </div>

          {/* File Upload Section */}
          <div className="border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg p-8">
            <div className="text-center">
              {!csvFile ? (
                <>
                  <Upload className="mx-auto h-12 w-12 text-slate-400" />
                  <h3 className="mt-2 text-sm font-medium text-slate-900 dark:text-white">Upload CSV file</h3>
                  <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                    Drag and drop your CSV file here, or click to browse
                  </p>
                  <div className="mt-4">
                    <button
                      type="button"
                      onClick={triggerFileInput}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-slate-700 hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Select CSV File
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <FileText className="mx-auto h-12 w-12 text-green-500" />
                  <h3 className="mt-2 text-sm font-medium text-slate-900 dark:text-white">File selected</h3>
                  <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                    {csvFileName} ({(csvFile.size / 1024).toFixed(2)} KB)
                  </p>
                  <div className="mt-4 flex justify-center space-x-3">
                    <button
                      type="button"
                      onClick={removeCsvFile}
                      className="inline-flex items-center px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm text-sm font-medium text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                    >
                      Remove File
                    </button>
                    <button
                      type="button"
                      onClick={triggerFileInput}
                      className="inline-flex items-center px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm text-sm font-medium text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                    >
                      Change File
                    </button>
                  </div>
                </>
              )}
            </div>

            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleCsvFileChange}
              className="hidden"
            />
          </div>

          {/* File Requirements */}
          <div className="bg-slate-50 dark:bg-slate-700/30 border border-slate-200 dark:border-slate-600 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-slate-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-slate-800 dark:text-slate-200">
                  File Requirements
                </h3>
                <div className="mt-2 text-sm text-slate-700 dark:text-slate-300">
                  <ul className="list-disc list-inside space-y-1">
                    <li>File must be in CSV format (.csv)</li>
                    <li>Maximum file size: 10MB</li>
                    <li>Required columns: firstName, surname, email</li>
                    <li>Use the template above for proper formatting</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={!csvFile}
              className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              Preview & Upload
            </button>
          </div>
          </form>
        </div>
      ) : (
        /* Preview Mode */
        <div className="bg-white dark:bg-slate-800 shadow rounded-lg">
          <div className="p-6 space-y-6">
            <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
              <h3 className="text-sm font-medium text-orange-800 dark:text-orange-200 mb-2">
                Review Data Before Upload
              </h3>
              <p className="text-xs text-orange-700 dark:text-orange-300">
                Please review the data below before confirming the upload. This action cannot be undone.
              </p>
            </div>

          {/* Data Preview */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    First Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Surname
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Country
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {bulkDataPreview.map((user, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {user.firstName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {user.surname}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {user.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {user.country}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Confirmation */}
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
              Confirmation Required
            </h3>
            <p className="text-xs text-red-700 dark:text-red-300 mb-4">
              Type &quot;insert data&quot; below to confirm the bulk upload of {bulkDataPreview.length} users.
            </p>
            <input
              type="text"
              value={adminConfirmation}
              onChange={(e) => setAdminConfirmation(e.target.value)}
              placeholder="Type 'insert data' to confirm"
              className="w-full px-3 py-2 border border-red-300 dark:border-red-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-red-800/20 dark:text-white"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleCancelBulkUpload}
              className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleConfirmBulkUpload}
              className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
            >
              Confirm Upload
            </button>
          </div>
          </div>
        </div>
      )}
    </div>
  );
};
