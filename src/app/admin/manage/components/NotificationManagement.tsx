'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Bell,
  Search,
  Plus,
  Filter,
  Download,
  <PERSON>Check,
  Trash2,
  Eye,
  EyeOff,
  RefreshCw,
  BarChart3,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  Users
} from 'lucide-react'
import { toast } from 'sonner'
import { useSession } from 'next-auth/react'

interface Notification {
  id: string
  type: string
  message: string
  recipients: string[]
  readBy: string[]
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  category: 'USER_MANAGEMENT' | 'TREATY' | 'REMOTE_SERVER' | 'SYSTEM' | 'SECURITY'
  trigger: string
  triggeredBy: string | null
  recipientTypes: any
  createdAt: string
  updatedAt: string
}

interface NotificationStats {
  total: number
  unread: number
  byPriority: Record<string, number>
  byCategory: Record<string, number>
  recent: number
}

export function NotificationManagement() {
  const { data: session } = useSession()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [stats, setStats] = useState<NotificationStats>({
    total: 0,
    unread: 0,
    byPriority: {},
    byCategory: {},
    recent: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([])

  // Filter states
  const [priorityFilter, setPriorityFilter] = useState<string>('ALL')
  const [categoryFilter, setCategoryFilter] = useState<string>('ALL')
  const [readStatusFilter, setReadStatusFilter] = useState<string>('ALL')
  const [dateRangeFilter, setDateRangeFilter] = useState<string>('ALL')

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [totalPages, setTotalPages] = useState(1)

  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(priorityFilter !== 'ALL' && { priority: priorityFilter }),
        ...(categoryFilter !== 'ALL' && { category: categoryFilter }),
        ...(readStatusFilter !== 'ALL' && { readStatus: readStatusFilter }),
        ...(dateRangeFilter !== 'ALL' && { dateRange: dateRangeFilter }),
      })

      const response = await fetch(`/api/admin/notifications?${params}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          setError('Authentication required. Please log in and try again.')
          return
        }
        if (response.status === 403) {
          setError('Notification management is only available to administrators')
          return
        }
        throw new Error(`Failed to fetch notifications (HTTP ${response.status})`)
      }

      const data = await response.json()

      setNotifications(data.notifications || [])
      setStats({
        total: data.pagination?.totalCount || 0,
        unread: data.unreadCount || 0,
        byPriority: data.stats?.byPriority || {},
        byCategory: data.stats?.byCategory || {},
        recent: data.stats?.recent || 0
      })
      setTotalPages(data.pagination?.totalPages || 1)
    } catch (error) {
      setError('Failed to load notifications')
      console.error('Error fetching notifications:', error)
    } finally {
      setLoading(false)
    }
  }, [currentPage, pageSize, searchTerm, priorityFilter, categoryFilter, readStatusFilter, dateRangeFilter])

  useEffect(() => {
    if (session?.user) {
      fetchNotifications()
    }
  }, [session, fetchNotifications])

  const handleBulkMarkAsRead = async () => {
    if (selectedNotifications.length === 0) {
      toast.error('Please select notifications to mark as read')
      return
    }

    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notificationIds: selectedNotifications,
          markAll: false
        }),
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('Failed to mark notifications as read')
      }

      toast.success(`Marked ${selectedNotifications.length} notifications as read`)
      setSelectedNotifications([])
      fetchNotifications()
    } catch (error) {
      toast.error('Failed to mark notifications as read')
      console.error('Error marking notifications as read:', error)
    }
  }

  const handleBulkDelete = async () => {
    if (selectedNotifications.length === 0) {
      toast.error('Please select notifications to delete')
      return
    }

    if (!confirm(`Are you sure you want to delete ${selectedNotifications.length} notifications?`)) {
      return
    }

    try {
      // Note: This would need a DELETE endpoint to be implemented
      toast.info('Bulk delete functionality requires additional API endpoint')
    } catch (error) {
      toast.error('Failed to delete notifications')
      console.error('Error deleting notifications:', error)
    }
  }

  const handleExport = () => {
    const csvContent = [
      ['ID', 'Type', 'Message', 'Priority', 'Category', 'Recipients', 'Created At', 'Read By'].join(','),
      ...notifications.map(n => [
        n.id,
        n.type,
        `"${n.message.replace(/"/g, '""')}"`,
        n.priority,
        n.category,
        `"${n.recipients.join('; ')}"`,
        n.createdAt,
        `"${n.readBy.join('; ')}"`
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `notifications-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
    toast.success('Notifications exported successfully')
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100'
      case 'HIGH': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-100'
      case 'NORMAL': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100'
      case 'LOW': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-100'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-100'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'SECURITY': return <AlertCircle className="w-4 h-4" />
      case 'USER_MANAGEMENT': return <Users className="w-4 h-4" />
      case 'TREATY': return <CheckCircle className="w-4 h-4" />
      case 'REMOTE_SERVER': return <RefreshCw className="w-4 h-4" />
      case 'SYSTEM': return <Clock className="w-4 h-4" />
      default: return <Bell className="w-4 h-4" />
    }
  }

  const filteredNotifications = notifications.filter(notification => {
    // Additional client-side filtering if needed
    return true
  })

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading notifications...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Notification Management</h2>
        <div className="flex gap-2">
          <Button variant="default" size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Create Notification
          </Button>
          <Button onClick={fetchNotifications} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={handleExport} variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Statistics Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Bell className="h-8 w-8 text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Notifications</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Eye className="h-8 w-8 text-orange-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Unread</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.unread}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <BarChart3 className="h-8 w-8 text-green-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Recent (24h)</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.recent}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-purple-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Read Rate</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.total > 0 ? Math.round(((stats.total - stats.unread) / stats.total) * 100) : 0}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <Filter className="h-5 w-5 mr-2" />
          Filters and Search
        </h3>
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <Input
                type="text"
                placeholder="Search notifications..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-slate-700"
            >
              <option value="ALL">All Priorities</option>
              <option value="URGENT">Urgent</option>
              <option value="HIGH">High</option>
              <option value="NORMAL">Normal</option>
              <option value="LOW">Low</option>
            </select>

            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-slate-700"
            >
              <option value="ALL">All Categories</option>
              <option value="SECURITY">Security</option>
              <option value="USER_MANAGEMENT">User Management</option>
              <option value="TREATY">Treaty</option>
              <option value="REMOTE_SERVER">Remote Server</option>
              <option value="SYSTEM">System</option>
            </select>

            <select
              value={readStatusFilter}
              onChange={(e) => setReadStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-slate-700"
            >
              <option value="ALL">All Status</option>
              <option value="UNREAD">Unread</option>
              <option value="READ">Read</option>
            </select>

            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar className="h-4 w-4 text-gray-400" />
              </div>
              <select
                value={dateRangeFilter}
                onChange={(e) => setDateRangeFilter(e.target.value)}
                className="pl-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-slate-700 appearance-none"
              >
              <option value="ALL">All Time</option>
              <option value="TODAY">Today</option>
              <option value="WEEK">This Week</option>
              <option value="MONTH">This Month</option>
              <option value="YEAR">This Year</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedNotifications.length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700 dark:text-blue-300">
              {selectedNotifications.length} notification{selectedNotifications.length !== 1 ? 's' : ''} selected
            </span>
            <div className="flex gap-2">
              <Button onClick={handleBulkMarkAsRead} size="sm" variant="outline">
                <CheckCheck className="w-4 h-4 mr-2" />
                Mark as Read
              </Button>
              <Button onClick={handleBulkDelete} size="sm" variant="outline">
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Notifications Table */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={selectedNotifications.length === notifications.length && notifications.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedNotifications(notifications.map(n => n.id))
                      } else {
                        setSelectedNotifications([])
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Message
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Priority
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Category
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Recipients
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Created
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredNotifications.length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    {searchTerm || priorityFilter !== 'ALL' || categoryFilter !== 'ALL' || readStatusFilter !== 'ALL'
                      ? 'No notifications match your search criteria.'
                      : 'No notifications found.'}
                  </td>
                </tr>
              ) : (
                filteredNotifications.map((notification) => (
                  <tr key={notification.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedNotifications.includes(notification.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedNotifications([...selectedNotifications, notification.id])
                          } else {
                            setSelectedNotifications(selectedNotifications.filter(id => id !== notification.id))
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {notification.readBy.length > 0 ? (
                        <Eye className="w-4 h-4 text-green-500" />
                      ) : (
                        <EyeOff className="w-4 h-4 text-gray-400" />
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {notification.type}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {notification.trigger}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 dark:text-white max-w-xs truncate">
                        {notification.message}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(notification.priority)}`}>
                        {notification.priority}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900 dark:text-white">
                        {getCategoryIcon(notification.category)}
                        <span className="ml-2">{notification.category}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {notification.recipients.length} recipient{notification.recipients.length !== 1 ? 's' : ''}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(notification.createdAt).toLocaleDateString()}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white dark:bg-slate-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
            <div className="flex-1 flex justify-between sm:hidden">
              <Button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                variant="outline"
                size="sm"
              >
                Previous
              </Button>
              <Button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                variant="outline"
                size="sm"
              >
                Next
              </Button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Showing <span className="font-medium">{((currentPage - 1) * pageSize) + 1}</span> to{' '}
                  <span className="font-medium">{Math.min(currentPage * pageSize, stats.total)}</span> of{' '}
                  <span className="font-medium">{stats.total}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <Button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    variant="outline"
                    size="sm"
                    className="rounded-r-none"
                  >
                    Previous
                  </Button>
                  <Button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    variant="outline"
                    size="sm"
                    className="rounded-l-none"
                  >
                    Next
                  </Button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}