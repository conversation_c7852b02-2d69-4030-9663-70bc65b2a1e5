'use client';

import React, { useState } from 'react';
import { toast } from 'sonner';
import { User, Mail, Calendar, FileText } from 'lucide-react';

interface UserFormData {
  firstName: string;
  surname: string;
  dob: string;
  email: string;
  personalEmail: string;
  nwaEmail: string;
  phone: string;
  mobile: string;
  bio: string;
}

export const SimpleCreateUserTab: React.FC = () => {
  const [userData, setUserData] = useState<UserFormData>({
    firstName: '',
    surname: '',
    dob: '',
    email: '',
    personalEmail: '',
    nwaEmail: '',
    phone: '',
    mobile: '',
    bio: '',
  });

  const [saving, setSaving] = useState(false);

  const handleUserChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setUserData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    if (!userData.firstName.trim() || !userData.surname.trim() || !userData.email.trim()) {
      toast.error('Please fill in all required fields (First Name, Surname, Email)');
      setSaving(false);
      return;
    }

    try {
      // Here you would typically send the user data to your API
      // For now, we'll just simulate a save
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('User created successfully!');
      
      // Reset form
      setUserData({
        firstName: '',
        surname: '',
        dob: '',
        email: '',
        personalEmail: '',
        nwaEmail: '',
        phone: '',
        mobile: '',
        bio: '',
      });
    } catch (error) {
      toast.error('Failed to create user');
      console.error('Error creating user:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Info Message */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center">
          <User className="h-4 w-4 mr-2" />
          Create New User
        </h3>
        <p className="text-xs text-blue-600 dark:text-blue-300">
          Enter the basic user information to create a new account. Additional details like contact information, identification, and positions can be added from their respective management pages.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              First Name <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <User className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={userData.firstName}
                onChange={handleUserChange}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                required
                placeholder="Enter first name"
              />
            </div>
          </div>

          <div>
            <label htmlFor="surname" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Surname <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <User className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                id="surname"
                name="surname"
                value={userData.surname}
                onChange={handleUserChange}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                required
                placeholder="Enter surname"
              />
            </div>
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Email <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Mail className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="email"
                id="email"
                name="email"
                value={userData.email}
                onChange={handleUserChange}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                required
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div>
            <label htmlFor="dob" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Date of Birth
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="date"
                id="dob"
                name="dob"
                value={userData.dob}
                onChange={handleUserChange}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div>
            <label htmlFor="personalEmail" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Personal Email
            </label>
            <input
              type="email"
              id="personalEmail"
              name="personalEmail"
              value={userData.personalEmail}
              onChange={handleUserChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="nwaEmail" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              NWA Email
            </label>
            <input
              type="email"
              id="nwaEmail"
              name="nwaEmail"
              value={userData.nwaEmail}
              onChange={handleUserChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Phone
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={userData.phone}
              onChange={handleUserChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="+****************"
            />
          </div>

          <div>
            <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Mobile
            </label>
            <input
              type="tel"
              id="mobile"
              name="mobile"
              value={userData.mobile}
              onChange={handleUserChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="+****************"
            />
          </div>
        </div>

        <div>
          <label htmlFor="bio" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Biography
          </label>
          <div className="relative">
            <div className="absolute top-3 left-3 pointer-events-none">
              <FileText className="h-5 w-5 text-gray-400" />
            </div>
            <textarea
              id="bio"
              name="bio"
              value={userData.bio}
              onChange={handleUserChange}
              rows={4}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter a brief biography..."
            />
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <h4 className="text-sm font-medium text-green-800 dark:text-green-200 mb-2">Next Steps</h4>
          <p className="text-xs text-green-700 dark:text-green-300">
            After creating the user, you can add contact details, identification documents, positions, and other information using the respective management pages in the admin menu.
          </p>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => {
              setUserData({
                firstName: '',
                surname: '',
                dob: '',
                email: '',
                personalEmail: '',
                nwaEmail: '',
                phone: '',
                mobile: '',
                bio: '',
              });
              toast.success('Form cleared!');
            }}
            className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Clear Form
          </button>
          <button
            type="submit"
            disabled={saving}
            className="px-6 py-2 bg-blue-800 text-white rounded-md hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 transition-colors duration-200"
          >
            {saving ? 'Creating...' : 'Create User'}
          </button>
        </div>
      </form>
    </div>
  );
};
