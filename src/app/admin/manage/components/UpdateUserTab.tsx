'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { toast } from 'sonner';
import { useSearchParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Search, X, User, Mail, MapPin, IdCard, Briefcase, FileText, BookOpen, History, ChevronDown, ChevronUp, Globe } from 'lucide-react';
import {
   UserBasicInfoForm, type UserBasicInfoData,
   UserContactForm, type UserContactData,
   UserIdentificationForm, type UserIdentificationData,
   UserPositionsForm, type UserPositionData,
   UserTreatiesForm, type UserTreatyData,
   UserOrdinancesForm, type UserOrdinanceData
 } from '../../../../components/users/manage/shared';
import AuditLogDisplay from '../../../../components/users/AuditLogDisplay';

interface UserSearchResult {
  id: string;
  name: string;
  email: string;
  nwaEmail?: string;
  profile?: {
    firstName: string;
    lastName: string;
  } | null;
}

export const UpdateUserTab: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { data: session, status } = useSession();
  
  // Ref to track the last loaded user ID to prevent duplicate API calls
  const lastLoadedUserIdRef = React.useRef<string | null>(null);
  
  // Search and user selection state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [isUserSelected, setIsUserSelected] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingUserData, setLoadingUserData] = useState(false);
  const [authChecking, setAuthChecking] = useState(true);
  const [searchError, setSearchError] = useState<string | null>(null);
  
  // Tab state
  const [activeUpdateTab, setActiveUpdateTab] = useState<'user' | 'contact' | 'identification' | 'positions' | 'treaty' | 'ordinance'>('user');

  // Retry search function with exponential backoff
  const retrySearch = useCallback(async (attempt = 1) => {
    if (!searchQuery || searchQuery.length <= 2 || status !== 'authenticated') return;
    
    try {
      const response = await fetch(`/api/users/search?q=${encodeURIComponent(searchQuery)}&limit=10`, {
        signal: AbortSignal.timeout(10000) // 10 second timeout
      });
      
      if (!response.ok) {
        if (response.status === 400) {
          setSearchResults([]);
          setShowSearchResults(false);
          return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setSearchResults(data.users || []);
      setShowSearchResults((data.users || []).length > 0);
      setSearchError(null);
    } catch (error: any) {
      console.error(`Search attempt ${attempt} failed:`, error);
      
      if (attempt < 3) {
        // Exponential backoff: 1s, 2s, 4s
        const delay = Math.pow(2, attempt) * 1000;
        setTimeout(() => retrySearch(attempt + 1), delay);
      } else {
        // Max retries reached
        let errorMessage = 'Failed to search users';
        if (error.name === 'AbortError') {
          errorMessage = 'Search request timed out. Please try again.';
        } else if (error.message.includes('Failed to fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (error.message.includes('HTTP')) {
          errorMessage = `Server error: ${error.message}`;
        }
        
        setSearchError(errorMessage);
        setSearchResults([]);
        setShowSearchResults(false);
        toast.error(errorMessage);
      }
    }
  }, [searchQuery, status]);
  
  // Form data state organized by sections
  const [basicInfo, setBasicInfo] = useState<UserBasicInfoData>({
    firstName: '',
    surname: '',
    dob: '',
    personalEmail: '',
    nwaEmail: '',
    phone: '',
    mobile: '',
    bio: ''
  });

  const [contactInfo, setContactInfo] = useState<UserContactData>({
    streetAddress1: '',
    streetAddress2: '',
    town: '',
    city: '',
    country: '',
    postalCode: '',
    regionId: '',
    regionText: ''
  });

  const [identificationInfo, setIdentificationInfo] = useState<UserIdentificationData>({
    nationOrTribeId: '',
    nationOrTribeName: '',
    nationOrTribeType: null,
    ambassadorialTitles: [],
    identifications: [],
    treatyNumbers: [],
    treatyTypeDetails: [],
    peaceAmbassadorNumber: ''
  });

  const [positionInfo, setPositionInfo] = useState<UserPositionData>({
    titleId: '',
    positionId: ''
  });

  const [treatyInfo, setTreatyInfo] = useState<UserTreatyData>({
    treatyEntries: []
  });

  const [ordinanceInfo, setOrdinanceInfo] = useState<UserOrdinanceData>({
    selectedOrdinances: []
  });


  const [purchasedTreatyIds, setPurchasedTreatyIds] = useState<string[]>([]);

  // Change tracking
  const [dirtyTabs, setDirtyTabs] = useState<Set<string>>(new Set());
  const [originalData, setOriginalData] = useState<any>(null);
  
  // Audit log state
  const [showAuditLog, setShowAuditLog] = useState(false);


  // Update URL when user or tab changes
  const updateURL = React.useCallback((userId?: string, tab?: string) => {
    const params = new URLSearchParams();
    if (userId) params.set('userId', userId);
    if (tab) params.set('tab', tab);
    
    const newURL = params.toString() ? `?${params.toString()}` : '/admin/manage/updateuser';
    router.replace(newURL, { scroll: false });
  }, [router]);

  // Debounced search for users
  useEffect(() => {
    if (searchQuery.length > 2 && !isUserSelected) {
      const searchUsers = async () => {
        if (status !== 'authenticated') return;
        
        setLoading(true);
        setSearchError(null); // Clear previous errors
        
        try {
          const response = await fetch(`/api/users/search?q=${encodeURIComponent(searchQuery)}&limit=10`, {
            signal: AbortSignal.timeout(10000) // 10 second timeout
          });
          
          if (!response.ok) {
            if (response.status === 400) {
              setSearchResults([]);
              setShowSearchResults(false);
              return;
            }
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          setSearchResults(data.users || []);
          setShowSearchResults((data.users || []).length > 0);
        } catch (error: any) {
          console.error('Error searching users:', error);
          
          let errorMessage = 'Failed to search users';
          if (error.name === 'AbortError') {
            errorMessage = 'Search request timed out. Please try again.';
          } else if (error.message.includes('Failed to fetch')) {
            errorMessage = 'Network error. Please check your connection and try again.';
          } else if (error.message.includes('HTTP')) {
            errorMessage = `Server error: ${error.message}`;
          }
          
          setSearchError(errorMessage);
          setSearchResults([]);
          setShowSearchResults(false);
          toast.error(errorMessage);
        } finally {
          setLoading(false);
        }
      };

      const timeoutId = setTimeout(searchUsers, 300); // Debounce
      return () => clearTimeout(timeoutId);
    } else {
      setSearchResults([]);
      setShowSearchResults(false);
      setSearchError(null);
    }
  }, [searchQuery, status, isUserSelected]);

  // Load user data when selectedUserId changes - FIXED VERSION
  useEffect(() => {
    if (status !== 'authenticated' || !selectedUserId) return;
    
    // Prevent duplicate API calls for the same user
    if (lastLoadedUserIdRef.current === selectedUserId) {
      return;
    }
    
    lastLoadedUserIdRef.current = selectedUserId;
    
    // Define the async function inside the useEffect to avoid dependency issues
    const loadUserDataForUser = async () => {
      setLoadingUserData(true);
      try {
        const response = await fetch(`/api/users/${selectedUserId}`);
        
        if (!response.ok) {
          const errorText = await response.text();
          console.error('API Error:', response.status, errorText);
          throw new Error(`Failed to load user data: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        const user = data.user;

        // Debug: Log the full user response
        console.log('User data from API:', user);
        console.log('User profile:', user.profile);
        console.log('Email fields check:', {
          profileEmail: user.profile?.personalEmail,
          nwaEmail: user.profile?.nwaEmail,
          mainEmail: user.email
        });
        
        // Populate form data from user with fallbacks for users without profiles
        const basicData: UserBasicInfoData = {
          firstName: user.profile?.firstName || user.name?.split(' ')[0] || '',
          surname: user.profile?.lastName || user.name?.split(' ').slice(1).join(' ') || '',
          dob: user.profile?.dateOfBirth ? user.profile.dateOfBirth.split('T')[0] : '',
          personalEmail: user.profile?.personalEmail || '',
          nwaEmail: user.profile?.nwaEmail || user.email, // Use main email as fallback
          phone: user.profile?.phone || '',
          mobile: user.profile?.mobile || '',
          bio: user.profile?.bio || ''
        };

        const contactData: UserContactData = {
          streetAddress1: user.profile?.streetAddress1 || '',
          streetAddress2: user.profile?.streetAddress2 || '',
          town: user.profile?.town || '',
          city: user.profile?.city?.name || '',
          country: user.profile?.country?.name || '',
          postalCode: user.profile?.postalCode || '',
          regionId: user.profile?.regionId?.toString() || '',
          regionText: user.profile?.regionText || ''
        };

        const peaceAmbassadorNumber = user.profile?.peaceAmbassadorNumber || '';
        console.log('DEBUG: Peace Ambassador Number mapping:', {
          profileExists: !!user.profile,
          profilePeaceAmbassadorNumber: user.profile?.peaceAmbassadorNumber,
          finalValue: peaceAmbassadorNumber
        });

        // Create ambassadorial titles from user's profile title if it exists and is ambassadorial
        const ambassadorialTitles = [];
        console.log('DEBUG: Creating ambassadorial titles from profile:', {
          hasProfile: !!user.profile,
          hasTitle: !!user.profile?.title,
          isAmbassadorial: user.profile?.title?.isAmbassadorial,
          titleId: user.profile?.titleId,
          titleName: user.profile?.title?.name,
          peaceAmbassadorNumber: user.profile?.peaceAmbassadorNumber
        });
        
        if (user.profile?.title?.isAmbassadorial && user.profile.titleId) {
          const ambassadorialTitle = {
            id: `title-${user.profile.titleId}`,
            titleId: user.profile.titleId,
            titleName: user.profile.title.name,
            identificationNumber: user.profile.peaceAmbassadorNumber || '',
            isActive: true,
            issuedDate: undefined, // Could be added to profile in future
            expiryDate: undefined  // Could be added to profile in future
          };
          ambassadorialTitles.push(ambassadorialTitle);
          console.log('DEBUG: Created ambassadorial title:', ambassadorialTitle);
        } else {
          console.log('DEBUG: User does not have an ambassadorial title');
        }

        const identificationData: UserIdentificationData = {
          nationOrTribeId: user.nationId || user.tribeId || '',
          nationOrTribeName: user.nationTreaty?.name || user.tribeTreaty?.name || '',
          nationOrTribeOfficialName: user.nationTreaty?.officialName || user.tribeTreaty?.officialName || '',
          nationOrTribeType: user.nationTreaty ? 'nation' : user.tribeTreaty ? 'tribe' : null,
          ambassadorialTitles, // Populated from user's profile title
          identifications: user.identifications || [],
          treatyNumbers: user.treatyNumbers?.map((treatyNumber: any) => ({
            id: treatyNumber.id,
            treatyId: treatyNumber.treatyTypeId, // Map treatyTypeId to treatyId for the interface
            treatyNumber: treatyNumber.treatyNumber,
            treatyName: treatyNumber.treatyType?.name
          })) || [],
          treatyTypeDetails: user.treatyTypeDetails || [],
          peaceAmbassadorNumber,
          titleName: user.profile?.title?.name || undefined // Add the title name for dynamic field label
        };
        
        console.log('DEBUG: Final identificationData:', {
          ambassadorialTitles: identificationData.ambassadorialTitles,
          ambassadorialTitlesLength: identificationData.ambassadorialTitles.length,
          peaceAmbassadorNumber: identificationData.peaceAmbassadorNumber,
          titleName: identificationData.titleName
        });

        const positionData: UserPositionData = {
          titleId: user.userPositions?.[0]?.titleId || '',
          positionId: user.userPositions?.[0]?.positionId || ''
        };

        const treatyData: UserTreatyData = {
          treatyEntries: user.treaties || []
        };

        // Extract purchased treaty IDs for the treaties form
        const purchasedTreatyIds = user.treatyNumbers?.map((treatyNumber: any) => treatyNumber.treatyTypeId) || [];
        setPurchasedTreatyIds(purchasedTreatyIds);
        
        // Debug: Log what we're loading
        console.log('DEBUG: Loading treaty numbers for user:', {
          userId: selectedUserId,
          treatyNumbers: user.treatyNumbers,
          purchasedTreatyIds,
          identificationData: {
            treatyNumbers: user.treatyNumbers?.map((treatyNumber: any) => ({
              id: treatyNumber.id,
              treatyId: treatyNumber.treatyTypeId,
              treatyNumber: treatyNumber.treatyNumber
            }))
          }
        });

        const ordinanceData: UserOrdinanceData = {
          selectedOrdinances: user.ordinances?.map((ordinance: any) => ordinance.id) || []
        };


        // Set all form data
        console.log('DEBUG: Setting form data:', { 
          basicData, 
          contactData,
          identificationData: {
            peaceAmbassadorNumber: identificationData.peaceAmbassadorNumber
          },
          emailFields: {
            personalEmail: basicData.personalEmail,
            nwaEmail: basicData.nwaEmail
          }
        });
        setBasicInfo(basicData);
        setContactInfo(contactData);
        setIdentificationInfo(identificationData);
        setPositionInfo(positionData);
        setTreatyInfo(treatyData);
        setOrdinanceInfo(ordinanceData);

        // Store original data for change tracking
        setOriginalData({
          basicInfo: basicData,
          contactInfo: contactData,
          identificationInfo: identificationData,
          positionInfo: positionData,
          treatyInfo: treatyData,
          ordinanceInfo: ordinanceData
        });

        // Clear dirty tabs since we just loaded fresh data
        setDirtyTabs(new Set());

        toast.success('User data loaded successfully');
      } catch (error) {
        console.error('Error loading user data:', error);
        toast.error('Failed to load user data');
      } finally {
        setLoadingUserData(false);
      }
    };

    loadUserDataForUser();
  }, [selectedUserId, status]); // Removed ordinanceInfo dependency to prevent circular dependency

  // Handle authentication state
  useEffect(() => {
    if (status !== 'loading') {
      setAuthChecking(false);
    }
  }, [status]);

  // Handle deep linking via query parameters
  useEffect(() => {
    if (status !== 'authenticated') return;
    
    if (!searchParams) return; // Ensure searchParams is not null
    const userId = searchParams.get('userId');
    const tab = searchParams.get('tab') as typeof activeUpdateTab;
    
    // Only set from URL if we don't already have a selected user (avoid overriding user selection)
    if (userId && !selectedUserId) {
      setSelectedUserId(userId);
    }
    
    if (tab && ['user', 'contact', 'identification', 'positions', 'treaty', 'ordinance'].includes(tab)) {
      setActiveUpdateTab(tab);
    }
  }, [searchParams, selectedUserId, activeUpdateTab, status]);

  // Handle user selection from search results
  const handleUserSelect = useCallback((user: UserSearchResult) => {
    setIsUserSelected(true);
    setSelectedUserId(user.id);
    setSearchQuery(user.name || user.email);
    setShowSearchResults(false);
    updateURL(user.id, activeUpdateTab);
    
    // Reset the flag after a short delay to allow for URL updates
    setTimeout(() => setIsUserSelected(false), 1000);
  }, [activeUpdateTab, updateURL]);

  // Handle clearing the selected user
  const handleClearUser = () => {
    setIsUserSelected(false);
    setSelectedUserId(null);
    setSearchQuery('');
    setShowSearchResults(false);
    lastLoadedUserIdRef.current = null;
    
    // Reset all form data
    setBasicInfo({
      firstName: '',
      surname: '',
      dob: '',
      personalEmail: '',
      nwaEmail: '',
      phone: '',
      mobile: '',
      bio: ''
    });
    setContactInfo({
      streetAddress1: '',
      streetAddress2: '',
      town: '',
      city: '',
      country: '',
      postalCode: '',
      regionId: '',
      regionText: ''
    });
    setIdentificationInfo({
      nationOrTribeId: '',
      nationOrTribeName: '',
      nationOrTribeType: null,
      ambassadorialTitles: [],
      identifications: [],
      treatyNumbers: [],
      treatyTypeDetails: [],
      peaceAmbassadorNumber: ''
    });
    setPositionInfo({
      titleId: '',
      positionId: ''
    });
    setTreatyInfo({
      treatyEntries: []
    });
    setPurchasedTreatyIds([]);
    setOrdinanceInfo({
      selectedOrdinances: []
    });
    
    setOriginalData(null);
    setDirtyTabs(new Set());
  };

  // Handle form section state changes with dirty tracking
  const markTabDirty = useCallback((tabName: string) => {
    setDirtyTabs(prev => new Set([...prev, tabName]));
  }, []);

  const handleBasicInfoChange = useCallback((data: UserBasicInfoData) => {
    setBasicInfo(data);
    markTabDirty('user');
  }, [markTabDirty]);

  const handleContactInfoChange = useCallback((data: UserContactData) => {
    setContactInfo(data);
    markTabDirty('contact');
  }, [markTabDirty]);

  const handleIdentificationInfoChange = useCallback((data: UserIdentificationData) => {
    setIdentificationInfo(data);
    markTabDirty('identification');
  }, [markTabDirty]);

  const handlePositionInfoChange = useCallback((data: UserPositionData) => {
    setPositionInfo(data);
    markTabDirty('positions');
  }, [markTabDirty]);

  const handleTreatyInfoChange = useCallback((data: UserTreatyData) => {
    setTreatyInfo(data);
    markTabDirty('treaty');
  }, [markTabDirty]);

  const handleOrdinanceInfoChange = useCallback((data: UserOrdinanceData) => {
    setOrdinanceInfo(data);
    markTabDirty('ordinance');
  }, [markTabDirty]);


  // Handle form section submissions (individual tab saves)
  const handleBasicInfoSave = React.useCallback(async (data: UserBasicInfoData) => {
    if (!selectedUserId || status !== 'authenticated') return;
    
    try {
      const response = await fetch(`/api/users/${selectedUserId}/basic`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update basic information');
      }

      const result = await response.json();
      setBasicInfo(data);
      setDirtyTabs(prev => {
        const newSet = new Set(prev);
        newSet.delete('user');
        return newSet;
      });
      toast.success('Basic information updated successfully!');
    } catch (error) {
      console.error('Error updating basic info:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update basic information');
    }
  }, [selectedUserId, status]);

  const handleContactInfoSave = React.useCallback(async (data: UserContactData) => {
    if (!selectedUserId || status !== 'authenticated') return;
    
    try {
      const response = await fetch(`/api/users/${selectedUserId}/contact`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update contact information');
      }

      const result = await response.json();
      setContactInfo(data);
      setDirtyTabs(prev => {
        const newSet = new Set(prev);
        newSet.delete('contact');
        return newSet;
      });
      toast.success('Contact information updated successfully!');
    } catch (error) {
      console.error('Error updating contact info:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update contact information');
    }
  }, [selectedUserId, status]);

  const handleIdentificationInfoSave = React.useCallback(async (data: UserIdentificationData) => {
    if (!selectedUserId || status !== 'authenticated') return;
    
    try {
      // Use existing identifications API endpoint
      const response = await fetch(`/api/users/${selectedUserId}/identifications`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ambassadorialTitles: data.ambassadorialTitles,
          identifications: data.identifications,
          treatyNumbers: data.treatyNumbers
        }),
      });

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch {
          errorData = {};
        }
        throw new Error(errorData.error || 'Failed to update identification information');
      }

      const result = await response.json();
      setIdentificationInfo(data);
      setDirtyTabs(prev => {
        const newSet = new Set(prev);
        newSet.delete('identification');
        return newSet;
      });
      toast.success('Identification information updated successfully!');
    } catch (error) {
      console.error('Error updating identification info:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update identification information');
    }
  }, [selectedUserId, status]);

  const handlePositionInfoSave = React.useCallback(async (data: UserPositionData) => {
    if (!selectedUserId || status !== 'authenticated') return;
    
    try {
      const response = await fetch(`/api/users/${selectedUserId}/positions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update position information');
      }

      const result = await response.json();
      setPositionInfo(data);
      setDirtyTabs(prev => {
        const newSet = new Set(prev);
        newSet.delete('positions');
        return newSet;
      });
      toast.success('Position information updated successfully!');
    } catch (error) {
      console.error('Error updating position info:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update position information');
    }
  }, [selectedUserId, status]);

  const handleTreatyInfoSave = React.useCallback(async (data: UserTreatyData) => {
    if (!selectedUserId || status !== 'authenticated') return;
    
    try {
      // Use existing treaties API endpoint
      const response = await fetch(`/api/users/${selectedUserId}/treaties`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          treatyEntries: data.treatyEntries
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update treaty information');
      }

      const result = await response.json();
      setTreatyInfo(data);
      setDirtyTabs(prev => {
        const newSet = new Set(prev);
        newSet.delete('treaty');
        return newSet;
      });
      toast.success('Treaty information updated successfully!');
    } catch (error) {
      console.error('Error updating treaty info:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update treaty information');
    }
  }, [selectedUserId, status]);

  const handleOrdinanceInfoSave = React.useCallback(async (data: UserOrdinanceData) => {
    if (!selectedUserId || status !== 'authenticated') return;

    try {
      const response = await fetch(`/api/users/${selectedUserId}/ordinances`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update ordinance information');
      }

      const result = await response.json();
      setOrdinanceInfo(data);
      setDirtyTabs(prev => {
        const newSet = new Set(prev);
        newSet.delete('ordinance');
        return newSet;
      });
      toast.success('Ordinance information updated successfully!');
    } catch (error) {
      console.error('Error updating ordinance info:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update ordinance information');
    }
  }, [selectedUserId, status]);


  // Bulk save all changes
  const handleBulkSave = React.useCallback(async () => {
    if (!selectedUserId || dirtyTabs.size === 0 || status !== 'authenticated') return;

    const savePromises = [];
    let hasErrors = false;

    try {
      // Save all dirty tabs
      if (dirtyTabs.has('user')) {
        savePromises.push(handleBasicInfoSave(basicInfo));
      }
      if (dirtyTabs.has('contact')) {
        savePromises.push(handleContactInfoSave(contactInfo));
      }
      if (dirtyTabs.has('identification')) {
        savePromises.push(handleIdentificationInfoSave(identificationInfo));
      }
      if (dirtyTabs.has('positions')) {
        savePromises.push(handlePositionInfoSave(positionInfo));
      }
      if (dirtyTabs.has('treaty')) {
        savePromises.push(handleTreatyInfoSave(treatyInfo));
      }
      if (dirtyTabs.has('ordinance')) {
        savePromises.push(handleOrdinanceInfoSave(ordinanceInfo));
      }

      await Promise.allSettled(savePromises);
      
      if (dirtyTabs.size === 0) {
        toast.success('All changes saved successfully!');
      } else {
        toast.warning('Some changes could not be saved. Please check individual tabs.');
      }
    } catch (error) {
      console.error('Error during bulk save:', error);
      toast.error('Failed to save some changes');
    }
  }, [selectedUserId, dirtyTabs, handleBasicInfoSave, handleContactInfoSave, handleIdentificationInfoSave, handlePositionInfoSave, handleTreatyInfoSave, handleOrdinanceInfoSave, basicInfo, contactInfo, identificationInfo, positionInfo, treatyInfo, ordinanceInfo, status]);

  // Check for unsaved changes before tab switch
  const handleTabSwitch = useCallback((newTab: typeof activeUpdateTab) => {
    if (dirtyTabs.size > 0) {
      const confirmSwitch = window.confirm(
        'You have unsaved changes. Do you want to switch tabs without saving?'
      );
      if (!confirmSwitch) {
        return;
      }
    }
    setActiveUpdateTab(newTab);
    updateURL(selectedUserId || undefined, newTab);
  }, [dirtyTabs.size, selectedUserId, updateURL]);

  // Memoize tab configuration for performance
  const tabConfig = useMemo(() => [
    { id: 'user', name: 'Update User', icon: User },
    { id: 'contact', name: 'Contact Details', icon: Mail },
    { id: 'identification', name: 'Identification', icon: MapPin },
    { id: 'positions', name: 'Positions', icon: Briefcase },
    { id: 'treaty', name: 'Treaties', icon: FileText },
    { id: 'ordinance', name: 'Ordinances', icon: BookOpen },
  ], []);

  // Warn before leaving page with unsaved changes
  React.useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (dirtyTabs.size > 0) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [dirtyTabs]);

  // Keyboard navigation
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + S to save current tab
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        if (selectedUserId) {
          // Save current tab based on activeUpdateTab
          switch (activeUpdateTab) {
            case 'user':
              handleBasicInfoSave(basicInfo);
              break;
            case 'contact':
              handleContactInfoSave(contactInfo);
              break;
            case 'identification':
              handleIdentificationInfoSave(identificationInfo);
              break;
            case 'positions':
              handlePositionInfoSave(positionInfo);
              break;
            case 'treaty':
              handleTreatyInfoSave(treatyInfo);
              break;
            case 'ordinance':
              handleOrdinanceInfoSave(ordinanceInfo);
              break;
          }
        }
      }
      
      // Ctrl/Cmd + Shift + S to bulk save
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'S') {
        e.preventDefault();
        handleBulkSave();
      }

      // Tab navigation with Ctrl/Cmd + number keys
      if ((e.ctrlKey || e.metaKey) && e.key >= '1' && e.key <= '6') {
        e.preventDefault();
        const tabIndex = parseInt(e.key) - 1;
        const targetTab = tabConfig[tabIndex];
        if (targetTab) {
          handleTabSwitch(targetTab.id as typeof activeUpdateTab);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedUserId, activeUpdateTab, basicInfo, contactInfo, identificationInfo, positionInfo, treatyInfo, ordinanceInfo, handleBulkSave, handleTabSwitch, tabConfig, handleBasicInfoSave, handleContactInfoSave, handleIdentificationInfoSave, handlePositionInfoSave, handleTreatyInfoSave, handleOrdinanceInfoSave]);

  // Show loading state while checking authentication
  if (authChecking) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show unauthorized state if not authenticated
  if (status === 'unauthenticated') {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Authentication Required</h3>
          <p className="text-gray-600 dark:text-gray-400">Please log in to access user management features.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search Section */}
      <div className="relative">
        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search for a user by name or email..."
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                if (searchError) {
                  setSearchError(null);
                }
              }}
              className={`w-full pl-10 pr-10 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white ${searchError ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'}`}
          />
          {searchQuery && (
            <button
              onClick={handleClearUser}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <X className="h-5 w-5 text-gray-400 hover:text-gray-600" />
            </button>
          )}
          {loading && (
            <div className="absolute inset-y-0 right-8 pr-3 flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            </div>
          )}
        </div>
        
        {/* Error Display */}
        {searchError && (
          <div className="mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800 dark:text-red-200">{searchError}</p>
                </div>
              </div>
              <div className="flex-shrink-0 ml-4">
                <button
                  onClick={() => retrySearch()}
                  className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 dark:text-red-200 bg-red-100 dark:bg-red-800 hover:bg-red-200 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Retry
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Search Results */}
        {showSearchResults && searchResults.length > 0 && (
          <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg">
            {searchResults.map((user) => (
              <button
                key={user.id}
                onMouseDown={() => handleUserSelect(user)}
                className="w-full px-4 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-600 last:border-b-0"
              >
                <div className="font-medium text-gray-900 dark:text-white">
                  {user.profile?.firstName && user.profile?.lastName 
                    ? `${user.profile.firstName} ${user.profile.lastName}`
                    : user.name || user.email}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">{user.email}</div>
                {user.nwaEmail && (
                  <div className="text-xs text-gray-400 dark:text-gray-500">{user.nwaEmail}</div>
                )}
              </button>
            ))}
          </div>
        )}
      </div>

        {loading && (
          <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg p-4">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-slate-600"></div>
              <span className="ml-2 text-gray-600 dark:text-gray-300">Searching...</span>
            </div>
          </div>
        )}
      </div>

      {/* Loading User Data with Skeleton */}
      {loadingUserData && (
        <div className="bg-white dark:bg-slate-800 shadow rounded-lg">
          {/* Tab Navigation Skeleton */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              {Array.from({ length: 7 }).map((_, index) => (
                <div key={index} className="py-4 px-1">
                  <div className="h-5 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              ))}
            </nav>
          </div>
          
          {/* Content Skeleton */}
          <div className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="space-y-2">
                  <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              ))}
            </div>
            <div className="flex justify-end">
              <div className="h-10 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      )}

      {/* Tabbed User Update Interface */}
      {selectedUserId && !loadingUserData && (
        <div className="bg-white dark:bg-slate-800 shadow rounded-lg">
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Globe className="h-5 w-5 mr-2" />
              Update User Information
            </h2>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              {tabConfig.map((tab) => {
                const Icon = tab.icon;
                const isActive = activeUpdateTab === tab.id;
                const isDirty = dirtyTabs.has(tab.id);
                
                return (
                  <button
                    key={tab.id}
                    onClick={() => handleTabSwitch(tab.id as any)}
                    className={`${
                      isActive
                        ? 'border-emerald-500 text-emerald-600 dark:text-emerald-400'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 relative focus:outline-none transition-all duration-200`}
                    role="tab"
                    aria-selected={isActive}
                    aria-controls={`tabpanel-${tab.id}`}
                    tabIndex={isActive ? 0 : -1}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{tab.name}</span>
                    {isDirty && (
                      <span className="absolute -top-1 -right-1 h-2 w-2 bg-orange-500 rounded-full"></span>
                    )}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Bulk Save Button */}
          {dirtyTabs.size > 0 && (
            <div className="px-6 py-3 bg-orange-50 dark:bg-orange-900/20 border-b border-orange-200 dark:border-orange-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 bg-orange-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-orange-700 dark:text-orange-300">
                    You have unsaved changes in {dirtyTabs.size} tab{dirtyTabs.size > 1 ? 's' : ''}
                  </span>
                </div>
                <button
                  onClick={handleBulkSave}
                  className="px-4 py-2 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors duration-200"
                >
                  Save All Changes
                </button>
              </div>
            </div>
          )}

          {/* Tab Content */}
          <div 
            className="p-6"
            role="tabpanel"
            id={`tabpanel-${activeUpdateTab}`}
            aria-labelledby={`tab-${activeUpdateTab}`}
          >
            {activeUpdateTab === 'user' ? (
              <UserBasicInfoForm
                mode="update"
                data={basicInfo}
                onChange={handleBasicInfoChange}
                onSave={handleBasicInfoSave}
              />
            ) : activeUpdateTab === 'contact' ? (
              <UserContactForm
                mode="update"
                data={contactInfo}
                onChange={handleContactInfoChange}
                onSave={handleContactInfoSave}
              />
            ) : activeUpdateTab === 'identification' ? (
              <UserIdentificationForm
                mode="update"
                data={identificationInfo}
                onChange={handleIdentificationInfoChange}
                onSave={handleIdentificationInfoSave}
              />
            ) : activeUpdateTab === 'positions' ? (
              <UserPositionsForm
                mode="update"
                data={positionInfo}
                onChange={handlePositionInfoChange}
                onSave={handlePositionInfoSave}
                currentUserId={selectedUserId}
              />
            ) : activeUpdateTab === 'treaty' ? (
              <UserTreatiesForm
                mode="update"
                data={treatyInfo}
                onChange={handleTreatyInfoChange}
                onSave={handleTreatyInfoSave}
                purchasedTreaties={purchasedTreatyIds}
              />
            ) : activeUpdateTab === 'ordinance' ? (
              <UserOrdinancesForm
                mode="update"
                data={ordinanceInfo}
                onChange={handleOrdinanceInfoChange}
                onSave={handleOrdinanceInfoSave}
              />
            ) : null}
          </div>
          
          {/* Audit Log Section */}
          <div className="border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={() => setShowAuditLog(!showAuditLog)}
              className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200"
            >
              <div className="flex items-center space-x-2">
                <History className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                <span className="font-medium text-gray-900 dark:text-white">
                  Audit Log
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  View change history
                </span>
              </div>
              {showAuditLog ? (
                <ChevronUp className="h-5 w-5 text-gray-400" />
              ) : (
                <ChevronDown className="h-5 w-5 text-gray-400" />
              )}
            </button>
            
            {showAuditLog && (
              <div className="px-6 pb-6">
                <AuditLogDisplay userId={selectedUserId} />
              </div>
            )}
          </div>
        </div>
      )}

      {/* No User Selected State */}
      {!selectedUserId && !loadingUserData && (
        <div className="text-center py-12">
          <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No user selected</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Search for a user above to update their information.
          </p>
        </div>
      )}
    </div>
  );
};