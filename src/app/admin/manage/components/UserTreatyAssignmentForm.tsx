'use client';

import React, { useState, useEffect, forwardRef, useImperativeHandle, useCallback } from 'react';
import { UserApplicantInformationSection } from './UserApplicantInformationSection';
import { UserResidentialDetailsSection } from './UserResidentialDetailsSection';
import { UserBusinessDetailsSection } from './UserBusinessDetailsSection';
import { UserProtectionItemsSection } from './UserProtectionItemsSection';
import { UserFinalConfirmationSection } from './UserFinalConfirmationSection';
import { UserProgressIndicator } from './UserProgressIndicator';
import { UserFormNavigation } from './UserFormNavigation';

import { UserTreatyAssignmentData } from '@/types/user-treaty-assignment';

interface UserTreatyAssignmentFormProps {
  userId: string;
  treatyId: string;
  treatyTypeId: string;
  onSubmit: (data: UserTreatyAssignmentData) => void;
  onSave: (data: UserTreatyAssignmentData) => void;
  onCancel: () => void;
  initialData?: UserTreatyAssignmentData;
}

const SECTIONS = [
  'applicant',
  'residential',
  'business',
  'protection',
  'confirmation'
] as const;

type SectionType = typeof SECTIONS[number];

export const UserTreatyAssignmentForm: React.FC<UserTreatyAssignmentFormProps> = ({
  userId,
  treatyId,
  treatyTypeId,
  onSubmit,
  onSave,
  onCancel,
  initialData
}) => {
  // Generate unique storage key for this form instance
  const storageKey = `user-treaty-form-${userId}-${treatyTypeId}`;

  const [currentSection, setCurrentSection] = useState<SectionType>('applicant');
  const [formData, setFormData] = useState<UserTreatyAssignmentData>(initialData || {});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load persisted data on component mount
  useEffect(() => {
    if (!initialData) {
      try {
        const savedData = localStorage.getItem(storageKey);
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          setFormData(parsedData.formData || {});
          setCurrentSection(parsedData.currentSection || 'applicant');
        }
      } catch (error) {
        console.warn('Failed to load saved form data:', error);
      }
    }
  }, [initialData, storageKey]);

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    try {
      const dataToSave = {
        formData,
        currentSection,
        lastSaved: new Date().toISOString()
      };
      localStorage.setItem(storageKey, JSON.stringify(dataToSave));
    } catch (error) {
      console.warn('Failed to save form data:', error);
    }
  }, [formData, currentSection, storageKey]);

  // Auto-save form data every 30 seconds
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      try {
        const dataToSave = {
          formData,
          currentSection,
          lastSaved: new Date().toISOString()
        };
        localStorage.setItem(storageKey, JSON.stringify(dataToSave));
      } catch (error) {
        console.warn('Failed to auto-save form data:', error);
      }
    }, 30000); // 30 seconds

    return () => clearInterval(autoSaveInterval);
  }, [formData, currentSection, storageKey]);


  const currentSectionIndex = SECTIONS.indexOf(currentSection);
  const isFirstSection = currentSectionIndex === 0;
  const isLastSection = currentSectionIndex === SECTIONS.length - 1;

  const handleDataChange = (newData: Partial<UserTreatyAssignmentData>) => {
    setFormData(prev => ({ ...prev, ...newData }));
    // Clear related errors when data changes
    const updatedErrors = { ...errors };
    Object.keys(newData).forEach(key => {
      if (updatedErrors[key]) {
        delete updatedErrors[key];
      }
    });
    setErrors(updatedErrors);
  };

  const validateCurrentSection = (): boolean => {
    const newErrors: Record<string, string> = {};

    switch (currentSection) {
      case 'applicant':
        if (!formData.fullLegalName?.trim()) {
          newErrors.fullLegalName = 'Full legal name is required';
        }
        if (!formData.email?.trim()) {
          newErrors.email = 'Email address is required';
        }
        break;

      case 'residential':
        if (!formData.residentialAddress?.trim()) {
          newErrors.residentialAddress = 'Residential address is required';
        }
        break;

      case 'confirmation':
        if (!formData.declarationAccepted) {
          newErrors.declarationAccepted = 'You must accept the declaration to submit';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateCurrentSection()) {
      const nextIndex = Math.min(currentSectionIndex + 1, SECTIONS.length - 1);
      setCurrentSection(SECTIONS[nextIndex]);
    }
  };

  const handlePrevious = () => {
    const prevIndex = Math.max(currentSectionIndex - 1, 0);
    setCurrentSection(SECTIONS[prevIndex]);
  };

  const handleSubmit = async () => {
    if (!validateCurrentSection()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      // Clear persisted data on successful submission
      localStorage.removeItem(storageKey);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const hasUnsavedChanges = useCallback((): boolean => {
    try {
      const savedData = localStorage.getItem(storageKey);
      if (!savedData) return false;

      const parsedData = JSON.parse(savedData);
      // Check if there are any meaningful changes
      const hasData = Object.keys(formData).some(key => {
        const value = formData[key as keyof UserTreatyAssignmentData];
        return value !== undefined && value !== '' && value !== null;
      });

      return hasData;
    } catch (error) {
      return false;
    }
  }, [formData, storageKey]);

  // Navigation guard - warn before leaving with unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges()) {
        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
        return e.returnValue;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [formData, hasUnsavedChanges]);

  const handleSave = async () => {
    try {
      await onSave(formData);
      // Update last saved timestamp
      const dataToSave = {
        formData,
        currentSection,
        lastSaved: new Date().toISOString()
      };
      localStorage.setItem(storageKey, JSON.stringify(dataToSave));
    } catch (error) {
      console.error('Form save error:', error);
    }
  };

  const handleCancel = () => {
    // Clear persisted data when canceling
    localStorage.removeItem(storageKey);
    onCancel();
  };

  const renderCurrentSection = () => {
    switch (currentSection) {
      case 'applicant':
        return (
          <UserApplicantInformationSection
            formData={formData}
            onChange={handleDataChange}
            errors={errors}
          />
        );

      case 'residential':
        return (
          <UserResidentialDetailsSection
            formData={formData}
            onChange={handleDataChange}
            errors={errors}
          />
        );

      case 'business':
        return (
          <UserBusinessDetailsSection
            formData={formData}
            onChange={handleDataChange}
            errors={errors}
          />
        );

      case 'protection':
        return (
          <UserProtectionItemsSection
            formData={formData}
            onChange={handleDataChange}
            errors={errors}
          />
        );

      case 'confirmation':
        return (
          <UserFinalConfirmationSection
            formData={formData}
            onChange={handleDataChange}
            onSubmit={handleSubmit}
            onSave={handleSave}
            errors={errors}
            isSubmitting={isSubmitting}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
      {/* Header Section */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-2">
          User Treaty Assignment
        </h1>
        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
          Please fill out all required information for this user&apos;s treaty assignment.
        </p>
      </div>

      {/* Progress Indicator */}
      <div className="mb-6">
        <UserProgressIndicator
          currentSection={currentSectionIndex + 1}
          totalSections={SECTIONS.length}
          sectionNames={[...SECTIONS]}
        />
      </div>

      {/* Form Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 sm:p-6 mb-6">
        <div className="max-w-none">
          {renderCurrentSection()}
        </div>
      </div>

      {/* Navigation */}
      <div className="sticky bottom-0 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 -mx-4 px-4 py-4 sm:relative sm:bg-transparent sm:border-0 sm:-mx-0 sm:px-0 sm:py-0">
        <UserFormNavigation
          onPrevious={handlePrevious}
          onNext={handleNext}
          onCancel={handleCancel}
          canGoNext={!isLastSection}
          canGoPrevious={!isFirstSection}
          isLastSection={isLastSection}
        />
      </div>
    </div>
  );
};