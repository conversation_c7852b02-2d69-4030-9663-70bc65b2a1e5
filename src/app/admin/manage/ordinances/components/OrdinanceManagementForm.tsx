'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { 
  BookOpen, 
  Plus, 
  X, 
  AlertCircle, 
  CheckCircle, 
  FileText,
  Users,
  Search,
  Upload
} from 'lucide-react';

interface Ordinance {
  id: string;
  name: string;
  description: string;
  isActive?: boolean;
  category?: string;
}

interface OrdinanceAssignment {
  id: string;
  userId: string;
  ordinanceId: string;
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING';
  assignedAt: string;
  ordinance: Ordinance;
}

interface OrdinanceManagementFormProps {
  userId?: string;
  mode?: 'assign' | 'create' | 'manage';
  onOrdinanceAssigned?: () => void;
  onOrdinanceCreated?: () => void;
}

export const OrdinanceManagementForm: React.FC<OrdinanceManagementFormProps> = ({
  userId,
  mode = 'assign',
  onOrdinanceAssigned,
  onOrdinanceCreated
}) => {
  const [availableOrdinances, setAvailableOrdinances] = useState<Ordinance[]>([]);
  const [selectedOrdinances, setSelectedOrdinances] = useState<string[]>([]);
  const [userOrdinances, setUserOrdinances] = useState<OrdinanceAssignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  // New ordinance creation state
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newOrdinance, setNewOrdinance] = useState({
    name: '',
    description: '',
    category: ''
  });

  const fetchUserOrdinances = useCallback(async () => {
    if (!userId) return;
    
    try {
      const response = await fetch(`/api/users/${userId}/ordinances`);
      if (response.ok) {
        const data = await response.json();
        setUserOrdinances(data.ordinances || []);
      }
    } catch (error) {
      console.error('Error fetching user ordinances:', error);
    }
  }, [userId]);

  useEffect(() => {
    fetchOrdinances();
    if (userId) {
      fetchUserOrdinances();
    }
  }, [userId, fetchUserOrdinances]);

  const fetchOrdinances = async () => {
    try {
      const response = await fetch('/api/ordinances');
      
      if (!response.ok) {
        if (response.status === 404) {
          setAvailableOrdinances([]);
          return;
        }
        throw new Error('Failed to fetch ordinance types');
      }

      const data = await response.json();
      setAvailableOrdinances(data.ordinanceTypes || []);
    } catch (error) {
      console.error('Error fetching ordinances:', error);
      setAvailableOrdinances([]);
      toast.error('Failed to load ordinance types');
    } finally {
      setLoading(false);
    }
  };

  const handleOrdinanceToggle = (ordinanceId: string) => {
    setSelectedOrdinances(prev =>
      prev.includes(ordinanceId)
        ? prev.filter(id => id !== ordinanceId)
        : [...prev, ordinanceId]
    );
  };

  const handleAddOrdinance = (ordinanceId: string) => {
    if (!selectedOrdinances.includes(ordinanceId)) {
      setSelectedOrdinances([...selectedOrdinances, ordinanceId]);
      toast.success('Ordinance added successfully!');
    }
  };

  const handleRemoveOrdinance = (ordinanceId: string) => {
    setSelectedOrdinances(selectedOrdinances.filter(id => id !== ordinanceId));
    toast.success('Ordinance removed!');
  };

  const handleSaveOrdinances = async () => {
    if (!userId && mode === 'assign') {
      toast.error('User ID is required for assignment');
      return;
    }

    setSaving(true);
    try {
      if (mode === 'assign' && userId) {
        // Save ordinance assignments for user
        const response = await fetch(`/api/users/${userId}/ordinances`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ordinanceIds: selectedOrdinances
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to assign ordinances');
        }

        toast.success(`Assigned ${selectedOrdinances.length} ordinances!`);
        if (onOrdinanceAssigned) {
          onOrdinanceAssigned();
        }
      } else {
        // Just save the selection locally
        toast.success(`Saved ${selectedOrdinances.length} ordinances!`);
      }
    } catch (error) {
      console.error('Error saving ordinances:', error);
      toast.error('Failed to save ordinances');
    } finally {
      setSaving(false);
    }
  };

  const handleCreateOrdinance = async () => {
    if (!newOrdinance.name.trim()) {
      toast.error('Ordinance name is required');
      return;
    }

    setSaving(true);
    try {
      const response = await fetch('/api/ordinance-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newOrdinance),
      });

      if (!response.ok) {
        throw new Error('Failed to create ordinance');
      }

      const data = await response.json();
      setAvailableOrdinances([...availableOrdinances, data.ordinanceType]);
      setNewOrdinance({ name: '', description: '', category: '' });
      setShowCreateForm(false);
      toast.success('Ordinance type created successfully!');
      
      if (onOrdinanceCreated) {
        onOrdinanceCreated();
      }
    } catch (error) {
      console.error('Error creating ordinance:', error);
      toast.error('Failed to create ordinance type');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-slate-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {mode === 'assign' && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center">
            <BookOpen className="h-4 w-4 mr-2" />
            Ordinance Assignment
          </h3>
          <p className="text-xs text-blue-600 dark:text-blue-300">
            Select ordinances to assign to the user. These represent policies, procedures, or regulations that apply to the user.
          </p>
        </div>
      )}

      {mode === 'create' && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <h3 className="text-sm font-medium text-green-800 dark:text-green-200 mb-2 flex items-center">
            <Plus className="h-4 w-4 mr-2" />
            Create Ordinance Types
          </h3>
          <p className="text-xs text-green-600 dark:text-green-300">
            Create new ordinance types that can be assigned to users across the system.
          </p>
        </div>
      )}

      {/* Create New Ordinance Type */}
      {mode === 'create' && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              Create New Ordinance Type
            </h4>
            {!showCreateForm && (
              <button
                onClick={() => setShowCreateForm(true)}
                className="px-4 py-2 bg-slate-600 text-white rounded-md hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 flex items-center text-sm"
              >
                <Plus className="w-4 h-4 mr-2" />
                New Ordinance Type
              </button>
            )}
          </div>

          {showCreateForm && (
            <div className="bg-white dark:bg-slate-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
              <div className="space-y-4">
                <div>
                  <label htmlFor="ordinanceName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Ordinance Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="ordinanceName"
                    value={newOrdinance.name}
                    onChange={(e) => setNewOrdinance({ ...newOrdinance, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    placeholder="e.g., Code of Conduct, Safety Protocol"
                  />
                </div>
                
                <div>
                  <label htmlFor="ordinanceCategory" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Category
                  </label>
                  <input
                    type="text"
                    id="ordinanceCategory"
                    value={newOrdinance.category}
                    onChange={(e) => setNewOrdinance({ ...newOrdinance, category: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    placeholder="e.g., Policies, Procedures, Guidelines"
                  />
                </div>

                <div>
                  <label htmlFor="ordinanceDescription" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description
                  </label>
                  <textarea
                    id="ordinanceDescription"
                    value={newOrdinance.description}
                    onChange={(e) => setNewOrdinance({ ...newOrdinance, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Brief description of this ordinance type..."
                  />
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => {
                      setShowCreateForm(false);
                      setNewOrdinance({ name: '', description: '', category: '' });
                    }}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateOrdinance}
                    disabled={saving}
                    className="px-4 py-2 bg-slate-600 text-white rounded-md hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 text-sm"
                  >
                    {saving ? 'Creating...' : 'Create Ordinance Type'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Existing User Ordinances */}
      {userId && userOrdinances.length > 0 && (
        <div>
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3 flex items-center">
            <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
            Current User Ordinances ({userOrdinances.length})
          </h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {userOrdinances.map((assignment) => (
              <div key={assignment.id} className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h5 className="text-sm font-medium text-green-900 dark:text-green-200">
                      {assignment.ordinance.name}
                    </h5>
                    <p className="text-xs text-green-700 dark:text-green-300 mt-1">
                      Status: {assignment.status}
                    </p>
                  </div>
                  <span className="text-xs px-2 py-1 bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200 rounded-full">
                    Active
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Available Ordinances */}
      <div className="grid grid-cols-1 gap-6">
        {availableOrdinances.length === 0 ? (
          <div className="text-center py-8">
            <BookOpen className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              No ordinance types available
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              There are currently no ordinance types configured in the system. 
              {mode === 'create' ? ' Create your first ordinance type above.' : ' Please contact an administrator to set up ordinance types.'}
            </p>
          </div>
        ) : (
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Available Ordinance Types ({availableOrdinances.length})
            </h4>
            
            {mode === 'assign' && (
              <div className="mb-4">
                <label htmlFor="ordinanceSelect" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Quick Add Ordinance
                </label>
                <div className="flex space-x-2">
                  <select
                    id="ordinanceSelect"
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    value=""
                    onChange={(e) => {
                      if (e.target.value) {
                        handleAddOrdinance(e.target.value);
                        e.target.value = '';
                      }
                    }}
                  >
                    <option value="">Select an ordinance to add</option>
                    {availableOrdinances
                      .filter(ordinance => !selectedOrdinances.includes(ordinance.id))
                      .map((ordinance) => (
                        <option key={ordinance.id} value={ordinance.id}>
                          {ordinance.name}
                        </option>
                      ))}
                  </select>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {availableOrdinances.map((ordinance) => (
                <div
                  key={ordinance.id}
                  className={`border rounded-lg p-4 transition-colors ${
                    selectedOrdinances.includes(ordinance.id)
                      ? 'border-slate-500 bg-slate-50 dark:bg-slate-700/50'
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h5 className="font-medium text-slate-800 dark:text-slate-200 text-sm">
                        {ordinance.name}
                      </h5>
                      {ordinance.description && (
                        <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                          {ordinance.description}
                        </p>
                      )}
                      {ordinance.category && (
                        <span className="text-xs px-2 py-1 bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-full mt-2 inline-block">
                          {ordinance.category}
                        </span>
                      )}
                    </div>
                    {mode === 'assign' && (
                      <button
                        type="button"
                        onClick={() => handleOrdinanceToggle(ordinance.id)}
                        className={`ml-2 flex-shrink-0 ${
                          selectedOrdinances.includes(ordinance.id)
                            ? 'text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300'
                            : 'text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300'
                        }`}
                      >
                        {selectedOrdinances.includes(ordinance.id) ? (
                          <X className="h-4 w-4" />
                        ) : (
                          <Plus className="h-4 w-4" />
                        )}
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Selected Ordinances Summary */}
        {mode === 'assign' && selectedOrdinances.length > 0 && (
          <div className="bg-slate-50 dark:bg-slate-700/50 border border-slate-200 dark:border-slate-600 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3 flex items-center">
              <AlertCircle className="h-4 w-4 mr-2 text-yellow-500" />
              Selected Ordinances ({selectedOrdinances.length})
            </h4>
            <div className="space-y-2">
              {selectedOrdinances.map((ordinanceId) => {
                const ordinance = availableOrdinances.find(o => o.id === ordinanceId);
                return ordinance ? (
                  <div key={ordinanceId} className="flex justify-between items-center bg-white dark:bg-slate-600 p-3 rounded border">
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {ordinance.name}
                    </span>
                    <button
                      type="button"
                      className="text-red-500 hover:text-red-700 text-sm font-medium"
                      onClick={() => handleRemoveOrdinance(ordinanceId)}
                    >
                      Remove
                    </button>
                  </div>
                ) : null;
              })}
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      {mode === 'assign' && (
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => setSelectedOrdinances([])}
            disabled={selectedOrdinances.length === 0}
            className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50"
          >
            Clear Selection
          </button>
          <button
            type="button"
            onClick={handleSaveOrdinances}
            disabled={saving || selectedOrdinances.length === 0}
            className="px-6 py-2 bg-slate-600 text-white rounded-md hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 transition-colors duration-200"
          >
            {saving ? 'Saving...' : `Save ${selectedOrdinances.length} Ordinance${selectedOrdinances.length !== 1 ? 's' : ''}`}
          </button>
        </div>
      )}
    </div>
  );
};
