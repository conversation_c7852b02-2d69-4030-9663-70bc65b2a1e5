'use client';

import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { VerticalNav } from '@/components/admin/VerticalNav';
import { OrdinanceManagementForm } from './components/OrdinanceManagementForm';
import { BookOpen, Plus, Users, Search, FileText, Upload, CheckCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';

type TabType = 'manage' | 'assign' | 'create';

export default function OrdinancesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<TabType>('manage');
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check admin permissions via API
  useEffect(() => {
    const verifyAdmin = async () => {
      if (status === 'loading') return;

      if (!session) {
        router.push('/unauthorized');
        return;
      }

      try {
        const response = await fetch('/api/admin/check-permissions');
        const data = await response.json();

        if (!response.ok || !data.isAdmin) {
          router.push('/unauthorized');
          return;
        }

        setIsAdmin(true);
      } catch (error) {
        console.error('Error verifying admin permissions:', error);
        router.push('/unauthorized');
        return;
      } finally {
        setIsLoading(false);
      }
    };

    verifyAdmin();
  }, [session, status, router]);

  const handleOrdinanceAssigned = () => {
    // Refresh or show success
  };

  const handleOrdinanceCreated = () => {
    // Refresh or show success
  };

  const [stats, setStats] = useState({ ordinanceTypes: 0, activeAssignments: 0, recentDocuments: 0 });
  const [ordinanceCategories, setOrdinanceCategories] = useState<string[]>([]);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/ordinances/stats');
        const data = await response.json();
        setStats(data);
      } catch (error) {
        console.error('Error fetching stats:', error);
      }
    };

    const fetchOrdinanceCategories = async () => {
      try {
        const response = await fetch('/api/ordinance-types?categories=true');
        const data = await response.json();
        setOrdinanceCategories(data.categories || []);
      } catch (error) {
        console.error('Error fetching ordinance categories:', error);
      }
    };

    fetchStats();
    fetchOrdinanceCategories();
  }, []);

  const tabs = [
    { id: 'manage' as TabType, label: 'Manage Ordinances' },
    { id: 'assign' as TabType, label: 'Assign Ordinances' },
    { id: 'create' as TabType, label: 'Create Types' },
  ];

  if (status === 'loading' || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!isAdmin) {
    return null; // Will redirect
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'assign':
        return (
          <div className="space-y-6">
            {/* User Selection for Assignment */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Select User for Ordinance Assignment
              </label>
              <input
                type="text"
                value={selectedUserId}
                onChange={(e) => setSelectedUserId(e.target.value)}
                placeholder="Enter user ID"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Enter the user ID to assign ordinances to that specific user.
              </p>
            </div>
            
            <OrdinanceManagementForm
              userId={selectedUserId}
              mode="assign"
              onOrdinanceAssigned={handleOrdinanceAssigned}
            />
          </div>
        );
      case 'create':
        return (
          <OrdinanceManagementForm
            mode="create"
            onOrdinanceCreated={handleOrdinanceCreated}
          />
        );
      default:
        return (
          <div className="space-y-6">
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <FileText className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-blue-700 dark:text-blue-300">
                      Total Ordinance Types
                    </p>
                    <p className="text-2xl font-semibold text-blue-900 dark:text-blue-100">
                      {stats.ordinanceTypes}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Users className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-green-700 dark:text-green-300">
                      Active Assignments
                    </p>
                    <p className="text-2xl font-semibold text-green-900 dark:text-green-100">
                      {stats.activeAssignments}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <BookOpen className="h-8 w-8 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-yellow-700 dark:text-yellow-300">
                      Recent Documents
                    </p>
                    <p className="text-2xl font-semibold text-yellow-900 dark:text-yellow-100">
                      {stats.recentDocuments}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Overview Information */}
            <div className="bg-white dark:bg-slate-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                Ordinances Management Overview
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Ordinance Categories:</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                    {ordinanceCategories.map((category) => (
                      <li key={category} className="flex items-center">
                        <FileText className="h-4 w-4 text-blue-500 mr-2" />
                        {category}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-slate-50 dark:bg-slate-700/50 border border-slate-200 dark:border-slate-600 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Quick Actions
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <button
                  onClick={() => setActiveTab('create')}
                  className="flex items-center p-4 bg-white dark:bg-slate-600 border border-gray-200 dark:border-gray-500 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-500 transition-colors"
                >
                  <Plus className="h-5 w-5 text-green-600 mr-3" />
                  <div className="text-left">
                    <p className="font-medium text-gray-900 dark:text-white">Create Ordinance Type</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Add new ordinance categories</p>
                  </div>
                </button>
                
                <button
                  onClick={() => setActiveTab('assign')}
                  className="flex items-center p-4 bg-white dark:bg-slate-600 border border-gray-200 dark:border-gray-500 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-500 transition-colors"
                >
                  <Users className="h-5 w-5 text-blue-600 mr-3" />
                  <div className="text-left">
                    <p className="font-medium text-gray-900 dark:text-white">Assign to User</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Assign ordinances to users</p>
                  </div>
                </button>
                
                <button
                  onClick={() => {/* Navigate to documents page */}}
                  className="flex items-center p-4 bg-white dark:bg-slate-600 border border-gray-200 dark:border-gray-500 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-500 transition-colors"
                >
                  <Upload className="h-5 w-5 text-purple-600 mr-3" />
                  <div className="text-left">
                    <p className="font-medium text-gray-900 dark:text-white">Upload Documents</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Add ordinance documents</p>
                  </div>
                </button>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <DashboardLayout>
      <div className="flex h-full">
        {/* Admin Vertical Navigation */}
        <div className="w-64 bg-slate-800 text-white shadow-lg flex-shrink-0 rounded-lg">
          <VerticalNav />
        </div>

        {/* Admin Content */}
        <div className="flex-1 p-6 bg-gray-50 dark:bg-gray-900">
          <div className="space-y-6">
            {/* Page Header */}
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
                <Search className="h-8 w-8 mr-3" />
                Ordinances Management
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                Create, assign, and manage ordinance types and user assignments.
              </p>
            </div>

            {/* Tabs */}
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="-mb-px flex space-x-8">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-slate-700 text-slate-700 dark:text-slate-300'
                        : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="bg-white dark:bg-slate-800 shadow rounded-lg p-6">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
