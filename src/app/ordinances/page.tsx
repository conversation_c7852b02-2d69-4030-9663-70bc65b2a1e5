'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { OrdinanceUploadForm } from '@/components/ordinances/OrdinanceUploadForm';
import { OrdinanceList } from '@/components/ordinances/OrdinanceList';
import { OrdinanceSearch } from '@/components/ordinances/OrdinanceSearch';
import { Button } from '@/components/ui/button';
import { Search, Upload, List } from 'lucide-react';

export default function OrdinancesPage() {
  const [activeTab, setActiveTab] = useState<'upload' | 'view'>('upload');
  const [searchQuery, setSearchQuery] = useState('');
  const [ordinances, setOrdinances] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 10
  });

  const fetchOrdinances = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/ordinances?search=${encodeURIComponent(searchQuery)}&page=${pagination.currentPage}&limit=${pagination.pageSize}`
      );
      
      if (!response.ok) {
        throw new Error('Failed to fetch ordinances');
      }
      
      const data = await response.json();
      setOrdinances(data.ordinances);
      setPagination(prev => ({
        ...prev,
        totalPages: data.totalPages,
        totalCount: data.totalCount
      }));
    } catch (error) {
      console.error('Error fetching ordinances:', error);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, pagination.currentPage, pagination.pageSize]);

  // Fetch ordinances when viewing the list or when search/pagination changes
  useEffect(() => {
    if (activeTab === 'view') {
      fetchOrdinances();
    }
  }, [activeTab, fetchOrdinances]);

  // Fetch recent ordinances for the upload tab
  useEffect(() => {
    if (activeTab === 'upload') {
      fetchOrdinances();
    }
  }, [activeTab, fetchOrdinances]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page on new search
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  return (
    <DashboardLayout>
      <div
        className="bg-white dark:bg-gray-800 rounded-lg shadow"
        data-testid="ordinances-page"
      >
        <div className="p-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-6">Ordinances</h1>
          
          {/* Tab Navigation */}
          <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
            <nav className="flex space-x-8" data-testid="ordinances-tabs">
              <button
                onClick={() => setActiveTab('upload')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'upload'
                    ? 'border-slate-700 text-slate-700 dark:text-slate-300 dark:border-slate-300'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
                data-testid="ordinances-tab-upload"
              >
                <div className="flex items-center">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Ordinance
                </div>
              </button>
              <button
                onClick={() => setActiveTab('view')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'view'
                    ? 'border-slate-700 text-slate-700 dark:text-slate-300 dark:border-slate-300'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
                data-testid="ordinances-tab-view"
              >
                <div className="flex items-center">
                  <List className="h-4 w-4 mr-2" />
                  View Ordinances
                </div>
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === 'upload' ? (
            <div>
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
                <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Create New Ordinance</h3>
                <p className="text-xs text-blue-600 dark:text-blue-300">
                  Use this form to create a new ordinance with an initial document. After creation, 
                  you can add more documents to it from the list below.
                </p>
              </div>
              <OrdinanceUploadForm onUploadSuccess={fetchOrdinances} />
              <div className="mt-8">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Recent Ordinances - Add More Documents</h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Click &ldquo;Upload Another Document&rdquo; on any ordinance below to add more documents to it.
                </p>
                {loading ? (
                  <div className="animate-pulse space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    ))}
                  </div>
                ) : (
                  <OrdinanceList 
                    ordinances={ordinances.slice(0, 5)} 
                    showPagination={false}
                    onViewDetails={(id) => console.log('View ordinance:', id)}
                    onDelete={() => fetchOrdinances()}
                  />
                )}
              </div>
            </div>
          ) : (
            <div>
              <OrdinanceSearch onSearch={handleSearch} />
              {loading ? (
                <div className="animate-pulse space-y-4 mt-6">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  ))}
                </div>
              ) : (
                <OrdinanceList 
                  ordinances={ordinances} 
                  showPagination={true}
                  pagination={pagination}
                  onPageChange={handlePageChange}
                  onViewDetails={(id) => console.log('View ordinance:', id)}
                  onDelete={() => fetchOrdinances()}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
