'use client';

import React, { useState } from 'react';
import { Settings } from 'lucide-react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { VerticalMenu } from '@/components/treaties/VerticalMenu';
import { TreatiesManagementTab } from '@/components/treaties/TreatiesManagementTab';
import { TreatyAuditTab } from '@/components/treaties/TreatyAuditTab';

export default function TradeTreatiesPage() {
  const [activeItem, setActiveItem] = useState('trade-treaties');

  const menuItems = [
    {
      id: 'trade-treaties',
      label: 'Trade Treaties',
      href: '/trade-treaties/trade'
    },
    {
      id: 'treaty-audit',
      label: 'Treaty Audit',
      href: '/trade-treaties/audit'
    },
    {
      id: 'system-settings',
      label: 'System Settings',
      href: '/trade-treaties/settings'
    }
  ];

  const renderContent = () => {
    switch (activeItem) {
      case 'trade-treaties':
        return <TreatiesManagementTab />;
      case 'treaty-audit':
        return <TreatyAuditTab />;
      case 'system-settings':
        return (
          <div className="text-center py-16 text-slate-500">
            <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">System Settings</h3>
            <p className="text-sm">System settings interface coming soon...</p>
          </div>
        );
      default:
        return <TreatiesManagementTab />;
    }
  };

  return (
    <DashboardLayout>
      <div className="flex flex-col lg:flex-row gap-6 min-h-screen">
        {/* Vertical Menu */}
        <div className="lg:w-80 flex-shrink-0">
          <VerticalMenu
            items={menuItems}
            activeItem={activeItem}
            onItemSelect={setActiveItem}
            title="Trade Treaty Management"
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 bg-white dark:bg-slate-800 rounded-lg shadow">
          <div className="p-6">
            {renderContent()}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}