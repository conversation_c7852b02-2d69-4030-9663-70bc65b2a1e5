'use client';

import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { TabNavigation } from '@/components/dashboard/TabNavigation';
import { OverviewTab } from '@/components/dashboard/tabs/OverviewTab';
import { NotificationsTab } from '@/components/dashboard/tabs/NotificationsTab';
import { ServersTab } from '@/components/dashboard/tabs/ServersTab';
import { AnalyticsTab } from '@/components/dashboard/tabs/AnalyticsTab';
import { SecurityTab } from '@/components/dashboard/tabs/SecurityTab';
import { SystemTab } from '@/components/dashboard/tabs/SystemTab';
import { useSession } from 'next-auth/react';
import { usePermissions } from '@/lib/hooks/usePermissions';

export default function DashboardPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const { data: session } = useSession();
  const { permissions, userRole } = usePermissions();

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: 'LayoutDashboard',
      // Available to all users
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: 'Bell',
      // Available to all users
    },
    {
      id: 'servers',
      label: 'Servers',
      icon: 'Server',
      roles: ['admin', 'super_admin'],
      requiredPermissions: ['remote_servers:read'],
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: 'BarChart3',
      roles: ['admin', 'super_admin'],
      requiredPermissions: ['view_analytics'],
    },
    {
      id: 'security',
      label: 'Security',
      icon: 'Shield',
      roles: ['admin', 'super_admin'],
      requiredPermissions: ['moderate_content'],
    },
    {
      id: 'system',
      label: 'System',
      icon: 'Settings',
      roles: ['admin', 'super_admin'],
      requiredPermissions: ['manage_all_media'],
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <OverviewTab />;
      case 'notifications':
        return <NotificationsTab />;
      case 'servers':
        return <ServersTab />;
      case 'analytics':
        return <AnalyticsTab />;
      case 'security':
        return <SecurityTab />;
      case 'system':
        return <SystemTab />;
      default:
        return <OverviewTab />;
    }
  };

  return (
    <DashboardLayout>
      <div className="p-6" data-testid="dashboard-page">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
          <p className="text-gray-600 dark:text-gray-400">Welcome to your personal dashboard</p>
        </div>

        <div className="flex flex-col h-full min-h-0">
          <TabNavigation
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={setActiveTab}
            userPermissions={permissions}
            userRole={userRole}
            className="flex-shrink-0"
          />
          <div className="flex-1 overflow-auto min-h-0">
            <div className="h-full">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
