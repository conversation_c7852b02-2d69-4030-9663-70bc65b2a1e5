import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { SessionProviderWrapper } from "@/components/providers/SessionProviderWrapper";
import { PermissionProvider } from "@/lib/contexts/PermissionContext";
import { ToasterProvider } from "@/components/providers/ToasterProvider";

export const metadata: Metadata = {
  title: "NWA User Portal",
  description: "Comprehensive user management system for NWA users",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
        <SessionProviderWrapper>
          <PermissionProvider>
            {children}
          </PermissionProvider>
        </SessionProviderWrapper>
        <ToasterProvider />
      </body>
    </html>
  );
}
