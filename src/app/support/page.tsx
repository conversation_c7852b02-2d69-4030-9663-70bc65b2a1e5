'use client';

import React from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Shield, 
  Key, 
  User, 
  Settings, 
  BookOpen, 
  Download,
  Smartphone,
  Laptop,
  Lock
} from 'lucide-react';

export default function SupportPage() {
  const supportSections = [
    {
      id: '2fa-setup',
      title: 'Two-Factor Authentication Setup',
      description: 'Step-by-step guide to setting up 2FA for your account',
      icon: <Shield className="h-8 w-8 text-blue-500" />,
      requiredRole: 'all',
      content: [
        {
          type: 'step',
          title: 'Step 1: Download an Authenticator App',
          description: 'Choose one of these free authenticator apps:',
          items: [
            'Google Authenticator (Android & iOS)',
            'Microsoft Authenticator (Android & iOS)', 
            'Authy (Android, iOS & Desktop)'
          ]
        },
        {
          type: 'step',
          title: 'Step 2: Enable 2FA in Your Profile',
          description: 'Go to your profile page and click "Enable 2FA"',
          items: []
        },
        {
          type: 'step',
          title: 'Step 3: Scan the QR Code',
          description: 'Open your authenticator app and scan the QR code shown on screen',
          items: []
        },
        {
          type: 'step',
          title: 'Step 4: Save Backup Codes',
          description: 'Download or copy your backup codes and store them in a safe place',
          items: ['These codes can be used if you lose access to your authenticator app']
        }
      ]
    },
    {
      id: 'password-reset',
      title: 'Password Reset Guide',
      description: 'How to reset your password if you forget it',
      icon: <Key className="h-8 w-8 text-green-500" />,
      requiredRole: 'all',
      content: [
        {
          type: 'step',
          title: 'Step 1: Click "Forgot Password"',
          description: 'On the login page, click the "Forgot Password" link',
          items: []
        },
        {
          type: 'step',
          title: 'Step 2: Enter Your Email',
          description: 'Provide the email address associated with your account',
          items: []
        },
        {
          type: 'step',
          title: 'Step 3: Check Your Email',
          description: 'Look for the password reset email and click the link',
          items: ['The link expires after 24 hours for security']
        },
        {
          type: 'step',
          title: 'Step 4: Create New Password',
          description: 'Enter a new strong password following our password policy',
          items: []
        }
      ]
    },
    {
      id: 'mobile-access',
      title: 'Mobile Device Access',
      description: 'How to access the portal from mobile devices',
      icon: <Smartphone className="h-8 w-8 text-purple-500" />,
      requiredRole: 'all',
      content: [
        {
          type: 'info',
          title: 'Mobile Browser Access',
          description: 'The portal is fully responsive and works on all mobile browsers:',
          items: [
            'Chrome Mobile',
            'Safari Mobile',
            'Firefox Mobile',
            'Edge Mobile'
          ]
        },
        {
          type: 'tip',
          title: 'Pro Tip: Add to Home Screen',
          description: 'For quick access, you can add the portal to your home screen:',
          items: [
            'iOS: Use the "Share" button and select "Add to Home Screen"',
            'Android: Use the menu and select "Add to Home Screen"'
          ]
        }
      ]
    },
    {
      id: 'admin-guide',
      title: 'Administrator Guide',
      description: 'Advanced administration features and system management',
      icon: <Settings className="h-8 w-8 text-orange-500" />,
      requiredRole: 'admin',
      content: [
        {
          type: 'section',
          title: 'User Management',
          description: 'Managing users and permissions:',
          items: [
            'Creating new user accounts',
            'Assigning roles and permissions',
            'Resetting user passwords',
            'Managing user sessions'
          ]
        },
        {
          type: 'section',
          title: 'System Settings',
          description: 'Configuring system-wide security settings:',
          items: [
            'Password policy configuration',
            '2FA enforcement settings',
            'Session timeout settings',
            'IP whitelist management'
          ]
        },
        {
          type: 'section',
          title: 'Security Monitoring',
          description: 'Monitoring system security:',
          items: [
            'Viewing audit logs',
            'Monitoring active sessions',
            'Reviewing security events',
            'Generating security reports'
          ]
        }
      ]
    },
    {
      id: 'security-best-practices',
      title: 'Security Best Practices',
      description: 'Tips for maintaining account security',
      icon: <Lock className="h-8 w-8 text-red-500" />,
      requiredRole: 'all',
      content: [
        {
          type: 'tip',
          title: 'Strong Passwords',
          description: 'Create strong, unique passwords:',
          items: [
            'Use at least 12 characters',
            'Include uppercase, lowercase, numbers, and symbols',
            'Avoid common words or personal information',
            'Use a password manager to generate and store passwords'
          ]
        },
        {
          type: 'tip',
          title: '2FA Security',
          description: 'Keep your 2FA secure:',
          items: [
            'Never share your 2FA codes',
            'Store backup codes in a secure location',
            'Use a dedicated authenticator app',
            'Register multiple devices if supported'
          ]
        },
        {
          type: 'warning',
          title: 'Phishing Awareness',
          description: 'Protect against phishing attacks:',
          items: [
            'Never click suspicious links in emails',
            'Always verify the website URL before logging in',
            'Be cautious of urgent or threatening messages',
            'Report suspicious emails to your administrator'
          ]
        }
      ]
    }
  ];

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto py-8 px-4">
        <div className="text-center mb-8">
          <BookOpen className="h-12 w-12 text-blue-500 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Support & Documentation
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            Find help guides, tutorials, and documentation for using the NWA Portal
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {supportSections.map((section) => (
            <Card key={section.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center mb-4">
                  {section.icon}
                  <CardTitle className="ml-3 text-lg">{section.title}</CardTitle>
                </div>
                <CardDescription>{section.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full">
                  View Guide
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Access Section */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Download className="h-6 w-6 mr-2 text-green-500" />
              Quick Downloads
            </CardTitle>
            <CardDescription>
              Download helpful resources and guides
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button variant="outline" className="justify-start">
                <Download className="h-4 w-4 mr-2" />
                2FA Setup Guide (PDF)
              </Button>
              <Button variant="outline" className="justify-start">
                <Download className="h-4 w-4 mr-2" />
                Password Policy (PDF)
              </Button>
              <Button variant="outline" className="justify-start">
                <Download className="h-4 w-4 mr-2" />
                Mobile Access Guide (PDF)
              </Button>
              <Button variant="outline" className="justify-start">
                <Download className="h-4 w-4 mr-2" />
                Security Best Practices (PDF)
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Contact Support */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Need Additional Help?</CardTitle>
            <CardDescription>
              Contact our support team for assistance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <strong>Email:</strong> <EMAIL>
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <strong>Hours:</strong> Monday - Friday, 9 AM - 5 PM EST
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <strong>Emergency:</strong> For urgent security issues, contact your administrator directly
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}