'use client';

import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { VerticalMenu } from '@/components/treaties/VerticalMenu';
import { TreatiesManagementTab } from '@/components/treaties/TreatiesManagementTab';
import { FileText, Users, List, Plus, Search } from 'lucide-react';

// Import the nation treaty tabs directly 
import { NationTreatyTab } from '../nation-treaties/tabs/NationTreatyTab';
import { NationMembersTab } from '../nation-treaties/tabs/NationMembersTab';
import { CreateNationTreatyTab } from '@/components/treaties/CreateNationTreatyTab';

export default function TreatyManagementPage() {
  const [activeItem, setActiveItem] = useState('user-treaties');
  const [activeNationTab, setActiveNationTab] = useState<'nation-treaty' | 'create-treaty' | 'nation-members'>('nation-treaty');

  const menuItems = [
    {
      id: 'user-treaties',
      label: 'User Treaties',
      href: '/user-treaties'
    },
    {
      id: 'nation-treaties',
      label: 'Nation Treaties',
      href: '/nation-treaties'
    }
  ];

  const nationTabs = [
    {
      id: 'nation-treaty',
      title: 'List Treaty',
      description: 'Create and manage nation treaties',
      icon: <List className="h-4 w-4" />,
      component: <NationTreatyTab />
    },
    {
      id: 'create-treaty',
      title: 'Create Treaty',
      description: 'Create a new nation treaty',
      icon: <Plus className="h-4 w-4" />,
      component: <CreateNationTreatyTab key="treaty-mgmt-create" onCreateSuccess={() => setActiveNationTab('nation-treaty')} />
    },
    {
      id: 'nation-members',
      title: 'Member Search',
      description: 'Search and view nation members',
      icon: <Search className="h-4 w-4" />,
      component: <NationMembersTab />
    },
    ];

  const renderContent = () => {
    switch (activeItem) {
      case 'user-treaties':
        return <TreatiesManagementTab />;
      case 'nation-treaties':
        const activeNationTabContent = nationTabs.find(tab => tab.id === activeNationTab) || nationTabs[0];
        return (
          <div className="space-y-6">
            {/* Tab Navigation */}
            <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
              <nav className="flex flex-wrap gap-4 sm:gap-8">
                {nationTabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveNationTab(tab.id as any)}
                    className={`py-3 px-2 border-b-2 font-medium text-sm whitespace-nowrap ${
                      activeNationTab === tab.id
                        ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                        : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                    }`}
                    data-testid={`treaty-nation-tab-${tab.id}`}
                  >
                    {tab.title}
                  </button>
                ))}
              </nav>
            </div>
            {/* Tab Content */}
            {activeNationTabContent.component}
          </div>
        );
      default:
        return <TreatiesManagementTab />;
    }
  };

  const treatySections = [
    {
      id: 'user-treaties',
      title: 'User Treaties',
      description: 'Manage user treaties',
      icon: <FileText className="h-5 w-5" />,
      component: <TreatiesManagementTab />
    },
    {
      id: 'nation-treaties',
      title: 'Nation Treaties',
      description: 'Manage nation treaties',
      icon: <Users className="h-5 w-5" />,
      component: (
        <div className="space-y-6">
          {/* Tab Navigation */}
          <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
            <nav className="flex flex-wrap gap-4 sm:gap-8">
              {nationTabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveNationTab(tab.id as any)}
                  className={`flex items-center py-3 px-2 border-b-2 font-medium text-sm whitespace-nowrap gap-2 ${
                    activeNationTab === tab.id
                      ? 'border-emerald-600 text-emerald-700 dark:text-emerald-300 dark:border-emerald-400'
                      : 'border-transparent text-emerald-600 hover:text-emerald-700 hover:border-emerald-400 dark:text-emerald-400 dark:hover:text-emerald-300'
                  }`}
                  data-testid={`treaty-nation-tab-${tab.id}`}
                >
                  {tab.icon}
                  {tab.title}
                </button>
              ))}
            </nav>
          </div>

          {/* Blue instruction box - positioned after tabs */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Create New Treaty</h3>
            <p className="text-xs text-blue-600 dark:text-blue-300">
              Fill in the treaty details and select the appropriate user. You can manage treaty assignments and track their status.
            </p>
          </div>

          {/* Tab Content */}
          {nationTabs.find(tab => tab.id === activeNationTab)?.component}
        </div>
      )
    }
  ];

  const activeSection = treatySections.find(section => section.id === activeItem) || treatySections[0];

  return (
    <DashboardLayout>
      <div
        className="bg-white dark:bg-slate-800 rounded-lg shadow"
        data-testid="treaty-management-page"
      >
        <div className="p-6">
          <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-100 mb-6">Treaty Management</h1>

          <div className="flex flex-col lg:flex-row gap-6">
            {/* Sidebar Navigation */}
            <div className="w-full lg:w-64">
              <nav
                className="space-y-1 bg-slate-800 p-2 rounded-lg"
                data-testid="treaty-management-nav"
              >
                {treatySections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveItem(section.id)}
                    className={`flex items-center p-3 rounded transition-colors duration-200 text-sm font-medium w-full text-left text-white ${
                      activeItem === section.id
                        ? 'bg-slate-700'
                        : 'hover:bg-slate-700'
                    }`}
                    data-testid={`treaty-nav-${section.id}`}
                  >
                    <div className="mr-3 text-white">
                      {section.icon}
                    </div>
                    {section.title}
                  </button>
                ))}
              </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              <div
                className="bg-white dark:bg-slate-800 rounded-lg shadow p-6"
                data-testid={`treaty-content-${activeSection.id}`}
              >
                <h2 className="text-xl font-semibold mb-2 flex items-center text-slate-800 dark:text-slate-100">
                  <div className="mr-3 text-slate-600 dark:text-slate-400">
                    {activeSection.icon}
                  </div>
                  {activeSection.title}
                </h2>
                <p className="text-slate-600 dark:text-slate-400 mb-6 text-sm">
                  {activeSection.description}
                </p>
                {activeSection.component}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
