 'use client';

import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { RemoteServersTab } from '@/components/settings/RemoteServersTab';
import { SecurityAuditTab } from '@/components/settings/SecurityAuditTab';
import { SystemSettingsTab } from '@/components/settings/SystemSettingsTab';
import { PlatformSettingsTab } from '@/components/settings/PlatformSettingsTab';
import { SessionManagementTab } from '@/components/settings/SessionManagementTab';
import { TreatyAuditTab } from '@/components/settings/TreatyAuditTab';
import { ServerAssignmentManager } from '@/components/settings/ServerAssignmentManager';
import PermissionManager from '@/components/settings/PermissionManager';
import { NotificationSettingsTab } from '@/components/settings/NotificationSettingsTab';
import {
  Server,
  Shield,
  Settings as SettingsIcon,
  Key,
  Activity,
  Globe,
  Monitor,
  FileText,
  Bell
} from 'lucide-react';

export default function SettingsClient() {
  const [activeTab, setActiveTab] = useState('remote-servers');

  const settingsSections = [
    {
      id: 'remote-servers',
      title: 'Remote Servers',
      description: 'Manage external applications and API connections',
      icon: <Server className="h-6 w-6" />,
      component: <RemoteServersTab />
    },
    {
      id: 'server-assignments',
      title: 'Server Assignments',
      description: 'Assign remote servers to users and roles',
      icon: <Shield className="h-6 w-6" />,
      component: <ServerAssignmentManager />
    },
    {
      id: 'local-permissions',
      title: 'Local Permissions',
      description: 'Manage NWAPromote roles and project permissions',
      icon: <Key className="h-6 w-6" />,
      component: <PermissionManager />
    },
    {
      id: 'security-audit',
      title: 'Security Audit',
      description: 'Monitor system activities and security events',
      icon: <Activity className="h-6 w-6" />,
      component: <SecurityAuditTab />
    },
    {
      id: 'treaty-audit',
      title: 'Treaty Audit',
      description: 'Monitor treaty applications, payments, and administrative actions',
      icon: <FileText className="h-6 w-6" />,
      component: <TreatyAuditTab />
    },
    {
      id: 'notification-settings',
      title: 'Notification Settings',
      description: 'Configure how and when you receive notifications',
      icon: <Bell className="h-6 w-6" />,
      component: <NotificationSettingsTab />
    },
    {
      id: 'system-settings',
      title: 'System Settings',
      description: 'Configure security settings and policies',
      icon: <SettingsIcon className="h-6 w-6" />,
      component: <SystemSettingsTab />
    },
    {
      id: 'platform-settings',
      title: 'Platform Settings',
      description: 'Manage countries, cities, categories, and subcategories',
      icon: <Globe className="h-6 w-6" />,
      component: <PlatformSettingsTab />
    },
    {
      id: 'session-management',
      title: 'Session Management',
      description: 'View session strategy and authentication status',
      icon: <Monitor className="h-6 w-6" />,
      component: <SessionManagementTab />
    },
    {
      id: 'api-keys',
      title: 'API Keys',
      description: 'Manage API keys and access tokens',
      icon: <Key className="h-6 w-6" />,
      component: (
        <div className="text-center py-12">
          <Key className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">API Keys</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            API key management coming soon.
          </p>
        </div>
      )
    }
  ];

  const activeSection = settingsSections.find(section => section.id === activeTab) || settingsSections[0];

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow" data-testid="settings-page">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-100 mb-6">System Settings</h1>
          
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Sidebar Navigation */}
            <div className="w-full lg:w-64">
              <nav className="space-y-1 bg-slate-800 p-2 rounded-lg" data-testid="settings-nav">
                {settingsSections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveTab(section.id)}
                    className={`flex items-center p-3 rounded transition-colors duration-200 text-sm font-medium w-full text-left text-white ${
                      activeTab === section.id
                        ? 'bg-slate-700'
                        : 'hover:bg-slate-700'
                    }`}
                    data-testid={`settings-nav-${section.id}`}
                  >
                    <div className="flex-none mr-3 text-white w-6">
                      {section.icon}
                    </div>
                    <span className="flex-1">{section.title}</span>
                  </button>
                ))}
              </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1" data-testid="settings-content-wrapper">
              <div
                className="bg-white dark:bg-slate-800 rounded-lg shadow p-6"
                data-testid={`settings-content-${activeSection.id}`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h2 className="text-xl font-semibold flex items-center text-slate-800 dark:text-slate-100">
                    <div className="mr-3 text-slate-600 dark:text-slate-400">
                      {activeSection.icon}
                    </div>
                    {activeSection.title}
                  </h2>
                  {activeTab === 'session-management' && (
                    <div className="flex items-center gap-2" data-testid="settings-session-actions">
                      <button className="px-3 py-1.5 text-sm bg-blue-500/20 hover:bg-blue-500/30 text-blue-700 dark:text-blue-300 border border-blue-500/30 hover:border-blue-500/50 rounded transition-colors" data-testid="settings-session-auto-refresh">
                        Auto Refresh
                      </button>
                      <button className="px-3 py-1.5 text-sm bg-blue-500/20 hover:bg-blue-500/30 text-blue-700 dark:text-blue-300 border border-blue-500/30 hover:border-blue-500/50 rounded transition-colors" data-testid="settings-session-refresh">
                        Refresh Now
                      </button>
                      <button className="px-3 py-1.5 text-sm bg-red-500/20 hover:bg-red-500/30 text-red-700 dark:text-red-300 border border-red-500/30 hover:border-red-500/50 rounded transition-colors" data-testid="settings-session-terminate">
                        Terminate All
                      </button>
                    </div>
                  )}
                </div>
                <p className="text-slate-600 dark:text-slate-400 mb-6 text-sm">
                  {activeSection.description}
                </p>
                {activeSection.component}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
