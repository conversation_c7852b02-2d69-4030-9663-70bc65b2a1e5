import React from 'react';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { redirect } from 'next/navigation';
import SettingsClient from './SettingsClient';

async function checkAdminAccess() {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect('/login');
  }

  console.log('Settings page - Session user ID:', (session.user as any).id);
  console.log('Settings page - Session user email:', session.user.email);

  // Check if user has admin role
  const user = await prisma.user.findUnique({
    where: { id: (session.user as any).id },
    include: { userRoles: { include: { role: true } } }
  });

  console.log('Settings page - Database user found:', !!user);
  if (user) {
    console.log('Settings page - User roles:', user.userRoles.map(ur => ur.role.name));
  }

  const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');

  console.log('Settings page - Is admin:', isAdmin);

  if (!isAdmin) {
    console.log('Settings page - Redirecting to dashboard (not admin)');
    redirect('/dashboard');
  }

  return { session, user };
}

export default async function SettingsPage() {
  await checkAdminAccess();
  return <SettingsClient />;
}