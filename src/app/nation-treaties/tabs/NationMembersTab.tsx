'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Users, Search, ChevronLeft, ChevronRight, MapPin } from 'lucide-react';
import { NationTreatySearchAutocomplete } from '@/components/treaties/NationTreatySearchAutocomplete';

interface NationTreaty {
  id: string;
  name: string;
  officialName: string;
  status: string;
}

interface NationMember {
  id: string;
  userId: string;
  nationTreatyId: string;
  role: string;
  status: string;
  joinedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    title?: string;
    isActive: boolean;
  };
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export function NationMembersTab() {
  const [selectedNationTreaty, setSelectedNationTreaty] = useState<string>('');
  const [members, setMembers] = useState<NationMember[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch members for selected nation treaty
  const fetchMembers = useCallback(async (page: number = 1) => {
    if (!selectedNationTreaty) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/nation-treaties/${selectedNationTreaty}/members?page=${page}&limit=${pagination.limit}`);
      if (!response.ok) {
        throw new Error('Failed to fetch members');
      }
      const data = await response.json();
      setMembers(data.data || []);
      setPagination(data.pagination || pagination);
    } catch (err) {
      console.error('Error fetching members:', err);
      setError('Failed to load members');
    } finally {
      setLoading(false);
    }
  }, [selectedNationTreaty, pagination.limit]);

  useEffect(() => {
    if (selectedNationTreaty) {
      fetchMembers(1);
    } else {
      setMembers([]);
      setPagination(prev => ({ ...prev, page: 1, total: 0, totalPages: 0 }));
    }
  }, [selectedNationTreaty, fetchMembers]);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      fetchMembers(newPage);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
          Nation Member Search
        </h2>
        <p className="text-slate-600 dark:text-slate-300">
          Search for members within a specific nation or tribe treaty
        </p>
      </div>

      {/* Nation/Tribe Selection */}
      <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-4 pt-8 relative">
        <h3 className="absolute top-0 left-6 transform -translate-y-1/2 bg-white dark:bg-gray-900 px-3 text-lg font-medium text-gray-900 dark:text-white whitespace-nowrap">
          Search Nation or Tribe Treaty
        </h3>
        <div className="max-w-md">
          <Label htmlFor="nationTreaty">Nation/Tribe Treaty</Label>
          <NationTreatySearchAutocomplete
            value={selectedNationTreaty}
            onChange={setSelectedNationTreaty}
            placeholder="Search for nation/tribe treaty..."
            statusFilter={['ACTIVE', 'PENDING']}
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Search by treaty name or official name. Active and pending treaties only.
          </p>
        </div>
      </div>

      {/* Members Display */}
      {selectedNationTreaty && (
        <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-4 pt-8 relative">
          <h3 className="absolute top-0 left-6 transform -translate-y-1/2 bg-white dark:bg-gray-900 px-3 text-lg font-medium text-gray-900 dark:text-white whitespace-nowrap">
            Treaty Members
          </h3>
          
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm text-slate-500">
              Showing {pagination.total} members
            </div>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
            </div>
          )}

          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin h-8 w-8 border-2 border-slate-500 border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="text-slate-600 dark:text-slate-300">Loading members...</p>
            </div>
          ) : members.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 mx-auto mb-4 text-slate-400" />
              <p className="text-slate-600 dark:text-slate-300">No members found for this nation/tribe</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Members Table */}
              <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                <div className="grid grid-cols-1 md:grid-cols-12 gap-4 p-4 bg-gray-50 dark:bg-gray-800 font-medium text-sm">
                  <div className="md:col-span-4">Name</div>
                  <div className="md:col-span-3">Email</div>
                  <div className="md:col-span-2">Role</div>
                  <div className="md:col-span-2">Status</div>
                  <div className="md:col-span-1">Joined</div>
                </div>
                
                <div className="divide-y divide-gray-200 dark:divide-gray-700">
                  {members.map((member) => (
                    <div key={member.id} className="grid grid-cols-1 md:grid-cols-12 gap-4 p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                      <div className="md:col-span-4">
                        <div className="font-medium text-gray-900 dark:text-white">
                          {member.user.name}
                        </div>
                        {member.user.title && (
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {member.user.title}
                          </div>
                        )}
                      </div>
                      <div className="md:col-span-3">
                        <div className="text-sm text-gray-900 dark:text-gray-100">
                          {member.user.email}
                        </div>
                        {!member.user.isActive && (
                          <div className="text-xs text-red-600 dark:text-red-400">
                            Inactive User
                          </div>
                        )}
                      </div>
                      <div className="md:col-span-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          member.role === 'ADMIN' 
                            ? 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100'
                            : member.role === 'ENVOY'
                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                        }`}>
                          {member.role}
                        </span>
                      </div>
                      <div className="md:col-span-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          member.status === 'ACTIVE'
                            ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                            : member.status === 'SUSPENDED'
                            ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                        }`}>
                          {member.status}
                        </span>
                      </div>
                      <div className="md:col-span-1">
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {new Date(member.joinedAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-4">
                  <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                    Showing page {pagination.page} of {pagination.totalPages}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1}
                      className="flex items-center gap-1"
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page >= pagination.totalPages}
                      className="flex items-center gap-1"
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}