'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Building, Plus, Search } from 'lucide-react';

export function EnvoyOfficeTab() {
  const [activeView, setActiveView] = useState<'manage' | 'create'>('manage');

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="flex gap-4 mb-6">
        <Button
          variant={activeView === 'manage' ? 'default' : 'outline'}
          onClick={() => setActiveView('manage')}
          className="flex items-center gap-2"
        >
          <Building className="h-4 w-4" />
          Manage Offices
        </Button>
        <Button
          variant={activeView === 'create' ? 'default' : 'outline'}
          onClick={() => setActiveView('create')}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Office
        </Button>
      </div>

      {/* Content Area */}
      {activeView === 'manage' && (
        <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Envoy Offices</h3>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <input
                  type="text"
                  placeholder="Search offices..."
                  className="pl-10 pr-4 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100"
                />
              </div>
            </div>
          </div>
          <div className="text-center py-8 text-slate-500">
            <Building className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Envoy offices management interface...</p>
          </div>
        </div>
      )}

      {activeView === 'create' && (
        <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">Create New Envoy Office</h3>
          <div className="text-center py-8 text-slate-500">
            <Plus className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Envoy office creation form...</p>
          </div>
        </div>
      )}
    </div>
  );
}