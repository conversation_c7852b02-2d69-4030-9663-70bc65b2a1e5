'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Edit, Search, Filter, Download, Eye, Plus, FileText, Users, Building2, Phone, AlertTriangle } from 'lucide-react';
import { NationMembersTab } from './NationMembersTab';
import { EnvoyOfficeTab } from './EnvoyOfficeTab';
import { SpecialEnvoysTab } from './SpecialEnvoysTab';
import { EmergencyContactTab } from './EmergencyContactTab';
// Removed CreateNationTreatyTab import - this component no longer handles create mode

interface NationTreatyEntity {
  id: string;
  name: string;
  officialName?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'SUSPENDED';
  description?: string;
  contactEmail?: string;
  contactPhone?: string;
  memberCount: number;
  envoyCount: number;
  createdAt: string;
  updatedAt: string;
}

interface NationTreatyTabProps {
  // Removed initialMode since this component should only handle list and edit views
}

export function NationTreatyTab() {
  const [activeView, setActiveView] = useState<'list' | 'edit'>('list');
  const [editingTreatyId, setEditingTreatyId] = useState<string | null>(null);
  const [activeEditTab, setActiveEditTab] = useState<'basic' | 'members' | 'envoy' | 'special-envoys' | 'emergency'>('basic');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [treaties, setTreaties] = useState<NationTreatyEntity[]>([]);
  const [loading, setLoading] = useState(false);

  // Load nation treaties from API
  const loadNationTreaties = useCallback(async (search?: string, page?: number) => {
    setLoading(true);
    try {
      const searchParam = search || searchTerm;
      const pageParam = page || currentPage;
      const response = await fetch(`/api/nation-treaties?search=${encodeURIComponent(searchParam)}&page=${pageParam}&limit=${itemsPerPage}`);
      
      if (response.ok) {
        const data = await response.json();
        // Transform API data to match our interface
        const transformedTreaties = data.nationTreaties?.map((treaty: any) => ({
          id: treaty.id,
          name: treaty.name || 'Unnamed Treaty Entity',
          officialName: treaty.officialName || '',
          status: treaty.status || 'ACTIVE',
          description: treaty.description || '',
          contactEmail: treaty.contactEmail || treaty.contact_email || '',
          contactPhone: treaty.contactPhone || treaty.contact_phone || '',
          memberCount: treaty._aggr_count_members || 0,
          envoyCount: treaty._aggr_count_envoys || 0,
          createdAt: treaty.createdAt || treaty.created_at,
          updatedAt: treaty.updatedAt || treaty.updated_at
        })) || [];
        
        setTreaties(transformedTreaties);
      } else {
        console.error('Failed to load nation treaties');
        setTreaties([]);
      }
    } catch (error) {
      console.error('Error loading nation treaties:', error);
      setTreaties([]);
    } finally {
      setLoading(false);
    }
  }, [searchTerm, currentPage, itemsPerPage]);

  // Load treaties on component mount and when search/page changes
  useEffect(() => {
    if (activeView === 'list') {
      loadNationTreaties();
    }
  }, [activeView, loadNationTreaties]);

  // Filter treaties based on search term
  const filteredTreaties = treaties.filter(treaty =>
    treaty.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (treaty.officialName && treaty.officialName.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (treaty.description && treaty.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredTreaties.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredTreaties.length / itemsPerPage);

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (activeView === 'list') {
        setCurrentPage(1);
        loadNationTreaties(searchTerm, 1);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, activeView, loadNationTreaties]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100';
      case 'INACTIVE':
      case 'SUSPENDED':
        return 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100';
    }
  };

  const handleEditTreaty = (treatyId: string) => {
    setEditingTreatyId(treatyId);
    setActiveView('edit');
    setActiveEditTab('basic'); // Start with basic info tab
  };

  const handleBackToList = () => {
    setActiveView('list');
    setEditingTreatyId(null);
    loadNationTreaties(); // Refresh the list
  };

  const getStatusBadge = (status: string) => {
    const statusColor = getStatusColor(status);
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColor}`}>
        {status}
      </span>
    );
  };

  // Find the currently editing treaty for display
  const editingTreaty = treaties.find(t => t.id === editingTreatyId);

  // If in edit mode, show the comprehensive treaty management interface
  if (activeView === 'edit' && editingTreatyId) {
    return (
      <div className="space-y-6">
        {/* Edit Mode Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={handleBackToList}
              className="flex items-center gap-2"
            >
              ← Back to List
            </Button>
            <div>
              <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200">
                Edit Treaty Entity
              </h2>
              {editingTreaty && (
                <p className="text-slate-500 dark:text-slate-400">
                  {editingTreaty.name} {editingTreaty.officialName && `(${editingTreaty.officialName})`}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Comprehensive Treaty Management Tabs */}
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveEditTab('basic')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeEditTab === 'basic'
                    ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                }`}
              >
                <div className="flex items-center">
                  <Building2 className="h-4 w-4 mr-2" />
                  Basic Info
                </div>
              </button>
              <button
                onClick={() => setActiveEditTab('members')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeEditTab === 'members'
                    ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                }`}
              >
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  Nation Members
                </div>
              </button>
              <button
                onClick={() => setActiveEditTab('envoy')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeEditTab === 'envoy'
                    ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                }`}
              >
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2" />
                  Envoy Office
                </div>
              </button>
              <button
                onClick={() => setActiveEditTab('special-envoys')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeEditTab === 'special-envoys'
                    ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                }`}
              >
                <div className="flex items-center">
                  <Building2 className="h-4 w-4 mr-2" />
                  Special Envoys
                </div>
              </button>
              <button
                onClick={() => setActiveEditTab('emergency')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeEditTab === 'emergency'
                    ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                }`}
              >
                <div className="flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Emergency Contact
                </div>
              </button>
            </nav>
          </div>

          {/* Edit Content Area */}
          <div className="p-6">
            {activeEditTab === 'basic' && (
              <div className="space-y-6">
                <div className="text-center py-8">
                  <Building2 className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-2">
                    Basic Treaty Information
                  </h3>
                  <p className="text-slate-500 dark:text-slate-400 mb-6">
                    Edit core treaty entity details and settings
                  </p>
                  <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-6 max-w-2xl mx-auto text-left">
                    <h4 className="font-medium text-slate-800 dark:text-slate-200 mb-4">
                      Treaty Entity ID: {editingTreatyId}
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <label className="block text-slate-600 dark:text-slate-400 mb-1">Name</label>
                        <Input defaultValue={editingTreaty?.name || ''} className="w-full" />
                      </div>
                      <div>
                        <label className="block text-slate-600 dark:text-slate-400 mb-1">Official Name</label>
                        <Input defaultValue={editingTreaty?.officialName || ''} className="w-full" />
                      </div>
                      <div>
                        <label className="block text-slate-600 dark:text-slate-400 mb-1">Status</label>
                        <select className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-800">
                          <option value="ACTIVE">Active</option>
                          <option value="INACTIVE">Inactive</option>
                          <option value="PENDING">Pending</option>
                          <option value="SUSPENDED">Suspended</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-slate-600 dark:text-slate-400 mb-1">Contact Email</label>
                        <Input defaultValue={editingTreaty?.contactEmail || ''} className="w-full" />
                      </div>
                      <div className="md:col-span-2">
                        <label className="block text-slate-600 dark:text-slate-400 mb-1 flex items-center">
                            <FileText className="h-4 w-4 mr-2" />
                            Description
                          </label>
                        <textarea 
                          defaultValue={editingTreaty?.description || ''} 
                          className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-800 h-24 resize-none"
                        />
                      </div>
                    </div>
                    <div className="flex gap-2 mt-6">
                      <Button>Save Changes</Button>
                      <Button variant="outline">Cancel</Button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeEditTab === 'members' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200">Nation Members Management</h3>
                  <Button size="sm" className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Add Member
                  </Button>
                </div>
                <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-4">
                  <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                    Manage members associated with this treaty entity. Current members: {editingTreaty?.memberCount || 0}
                  </p>
                  <NationMembersTab />
                </div>
              </div>
            )}

            {activeEditTab === 'envoy' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200">Envoy Office Management</h3>
                  <Button size="sm" className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Add Office
                  </Button>
                </div>
                <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-4">
                  <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                    Manage envoy offices for this treaty entity. Current offices: {editingTreaty?.envoyCount || 0}
                  </p>
                  <EnvoyOfficeTab />
                </div>
              </div>
            )}

            {activeEditTab === 'special-envoys' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200">Special Envoys Management</h3>
                  <Button size="sm" className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Add Envoy
                  </Button>
                </div>
                <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-4">
                  <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                    Manage special envoys appointed for this treaty entity.
                  </p>
                  <SpecialEnvoysTab />
                </div>
              </div>
            )}

            {activeEditTab === 'emergency' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200">Emergency Contact Management</h3>
                  <Button size="sm" className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Add Contact
                  </Button>
                </div>
                <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-4">
                  <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                    Manage emergency contact information for this treaty entity.
                  </p>
                  <EmergencyContactTab />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Removed create mode - this component only handles list and edit views

  return (
    <div className="space-y-6">
      {/* Content Area - Always show list since create is now a separate page */}
      <div className="space-y-6">
          {/* Search and Filter Controls */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <input
                  type="text"
                  placeholder="Search treaty entities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filter
              </Button>
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Export
              </Button>
            </div>
          </div>

          {/* Treaty Entities Table */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                <thead className="bg-slate-50 dark:bg-slate-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                      Treaty Entity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                      Members
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                      Envoys
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                      Contact
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                  {loading ? (
                    <tr>
                      <td colSpan={7} className="px-6 py-8 text-center text-slate-500 dark:text-slate-400">
                        Loading treaty entities...
                      </td>
                    </tr>
                  ) : currentItems.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="px-6 py-8 text-center text-slate-500 dark:text-slate-400">
                        {searchTerm ? 'No treaty entities found matching your search.' : 'No treaty entities found.'}
                      </td>
                    </tr>
                  ) : (
                    currentItems.map((treaty) => (
                      <tr key={treaty.id} className="hover:bg-slate-50 dark:hover:bg-slate-700">
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-slate-900 dark:text-slate-100">
                              {treaty.name}
                            </div>
                            {treaty.officialName && (
                              <div className="text-sm text-slate-500 dark:text-slate-400">
                                {treaty.officialName}
                              </div>
                            )}
                            {treaty.description && (
                              <div className="text-xs text-slate-400 dark:text-slate-500 mt-1">
                                {treaty.description.length > 60 
                                  ? treaty.description.substring(0, 60) + '...' 
                                  : treaty.description}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(treaty.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                          <div className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            {treaty.memberCount}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                          <div className="flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            {treaty.envoyCount}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-500 dark:text-slate-400">
                          {treaty.contactEmail && (
                            <div className="text-xs">{treaty.contactEmail}</div>
                          )}
                          {treaty.contactPhone && (
                            <div className="text-xs">{treaty.contactPhone}</div>
                          )}
                          {!treaty.contactEmail && !treaty.contactPhone && (
                            <div className="text-xs text-slate-400">No contact info</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                          {new Date(treaty.createdAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex gap-2">
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="flex items-center gap-1"
                              onClick={() => handleEditTreaty(treaty.id)}
                            >
                              <Edit className="h-3 w-3" />
                              Edit
                            </Button>
                            <Button variant="ghost" size="sm" className="flex items-center gap-1">
                              <Eye className="h-3 w-3" />
                              View
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white dark:bg-slate-800 px-4 py-3 flex items-center justify-between border-t border-slate-200 dark:border-slate-700">
                <div className="flex-1 flex justify-between sm:hidden">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-slate-700 dark:text-slate-300">
                      Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
                      <span className="font-medium">
                        {Math.min(indexOfLastItem, filteredTreaties.length)}
                      </span>{' '}
                      of <span className="font-medium">{filteredTreaties.length}</span> results
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className="rounded-l-md"
                      >
                        Previous
                      </Button>
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={currentPage === pageNum ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setCurrentPage(pageNum)}
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className="rounded-r-md"
                      >
                        Next
                      </Button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
    </div>
  );
}
