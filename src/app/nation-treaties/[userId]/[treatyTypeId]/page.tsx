'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { UserTreatyAssignmentForm } from '@/components/users/manage/UserTreatyAssignmentForm';
import { UserTreatyAssignmentData } from '@/types/user-treaty-assignment';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Save, Send } from 'lucide-react';
import { useSession } from 'next-auth/react';

export default function TreatyTypeDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [initialData, setInitialData] = useState<UserTreatyAssignmentData | undefined>();
  const [error, setError] = useState<string | null>(null);

  const userId = (params?.userId as string) || '';
  const treatyTypeId = (params?.treatyTypeId as string) || '';

  // Load existing form data if available
  useEffect(() => {
    const loadExistingData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/treaty-type-details/${userId}/${treatyTypeId}`);

        if (response.ok) {
          const data = await response.json();
          setInitialData(data);
        } else if (response.status !== 404) {
          // 404 is expected for new forms, other errors should be shown
          throw new Error('Failed to load form data');
        }
      } catch (err) {
        console.error('Error loading form data:', err);
        setError('Failed to load existing form data');
      } finally {
        setIsLoading(false);
      }
    };

    if (userId && treatyTypeId) {
      loadExistingData();
    }
  }, [userId, treatyTypeId]);

  const handleFormSubmit = async (data: UserTreatyAssignmentData) => {
    try {
      const response = await fetch('/api/treaty-type-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          userId,
          treatyTypeId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit form');
      }

      const result = await response.json();

      // Navigate to success page or treaty management
      router.push('/treaties?success=submitted');
    } catch (err) {
      console.error('Error submitting form:', err);
      throw new Error('Failed to submit treaty type details');
    }
  };

  const handleFormSave = async (data: UserTreatyAssignmentData) => {
    try {
      const response = await fetch('/api/treaty-type-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          userId,
          treatyTypeId,
          isDraft: true,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save draft');
      }

      // Show success message
      console.log('Draft saved successfully');
    } catch (err) {
      console.error('Error saving draft:', err);
      throw new Error('Failed to save draft');
    }
  };

  const handleCancel = () => {
    router.push('/treaties');
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading treaty type details...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Form
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                {error}
              </div>
            </div>
          </div>
          <div className="mt-4">
            <Button onClick={() => router.push('/treaties')} variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Treaties
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-6xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-6 sm:mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center space-x-3 sm:space-x-4">
                <Button
                  onClick={() => router.push('/treaties')}
                  variant="outline"
                  size="sm"
                  className="flex-shrink-0"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">Back to Treaties</span>
                  <span className="sm:hidden">Back</span>
                </Button>
                <div className="min-w-0 flex-1">
                  <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white truncate">
                    Treaty Type Details
                  </h1>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Complete the details for this treaty type application
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Form */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <UserTreatyAssignmentForm
              userId={userId}
              treatyId={treatyTypeId} // Using treatyTypeId as treatyId for now
              treatyTypeId={treatyTypeId}
              onSubmit={handleFormSubmit}
              onSave={handleFormSave}
              onCancel={handleCancel}
              initialData={initialData}
            />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}