'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { CreateNationTreatyTab } from '@/components/treaties/CreateNationTreatyTab';

export default function CreateNationTreatyPage() {
  const router = useRouter();

  const handleDone = () => {
    router.push('/nation-treaties');
  };

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
        <div className="p-6">
          <CreateNationTreatyTab onCreateSuccess={handleDone} onCancel={handleDone} />
        </div>
      </div>
    </DashboardLayout>
  );
}
