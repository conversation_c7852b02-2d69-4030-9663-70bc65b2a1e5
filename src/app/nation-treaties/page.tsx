'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { FileText, Users, Edit, Plus } from 'lucide-react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { NationTreatyTab } from './tabs/NationTreatyTab';
import { NationMembersTab } from './tabs/NationMembersTab';
import { UpdateTreatyTab } from './tabs/UpdateTreatyTab';
import { CreateNationTreatyTab } from '@/components/treaties/CreateNationTreatyTab';

export default function NationTreatiesPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'nation-treaty' | 'create-treaty' | 'nation-members' | 'update-treaty'>('nation-treaty');

  const tabs = [
    {
      id: 'nation-treaty',
      title: 'Treaty List',
      description: 'View and manage nation treaties',
      icon: <FileText className="h-4 w-4" />,
      component: <NationTreatyTab />
    },
    {
      id: 'create-treaty',
      title: 'Create Treaty',
      description: 'Create a new nation treaty',
      icon: <Plus className="h-4 w-4" />,
      component: <CreateNationTreatyTab key="create-treaty-tab" onCreateSuccess={() => setActiveTab('nation-treaty')} />
    },
    {
      id: 'nation-members',
      title: 'Member Search',
      description: 'Search and view nation members',
      icon: <Users className="h-4 w-4" />,
      component: <NationMembersTab />
    },
    {
      id: 'update-treaty',
      title: 'Update Treaty',
      description: 'Search and update treaties',
      icon: <Edit className="h-4 w-4" />,
      component: <UpdateTreatyTab />
    }
  ];

  const activeTabContent = tabs.find(tab => tab.id === activeTab) || tabs[0];

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-100 mb-6">Nation Treaty Management</h1>
          
          {/* Tab Navigation */}
          <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
            <nav className="flex flex-wrap gap-4 sm:gap-8">
              {/* Debug: Show all tabs - should be 4 total */}
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-3 px-2 border-b-2 font-medium text-sm whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                      : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                  }`}
                >
                  <div className="flex items-center">
                    <div className="mr-2 text-slate-600 dark:text-slate-400">
                      {tab.icon}
                    </div>
                    {tab.title}
                    {/* Debug: {tab.id} */}
                  </div>
                </button>
              ))}
              
              {/* Debug info - should show 4 tabs: */}
              <div className="text-xs text-red-500 mt-2">
                Debug: Total tabs: {tabs.length}, Active: {activeTab}
              </div>
            </nav>
          </div>

          {/* Tab Content */}
          <div>
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-2 flex items-center text-slate-800 dark:text-slate-100">
                <div className="mr-3 text-slate-600 dark:text-slate-400">
                  {activeTabContent.icon}
                </div>
                {activeTabContent.title}
              </h2>
              <p className="text-slate-600 dark:text-slate-400 mb-6 text-sm">
                {activeTabContent.description}
              </p>
              {activeTabContent.component}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
