'use client';

import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { User, Mail, Camera, Shield } from 'lucide-react';
import PersonalInfoTab from '@/components/profile/PersonalInfoTab';
import ContactDetailsTab from '@/components/profile/ContactDetailsTab';
import SecurityTab from '@/components/profile/SecurityTab';
import ProfilePhotoTab from '@/components/profile/ProfilePhotoTab';
import { useProfileForm } from '@/components/profile/useProfileForm';
import { ProtectedPage } from '@/components/auth/ProtectedPage';

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState('personal');

  const {
    profile,
    loading,
    saving,
    twoFAEnabled,
    showQRCode,
    qrCodeUrl,
    formData,
    contactData,
    passwordData,
    imagePreview,
    handleInputChange,
    handleContactChange,
    handleContactDataChange,
    handlePasswordChange,
    handleImageChange,
    triggerFileInput,
    removeImage,
    handleSubmit,
    handlePasswordSubmit,
    handleEnable2FA,
    handleDisable2FA,
  } = useProfileForm();

  if (loading) {
    return (
      <DashboardLayout>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6" data-testid="profile-loading">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="space-y-3">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                  <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const tabs = [
    { id: 'personal', label: 'Personal Info', icon: <User className="h-4 w-4" /> },
    { id: 'contact', label: 'Contact Details', icon: <Mail className="h-4 w-4" /> },
    { id: 'profile', label: 'Profile Photo', icon: <Camera className="h-4 w-4" /> },
    { id: 'security', label: 'Security', icon: <Shield className="h-4 w-4" /> },
  ];

  return (
    <ProtectedPage permissions={[{ resource: 'profile', action: 'read' }, { resource: 'profile', action: 'write' }]}>
      <DashboardLayout>
        <div className="p-6" data-testid="profile-page">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow" data-testid="profile-card">
            <div className="p-6">
              <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-6">User Profile</h1>

              {/* Tab Navigation */}
              <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
                <nav className="flex space-x-8" data-testid="profile-nav">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? 'border-emerald-500 text-emerald-600 dark:text-emerald-400'
                          : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                      }`}
                      data-testid={`profile-nav-${tab.id}`}
                    >
                      {tab.icon}
                      <span className="ml-2">{tab.label}</span>
                    </button>
                  ))}
                </nav>
              </div>

              <div className="space-y-6" data-testid="profile-content">
                {/* Personal Tab */}
                {activeTab === 'personal' && (
                  <PersonalInfoTab
                    formData={formData}
                    onInputChange={handleInputChange}
                    saving={saving}
                  />
                )}

                {/* Contact Tab */}
                {activeTab === 'contact' && (
                  <ContactDetailsTab
                    formData={formData}
                    contactData={contactData}
                    onInputChange={handleInputChange}
                    onContactChange={handleContactChange}
                    onContactDataChange={handleContactDataChange}
                    saving={saving}
                  />
                )}

                {/* Security Tab */}
                {activeTab === 'security' && (
                  <SecurityTab
                    passwordData={passwordData}
                    twoFAEnabled={twoFAEnabled}
                    showQRCode={showQRCode}
                    qrCodeUrl={qrCodeUrl}
                    onPasswordChange={handlePasswordChange}
                    onPasswordSubmit={handlePasswordSubmit}
                    onEnable2FA={handleEnable2FA}
                    onDisable2FA={handleDisable2FA}
                    saving={saving}
                  />
                )}

                {/* Profile Tab */}
                {activeTab === 'profile' && (
                  <ProfilePhotoTab
                    imagePreview={imagePreview}
                    onImageChange={handleImageChange}
                    onTriggerFileInput={triggerFileInput}
                    onRemoveImage={removeImage}
                    saving={saving}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedPage>
  );
}
