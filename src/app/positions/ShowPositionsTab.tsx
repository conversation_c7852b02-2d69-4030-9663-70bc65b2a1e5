'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

interface Position {
  id: string;
  title: string;
  description: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  titleId?: string;
  titleName?: string;
  titlePositions?: Array<{
    titleId: string;
    positionId: string;
    title: {
      id: string;
      name: string;
      isActive: boolean;
    };
  }>;
}

interface Pagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  pageSize: number;
}

export const ShowPositionsTab: React.FC = () => {
  const [positions, setPositions] = useState<Position[]>([]);
  const [titles, setTitles] = useState<Array<{id: string, name: string, isActive: boolean}>>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [pagination, setPagination] = useState<Pagination>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 10,
  });
  const [pageSize, setPageSize] = useState(10);
  const [sortField, setSortField] = useState<'title' | 'createdAt'>('title');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [reassigningPosition, setReassigningPosition] = useState<string | null>(null);
  const [newTitleId, setNewTitleId] = useState<string>('');
  const [titleFilter, setTitleFilter] = useState<string>('');

  // Fetch titles on component mount
  useEffect(() => {
    fetchTitles();
  }, []);

  const fetchTitles = async () => {
    try {
      const response = await fetch('/api/positions/titles');

      if (!response.ok) {
        throw new Error('Failed to fetch titles');
      }

      const data = await response.json();
      setTitles(data);
    } catch (error) {
      console.error('Error fetching titles:', error);
      toast.error('Failed to load titles');
    }
  };

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page when search changes
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const fetchPositions = useCallback(async () => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: (pagination.currentPage || 1).toString(),
        limit: (pageSize || 10).toString(),
        search: debouncedSearchQuery || '',
        sortBy: sortField,
        sortOrder: sortDirection,
      });

      // Add title filter if selected
      if (titleFilter) {
        queryParams.set('titleId', titleFilter);
      }

      const response = await fetch(`/api/positions/positions?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch positions');
      }
      
      const data = await response.json();
      setPositions(data.positions || []);
      setPagination({
        currentPage: data.pagination?.currentPage || 1,
        totalPages: data.pagination?.totalPages || 1,
        totalCount: data.pagination?.totalCount || 0,
        pageSize: data.pagination?.pageSize || 10,
      });
    } catch (error) {
      console.error('Error fetching positions:', error);
      toast.error('Failed to load positions');
    } finally {
      setLoading(false);
    }
  }, [pagination.currentPage, pageSize, debouncedSearchQuery, sortField, sortDirection, titleFilter]);

  // Fetch positions when page, page size, search query, or sort changes
  useEffect(() => {
    fetchPositions();
  }, [fetchPositions]);

  // Refresh data when component becomes visible (tab is selected)
  useEffect(() => {
    const refreshData = () => {
      fetchPositions();
    };

    // Refresh data immediately when component mounts
    refreshData();

    // Also refresh when window gets focus (user switches back to tab)
    const handleFocus = () => refreshData();
    window.addEventListener('focus', handleFocus);

    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [fetchPositions]);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= pagination.totalPages) {
      setPagination(prev => ({ ...prev, currentPage: page }));
    }
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page when page size changes
  };

  const handleSort = (field: 'title' | 'createdAt') => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page when sort changes
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleStartReassign = (position: Position) => {
    setReassigningPosition(position.id);
    setNewTitleId(position.titlePositions?.[0]?.titleId || '');
  };

  const handleCancelReassign = () => {
    setReassigningPosition(null);
    setNewTitleId('');
  };

  const handleSaveReassign = async (positionId: string) => {
    if (!newTitleId) {
      toast.error('Please select a title');
      return;
    }

    try {
      // Get the current position to check if it has an existing association
      const currentPosition = positions.find(pos => pos.id === positionId);
      const currentTitleId = currentPosition?.titlePositions?.[0]?.titleId;

      // If there's an existing association with a different title, remove it first
      if (currentTitleId && currentTitleId !== newTitleId) {
        try {
          const deleteResponse = await fetch('/api/positions/title-positions', {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              titleId: currentTitleId,
              positionId: positionId,
            }),
          });

          // Only log error if it's not a 404 (association doesn't exist)
          if (!deleteResponse.ok && deleteResponse.status !== 404) {
            console.warn('Failed to delete old association:', await deleteResponse.text());
          }
        } catch (deleteError) {
          console.warn('Error deleting old association:', deleteError);
          // Continue with creating new association even if delete fails
        }
      }

      // Create new association
      const response = await fetch('/api/positions/title-positions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          titleId: newTitleId,
          positionId: positionId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reassign position');
      }

      // Update the position in the local state
      const selectedTitle = titles.find(t => t.id === newTitleId);
      setPositions(positions.map(pos =>
        pos.id === positionId
          ? {
              ...pos,
              titlePositions: newTitleId ? [{
                titleId: newTitleId,
                positionId: positionId,
                title: {
                  id: newTitleId,
                  name: selectedTitle?.name || '',
                  isActive: selectedTitle?.isActive || true
                }
              }] : []
            }
          : pos
      ));

      setReassigningPosition(null);
      setNewTitleId('');
      toast.success('Position reassigned successfully');
    } catch (error: any) {
      console.error('Error reassigning position:', error);
      toast.error(error.message || 'Failed to reassign position');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <h2 className="text-xl font-bold text-slate-800 dark:text-slate-200">
            Reassign Positions
          </h2>

          <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
            <div className="relative flex-1 md:w-64">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search positions..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div className="relative flex-1 md:w-64">
              <select
                value={titleFilter}
                onChange={(e) => {
                  setTitleFilter(e.target.value);
                  setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page when filter changes
                }}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Titles</option>
                {titles
                  .filter(title => title.isActive)
                  .map(title => (
                    <option key={title.id} value={title.id}>
                      {title.name}
                    </option>
                  ))}
              </select>
            </div>
          </div>
        </div>
        
        {positions.length === 0 ? (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No positions found</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {debouncedSearchQuery || titleFilter
                ? `No positions found ${debouncedSearchQuery ? `matching "${debouncedSearchQuery}"` : ''}${debouncedSearchQuery && titleFilter ? ' and ' : ''}${titleFilter ? `for the selected title` : ''}.`
                : 'Get started by creating a new position.'
              }
            </p>
            {(debouncedSearchQuery || titleFilter) && (
              <div className="mt-4 flex justify-center space-x-2">
                {debouncedSearchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="px-3 py-1 text-sm text-slate-600 hover:text-slate-800 dark:text-slate-400 dark:hover:text-slate-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Clear Search
                  </button>
                )}
                {titleFilter && (
                  <button
                    onClick={() => {
                      setTitleFilter('');
                      setPagination(prev => ({ ...prev, currentPage: 1 }));
                    }}
                    className="px-3 py-1 text-sm text-slate-600 hover:text-slate-800 dark:text-slate-400 dark:hover:text-slate-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Clear Filter
                  </button>
                )}
              </div>
            )}
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                      Current Title
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                      onClick={() => handleSort('title')}
                    >
                      <div className="flex items-center">
                        Position
                        {sortField === 'title' && (
                          <span className="ml-1">
                            {sortDirection === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                      Actions
                    </th>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                      onClick={() => handleSort('createdAt')}
                    >
                      <div className="flex items-center">
                        Created
                        {sortField === 'createdAt' && (
                          <span className="ml-1">
                            {sortDirection === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {positions.map((position) => (
                    <tr key={position.id} className="hover:bg-gray-50 dark:hover:bg-gray-750">
                      <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                        {position.titlePositions?.[0]?.title?.name || 'No title assigned'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-800 dark:text-slate-200">
                        {position.title}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                        {position.description || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {position.isActive ? (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Active
                          </span>
                        ) : (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            Inactive
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {reassigningPosition === position.id ? (
                          <div className="flex items-center space-x-2">
                            <select
                              value={newTitleId}
                              onChange={(e) => setNewTitleId(e.target.value)}
                              className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white text-xs"
                            >
                              <option value="">Select Title</option>
                              {titles
                                .filter(title => title.isActive)
                                .map(title => (
                                  <option key={title.id} value={title.id}>
                                    {title.name}
                                  </option>
                                ))}
                            </select>
                            <button
                              onClick={() => handleSaveReassign(position.id)}
                              className="px-2 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 text-xs"
                            >
                              Save
                            </button>
                            <button
                              onClick={handleCancelReassign}
                              className="px-2 py-1 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-xs"
                            >
                              Cancel
                            </button>
                          </div>
                        ) : (
                          <button
                            onClick={() => handleStartReassign(position)}
                            className="px-3 py-1 bg-slate-600 text-white rounded-md hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 text-xs"
                          >
                            Reassign
                          </button>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {formatDate(position.createdAt)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {/* Pagination */}
            <div className="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 px-4 py-3 sm:px-6">
              <div className="flex flex-1 justify-between sm:hidden">
                <button
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 1}
                  className="relative inline-flex items-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
                >
                  Previous
                </button>
                <button
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage === pagination.totalPages}
                  className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    Showing <span className="font-medium">{((pagination.currentPage - 1) * pageSize) + 1}</span> to{' '}
                    <span className="font-medium">{Math.min(pagination.currentPage * pageSize, pagination.totalCount)}</span> of{' '}
                    <span className="font-medium">{pagination.totalCount}</span> results
                  </p>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center">
                    <label htmlFor="pageSize" className="mr-2 text-sm text-gray-700 dark:text-gray-300">
                      Rows per page:
                    </label>
                    <select
                      id="pageSize"
                      value={pageSize}
                      onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                      className="rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-2 py-1 text-sm text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-slate-500 focus:border-slate-500"
                    >
                      {[10, 25, 50, 100].map((size) => (
                        <option key={size} value={size}>
                          {size}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                      <button
                        onClick={() => handlePageChange(1)}
                        disabled={pagination.currentPage === 1}
                        className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                      >
                        <span className="sr-only">First</span>
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M15.79 14.79a.75.75 0 01-1.06 0L10 10.06l-4.73 4.73a.75.75 0 11-1.06-1.06l5.25-5.25a.75.75 0 011.06 0l5.25 5.25a.75.75 0 010 1.06z" clipRule="evenodd" />
                        </svg>
                      </button>
                      <button
                        onClick={() => handlePageChange(pagination.currentPage - 1)}
                        disabled={pagination.currentPage === 1}
                        className="relative inline-flex items-center px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                      >
                        <span className="sr-only">Previous</span>
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10 12.77 13.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clipRule="evenodd" />
                        </svg>
                      </button>
                      <span className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 dark:text-white ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:outline-offset-0">
                        Page {pagination.currentPage} of {pagination.totalPages}
                      </span>
                      <button
                        onClick={() => handlePageChange(pagination.currentPage + 1)}
                        disabled={pagination.currentPage === pagination.totalPages}
                        className="relative inline-flex items-center px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                      >
                        <span className="sr-only">Next</span>
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clipRule="evenodd" />
                        </svg>
                      </button>
                      <button
                        onClick={() => handlePageChange(pagination.totalPages)}
                        disabled={pagination.currentPage === pagination.totalPages}
                        className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                      >
                        <span className="sr-only">Last</span>
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M4.21 5.23a.75.75 0 011.06 0L10 9.94l4.73-4.71a.75.75 0 111.06 1.06l-5.25 5.25a.75.75 0 01-1.06 0L4.21 6.29a.75.75 0 010-1.06z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};