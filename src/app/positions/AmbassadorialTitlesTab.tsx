'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';

interface Title {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export const AmbassadorialTitlesTab: React.FC = () => {
  const [titles, setTitles] = useState<Title[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState<string | null>(null);
  const [newTitle, setNewTitle] = useState({ name: '', description: '' });
  const [editTitle, setEditTitle] = useState({ name: '', description: '' });

  // Fetch titles from API
  useEffect(() => {
    fetchTitles();
  }, []);

  const fetchTitles = async () => {
    try {
      setLoading(true);
      console.log('DEBUG: Frontend - Starting fetchTitles request');
      const response = await fetch('/api/positions/titles');
      console.log('DEBUG: Frontend - Response status:', response.status, 'ok:', response.ok);

      if (!response.ok) {
        console.log('DEBUG: Frontend - Response not ok, status:', response.status);
        if (response.status === 401) {
          console.log('DEBUG: Frontend - 401 error detected');
          throw new Error('Authentication required. Please log in and try again.');
        }
        if (response.status === 403) {
          console.log('DEBUG: Frontend - 403 error detected');
          throw new Error('Access denied. Administrator privileges required to manage titles.');
        }
        const errorData = await response.json();
        console.log('DEBUG: Frontend - Error data:', errorData);
        throw new Error(errorData.error || `Failed to fetch titles (HTTP ${response.status})`);
      }

      const data = await response.json();
      console.log('DEBUG: Frontend - Success, received', data.length, 'titles');
      setTitles(data);
    } catch (error: any) {
      console.error('Error fetching titles:', error);
      if (error.message) {
        toast.error(error.message);
      } else {
        toast.error('Failed to load titles. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTitle = async () => {
    if (!newTitle.name.trim()) {
      toast.error('Title name is required');
      return;
    }
    
    try {
      const response = await fetch('/api/positions/titles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newTitle.name,
          description: newTitle.description,
        }),
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required. Please log in and try again.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. Administrator privileges required to create titles.');
        }
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to create title (HTTP ${response.status})`);
      }
      
      const createdTitle = await response.json();
      setTitles([...titles, createdTitle]);
      setNewTitle({ name: '', description: '' });
      toast.success('Title created successfully');
    } catch (error: any) {
      console.error('Error creating title:', error);
      if (error.message) {
        toast.error(error.message);
      } else {
        toast.error('Failed to create title. Please try again later.');
      }
    }
  };

  const handleStartEdit = (title: Title) => {
    setIsEditing(title.id);
    setEditTitle({ 
      name: title.name, 
      description: title.description || '' 
    });
  };

  const handleUpdateTitle = async (id: string) => {
    if (!editTitle.name.trim()) {
      toast.error('Title name is required');
      return;
    }
    
    try {
      const response = await fetch(`/api/positions/titles/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: editTitle.name,
          description: editTitle.description,
        }),
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required. Please log in and try again.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. Administrator privileges required to update titles.');
        }
        if (response.status === 404) {
          throw new Error('Title not found. It may have been deleted.');
        }
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update title (HTTP ${response.status})`);
      }
      
      const updatedTitle = await response.json();
      setTitles(titles.map(title => 
        title.id === id ? updatedTitle : title
      ));
      
      setIsEditing(null);
      toast.success('Title updated successfully');
    } catch (error: any) {
      console.error('Error updating title:', error);
      if (error.message) {
        toast.error(error.message);
      } else {
        toast.error('Failed to update title. Please try again later.');
      }
    }
  };

  const handleToggleStatus = async (id: string) => {
    try {
      const title = titles.find(t => t.id === id);
      if (!title) return;
      
      const response = await fetch(`/api/positions/titles/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: title.name,
          description: title.description,
          isActive: !title.isActive,
        }),
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required. Please log in and try again.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. Administrator privileges required to update titles.');
        }
        if (response.status === 404) {
          throw new Error('Title not found. It may have been deleted.');
        }
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update title (HTTP ${response.status})`);
      }
      
      const updatedTitle = await response.json();
      setTitles(titles.map(title => 
        title.id === id ? updatedTitle : title
      ));
      
      toast.success(`Title ${title.isActive ? 'deactivated' : 'activated'} successfully`);
    } catch (error: any) {
      console.error('Error updating title:', error);
      if (error.message) {
        toast.error(error.message);
      } else {
        toast.error('Failed to update title. Please try again later.');
      }
    }
  };

  const handleDeleteTitle = async (id: string) => {
    try {
      const response = await fetch(`/api/positions/titles/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete title');
      }
      
      setTitles(titles.filter(title => title.id !== id));
      toast.success('Title deleted successfully');
    } catch (error: any) {
      console.error('Error deleting title:', error);
      toast.error(error.message || 'Failed to delete title');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h2 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">Create New Title</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
                      <label htmlFor="titleName" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
            Title Name <span className="text-red-500">*</span>
          </label>
                          <input
                type="text"
                id="titleName"
                value={newTitle.name}
                onChange={(e) => setNewTitle({ ...newTitle, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-600 dark:text-white"
                placeholder="e.g., Ambassador"
              />
          </div>
          <div>
            <label htmlFor="titleDescription" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
            Description
          </label>
            <input
                type="text"
                id="titleDescription"
                value={newTitle.description}
                onChange={(e) => setNewTitle({ ...newTitle, description: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-600 dark:text-white"
                placeholder="Brief description of the title"
              />
          </div>
        </div>
        <div className="mt-4">
          <button
            onClick={handleCreateTitle}
            className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
          >
            Create Title
          </button>
        </div>
      </div>

      <div>
        <h2 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">Existing Titles</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                  Title
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {titles.map((title) => (
                <tr key={title.id}>
                  {isEditing === title.id ? (
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          value={editTitle.name}
                          onChange={(e) => setEditTitle({ ...editTitle, name: e.target.value })}
                          className="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          value={editTitle.description}
                          onChange={(e) => setEditTitle({ ...editTitle, description: e.target.value })}
                          className="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {title.isActive ? (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            Active
                          </span>
                        ) : (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                            Inactive
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleUpdateTitle(title.id)}
                          className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                        >
                          Save
                        </button>
                        <button
                          onClick={() => setIsEditing(null)}
                          className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                        >
                          Cancel
                        </button>
                      </td>
                    </>
                  ) : (
                    <>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-800 dark:text-slate-200">
                        {title.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                        {title.description || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {title.isActive ? (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            Active
                          </span>
                        ) : (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                            Inactive
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleStartEdit(title)}
                          className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-300 mr-3 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleToggleStatus(title.id)}
                          className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-300 mr-3 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                        >
                          {title.isActive ? 'Deactivate' : 'Activate'}
                        </button>
                        <button
                          onClick={() => handleDeleteTitle(title.id)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                        >
                          Delete
                        </button>
                      </td>
                    </>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};