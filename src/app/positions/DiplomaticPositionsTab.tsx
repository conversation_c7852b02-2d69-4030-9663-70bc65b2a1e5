'use client';

import React, { useState } from 'react';
import { toast } from 'sonner';
import { CreatePositionTab } from './CreatePositionTab';
import { ManagePositionTab } from './ManagePositionTab';
import { ShowPositionsTab } from './ShowPositionsTab';

interface Position {
  id: string;
  title: string;
  description: string | null;
  level: number;
  parentId: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  parent?: Position | null;
  children?: Position[];
}

interface Title {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export const DiplomaticPositionsTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'create' | 'manage' | 'reassign'>('create');
  
  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('create')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'create'
                ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
            }`}
          >
            Create New Position
          </button>
          <button
            onClick={() => setActiveTab('manage')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'manage'
                ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
            }`}
          >
            Edit Positions
          </button>
          <button
            onClick={() => setActiveTab('reassign')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'reassign'
                ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
            }`}
          >
            Reassign Position
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'create' ? <CreatePositionTab /> :
         activeTab === 'manage' ? <ManagePositionTab /> :
         <ShowPositionsTab />}
      </div>
    </div>
  );
};