import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';

interface Position {
  id: string;
  title: string;
  description: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  titlePositions?: Array<{
    titleId: string;
    positionId: string;
    title: {
      id: string;
      name: string;
      isActive: boolean;
    };
  }>;
}

interface Title {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export const ManagePositionTab: React.FC = () => {
  const [titles, setTitles] = useState<Title[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTitle, setSelectedTitle] = useState<string>('');
  const [filteredPositions, setFilteredPositions] = useState<Position[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [isEditing, setIsEditing] = useState<string | null>(null);
  const [editPosition, setEditPosition] = useState({
    title: '',
    description: ''
  });

  // Fetch titles and positions from API
  useEffect(() => {
    Promise.all([
      fetchTitles(),
      fetchPositions()
    ]).finally(() => {
      setLoading(false);
    });
  }, []);

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const fetchTitles = async () => {
    try {
      const response = await fetch('/api/positions/titles');
      
      if (!response.ok) {
        throw new Error('Failed to fetch titles');
      }
      
      const data = await response.json();
      setTitles(data);
    } catch (error) {
      console.error('Error fetching titles:', error);
      toast.error('Failed to load titles');
    }
  };

  const fetchPositions = async (titleId?: string) => {
    try {
      const url = titleId
        ? `/api/positions/positions?titleId=${titleId}`
        : '/api/positions/positions';

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('Failed to fetch positions');
      }

      const data = await response.json();

      // The API returns { positions: [...], pagination: {...} }
      // Extract the positions array from the response
      const positionsArray = data.positions || [];

      if (titleId) {
        setFilteredPositions(positionsArray);
      } else {
        setPositions(positionsArray);
      }
    } catch (error) {
      console.error('Error fetching positions:', error);
      toast.error('Failed to load positions');
    }
  };

  // Filter positions when a title is selected
  const handleTitleChange = async (titleId: string) => {
    setSelectedTitle(titleId);
    if (titleId) {
      await fetchPositions(titleId);
    } else {
      setFilteredPositions([]);
    }
  };


  const handleStartEdit = (position: Position) => {
    setIsEditing(position.id);
    setEditPosition({
      title: position.title,
      description: position.description || ''
    });
  };

  const handleUpdatePosition = async (id: string) => {
    if (!editPosition.title.trim()) {
      toast.error('Position title is required');
      return;
    }
    
    try {
      const response = await fetch(`/api/positions/positions/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: editPosition.title,
          description: editPosition.description,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update position');
      }
      
      const updatedPosition = await response.json();
      setPositions(positions.map(position => 
        position.id === id ? updatedPosition : position
      ));
      
      // Update filtered positions if we're viewing a specific title
      if (selectedTitle) {
        setFilteredPositions(filteredPositions.map(position => 
          position.id === id ? updatedPosition : position
        ));
      }
      
      setIsEditing(null);
      toast.success('Position updated successfully');
    } catch (error: any) {
      console.error('Error updating position:', error);
      toast.error(error.message || 'Failed to update position');
    }
  };

  const handleToggleStatus = async (id: string) => {
    try {
      const position = positions.find(p => p.id === id);
      if (!position) return;
      
      const response = await fetch(`/api/positions/positions/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: position.title,
          description: position.description,
          isActive: !position.isActive,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update position');
      }
      
      const updatedPosition = await response.json();
      setPositions(positions.map(pos => 
        pos.id === id ? updatedPosition : pos
      ));
      
      // Update filtered positions if we're viewing a specific title
      if (selectedTitle) {
        setFilteredPositions(filteredPositions.map(pos => 
          pos.id === id ? updatedPosition : pos
        ));
      }
      
      toast.success(`Position ${position.isActive ? 'deactivated' : 'activated'} successfully`);
    } catch (error: any) {
      console.error('Error updating position:', error);
      toast.error(error.message || 'Failed to update position');
    }
  };

  const handleDeletePosition = async (id: string) => {
    try {
      // Check if position is assigned to any users before deletion
      const checkResponse = await fetch(`/api/positions/positions/${id}/assignments`);
      
      if (checkResponse.ok) {
        const assignments = await checkResponse.json();
        if (assignments.count > 0) {
          if (!window.confirm(`This position is currently assigned to ${assignments.count} user(s). Deleting it will remove it from their profiles. Are you sure you want to continue?`)) {
            return;
          }
        }
      }
      
      const response = await fetch(`/api/positions/positions/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete position');
      }
      
      setPositions(positions.filter(position => position.id !== id));
      
      // Update filtered positions if we're viewing a specific title
      if (selectedTitle) {
        setFilteredPositions(filteredPositions.filter(position => position.id !== id));
      }
      
      toast.success('Position deleted successfully');
    } catch (error: any) {
      console.error('Error deleting position:', error);
      toast.error(error.message || 'Failed to delete position');
    }
  };

  // Get positions to display (filtered by title and search)
  const getDisplayPositions = () => {
    let positionsToFilter = selectedTitle ? filteredPositions : positions;

    // Apply search filter if there's a search query
    if (debouncedSearchQuery.trim()) {
      const query = debouncedSearchQuery.toLowerCase().trim();
      positionsToFilter = positionsToFilter.filter(position =>
        position.title.toLowerCase().includes(query) ||
        (position.description && position.description.toLowerCase().includes(query))
      );
    }

    return positionsToFilter;
  };

  const displayPositions = getDisplayPositions();

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <h2 className="text-lg font-medium text-slate-800 dark:text-slate-200">All Positions</h2>

          <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
            <div className="relative flex-1 md:w-64">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search positions..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div className="relative flex-1 md:w-64">
              <select
                value={selectedTitle}
                onChange={(e) => handleTitleChange(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Titles</option>
                {titles.map(title => (
                  <option key={title.id} value={title.id}>{title.name}</option>
                ))}
              </select>
            </div>
          </div>

          {(debouncedSearchQuery || selectedTitle) && (
            <div className="flex justify-center space-x-2">
              {debouncedSearchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="px-3 py-1 text-sm text-slate-600 hover:text-slate-800 dark:text-slate-400 dark:hover:text-slate-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Clear Search
                </button>
              )}
              {selectedTitle && (
                <button
                  onClick={() => {
                    setSelectedTitle('');
                    setFilteredPositions([]);
                  }}
                  className="px-3 py-1 text-sm text-slate-600 hover:text-slate-800 dark:text-slate-400 dark:hover:text-slate-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Clear Filter
                </button>
              )}
            </div>
          )}
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                  Position
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {displayPositions.map((position) => (
                <tr key={position.id}>
                  {isEditing === position.id ? (
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          value={editPosition.title}
                          onChange={(e) => setEditPosition({ ...editPosition, title: e.target.value })}
                          className="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          value={editPosition.description}
                          onChange={(e) => setEditPosition({ ...editPosition, description: e.target.value })}
                          className="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {position.isActive ? (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Active
                          </span>
                        ) : (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            Inactive
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleUpdatePosition(position.id)}
                          className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                        >
                          Save
                        </button>
                        <button
                          onClick={() => setIsEditing(null)}
                          className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                        >
                          Cancel
                        </button>
                      </td>
                    </>
                  ) : (
                    <>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-800 dark:text-slate-200">
                        {position.title}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {position.description || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {position.isActive ? (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Active
                          </span>
                        ) : (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            Inactive
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleStartEdit(position)}
                          className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-300 mr-3 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleToggleStatus(position.id)}
                          className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-300 mr-3 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                        >
                          {position.isActive ? 'Deactivate' : 'Activate'}
                        </button>
                        <button
                          onClick={() => handleDeletePosition(position.id)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                        >
                          Delete
                        </button>
                      </td>
                    </>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};