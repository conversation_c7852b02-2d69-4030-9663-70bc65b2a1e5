import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';

interface Title {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Position {
  id: string;
  title: string;
  description: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  titlePositions?: Array<{
    titleId: string;
    positionId: string;
    title: {
      id: string;
      name: string;
      isActive: boolean;
    };
  }>;
}

export const CreatePositionTab: React.FC = () => {
  const [titles, setTitles] = useState<Title[]>([]);
  const [selectedTitle, setSelectedTitle] = useState<string>('');
  const [positionTitle, setPositionTitle] = useState('');
  const [positionDescription, setPositionDescription] = useState('');
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [existingPositions, setExistingPositions] = useState<Position[]>([]);
  const [showWarning, setShowWarning] = useState(false);

  // Fetch titles on component mount
  useEffect(() => {
    fetchTitles().finally(() => {
      setLoading(false);
    });
  }, []);

  const fetchTitles = async () => {
    try {
      const response = await fetch('/api/positions/titles');

      if (!response.ok) {
        throw new Error('Failed to fetch titles');
      }

      const data = await response.json();
      setTitles(data);
    } catch (error) {
      console.error('Error fetching titles:', error);
      toast.error('Failed to load titles');
    }
  };

  // Fetch existing positions for the selected title when title changes
  const handleTitleChange = async (titleId: string) => {
    setSelectedTitle(titleId);
    setPositionTitle('');
    setShowWarning(false);

    if (titleId) {
      try {
        const response = await fetch(`/api/positions/positions?titleId=${titleId}`);

        if (!response.ok) {
          throw new Error('Failed to fetch positions');
        }

        const data = await response.json();

        // The API returns { positions: [...], pagination: {...} }
        // Extract the positions array from the response
        const positions = data.positions || [];

        // Ensure positions is an array before setting it
        if (Array.isArray(positions)) {
          setExistingPositions(positions);
        } else {
          console.error('Expected array of positions, received:', positions);
          setExistingPositions([]);
          toast.error('Invalid response format from server');
        }
      } catch (error) {
        console.error('Error fetching positions:', error);
        toast.error('Failed to load positions for selected title');
        setExistingPositions([]);
      }
    } else {
      setExistingPositions([]);
    }
  };

  // Check for duplicate positions
  const checkForDuplicates = () => {
    if (positionTitle.trim() && selectedTitle) {
      // Ensure existingPositions is an array before calling .some()
      const positions = Array.isArray(existingPositions) ? existingPositions : [];
      const isDuplicate = positions.some(
        position => position.title.toLowerCase() === positionTitle.trim().toLowerCase()
      );

      if (isDuplicate) {
        setShowWarning(true);
        toast.warning('A position with this title already exists for the selected ambassadorial title.');
        return true;
      } else {
        setShowWarning(false);
      }
    }
    return false;
  };

  // Handle position title change
  const handlePositionTitleChange = (value: string) => {
    setPositionTitle(value);
    if (showWarning) {
      setShowWarning(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedTitle) {
      toast.error('Please select an ambassadorial title');
      return;
    }

    if (!positionTitle.trim()) {
      toast.error('Position title is required');
      return;
    }

    // Check for duplicates
    if (checkForDuplicates()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/positions/positions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: positionTitle.trim(),
          description: positionDescription,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create position');
      }

      const createdPosition = await response.json();

      // Create the title-position association if a title was selected
      if (selectedTitle) {
        try {
          const associationResponse = await fetch('/api/positions/title-positions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              titleId: selectedTitle,
              positionId: createdPosition.id,
            }),
          });

          if (!associationResponse.ok) {
            const errorData = await associationResponse.json();
            console.error('Failed to create title-position association:', errorData);
            // Don't fail the whole operation if association fails
          }
        } catch (associationError) {
          console.error('Error creating title-position association:', associationError);
          // Don't fail the whole operation if association fails
        }
      }

      toast.success('Position created successfully!');

      // Reset form
      setPositionTitle('');
      setPositionDescription('');
      setShowWarning(false);

      // Update existing positions list with the new association
      const currentPositions = Array.isArray(existingPositions) ? existingPositions : [];
      const positionWithAssociation = {
        ...createdPosition,
        titlePositions: selectedTitle ? [{
          titleId: selectedTitle,
          positionId: createdPosition.id,
          title: titles.find(t => t.id === selectedTitle) || { id: selectedTitle, name: '', isActive: true }
        }] : []
      };
      setExistingPositions([...currentPositions, positionWithAssociation]);
    } catch (error: any) {
      console.error('Error creating position:', error);
      toast.error(error.message || 'Failed to create position');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
        <h2 className="text-xl font-bold text-slate-800 dark:text-slate-200 mb-6">Create New Position</h2>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="ambassadorialTitle" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Ambassadorial Title <span className="text-red-500">*</span>
              </label>
              <select
                id="ambassadorialTitle"
                value={selectedTitle}
                onChange={(e) => handleTitleChange(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 bg-white dark:bg-gray-800 dark:text-white"
                required
              >
                <option value="">Select an Ambassadorial Title</option>
                {titles
                  .filter(title => title.isActive)
                  .map(title => (
                    <option key={title.id} value={title.id}>
                      {title.name}
                    </option>
                  ))}
              </select>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Select the ambassadorial title this position belongs to
              </p>
            </div>

            <div>
              <label htmlFor="positionTitle" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Position Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="positionTitle"
                value={positionTitle}
                onChange={(e) => handlePositionTitleChange(e.target.value)}
                className={`w-full px-4 py-2 border ${
                  showWarning ? 'border-yellow-500' : 'border-gray-300'
                } dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white`}
                placeholder="e.g., Cultural Attaché"
                required
              />
              {showWarning && (
                <p className="mt-1 text-sm text-yellow-600 dark:text-yellow-400">
                  Warning: A position with this title already exists for the selected title
                </p>
              )}
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Enter the official title for this diplomatic position
              </p>
            </div>

            <div className="md:col-span-2">
              <label htmlFor="positionDescription" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Description
              </label>
              <textarea
                id="positionDescription"
                value={positionDescription}
                onChange={(e) => setPositionDescription(e.target.value)}
                rows={3}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                placeholder="Brief description of the position's responsibilities and duties"
              />
            </div>
          </div>

          <div className="flex justify-end pt-4">
            <button
              type="submit"
              disabled={isSubmitting || !selectedTitle || !positionTitle.trim()}
              className="px-6 py-3 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 transition-colors duration-200 flex items-center"
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating...
                </>
              ) : (
                'Create Position'
              )}
            </button>
          </div>
        </form>
      </div>

      {selectedTitle && Array.isArray(existingPositions) && existingPositions.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
          <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4">
            Existing Positions for Selected Title
          </h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                    Position
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-300 uppercase tracking-wider">
                    Description
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {(Array.isArray(existingPositions) ? existingPositions : []).map((position) => (
                  <tr key={position.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-800 dark:text-slate-200">
                      {position.title}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {position.description || '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};