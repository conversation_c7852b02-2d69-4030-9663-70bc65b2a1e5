'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, Download, Copy, CheckCircle } from 'lucide-react';
import Image from 'next/image';

export default function Setup2FAPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [enabling, setEnabling] = useState(false);
  const [twoFAEnabled, setTwoFAEnabled] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [secret, setSecret] = useState('');
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (status === 'loading') return;
    
    // Check if user already has 2FA enabled
    if ((session?.user as any)?.twoFactorEnabled) {
      setTwoFAEnabled(true);
      setLoading(false);
      return;
    }

    // If 2FA is not enabled, generate setup data
    generate2FASetup();
  }, [session, status]);

  const generate2FASetup = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/user/2fa', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to generate 2FA setup');
      }

      const data = await response.json();
      setQrCodeUrl(data.qrCodeUrl);
      setSecret(data.secret);
      
      // Generate backup codes
      const codes = Array.from({ length: 10 }, () => 
        Math.random().toString(36).substring(2, 10).toUpperCase()
      );
      setBackupCodes(codes);
      
    } catch (error: any) {
      toast.error(error.message || 'Failed to generate 2FA setup');
    } finally {
      setLoading(false);
    }
  };

  const handleEnable2FA = async () => {
    try {
      setEnabling(true);
      
      // Save backup codes to server
      const response = await fetch('/api/user/2fa/backup-codes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ backupCodes }),
      });

      if (!response.ok) {
        throw new Error('Failed to save backup codes');
      }

      setTwoFAEnabled(true);
      toast.success('Two-factor authentication enabled successfully!');
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
      
    } catch (error: any) {
      toast.error(error.message || 'Failed to enable 2FA');
    } finally {
      setEnabling(false);
    }
  };

  const copyBackupCodes = () => {
    const codesText = backupCodes.join('\n');
    navigator.clipboard.writeText(codesText);
    setCopied(true);
    toast.success('Backup codes copied to clipboard');
    setTimeout(() => setCopied(false), 2000);
  };

  const downloadBackupCodes = () => {
    const codesText = backupCodes.join('\n');
    const blob = new Blob([codesText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'nwa-2fa-backup-codes.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Backup codes downloaded');
  };

  if (status === 'loading' || loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading 2FA setup...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (twoFAEnabled) {
    return (
      <DashboardLayout>
        <div className="max-w-2xl mx-auto py-8">
          <Card>
            <CardHeader className="text-center">
              <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <CardTitle>Two-Factor Authentication Enabled</CardTitle>
              <p className="text-gray-600 dark:text-gray-400">
                Your account is now protected with two-factor authentication.
              </p>
            </CardHeader>
            <CardContent className="text-center">
              <Button onClick={() => router.push('/dashboard')}>
                Go to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="w-6 h-6 mr-2 text-blue-500" />
              Set Up Two-Factor Authentication
            </CardTitle>
            <p className="text-gray-600 dark:text-gray-400">
              Two-factor authentication is required for your account. Follow these steps to set it up.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Step 1: Download Authenticator App */}
            <div>
              <h3 className="text-lg font-semibold mb-2">Step 1: Download an Authenticator App</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Download one of these authenticator apps on your phone:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 mb-2">
                    <span className="text-lg font-semibold">Google Authenticator</span>
                  </div>
                  <p className="text-xs text-gray-500">Android & iOS</p>
                </div>
                <div className="text-center">
                  <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 mb-2">
                    <span className="text-lg font-semibold">Microsoft Authenticator</span>
                  </div>
                  <p className="text-xs text-gray-500">Android & iOS</p>
                </div>
                <div className="text-center">
                  <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 mb-2">
                    <span className="text-lg font-semibold">Authy</span>
                  </div>
                  <p className="text-xs text-gray-500">Android, iOS & Desktop</p>
                </div>
              </div>
            </div>

            {/* Step 2: Scan QR Code */}
            <div>
              <h3 className="text-lg font-semibold mb-2">Step 2: Scan QR Code</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Open your authenticator app and scan this QR code:
              </p>
              {qrCodeUrl && (
                <div className="text-center" data-testid="setup-2fa-qr">
                  <Image
                    src={qrCodeUrl}
                    alt="2FA QR Code"
                    width={200}
                    height={200}
                    className="mx-auto border rounded-lg"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    Or enter this code manually: <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">{secret}</code>
                  </p>
                </div>
              )}
            </div>

            {/* Step 3: Backup Codes */}
            <div>
              <h3 className="text-lg font-semibold mb-2">Step 3: Save Your Backup Codes</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                These backup codes can be used if you lose access to your authenticator app. 
                Save them in a secure place:
              </p>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 mb-4">
                <div className="grid grid-cols-2 gap-2 text-sm font-mono">
                  {backupCodes.map((code, index) => (
                    <div key={index} className="text-center py-2 bg-white dark:bg-gray-700 rounded">
                      {code}
                    </div>
                  ))}
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={copyBackupCodes}
                  disabled={copied}
                  data-testid="setup-2fa-copy"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  {copied ? 'Copied!' : 'Copy Codes'}
                </Button>
                <Button
                  variant="outline"
                  onClick={downloadBackupCodes}
                  data-testid="setup-2fa-download"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download Codes
                </Button>
              </div>
            </div>

            {/* Step 4: Enable */}
            <div className="text-center pt-4">
              <Button
                onClick={handleEnable2FA}
                disabled={enabling}
                className="w-full md:w-auto"
                data-testid="setup-2fa-enable"
              >
                {enabling ? 'Enabling 2FA...' : 'Enable Two-Factor Authentication'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
