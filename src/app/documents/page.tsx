'use client';

import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { OrdinancesTab } from '@/components/documents/OrdinancesTab';
import { DirectivesTab } from '@/components/documents/DirectivesTab';
import { FileText, ScrollText } from 'lucide-react';

export default function DocumentsPage() {
  const [activeTab, setActiveTab] = useState<'ordinances' | 'directives'>('ordinances');

  const documentSections = [
    {
      id: 'ordinances',
      title: 'Ordinances',
      description: 'Create and manage ordinances',
      icon: <FileText className="h-5 w-5" />,
      component: <OrdinancesTab />
    },
    {
      id: 'directives',
      title: 'Directives',
      description: 'Create and manage directives',
      icon: <ScrollText className="h-5 w-5" />,
      component: <DirectivesTab />
    }
  ];

  const activeSection = documentSections.find(section => section.id === activeTab) || documentSections[0];

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-100 mb-6">Documents Management</h1>
          
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Sidebar Navigation */}
            <div className="w-full lg:w-64">
              <nav className="space-y-1 bg-slate-800 p-2 rounded-lg">
                {documentSections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveTab(section.id as 'ordinances' | 'directives')}
                    className={`flex items-center p-3 rounded transition-colors duration-200 text-sm font-medium w-full text-left text-white ${
                      activeTab === section.id
                        ? 'bg-slate-700'
                        : 'hover:bg-slate-700'
                    }`}
                  >
                    <div className="mr-3 text-white">
                      {section.icon}
                    </div>
                    {section.title}
                  </button>
                ))}
              </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-2 flex items-center text-slate-800 dark:text-slate-100">
                  <div className="mr-3 text-slate-600 dark:text-slate-400">
                    {activeSection.icon}
                  </div>
                  {activeSection.title}
                </h2>
                <p className="text-slate-600 dark:text-slate-400 mb-6 text-sm">
                  {activeSection.description}
                </p>
                {activeSection.component}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
