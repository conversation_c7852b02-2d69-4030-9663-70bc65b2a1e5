import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { userAuditLogger } from '@/lib/user-audit-logger';
import { validatePostalCode } from '@/lib/utils/address-format-utils';
import { performanceMonitor } from '@/lib/performance';
import { broadcastNotification } from '@/lib/notifications';

// Input sanitization function
function sanitizeInput(input: string): string {
  if (!input) return input;
  return input.trim().replace(/[<>]/g, '');
}

// Validation schema for user creation
const userCreateSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  surname: z.string().min(1, 'Surname is required'),
  email: z.string().email('Invalid email format'),
  personalEmail: z.string().email('Invalid personal email format').optional(),
  nwaEmail: z.string().email('Invalid NWA email format').optional(),
  dob: z.string().optional(),
  phone: z.string().optional(),
  mobile: z.string().optional(),
  // New structured address fields
  streetAddress1: z.string().optional(),
  streetAddress2: z.string().optional(),
  town: z.string().optional(),
  country: z.string().optional(),
  city: z.string().optional(),
  regionId: z.string().optional(),
  regionText: z.string().optional(),
  postalCode: z.string().optional(),
  // Backward compatibility - legacy field
  streetAddress: z.string().optional(),
  peaceAmbassadorNumber: z.string().optional(),
  treatyNumbers: z.array(z.object({
    treatyTypeId: z.string(),
    treatyNumber: z.string()
  })).optional(),
  bio: z.string().optional(),
  titleId: z.string().optional(),
  positionId: z.string().optional(),
});

// Validation schema for user update
const userUpdateSchema = z.object({
  firstName: z.string().min(1, 'First name is required').optional(),
  surname: z.string().min(1, 'Surname is required').optional(),
  email: z.string().email('Invalid email format').optional(),
  personalEmail: z.string().email('Invalid personal email format').optional(),
  nwaEmail: z.string().email('Invalid NWA email format').optional(),
  dob: z.string().optional(),
  phone: z.string().optional(),
  mobile: z.string().optional(),
  // New structured address fields
  streetAddress1: z.string().optional(),
  streetAddress2: z.string().optional(),
  town: z.string().optional(),
  country: z.string().optional(),
  city: z.string().optional(),
  regionId: z.string().optional(),
  regionText: z.string().optional(),
  postalCode: z.string().optional(),
  // Backward compatibility - legacy field
  streetAddress: z.string().optional(),
  peaceAmbassadorNumber: z.string().optional(),
  treatyNumbers: z.array(z.object({
    treatyTypeId: z.string(),
    treatyNumber: z.string()
  })).optional(),
  bio: z.string().optional(),
  titleId: z.string().optional(),
  positionId: z.string().optional(),
});

export async function GET(req: NextRequest) {
  performanceMonitor.startTimer('api_users_get', { endpoint: '/api/users' });
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const search = searchParams.get('search') || '';
    const position = searchParams.get('position') || '';
    const title = searchParams.get('title') || '';
    const email = searchParams.get('email') || '';
    const phone = searchParams.get('phone') || '';
    const country = searchParams.get('country') || '';
    const city = searchParams.get('city') || '';
    
    const skip = (page - 1) * limit;
    
    // Build where clause for search
    const where: any = {};
    
    // Add search conditions
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    // Add filter conditions
    if (position) {
      where.positionId = position;
    }
    
    if (title) {
      where.titleId = title;
    }
    
    if (email) {
      where.email = { contains: email, mode: 'insensitive' };
    }
    
    if (phone) {
      where.profile = {
        mobile: { contains: phone, mode: 'insensitive' }
      };
    }
    
    if (country) {
      where.profile = {
        ...where.profile,
        country: { contains: country, mode: 'insensitive' }
      };
    }
    
    if (city) {
      where.profile = {
        ...where.profile,
        city: { contains: city, mode: 'insensitive' }
      };
    }
    
    // Fetch users with pagination
    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where,
        include: {
          profile: {
            include: {
              country: true,
              city: true,
            },
          },
          userPositions: {
            include: {
              position: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.user.count({ where }),
    ]);
    
    const totalPages = Math.ceil(totalCount / limit);
    
    return NextResponse.json({
      users: users.map(user => ({
        id: user.id,
        firstName: user.profile?.firstName || user.name?.split(' ')[0] || '',
        surname: user.profile?.lastName || user.name?.split(' ')[1] || user.name || '',
        email: user.email,
        personalEmail: user.profile?.personalEmail,
        nwaEmail: user.profile?.nwaEmail,
        dob: user.profile?.dateOfBirth,
        phone: user.profile?.phone,
        mobile: user.profile?.mobile,
        country: user.profile?.country?.name,
        city: user.profile?.city?.name,
        peaceAmbassadorNumber: user.profile?.peaceAmbassadorNumber || '',
        bio: user.profile?.bio,
        titleId: user.profile?.titleId || '',
        positionId: user.userPositions[0]?.positionId || '', // Get the first position
        created: user.createdAt,
        updated: user.updatedAt,
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        pageSize: limit,
      },
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  } finally {
    performanceMonitor.endTimer('api_users_get');
  }
}

export async function POST(req: NextRequest) {
  performanceMonitor.startTimer('api_users_post', { endpoint: '/api/users' });
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    
    // Validate input
    const validation = userCreateSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    const {
      firstName,
      surname,
      email,
      personalEmail,
      nwaEmail,
      dob,
      phone,
      mobile,
      // New address fields
      streetAddress1,
      streetAddress2,
      town,
      postalCode,
      regionId,
      regionText,
      country: countryName, // Rename to countryName to avoid confusion
      city: cityName,       // Rename to cityName to avoid confusion
      // Legacy address field for backward compatibility
      streetAddress,
      peaceAmbassadorNumber,
      treatyNumbers,
      bio,
      titleId,
      positionId
    } = validation.data;

    // Sanitize inputs
    const sanitizedFirstName = sanitizeInput(firstName);
    const sanitizedSurname = sanitizeInput(surname);
    const sanitizedEmail = sanitizeInput(email);
    const sanitizedPersonalEmail = personalEmail ? sanitizeInput(personalEmail) : undefined;
    const sanitizedNwaEmail = nwaEmail ? sanitizeInput(nwaEmail) : undefined;
    const sanitizedPhone = phone ? sanitizeInput(phone) : undefined;
    const sanitizedMobile = mobile ? sanitizeInput(mobile) : undefined;
    
    // Sanitize new address fields
    const sanitizedStreetAddress1 = streetAddress1 ? sanitizeInput(streetAddress1) : (streetAddress ? sanitizeInput(streetAddress) : undefined);
    const sanitizedStreetAddress2 = streetAddress2 ? sanitizeInput(streetAddress2) : undefined;
    const sanitizedTown = town ? sanitizeInput(town) : undefined;
    const sanitizedPostalCode = postalCode ? sanitizeInput(postalCode) : undefined;
    const sanitizedRegionText = regionText ? sanitizeInput(regionText) : undefined;
    
    const sanitizedCountryName = countryName ? sanitizeInput(countryName) : undefined;
    const sanitizedCityName = cityName ? sanitizeInput(cityName) : undefined;
    const sanitizedPeaceAmbassadorNumber = peaceAmbassadorNumber ? sanitizeInput(peaceAmbassadorNumber) : undefined;
    const sanitizedBio = bio ? sanitizeInput(bio) : undefined;
    
    // Find the country and city by name
    const country = sanitizedCountryName ? await prisma.country.findFirst({
      where: { name: sanitizedCountryName }
    }) : null;

    if (sanitizedCountryName && !country) {
      return NextResponse.json({ error: 'Invalid country' }, { status: 400 });
    }

    const city = sanitizedCityName && country ? await prisma.city.findFirst({
      where: {
        name: sanitizedCityName,
        countryId: country.id
      }
    }) : null;

    if (sanitizedCityName && !city) {
      return NextResponse.json({ error: 'Invalid city' }, { status: 400 });
    }
    
    // Validate region if regionId is provided
    let region = null;
    if (regionId && country) {
      region = await prisma.region.findFirst({
        where: {
          id: parseInt(regionId),
          countryId: country.id,
          isActive: true
        }
      });
      
      if (!region) {
        return NextResponse.json({ error: 'Invalid region for the selected country' }, { status: 400 });
      }
    }

    // Validate postal code against country rules (if country is known)
    if (country) {
      const addressFormat = await prisma.countryAddressFormat.findUnique({
        where: { countryId: country.id }
      });

      const effectivePostalPattern = addressFormat?.postalCodeFormat || '^.+$';
      const postalRequired = !!addressFormat?.postalCodeRequired;

      if (postalRequired && (!sanitizedPostalCode || sanitizedPostalCode.trim() === '')) {
        return NextResponse.json({ error: 'Postal code is required for the selected country' }, { status: 400 });
      }

      if (sanitizedPostalCode) {
        const isValidPostal = validatePostalCode(sanitizedPostalCode, effectivePostalPattern);
        if (!isValidPostal) {
          return NextResponse.json({ error: 'Invalid postal code format for the selected country' }, { status: 400 });
        }
      }
    }
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: sanitizedEmail },
    });

    if (existingUser) {
      return NextResponse.json({ error: 'User with this email already exists' }, { status: 400 });
    }

    // Check if user with same name already exists
    const existingUserByName = await prisma.user.findFirst({
      where: {
        name: `${sanitizedFirstName} ${sanitizedSurname}`,
      },
    });

    if (existingUserByName) {
      return NextResponse.json({ error: 'User with this name already exists' }, { status: 400 });
    }

    // Check if phone number already exists (if provided)
    if (sanitizedPhone) {
      const existingUserByPhone = await prisma.userProfile.findFirst({
        where: { phone: sanitizedPhone },
      });

      if (existingUserByPhone) {
        return NextResponse.json({ error: 'User with this phone number already exists' }, { status: 400 });
      }
    }

    // Check if mobile number already exists (if provided)
    if (sanitizedMobile) {
      const existingUserByMobile = await prisma.userProfile.findFirst({
        where: { mobile: sanitizedMobile },
      });

      if (existingUserByMobile) {
        return NextResponse.json({ error: 'User with this mobile number already exists' }, { status: 400 });
      }
    }

    // Check if personal email already exists (if provided)
    if (sanitizedPersonalEmail) {
      const existingUserByPersonalEmail = await prisma.userProfile.findFirst({
        where: { personalEmail: sanitizedPersonalEmail },
      });

      if (existingUserByPersonalEmail) {
        return NextResponse.json({ error: 'User with this personal email already exists' }, { status: 400 });
      }
    }

    // Check if NWA email already exists (if provided)
    if (sanitizedNwaEmail) {
      const existingUserByNwaEmail = await prisma.userProfile.findFirst({
        where: { nwaEmail: sanitizedNwaEmail },
      });

      if (existingUserByNwaEmail) {
        return NextResponse.json({ error: 'User with this NWA email already exists' }, { status: 400 });
      }
    }
    
    // Create user
    const user = await prisma.user.create({
      data: {
        name: `${sanitizedFirstName} ${sanitizedSurname}`,
        email: sanitizedEmail,
        profile: {
          create: {
            firstName: sanitizedFirstName,
            lastName: sanitizedSurname,
            personalEmail: sanitizedPersonalEmail,
            nwaEmail: sanitizedNwaEmail || sanitizedEmail, // Use provided NWA email or fallback to main email
            dateOfBirth: dob ? new Date(dob) : undefined,
            phone: sanitizedPhone,
            mobile: sanitizedMobile,
            // New structured address fields
            streetAddress1: sanitizedStreetAddress1,
            streetAddress2: sanitizedStreetAddress2,
            town: sanitizedTown,
            postalCode: sanitizedPostalCode,
            regionId: region?.id,
            // If a predefined region is selected, prefer it and omit free-text
            regionText: region ? undefined : sanitizedRegionText,
            countryId: country?.id, // Use countryId
            cityId: city?.id,       // Use cityId
            peaceAmbassadorNumber: sanitizedPeaceAmbassadorNumber,
            bio: sanitizedBio,
            titleId,
          },
        },
        // Note: Position assignment would require a separate UserPosition record
      },
    });

    // Create identification records if provided
    const identificationPromises: Promise<any>[] = [];
    
    
    // Create treaty number records if provided
    if (treatyNumbers && treatyNumbers.length > 0) {
      for (const treatyNum of treatyNumbers) {
        if (treatyNum.treatyTypeId && treatyNum.treatyNumber) {
          identificationPromises.push(
            prisma.userTreatyNumber.create({
              data: {
                userId: user.id,
                treatyTypeId: treatyNum.treatyTypeId,
                treatyNumber: sanitizeInput(treatyNum.treatyNumber),
              }
            })
          );
        }
      }
    }
    
    // Execute all identification creations
    if (identificationPromises.length > 0) {
      try {
        await Promise.all(identificationPromises);
      } catch (identificationError) {
        console.error('Failed to create identification records:', identificationError);
        // Continue with user creation even if identification records fail
      }
    }
    
    // Log user creation for audit purposes
    try {
      await userAuditLogger.logUserCreation(
        user.id,
        {
          name: user.name,
          email: user.email,
          firstName: sanitizedFirstName,
          lastName: sanitizedSurname,
          personalEmail: sanitizedPersonalEmail,
          nwaEmail: sanitizedNwaEmail || sanitizedEmail,
          dateOfBirth: dob,
          phone: sanitizedPhone,
          mobile: sanitizedMobile,
          country: country ? { id: country.id, name: country.name } : undefined,
          city: city ? { id: city.id, name: city.name } : undefined,
          peaceAmbassadorNumber: sanitizedPeaceAmbassadorNumber,
          bio: sanitizedBio,
          titleId,
          createdAt: user.createdAt,
        },
        (session.user as any).id, // Admin user creating the user
        {
          source: 'admin_create_user',
          ipAddress: userAuditLogger.getClientIP(req.headers),
          userAgent: userAuditLogger.getUserAgent(req.headers),
        }
      );
    } catch (auditError) {
      console.error('Failed to log user creation audit:', auditError);
      // Continue with the creation process even if audit logging fails
    }

    // Send notification for new user creation
    try {
      await prisma.notification.create({
        data: {
          type: 'USER_CREATED',
          message: `New user ${sanitizedFirstName} ${sanitizedSurname} (${sanitizedEmail}) has been registered and requires approval`,
          recipients: ['admin'],
          trigger: 'user_creation',
          triggeredBy: (session.user as any).id,
          priority: 'HIGH',
          category: 'USER_MANAGEMENT',
          recipientTypes: {
            roles: ['admin'],
          },
          data: {
            userId: user.id,
            userEmail: sanitizedEmail,
            userName: `${sanitizedFirstName} ${sanitizedSurname}`,
            createdBy: (session.user as any).id,
            registrationDate: user.createdAt.toISOString(),
          },
        },
      });

      // Broadcast notification to connected admin clients
      await broadcastNotification({
        type: 'USER_CREATED',
        message: `New user ${sanitizedFirstName} ${sanitizedSurname} has been registered`,
        recipients: ['admin'],
        priority: 'HIGH',
        category: 'USER_MANAGEMENT',
        data: {
          userId: user.id,
          userEmail: sanitizedEmail,
          userName: `${sanitizedFirstName} ${sanitizedSurname}`,
        },
      });
    } catch (notificationError) {
      console.error('Failed to send user creation notification:', notificationError);
      // Continue with the creation process even if notification fails
    }
    
    return NextResponse.json({
      message: 'User created successfully',
      id: user.id,
    });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  } finally {
    performanceMonitor.endTimer('api_users_post');
  }
}

export async function PUT(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { id, ...updateData } = body;
    
    if (!id) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }
    
    // Validate input
    const validation = userUpdateSchema.safeParse(updateData);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    const {
      firstName,
      surname,
      email,
      personalEmail,
      nwaEmail,
      dob,
      phone,
      mobile,
      country: countryName, // Rename to countryName to avoid confusion
      city: cityName,       // Rename to cityName to avoid confusion
      peaceAmbassadorNumber,
      bio,
      titleId,
      positionId
    } = validation.data;
    
    // Check for duplicates if email is being updated
    if (email) {
      const existingUser = await prisma.user.findFirst({
        where: {
          email: sanitizeInput(email),
          NOT: { id }
        },
      });

      if (existingUser) {
        return NextResponse.json({ error: 'User with this email already exists' }, { status: 400 });
      }
    }

    // Check for duplicate name if name is being updated
    if (firstName && surname) {
      const existingUserByName = await prisma.user.findFirst({
        where: {
          name: `${sanitizeInput(firstName)} ${sanitizeInput(surname)}`,
          NOT: { id }
        },
      });

      if (existingUserByName) {
        return NextResponse.json({ error: 'User with this name already exists' }, { status: 400 });
      }
    }

    // Check for duplicate phone if phone is being updated
    if (phone) {
      const existingUserByPhone = await prisma.userProfile.findFirst({
        where: {
          phone: sanitizeInput(phone),
          NOT: { userId: id }
        },
      });

      if (existingUserByPhone) {
        return NextResponse.json({ error: 'User with this phone number already exists' }, { status: 400 });
      }
    }

    // Check for duplicate mobile if mobile is being updated
    if (mobile) {
      const existingUserByMobile = await prisma.userProfile.findFirst({
        where: {
          mobile: sanitizeInput(mobile),
          NOT: { userId: id }
        },
      });

      if (existingUserByMobile) {
        return NextResponse.json({ error: 'User with this mobile number already exists' }, { status: 400 });
      }
    }

    // Check for duplicate personal email if personal email is being updated
    if (personalEmail) {
      const existingUserByPersonalEmail = await prisma.userProfile.findFirst({
        where: {
          personalEmail: sanitizeInput(personalEmail),
          NOT: { userId: id }
        },
      });

      if (existingUserByPersonalEmail) {
        return NextResponse.json({ error: 'User with this personal email already exists' }, { status: 400 });
      }
    }

    // Check for duplicate NWA email if NWA email is being updated
    if (nwaEmail) {
      const existingUserByNwaEmail = await prisma.userProfile.findFirst({
        where: {
          nwaEmail: sanitizeInput(nwaEmail),
          NOT: { userId: id }
        },
      });

      if (existingUserByNwaEmail) {
        return NextResponse.json({ error: 'User with this NWA email already exists' }, { status: 400 });
      }
    }

    // Find the country and city by name if they are provided
    let countryId: number | undefined;
    let cityId: number | undefined;

    if (countryName) {
      const country = await prisma.country.findFirst({
        where: { name: sanitizeInput(countryName) }
      });

      if (!country) {
        return NextResponse.json({ error: 'Invalid country' }, { status: 400 });
      }

      countryId = country.id;
    }

    if (cityName && countryId) {
      const city = await prisma.city.findFirst({
        where: {
          name: sanitizeInput(cityName),
          countryId: countryId
        }
      });

      if (!city) {
        return NextResponse.json({ error: 'Invalid city' }, { status: 400 });
      }

      cityId = city.id;
    }
    
    // Get current user for audit logging
    const currentUser = await prisma.user.findUnique({
      where: { id },
      include: {
        profile: true,
      },
    });

    // Update user
    const user = await prisma.user.update({
      where: { id },
      data: {
        ...(firstName || surname) && {
          name: `${firstName ? sanitizeInput(firstName) : (currentUser?.profile?.firstName || '')} ${surname ? sanitizeInput(surname) : (currentUser?.profile?.lastName || '')}`.trim()
        },
        ...(email && { email: sanitizeInput(email) }),
        profile: {
          update: {
            ...(firstName !== undefined && { firstName: sanitizeInput(firstName) }),
            ...(surname !== undefined && { lastName: sanitizeInput(surname) }),
            ...(personalEmail !== undefined && { personalEmail: sanitizeInput(personalEmail) }),
            ...(nwaEmail !== undefined && { nwaEmail: sanitizeInput(nwaEmail) }),
            ...(dob !== undefined && { dateOfBirth: dob ? new Date(dob) : undefined }),
            ...(phone !== undefined && { phone: sanitizeInput(phone) }),
            ...(mobile !== undefined && { mobile: sanitizeInput(mobile) }),
            ...(countryId !== undefined && { countryId }), // Use countryId
            ...(cityId !== undefined && { cityId }),       // Use cityId
            ...(peaceAmbassadorNumber !== undefined && { peaceAmbassadorNumber: sanitizeInput(peaceAmbassadorNumber) }),
            ...(bio !== undefined && { bio: sanitizeInput(bio) }),
            ...(titleId !== undefined && { titleId }),
          },
        },
      },
      include: {
        profile: true,
      },
    });
    
    // Log user update for audit purposes
    try {
      // Fetch country and city names for audit logging
      let previousCountryName: string | null = null;
      let previousCityName: string | null = null;

      if (currentUser?.profile?.countryId) {
        const previousCountry = await prisma.country.findUnique({
          where: { id: currentUser.profile.countryId }
        });
        previousCountryName = previousCountry?.name || null;
      }

      if (currentUser?.profile?.cityId) {
        const previousCity = await prisma.city.findUnique({
          where: { id: currentUser.profile.cityId }
        });
        previousCityName = previousCity?.name || null;
      }

      // Fetch country and city names for the updated user
      let newCountryName: string | null = null;
      let newCityName: string | null = null;

      if (user.profile?.countryId) {
        const newCountry = await prisma.country.findUnique({
          where: { id: user.profile.countryId }
        });
        newCountryName = newCountry?.name || null;
      }

      if (user.profile?.cityId) {
        const newCity = await prisma.city.findUnique({
          where: { id: user.profile.cityId }
        });
        newCityName = newCity?.name || null;
      }

      await userAuditLogger.logUserUpdate(
        id,
        {
          name: currentUser?.name,
          email: currentUser?.email,
          firstName: currentUser?.profile?.firstName,
          lastName: currentUser?.profile?.lastName,
          personalEmail: currentUser?.profile?.personalEmail,
          nwaEmail: currentUser?.profile?.nwaEmail,
          dateOfBirth: currentUser?.profile?.dateOfBirth,
          phone: currentUser?.profile?.phone,
          mobile: currentUser?.profile?.mobile,
          country: previousCountryName, // Use country name
          city: previousCityName,       // Use city name
          peaceAmbassadorNumber: currentUser?.profile?.peaceAmbassadorNumber,
          bio: currentUser?.profile?.bio,
          titleId: currentUser?.profile?.titleId,
        },
        {
          name: user.name,
          email: user.email,
          firstName: user.profile?.firstName,
          lastName: user.profile?.lastName,
          personalEmail: user.profile?.personalEmail,
          nwaEmail: user.profile?.nwaEmail,
          dateOfBirth: user.profile?.dateOfBirth,
          phone: user.profile?.phone,
          mobile: user.profile?.mobile,
          country: newCountryName, // Use country name
          city: newCityName,       // Use city name
          peaceAmbassadorNumber: user.profile?.peaceAmbassadorNumber,
          bio: user.profile?.bio,
          titleId: user.profile?.titleId,
        },
        (session.user as any).id, // Admin user updating the user
        {
          source: 'admin_update_user',
          ipAddress: userAuditLogger.getClientIP(req.headers),
          userAgent: userAuditLogger.getUserAgent(req.headers),
        }
      );
    } catch (auditError) {
      console.error('Failed to log user update audit:', auditError);
      // Continue with the update process even if audit logging fails
    }

    // Send notification for user update
    try {
      const changes: string[] = [];

      if (currentUser?.name !== user.name) changes.push('name');
      if (currentUser?.email !== user.email) changes.push('email');
      if (currentUser?.profile?.phone !== user.profile?.phone) changes.push('phone');
      if (currentUser?.profile?.mobile !== user.profile?.mobile) changes.push('mobile');
      if (currentUser?.profile?.personalEmail !== user.profile?.personalEmail) changes.push('personal email');
      if (currentUser?.profile?.nwaEmail !== user.profile?.nwaEmail) changes.push('NWA email');
      if (currentUser?.profile?.countryId !== user.profile?.countryId) changes.push('country');
      if (currentUser?.profile?.cityId !== user.profile?.cityId) changes.push('city');
      if (currentUser?.profile?.titleId !== user.profile?.titleId) changes.push('title');

      if (changes.length > 0) {
        await prisma.notification.create({
          data: {
            type: 'USER_UPDATED',
            message: `User ${user.name} (${user.email}) has been updated. Changed fields: ${changes.join(', ')}`,
            recipients: ['admin'],
            trigger: 'user_update',
            triggeredBy: (session.user as any).id,
            priority: 'NORMAL',
            category: 'USER_MANAGEMENT',
            recipientTypes: {
              roles: ['admin'],
            },
            data: {
              userId: user.id,
              userEmail: user.email,
              userName: user.name,
              updatedBy: (session.user as any).id,
              changedFields: changes,
              updateDate: user.updatedAt.toISOString(),
            },
          },
        });

        // Broadcast notification to connected admin clients
        await broadcastNotification({
          type: 'USER_UPDATED',
          message: `User ${user.name} has been updated`,
          recipients: ['admin'],
          priority: 'NORMAL',
          category: 'USER_MANAGEMENT',
          data: {
            userId: user.id,
            userEmail: user.email,
            userName: user.name,
            changedFields: changes,
          },
        });
      }
    } catch (notificationError) {
      console.error('Failed to send user update notification:', notificationError);
      // Continue with the update process even if notification fails
    }
    
    return NextResponse.json({
      message: 'User updated successfully',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        profile: user.profile,
      },
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}