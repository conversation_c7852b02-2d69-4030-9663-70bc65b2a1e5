import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = user.userRoles.some(userRole => 
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse query parameters
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = parseInt(url.searchParams.get('limit') || '10', 10);
    const query = url.searchParams.get('q') || '';

    // Validate query parameter
    if (!query || query.trim() === '') {
      return NextResponse.json(
        { error: 'Bad Request', message: 'Search query is required' },
        { status: 400 }
      );
    }

    if (query.trim().length < 2) {
      return NextResponse.json(
        { error: 'Bad Request', message: 'Search query must be at least 2 characters long' },
        { status: 400 }
      );
    }

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Bad Request', message: 'Invalid pagination parameters' },
        { status: 400 }
      );
    }

    // Build where clause for search
    const where = {
      OR: [
        { name: { contains: query.trim(), mode: 'insensitive' as const } },
        { email: { contains: query.trim(), mode: 'insensitive' as const } },
        { profile: { nwaEmail: { contains: query.trim(), mode: 'insensitive' as const } } },
        { profile: { firstName: { contains: query.trim(), mode: 'insensitive' as const } } },
        { profile: { lastName: { contains: query.trim(), mode: 'insensitive' as const } } },
      ],
    };

    // Debug: check if darren exists
    if (query.trim() === 'dar') {
      console.log('DEBUG: Searching for dar users, checking database...');
      const allUsers = await prisma.user.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
              nwaEmail: true
            }
          }
        },
        take: 5
      });
      console.log('DEBUG: First 5 users in database:', allUsers);
      
      // Check specifically for darren
      const darrenUsers = await prisma.user.findMany({
        where: {
          OR: [
            { name: { contains: 'darren', mode: 'insensitive' } },
            { profile: { firstName: { contains: 'darren', mode: 'insensitive' } } },
            { profile: { lastName: { contains: 'darren', mode: 'insensitive' } } },
          ]
        },
        select: {
          id: true,
          name: true,
          email: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
              nwaEmail: true
            }
          }
        }
      });
      console.log('DEBUG: Darren users found:', darrenUsers);
    }

    // Fetch users with pagination
    let [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        include: {
          profile: {
            select: {
              firstName: true,
              lastName: true,
              nwaEmail: true,
              phone: true,
              mobile: true,
              personalEmail: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.user.count({ where }),
    ]);

    // Debug: log what we found
    console.log('Search results:', { 
      query: query.trim(), 
      usersFound: users.length, 
      totalUsers: total,
      firstUser: users[0],
      whereClause: where
    });

    // If no users found with the complex query, try simpler searches
    if (users.length === 0) {
      console.log('No users found with complex search, trying fallbacks...');
      
      // Try searching just by name (most common case)
      const nameSearch = await prisma.user.findMany({
        where: {
          name: { contains: query.trim(), mode: 'insensitive' }
        },
        include: {
          profile: {
            select: {
              firstName: true,
              lastName: true,
              nwaEmail: true,
              phone: true,
              mobile: true,
              personalEmail: true,
            },
          },
        },
        take: limit,
      });
      
      console.log('Name search results:', nameSearch.length);
      
      if (nameSearch.length > 0) {
        users.push(...nameSearch);
        total = nameSearch.length;
      } else {
        // Try searching by email
        const emailSearch = await prisma.user.findMany({
          where: {
            email: { contains: query.trim(), mode: 'insensitive' }
          },
          include: {
            profile: {
              select: {
                firstName: true,
                lastName: true,
                nwaEmail: true,
                phone: true,
                mobile: true,
                personalEmail: true,
              },
            },
          },
          take: limit,
        });
        
        console.log('Email search results:', emailSearch.length);
        
        if (emailSearch.length > 0) {
          users.push(...emailSearch);
          total = emailSearch.length;
        }
      }
    }

    // Transform users for response
    const transformedUsers = users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      nwaEmail: user.profile?.nwaEmail,
      phone: user.profile?.phone || user.profile?.mobile || null,
      createdAt: user.createdAt.toISOString(),
      profile: user.profile ? {
        firstName: user.profile.firstName,
        lastName: user.profile.lastName,
        nwaEmail: user.profile.nwaEmail,
        phone: user.profile.phone,
        mobile: user.profile.mobile,
        personalEmail: user.profile.personalEmail,
      } : null,
    }));

    // Return paginated response
    return NextResponse.json({
      users: transformedUsers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error searching users:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
