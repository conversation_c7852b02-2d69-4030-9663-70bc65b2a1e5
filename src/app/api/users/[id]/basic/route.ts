import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';
import { logUserAudit } from '@/lib/user-audit-logger';

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const adminUser = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!adminUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = adminUser.userRoles.some(userRole => 
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const body = await req.json();

    // Validate required fields
    const {
      firstName,
      surname,
      dob,
      email,
      personalEmail,
      nwaEmail,
      phone,
      mobile,
      bio
    } = body;

    if (!firstName || !surname || !email) {
      return NextResponse.json({ 
        error: 'Validation Error', 
        message: 'First name, surname, and email are required' 
      }, { status: 400 });
    }

    // Find the user to update
    const user = await prisma.user.findUnique({
      where: { id },
      include: { profile: true }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if email is already taken by another user
    if (email !== user.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });
      if (existingUser) {
        return NextResponse.json({ 
          error: 'Validation Error', 
          message: 'Email is already taken by another user' 
        }, { status: 400 });
      }
    }

    // Update user basic information
    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        name: `${firstName} ${surname}`,
        email: email,
        updatedAt: new Date()
      }
    });

    // Update or create profile with basic information
    const updatedProfile = await prisma.userProfile.upsert({
      where: { userId: id },
      update: {
        firstName: firstName,
        lastName: surname,
        dateOfBirth: dob ? new Date(dob) : null,
        personalEmail: personalEmail || null,
        nwaEmail: nwaEmail || null,
        phone: phone || null,
        mobile: mobile || null,
        bio: bio || null,
        updatedAt: new Date()
      },
      create: {
        userId: id,
        firstName: firstName,
        lastName: surname,
        dateOfBirth: dob ? new Date(dob) : null,
        personalEmail: personalEmail || null,
        nwaEmail: nwaEmail || null,
        phone: phone || null,
        mobile: mobile || null,
        bio: bio || null,
      }
    });

    // Log the audit trail
    await logUserAudit({
      userId: (session.user as any).id,
      action: 'update_user_basic_info',
      targetUserId: id,
      details: JSON.stringify({
        changes: {
          name: updatedUser.name,
          email: updatedUser.email,
          profile: {
            firstName: firstName,
            lastName: surname,
            dateOfBirth: dob,
            personalEmail: personalEmail,
            nwaEmail: nwaEmail,
            phone: phone,
            mobile: mobile,
            bio: bio,
          }
        },
        previousValues: {
          name: user.name,
          email: user.email,
          profile: user.profile
        }
      }),
      ipAddress: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown'
    });

    return NextResponse.json({
      message: 'Basic information updated successfully',
      user: {
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        profile: {
          firstName: updatedProfile.firstName,
          lastName: updatedProfile.lastName,
          dateOfBirth: updatedProfile.dateOfBirth?.toISOString(),
          personalEmail: updatedProfile.personalEmail,
          nwaEmail: updatedProfile.nwaEmail,
          phone: updatedProfile.phone,
          mobile: updatedProfile.mobile,
          bio: updatedProfile.bio,
        }
      }
    });
  } catch (error) {
    console.error('Error updating basic information:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}