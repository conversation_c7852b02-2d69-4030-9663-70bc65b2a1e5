import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { permissionInheritance } from '@/lib/services/permission-inheritance'
import { unifiedPermissions } from '@/lib/services/unified-permissions'
import { ServerAssignmentAuditService } from '@/lib/services/server-assignment-audit-service'

// GET /api/users/[id]/remote-servers - Get user's assigned remote servers
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: userId } = await params

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      return Response.json({ error: 'User not found' }, { status: 404 })
    }

    // Get user's remote server access
    const userServerAccess = await prisma.user_remote_server_access.findMany({
      where: {
        user_id: userId,
        is_active: true
      },
      include: {
        remote_servers: {
          select: {
            id: true,
            name: true,
            url: true,
            description: true,
            isActive: true
          }
        },
        users_user_remote_server_access_granted_byTousers: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    // Use unified permission service to get complete permissions picture
    const allPermissions: any[] = []

    for (const access of userServerAccess) {
      // Get all permissions for this user on this server using unified service
      const serverPermissions = await permissionInheritance.calculateUserServerPermissions(
        userId,
        access.remote_server_id
      )

      // Validate each permission using unified service for consistency
      const validatedPermissions = []
      for (const perm of serverPermissions.permissions) {
        const validation = await unifiedPermissions.checkPermission({
          userId,
          permission: perm.name,
          serverId: access.remote_server_id
        })

        if (validation.allowed) {
          validatedPermissions.push({
            permission: perm.name,
            source: perm.source,
            grantedAt: perm.grantedAt,
            expiresAt: perm.expiresAt,
            grantedBy: perm.grantedBy,
            roleId: perm.roleId,
            roleName: perm.roleName,
            context: 'remote',
            serverId: access.remote_server_id
          })
        }
      }

      allPermissions.push({
        id: access.id,
        userId: access.user_id,
        serverId: access.remote_server_id,
        server: access.remote_servers,
        grantedAt: access.granted_at,
        grantedBy: access.users_user_remote_server_access_granted_byTousers,
        notes: access.notes,
        permissions: validatedPermissions,
        roles: serverPermissions.roles,
        hasAccess: serverPermissions.hasAccess
      })
    }

    const result = allPermissions

    console.log('GET /api/users/[id]/remote-servers - Returning result:', {
      userId,
      assignmentCount: result.length,
      assignments: result.map(a => ({ id: a.id, serverId: a.serverId, serverName: a.server?.name }))
    })

    return Response.json({ success: true, data: result })
  } catch (error) {
    console.error('Error fetching user remote servers:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/users/[id]/remote-servers - Assign user to remote server
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  let userId: string = ''
  let serverId: string = ''
  let userAccess: any = null

  // Add immediate response to test if route is being called
  console.log('POST /api/users/[id]/remote-servers - Route called')

  // Add a simple test response to verify the route is working
  try {
    const testUrl = new URL(request.url)
    if (testUrl.searchParams.get('test') === '1') {
      console.log('Test mode detected')
      return Response.json({
        success: true,
        test: true,
        message: 'API route is working'
      })
    }
  } catch (error) {
    console.error('Error checking test parameter:', error)
  }

  try {
    console.log('POST /api/users/[id]/remote-servers - Starting request')

    // Get session with detailed logging
    console.log('Getting server session...')
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      console.error('Unauthorized: No valid session found')
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }
    console.log('Session obtained successfully for user:', (session.user as any)?.id)

    // Get parameters
    console.log('Extracting parameters...')
    const { id } = await params
    userId = id
    console.log('User ID:', userId)

    // Parse request body with error handling
    let body
    try {
      console.log('Parsing request body...')
      body = await request.json()
      console.log('Request body parsed successfully:', { ...body, serverId: body.serverId ? '[PRESENT]' : '[MISSING]' })
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError)
      return Response.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      )
    }

    const { serverId: serverIdParam, permissions = [], notes } = body
    serverId = serverIdParam

    if (!serverId) {
      console.error('Server ID is missing from request body')
      return Response.json(
        { error: 'Server ID is required' },
        { status: 400 }
      )
    }

    // Verify user exists
    console.log('Verifying user exists...')
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      console.error('User not found:', userId)
      return Response.json({ error: 'User not found' }, { status: 404 })
    }
    console.log('User verified successfully:', user.name || user.email)

    // Verify server exists
    console.log('Verifying server exists...')
    const server = await prisma.remoteServer.findUnique({
      where: { id: serverId }
    })

    if (!server) {
      console.error('Remote server not found:', serverId)
      return Response.json({ error: 'Remote server not found' }, { status: 404 })
    }
    console.log('Server verified successfully:', server.name)

    // Check if assignment already exists
    console.log('Checking for existing assignment...')
    const existingAccess = await prisma.user_remote_server_access.findUnique({
      where: {
        user_id_remote_server_id: {
          user_id: userId,
          remote_server_id: serverId
        }
      }
    })

    if (existingAccess) {
      console.error('User already has access to this server:', { userId, serverId })
      return Response.json(
        { error: 'User already has access to this remote server' },
        { status: 400 }
      )
    }

    // Create user remote server access
    console.log('Creating user remote server access...')
    userAccess = await prisma.user_remote_server_access.create({
      data: {
        user_id: userId,
        remote_server_id: serverId,
        granted_by: (session.user as any).id,
        notes: notes || null
      }
    })
    console.log('User remote server access created successfully:', userAccess.id)

    // Assign specific permissions if provided
    if (permissions.length > 0) {
      console.log('Processing permissions...', permissions)

      // Use the unified permission service to validate permissions with context
      console.log('Validating permissions...')
      const validation = await permissionInheritance.validatePermissions(serverId, permissions)

      if (!validation.valid) {
        console.error('Permission validation failed:', validation)
        return Response.json(
          {
            error: 'Invalid permissions for this server',
            invalidPermissions: validation.invalidPermissions,
            reason: validation.reason
          },
          { status: 400 }
        )
      }

      // Get effective permissions (what user will have after assignment)
      console.log('Getting effective permissions...')
      const effectivePermissions = await permissionInheritance.getEffectivePermissions(
        userId,
        serverId,
        permissions
      )
      console.log('Effective permissions calculated:', effectivePermissions)

      // Validate each permission using unified service for consistency
      console.log('Validating individual permissions...')
      const validatedPermissions = []
      for (const perm of permissions) {
        const check = await unifiedPermissions.checkPermission({
          userId,
          permission: perm,
          serverId
        })
        if (check.allowed) {
          validatedPermissions.push(perm)
        }
      }
      console.log('Individual permissions validated:', validatedPermissions.length)

      // Only assign new permissions (not duplicates)
      if (effectivePermissions.additional.length > 0) {
        console.log('Creating permission assignments...')
        const permissionAssignments = effectivePermissions.additional.map(permission => ({
          user_id: userId,
          remote_server_id: serverId,
          permission_name: permission,
          granted_by: (session.user as any).id
        }))

        await prisma.user_remote_server_permissions.createMany({
          data: permissionAssignments
        })
        console.log('Permission assignments created successfully')
      }

      // Log the server assignment
      try {
        console.log('Logging server assignment audit...')
        const auditService = new ServerAssignmentAuditService()
        await auditService.logServerAssignment({
          userId,
          serverId,
          serverName: server.name,
          action: 'assign',
          permissions: permissions,
          performedBy: (session.user as any).id,
          metadata: {
            accessId: userAccess.id,
            permissionsRequested: permissions.length,
            permissionsAssigned: effectivePermissions.additional.length,
            duplicatePermissions: effectivePermissions.duplicates
          }
        })
        console.log('Server assignment audit logged successfully')
      } catch (auditError) {
        console.error('Failed to log server assignment audit:', auditError)
        // Don't fail the request if audit logging fails
      }

      // Return detailed assignment result
      console.log('Returning detailed assignment result')
      return Response.json({
        success: true,
        data: {
          accessId: userAccess.id,
          permissionsRequested: permissions.length,
          permissionsAssigned: effectivePermissions.additional.length,
          duplicatePermissions: effectivePermissions.duplicates,
          finalPermissions: effectivePermissions.final
        }
      })
    }

    // Log the server assignment (without specific permissions)
    try {
      console.log('Logging server assignment audit (no permissions)...')
      const auditService = new ServerAssignmentAuditService()
      await auditService.logServerAssignment({
        userId,
        serverId,
        serverName: server.name,
        action: 'assign',
        performedBy: (session.user as any).id,
        metadata: {
          accessId: userAccess.id
        }
      })
      console.log('Server assignment audit logged successfully (no permissions)')
    } catch (auditError) {
      console.error('Failed to log server assignment audit:', auditError)
      // Don't fail the request if audit logging fails
    }

    console.log('Returning basic assignment result')
    return Response.json({
      success: true,
      data: {
        accessId: userAccess.id,
        permissionsAssigned: permissions.length
      }
    })
  } catch (error) {
    console.error('Error assigning user to remote server:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    const errorStack = error instanceof Error ? error.stack : undefined

    console.error('Full error details:', {
      message: errorMessage,
      stack: errorStack,
      error: error,
      userId: userId ?? 'unknown',
      serverId: serverId || 'unknown',
      userAccessId: userAccess?.id || 'none'
    })

    // If we created a user access record but something failed later, try to clean it up
    if (userAccess?.id && userId && serverId) {
      try {
        console.log('Attempting to clean up failed user access record...')
        await prisma.user_remote_server_access.delete({
          where: { id: userAccess.id }
        })
        console.log('Successfully cleaned up user access record')
      } catch (cleanupError) {
        console.error('Failed to clean up user access record:', cleanupError)
      }
    }

    return Response.json(
      {
        error: 'Internal server error',
        details: errorMessage,
        stack: errorStack,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// DELETE /api/users/[id]/remote-servers - Remove user from remote server
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: userId } = await params
    const { searchParams } = new URL(request.url)
    const serverId = searchParams.get('serverId')

    if (!serverId) {
      return Response.json(
        { error: 'Server ID is required' },
        { status: 400 }
      )
    }

    // Get server info for audit logging before removal
    const server = await prisma.remoteServer.findUnique({
      where: { id: serverId },
      select: { name: true }
    })

    if (!server) {
      return Response.json({ error: 'Remote server not found' }, { status: 404 })
    }

    // Log the server removal audit before actually removing
    try {
      const auditService = new ServerAssignmentAuditService()
      await auditService.logServerAssignment({
        userId,
        serverId,
        serverName: server.name,
        action: 'remove',
        performedBy: (session.user as any).id,
        metadata: {
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      })
    } catch (auditError) {
      console.error('Failed to log server removal audit:', auditError)
      // Don't fail the removal if audit logging fails
    }

    // Remove user access and all associated permissions
    await prisma.$transaction(async (tx) => {
      // Remove specific permissions
      await tx.user_remote_server_permissions.deleteMany({
        where: {
          user_id: userId,
          remote_server_id: serverId
        }
      })

      // Remove server access
      await tx.user_remote_server_access.deleteMany({
        where: {
          user_id: userId,
          remote_server_id: serverId
        }
      })
    })

    return Response.json({ success: true })
  } catch (error) {
    console.error('Error removing user from remote server:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}