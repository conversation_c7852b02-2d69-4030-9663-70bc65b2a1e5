import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { localPermissionService } from '@/lib/services/local-permission-service'

// GET /api/users/[id]/local-permissions - Get user's local permissions
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: userId } = await params

    const userPermissions = await localPermissionService.getUserPermissions(userId)

    return Response.json({
      success: true,
      data: userPermissions
    })
  } catch (error) {
    console.error('Error fetching user permissions:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}