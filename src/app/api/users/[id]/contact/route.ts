import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';
import { logUserAudit } from '@/lib/user-audit-logger';

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const adminUser = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!adminUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = adminUser.userRoles.some(userRole => 
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const body = await req.json();

    // Validate required fields
    const {
      streetAddress1,
      streetAddress2,
      town,
      city,
      country,
      postalCode,
      regionId,
      regionText
    } = body;

    // Find the user to update
    const user = await prisma.user.findUnique({
      where: { id },
      include: { profile: true }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Find country and city IDs if provided by name
    let countryId = null;
    let cityId = null;

    if (country) {
      const countryRecord = await prisma.country.findFirst({
        where: { name: { contains: country, mode: 'insensitive' } }
      });
      countryId = countryRecord?.id;
    }

    if (city && countryId) {
      const cityRecord = await prisma.city.findFirst({
        where: { 
          name: { contains: city, mode: 'insensitive' },
          countryId: countryId
        }
      });
      cityId = cityRecord?.id;
    }

    // Update or create profile with contact information
    const updatedProfile = await prisma.userProfile.upsert({
      where: { userId: id },
      update: {
        streetAddress1: streetAddress1 || null,
        streetAddress2: streetAddress2 || null,
        town: town || null,
        cityId: cityId,
        countryId: countryId,
        postalCode: postalCode || null,
        regionId: regionId || null,
        regionText: regionText || null,
        updatedAt: new Date()
      },
      create: {
        userId: id,
        streetAddress1: streetAddress1 || null,
        streetAddress2: streetAddress2 || null,
        town: town || null,
        cityId: cityId,
        countryId: countryId,
        postalCode: postalCode || null,
        regionId: regionId || null,
        regionText: regionText || null,
      },
      include: {
        city: true,
        country: true,
        region: true
      }
    });

    // Log the audit trail
    await logUserAudit({
      userId: (session.user as any).id,
      action: 'update_user_contact_info',
      targetUserId: id,
      details: JSON.stringify({
        changes: {
          streetAddress1,
          streetAddress2,
          town,
          city,
          country,
          postalCode,
          regionId,
          regionText
        },
        previousValues: user.profile
      }),
      ipAddress: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown'
    });

    return NextResponse.json({
      message: 'Contact information updated successfully',
      profile: {
        streetAddress1: updatedProfile.streetAddress1,
        streetAddress2: updatedProfile.streetAddress2,
        town: updatedProfile.town,
        city: updatedProfile.city,
        country: updatedProfile.country,
        postalCode: updatedProfile.postalCode,
        regionId: updatedProfile.regionId,
        regionText: updatedProfile.regionText,
      }
    });
  } catch (error) {
    console.error('Error updating contact information:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}