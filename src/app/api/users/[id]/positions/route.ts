import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';
import { logUserAudit } from '@/lib/user-audit-logger';

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const adminUser = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!adminUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = adminUser.userRoles.some(userRole => 
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const body = await req.json();

    const { titleId, positionId } = body;

    // Find the user to update
    const user = await prisma.user.findUnique({
      where: { id },
      include: { userPositions: true }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Validate title and position if provided
    if (titleId) {
      const title = await prisma.title.findUnique({
        where: { id: titleId }
      });
      if (!title) {
        return NextResponse.json({ error: 'Invalid title ID' }, { status: 400 });
      }
    }

    if (positionId) {
      const position = await prisma.position.findUnique({
        where: { id: positionId }
      });
      if (!position) {
        return NextResponse.json({ error: 'Invalid position ID' }, { status: 400 });
      }
    }

    // End current active positions
    await prisma.userPosition.updateMany({
      where: {
        userId: id,
        isActive: true
      },
      data: {
        isActive: false,
        endDate: new Date()
      }
    });

    let newPosition = null;

    // Create new position assignment if provided
    if (positionId) {
      // Check if position is already assigned to another active user
      const existingAssignment = await prisma.userPosition.findFirst({
        where: {
          positionId: positionId,
          isActive: true,
          userId: { not: id } // Exclude current user
        }
      });

      if (existingAssignment) {
        return NextResponse.json({ 
          error: 'Position already assigned', 
          message: `This position is already assigned to another user. Please reassign it first.` 
        }, { status: 409 });
      }

      newPosition = await prisma.userPosition.create({
        data: {
          userId: id,
          positionId: positionId,
          startDate: new Date(),
          isActive: true
        },
        include: {
          position: {
            select: {
              id: true,
              title: true,
              description: true
            }
          }
        }
      });
    }

    // Log the audit trail
    await logUserAudit({
      userId: (session.user as any).id,
      action: 'update_user_positions',
      targetUserId: id,
      details: JSON.stringify({
        changes: {
          titleId,
          positionId,
          newPosition: newPosition ? {
            id: newPosition.id,
            positionId: newPosition.positionId,
            title: newPosition.position.title,
            startDate: newPosition.startDate,
            isActive: newPosition.isActive
          } : null
        },
        previousPositions: user.userPositions
      }),
      ipAddress: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown'
    });

    return NextResponse.json({
      message: 'Position information updated successfully',
      position: newPosition ? {
        id: newPosition.id,
        positionId: newPosition.positionId,
        title: newPosition.position.title,
        startDate: newPosition.startDate,
        isActive: newPosition.isActive
      } : null
    });
  } catch (error) {
    console.error('Error updating position information:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}