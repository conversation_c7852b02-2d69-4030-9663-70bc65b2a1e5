import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/middleware/require-auth';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const authUser = await requireAuth(request);

    const { id: userId } = await params;

    // Check if user can access this data (own profile or admin)
    if (authUser.userId !== userId && !authUser.roles.includes('admin')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Verify the target user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!targetUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const identifications = await prisma.userIdentification.findMany({
      where: { 
        userId,
        isActive: true 
      },
      include: {
        identificationType: {
          select: {
            id: true,
            name: true,
            category: true,
            description: true
          }
        }
      },
      orderBy: [
        { identificationType: { category: 'asc' } },
        { identificationType: { name: 'asc' } }
      ]
    });

    // Log the audit event
    await 
auditLoggingMiddleware.logApiAccess(
      
      authUser.userId,
      'read',
      'user_identifications',
      true,
      request,
      {
        statusCode: 200,
        metadata: { targetUserId: userId, count: identifications.length }
      }
    );

    return NextResponse.json({ identifications });
  } catch (error) {
    console.error('Error fetching user identifications:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const authUser = await requireAuth(request);

    const { id: userId } = await params;
    const body = await request.json();
    const { identificationTypeId, idNumber } = body;

    // Check if user can modify this data (own profile or admin)
    if (authUser.userId !== userId && !authUser.roles.includes('admin')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Validate required fields
    if (!identificationTypeId || !idNumber) {
      return NextResponse.json(
        { error: 'Identification type and ID number are required' },
        { status: 400 }
      );
    }

    // Validate ID number format: 1-50 alphanumeric characters
    const idNumberTrimmed = String(idNumber).trim();
    if (!/^[A-Za-z0-9]{1,50}$/.test(idNumberTrimmed)) {
      return NextResponse.json(
        { error: 'Invalid ID number format', code: 'INVALID_ID_NUMBER' },
        { status: 400 }
      );
    }

    // Verify the target user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!targetUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Verify identification type exists
    const identificationType = await prisma.identificationType.findUnique({
      where: { id: identificationTypeId }
    });

    if (!identificationType) {
      return NextResponse.json(
        { error: 'Identification type not found' },
        { status: 404 }
      );
    }

    // Check for duplicate identification
    const existingIdentification = await prisma.userIdentification.findFirst({
      where: {
        userId,
        identificationTypeId,
        idNumber: idNumber.trim(),
        isActive: true
      }
    });

    if (existingIdentification) {
      return NextResponse.json(
        { 
          error: 'Duplicate identification',
          code: 'DUPLICATE_IDENTIFICATION'
        },
        { status: 409 }
      );
    }

    const identification = await prisma.userIdentification.create({
      data: {
        userId,
        identificationTypeId,
        idNumber: idNumberTrimmed
      },
      include: {
        identificationType: {
          select: {
            id: true,
            name: true,
            category: true,
            description: true
          }
        }
      }
    });

    // Log the audit event
    await 
auditLoggingMiddleware.logApiAccess(
      
      authUser.userId,
      'create',
      'user_identifications',
      true,
      request,
      {
        statusCode: 201,
        metadata: { 
          targetUserId: userId, 
          identificationId: identification.id,
          identificationTypeName: identificationType.name
        }
      }
    );

    return NextResponse.json({ identification }, { status: 201 });
  } catch (error) {
    console.error('Error creating user identification:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// Helper function to get a default treaty type
async function getDefaultTreatyTypeId(): Promise<string | null> {
  try {
    const treatyType = await prisma.treatyType.findFirst({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    });
    return treatyType?.id || null;
  } catch (error) {
    console.error('Error getting default treaty type:', error);
    return null;
  }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const authUser = await requireAuth(request);

    const { id: userId } = await params;
    const body = await request.json();
    const { ambassadorialTitles, identifications, treatyNumbers } = body;

    // Check if user can modify this data (own profile or admin)
    if (authUser.userId !== userId && !authUser.roles.includes('admin')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Verify the target user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!targetUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Handle ambassadorial titles update
    if (ambassadorialTitles && Array.isArray(ambassadorialTitles)) {
      // For now, we only support one ambassadorial title via UserProfile.titleId
      // Take the first active title if available
      const activeTitle = ambassadorialTitles.find((title: any) => 
        title.titleId && title.identificationNumber
      );
      
      if (activeTitle) {
        // Update user profile with the selected title
        await prisma.userProfile.upsert({
          where: { userId },
          update: { 
            titleId: activeTitle.titleId,
            updatedAt: new Date()
          },
          create: { 
            userId,
            titleId: activeTitle.titleId,
            firstName: '',
            lastName: '',
            bio: ''
          }
        });
      } else {
        // Clear title if no active titles
        await prisma.userProfile.updateMany({
          where: { userId },
          data: { titleId: null }
        });
      }
    }

    // Handle identifications update
    if (identifications && Array.isArray(identifications)) {
      // Deactivate existing identifications
      await prisma.userIdentification.updateMany({
        where: { userId },
        data: { isActive: false }
      });

      // Create new identifications
      for (const identification of identifications) {
        if (identification.identificationTypeId && identification.idNumber) {
          // Validate ID number format
          const idNumberTrimmed = String(identification.idNumber).trim();
          if (!/^[A-Za-z0-9]{1,50}$/.test(idNumberTrimmed)) {
            continue; // Skip invalid entries
          }

          await prisma.userIdentification.create({
            data: {
              userId,
              identificationTypeId: identification.identificationTypeId,
              idNumber: idNumberTrimmed
            }
          });
        }
      }
    }

    // Handle treaty numbers update
    if (treatyNumbers && Array.isArray(treatyNumbers)) {
      // Remove existing treaty numbers
      await prisma.userTreatyNumber.deleteMany({
        where: { userId }
      });

      // Add new treaty numbers
      if (treatyNumbers.length > 0) {
        let fallbackTreatyTypeId: string | null | undefined;
        const treatyNumberRecords: Array<{ userId: string; treatyTypeId: string; treatyNumber: string }> = [];

        for (const treatyNumber of treatyNumbers) {
          const rawNumber = typeof treatyNumber?.treatyNumber === 'string' ? treatyNumber.treatyNumber.trim() : '';
          if (!rawNumber) {
            continue;
          }

          // Prefer explicit treaty type identifiers from the payload
          let treatyTypeId = treatyNumber?.treatyTypeId || treatyNumber?.treatyId;

          if (!treatyTypeId) {
            if (fallbackTreatyTypeId === undefined) {
              fallbackTreatyTypeId = await getDefaultTreatyTypeId();
            }

            if (!fallbackTreatyTypeId) {
              // No valid treaty type available; skip this entry
              continue;
            }

            treatyTypeId = fallbackTreatyTypeId;
          }

          treatyNumberRecords.push({
            userId,
            treatyTypeId,
            treatyNumber: rawNumber
          });
        }

        if (treatyNumberRecords.length > 0) {
          await prisma.userTreatyNumber.createMany({
            data: treatyNumberRecords
          });
        }
      }
    }

    // Log the audit event
    await auditLoggingMiddleware.logApiAccess(
      authUser.userId,
      'update',
      'user_identifications',
      true,
      request,
      {
        statusCode: 200,
        metadata: { 
          targetUserId: userId,
          ambassadorialTitlesCount: ambassadorialTitles?.length || 0,
          identificationsCount: identifications?.length || 0,
          treatyNumbersCount: treatyNumbers?.length || 0
        }
      }
    );

    return NextResponse.json({ 
      message: 'Identification information updated successfully',
      data: {
        ambassadorialTitles,
        identifications,
        treatyNumbers
      }
    });
  } catch (error) {
    console.error('Error updating user identifications:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
