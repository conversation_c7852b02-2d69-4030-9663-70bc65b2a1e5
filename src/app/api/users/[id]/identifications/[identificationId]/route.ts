import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/middleware/require-auth';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; identificationId: string }> }
) {
  try {
    const authUser = await requireAuth(request);

    const { id: userId, identificationId } = await params;
    const body = await request.json();
    const { identificationTypeId, idNumber } = body;

    // Check if user can modify this data (own profile or admin)
    if (authUser.userId !== userId && !authUser.roles.includes('admin')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Validate required fields
    if (!identificationTypeId || !idNumber) {
      return NextResponse.json(
        { error: 'Identification type and ID number are required' },
        { status: 400 }
      );
    }

    // Validate ID number format: 1-50 alphanumeric characters
    const idNumberTrimmed = String(idNumber).trim();
    if (!/^[A-Za-z0-9]{1,50}$/.test(idNumberTrimmed)) {
      return NextResponse.json(
        { error: 'Invalid ID number format', code: 'INVALID_ID_NUMBER' },
        { status: 400 }
      );
    }

    // Verify the identification exists and belongs to the user
    const existingIdentification = await prisma.userIdentification.findFirst({
      where: {
        id: identificationId,
        userId,
        isActive: true
      },
      include: {
        identificationType: true
      }
    });

    if (!existingIdentification) {
      return NextResponse.json(
        { error: 'Identification not found' },
        { status: 404 }
      );
    }

    // Verify identification type exists
    const identificationType = await prisma.identificationType.findUnique({
      where: { id: identificationTypeId }
    });

    if (!identificationType) {
      return NextResponse.json(
        { error: 'Identification type not found' },
        { status: 404 }
      );
    }

    // Check for duplicate identification (excluding current one)
    const duplicateIdentification = await prisma.userIdentification.findFirst({
      where: {
        userId,
        identificationTypeId,
        idNumber: idNumber.trim(),
        isActive: true,
        NOT: {
          id: identificationId
        }
      }
    });

    if (duplicateIdentification) {
      return NextResponse.json(
        { 
          error: 'Duplicate identification',
          code: 'DUPLICATE_IDENTIFICATION'
        },
        { status: 409 }
      );
    }

    const updatedIdentification = await prisma.userIdentification.update({
      where: { id: identificationId },
      data: {
        identificationTypeId,
        idNumber: idNumberTrimmed,
        updatedAt: new Date()
      },
      include: {
        identificationType: {
          select: {
            id: true,
            name: true,
            category: true,
            description: true
          }
        }
      }
    });

    // Log the audit event
    await 
auditLoggingMiddleware.logApiAccess(
      
      authUser.userId,
      'update',
      'user_identifications',
      true,
      request,
      {
        statusCode: 200,
        metadata: { 
          targetUserId: userId,
          identificationId,
          oldIdentificationTypeName: existingIdentification.identificationType.name,
          newIdentificationTypeName: identificationType.name
        }
      }
    );

    return NextResponse.json({ identification: updatedIdentification });
  } catch (error) {
    console.error('Error updating user identification:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; identificationId: string }> }
) {
  try {
    const authUser = await requireAuth(request);

    const { id: userId, identificationId } = await params;

    // Check if user can modify this data (own profile or admin)
    if (authUser.userId !== userId && !authUser.roles.includes('admin')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Verify the identification exists and belongs to the user
    const existingIdentification = await prisma.userIdentification.findFirst({
      where: {
        id: identificationId,
        userId,
        isActive: true
      },
      include: {
        identificationType: true
      }
    });

    if (!existingIdentification) {
      return NextResponse.json(
        { error: 'Identification not found' },
        { status: 404 }
      );
    }

    // Enforce minimum 2 active identifications per user
    const activeCount = await prisma.userIdentification.count({
      where: { userId, isActive: true }
    });
    if (activeCount <= 2) {
      return NextResponse.json(
        { error: 'User must have at least 2 identifications', code: 'MINIMUM_IDS_REQUIRED' },
        { status: 400 }
      );
    }

    // Soft delete the identification
    await prisma.userIdentification.update({
      where: { id: identificationId },
      data: {
        isActive: false,
        updatedAt: new Date()
      }
    });

    // Log the audit event
    await 
auditLoggingMiddleware.logApiAccess(
      
      authUser.userId,
      'delete',
      'user_identifications',
      true,
      request,
      {
        statusCode: 200,
        metadata: { 
          targetUserId: userId,
          identificationId,
          identificationTypeName: existingIdentification.identificationType.name
        }
      }
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting user identification:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; identificationId: string }> }
) {
  try {
    const authUser = await requireAuth(request);

    const { id: userId, identificationId } = await params;

    // Check if user can access this data (own profile or admin)
    if (authUser.userId !== userId && !authUser.roles.includes('admin')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    const identification = await prisma.userIdentification.findFirst({
      where: {
        id: identificationId,
        userId,
        isActive: true
      },
      include: {
        identificationType: {
          select: {
            id: true,
            name: true,
            category: true,
            description: true
          }
        }
      }
    });

    if (!identification) {
      return NextResponse.json(
        { error: 'Identification not found' },
        { status: 404 }
      );
    }

    // Log the audit event
    await 
auditLoggingMiddleware.logApiAccess(
      
      authUser.userId,
      'read',
      'user_identifications',
      true,
      request,
      {
        statusCode: 200,
        metadata: { 
          targetUserId: userId,
          identificationId
        }
      }
    );

    return NextResponse.json({ identification });
  } catch (error) {
    console.error('Error fetching user identification:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
