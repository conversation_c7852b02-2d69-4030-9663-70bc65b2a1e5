import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { localPermissionService } from '@/lib/services/local-permission-service'

// POST /api/users/[id]/projects - Assign project scope to user
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: userId } = await params
    const body = await request.json()
    const { projectId, scopeId } = body

    if (!projectId || !scopeId) {
      return Response.json(
        { error: 'Missing required parameters: projectId and scopeId' },
        { status: 400 }
      )
    }

    await localPermissionService.assignProjectScope(userId, projectId, scopeId, (session.user as any).id)

    return Response.json({
      success: true,
      message: 'Project scope assigned successfully'
    })
  } catch (error) {
    console.error('Error assigning project scope:', error)
    const message = error instanceof Error ? error.message : 'Internal server error'
    return Response.json(
      { error: message },
      { status: error instanceof Error && error.message.includes('already has') ? 400 : 500 }
    )
  }
}

// DELETE /api/users/[id]/projects - Remove project scope from user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: userId } = await params
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')
    const scopeId = searchParams.get('scopeId')

    if (!projectId || !scopeId) {
      return Response.json(
        { error: 'Missing required parameters: projectId and scopeId' },
        { status: 400 }
      )
    }

    await localPermissionService.removeProjectScope(userId, projectId, scopeId)

    return Response.json({
      success: true,
      message: 'Project scope removed successfully'
    })
  } catch (error) {
    console.error('Error removing project scope:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}