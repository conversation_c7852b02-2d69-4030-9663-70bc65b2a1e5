import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { localPermissionService } from '@/lib/services/local-permission-service'

// GET /api/users/[id]/permissions - Get user's permissions for remote servers
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: userId } = await params

    // Get user's remote server permissions
    const { prisma } = await import('@/lib/prisma')

    const remoteServerPermissions = await prisma.user_remote_server_permissions.findMany({
      where: {
        user_id: userId,
        is_active: true
      },
      include: {
        remote_servers: {
          select: {
            id: true,
            name: true,
            url: true
          }
        }
      }
    })

    // Transform to the format expected by the frontend
    const permissions = remoteServerPermissions.map(perm => ({
      id: `perm_${perm.user_id}_${perm.remote_server_id}_${perm.permission_name}`,
      userId: perm.user_id,
      serverId: perm.remote_server_id,
      permissionName: perm.permission_name,
      isAssigned: perm.is_active,
      assignedAt: perm.granted_at,
      server: perm.remote_servers
    }))

    return Response.json({
      success: true,
      permissions: permissions
    })
  } catch (error) {
    console.error('Error fetching user permissions:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/users/[id]/permissions - Toggle user permission
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: userId } = await params
    const body = await request.json()
    const { serverId, permissionName, action } = body

    if (!serverId || !permissionName || !action) {
      return Response.json(
        { error: 'Missing required fields: serverId, permissionName, action' },
        { status: 400 }
      )
    }

    const { prisma } = await import('@/lib/prisma')

    // Handle remote server permissions
    if (serverId !== 'local') {
      if (action === 'toggle') {
        // Check if user already has this permission for this server
        const existingPermission = await prisma.user_remote_server_permissions.findUnique({
          where: {
            user_id_remote_server_id_permission_name: {
              user_id: userId,
              remote_server_id: serverId,
              permission_name: permissionName
            }
          }
        })

        if (existingPermission) {
          // Toggle existing permission
          await prisma.user_remote_server_permissions.update({
            where: {
              user_id_remote_server_id_permission_name: {
                user_id: userId,
                remote_server_id: serverId,
                permission_name: permissionName
              }
            },
            data: {
              is_active: !existingPermission.is_active,
              granted_by: (session.user as any).id,
              granted_at: new Date()
            }
          })

          return Response.json({
            success: true,
            message: `Permission ${existingPermission.is_active ? 'revoked' : 'granted'} successfully`,
            isAssigned: !existingPermission.is_active
          })
        } else {
          // Create new permission assignment
          await prisma.user_remote_server_permissions.create({
            data: {
              user_id: userId,
              remote_server_id: serverId,
              permission_name: permissionName,
              granted_by: (session.user as any).id,
              is_active: true
            }
          })

          return Response.json({
            success: true,
            message: 'Permission granted successfully',
            isAssigned: true
          })
        }
      } else {
        return Response.json(
          { error: 'Invalid action. Use "toggle"' },
          { status: 400 }
        )
      }
    }

    // Handle local permissions (existing logic)
    console.log(`Toggling local permission ${permissionName} for user ${userId}`)
    return Response.json({
      success: true,
      message: 'Local permission toggled successfully'
    })
  } catch (error) {
    console.error('Error toggling user permission:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}