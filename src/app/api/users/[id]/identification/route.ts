import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: userId } = await params;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Fetch user with all identification-related data
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: {
          include: {
            country: true,
            city: true,
            title: true
          }
        },
        userIdentifications: {
          include: {
            identificationType: true
          },
          orderBy: {
            createdAt: 'asc'
          }
        },
        userTreatyNumbers: {
          include: {
            treatyType: true
          },
          orderBy: {
            createdAt: 'asc'
          }
        },
        userPositions: {
          include: {
            position: true
          },
          where: {
            isActive: true
          },
          orderBy: {
            startDate: 'asc'
          }
        },
        nationTreatyMemberships: {
          include: {
            nationTreaty: true
          },
          orderBy: {
            joinDate: 'asc'
          }
        },
        nationTreatyEnvoys: {
          include: {
            nationTreaty: true
          },
          orderBy: {
            createdAt: 'asc'
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Transform the data for the frontend with null checks
    const identificationData = {
      user: {
        id: user.id,
        name: user.name || 'Unknown User',
        email: user.email || ''
      },
      profile: user.profile ? {
        peaceAmbassadorNumber: user.profile.peaceAmbassadorNumber,
        title: user.profile.title?.name || null,
        country: user.profile.country?.name || null,
        city: user.profile.city?.name || null,
        dateOfBirth: user.profile.dateOfBirth
      } : null,
      identifications: user.userIdentifications.map(identification => ({
        id: identification.id,
        type: identification.identificationType?.name || 'Unknown Type',
        number: identification.idNumber,
        isActive: identification.isActive,
        createdAt: identification.createdAt
      })),
      treatyNumbers: user.userTreatyNumbers.map(treatyNumber => ({
        id: treatyNumber.id,
        treatyType: treatyNumber.treatyType?.name || 'Unknown Treaty Type',
        number: treatyNumber.treatyNumber,
        isActive: treatyNumber.isActive,
        createdAt: treatyNumber.createdAt
      })),
      positions: user.userPositions.map(position => ({
        id: position.id,
        title: position.position?.title || 'Unknown Position',
        description: position.position?.description || null,
        level: position.position?.level || 0,
        isActive: position.isActive,
        startDate: position.startDate,
        endDate: position.endDate,
        notes: position.notes
      })),
      nationTreatyMemberships: user.nationTreatyMemberships.map(membership => ({
        id: membership.id,
        nationTreaty: membership.nationTreaty?.name || 'Unknown Nation Treaty',
        role: membership.role,
        status: membership.status,
        joinDate: membership.joinDate,
        notes: membership.notes
      })),
      nationTreatyEnvoys: user.nationTreatyEnvoys.map(envoy => ({
        id: envoy.id,
        nationTreaty: envoy.nationTreaty?.name || 'Unknown Nation Treaty',
        envoyType: envoy.envoyType,
        title: envoy.title,
        status: envoy.status,
        address: envoy.address,
        city: envoy.city,
        country: envoy.country,
        phone: envoy.phone,
        mobile: envoy.mobile,
        email: envoy.email
      }))
    };

    return NextResponse.json(identificationData);

  } catch (error) {
    console.error('Error fetching user identification data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}