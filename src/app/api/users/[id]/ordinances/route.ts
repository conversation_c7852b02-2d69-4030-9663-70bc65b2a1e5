import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';
import { logUserAudit } from '@/lib/user-audit-logger';

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const adminUser = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!adminUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = adminUser.userRoles.some(userRole => 
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const body = await req.json();

    const { selectedOrdinances } = body;

    // Find the user to update
    const user = await prisma.user.findUnique({
      where: { id },
      include: { ordinances: true }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Validate ordinance type IDs if provided
    if (selectedOrdinances && selectedOrdinances.length > 0) {
      const ordinanceTypeIds = selectedOrdinances.map((ord: any) => ord.ordinanceTypeId);
      const validOrdinanceTypes = await prisma.ordinanceType.findMany({
        where: { id: { in: ordinanceTypeIds } }
      });

      if (validOrdinanceTypes.length !== ordinanceTypeIds.length) {
        return NextResponse.json({ error: 'One or more invalid ordinance type IDs' }, { status: 400 });
      }
    }

    // Remove existing ordinances
    await prisma.ordinance.deleteMany({
      where: { userId: id }
    });

    // Create new ordinance assignments
    const newOrdinances = [];
    if (selectedOrdinances && selectedOrdinances.length > 0) {
      for (const ordinanceData of selectedOrdinances) {
        const newOrdinance = await prisma.ordinance.create({
          data: {
            userId: id,
            ordinanceTypeId: ordinanceData.ordinanceTypeId,
            status: ordinanceData.status || 'assigned',
            completedDate: ordinanceData.completedDate ? new Date(ordinanceData.completedDate) : null,
            expirationDate: ordinanceData.expirationDate ? new Date(ordinanceData.expirationDate) : null,
            notes: ordinanceData.notes || null,
            documentPath: ordinanceData.documentPath || null
          },
          include: {
            ordinanceType: true
          }
        });
        newOrdinances.push(newOrdinance);
      }
    }

    // Log the audit trail
    await logUserAudit({
      userId: (session.user as any).id,
      action: 'update_user_ordinances',
      targetUserId: id,
      details: JSON.stringify({
        changes: {
          selectedOrdinances,
          newOrdinances: newOrdinances.map(ord => ({
            id: ord.id,
            ordinanceTypeId: ord.ordinanceTypeId,
            status: ord.status,
            completedDate: ord.completedDate?.toISOString(),
            expirationDate: ord.expirationDate?.toISOString(),
            notes: ord.notes,
            documentPath: ord.documentPath
          }))
        },
        previousOrdinances: user.ordinances
      }),
      ipAddress: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown'
    });

    return NextResponse.json({
      message: 'Ordinance assignments updated successfully',
      ordinances: newOrdinances.map(ord => ({
        id: ord.id,
        ordinanceTypeId: ord.ordinanceTypeId,
        status: ord.status,
        completedDate: ord.completedDate?.toISOString(),
        expirationDate: ord.expirationDate?.toISOString(),
        notes: ord.notes,
        documentPath: ord.documentPath,
        ordinanceType: ord.ordinanceType
      }))
    });
  } catch (error) {
    console.error('Error updating ordinance assignments:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}