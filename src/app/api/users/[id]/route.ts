import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { userAuditLogger } from '@/lib/user-audit-logger';
import { validatePostalCode } from '@/lib/utils/address-format-utils';
import { performanceMonitor } from '@/lib/performance';
import { checkRateLimit } from '@/lib/middleware/rate-limiting';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';
import { requireAuth } from '@/lib/middleware/require-auth';
import { AuthenticationError, AuthorizationError } from '@/lib/errors';

// Input sanitization function
function sanitizeInput(input: string): string {
  if (!input) return input;
  return input.trim().replace(/[<>]/g, '');
}

// Validation schema for user update
const userUpdateSchema = z.object({
  firstName: z.string().min(1, 'First name is required').optional(),
  surname: z.string().min(1, 'Surname is required').optional(),
  email: z.string().email('Invalid email format').optional(),
  personalEmail: z.string().email('Invalid personal email format').optional(),
  nwaEmail: z.string().email('Invalid NWA email format').optional(),
  dob: z.string().optional(),
  phone: z.string().optional(),
  mobile: z.string().optional(),
  // New structured address fields
  streetAddress1: z.string().optional(),
  streetAddress2: z.string().optional(),
  town: z.string().optional(),
  country: z.string().optional(),
  city: z.string().optional(),
  regionId: z.string().optional(),
  regionText: z.string().optional(),
  postalCode: z.string().optional(),
  // Backward compatibility - legacy field
  streetAddress: z.string().optional(),
  peaceAmbassadorNumber: z.string().optional(),
  bio: z.string().optional(),
  titleId: z.string().optional(),
  positionId: z.string().optional(),
  // Nation/Tribe fields
  nationId: z.string().optional(),
  tribeId: z.string().optional(),
  nationRole: z.enum(['member', 'envoy', 'admin']).optional(),
  tribeRole: z.enum(['member', 'envoy', 'admin']).optional(),
});

// Helper function to create secure responses with CORS and security headers
function createSecureResponse(data: any, status: number = 200, request: NextRequest): NextResponse {
  const response = NextResponse.json(data, { status });

  // Add CORS headers for cross-origin requests
  const origin = request.headers.get('origin');

  if (origin) {
    // For development, allow localhost origins
    if (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002')) {
      response.headers.set('Access-Control-Allow-Origin', origin);
      response.headers.set('Access-Control-Allow-Credentials', 'true');
      response.headers.set('Vary', 'Origin');
    }
  }

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
}

// GET /api/users/[id] - Get a specific user
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  performanceMonitor.startTimer('api_users_get_by_id', { endpoint: '/api/users/[id]' });
  try {
    // Apply rate limiting for user data requests
    const rateLimitResult = await checkRateLimit(req, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100, // 100 user data requests per minute
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        req,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return createSecureResponse(
        {
          error: 'too_many_requests',
          error_description: 'Rate limit exceeded. Please try again later.'
        },
        429,
        req
      );
    }

    // Handle OPTIONS preflight requests
    if (req.method === 'OPTIONS') {
      const response = new NextResponse(null, { status: 204 });
      const origin = req.headers.get('origin');

      if (origin && (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002'))) {
        response.headers.set('Access-Control-Allow-Origin', origin);
        response.headers.set('Access-Control-Allow-Credentials', 'true');
        response.headers.set('Access-Control-Allow-Methods', 'GET, PUT, OPTIONS');
        response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        response.headers.set('Access-Control-Max-Age', '86400');
      }

      return response;
    }

    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      await auditLoggingMiddleware.logApiAccess(
        undefined,
        'unauthorized_user_access',
        'users/[id]',
        false,
        req,
        {
          statusCode: 401,
          errorMessage: 'Unauthorized access attempt'
        }
      );

      return createSecureResponse({ error: 'Unauthorized' }, 401, req);
    }

    const { id } = await params;
    
    // Fetch user with full profile data
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        profile: {
          include: {
            country: true,
            city: true,
            region: true,
            title: true,
          },
        },
        userPositions: {
          include: {
            position: true
          }
        },
        userIdentifications: {
          where: { isActive: true },
          include: {
            identificationType: {
              select: {
                id: true,
                name: true,
                category: true,
                description: true
              }
            }
          },
          orderBy: [
            { identificationType: { category: 'asc' } },
            { identificationType: { name: 'asc' } }
          ]
        },
        userTreatyNumbers: {
          include: {
            treatyType: {
              select: {
                id: true,
                name: true,
                category: true
              }
            }
          }
        },
        treaties: true,
        treatyTypeDetails: true,
        ordinances: true,
        nationTreaty: true,
        tribeTreaty: true
      },
    });

    if (!user) {
      await auditLoggingMiddleware.logApiAccess(
        (session.user as any).id,
        'user_not_found',
        'users/[id]',
        false,
        req,
        {
          statusCode: 404,
          errorMessage: 'User not found',
          metadata: {
            requestedUserId: id
          }
        }
      );

      return createSecureResponse({ error: 'User not found' }, 404, req);
    }

    // Log successful user access
    await auditLoggingMiddleware.logApiAccess(
      (session.user as any).id,
      'user_access',
      'users/[id]',
      true,
      req,
      {
        statusCode: 200,
        metadata: {
          requestedUserId: id,
          hasProfile: !!user.profile
        }
      }
    );

    return createSecureResponse({
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
        image: user.image,
        twoFactorEnabled: user.twoFactorEnabled,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        // Nested profile object as expected by UpdateUserTab
        profile: user.profile ? {
          id: user.profile.id,
          firstName: user.profile.firstName,
          lastName: user.profile.lastName,
          personalEmail: user.profile.personalEmail,
          nwaEmail: user.profile.nwaEmail,
          phone: user.profile.phone,
          mobile: user.profile.mobile,
          dateOfBirth: user.profile.dateOfBirth,
          streetAddress1: user.profile.streetAddress1,
          streetAddress2: user.profile.streetAddress2,
          town: user.profile.town,
          postalCode: user.profile.postalCode,
          regionId: user.profile.regionId,
          regionText: user.profile.regionText,
          peaceAmbassadorNumber: user.profile.peaceAmbassadorNumber,
          bio: user.profile.bio,
          titleId: user.profile.titleId,
          // Nested relationships
          country: user.profile.country,
          city: user.profile.city,
          region: user.profile.region,
          title: user.profile.title,
        } : null,
        // User positions
        userPositions: user.userPositions,
        // Additional data that might be needed
        identifications: user.userIdentifications || [],
        treatyNumbers: user.userTreatyNumbers || [],
        treaties: user.treaties || [],
        treatyTypeDetails: user.treatyTypeDetails || [],
        ordinances: user.ordinances || [],
        nationTreaty: user.nationTreaty || null,
        tribeTreaty: user.tribeTreaty || null,
        nationId: user.nationId || null,
        tribeId: user.tribeId || null
      }
    }, 200, req);
  } catch (error: any) {
    await auditLoggingMiddleware.logApiAccess(
      undefined,
      'user_access_error',
      'users/[id]',
      false,
      req,
      {
        statusCode: 500,
        errorMessage: error.message || 'Unknown error',
        metadata: {
          stack: error.stack || 'No stack trace'
        }
      }
    );

    console.error('Error fetching user:', error);
    return createSecureResponse({ error: 'Internal server error' }, 500, req);
  } finally {
    performanceMonitor.endTimer('api_users_get_by_id');
  }
}

// PUT /api/users/[id] - Update a specific user
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  performanceMonitor.startTimer('api_users_put', { endpoint: '/api/users/[id]' });
  try {
    // Apply stricter rate limiting for user updates
    const rateLimitResult = await checkRateLimit(req, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30, // 30 user updates per minute (lower than read operations)
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        req,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return createSecureResponse(
        {
          error: 'too_many_requests',
          error_description: 'Rate limit exceeded. Please try again later.'
        },
        429,
        req
      );
    }

    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      await auditLoggingMiddleware.logApiAccess(
        undefined,
        'unauthorized_user_update',
        'users/[id]',
        false,
        req,
        {
          statusCode: 401,
          errorMessage: 'Unauthorized update attempt'
        }
      );

      return createSecureResponse({ error: 'Unauthorized' }, 401, req);
    }

    const { id } = await params;
    const body = await req.json();
    
    // Validate input
    const validation = userUpdateSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
      include: { profile: true }
    });

    if (!existingUser) {
      await auditLoggingMiddleware.logApiAccess(
        (session.user as any).id,
        'user_update_not_found',
        'users/[id]',
        false,
        req,
        {
          statusCode: 404,
          errorMessage: 'User not found for update',
          metadata: {
            requestedUserId: id
          }
        }
      );

      return createSecureResponse({ error: 'User not found' }, 404, req);
    }
    
    const {
       firstName,
       surname,
       email,
       personalEmail,
       nwaEmail,
       dob,
       phone,
       mobile,
       // New address fields
       streetAddress1,
       streetAddress2,
       town,
       postalCode,
       regionId,
       regionText,
       country: countryName,
       city: cityName,
       // Legacy address field for backward compatibility
       streetAddress,
       peaceAmbassadorNumber,
       bio,
       titleId,
       positionId,
       // Nation/Tribe fields
       nationId,
       tribeId,
       nationRole,
       tribeRole
     } = validation.data;

    // Sanitize inputs if provided
    const sanitizedFirstName = firstName ? sanitizeInput(firstName) : undefined;
    const sanitizedSurname = surname ? sanitizeInput(surname) : undefined;
    const sanitizedEmail = email ? sanitizeInput(email) : undefined;
    const sanitizedPersonalEmail = personalEmail ? sanitizeInput(personalEmail) : undefined;
    const sanitizedNwaEmail = nwaEmail ? sanitizeInput(nwaEmail) : undefined;
    const sanitizedPhone = phone ? sanitizeInput(phone) : undefined;
    const sanitizedMobile = mobile ? sanitizeInput(mobile) : undefined;
    
    // Sanitize new address fields
    const sanitizedStreetAddress1 = streetAddress1 !== undefined 
      ? sanitizeInput(streetAddress1) 
      : (streetAddress !== undefined ? sanitizeInput(streetAddress) : undefined);
    const sanitizedStreetAddress2 = streetAddress2 !== undefined ? sanitizeInput(streetAddress2) : undefined;
    const sanitizedTown = town !== undefined ? sanitizeInput(town) : undefined;
    const sanitizedPostalCode = postalCode !== undefined ? sanitizeInput(postalCode) : undefined;
    const sanitizedRegionText = regionText !== undefined ? sanitizeInput(regionText) : undefined;
    
    const sanitizedCountryName = countryName ? sanitizeInput(countryName) : undefined;
    const sanitizedCityName = cityName ? sanitizeInput(cityName) : undefined;
    const sanitizedPeaceAmbassadorNumber = peaceAmbassadorNumber !== undefined ? sanitizeInput(peaceAmbassadorNumber) : undefined;
    const sanitizedBio = bio !== undefined ? sanitizeInput(bio) : undefined;
    
    // Find the country and city by name if provided
    let country = null;
    if (sanitizedCountryName) {
      country = await prisma.country.findFirst({
        where: { name: sanitizedCountryName }
      });
      
      if (!country) {
        return NextResponse.json({ error: 'Invalid country' }, { status: 400 });
      }
    }

    let city = null;
    if (sanitizedCityName && country) {
      city = await prisma.city.findFirst({
        where: {
          name: sanitizedCityName,
          countryId: country.id
        }
      });
      
      if (!city) {
        return NextResponse.json({ error: 'Invalid city' }, { status: 400 });
      }
    }
    
    // Validate region if regionId is provided
    let region = null;
    if (regionId && country) {
      region = await prisma.region.findFirst({
        where: {
          id: parseInt(regionId),
          countryId: country.id,
          isActive: true
        }
      });
      
      if (!region) {
        return NextResponse.json({ error: 'Invalid region for the selected country' }, { status: 400 });
      }
    }

    // Postal code validation against country rules
    const effectiveCountryId = country?.id ?? existingUser.profile?.countryId ?? null;
    if (effectiveCountryId && sanitizedPostalCode !== undefined) {
      const addressFormat = await prisma.countryAddressFormat.findUnique({
        where: { countryId: effectiveCountryId }
      });
      const effectivePostalPattern = addressFormat?.postalCodeFormat || '^.+$';
      const postalRequired = !!addressFormat?.postalCodeRequired;

      if (postalRequired && sanitizedPostalCode.trim() === '') {
        return NextResponse.json({ error: 'Postal code is required for the selected country' }, { status: 400 });
      }
      if (sanitizedPostalCode) {
        const isValidPostal = validatePostalCode(sanitizedPostalCode, effectivePostalPattern);
        if (!isValidPostal) {
          return NextResponse.json({ error: 'Invalid postal code format for the selected country' }, { status: 400 });
        }
      }
    }
    
    // Validate regionId/regionText mutual exclusion
    if (regionId && sanitizedRegionText) {
      return NextResponse.json({ 
        error: 'Cannot specify both regionId and regionText. Use regionId for predefined regions or regionText for custom regions.' 
      }, { status: 400 });
    }

    // Check for duplicate email if changing email
    if (sanitizedEmail && sanitizedEmail !== existingUser.email) {
      const duplicateUser = await prisma.user.findUnique({
        where: { email: sanitizedEmail }
      });
      
      if (duplicateUser) {
        return NextResponse.json({ error: 'User with this email already exists' }, { status: 400 });
      }
    }

    // Check for duplicate phone if changing phone
    if (sanitizedPhone) {
      const duplicatePhone = await prisma.userProfile.findFirst({
        where: { 
          phone: sanitizedPhone,
          NOT: { userId: id }
        }
      });
      
      if (duplicatePhone) {
        return NextResponse.json({ error: 'User with this phone number already exists' }, { status: 400 });
      }
    }

    // Check for duplicate mobile if changing mobile
    if (sanitizedMobile) {
      const duplicateMobile = await prisma.userProfile.findFirst({
        where: { 
          mobile: sanitizedMobile,
          NOT: { userId: id }
        }
      });
      
      if (duplicateMobile) {
        return NextResponse.json({ error: 'User with this mobile number already exists' }, { status: 400 });
      }
    }

    // Build profile update data - only include fields that are provided
    const profileUpdateData: any = {};
    
    if (sanitizedFirstName !== undefined) profileUpdateData.firstName = sanitizedFirstName;
    if (sanitizedSurname !== undefined) profileUpdateData.lastName = sanitizedSurname;
    if (sanitizedPersonalEmail !== undefined) profileUpdateData.personalEmail = sanitizedPersonalEmail;
    if (sanitizedNwaEmail !== undefined) profileUpdateData.nwaEmail = sanitizedNwaEmail;
    if (dob !== undefined) profileUpdateData.dateOfBirth = dob ? new Date(dob) : null;
    if (sanitizedPhone !== undefined) profileUpdateData.phone = sanitizedPhone;
    if (sanitizedMobile !== undefined) profileUpdateData.mobile = sanitizedMobile;
    
    // New structured address fields
    if (sanitizedStreetAddress1 !== undefined) profileUpdateData.streetAddress1 = sanitizedStreetAddress1;
    if (sanitizedStreetAddress2 !== undefined) profileUpdateData.streetAddress2 = sanitizedStreetAddress2;
    if (sanitizedTown !== undefined) profileUpdateData.town = sanitizedTown;
    if (sanitizedPostalCode !== undefined) profileUpdateData.postalCode = sanitizedPostalCode;
    if (regionId !== undefined) profileUpdateData.regionId = region?.id || null;
    if (sanitizedRegionText !== undefined) profileUpdateData.regionText = sanitizedRegionText;
    
    if (country) profileUpdateData.countryId = country.id;
    if (city) profileUpdateData.cityId = city.id;
    if (sanitizedPeaceAmbassadorNumber !== undefined) profileUpdateData.peaceAmbassadorNumber = sanitizedPeaceAmbassadorNumber;
    if (sanitizedBio !== undefined) profileUpdateData.bio = sanitizedBio;
    if (titleId !== undefined) profileUpdateData.titleId = titleId || null;

    // Build user update data
    const userUpdateData: any = {};
    if (sanitizedEmail) userUpdateData.email = sanitizedEmail;
    if (sanitizedFirstName && sanitizedSurname) {
      userUpdateData.name = `${sanitizedFirstName} ${sanitizedSurname}`;
    } else if (sanitizedFirstName) {
      userUpdateData.name = `${sanitizedFirstName} ${existingUser.profile?.lastName || ''}`.trim();
    } else if (sanitizedSurname) {
      userUpdateData.name = `${existingUser.profile?.firstName || ''} ${sanitizedSurname}`.trim();
    }

    // Add nation/tribe fields to user update data
    if (nationId !== undefined) userUpdateData.nationId = nationId || null;
    if (tribeId !== undefined) userUpdateData.tribeId = tribeId || null;
    if (nationRole !== undefined) userUpdateData.nationRole = nationRole || null;
    if (tribeRole !== undefined) userUpdateData.tribeRole = tribeRole || null;

    // Update user and profile
    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        ...userUpdateData,
        profile: existingUser.profile ? {
          update: profileUpdateData
        } : {
          create: {
            firstName: sanitizedFirstName || '',
            lastName: sanitizedSurname || '',
            ...profileUpdateData
          }
        }
      },
      include: {
        profile: {
          include: {
            country: true,
            city: true,
            region: true,
            title: true,
          },
        },
      }
    });
    
    // Log user update for audit purposes
    try {
      await userAuditLogger.logUserUpdate(
        id,
        {
          email: existingUser.email,
          name: existingUser.name,
          profile: existingUser.profile
        },
        {
          email: updatedUser.email,
          name: updatedUser.name,
          profile: updatedUser.profile
        },
        (session.user as any).id, // Admin user updating the user
        {
          source: 'admin_update_user',
          ipAddress: userAuditLogger.getClientIP(req.headers),
          userAgent: userAuditLogger.getUserAgent(req.headers),
        }
      );
    } catch (auditError) {
      console.error('Failed to log user update audit:', auditError);
      // Continue with the update process even if audit logging fails
    }
    
    // Log successful user update
    await auditLoggingMiddleware.logApiAccess(
      (session.user as any).id,
      'user_update',
      'users/[id]',
      true,
      req,
      {
        statusCode: 200,
        metadata: {
          updatedUserId: id,
          updatedFields: Object.keys(validation.data)
        }
      }
    );

    return createSecureResponse({
      message: 'User updated successfully',
      user: {
        id: updatedUser.id,
        firstName: updatedUser.profile?.firstName || '',
        lastName: updatedUser.profile?.lastName || '',
        email: updatedUser.email,
        updatedAt: updatedUser.updatedAt,
      }
    }, 200, req);
  } catch (error: any) {
    await auditLoggingMiddleware.logApiAccess(
      undefined,
      'user_update_error',
      'users/[id]',
      false,
      req,
      {
        statusCode: 500,
        errorMessage: error.message || 'Unknown error',
        metadata: {
          stack: error.stack || 'No stack trace'
        }
      }
    );

    console.error('Error updating user:', error);
    return createSecureResponse({ error: 'Internal server error' }, 500, req);
  } finally {
    performanceMonitor.endTimer('api_users_put');
  }
}
