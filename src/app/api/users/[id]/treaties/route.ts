import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for enhanced user treaty assignment with multiple treaty types
const userTreatyCreateSchema = z.object({
  treatyName: z.string().min(1, 'Treaty name is required').max(255, 'Name must be less than 255 characters'),
  treatyTypes: z.array(z.object({
    treatyTypeId: z.string().min(1, 'Treaty type is required'),
    businessDetails: z.object({
      businessName: z.string().min(1, 'Business name is required'),
      businessAddress: z.string().min(1, 'Business address is required'),
      businessEmail: z.string().email('Valid business email is required'),
      businessPhone: z.string().optional(),
      businessWebsite: z.string().optional(),
      businessDescription: z.string().optional(),
      categoryId: z.string().min(1, 'Business category is required'),
      subcategoryId: z.string().min(1, 'Business subcategory is required'),
    }),
  })).min(1, 'At least one treaty type is required'),
  email: z.string().email('Valid email is required'),
  currentCountryId: z.string().min(1, 'Current country is required'),
  currentCityId: z.string().min(1, 'Current city is required'),
  residenceCountryId: z.string().optional(),
  residenceCityId: z.string().optional(),
  residentialAddress: z.string().optional(),
  phoneNumbers: z.array(z.string()).optional(),
  signedDate: z.string().optional(),
  expirationDate: z.string().optional(),
  renewalDate: z.string().optional(),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
});

// GET /api/users/[id]/treaties - Get treaties for a specific user
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: userId } = await params;
    const { searchParams } = new URL(req.url);
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);

    // Verify the user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const skip = (page - 1) * limit;

    // Build where clause for search and filters - get treaties assigned to user via UserTreaty
    const where: any = {
      userTreaties: {
        some: {
          userId: userId,
        }
      },
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } },
        {
          treatyTreatyTypes: {
            some: {
              treatyType: {
                name: { contains: search, mode: 'insensitive' }
              }
            }
          }
        }
      ];
    }

    if (status && status !== 'all') {
      where.status = status;
    }

    // Fetch treaties with pagination
    const [treaties, totalCount] = await Promise.all([
      prisma.treaty.findMany({
        where,
        include: {
          userTreaties: true,
          treatyTreatyTypes: {
            include: {
              treatyType: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.treaty.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      treaties: treaties.map(treaty => ({
        id: treaty.id,
        name: treaty.name,
        status: treaty.status,
        signedDate: treaty.signedDate,
        expirationDate: treaty.expirationDate,
        renewalDate: treaty.renewalDate,
        treatyTypes: treaty.treatyTreatyTypes.map(tt => ({
          id: tt.treatyTypeId,
          name: tt.treatyType.name,
        })),
        notes: treaty.notes,
        createdAt: treaty.createdAt,
        updatedAt: treaty.updatedAt,
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        pageSize: limit,
      },
    });
  } catch (error) {
    console.error('Error fetching user treaties:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/users/[id]/treaties - Create a comprehensive treaty for a user
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: userId } = await params;
    
    // Handle both JSON and FormData
    let body: any;
    const contentType = req.headers.get('content-type');
    
    if (contentType?.includes('multipart/form-data')) {
      const formData = await req.formData();
      body = {};
      
      // Parse form data
      for (const [key, value] of formData.entries()) {
        if (key === 'treatyTypes') {
          body.treatyTypes = JSON.parse(value as string);
        } else if (key.startsWith('phoneNumbers[')) {
          if (!body.phoneNumbers) body.phoneNumbers = [];
          body.phoneNumbers.push(value);
        } else if (key.startsWith('logo_') || key === 'attachments') {
          // Handle file uploads later
          continue;
        } else {
          body[key] = value;
        }
      }
    } else {
      body = await req.json();
    }

    // Validate input
    const validation = userTreatyCreateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { 
      treatyName, 
      treatyTypes, 
      email, 
      currentCountryId, 
      currentCityId,
      residenceCountryId,
      residenceCityId,
      residentialAddress,
      phoneNumbers,
      signedDate, 
      expirationDate, 
      renewalDate, 
      notes 
    } = validation.data;

    // Verify the user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user already has a treaty with the same name
    const existingTreaty = await prisma.treaty.findFirst({
      where: {
        userId: userId,
        name: treatyName,
      },
    });

    if (existingTreaty) {
      return NextResponse.json(
        { error: 'User already has a treaty with this name', code: 'DUPLICATE_TREATY_NAME' },
        { status: 409 }
      );
    }

    // Validate all treaty types and check pricing
    const treatyTypeIds = treatyTypes.map(tt => tt.treatyTypeId);
    const validTreatyTypes = await prisma.treatyType.findMany({
      where: { 
        id: { in: treatyTypeIds },
        isActive: true,
        status: 'APPROVED'
      },
    });

    if (validTreatyTypes.length !== treatyTypeIds.length) {
      const foundIds = validTreatyTypes.map(tt => tt.id);
      const missingIds = treatyTypeIds.filter(id => !foundIds.includes(id));
      return NextResponse.json({ 
        error: 'Invalid treaty type(s)', 
        missingTypes: missingIds 
      }, { status: 400 });
    }

    // Check if payment is required (for future payment integration)
    const totalCost = validTreatyTypes.reduce((sum, tt) => sum + ((tt as any).price || 0), 0);
    const requiresPayment = totalCost > 0;
    if (requiresPayment) {
      // TODO: Integrate with payment system
      // For now, we'll allow creation but mark as pending payment
    }

    // Validate countries and cities
    const currentCountry = await prisma.country.findUnique({
      where: { id: parseInt(currentCountryId) },
    });

    const currentCity = await prisma.city.findUnique({
      where: { id: parseInt(currentCityId) },
    });

    if (!currentCountry || !currentCity) {
      return NextResponse.json({ error: 'Invalid country or city' }, { status: 400 });
    }

    // Validate business categories and subcategories for all treaty types
    const allCategoryIds = treatyTypes.map(tt => parseInt(tt.businessDetails.categoryId));
    const allSubcategoryIds = treatyTypes.map(tt => parseInt(tt.businessDetails.subcategoryId));

    const categories = await prisma.category.findMany({
      where: { id: { in: allCategoryIds } },
    });

    const subcategories = await prisma.subcategory.findMany({
      where: { id: { in: allSubcategoryIds } },
    });

    if (categories.length !== allCategoryIds.length || subcategories.length !== allSubcategoryIds.length) {
      return NextResponse.json({ error: 'Invalid business category or subcategory' }, { status: 400 });
    }

    // Create treaty record
    const treaty = await prisma.treaty.create({
      data: {
        userId: userId,
        name: treatyName,
        notes: notes || '',
        signedDate: signedDate ? new Date(signedDate) : undefined,
        expirationDate: expirationDate ? new Date(expirationDate) : undefined,
        renewalDate: renewalDate ? new Date(renewalDate) : undefined,
        status: requiresPayment ? 'PENDING_PAYMENT' : 'ACTIVE',
        treatyTreatyTypes: {
          create: treatyTypes.map(tt => ({
            treatyTypeId: tt.treatyTypeId,
            assignedBy: (session.user as any).id,
            status: 'ACTIVE',
          })),
        },
      },
      include: {
        treatyTreatyTypes: {
          include: {
            treatyType: true,
          },
        },
      },
    });

    // Create comprehensive treaty type details for each treaty type
    const treatyTypeDetailsRecords = await Promise.all(
      treatyTypes.map(async (treatyTypeEntry) => {
        const category = categories.find(c => c.id === parseInt(treatyTypeEntry.businessDetails.categoryId));
        const subcategory = subcategories.find(sc => sc.id === parseInt(treatyTypeEntry.businessDetails.subcategoryId));

        return prisma.treatyTypeDetails.create({
          data: {
            userId: userId,
            treatyId: treaty.id,
            treatyTypeId: treatyTypeEntry.treatyTypeId,
            status: requiresPayment ? 'PENDING_PAYMENT' : 'DRAFT',
            
            // Contact information
            email: email,
            phoneNumbers: phoneNumbers ? JSON.stringify(phoneNumbers.filter((p: string) => p.trim())) : undefined,
            currentCountryId: parseInt(currentCountryId),
            currentCityId: parseInt(currentCityId),
            residenceCountryId: residenceCountryId ? parseInt(residenceCountryId) : null,
            residenceCityId: residenceCityId ? parseInt(residenceCityId) : null,
            residentialAddress: residentialAddress,
            
            // Business details specific to this treaty type
            businessDetails: JSON.stringify({
              businessName: treatyTypeEntry.businessDetails.businessName,
              businessAddress: treatyTypeEntry.businessDetails.businessAddress,
              businessEmail: treatyTypeEntry.businessDetails.businessEmail,
              businessPhone: treatyTypeEntry.businessDetails.businessPhone,
              businessWebsite: treatyTypeEntry.businessDetails.businessWebsite,
              businessDescription: treatyTypeEntry.businessDetails.businessDescription,
              categoryId: treatyTypeEntry.businessDetails.categoryId,
              subcategoryId: treatyTypeEntry.businessDetails.subcategoryId,
              categoryName: category?.name,
              subcategoryName: subcategory?.name,
            }),
          },
        });
      })
    );

    // Create UserTreaty record for the junction table
    await prisma.userTreaty.create({
      data: {
        userId: userId,
        treatyId: treaty.id,
        status: 'ACTIVE',
      },
    });

    return NextResponse.json({
      message: 'Treaty created successfully',
      treaty: {
        id: treaty.id,
        name: treaty.name,
        status: treaty.status,
        signedDate: treaty.signedDate,
        expirationDate: treaty.expirationDate,
        renewalDate: treaty.renewalDate,
        treatyTypes: treaty.treatyTreatyTypes.map(tt => ({
          id: tt.treatyTypeId,
          name: tt.treatyType.name,
        })),
        notes: treaty.notes,
        requiresPayment: requiresPayment,
        totalCost: totalCost,
        currency: 'USD',
        createdAt: treaty.createdAt,
        updatedAt: treaty.updatedAt,
      },
      treatyTypeDetails: treatyTypeDetailsRecords.map(ttd => ({
        id: ttd.id,
        treatyTypeId: ttd.treatyTypeId,
        status: ttd.status,
      })),
    });
  } catch (error) {
    console.error('Error creating comprehensive treaty:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}