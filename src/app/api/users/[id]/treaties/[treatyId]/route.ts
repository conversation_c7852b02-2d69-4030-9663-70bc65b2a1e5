import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// DELETE /api/users/[id]/treaties/[treatyId] - Remove treaty assignment from user
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string; treatyId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: userId, treatyId } = await params;

    // Verify the user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if the user treaty assignment exists
    const userTreaty = await prisma.userTreaty.findUnique({
      where: {
        userId_treatyId: {
          userId: userId,
          treatyId: treatyId,
        },
      },
    });

    if (!userTreaty) {
      return NextResponse.json({ error: 'Treaty assignment not found' }, { status: 404 });
    }

    // Remove the user treaty assignment
    await prisma.userTreaty.delete({
      where: {
        userId_treatyId: {
          userId: userId,
          treatyId: treatyId,
        },
      },
    });

    // Check if this treaty is assigned to any other users
    const otherAssignments = await prisma.userTreaty.count({
      where: { treatyId: treatyId },
    });

    // If no other users have this treaty, we could optionally delete the treaty itself
    // But for now, we'll keep the treaty record for historical purposes
    // You can uncomment the following lines if you want to delete orphaned treaties:
    /*
    if (otherAssignments === 0) {
      await prisma.treaty.delete({
        where: { id: treatyId },
      });
    }
    */

    return NextResponse.json({
      message: 'Treaty assignment removed successfully',
    });
  } catch (error) {
    console.error('Error removing treaty assignment:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET /api/users/[id]/treaties/[treatyId] - Get specific treaty details for user
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string; treatyId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: userId, treatyId } = await params;

    // Verify the user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get the treaty with user assignment details
    const userTreaty = await prisma.userTreaty.findUnique({
      where: {
        userId_treatyId: {
          userId: userId,
          treatyId: treatyId,
        },
      },
      include: {
        treaty: {
          include: {
            treatyTreatyTypes: {
              include: {
                treatyType: true,
              },
            },
          },
        },
      },
    });

    if (!userTreaty) {
      return NextResponse.json({ error: 'Treaty assignment not found' }, { status: 404 });
    }

    const treaty = userTreaty.treaty;

    return NextResponse.json({
      id: treaty.id,
      name: treaty.name,
      status: treaty.status,
      signedDate: treaty.signedDate,
      expirationDate: treaty.expirationDate,
      renewalDate: treaty.renewalDate,
      treatyTypes: treaty.treatyTreatyTypes.map(tt => ({
        id: tt.treatyTypeId,
        name: tt.treatyType.name,
      })),
      notes: treaty.notes,
      assignedAt: userTreaty.assignedAt,
      assignmentStatus: userTreaty.status,
      createdAt: treaty.createdAt,
      updatedAt: treaty.updatedAt,
    });
  } catch (error) {
    console.error('Error fetching user treaty details:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}