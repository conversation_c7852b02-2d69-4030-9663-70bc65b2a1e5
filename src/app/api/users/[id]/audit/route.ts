import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const adminUser = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!adminUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = adminUser.userRoles.some(userRole => 
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = parseInt(url.searchParams.get('limit') || '20', 10);
    const action = url.searchParams.get('action'); // filter by action type

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Bad Request', message: 'Invalid pagination parameters' },
        { status: 400 }
      );
    }

    // Check if target user exists
    const targetUser = await prisma.user.findUnique({
      where: { id },
      select: { id: true, name: true, email: true }
    });

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Build where clause for audit logs
    const where: any = {
      OR: [
        { userId: id }, // Changes made by this user
        { 
          AND: [
            { resource: { contains: 'user', mode: 'insensitive' } },
            { resourceId: id }
          ]
        },
        { 
          AND: [
            { action: { contains: 'user', mode: 'insensitive' } },
            { 
              OR: [
                { details: { contains: id } },
                { oldValues: { path: ['id'], equals: id } },
                { newValues: { path: ['id'], equals: id } }
              ]
            }
          ]
        }
      ]
    };

    if (action) {
      where.action = { contains: action, mode: 'insensitive' };
    }

    // Fetch audit logs with pagination
    const [auditLogs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              profile: {
                select: {
                  firstName: true,
                  lastName: true
                }
              }
            }
          }
        },
        orderBy: {
          timestamp: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.auditLog.count({ where }),
    ]);

    // Transform audit logs for response
    const transformedLogs = auditLogs.map(log => ({
      id: log.id,
      action: log.action,
      details: log.oldValues || log.newValues || {},
      ipAddress: log.ipAddress,
      userAgent: log.userAgent,
      createdAt: log.timestamp.toISOString(),
      user: log.user ? {
        id: log.user.id,
        name: log.user.name,
        email: log.user.email,
        displayName: log.user.profile?.firstName && log.user.profile?.lastName
          ? `${log.user.profile.firstName} ${log.user.profile.lastName}`
          : log.user.name || log.user.email
      } : null
    }));

    return NextResponse.json({
      auditLogs: transformedLogs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      targetUser: {
        id: targetUser.id,
        name: targetUser.name,
        email: targetUser.email
      }
    });
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}