import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { localPermissionService } from '@/lib/services/local-permission-service'

// GET /api/users/[id]/roles - Get user's roles for both local and remote servers
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: userId } = await params
    const { searchParams } = new URL(request.url)
    const includeLocal = searchParams.get('includeLocal') === 'true'

    const { prisma } = await import('@/lib/prisma')

    // Get user's remote server access (roles are handled through role_remote_server_access)
    const userServerAccess = await prisma.user_remote_server_access.findMany({
      where: {
        user_id: userId,
        is_active: true
      },
      include: {
        remote_servers: {
          select: {
            id: true,
            name: true,
            url: true
          }
        }
      }
    })

    // Get role-based server access
    const roleServerAccess = await prisma.role_remote_server_access.findMany({
      where: {
        is_active: true
      },
      include: {
        remote_servers: {
          select: {
            id: true,
            name: true,
            url: true
          }
        },
        roles: {
          select: {
            name: true
          }
        }
      }
    })

    // Get user's roles to determine which role-based server access they have
    const userRoles = await prisma.userRole.findMany({
      where: {
        userId: userId
      },
      include: {
        role: {
          select: {
            name: true
          }
        }
      }
    })

    // Combine direct server access and role-based server access
    const allAccess: any[] = []

    // Add direct server access
    userServerAccess.forEach(access => {
      allAccess.push({
        id: `access_${access.id}`,
        userId: access.user_id,
        serverId: access.remote_server_id,
        server: access.remote_servers,
        grantedAt: access.granted_at,
        source: 'direct'
      })
    })

    // Add role-based server access
    userRoles.forEach(userRole => {
      const roleName = userRole.role.name
      roleServerAccess.forEach(roleAccess => {
        if (roleAccess.role_id === userRole.roleId) {
          allAccess.push({
            id: `role_access_${roleAccess.id}`,
            userId: userId,
            serverId: roleAccess.remote_server_id,
            server: roleAccess.remote_servers,
            grantedAt: roleAccess.created_at,
            source: 'role',
            roleName: roleName
          })
        }
      })
    })

    // Transform to the format expected by the frontend
    const remoteRoles = allAccess.map(access => ({
      id: access.id,
      userId: access.userId,
      serverId: access.serverId,
      roleName: access.roleName || 'member',
      isAssigned: true,
      assignedAt: access.grantedAt,
      server: access.server,
      source: access.source
    }))

    // Get local roles if requested
    let localRoles: any[] = []
    if (includeLocal) {
      localRoles = userRoles.map(userRole => ({
        id: `local_role_${userRole.id}`,
        userId: userId,
        roleId: userRole.roleId,
        roleName: userRole.role.name,
        isAssigned: true,
        assignedAt: userRole.assignedAt,
        source: 'local'
      }))
    }

    return Response.json({
      success: true,
      roles: [...remoteRoles, ...localRoles],
      localRoles: localRoles,
      remoteRoles: remoteRoles
    })
  } catch (error) {
    console.error('Error fetching user roles:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/users/[id]/roles - Toggle user role
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: userId } = await params
    const body = await request.json()
    const { serverId, roleName, action } = body

    console.log('Role assignment request:', { userId, serverId, roleName, action })

    if (!serverId || !roleName || !action) {
      console.log('Missing required fields:', { serverId, roleName, action })
      return Response.json(
        { error: 'Missing required fields: serverId, roleName, action' },
        { status: 400 }
      )
    }

    const { prisma } = await import('@/lib/prisma')

    // Handle remote server roles
    if (serverId !== 'local') {
      if (action === 'toggle') {
        // For remote servers, we use the user_remote_server_permissions table for individual permissions
        // or role_remote_server_access for role-based access

        if (roleName && roleName !== 'member') {
          // This is a specific role assignment - use role_remote_server_access
          // First, get the role ID from the role name
          let role = await prisma.role.findUnique({
            where: { name: roleName }
          })

          // If role doesn't exist locally, create it
          if (!role) {
            console.log(`Creating new role: ${roleName}`)
            role = await prisma.role.create({
              data: {
                name: roleName,
                description: `Role from remote server: ${roleName}`,
                isSystem: false
              }
            })
          }

          const existingRoleAccess = await prisma.role_remote_server_access.findUnique({
            where: {
              role_id_remote_server_id: {
                role_id: role.id, // Use the actual role ID
                remote_server_id: serverId
              }
            }
          })

          if (existingRoleAccess) {
            // Toggle existing role access
            await prisma.role_remote_server_access.update({
              where: {
                role_id_remote_server_id: {
                  role_id: role.id,
                  remote_server_id: serverId
                }
              },
              data: {
                is_active: !existingRoleAccess.is_active
              }
            })

            return Response.json({
              success: true,
              message: `Role access ${existingRoleAccess.is_active ? 'revoked' : 'granted'} successfully`,
              isAssigned: !existingRoleAccess.is_active
            })
          } else {
            // Create new role access
            await prisma.role_remote_server_access.create({
              data: {
                role_id: role.id, // Use the actual role ID
                remote_server_id: serverId,
                auto_grant_permissions: [],
                is_active: true
              }
            })

            return Response.json({
              success: true,
              message: 'Role access granted successfully',
              isAssigned: true
            })
          }
        } else {
          // This is basic server access - use user_remote_server_access
          const existingAccess = await prisma.user_remote_server_access.findUnique({
            where: {
              user_id_remote_server_id: {
                user_id: userId,
                remote_server_id: serverId
              }
            }
          })

          if (existingAccess) {
            // Toggle existing access
            await prisma.user_remote_server_access.update({
              where: {
                user_id_remote_server_id: {
                  user_id: userId,
                  remote_server_id: serverId
                }
              },
              data: {
                is_active: !existingAccess.is_active
              }
            })

            return Response.json({
              success: true,
              message: `Server access ${existingAccess.is_active ? 'revoked' : 'granted'} successfully`,
              isAssigned: !existingAccess.is_active
            })
          } else {
            // Create new access
            await prisma.user_remote_server_access.create({
              data: {
                user_id: userId,
                remote_server_id: serverId,
                granted_by: (session.user as any).id,
                is_active: true
              }
            })

            return Response.json({
              success: true,
              message: 'Server access granted successfully',
              isAssigned: true
            })
          }
        }
      } else {
        return Response.json(
          { error: 'Invalid action. Use "toggle"' },
          { status: 400 }
        )
      }
    }

    // Handle local roles (existing logic)
    if (action === 'assign') {
      // Check if role exists
      console.log('Looking for role:', roleName)
      const role = await prisma.role.findUnique({
        where: { name: roleName }
      })

      console.log('Found role:', role)

      if (!role) {
        // List all available roles for debugging
        const allRoles = await prisma.role.findMany({
          select: { name: true }
        })
        console.log('Available roles:', allRoles.map(r => r.name))

        return Response.json(
          { error: `Role '${roleName}' not found. Available roles: ${allRoles.map(r => r.name).join(', ')}` },
          { status: 404 }
        )
      }

      // Check if user already has this role
      const existingUserRole = await prisma.userRole.findUnique({
        where: {
          userId_roleId: {
            userId: userId,
            roleId: role.id
          }
        }
      })

      if (existingUserRole) {
        return Response.json(
          { error: 'User already has this role' },
          { status: 400 }
        )
      }

      // Assign role to user
      await prisma.userRole.create({
        data: {
          userId: userId,
          roleId: role.id,
          assignedBy: (session.user as any).id
        }
      })

      return Response.json({
        success: true,
        message: 'Role assigned successfully'
      })

    } else if (action === 'remove') {
      // Check if role exists
      const role = await prisma.role.findUnique({
        where: { name: roleName }
      })

      if (!role) {
        return Response.json(
          { error: `Role '${roleName}' not found` },
          { status: 404 }
        )
      }

      // Check if user has this role
      const existingUserRole = await prisma.userRole.findUnique({
        where: {
          userId_roleId: {
            userId: userId,
            roleId: role.id
          }
        }
      })

      if (!existingUserRole) {
        return Response.json(
          { error: 'User does not have this role' },
          { status: 400 }
        )
      }

      // Remove role from user
      await prisma.userRole.delete({
        where: {
          userId_roleId: {
            userId: userId,
            roleId: role.id
          }
        }
      })

      return Response.json({
        success: true,
        message: 'Role removed successfully'
      })
    } else {
      return Response.json(
        { error: 'Invalid action. Use "assign" or "remove"' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Error toggling user role:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}