import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';

// Helper function to format dates
function formatDate(date: Date | null | undefined): string | null {
  if (!date) return null;
  return date.toISOString();
}

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const adminUser = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!adminUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = adminUser.userRoles.some(userRole => 
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    
    // Fetch user with all relations
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        profile: {
          include: {
            city: true,
            country: true,
            region: true,
          }
        },
        userIdentifications: true,
        userPositions: true,
        userTreaties: true,
        userTreatyNumbers: {
          include: {
            treatyType: true
          }
        },
        ordinances: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Transform user data for response
    const transformedUser = {
      id: user.id,
      name: user.name,
      email: user.email,
      createdAt: formatDate(user.createdAt),
      updatedAt: formatDate(user.updatedAt),
      profile: user.profile ? {
        firstName: user.profile.firstName,
        lastName: user.profile.lastName,
        personalEmail: user.profile.personalEmail,
        nwaEmail: user.profile.nwaEmail,
        dateOfBirth: formatDate(user.profile.dateOfBirth),
        phone: user.profile.phone,
        mobile: user.profile.mobile,
        bio: user.profile.bio,
        streetAddress1: user.profile.streetAddress1,
        streetAddress2: user.profile.streetAddress2,
        town: user.profile.town,
        city: user.profile.city,
        country: user.profile.country,
        postalCode: user.profile.postalCode,
        regionId: user.profile.regionId,
        regionText: user.profile.regionText,
        peaceAmbassadorNumber: user.profile.peaceAmbassadorNumber,
      } : null,
      identifications: user.userIdentifications.map(identification => ({
        id: identification.id,
        identificationTypeId: identification.identificationTypeId,
        idNumber: identification.idNumber,
        isActive: identification.isActive,
        createdAt: formatDate(identification.createdAt),
        updatedAt: formatDate(identification.updatedAt),
      })),
      positions: user.userPositions.map(position => ({
        id: position.id,
        positionId: position.positionId,
        startDate: formatDate(position.startDate),
        endDate: formatDate(position.endDate),
        isActive: position.isActive,
      })),
      treaties: user.userTreaties.map(treaty => ({
        id: treaty.id,
        treatyId: treaty.treatyId,
        assignedAt: formatDate(treaty.assignedAt),
        status: treaty.status,
      })),
      ordinances: user.ordinances.map(ordinance => ({
        id: ordinance.id,
        ordinanceTypeId: ordinance.ordinanceTypeId,
        status: ordinance.status,
        completedDate: formatDate(ordinance.completedDate),
        expirationDate: formatDate(ordinance.expirationDate),
        notes: ordinance.notes,
        documentPath: ordinance.documentPath,
        createdAt: formatDate(ordinance.createdAt),
        updatedAt: formatDate(ordinance.updatedAt),
      })),
      treatyNumbers: user.userTreatyNumbers.map(treatyNumber => ({
        id: treatyNumber.id,
        treatyTypeId: treatyNumber.treatyTypeId,
        treatyNumber: treatyNumber.treatyNumber,
        isActive: treatyNumber.isActive,
        createdAt: formatDate(treatyNumber.createdAt),
        updatedAt: formatDate(treatyNumber.updatedAt),
        treatyType: treatyNumber.treatyType ? {
          id: treatyNumber.treatyType.id,
          name: treatyNumber.treatyType.name,
          description: treatyNumber.treatyType.description,
          category: treatyNumber.treatyType.category,
        } : null
      })),
    };

    return NextResponse.json({
      user: transformedUser,
    });
  } catch (error) {
    console.error('Error fetching full user data:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
