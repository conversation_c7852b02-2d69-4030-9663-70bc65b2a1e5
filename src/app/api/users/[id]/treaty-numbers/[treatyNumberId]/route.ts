import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/middleware/require-auth';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; treatyNumberId: string }> }
) {
  try {
    const authUser = await requireAuth(request);

    const { id: userId, treatyNumberId } = await params;
    const body = await request.json();
    const { treatyNumber, treatyTypeId } = body;

    // Check if user can modify this data (own profile or admin)
    if (authUser.userId !== userId && !authUser.roles.includes('admin')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Validate required fields
    if (!treatyNumber || !treatyTypeId) {
      return NextResponse.json(
        { error: 'Treaty number and treatyTypeId are required' },
        { status: 400 }
      );
    }

    // Validate treaty number format: 3 letters + 6 alphanumeric characters
    const treatyNumberTrimmed = String(treatyNumber).trim();
    if (!/^[A-Za-z]{3}[A-Za-z0-9]{6}$/.test(treatyNumberTrimmed)) {
      return NextResponse.json(
        { error: 'Invalid treaty number format', code: 'INVALID_TREATY_FORMAT' },
        { status: 400 }
      );
    }

    // Verify the treaty number exists and belongs to the user
    const existingTreatyNumber = await prisma.userTreatyNumber.findFirst({
      where: {
        id: treatyNumberId,
        userId,
        isActive: true
      },
      include: { treatyType: { select: { id: true, name: true } } }
    });

    if (!existingTreatyNumber) {
      return NextResponse.json(
        { error: 'Treaty number not found' },
        { status: 404 }
      );
    }

    // Verify treaty type exists and is active
    const treatyType = await prisma.treatyType.findUnique({ where: { id: treatyTypeId } });
    if (!treatyType || !treatyType.isActive) {
      return NextResponse.json(
        { error: 'Treaty type not found or inactive' },
        { status: 404 }
      );
    }

    // Check uniqueness per (userId, treatyTypeId) excluding current record
    const duplicateForType = await prisma.userTreatyNumber.findFirst({
      where: {
        userId,
        treatyTypeId,
        isActive: true,
        NOT: { id: treatyNumberId }
      }
    });

    if (duplicateForType) {
      return NextResponse.json(
        { error: 'User already has a treaty number for this type', code: 'TREATY_TYPE_EXISTS' },
        { status: 409 }
      );
    }

    const updatedTreatyNumber = await prisma.userTreatyNumber.update({
      where: { id: treatyNumberId },
      data: {
        treatyNumber: treatyNumberTrimmed,
        treatyTypeId,
        updatedAt: new Date()
      },
      include: { treatyType: { select: { id: true, name: true } } }
    });

    // Log the audit event
    await 
auditLoggingMiddleware.logApiAccess(
      
      authUser.userId,
      'update',
      'user_treaty_numbers',
      true,
      request,
      {
        statusCode: 200,
        metadata: { 
          targetUserId: userId,
          treatyNumberId,
          oldTreatyNumber: existingTreatyNumber.treatyNumber,
          newTreatyNumber: treatyNumberTrimmed,
          oldTreatyTypeId: existingTreatyNumber.treatyType?.id,
          newTreatyTypeId: treatyTypeId
        }
      }
    );

    return NextResponse.json({ treatyNumber: updatedTreatyNumber });
  } catch (error) {
    console.error('Error updating user treaty number:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; treatyNumberId: string }> }
) {
  try {
    const authUser = await requireAuth(request);

    const { id: userId, treatyNumberId } = await params;

    // Check if user can modify this data (own profile or admin)
    if (authUser.userId !== userId && !authUser.roles.includes('admin')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Verify the treaty number exists and belongs to the user
    const existingTreatyNumber = await prisma.userTreatyNumber.findFirst({
      where: {
        id: treatyNumberId,
        userId,
        isActive: true
      },
      include: { treatyType: { select: { id: true, name: true } } }
    });

    if (!existingTreatyNumber) {
      return NextResponse.json(
        { error: 'Treaty number not found' },
        { status: 404 }
      );
    }

    // Soft delete the treaty number
    await prisma.userTreatyNumber.update({
      where: { id: treatyNumberId },
      data: {
        isActive: false,
        updatedAt: new Date()
      }
    });

    // Log the audit event
    await 
auditLoggingMiddleware.logApiAccess(
      
      authUser.userId,
      'delete',
      'user_treaty_numbers',
      true,
      request,
      {
        statusCode: 200,
        metadata: { 
          targetUserId: userId,
          treatyNumberId,
          treatyNumber: existingTreatyNumber.treatyNumber,
          treatyType: existingTreatyNumber.treatyType
        }
      }
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting user treaty number:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; treatyNumberId: string }> }
) {
  try {
    const authUser = await requireAuth(request);

    const { id: userId, treatyNumberId } = await params;

    // Check if user can access this data (own profile or admin)
    if (authUser.userId !== userId && !authUser.roles.includes('admin')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    const treatyNumber = await prisma.userTreatyNumber.findFirst({
      where: {
        id: treatyNumberId,
        userId,
        isActive: true
      },
      include: { treatyType: { select: { id: true, name: true } } }
    });

    if (!treatyNumber) {
      return NextResponse.json(
        { error: 'Treaty number not found' },
        { status: 404 }
      );
    }

    // Log the audit event
    await 
auditLoggingMiddleware.logApiAccess(
      
      authUser.userId,
      'read',
      'user_treaty_numbers',
      true,
      request,
      {
        statusCode: 200,
        metadata: { 
          targetUserId: userId,
          treatyNumberId
        }
      }
    );

    return NextResponse.json({ treatyNumber });
  } catch (error) {
    console.error('Error fetching user treaty number:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
