import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/middleware/require-auth';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const authUser = await requireAuth(request);

    const { id: userId } = await params;

    // Check if user can access this data (own profile or admin)
    if (authUser.userId !== userId && !authUser.roles.includes('admin')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Verify the target user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!targetUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const treatyNumbers = await prisma.userTreatyNumber.findMany({
      where: { 
        userId,
        isActive: true 
      },
      include: {
        treatyType: {
          select: { id: true, name: true }
        }
      },
      orderBy: [
        { treatyType: { name: 'asc' } },
        { treatyNumber: 'asc' }
      ]
    });

    // TODO: Re-enable audit logging after fixing foreign key constraint issue
    // await auditLoggingMiddleware.logApiAccess(
    //   authUser.userId,
    //   'read',
    //   'user_treaty_numbers',
    //   true,
    //   request,
    //   {
    //     statusCode: 200,
    //     metadata: { targetUserId: userId, count: treatyNumbers.length }
    //   }
    // );

    return NextResponse.json({ treatyNumbers });
  } catch (error) {
    console.error('Error fetching user treaty numbers:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const authUser = await requireAuth(request);

    const { id: userId } = await params;
    const body = await request.json();
    const { treatyNumber, treatyTypeId } = body;

    // Check if user can modify this data (own profile or admin)
    if (authUser.userId !== userId && !authUser.roles.includes('admin')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Validate required fields
    if (!treatyNumber || !treatyTypeId) {
      return NextResponse.json(
        { error: 'Treaty number and treatyTypeId are required' },
        { status: 400 }
      );
    }

    // Validate treaty number format: 3 letters + 6 alphanumeric characters
    const treatyNumberTrimmed = String(treatyNumber).trim();
    if (!/^[A-Za-z]{3}[A-Za-z0-9]{6}$/.test(treatyNumberTrimmed)) {
      return NextResponse.json(
        { error: 'Invalid treaty number format', code: 'INVALID_TREATY_FORMAT' },
        { status: 400 }
      );
    }

    // Verify the target user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!targetUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Verify treaty type exists and is active
    const treatyType = await prisma.treatyType.findUnique({ where: { id: treatyTypeId } });
    if (!treatyType || !treatyType.isActive) {
      return NextResponse.json(
        { error: 'Treaty type not found or inactive' },
        { status: 404 }
      );
    }

    // Check uniqueness: one number per user per treaty type
    const existingForType = await prisma.userTreatyNumber.findFirst({
      where: { userId, treatyTypeId, isActive: true }
    });

    if (existingForType) {
      return NextResponse.json(
        { error: 'User already has a treaty number for this type', code: 'TREATY_TYPE_EXISTS' },
        { status: 409 }
      );
    }

    // Find which treaty this treaty type belongs to
    const treatyTreatyType = await prisma.treatyTreatyType.findFirst({
      where: { treatyTypeId },
      include: { treaty: true }
    });

    if (!treatyTreatyType) {
      return NextResponse.json(
        { error: 'Treaty type is not assigned to any treaty', code: 'TREATY_TYPE_NOT_ASSIGNED' },
        { status: 400 }
      );
    }

    const treatyNumberRecord = await prisma.userTreatyNumber.create({
      data: {
        userId,
        treatyTypeId,
        treatyNumber: treatyNumberTrimmed
      },
      include: {
        treatyType: { select: { id: true, name: true } }
      }
    });

    // Create UserTreaty record if it doesn't exist
    const existingUserTreaty = await prisma.userTreaty.findFirst({
      where: {
        userId: userId,
        treatyId: treatyTreatyType.treatyId
      }
    });

    if (!existingUserTreaty) {
      await prisma.userTreaty.create({
        data: {
          userId: userId,
          treatyId: treatyTreatyType.treatyId,
          status: 'ACTIVE',
          notes: `Auto-created from treaty number ${treatyNumberTrimmed} assignment`
        }
      });
    }

    // TODO: Re-enable audit logging after fixing foreign key constraint issue
    // await auditLoggingMiddleware.logApiAccess(
    //   authUser.userId,
    //   'create',
    //   'user_treaty_numbers',
    //   true,
    //   request,
    //   {
    //     statusCode: 201,
    //     metadata: { 
    //       targetUserId: userId, 
    //       treatyNumberId: treatyNumberRecord.id,
    //       treatyNumber: treatyNumberTrimmed,
    //       treatyTypeId
    //     }
    //   }
    // );

    return NextResponse.json({ treatyNumber: treatyNumberRecord }, { status: 201 });
  } catch (error) {
    console.error('Error creating user treaty number:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authUser = await requireAuth(request);
    const { id: userId } = await params;
    const { searchParams } = new URL(request.url);
    const treatyNumberId = searchParams.get('treatyNumberId');

    if (!treatyNumberId) {
      return NextResponse.json(
        { error: 'Treaty number ID is required' },
        { status: 400 }
      );
    }

    // Check if user can modify this data (own profile or admin)
    if (authUser.userId !== userId && !authUser.roles.includes('admin')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Verify the treaty number exists and belongs to the user
    const treatyNumber = await prisma.userTreatyNumber.findFirst({
      where: { 
        id: treatyNumberId,
        userId,
        isActive: true 
      },
    });

    if (!treatyNumber) {
      return NextResponse.json(
        { error: 'Treaty number not found' },
        { status: 404 }
      );
    }

    // Soft delete by setting isActive to false
    await prisma.userTreatyNumber.update({
      where: { id: treatyNumberId },
      data: { isActive: false },
    });

    // TODO: Re-enable audit logging after fixing foreign key constraint issue
    // await auditLoggingMiddleware.logApiAccess(
    //   authUser.userId,
    //   'delete',
    //   'user_treaty_numbers',
    //   true,
    //   request,
    //   {
    //     statusCode: 200,
    //     metadata: { 
    //       targetUserId: userId, 
    //       treatyNumberId,
    //       treatyNumber: treatyNumber.treatyNumber
    //     }
    //   }
    // );

    return NextResponse.json({ 
      success: true,
      message: 'Treaty number removed successfully'
    });
  } catch (error) {
    console.error('Error removing user treaty number:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
