import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// Helper function to check if user is admin
async function isAdmin(userId: string): Promise<boolean> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { userRoles: { include: { role: true } } },
  });

  if (!user) return false;

  const hasAdminRole = user.userRoles.some(ur => 
    ur.role.name.toLowerCase().includes('admin')
  );

  return hasAdminRole;
}

// PUT /api/admin/treaty-types/[id]/pricing - Update treaty type pricing (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const adminCheck = await isAdmin((session.user as any).id);
    if (!adminCheck) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const {
      price,
      currency,
      requiresPayment,
      paymentDeadlineDays,
      autoApprove,
    } = body;

    // Validate required fields
    if (price === undefined || price === null) {
      return NextResponse.json(
        { error: 'Price is required' },
        { status: 400 }
      );
    }

    // Validate price is non-negative
    if (typeof price !== 'number' || price < 0) {
      return NextResponse.json(
        { error: 'Price must be a non-negative number' },
        { status: 400 }
      );
    }

    // Validate currency if provided
    if (currency && !['USD', 'EUR', 'GBP', 'CAD', 'AUD'].includes(currency)) {
      return NextResponse.json(
        { error: 'Invalid currency code' },
        { status: 400 }
      );
    }

    // Get existing treaty type
    const existingTreatyType = await prisma.treatyType.findUnique({
      where: { id },
    });

    if (!existingTreatyType) {
      return NextResponse.json(
        { error: 'Treaty type not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      price,
      ...(currency && { currency }),
      ...(requiresPayment !== undefined && { requiresPayment }),
      ...(paymentDeadlineDays !== undefined && { paymentDeadlineDays }),
      ...(autoApprove !== undefined && { autoApprove }),
    };

    // If requiresPayment is false, set price to 0
    if (requiresPayment === false) {
      updateData.price = 0;
    }

    // Update the treaty type
    const updatedTreatyType = await prisma.treatyType.update({
      where: { id },
      data: updateData,
    });

    // Log the admin action
    await prisma.auditLog.create({
      data: {
        userId: (session.user as any).id,
        action: 'UPDATE_TREATY_TYPE_PRICING',
        resource: 'TreatyType',
        resourceId: id,
        success: true,
        oldValues: {
          price: existingTreatyType.price,
          currency: existingTreatyType.currency,
          requiresPayment: existingTreatyType.requiresPayment,
          paymentDeadlineDays: existingTreatyType.paymentDeadlineDays,
        },
        newValues: updateData,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    });

    return NextResponse.json({
      success: true,
      treatyType: updatedTreatyType,
      message: 'Treaty type pricing updated successfully',
    });
  } catch (error) {
    console.error('Error updating treaty type pricing:', error);
    return NextResponse.json(
      { error: 'Failed to update treaty type pricing' },
      { status: 500 }
    );
  }
}

// GET /api/admin/treaty-types/[id]/pricing - Get treaty type pricing (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const adminCheck = await isAdmin((session.user as any).id);
    if (!adminCheck) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;

    // Get treaty type pricing details
    const treatyType = await prisma.treatyType.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        description: true,
        price: true,
        currency: true,
        requiresPayment: true,
        paymentDeadlineDays: true,
                isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!treatyType) {
      return NextResponse.json(
        { error: 'Treaty type not found' },
        { status: 404 }
      );
    }

    // Get additional statistics
    const [applicationCount, activeCount, pendingCount] = await Promise.all([
      prisma.userTreatyType.count({
        where: { treatyTypeId: id },
      }),
      prisma.userTreatyType.count({
        where: {
          treatyTypeId: id,
          status: 'ACTIVE',
        },
      }),
      prisma.userTreatyType.count({
        where: {
          treatyTypeId: id,
          status: {
            in: ['APPLIED', 'UNDER_REVIEW', 'APPROVED'],
          },
        },
      }),
    ]);

    return NextResponse.json({
      treatyType,
      statistics: {
        totalApplications: applicationCount,
        activeTreaties: activeCount,
        pendingApplications: pendingCount,
      },
    });
  } catch (error) {
    console.error('Error fetching treaty type pricing:', error);
    return NextResponse.json(
      { error: 'Failed to fetch treaty type pricing' },
      { status: 500 }
    );
  }
}