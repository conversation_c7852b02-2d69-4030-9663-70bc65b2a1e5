import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { checkAdmin } from '@/lib/check-admin';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import * as bcrypt from 'bcryptjs';

// Zod schema for request validation
const projectSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  allowedOrigins: z.array(z.string().url()).optional(),
});

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    const userId = (session.user as any).id;
    const isAdmin = await checkAdmin(userId);
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = parseInt(url.searchParams.get('limit') || '25', 10);
    const active = url.searchParams.get('active');

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Bad Request', message: 'Invalid pagination parameters' },
        { status: 400 }
      );
    }

    // Build where clause
    const where: any = {};
    if (active === 'true') {
      where.isActive = true;
    } else if (active === 'false') {
      where.isActive = false;
    }

    // Fetch projects with pagination
    const [projects, total] = await Promise.all([
      prisma.project.findMany({
        where,
        select: {
          id: true,
          name: true,
          description: true,
          allowedOrigins: true,
          isActive: true,
          createdAt: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.project.count({ where }),
    ]);

    // Return paginated response
    return NextResponse.json({
      projects,
      pagination: {
        page,
        limit,
        total,
      },
    });
  } catch (error) {
    console.error('Error fetching projects:', error);

    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    const userId = (session.user as any).id;
    const isAdmin = await checkAdmin(userId);
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    const parsed = projectSchema.safeParse(body);

    if (!parsed.success) {
      return NextResponse.json(
        {
          error: 'Bad Request',
          message: 'Invalid request body',
          details: parsed.error.flatten()
        },
        { status: 400 }
      );
    }

    const { name, description, allowedOrigins } = parsed.data;

    // Generate a simple API key hash
    const apiKey = `prj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const apiKeyHash = await bcrypt.hash(apiKey, 10);

    // Create new project
    const project = await prisma.project.create({
      data: {
        name,
        description,
        apiKeyHash,
        allowedOrigins: allowedOrigins || [],
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        description: true,
        allowedOrigins: true,
        isActive: true,
        createdAt: true,
      },
    });

    // Log the project creation
    await prisma.auditLog.create({
      data: {
        userId: (session.user as any).id,
        action: 'ADMIN_CREATE_PROJECT',
        resource: 'Project',
        resourceId: project.id,
        success: true,
        oldValues: { id: null, name: null, description: null },
        newValues: {
          id: project.id,
          name: project.name,
          description: project.description,
          allowedOrigins: project.allowedOrigins,
          isActive: project.isActive,
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    });

    // Return project with API key (only on creation)
    return NextResponse.json({
      project: {
        ...project,
        apiKey, // Include the API key only on creation
      },
    });
  } catch (error) {
    console.error('Error creating project:', error);

    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}