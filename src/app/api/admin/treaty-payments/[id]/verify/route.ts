import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { Prisma, ApplicationStatus, PaymentStatus } from '@prisma/client';

// Helper function to check if user is admin
async function isAdmin(userId: string): Promise<boolean> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { userRoles: { include: { role: true } } },
  });

  if (!user) return false;

  const hasAdminRole = user.userRoles.some(ur => 
    ur.role.name.toLowerCase().includes('admin')
  );

  return hasAdminRole;
}

// PUT /api/admin/treaty-payments/[id]/verify - Verify payment (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const adminCheck = await isAdmin((session.user as any).id);
    if (!adminCheck) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { action, verificationNotes, receiptNumber } = body;

    // Validate required fields
    if (!action || !['verify', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: 'Valid action (verify or reject) is required' },
        { status: 400 }
      );
    }

    // Get existing payment
    const existingPayment = await prisma.payment.findUnique({
      where: { id },
      include: {
        userTreatyType: {
          include: { treatyType: true },
        },
      },
    });

    if (!existingPayment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      );
    }

    // Check if payment is in a verifiable state
    const verifiableStatuses: PaymentStatus[] = [
      PaymentStatus.AWAITING_PAYMENT,
      PaymentStatus.PENDING,
      PaymentStatus.FAILED
    ];
    if (!verifiableStatuses.includes(existingPayment.status)) {
      return NextResponse.json(
        { error: `Cannot ${action} payment with status ${existingPayment.status}` },
        { status: 400 }
      );
    }

    let newStatus: PaymentStatus;
    let applicationUpdate: any = {};

    switch (action) {
      case 'verify':
        newStatus = PaymentStatus.PAID;
        applicationUpdate = {
          paymentStatus: PaymentStatus.PAID,
          status: ApplicationStatus.ACTIVE, // Activate the treaty
        };
        break;
      case 'reject':
        newStatus = PaymentStatus.FAILED;
        applicationUpdate = {
          paymentStatus: PaymentStatus.AWAITING_PAYMENT, // Reset to awaiting payment
        };
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    // Update the payment
    const updatedPayment = await prisma.payment.update({
      where: { id },
      data: {
        status: newStatus,
        processedBy: (session.user as any).id,
        notes: verificationNotes || existingPayment.notes,
        ...(receiptNumber && { transactionId: receiptNumber }),
      },
      include: {
        userTreatyType: {
          include: {
            treatyType: true,
            user: true,
          },
        },
        processor: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Update the application status
    await prisma.userTreatyType.update({
      where: { id: existingPayment.userTreatyTypeId },
      data: applicationUpdate,
    });

    // Log the admin action
    await prisma.auditLog.create({
      data: {
        userId: (session.user as any).id,
        action: `ADMIN_${action.toUpperCase()}_PAYMENT`,
        resource: 'Payment',
        resourceId: id,
        success: true,
        oldValues: {
          status: existingPayment.status,
          notes: existingPayment.notes,
        },
        newValues: {
          paymentStatus: newStatus,
          verificationNotes,
          receiptNumber,
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    });

    // If activating a treaty, log that as well
    if (action === 'verify') {
      await prisma.auditLog.create({
        data: {
          userId: (session.user as any).id,
          action: 'ACTIVATE_TREATY',
          resource: 'UserTreatyType',
          resourceId: existingPayment.userTreatyTypeId,
          success: true,
          oldValues: {
            status: existingPayment.userTreatyType.status,
            paymentStatus: existingPayment.userTreatyType.paymentStatus,
          },
          newValues: {
            status: ApplicationStatus.ACTIVE,
            paymentStatus: PaymentStatus.PAID,
          },
          ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
        },
      });
    }

    return NextResponse.json({
      success: true,
      payment: updatedPayment,
      message: `Payment ${action}ed successfully`,
    });
  } catch (error) {
    console.error('Error verifying payment:', error);
    return NextResponse.json(
      { error: 'Failed to verify payment' },
      { status: 500 }
    );
  }
}