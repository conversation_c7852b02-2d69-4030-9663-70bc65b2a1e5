import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { Prisma, ApplicationStatus, PaymentStatus } from '@prisma/client';

// Helper function to check if user is admin
async function isAdmin(userId: string): Promise<boolean> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { userRoles: { include: { role: true } } },
  });

  if (!user) return false;

  const hasAdminRole = user.userRoles.some(ur => 
    ur.role.name.toLowerCase().includes('admin')
  );

  return hasAdminRole;
}

// GET /api/admin/treaty-payments/pending - Get pending payments for verification
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const adminCheck = await isAdmin((session.user as any).id);
    if (!adminCheck) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const skip = (page - 1) * limit;

    // Get pending payments with related application data
    const [payments, total] = await Promise.all([
      prisma.payment.findMany({
        where: {
          status: {
            in: [PaymentStatus.AWAITING_PAYMENT, PaymentStatus.PENDING],
          },
        },
        include: {
          userTreatyType: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  profile: {
                    select: {
                      peaceAmbassadorNumber: true,
                    },
                  },
                },
              },
              treatyType: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                  price: true,
                  requiresPayment: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'asc', // Oldest first for processing priority
        },
        skip,
        take: limit,
      }),
      prisma.payment.count({
        where: {
          status: {
            in: [PaymentStatus.AWAITING_PAYMENT, PaymentStatus.PENDING],
          },
        },
      }),
    ]);

    return NextResponse.json({
      payments,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching pending payments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pending payments' },
      { status: 500 }
    );
  }
}

// POST /api/admin/treaty-payments - Create payment record (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const adminCheck = await isAdmin((session.user as any).id);
    if (!adminCheck) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const {
      userTreatyTypeId,
      amount,
      currency,
      paymentMethod,
      status,
      notes,
    } = body;

    // Validate required fields
    if (!userTreatyTypeId || !amount || !paymentMethod || !status) {
      return NextResponse.json(
        { error: 'User treaty type ID, amount, payment method, and status are required' },
        { status: 400 }
      );
    }

    // Get the application
    const application = await prisma.userTreatyType.findUnique({
      where: { id: userTreatyTypeId },
      include: { treatyType: true },
    });

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    // Create the payment
    const payment = await prisma.payment.create({
      data: {
        userTreatyTypeId,
        amount,
        currency: currency || 'USD',
        paymentMethod,
        status,
        paymentDate: new Date(),
        notes: notes || null,
        processedBy: (session.user as any).id,
      },
      include: {
        userTreatyType: {
          include: {
            treatyType: true,
            user: true,
          },
        },
        processor: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Update application payment status if needed
    if (status === PaymentStatus.PAID) {
      await prisma.userTreatyType.update({
        where: { id: userTreatyTypeId },
        data: {
          paymentStatus: PaymentStatus.PAID,
          status: ApplicationStatus.ACTIVE, // Activate the treaty
        },
      });
    }

    // Log the admin action
    await prisma.auditLog.create({
      data: {
        userId: (session.user as any).id,
        action: 'ADMIN_CREATE_PAYMENT',
        resource: 'Payment',
        resourceId: payment.id,
        success: true,
        oldValues: { id: null, userTreatyTypeId: null, amount: null },
        newValues: {
          userTreatyTypeId,
          amount,
          paymentMethod,
          status,
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    });

    return NextResponse.json({
      success: true,
      payment,
    });
  } catch (error) {
    console.error('Error creating payment:', error);
    return NextResponse.json(
      { error: 'Failed to create payment' },
      { status: 500 }
    );
  }
}