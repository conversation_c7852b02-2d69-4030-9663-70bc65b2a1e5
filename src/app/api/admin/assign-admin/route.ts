import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST(req: NextRequest) {
  try {
    // Only allow in development mode for security
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'This endpoint is only available in development mode' }, { status: 403 });
    }

    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = (session.user as any).id;

    // Find the admin role
    const adminRole = await prisma.role.findFirst({
      where: { name: 'admin' }
    });

    if (!adminRole) {
      return NextResponse.json({ error: 'Admin role not found in database' }, { status: 500 });
    }

    // Check if user already has admin role
    const existingAdminRole = await prisma.userRole.findFirst({
      where: {
        userId: userId,
        roleId: adminRole.id
      }
    });

    if (existingAdminRole) {
      return NextResponse.json({
        message: 'User already has admin role',
        userId,
        roleId: adminRole.id
      });
    }

    // Assign admin role to user
    const newUserRole = await prisma.userRole.create({
      data: {
        userId: userId,
        roleId: adminRole.id
      },
      include: {
        role: true
      }
    });

    // Log the admin role assignment
    await prisma.auditLog.create({
      data: {
        userId: userId,
        action: 'ADMIN_ASSIGN_ADMIN_ROLE',
        resource: 'UserRole',
        resourceId: newUserRole.id,
        success: true,
        oldValues: { userId, roleId: null, roleName: null },
        newValues: { 
          userId, 
          roleId: adminRole.id, 
          roleName: adminRole.name,
          assignedBy: userId,
          assignedAt: new Date().toISOString(),
        },
        ipAddress: req.headers.get('x-forwarded-for') || 'unknown',
        userAgent: req.headers.get('user-agent') || 'unknown',
      },
    });

    return NextResponse.json({
      message: 'Admin role assigned successfully',
      userId,
      role: {
        id: newUserRole.role.id,
        name: newUserRole.role.name,
        description: newUserRole.role.description
      }
    });

  } catch (error) {
    console.error('Error assigning admin role:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}