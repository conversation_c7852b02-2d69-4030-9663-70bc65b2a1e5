import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        {
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = user.userRoles.some(userRole =>
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Fetch roles that have access to remote servers
    const rolesWithServerAccess = await prisma.role.findMany({
      where: {
        role_remote_server_access: {
          some: {
            is_active: true
          }
        }
      },
      include: {
        role_remote_server_access: {
          where: { is_active: true },
          include: {
            remote_servers: {
              select: {
                id: true,
                name: true,
                url: true,
                isActive: true
              }
            }
          }
        },
        _count: {
          select: {
            userRoles: true,
            rolePermissions: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    // Transform roles for response
    const transformedRoles = rolesWithServerAccess.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description,
      isSystem: role.isSystem,
      userCount: role._count.userRoles,
      permissionCount: role._count.rolePermissions,
      createdAt: role.createdAt.toISOString(),
      updatedAt: role.updatedAt.toISOString(),
      // Add remote server access information
      remoteServerAccess: role.role_remote_server_access.map(access => ({
        remoteServerId: access.remote_servers.id,
        remoteServerName: access.remote_servers.name,
        remoteServerUrl: access.remote_servers.url,
        isActive: access.remote_servers.isActive,
        autoGrantPermissions: access.auto_grant_permissions,
        accessGrantedAt: access.created_at.toISOString()
      }))
    }));

    return NextResponse.json({ roles: transformedRoles });
  } catch (error) {
    console.error('Error fetching remote server roles:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}