import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has admin role
    const userRoles = await prisma.userRole.findMany({
      where: { userId: user.id },
      include: { role: true }
    });

    const isAdmin = userRoles.some(ur =>
      ur.role.name === 'admin' ||
      ur.role.name === 'ADMIN' ||
      ur.role.name === 'SUPER_ADMIN'
    );

    const isSuperAdmin = userRoles.some(ur => ur.role.name === 'SUPER_ADMIN');

    return NextResponse.json({
      isAdmin,
      isSuperAdmin,
      userId: user.id,
      roles: userRoles.map(ur => ur.role.name)
    });
  } catch (error) {
    console.error('Error checking admin permissions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}