import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({
        error: 'No session or user',
        session: null,
        user: null
      }, { status: 401 });
    }

    console.log('Session user ID:', (session.user as any).id);

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({
        error: 'User not found in database',
        userId: (session.user as any).id,
        user: null
      }, { status: 404 });
    }

    const isAdmin = user.userRoles.some(userRole =>
      userRole.role.name === 'admin'
    );

    return NextResponse.json({
      userId: user.id,
      userName: user.name,
      userEmail: user.email,
      userRoles: user.userRoles.map(ur => ({
        roleId: ur.role.id,
        roleName: ur.role.name,
        roleDescription: ur.role.description
      })),
      isAdmin,
      sessionUserId: (session.user as any).id
    });

  } catch (error) {
    console.error('Error checking admin permissions:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}