import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { Prisma, ApplicationStatus, PaymentStatus } from '@prisma/client';
import { broadcastNotification } from '@/lib/notifications';

// Helper function to check if user is admin
async function isAdmin(userId: string): Promise<boolean> {
  // For now, let's check if user has admin role or is in admin users list
  // This should be enhanced with proper role-based access control
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { userRoles: { include: { role: true } } },
  });

  if (!user) return false;

  // Check if user has admin role
  const hasAdminRole = user.userRoles.some(ur => 
    ur.role.name.toLowerCase().includes('admin')
  );

  return hasAdminRole;
}

// GET /api/admin/treaty-applications - Admin dashboard for all applications
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const adminCheck = await isAdmin((session.user as any).id);
    if (!adminCheck) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') as ApplicationStatus | null;
    const paymentStatus = searchParams.get('paymentStatus') as PaymentStatus | null;
    const userId = searchParams.get('userId') || null;
    const treatyTypeId = searchParams.get('treatyTypeId') || null;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (status) where.status = status;
    if (paymentStatus) where.paymentStatus = paymentStatus;
    if (userId) where.userId = userId;
    if (treatyTypeId) where.treatyTypeId = treatyTypeId;

    // Get applications with pagination
    const [applications, total] = await Promise.all([
      prisma.userTreatyType.findMany({
        where,
        include: {
          treatyType: {
            select: {
              id: true,
              name: true,
              description: true,
              price: true,
              requiresPayment: true,
              paymentDeadlineDays: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              profile: {
                select: {
                  peaceAmbassadorNumber: true,
                },
              },
            },
          },
          payments: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 1,
          },
        },
        orderBy: {
          appliedAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.userTreatyType.count({ where }),
    ]);

    // Transform data for frontend
    const transformedApplications = applications.map(app => ({
      id: app.id,
      userId: app.userId,
      treatyTypeId: app.treatyTypeId,
      status: app.status,
      paymentStatus: app.paymentStatus,
      appliedAt: app.appliedAt,
      approvedAt: app.approvedAt,
      rejectedAt: app.rejectedAt,
      approvedBy: app.approvedBy,
      rejectedBy: app.rejectedBy,
      rejectionReason: app.rejectionReason,
      notes: app.notes,
      treatyType: app.treatyType,
      user: {
        id: app.user.id,
        name: app.user.name,
        email: app.user.email,
        peaceAmbassadorNumber: app.user.profile?.peaceAmbassadorNumber,
      },
      latestPayment: app.payments[0] || null,
    }));

    return NextResponse.json({
      applications: transformedApplications,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching admin treaty applications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch treaty applications' },
      { status: 500 }
    );
  }
}

// POST /api/admin/treaty-applications - Create application (admin only, for special cases)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const adminCheck = await isAdmin((session.user as any).id);
    if (!adminCheck) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { userId, treatyTypeId, status, notes } = body;

    // Validate required fields
    if (!userId || !treatyTypeId) {
      return NextResponse.json(
        { error: 'User ID and Treaty Type ID are required' },
        { status: 400 }
      );
    }

    // Check if user has Peace Ambassador status
    const userProfile = await prisma.userProfile.findUnique({
      where: { userId },
    });

    if (!userProfile || !userProfile.peaceAmbassadorNumber) {
      return NextResponse.json(
        { error: 'User must have Peace Ambassador status to apply for treaties' },
        { status: 400 }
      );
    }

    // Check if treaty type exists
    const treatyType = await prisma.treatyType.findUnique({
      where: { id: treatyTypeId },
    });

    if (!treatyType) {
      return NextResponse.json(
        { error: 'Treaty type not found' },
        { status: 404 }
      );
    }

    // Check if user already has an active application for this treaty type
    const existingApplication = await prisma.userTreatyType.findFirst({
      where: {
        userId,
        treatyTypeId,
        status: {
          in: [ApplicationStatus.APPLIED, ApplicationStatus.UNDER_REVIEW, ApplicationStatus.APPROVED, ApplicationStatus.ACTIVE],
        },
      },
    });

    if (existingApplication) {
      return NextResponse.json(
        { error: 'User already has an active or pending application for this treaty type' },
        { status: 409 }
      );
    }

    // Create the application with specified status
    const application = await prisma.userTreatyType.create({
      data: {
        userId,
        treatyTypeId,
        status: status || ApplicationStatus.APPLIED,
        appliedAt: new Date(),
        approvedAt: status === ApplicationStatus.APPROVED ? new Date() : null,
        approvedBy: status === ApplicationStatus.APPROVED ? (session.user as any).id : null,
        notes: notes || null,
      },
      include: {
        treatyType: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Log the admin action
    await prisma.auditLog.create({
      data: {
        userId: (session.user as any).id,
        action: 'ADMIN_CREATE_TREATY_APPLICATION',
        resource: 'UserTreatyType',
        resourceId: application.id,
        success: true,
        oldValues: { id: null, userId: null, treatyTypeId: null },
        newValues: {
          userId,
          treatyTypeId,
          status: application.status,
          createdByAdmin: true,
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    });

    // Send notification for treaty application creation
    try {
      await prisma.notification.create({
        data: {
          type: 'TREATY_APPLICATION_CREATED',
          message: `New treaty application created for ${application.user.name} (${application.user.email}) - ${application.treatyType.name}`,
          recipients: ['admin'],
          trigger: 'treaty_application_creation',
          triggeredBy: (session.user as any).id,
          priority: 'NORMAL',
          category: 'TREATY',
          recipientTypes: {
            roles: ['admin'],
          },
          data: {
            applicationId: application.id,
            userId: application.userId,
            userEmail: application.user.email,
            userName: application.user.name,
            treatyTypeId: application.treatyTypeId,
            treatyTypeName: application.treatyType.name,
            status: application.status,
            createdBy: (session.user as any).id,
            creationDate: application.appliedAt?.toISOString(),
          },
        },
      });

      // Broadcast notification to connected admin clients
      await broadcastNotification({
        type: 'TREATY_APPLICATION_CREATED',
        message: `New treaty application: ${application.treatyType.name}`,
        recipients: ['admin'],
        priority: 'NORMAL',
        category: 'TREATY',
        data: {
          applicationId: application.id,
          userId: application.userId,
          userEmail: application.user.email,
          userName: application.user.name,
          treatyTypeName: application.treatyType.name,
          status: application.status,
        },
      });
    } catch (notificationError) {
      console.error('Failed to send treaty application creation notification:', notificationError);
      // Continue with the creation process even if notification fails
    }

    return NextResponse.json({
      success: true,
      application,
    });
  } catch (error) {
    console.error('Error creating treaty application:', error);
    return NextResponse.json(
      { error: 'Failed to create treaty application' },
      { status: 500 }
    );
  }
}