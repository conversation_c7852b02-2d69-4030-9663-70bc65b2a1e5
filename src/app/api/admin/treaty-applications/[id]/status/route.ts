import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { Prisma, ApplicationStatus, PaymentStatus } from '@prisma/client';
import { ApplicationStateMachine, WorkflowCoordinator, TransitionContext } from '@/lib/workflow/StateMachine';
import { broadcastNotification } from '@/lib/notifications';

// Helper function to check if user is admin
async function isAdmin(userId: string): Promise<boolean> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { userRoles: { include: { role: true } } },
  });

  if (!user) return false;

  const hasAdminRole = user.userRoles.some(ur => 
    ur.role.name.toLowerCase().includes('admin')
  );

  return hasAdminRole;
}

// PUT /api/admin/treaty-applications/[id]/status - Update application status (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const adminCheck = await isAdmin((session.user as any).id);
    if (!adminCheck) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { status, reviewComments, rejectionReason } = body;

    // Validate required fields
    if (!status || !Object.values(ApplicationStatus).includes(status as ApplicationStatus)) {
      return NextResponse.json(
        { error: 'Valid status is required' },
        { status: 400 }
      );
    }

    // Get existing application
    const existingApplication = await prisma.userTreatyType.findUnique({
      where: { id },
      include: { treatyType: true },
    });

    if (!existingApplication) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    // Use state machine to validate transition
    const context: TransitionContext = {
      userId: (session.user as any).id,
      notes: rejectionReason || reviewComments,
    };

    const workflowResult = await WorkflowCoordinator.handleApplicationWorkflow(
      id,
      existingApplication.status,
      existingApplication.paymentStatus,
      status,
      context,
      existingApplication.treatyType.requiresPayment
    );

    if (!workflowResult.applicationResult.success) {
      return NextResponse.json(
        { error: workflowResult.applicationResult.error },
        { status: 400 }
      );
    }

    let updateData: any = {
      status: workflowResult.applicationResult.newStatus,
    };

    // Add status-specific fields
    switch (workflowResult.applicationResult.newStatus) {
      case ApplicationStatus.UNDER_REVIEW:
        updateData.reviewedAt = new Date();
        break;
      case ApplicationStatus.APPROVED:
        updateData.approvedAt = new Date();
        updateData.approvedBy = (session.user as any).id;
        updateData.rejectionReason = null;
        
        // Handle payment status based on workflow result
        if (workflowResult.paymentResult) {
          updateData.paymentStatus = workflowResult.paymentResult.newStatus;
        } else if (workflowResult.applicationResult.autoActivate) {
          updateData.paymentStatus = PaymentStatus.NOT_REQUIRED;
          updateData.status = ApplicationStatus.ACTIVE; // Auto-activate for free treaties
        }
        break;
      case ApplicationStatus.REJECTED:
        updateData.rejectedAt = new Date();
        updateData.rejectedBy = (session.user as any).id;
        updateData.rejectionReason = rejectionReason || 'Rejected by admin';
        break;
    }

    // Update the application
    const updatedApplication = await prisma.userTreatyType.update({
      where: { id },
      data: updateData,
      include: {
        treatyType: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        payments: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
      },
    });

    // Log the admin action
    await prisma.auditLog.create({
      data: {
        userId: (session.user as any).id,
        action: 'ADMIN_UPDATE_APPLICATION_STATUS',
        resource: 'UserTreatyType',
        resourceId: id,
        success: true,
        oldValues: {
        success: true,
          status: existingApplication.status,
          paymentStatus: existingApplication.paymentStatus,
        },
        newValues: {
        success: true,
          status: updatedApplication.status,
          paymentStatus: updatedApplication.paymentStatus,
          rejectionReason: rejectionReason,
          reviewComments,
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    });

    // Create audit log entry for review comments if provided
    if (reviewComments) {
      await prisma.auditLog.create({
        data: {
          userId: (session.user as any).id,
          action: 'ADD_REVIEW_COMMENTS',
          resource: 'UserTreatyType',
          resourceId: id,
          success: true,
          oldValues: { reviewComments: null },
          newValues: { reviewComments },
          ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
        },
      });
    }

    // Send notification for treaty application status change
    try {
      let notificationType = 'TREATY_APPLICATION_UPDATED';
      let notificationMessage = '';
      let notificationPriority = 'NORMAL';

      switch (updatedApplication.status) {
        case ApplicationStatus.APPROVED:
          notificationType = 'TREATY_APPLICATION_APPROVED';
          notificationMessage = `Treaty application approved for ${updatedApplication.user.name} (${updatedApplication.user.email}) - ${updatedApplication.treatyType.name}`;
          notificationPriority = 'HIGH';
          break;
        case ApplicationStatus.REJECTED:
          notificationType = 'TREATY_APPLICATION_REJECTED';
          notificationMessage = `Treaty application rejected for ${updatedApplication.user.name} (${updatedApplication.user.email}) - ${updatedApplication.treatyType.name}`;
          notificationPriority = 'NORMAL';
          break;
        case ApplicationStatus.UNDER_REVIEW:
          notificationType = 'TREATY_APPLICATION_UNDER_REVIEW';
          notificationMessage = `Treaty application moved to review for ${updatedApplication.user.name} (${updatedApplication.user.email}) - ${updatedApplication.treatyType.name}`;
          notificationPriority = 'NORMAL';
          break;
        default:
          notificationMessage = `Treaty application status updated for ${updatedApplication.user.name} (${updatedApplication.user.email}) - ${updatedApplication.treatyType.name}`;
      }

      await prisma.notification.create({
        data: {
          type: notificationType,
          message: notificationMessage,
          recipients: ['admin'],
          trigger: 'treaty_application_status_change',
          triggeredBy: (session.user as any).id,
          priority: notificationPriority,
          category: 'TREATY',
          recipientTypes: {
            roles: ['admin'],
          },
          data: {
            applicationId: updatedApplication.id,
            userId: updatedApplication.userId,
            userEmail: updatedApplication.user.email,
            userName: updatedApplication.user.name,
            treatyTypeId: updatedApplication.treatyTypeId,
            treatyTypeName: updatedApplication.treatyType.name,
            oldStatus: existingApplication.status,
            newStatus: updatedApplication.status,
            paymentStatus: updatedApplication.paymentStatus,
            updatedBy: (session.user as any).id,
            updateDate: updatedApplication.approvedAt?.toISOString() || updatedApplication.rejectedAt?.toISOString() || new Date().toISOString(),
            rejectionReason: updatedApplication.rejectionReason,
            reviewComments: reviewComments,
          },
        },
      });

      // Broadcast notification to connected admin clients
      await broadcastNotification({
        type: notificationType,
        message: notificationMessage,
        recipients: ['admin'],
        priority: notificationPriority,
        category: 'TREATY',
        data: {
          applicationId: updatedApplication.id,
          userId: updatedApplication.userId,
          userEmail: updatedApplication.user.email,
          userName: updatedApplication.user.name,
          treatyTypeName: updatedApplication.treatyType.name,
          oldStatus: existingApplication.status,
          newStatus: updatedApplication.status,
        },
      });
    } catch (notificationError) {
      console.error('Failed to send treaty application status notification:', notificationError);
      // Continue with the update process even if notification fails
    }

    return NextResponse.json({
      success: true,
      application: updatedApplication,
    });
  } catch (error) {
    console.error('Error updating application status:', error);
    return NextResponse.json(
      { error: 'Failed to update application status' },
      { status: 500 }
    );
  }
}