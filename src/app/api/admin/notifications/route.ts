import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    const isAdmin = user?.userRoles.some(ur => ur.role.name === 'admin') || false;

    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const search = searchParams.get('search') || '';
    const priority = searchParams.get('priority') || '';
    const category = searchParams.get('category') || '';
    const readStatus = searchParams.get('readStatus') || '';
    const dateRange = searchParams.get('dateRange') || '';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    // Search functionality
    if (search) {
      where.OR = [
        { type: { contains: search, mode: 'insensitive' } },
        { message: { contains: search, mode: 'insensitive' } },
        { trigger: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Priority filter
    if (priority && priority !== 'ALL') {
      where.priority = priority;
    }

    // Category filter
    if (category && category !== 'ALL') {
      where.category = category;
    }

    // Read status filter
    if (readStatus && readStatus !== 'ALL') {
      if (readStatus === 'READ') {
        where.readBy = { isEmpty: false };
      } else if (readStatus === 'UNREAD') {
        where.readBy = { isEmpty: true };
      }
    }

    // Date range filter
    if (dateRange && dateRange !== 'ALL') {
      const now = new Date();
      let startDate: Date;

      switch (dateRange) {
        case 'TODAY':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'WEEK':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'MONTH':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(0);
      }

      where.createdAt = {
        gte: startDate,
      };
    }

    // Fetch notifications with pagination
    const [notifications, totalCount] = await Promise.all([
      prisma.notification.findMany({
        where,
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.notification.count({ where }),
    ]);

    // Calculate unread count (for current user)
    // Count notifications where the user is NOT in the readBy array
    const allNotifications = await prisma.notification.findMany({
      where,
      select: { id: true, readBy: true },
    });

    const unreadCount = allNotifications.filter(
      notification => !notification.readBy.includes(session.user.id)
    ).length;

    // Calculate statistics
    const stats = await prisma.notification.groupBy({
      by: ['priority', 'category'],
      where,
      _count: {
        id: true,
      },
    });

    const byPriority: Record<string, number> = {};
    const byCategory: Record<string, number> = {};

    stats.forEach(stat => {
      byPriority[stat.priority] = stat._count.id;
      byCategory[stat.category] = stat._count.id;
    });

    // Calculate recent notifications (last 24 hours)
    const recentCount = await prisma.notification.count({
      where: {
        ...where,
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
        },
      },
    });

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      notifications,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        pageSize: limit,
      },
      unreadCount,
      stats: {
        byPriority,
        byCategory,
        recent: recentCount,
      },
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}