import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { broadcastNotification } from '@/lib/notifications';
import { getNotificationTemplate, createNotificationFromTemplate, validateTemplateVariables } from '@/lib/notification-templates';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

// Notification priority enum
const NOTIFICATION_PRIORITIES = ['LOW', 'NORMAL', 'HIGH', 'URGENT'] as const;
type NotificationPriority = typeof NOTIFICATION_PRIORITIES[number];

// Notification category enum
const NOTIFICATION_CATEGORIES = ['USER_MANAGEMENT', 'TREATY', 'REMOTE_SERVER', 'SYSTEM', 'SECURITY'] as const;
type NotificationCategory = typeof NOTIFICATION_CATEGORIES[number];

interface CreateNotificationRequest {
  type: string;
  message: string;
  recipients: string[];
  priority?: NotificationPriority;
  category?: NotificationCategory;
  trigger?: string;
  data?: any;
  // Template support
  templateType?: string;
  templateVariables?: Record<string, string | number | boolean>;
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    const isAdmin = user?.userRoles.some(ur => ur.role.name === 'admin') || false;

    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body: CreateNotificationRequest = await req.json();

    // Handle template-based notification creation
    let notificationData: any = { ...body };

    if (body.templateType) {
      // Use template system
      const templateResult = createNotificationFromTemplate(
        body.templateType,
        body.templateVariables || {},
        {
          recipients: body.recipients,
          triggeredBy: session.user.id,
          data: body.data,
        }
      );

      if (!templateResult) {
        return NextResponse.json({
          error: `Invalid template type: ${body.templateType}`
        }, { status: 400 });
      }

      // Validate template variables
      const validation = validateTemplateVariables(body.templateType, body.templateVariables || {});
      if (!validation.valid) {
        return NextResponse.json({
          error: `Template validation failed. Missing variables: ${validation.missing.join(', ')}`,
          missingVariables: validation.missing,
          extraVariables: validation.extra,
        }, { status: 400 });
      }

      // Override the notification data with template data
      notificationData = {
        ...templateResult,
        data: body.data || templateResult.data, // Allow overriding template data
      };
    }

    // Validate required fields
    if (!notificationData.type || !notificationData.message || !notificationData.recipients) {
      return NextResponse.json({
        error: 'Missing required fields: type, message, and recipients are required'
      }, { status: 400 });
    }

    // Validate recipients array
    if (!Array.isArray(notificationData.recipients) || notificationData.recipients.length === 0) {
      return NextResponse.json({
        error: 'Recipients must be a non-empty array'
      }, { status: 400 });
    }

    // Validate priority
    const priority = notificationData.priority || 'NORMAL';
    if (!NOTIFICATION_PRIORITIES.includes(priority as NotificationPriority)) {
      return NextResponse.json({
        error: `Invalid priority. Must be one of: ${NOTIFICATION_PRIORITIES.join(', ')}`
      }, { status: 400 });
    }

    // Validate category
    const category = notificationData.category || 'SYSTEM';
    if (!NOTIFICATION_CATEGORIES.includes(category as NotificationCategory)) {
      return NextResponse.json({
        error: `Invalid category. Must be one of: ${NOTIFICATION_CATEGORIES.join(', ')}`
      }, { status: 400 });
    }

    // Determine recipient types
    const recipientTypes: any = {};
    const userRecipients: string[] = [];
    const roleRecipients: string[] = [];

    for (const recipient of body.recipients) {
      if (recipient === 'admin' || recipient === 'system') {
        roleRecipients.push(recipient);
      } else {
        userRecipients.push(recipient);
      }
    }

    if (userRecipients.length > 0) {
      recipientTypes.users = userRecipients;
    }
    if (roleRecipients.length > 0) {
      recipientTypes.roles = roleRecipients;
    }

    // Create the notification
    const notification = await prisma.notification.create({
      data: {
        type: notificationData.type,
        message: notificationData.message,
        recipients: notificationData.recipients,
        data: notificationData.data || null,
        trigger: notificationData.trigger || 'manual_creation',
        triggeredBy: session.user.id,
        priority,
        category,
        recipientTypes: Object.keys(recipientTypes).length > 0 ? recipientTypes : null,
      },
    });

    // Broadcast the notification to connected clients
    try {
      await broadcastNotification({
        id: notification.id,
        type: notification.type,
        message: notification.message,
        data: notification.data,
        recipients: notification.recipients,
        priority: notification.priority,
        category: notification.category,
        createdAt: notification.createdAt,
      });
    } catch (broadcastError) {
      console.error('Error broadcasting notification:', broadcastError);
      // Don't fail the request if broadcasting fails
    }

    // Create audit log entry
    try {
      await prisma.auditLog.create({
        data: {
          userId: session.user.id,
          action: 'NOTIFICATION_CREATED',
          resource: 'Notification',
          resourceId: notification.id,
          newValues: {
            type: notification.type,
            message: notification.message,
            recipients: notification.recipients,
            priority: notification.priority,
            category: notification.category,
          },
          success: true,
          apiEndpoint: '/api/admin/notifications/create',
          requestMethod: 'POST',
        },
      });
    } catch (auditError) {
      console.error('Error creating audit log:', auditError);
      // Don't fail the request if audit logging fails
    }

    return NextResponse.json({
      success: true,
      notification: {
        id: notification.id,
        type: notification.type,
        message: notification.message,
        recipients: notification.recipients,
        priority: notification.priority,
        category: notification.category,
        trigger: notification.trigger,
        triggeredBy: notification.triggeredBy,
        createdAt: notification.createdAt,
      },
    });

  } catch (error) {
    console.error('Error creating notification:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}