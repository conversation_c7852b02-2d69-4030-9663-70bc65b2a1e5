import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { Prisma } from '@prisma/client';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import {
  SECURITY_ALERT_CHANNEL_TYPES,
  SECURITY_ALERT_STATUSES,
  SECURITY_DASHBOARD_TIMEFRAMES,
  SECURITY_EVENT_SEVERITIES,
  SECURITY_EVENT_STATUSES,
  type SecurityAlertChannelType,
  type SecurityAlertStatus,
  type SecurityDashboardApiResponse,
  type SecurityDashboardTimeframe,
  type SecurityEventSeverity,
  type SecurityEventStatus,
} from '@/lib/types/security-dashboard.types';

const DEFAULT_LIMIT = 50;

const isRecord = (value: unknown): value is Record<string, unknown> =>
  typeof value === 'object' && value !== null && !Array.isArray(value);

const isSecurityEventSeverity = (value: unknown): value is SecurityEventSeverity =>
  typeof value === 'string' && SECURITY_EVENT_SEVERITIES.includes(value as SecurityEventSeverity);

const isSecurityEventStatus = (value: unknown): value is SecurityEventStatus =>
  typeof value === 'string' && SECURITY_EVENT_STATUSES.includes(value as SecurityEventStatus);

const isSecurityAlertStatus = (value: unknown): value is SecurityAlertStatus =>
  typeof value === 'string' && SECURITY_ALERT_STATUSES.includes(value as SecurityAlertStatus);

const isAlertChannelType = (value: unknown): value is SecurityAlertChannelType =>
  typeof value === 'string' && SECURITY_ALERT_CHANNEL_TYPES.includes(value as SecurityAlertChannelType);

const parseTimeframe = (value: string | null): SecurityDashboardTimeframe => {
  if (value && SECURITY_DASHBOARD_TIMEFRAMES.includes(value as SecurityDashboardTimeframe)) {
    return value as SecurityDashboardTimeframe;
  }

  return 'day';
};

const getTimeRange = (timeframe: SecurityDashboardTimeframe) => {
  const end = new Date();
  const start = new Date(end);

  switch (timeframe) {
    case 'hour':
      start.setHours(end.getHours() - 1);
      break;
    case 'day':
      start.setDate(end.getDate() - 1);
      break;
    case 'week':
      start.setDate(end.getDate() - 7);
      break;
    case 'month':
      start.setMonth(end.getMonth() - 1);
      break;
    default:
      start.setDate(end.getDate() - 1);
  }

  return { start, end };
};

const toRecord = (value: Prisma.JsonValue | null | undefined): Record<string, unknown> => {
  if (isRecord(value)) {
    return value;
  }

  return {};
};

const toChannels = (value: Prisma.JsonValue | null | undefined) => {
  if (!Array.isArray(value)) {
    return [] as Array<{ type: SecurityAlertChannelType; config: Record<string, unknown> }>;
  }

  return value.reduce<Array<{ type: SecurityAlertChannelType; config: Record<string, unknown> }>>((acc, entry) => {
    if (!isRecord(entry)) {
      return acc;
    }

    const rawType = entry.type;
    const channelType = isAlertChannelType(rawType) ? rawType : SECURITY_ALERT_CHANNEL_TYPES[0];
    const config = isRecord(entry.config) ? (entry.config as Record<string, unknown>) : {};

    acc.push({
      type: channelType,
      config,
    });

    return acc;
  }, []);
};

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({ where: { email: session.user.email } });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const userRoles = await prisma.userRole.findMany({
      where: { userId: user.id },
      include: { role: true },
    });

    const isAdmin = userRoles.some((role) => ['admin', 'ADMIN', 'SUPER_ADMIN'].includes(role.role.name));

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const timeframe = parseTimeframe(request.nextUrl.searchParams.get('timeframe'));
    const severityFilter = request.nextUrl.searchParams.get('severity');
    const typeFilter = request.nextUrl.searchParams.get('type');

    const severity = isSecurityEventSeverity(severityFilter) ? severityFilter : undefined;
    const eventType = typeFilter && typeFilter !== 'all' ? typeFilter : undefined;

    const { start, end } = getTimeRange(timeframe);

    const baseEventWhere: Prisma.SecurityEventWhereInput = {
      detectedAt: { gte: start, lte: end },
    };

    if (severity) {
      baseEventWhere.severity = severity;
    }

    if (eventType) {
      baseEventWhere.type = eventType as Prisma.SecurityEventType;
    }

    const [
      events,
      alerts,
      totalEvents,
      unresolvedEvents,
      eventsByType,
      eventsBySeverity,
      totalAlerts,
      failedAlerts,
    ] = await Promise.all([
      prisma.securityEvent.findMany({
        where: baseEventWhere,
        orderBy: { detectedAt: 'desc' },
        take: DEFAULT_LIMIT,
      }),
      prisma.securityAlert.findMany({
        where: { sentAt: { gte: start, lte: end } },
        orderBy: { sentAt: 'desc' },
        take: DEFAULT_LIMIT,
      }),
      prisma.securityEvent.count({ where: baseEventWhere }),
      prisma.securityEvent.count({
        where: {
          ...baseEventWhere,
          status: { in: ['OPEN', 'INVESTIGATING'] },
        },
      }),
      prisma.securityEvent.groupBy({
        by: ['type'],
        where: baseEventWhere,
        _count: { id: true },
      }),
      prisma.securityEvent.groupBy({
        by: ['severity'],
        where: baseEventWhere,
        _count: { id: true },
      }),
      prisma.securityAlert.count({ where: { sentAt: { gte: start, lte: end } } }),
      prisma.securityAlert.count({ where: { sentAt: { gte: start, lte: end }, status: 'FAILED' } }),
    ]);

    const eventPayload = events.map((event) => ({
      id: event.id,
      type: event.type,
      severity: isSecurityEventSeverity(event.severity) ? event.severity : 'LOW',
      description: event.description,
      source: event.source,
      userId: event.userId,
      serverId: event.serverId,
      ipAddress: event.ipAddress,
      userAgent: event.userAgent,
      metadata: toRecord(event.metadata),
      detectedAt: event.detectedAt.toISOString(),
      resolvedAt: event.resolvedAt ? event.resolvedAt.toISOString() : null,
      status: isSecurityEventStatus(event.status) ? event.status : 'OPEN',
      assignedTo: event.assignedTo,
      resolution: event.resolution,
    }));

    const alertPayload = alerts.map((alert) => ({
      id: alert.id,
      eventId: alert.eventId,
      ruleId: alert.ruleId,
      channels: toChannels(alert.channels),
      sentAt: alert.sentAt.toISOString(),
      status: isSecurityAlertStatus(alert.status) ? alert.status : 'PENDING',
      errorMessage: alert.errorMessage,
      metadata: toRecord(alert.metadata),
    }));

    const responsePayload: SecurityDashboardApiResponse = {
      events: eventPayload,
      alerts: alertPayload,
      stats: {
        totalEvents,
        unresolvedEvents,
        totalAlerts,
        failedAlerts,
        timeframe,
        eventsByType: eventsByType.map((item) => ({
          type: item.type,
          count: item._count.id,
        })),
        eventsBySeverity: eventsBySeverity.map((item) => ({
          severity: isSecurityEventSeverity(item.severity) ? item.severity : 'LOW',
          count: item._count.id,
        })),
      },
    };

    return NextResponse.json(responsePayload);
  } catch (error) {
    console.error('Failed to load security dashboard data', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
