import { NextRequest, NextResponse } from 'next/server';
import { createAuth<PERSON>hain } from '@/lib/middleware';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Zod schema for request validation
const scopeSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  category: z.string().max(50).optional(),
});

// Create authentication middleware chain for admin access
const authMiddleware = createAuthChain({
  requireApiKey: true,
  requireJwt: true,
  requireCors: true,
  requiredScopes: ['admin:*', 'admin:scopes'],
  rateLimitConfig: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 50, // 50 requests per minute
  },
});

export async function GET(request: NextRequest) {
  try {
    // Run authentication middleware
    const authResponse = await authMiddleware(request);
    if (authResponse) {
      return authResponse;
    }

    // Parse query parameters
    const url = new URL(request.url);
    const category = url.searchParams.get('category');
    const active = url.searchParams.get('active');

    // Build where clause
    const where: any = {};
    if (category) {
      where.category = category;
    }
    if (active === 'true') {
      where.isActive = true;
    } else if (active === 'false') {
      where.isActive = false;
    }

    // Fetch scopes
    const scopes = await prisma.scope.findMany({
      where,
      select: {
        id: true,
        name: true,
        description: true,
        category: true,
        isActive: true,
        createdAt: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    // Return scopes
    return NextResponse.json({ scopes });
  } catch (error) {
    console.error('Error fetching scopes:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Run authentication middleware
    const authResponse = await authMiddleware(request);
    if (authResponse) {
      return authResponse;
    }

    // Get session for audit logging
    const session = await getServerSession(authOptions);
    const userId = session ? (session.user as any).id : 'unknown';

    // Parse and validate request body
    const body = await request.json();
    const parsed = scopeSchema.safeParse(body);
    
    if (!parsed.success) {
      return NextResponse.json(
        { 
          error: 'Bad Request', 
          message: 'Invalid request body',
          details: parsed.error.flatten()
        },
        { status: 400 }
      );
    }

    const { name, description, category } = parsed.data;

    // Check if scope already exists
    const existingScope = await prisma.scope.findUnique({
      where: { name },
    });

    if (existingScope) {
      return NextResponse.json(
        { error: 'Conflict', message: 'Scope with this name already exists' },
        { status: 409 }
      );
    }

    // Create new scope
    const scope = await prisma.scope.create({
      data: {
        name,
        description,
        category: category || 'general',
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        description: true,
        category: true,
        isActive: true,
        createdAt: true,
      },
    });

    // Log the scope creation
    await prisma.auditLog.create({
      data: {
        userId: userId,
        action: 'ADMIN_CREATE_SCOPE',
        resource: 'Scope',
        resourceId: scope.id,
        success: true,
        oldValues: { id: null, name: null, category: null },
        newValues: {
          id: scope.id,
          name: scope.name,
          description: scope.description,
          category: scope.category,
          isActive: scope.isActive,
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    });

    // Return created scope
    return NextResponse.json({ scope });
  } catch (error) {
    console.error('Error creating scope:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}