import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string; projectId: string }> }) {
  return NextResponse.json({ message: 'Mock GET response' });
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string; projectId: string }> }) {
  return NextResponse.json({ message: 'Mock POST response' });
}