import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';
import { userAuditLogger } from '@/lib/user-audit-logger';

// Validation schema for updating user roles
const updateUserRolesSchema = z.object({
  roleIds: z.array(z.string()).min(1, 'At least one role is required'),
});

export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const currentUser = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = currentUser.userRoles.some(userRole => 
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id: userId } = await params;

    // Fetch specific user
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: {
          include: {
            country: true,
            city: true,
          },
        },
        userRoles: {
          include: {
            role: true,
          },
        },
        userPositions: {
          include: {
            position: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Transform user for response
    const userProfile = user.profile;
    const userCountry = userProfile?.country;
    const userCity = userProfile?.city;
    
    const transformedUser = {
      id: user.id,
      name: user.name,
      email: user.email,
      nwaEmail: userProfile?.nwaEmail,
      firstName: userProfile?.firstName,
      lastName: userProfile?.lastName,
      bio: userProfile?.bio,
      phone: userProfile?.mobile,
      dob: userProfile?.dateOfBirth,
      peaceAmbassadorNumber: userProfile?.peaceAmbassadorNumber,
      titleId: userProfile?.titleId,
      position: user.userPositions.length > 0 ? user.userPositions[0].position : null,
      country: userCountry ? {
        id: userCountry.id,
        name: userCountry.name,
      } : null,
      city: userCity ? {
        id: userCity.id,
        name: userCity.name,
      } : null,
      roles: user.userRoles.map(userRole => ({
        id: userRole.role.id,
        name: userRole.role.name,
        description: userRole.role.description,
      })),
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
    };

    return NextResponse.json({ user: transformedUser });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await getServerSession(authOptions);
    console.log('Session data:', session);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const currentUser = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = currentUser.userRoles.some(userRole => 
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id: userId } = await params;
    console.log('PUT request received for userId:', userId);

    // Validate user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Prevent modifying admin user (self-protection)
    // if (userId === (session.user as any).id) {
    //   return NextResponse.json({ error: 'Cannot modify your own roles' }, { status: 400 });
    // }

    const body = await req.json();
    const validation = updateUserRolesSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Bad Request', message: 'Invalid request body', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { roleIds } = validation.data;

    // Verify all roles exist
    const roles = await prisma.role.findMany({
      where: {
        id: {
          in: roleIds,
        },
      },
    });

    if (roles.length !== roleIds.length) {
      return NextResponse.json({ error: 'One or more roles not found' }, { status: 400 });
    }

    // Update user roles
    // First, get current roles for audit logging
    const currentUserRoles = await prisma.userRole.findMany({
      where: {
        userId: userId,
      },
      include: {
        role: true,
      },
    });

    // Delete existing roles
    await prisma.userRole.deleteMany({
      where: {
        userId: userId,
      },
    });

    // Create new roles
    await Promise.all(
      roleIds.map(roleId =>
        prisma.userRole.create({
          data: {
            userId: userId,
            roleId: roleId,
          },
        })
      )
    );

    // Fetch updated user with roles, profile, and related data
    const updatedUser = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: {
          include: {
            country: true,
            city: true,
          },
        },
        userRoles: {
          include: {
            role: true,
          },
        },
        userPositions: {
          include: {
            position: true,
          },
        },
      },
    });

    // Log user role update for audit purposes
    try {
      const previousRoles = currentUserRoles.map(ur => ({ id: ur.role.id, name: ur.role.name }));
      const newRoles = updatedUser!.userRoles.map(ur => ({ id: ur.role.id, name: ur.role.name }));
      
      await userAuditLogger.logUserUpdate(
        userId,
        {
          roles: previousRoles,
        },
        {
          roles: newRoles,
        },
        (session.user as any).id, // Admin user making the change
        {
          action: 'update_user_roles',
          ipAddress: userAuditLogger.getClientIP(req.headers),
          userAgent: userAuditLogger.getUserAgent(req.headers),
        }
      );
    } catch (auditError) {
      console.error('Failed to log user role update audit:', auditError);
      // Continue with the update process even if audit logging fails
    }

    // Transform user for response
    const userProfile = updatedUser!.profile;
    const userCountry = userProfile?.country;
    const userCity = userProfile?.city;
    
    const transformedUser = {
      id: updatedUser!.id,
      name: updatedUser!.name,
      email: updatedUser!.email,
      nwaEmail: userProfile?.nwaEmail,
      firstName: userProfile?.firstName,
      lastName: userProfile?.lastName,
      bio: userProfile?.bio,
      phone: userProfile?.mobile,
      dob: userProfile?.dateOfBirth,
      peaceAmbassadorNumber: userProfile?.peaceAmbassadorNumber,
      titleId: userProfile?.titleId,
      position: updatedUser!.userPositions.length > 0 ? updatedUser!.userPositions[0].position : null, // Get the first position or null
      country: userCountry ? {
        id: userCountry.id,
        name: userCountry.name,
      } : null, // Include country details or null
      city: userCity ? {
        id: userCity.id,
        name: userCity.name,
      } : null, // Include city details or null
      roles: updatedUser!.userRoles.map(userRole => ({
        id: userRole.role.id,
        name: userRole.role.name,
        description: userRole.role.description,
      })),
      createdAt: updatedUser!.createdAt.toISOString(),
      updatedAt: updatedUser!.updatedAt.toISOString(),
    };

    return NextResponse.json({ 
      message: 'User roles updated successfully',
      user: transformedUser,
    });
  } catch (error) {
    console.error('Error updating user roles:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}