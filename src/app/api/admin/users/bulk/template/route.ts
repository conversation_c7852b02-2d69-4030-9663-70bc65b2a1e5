import { NextRequest, NextResponse } from 'next/server';
import { with<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/middleware/require-auth';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';

/**
 * CSV Template columns based on our User and UserProfile schema
 * Core fields for bulk user creation
 */
const CSV_TEMPLATE_HEADERS = [
  // Basic User Information
  'name',
  'email',
  'firstName',
  'lastName',
  'personalEmail',
  'nwaEmail',
  'phone',
  'mobile',
  'bio',
  'dateOfBirth', // Format: YYYY-MM-DD
  
  // Address Information
  'streetAddress1',
  'streetAddress2',
  'town',
  'cityName', // City name (will be matched to existing cities)
  'regionName', // Region/state name (will be matched)
  'countryCode', // 2-3 letter country code (will be matched)
  'postalCode',
  
  // Identification Information
  'peaceAmbassadorNumber',
  'identificationTypes', // Comma-separated list of identification types
  'identificationNumbers', // Comma-separated list corresponding to types
  
  // Position and Role Information
  'roles', // Comma-separated role names (admin, user, etc.)
  'titleName', // Diplomatic title name
  'positionNames', // Comma-separated position names
  
  // Treaty Information
  'treatyTypeNames', // Comma-separated treaty type names
  'treatyNumbers', // Comma-separated treaty numbers corresponding to types
  
  // Account Settings
  'twoFactorEnabled', // true/false
  'isActive', // true/false
  
  // Notes
  'notes' // General notes about the user
];

/**
 * Example data to help users understand the expected format
 */
const EXAMPLE_ROWS = [
  {
    name: 'John Doe',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    personalEmail: '<EMAIL>',
    nwaEmail: '<EMAIL>',
    phone: '******-123-4567',
    mobile: '******-987-6543',
    bio: 'Experienced diplomat and peace ambassador',
    dateOfBirth: '1985-03-15',
    streetAddress1: '123 Peace Avenue',
    streetAddress2: 'Suite 456',
    town: 'Harmony',
    cityName: 'New York',
    regionName: 'New York',
    countryCode: 'USA',
    postalCode: '10001',
    peaceAmbassadorNumber: 'PA-2024-001',
    identificationTypes: 'passport,driver_license',
    identificationNumbers: 'P123456789,DL987654321',
    roles: 'user,ambassador',
    titleName: 'Peace Ambassador',
    positionNames: 'Regional Coordinator,Treaty Negotiator',
    treatyTypeNames: 'Trade Agreement,Peace Treaty',
    treatyNumbers: 'TA-2024-001,PT-2024-001',
    twoFactorEnabled: 'true',
    isActive: 'true',
    notes: 'Bulk upload example - please customize for your users'
  }
];

/**
 * GET /api/admin/users/bulk/template
 * Downloads a CSV template file for bulk user uploads
 */
export const GET = withAuthHandler(
  async (request: NextRequest, auth) => {
    try {
      // Generate CSV content
      const csvContent = generateCSVTemplate();
      
      // Log the template download
      await 
auditLoggingMiddleware.logApiAccess(
        auth.userId,
        'download_template',
        'bulk_upload',
        true,
        request,
        {
          statusCode: 200,
          metadata: {
            templateType: 'user_bulk_upload',
            columnCount: CSV_TEMPLATE_HEADERS.length,
            hasExampleData: true
          }
        }
      );

      // Return CSV file
      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv; charset=utf-8',
          'Content-Disposition': 'attachment; filename="user_bulk_upload_template.csv"',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
    } catch (error) {
      console.error('Error generating CSV template:', error);
      
      // Log the error
      await 
auditLoggingMiddleware.logApiAccess(
        auth.userId,
        'download_template',
        'bulk_upload',
        false,
        request,
        {
          statusCode: 500,
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
          metadata: {
            templateType: 'user_bulk_upload'
          }
        }
      );

      return NextResponse.json(
        { 
          error: 'Internal Server Error', 
          message: 'Failed to generate CSV template' 
        },
        { status: 500 }
      );
    }
  },
  {
    requireAdmin: true,
    customErrorMessage: 'Admin privileges required to download bulk upload templates'
  }
);

/**
 * Generates the CSV template content with headers and example data
 */
function generateCSVTemplate(): string {
  const csvRows: string[] = [];
  
  // Add headers
  csvRows.push(CSV_TEMPLATE_HEADERS.map(escapeCSVField).join(','));
  
  // Add example rows
  EXAMPLE_ROWS.forEach(row => {
    const csvRow = CSV_TEMPLATE_HEADERS.map(header => {
      const value = (row as any)[header] || '';
      return escapeCSVField(String(value));
    });
    csvRows.push(csvRow.join(','));
  });
  
  // Add UTF-8 BOM for Excel compatibility
  return '\ufeff' + csvRows.join('\n');
}

/**
 * Escapes CSV field values to handle commas, quotes, and newlines
 */
function escapeCSVField(field: string): string {
  if (field.includes(',') || field.includes('"') || field.includes('\n') || field.includes('\r')) {
    // Escape quotes by doubling them and wrap field in quotes
    return `"${field.replace(/"/g, '""')}"`;
  }
  return field;
}

/**
 * POST endpoint is not supported for template download
 */
export async function POST() {
  return NextResponse.json(
    { error: 'Method Not Allowed', message: 'Use GET to download template' },
    { status: 405 }
  );
}

/**
 * PUT endpoint is not supported for template download
 */
export async function PUT() {
  return NextResponse.json(
    { error: 'Method Not Allowed', message: 'Use GET to download template' },
    { status: 405 }
  );
}

/**
 * DELETE endpoint is not supported for template download
 */
export async function DELETE() {
  return NextResponse.json(
    { error: 'Method Not Allowed', message: 'Use GET to download template' },
    { status: 405 }
  );
}
