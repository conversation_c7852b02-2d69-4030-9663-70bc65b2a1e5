import { NextRequest, NextResponse } from 'next/server';
import { with<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/middleware/require-auth';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';
import { prisma } from '@/lib/prisma';
import { 
  analyzeCSVHeaders, 
  createFieldMapping, 
  validateAndTransformRow,
  validateCustomMappings,
  estimateProcessingTime,
  type CustomFieldMapping,
  type HeaderAnalysisResult 
} from '@/lib/utils/csv-mapping';
import * as bcrypt from 'bcryptjs';

/**
 * Maximum file size (10MB)
 */
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

/**
 * Maximum number of rows per upload
 */
const MAX_ROWS = 1000;

/**
 * Supported file types
 */
const SUPPORTED_MIME_TYPES = [
  'text/csv',
  'application/csv',
  'text/plain',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
];

/**
 * POST /api/admin/users/bulk/upload
 * Handles bulk user upload via CSV/XLSX files
 */
export const POST = withAuthHandler(
  async (request: NextRequest, auth) => {
    try {
      const formData = await request.formData();
      const file = formData.get('file') as File;
      const mode = formData.get('mode') as string || 'smart'; // strict, smart, custom
      const customMappingsJson = formData.get('customMappings') as string;
      const validateOnly = formData.get('validateOnly') === 'true';

      // Validate file presence
      if (!file) {
        return NextResponse.json(
          { error: 'Bad Request', message: 'No file uploaded' },
          { status: 400 }
        );
      }

      // Validate file size
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { 
            error: 'Bad Request', 
            message: `File size exceeds maximum limit of ${MAX_FILE_SIZE / (1024 * 1024)}MB` 
          },
          { status: 400 }
        );
      }

      // Validate file type
      if (!SUPPORTED_MIME_TYPES.includes(file.type)) {
        return NextResponse.json(
          {
            error: 'Bad Request',
            message: 'Unsupported file type. Please upload a CSV or Excel file'
          },
          { status: 400 }
        );
      }

      // Parse custom mappings if provided
      let customMappings: CustomFieldMapping[] | undefined;
      if (customMappingsJson) {
        try {
          customMappings = JSON.parse(customMappingsJson);
          const validationResult = validateCustomMappings(customMappings || []);
          if (!validationResult.valid) {
            return NextResponse.json(
              {
                error: 'Bad Request',
                message: 'Invalid custom mappings',
                details: validationResult.errors
              },
              { status: 400 }
            );
          }
        } catch (error) {
          return NextResponse.json(
            {
              error: 'Bad Request',
              message: 'Invalid custom mappings JSON format'
            },
            { status: 400 }
          );
        }
      }

      // Read and parse CSV content
      const csvContent = await file.text();
      const { headers, rows } = parseCSV(csvContent);

      // Validate row count
      if (rows.length > MAX_ROWS) {
        return NextResponse.json(
          {
            error: 'Bad Request',
            message: `File contains too many rows. Maximum ${MAX_ROWS} rows allowed, found ${rows.length}`
          },
          { status: 400 }
        );
      }

      // Analyze headers and create mappings
      let headerAnalysis: HeaderAnalysisResult;
      let fieldMapping: Record<string, string>;

      if (mode === 'custom' && customMappings) {
        fieldMapping = createFieldMapping(headers, customMappings);
        headerAnalysis = {
          exactMatches: [],
          suggestedMappings: [],
          unmappedHeaders: [],
          missingRequired: [],
          validationPassed: true,
          mode: 'custom'
        };
      } else {
        headerAnalysis = analyzeCSVHeaders(headers, mode as any);
        fieldMapping = createFieldMapping(headers);
      }

      if (!headerAnalysis.validationPassed) {
        return NextResponse.json(
          {
            error: 'Bad Request',
            message: 'CSV header validation failed',
            headerAnalysis,
            suggestions: headerAnalysis.suggestedMappings
          },
          { status: 400 }
        );
      }

      // Process rows and validate data
      const processingResults = {
        totalRows: rows.length,
        validRows: 0,
        invalidRows: 0,
        errors: [] as string[],
        warnings: [] as string[],
        processedUsers: [] as any[],
        duplicateEmails: [] as string[],
        skippedRows: [] as number[]
      };

      const emailsInFile = new Set<string>();
      const transformedRows: any[] = [];

      // First pass: validate all rows
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const validation = await validateAndTransformRow(row, fieldMapping, i);
        
        if (validation.valid) {
          // Check for duplicate emails within the file
          const email = validation.transformedRow.email;
          if (emailsInFile.has(email)) {
            processingResults.duplicateEmails.push(email);
            processingResults.invalidRows++;
            processingResults.errors.push(`Row ${i + 1}: Duplicate email '${email}' found in file`);
            continue;
          }
          emailsInFile.add(email);

          // Check for existing users in database
          const existingUser = await prisma.user.findUnique({
            where: { email },
            select: { id: true, email: true, name: true }
          });

          if (existingUser) {
            processingResults.warnings.push(
              `Row ${i + 1}: User with email '${email}' already exists (ID: ${existingUser.id})`
            );
            processingResults.skippedRows.push(i + 1);
            continue;
          }

          transformedRows.push({
            rowIndex: i,
            data: validation.transformedRow
          });
          processingResults.validRows++;
        } else {
          processingResults.invalidRows++;
          processingResults.errors.push(...validation.errors);
        }

        processingResults.warnings.push(...validation.warnings);
      }

      // If validation only, return results without creating users
      if (validateOnly) {
        const timeEstimate = estimateProcessingTime(processingResults.validRows);
        
        await 
auditLoggingMiddleware.logApiAccess(
          
          auth.userId,
          'validate_bulk_upload',
          'bulk_upload',
          true,
          request,
          {
            statusCode: 200,
            metadata: {
              fileName: file.name,
              fileSize: file.size,
              totalRows: processingResults.totalRows,
              validRows: processingResults.validRows,
              invalidRows: processingResults.invalidRows,
              mode,
              validateOnly: true
            }
          }
        );

        return NextResponse.json({
          validation: {
            passed: processingResults.invalidRows === 0,
            totalRows: processingResults.totalRows,
            validRows: processingResults.validRows,
            invalidRows: processingResults.invalidRows,
            errors: processingResults.errors,
            warnings: processingResults.warnings,
            duplicateEmails: processingResults.duplicateEmails,
            skippedRows: processingResults.skippedRows
          },
          headerAnalysis,
          fieldMapping,
          estimatedProcessingTime: timeEstimate,
          message: 'Validation completed. Review results and submit again to process users.'
        });
      }

      // Second pass: create users (if not validation only)
      if (processingResults.validRows === 0) {
        return NextResponse.json(
          {
            error: 'Bad Request',
            message: 'No valid users to create',
            validation: {
              totalRows: processingResults.totalRows,
              validRows: processingResults.validRows,
              invalidRows: processingResults.invalidRows,
              errors: processingResults.errors,
              warnings: processingResults.warnings
            }
          },
          { status: 400 }
        );
      }

      // Process users in transaction
      const createdUsers = await prisma.$transaction(async (tx) => {
        const users = [];

        for (const { rowIndex, data } of transformedRows) {
          try {
            const user = await createUserFromRowData(tx, data, auth.userId);
            users.push({
              rowIndex: rowIndex + 1,
              user: {
                id: user.id,
                email: user.email,
                name: user.name
              }
            });
            processingResults.processedUsers.push(user);
          } catch (error) {
            processingResults.errors.push(
              `Row ${rowIndex + 1}: Failed to create user - ${error instanceof Error ? error.message : 'Unknown error'}`
            );
            processingResults.validRows--;
            processingResults.invalidRows++;
          }
        }

        return users;
      });

      // Log successful bulk upload
      await 
auditLoggingMiddleware.logApiAccess(
        
        auth.userId,
        'bulk_upload_users',
        'bulk_upload',
        true,
        request,
        {
          statusCode: 200,
          metadata: {
            fileName: file.name,
            fileSize: file.size,
            totalRows: processingResults.totalRows,
            processedUsers: createdUsers.length,
            failedUsers: processingResults.invalidRows,
            mode
          }
        }
      );

      return NextResponse.json({
        success: true,
        message: `Successfully processed ${createdUsers.length} users`,
        results: {
          totalRows: processingResults.totalRows,
          processedUsers: createdUsers.length,
          failedUsers: processingResults.invalidRows,
          errors: processingResults.errors,
          warnings: processingResults.warnings,
          createdUsers
        }
      });

    } catch (error) {
      console.error('Bulk upload error:', error);
      
      await 
auditLoggingMiddleware.logApiAccess(
        
        auth.userId,
        'bulk_upload_users',
        'bulk_upload',
        false,
        request,
        {
          statusCode: 500,
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        }
      );

      return NextResponse.json(
        { 
          error: 'Internal Server Error', 
          message: 'Failed to process bulk upload' 
        },
        { status: 500 }
      );
    }
  },
  {
    requireAdmin: true,
    customErrorMessage: 'Admin privileges required for bulk user uploads'
  }
);

/**
 * Parses CSV content and returns headers and rows
 */
function parseCSV(csvContent: string): { headers: string[]; rows: Record<string, string>[] } {
  const lines = csvContent.split('\n').map(line => line.trim()).filter(line => line);
  
  if (lines.length === 0) {
    throw new Error('CSV file is empty');
  }

  const headers = parseCSVLine(lines[0]);
  const rows: Record<string, string>[] = [];

  for (let i = 1; i < lines.length; i++) {
    const values = parseCSVLine(lines[i]);
    if (values.length === 0) continue; // Skip empty lines
    
    const row: Record<string, string> = {};
    headers.forEach((header, index) => {
      row[header] = values[index] || '';
    });
    rows.push(row);
  }

  return { headers, rows };
}

/**
 * Parses a single CSV line, handling quoted values and commas
 */
function parseCSVLine(line: string): string[] {
  const result: string[] = [];
  let current = '';
  let inQuotes = false;
  let i = 0;

  while (i < line.length) {
    const char = line[i];

    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        // Escaped quote
        current += '"';
        i += 2;
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
        i++;
      }
    } else if (char === ',' && !inQuotes) {
      // End of field
      result.push(current.trim());
      current = '';
      i++;
    } else {
      current += char;
      i++;
    }
  }

  // Add the last field
  result.push(current.trim());

  return result;
}

/**
 * Creates a user from transformed row data
 */
async function createUserFromRowData(
  tx: any, // Prisma transaction client
  data: Record<string, any>,
  createdByUserId: string
): Promise<any> {
  // Generate default password for bulk created users
  const defaultPassword = generateSecurePassword();
  const passwordHash = await bcrypt.hash(defaultPassword, 12);

  // Create the user
  const user = await tx.user.create({
    data: {
      name: data.name || `${data.firstName || ''} ${data.lastName || ''}`.trim() || 'Bulk Import User',
      email: data.email,
      passwordHash,
      twoFactorEnabled: data.twoFactorEnabled || false,
    }
  });

  // Create user profile if we have profile data
  const profileData: any = {};
  if (data.firstName) profileData.firstName = data.firstName;
  if (data.lastName) profileData.lastName = data.lastName;
  if (data.personalEmail) profileData.personalEmail = data.personalEmail;
  if (data.nwaEmail) profileData.nwaEmail = data.nwaEmail;
  if (data.phone) profileData.phone = data.phone;
  if (data.mobile) profileData.mobile = data.mobile;
  if (data.bio) profileData.bio = data.bio;
  if (data.dateOfBirth) profileData.dateOfBirth = data.dateOfBirth;
  if (data.streetAddress1) profileData.streetAddress1 = data.streetAddress1;
  if (data.streetAddress2) profileData.streetAddress2 = data.streetAddress2;
  if (data.town) profileData.town = data.town;
  if (data.postalCode) profileData.postalCode = data.postalCode;
  if (data.peaceAmbassadorNumber) profileData.peaceAmbassadorNumber = data.peaceAmbassadorNumber;

  // Handle country/city/region lookups
  if (data.countryCode) {
    const country = await tx.country.findUnique({
      where: { code: data.countryCode }
    });
    if (country) {
      profileData.countryId = country.id;

      // Look up city if provided
      if (data.cityName) {
        const city = await tx.city.findFirst({
          where: {
            name: { equals: data.cityName, mode: 'insensitive' },
            countryId: country.id
          }
        });
        if (city) {
          profileData.cityId = city.id;
        }
      }

      // Look up region if provided
      if (data.regionName) {
        const region = await tx.region.findFirst({
          where: {
            name: { equals: data.regionName, mode: 'insensitive' },
            countryId: country.id
          }
        });
        if (region) {
          profileData.regionId = region.id;
        }
      }
    }
  }

  // Create profile if we have data
  if (Object.keys(profileData).length > 0) {
    await tx.userProfile.create({
      data: {
        userId: user.id,
        ...profileData
      }
    });
  }

  // Handle roles
  if (data.roles && Array.isArray(data.roles)) {
    for (const roleName of data.roles) {
      const role = await tx.role.findUnique({
        where: { name: roleName.toLowerCase() }
      });
      if (role) {
        await tx.userRole.create({
          data: {
            userId: user.id,
            roleId: role.id,
            assignedBy: createdByUserId
          }
        });
      }
    }
  }

  // Handle identification documents
  if (data.identificationTypes && data.identificationNumbers) {
    const types = Array.isArray(data.identificationTypes) ? data.identificationTypes : [];
    const numbers = Array.isArray(data.identificationNumbers) ? data.identificationNumbers : [];
    
    for (let i = 0; i < Math.min(types.length, numbers.length); i++) {
      const typeName = types[i];
      const idNumber = numbers[i];
      
      const idType = await tx.identificationType.findFirst({
        where: { name: { equals: typeName, mode: 'insensitive' } }
      });
      
      if (idType) {
        await tx.userIdentification.create({
          data: {
            userId: user.id,
            identificationTypeId: idType.id,
            idNumber
          }
        });
      }
    }
  }

  // Handle treaty type assignments
  if (data.treatyTypeNames && data.treatyNumbers) {
    const typeNames = Array.isArray(data.treatyTypeNames) ? data.treatyTypeNames : [];
    const numbers = Array.isArray(data.treatyNumbers) ? data.treatyNumbers : [];
    
    for (let i = 0; i < Math.min(typeNames.length, numbers.length); i++) {
      const typeName = typeNames[i];
      const treatyNumber = numbers[i];
      
      const treatyType = await tx.treatyType.findFirst({
        where: { name: { equals: typeName, mode: 'insensitive' } }
      });
      
      if (treatyType) {
        await tx.userTreatyNumber.create({
          data: {
            userId: user.id,
            treatyTypeId: treatyType.id,
            treatyNumber
          }
        });
      }
    }
  }

  return user;
}

/**
 * Generates a secure random password for bulk created users
 */
function generateSecurePassword(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < 16; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

/**
 * GET endpoint is not supported for upload
 */
export async function GET() {
  return NextResponse.json(
    { error: 'Method Not Allowed', message: 'Use POST to upload CSV files' },
    { status: 405 }
  );
}
