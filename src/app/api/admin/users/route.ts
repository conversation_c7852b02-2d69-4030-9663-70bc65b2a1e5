import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';
import { requireAuth } from '@/lib/middleware/require-auth';
import { checkRateLimit } from '@/lib/middleware/rate-limiting';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';
import { AuthenticationError, AuthorizationError } from '@/lib/errors';

export async function GET(req: NextRequest) {
  try {
    // Apply strict rate limiting for admin user operations
    const rateLimitResult = await checkRateLimit(req, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30, // 30 admin user requests per minute
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        req,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Require admin authentication for user management
    const authContext = await requireAuth(req, {
      requirePermissions: ['admin:users:read'],
      auditLogging: true,
    });

    // Parse query parameters with validation
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = parseInt(url.searchParams.get('limit') || '25', 10);
    const search = url.searchParams.get('search') || '';

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'read',
        'admin_users',
        false,
        req,
        {
          statusCode: 400,
          errorMessage: 'Invalid pagination parameters',
          metadata: { page, limit, search }
        }
      );

      return NextResponse.json(
        { error: 'Bad Request', message: 'Invalid pagination parameters' },
        { status: 400 }
      );
    }

    // Sanitize search input to prevent injection attacks
    const sanitizedSearch = search.replace(/[<>]/g, '').trim();

    // Build where clause for search
    const where: any = {};
    if (sanitizedSearch) {
      where.OR = [
        { name: { contains: sanitizedSearch, mode: 'insensitive' } },
        { email: { contains: sanitizedSearch, mode: 'insensitive' } },
        { profile: { nwaEmail: { contains: sanitizedSearch, mode: 'insensitive' } } },
      ];
    }

    // Fetch users with pagination
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        include: {
          profile: true,
          userRoles: {
            include: {
              role: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.user.count({ where }),
    ]);

    // Transform users for response (limit sensitive information)
    const transformedUsers = users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      nwaEmail: user.profile?.nwaEmail,
      roles: user.userRoles.map(userRole => ({
        id: userRole.role.id,
        name: userRole.role.name,
        description: userRole.role.description,
      })),
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
    }));

    // Log successful admin user access
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'read',
      'admin_users',
      true,
      req,
      {
        metadata: {
          pagination: { page, limit, total },
          searchPerformed: !!sanitizedSearch,
          searchTerm: sanitizedSearch,
          usersReturned: transformedUsers.length
        }
      }
    );

    // Create secure response with CORS and security headers
    const response = NextResponse.json({
      users: transformedUsers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });

    // Add CORS headers for cross-origin requests
    const origin = req.headers.get('origin');
    if (origin) {
      // For development, allow localhost origins
      if (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002')) {
        response.headers.set('Access-Control-Allow-Origin', origin);
        response.headers.set('Access-Control-Allow-Credentials', 'true');
        response.headers.set('Vary', 'Origin');
      }
    }

    // Add security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');

    return response;
  } catch (error) {
    console.error('Error fetching users:', error);

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    // Handle unknown error types safely
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

    return NextResponse.json(
      { error: 'Internal Server Error', message: errorMessage },
      { status: 500 }
    );
  }
}