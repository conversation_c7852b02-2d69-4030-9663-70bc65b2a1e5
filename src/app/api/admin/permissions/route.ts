import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';
import { withAuth<PERSON>andler, requireAdminAuth } from '@/lib/middleware/require-auth';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// Handler function for GET permissions
async function getPermissionsHandler(req: NextRequest) {
  // Apply rate limiting
  const rateLimitResult = await predefinedRateLimiters.api(req);
  if (!rateLimitResult.allowed) {
    return NextResponse.json(
      { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
      { 
        status: 429,
        headers: rateLimitResult.headers
      }
    );
  }

  // Get session for audit logging
  const session = await getServerSession(authOptions);
  const userId = session ? (session.user as any).id : 'unknown';

  // Fetch all permissions
  const permissions = await prisma.permission.findMany({
    orderBy: {
      resource: 'asc',
    },
  });

  // Transform permissions for response
  const transformedPermissions = permissions.map(permission => ({
    id: permission.id,
    name: permission.name,
    resource: permission.resource,
    action: permission.action,
    description: permission.description,
    createdAt: permission.createdAt.toISOString(),
  }));

  // Log the permissions access (sensitive operation)
  await prisma.auditLog.create({
    data: {
      userId: userId,
      action: 'ADMIN_VIEW_PERMISSIONS',
      resource: 'Permission',
      resourceId: 'all',
      success: true,
      oldValues: { accessCount: null },
      newValues: { 
        permissionCount: permissions.length,
        accessedAt: new Date().toISOString(),
      },
      ipAddress: req.headers.get('x-forwarded-for') || 'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown',
    },
  });

  return NextResponse.json({ permissions: transformedPermissions });
}

// Export the wrapped handler with admin authentication
export const GET = withAuthHandler(getPermissionsHandler, { requireAdmin: true });