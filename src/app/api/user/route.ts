import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { userAuditLogger } from '@/lib/user-audit-logger';
import { requireAuth } from '@/lib/middleware/require-auth';
import { checkRateLimit } from '@/lib/middleware/rate-limiting';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';
import { AuthenticationError, AuthorizationError } from '@/lib/errors';

// Validation schema for profile updates
const profileUpdateSchema = z.object({
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(),
  streetName: z.string().optional(),
  aptSuiteUnit: z.string().optional(),
  townCity: z.string().optional(),
  stateProvince: z.string().optional(),
  postcodeZip: z.string().optional(),
  country: z.string().optional(),
  city: z.string().optional(),
});

export async function GET(req: NextRequest) {
   try {
     console.log('🔍 [DEBUG] GET /api/user - Request received');

     // Apply rate limiting for user profile access
     const rateLimitResult = await checkRateLimit(req, {
       windowMs: 60 * 1000, // 1 minute
       maxRequests: 100, // 100 profile requests per minute
     });

     console.log('🔍 [DEBUG] Rate limit check result:', rateLimitResult);

     if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        req,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Require authentication for profile access
    // Allow users to access their own profile without requiring special permissions
    console.log('🔍 [DEBUG] Checking authentication and permissions');
    const authContext = await requireAuth(req, {
      auditLogging: true,
    });
    console.log('🔍 [DEBUG] Authentication successful, userId:', authContext.userId);

    console.log('🔍 [DEBUG] Fetching user from database');
    const user = await prisma.user.findUnique({
      where: { id: authContext.userId },
      include: {
        profile: {
          include: {
            country: true,
            city: true,
          },
        },
      },
    });

    console.log('🔍 [DEBUG] User found:', !!user);

    if (!user) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'read',
        'user_profile',
        false,
        req,
        {
          statusCode: 404,
          errorMessage: 'User not found',
          metadata: { userId: authContext.userId },
        }
      );

      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Log successful profile access
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'read',
      'user_profile',
      true,
      req,
      {
        metadata: {
          profileFields: [
            'id', 'name', 'email', 'firstName', 'lastName', 'bio',
            'phone', 'dateOfBirth', 'streetName', 'aptSuiteUnit', 'townCity',
            'stateProvince', 'postcodeZip', 'country', 'city', 'image', 'twoFactorEnabled'
          ]
        }
      }
    );

    return NextResponse.json({
      id: user.id,
      name: user.name,
      email: user.email,
      nwaEmail: user.profile?.nwaEmail,
      firstName: user.profile?.firstName,
      lastName: user.profile?.lastName,
      bio: user.profile?.bio,
      phone: user.profile?.mobile,
      dateOfBirth: user.profile?.dateOfBirth,
      streetName: user.profile?.streetAddress1,
      aptSuiteUnit: user.profile?.streetAddress2,
      townCity: user.profile?.town,
      stateProvince: user.profile?.regionText,
      postcodeZip: user.profile?.postalCode,
      country: user.profile?.country?.name,
      city: user.profile?.city?.name,
      image: user.image,
      twoFactorEnabled: user.twoFactorEnabled,
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    // Handle unknown error types safely
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

    return NextResponse.json(
      { error: 'Internal Server Error', message: errorMessage },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    // Apply stricter rate limiting for profile updates
    const rateLimitResult = await checkRateLimit(req, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30, // 30 profile updates per minute
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        req,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Require authentication for profile updates
    // Allow users to update their own profile without requiring special permissions
    const authContext = await requireAuth(req, {
      auditLogging: true,
    });

    const body = await req.json();

    // Check if this is a password update
    if (body.currentPassword && body.newPassword) {
      // Handle password update in the password route
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'update',
        'user_profile',
        false,
        req,
        {
          statusCode: 400,
          errorMessage: 'Invalid update type',
          metadata: { updateType: 'password_update', redirectTo: '/api/user/password' }
        }
      );

      return NextResponse.json({ error: 'Use /api/user/password for password updates' }, { status: 400 });
    }

    // Regular profile update
    const validation = profileUpdateSchema.safeParse(body);

    if (!validation.success) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'update',
        'user_profile',
        false,
        req,
        {
          statusCode: 400,
          errorMessage: 'Validation error',
          metadata: { validationErrors: validation.error.flatten() }
        }
      );

      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    const { firstName, lastName, bio, phone, dateOfBirth, streetName, aptSuiteUnit, townCity, stateProvince, postcodeZip, country: countryName, city: cityName } = validation.data;

    // Find the country and city by name if they are provided
    let countryId: number | undefined;
    let cityId: number | undefined;

    if (countryName) {
      const country = await prisma.country.findFirst({
        where: { name: countryName }
      });

      if (!country) {
        await auditLoggingMiddleware.logApiAccess(
          authContext.userId,
          'update',
          'user_profile',
          false,
          req,
          {
            statusCode: 400,
            errorMessage: 'Invalid country',
            metadata: { countryName }
          }
        );

        return NextResponse.json({ error: 'Invalid country' }, { status: 400 });
      }

      countryId = country.id;
    }

    if (cityName && countryId) {
      const city = await prisma.city.findFirst({
        where: {
          name: cityName,
          countryId: countryId
        }
      });

      if (!city) {
        await auditLoggingMiddleware.logApiAccess(
          authContext.userId,
          'update',
          'user_profile',
          false,
          req,
          {
            statusCode: 400,
            errorMessage: 'Invalid city',
            metadata: { cityName, countryId }
          }
        );

        return NextResponse.json({ error: 'Invalid city' }, { status: 400 });
      }

      cityId = city.id;
    }

    // Get current user profile for audit logging
    const currentUser = await prisma.user.findUnique({
      where: { id: authContext.userId },
      include: {
        profile: {
          include: {
            country: true,
            city: true,
          },
        },
      },
    });

    // Update user and profile
    const updatedUser = await prisma.user.update({
      where: { id: authContext.userId },
      data: {
        // Update the main user name with the full name
        name: firstName && lastName ? `${firstName} ${lastName}` : (firstName || lastName || undefined),
        profile: {
          upsert: {
            create: {
              firstName,
              lastName,
              bio,
              mobile: phone,
              dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
              streetAddress1: streetName,
              streetAddress2: aptSuiteUnit,
              town: townCity,
              regionText: stateProvince,
              postalCode: postcodeZip,
              countryId, // Use countryId
              cityId,    // Use cityId
            },
            update: {
              firstName,
              lastName,
              bio,
              mobile: phone,
              dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
              streetAddress1: streetName,
              streetAddress2: aptSuiteUnit,
              town: townCity,
              regionText: stateProvince,
              postalCode: postcodeZip,
              countryId, // Use countryId
              cityId,    // Use cityId
            },
          },
        },
      },
      include: {
        profile: {
          include: {
            country: true,
            city: true,
          },
        },
      },
    });

    // Log successful profile update
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'update',
      'user_profile',
      true,
      req,
      {
        metadata: {
          updatedFields: Object.keys(validation.data),
          previousData: {
            name: currentUser?.name,
            firstName: currentUser?.profile?.firstName,
            lastName: currentUser?.profile?.lastName,
            bio: currentUser?.profile?.bio,
            phone: currentUser?.profile?.mobile,
            dateOfBirth: currentUser?.profile?.dateOfBirth,
            streetName: currentUser?.profile?.streetAddress1,
            aptSuiteUnit: currentUser?.profile?.streetAddress2,
            townCity: currentUser?.profile?.town,
            stateProvince: currentUser?.profile?.regionText,
            postcodeZip: currentUser?.profile?.postalCode,
            country: currentUser?.profile?.country?.name,
            city: currentUser?.profile?.city?.name,
          },
          newData: {
            name: updatedUser.name,
            firstName: updatedUser.profile?.firstName,
            lastName: updatedUser.profile?.lastName,
            bio: updatedUser.profile?.bio,
            phone: updatedUser.profile?.mobile,
            dateOfBirth: updatedUser.profile?.dateOfBirth,
            streetName: updatedUser.profile?.streetAddress1,
            aptSuiteUnit: updatedUser.profile?.streetAddress2,
            townCity: updatedUser.profile?.town,
            stateProvince: updatedUser.profile?.regionText,
            postcodeZip: updatedUser.profile?.postalCode,
            country: updatedUser.profile?.country?.name,
            city: updatedUser.profile?.city?.name,
          }
        }
      }
    );
    
    // Log profile update for audit purposes
    try {
      // Fetch country and city names for audit logging
      let previousCountryName: string | null = null;
      let previousCityName: string | null = null;
      
      if (currentUser?.profile?.countryId) {
        const previousCountry = await prisma.country.findUnique({
          where: { id: currentUser.profile.countryId }
        });
        previousCountryName = previousCountry?.name || null;
      }
      
      if (currentUser?.profile?.cityId) {
        const previousCity = await prisma.city.findUnique({
          where: { id: currentUser.profile.cityId }
        });
        previousCityName = previousCity?.name || null;
      }
      
      // Fetch country and city names for the updated user
      let newCountryName: string | null = null;
      let newCityName: string | null = null;
      
      if (updatedUser.profile?.countryId) {
        const newCountry = await prisma.country.findUnique({
          where: { id: updatedUser.profile.countryId }
        });
        newCountryName = newCountry?.name || null;
      }
      
      if (updatedUser.profile?.cityId) {
        const newCity = await prisma.city.findUnique({
          where: { id: updatedUser.profile.cityId }
        });
        newCityName = newCity?.name || null;
      }
      
      // Keep the existing userAuditLogger for detailed audit logging
      await userAuditLogger.logUserUpdate(
        authContext.userId,
        {
          name: currentUser?.name,
          firstName: currentUser?.profile?.firstName,
          lastName: currentUser?.profile?.lastName,
          bio: currentUser?.profile?.bio,
          phone: currentUser?.profile?.mobile,
          dateOfBirth: currentUser?.profile?.dateOfBirth,
          streetName: currentUser?.profile?.streetAddress1,
          aptSuiteUnit: currentUser?.profile?.streetAddress2,
          townCity: currentUser?.profile?.town,
          stateProvince: currentUser?.profile?.regionText,
          postcodeZip: currentUser?.profile?.postalCode,
          country: previousCountryName, // Use country name
          city: previousCityName,       // Use city name
        },
        {
          name: updatedUser.name,
          firstName: updatedUser.profile?.firstName,
          lastName: updatedUser.profile?.lastName,
          bio: updatedUser.profile?.bio,
          phone: updatedUser.profile?.mobile,
          dateOfBirth: updatedUser.profile?.dateOfBirth,
          streetName: updatedUser.profile?.streetAddress1,
          aptSuiteUnit: updatedUser.profile?.streetAddress2,
          townCity: updatedUser.profile?.town,
          stateProvince: updatedUser.profile?.regionText,
          postcodeZip: updatedUser.profile?.postalCode,
          country: newCountryName, // Use country name
          city: newCityName,       // Use city name
        },
        authContext.userId, // User updating their own profile
        {
          action: 'update_profile',
          ipAddress: userAuditLogger.getClientIP(req.headers),
          userAgent: userAuditLogger.getUserAgent(req.headers),
        }
      );
    } catch (auditError) {
      console.error('Failed to log profile update audit:', auditError);
      // Continue with the update process even if audit logging fails
    }
    
    return NextResponse.json({
      id: updatedUser.id,
      name: updatedUser.name,
      email: updatedUser.email,
      nwaEmail: updatedUser.profile?.nwaEmail,
      firstName: updatedUser.profile?.firstName,
      lastName: updatedUser.profile?.lastName,
      bio: updatedUser.profile?.bio,
      phone: updatedUser.profile?.mobile,
      dateOfBirth: updatedUser.profile?.dateOfBirth,
      streetName: updatedUser.profile?.streetAddress1,
      aptSuiteUnit: updatedUser.profile?.streetAddress2,
      townCity: updatedUser.profile?.town,
      stateProvince: updatedUser.profile?.regionText,
      postcodeZip: updatedUser.profile?.postalCode,
      country: updatedUser.profile?.country?.name,
      city: updatedUser.profile?.city?.name,
      image: updatedUser.image,
      twoFactorEnabled: updatedUser.twoFactorEnabled,
    });
  } catch (error) {
    console.error('Error updating user profile:', error);

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    // Handle unknown error types safely
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

    return NextResponse.json(
      { error: 'Internal Server Error', message: errorMessage },
      { status: 500 }
    );
  }
}