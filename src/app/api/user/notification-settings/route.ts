import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  inAppNotifications: boolean;
  notificationTypes: {
    userManagement: boolean;
    treaty: boolean;
    remoteServer: boolean;
    system: boolean;
    security: boolean;
  };
  priorityFilters: {
    urgent: boolean;
    high: boolean;
    normal: boolean;
    low: boolean;
  };
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

// GET /api/user/notification-settings - Get user's notification settings
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user ID from email
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // For now, return default settings
    // In a real implementation, you would store these in the database
    const defaultSettings: NotificationSettings = {
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      inAppNotifications: true,
      notificationTypes: {
        userManagement: true,
        treaty: true,
        remoteServer: true,
        system: true,
        security: true,
      },
      priorityFilters: {
        urgent: true,
        high: true,
        normal: true,
        low: false,
      },
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '08:00',
      },
    };

    return NextResponse.json(defaultSettings);
  } catch (error) {
    console.error('Error fetching notification settings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/user/notification-settings - Update user's notification settings
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user ID from email
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const body = await request.json();
    const settings: NotificationSettings = body;

    // Validate the settings
    if (!settings || typeof settings !== 'object') {
      return NextResponse.json(
        { error: 'Invalid settings data' },
        { status: 400 }
      );
    }

    // In a real implementation, you would save these to the database
    // For now, we'll just validate and return success
    console.log('Notification settings updated for user:', user.id, settings);

    // TODO: Save to database when user preferences table is created
    // await prisma.userNotificationSettings.upsert({
    //   where: { userId: session.user.id },
    //   update: settings,
    //   create: {
    //     userId: session.user.id,
    //     ...settings,
    //   },
    // });

    return NextResponse.json({
      message: 'Notification settings updated successfully',
      settings
    });
  } catch (error) {
    console.error('Error updating notification settings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}