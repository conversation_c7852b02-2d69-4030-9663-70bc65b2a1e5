import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // For now, return mock permissions based on user relationships
    // In a real implementation, this would fetch from proper permission tables
    const userRoles = await prisma.userRole.findMany({
      where: { userId: user.id },
      include: { role: true }
    });

    const isAdmin = userRoles.some(ur => ur.role.name === 'admin' || ur.role.name === 'ADMIN' || ur.role.name === 'SUPER_ADMIN');
    const isSuperAdmin = userRoles.some(ur => ur.role.name === 'SUPER_ADMIN');

    const permissions = [];
    const roles = [];

    // Add basic permissions for all users
    permissions.push(
      { id: '1', name: 'Authentication', resource: 'AUTHENTICATION', action: 'ACCESS', description: 'Basic authentication access' },
      { id: '2', name: 'User Read', resource: 'users', action: 'read', description: 'Read user information' },
      { id: '3', name: 'Profile Access', resource: 'USER_PROFILE', action: 'READ', description: 'Access to user profile' },
      { id: '4', name: 'Profile Update', resource: 'USER_PROFILE', action: 'UPDATE', description: 'Update user profile' },
      { id: '5', name: 'Profile Read', resource: 'profile', action: 'read', description: 'Read profile information' }
    );

    // Add admin permissions
    if (isAdmin) {
      permissions.push(
        { id: '3', name: 'Admin Panel Access', resource: 'ADMIN_PANEL', action: 'ACCESS', description: 'Access to admin panel' },
        { id: '4', name: 'User Management', resource: 'users', action: 'manage', description: 'Manage users' },
        { id: '5', name: 'Admin Users', resource: 'admin', action: 'users', description: 'Access admin user features' },
        { id: '6', name: 'Security Management', resource: 'SECURITY_MANAGEMENT', action: 'READ', description: 'Access security features' }
      );

      roles.push({
        id: '1',
        name: isSuperAdmin ? 'SUPER_ADMIN' : 'admin',
        description: isSuperAdmin ? 'Super Administrator' : 'Administrator',
        permissions: permissions.filter(p => p.resource.includes('ADMIN') || p.resource.includes('SECURITY'))
      });
    }

    // Add super admin permissions
    if (isSuperAdmin) {
      permissions.push(
        { id: '6', name: 'System Settings', resource: 'SYSTEM_SETTINGS', action: 'UPDATE', description: 'Update system settings' },
        { id: '7', name: 'User Creation', resource: 'USER_MANAGEMENT', action: 'CREATE', description: 'Create new users' },
        { id: '8', name: 'User Deletion', resource: 'USER_MANAGEMENT', action: 'DELETE', description: 'Delete users' }
      );
    }

    const response = {
      id: user.id,
      userId: user.id,
      permissions: permissions,
      roles: roles,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching user permissions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}