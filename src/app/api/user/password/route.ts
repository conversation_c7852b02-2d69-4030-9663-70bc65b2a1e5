import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { z } from 'zod';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';
import { requireAuth } from '@/lib/middleware/require-auth';
import { checkRateLimit } from '@/lib/middleware/rate-limiting';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';
import { AuthenticationError, AuthorizationError } from '@/lib/errors';

// Validation schema for password updates
const passwordUpdateSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'Password must be at least 8 characters long'),
});

export async function PUT(req: NextRequest) {
  try {
    // Apply strict rate limiting for password updates (more restrictive than regular requests)
    const rateLimitResult = await checkRateLimit(req, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 5, // Only 5 password update attempts per minute
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        req,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Require authentication for password updates
    const authContext = await requireAuth(req, {
      requirePermissions: ['users:write'],
      auditLogging: true,
    });

    const body = await req.json();
    const validation = passwordUpdateSchema.safeParse(body);

    if (!validation.success) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'update',
        'user_password',
        false,
        req,
        {
          statusCode: 400,
          errorMessage: 'Validation error',
          metadata: { validationErrors: validation.error.flatten() }
        }
      );

      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { currentPassword, newPassword } = validation.data;

    // Fetch the user with their hashed password
    const user = await prisma.user.findUnique({
      where: { id: authContext.userId },
    });

    // Check if user exists and has a password hash
    if (!user || !user.passwordHash) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'update',
        'user_password',
        false,
        req,
        {
          statusCode: 400,
          errorMessage: 'Password not set',
          metadata: { reason: 'no_existing_password' }
        }
      );

      return NextResponse.json({ error: 'Password not set' }, { status: 400 });
    }

    // Compare the currentPassword with the stored hash
    const isValid = await bcrypt.compare(currentPassword, user.passwordHash);

    if (!isValid) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'update',
        'user_password',
        false,
        req,
        {
          statusCode: 400,
          errorMessage: 'Current password is incorrect',
          metadata: { reason: 'invalid_current_password' }
        }
      );

      return NextResponse.json({ error: 'Current password is incorrect' }, { status: 400 });
    }

    // Hash the newPassword and update it
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    await prisma.user.update({
      where: { id: authContext.userId },
      data: { passwordHash: hashedPassword },
    });

    // Log successful password update
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'update',
      'user_password',
      true,
      req,
      {
        metadata: {
          passwordChanged: true,
          userId: authContext.userId,
          timestamp: new Date().toISOString()
        }
      }
    );

    return NextResponse.json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Error updating password:', error);

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    // Handle unknown error types safely
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

    return NextResponse.json(
      { error: 'Internal Server Error', message: errorMessage },
      { status: 500 }
    );
  }
}