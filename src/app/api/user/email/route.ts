import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import crypto from 'crypto';
import { EmailService } from '@/lib/services/email-service';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';
import { requireAuth } from '@/lib/middleware/require-auth';
import { checkRateLimit } from '@/lib/middleware/rate-limiting';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';
import { AuthenticationError, AuthorizationError } from '@/lib/errors';

// Validation schema for email update request
const emailUpdateSchema = z.object({
  newEmail: z.string().email('Invalid email format'),
});

// Validation schema for email verification
const emailVerificationSchema = z.object({
  token: z.string(),
});

export async function POST(req: NextRequest) {
  try {
    // Apply strict rate limiting for email operations
    const rateLimitResult = await checkRateLimit(req, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10, // 10 email operations per minute
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        req,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Require authentication for email operations
    const authContext = await requireAuth(req, {
      requirePermissions: ['users:write'],
      auditLogging: true,
    });

    const body = await req.json();

    // If this is a verification request (with token)
    if (body.token) {
      const validation = emailVerificationSchema.safeParse(body);

      if (!validation.success) {
        await auditLoggingMiddleware.logApiAccess(
          authContext.userId,
          'verify',
          'user_email',
          false,
          req,
          {
            statusCode: 400,
            errorMessage: 'Validation error',
            metadata: { validationErrors: validation.error.flatten() }
          }
        );

        return NextResponse.json(
          { error: 'Validation error', details: validation.error.flatten() },
          { status: 400 }
        );
      }

      const { token } = validation.data;

      // Find verification token
      const verificationToken = await prisma.verificationToken.findUnique({
        where: {
          token: token,
        },
      });

      if (!verificationToken || verificationToken.expires < new Date()) {
        await auditLoggingMiddleware.logApiAccess(
          authContext.userId,
          'verify',
          'user_email',
          false,
          req,
          {
            statusCode: 400,
            errorMessage: 'Invalid or expired token',
            metadata: { tokenExpired: !verificationToken, tokenId: token.substring(0, 8) + '...' }
          }
        );

        return NextResponse.json({ error: 'Invalid or expired token' }, { status: 400 });
      }

      // Update user's personal email
      await prisma.user.update({
        where: { id: authContext.userId },
        data: {
          email: verificationToken.identifier,
          emailVerified: new Date(), // Mark as verified when email is updated
        },
      });

      // Delete used token
      await prisma.verificationToken.delete({
        where: { token: token },
      });

      // Log successful email verification
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'verify',
        'user_email',
        true,
        req,
        {
          metadata: {
            emailVerified: verificationToken.identifier,
            verificationMethod: 'token',
            tokenId: token.substring(0, 8) + '...'
          }
        }
      );

      return NextResponse.json({ message: 'Email updated successfully' });
    }
    
    // If this is an email update request (with new email)
    const validation = emailUpdateSchema.safeParse(body);

    if (!validation.success) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'update',
        'user_email',
        false,
        req,
        {
          statusCode: 400,
          errorMessage: 'Validation error',
          metadata: { validationErrors: validation.error.flatten() }
        }
      );

      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { newEmail } = validation.data;

    // Check if email is already in use
    const existingUser = await prisma.user.findUnique({
      where: { email: newEmail },
    });

    if (existingUser) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'update',
        'user_email',
        false,
        req,
        {
          statusCode: 400,
          errorMessage: 'Email is already in use',
          metadata: { attemptedEmail: newEmail, existingUserId: existingUser.id }
        }
      );

      return NextResponse.json({ error: 'Email is already in use' }, { status: 400 });
    }

    // Generate verification token
    const token = crypto.randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Save verification token
    await prisma.verificationToken.create({
      data: {
        identifier: newEmail,
        token: token,
        expires: expires,
      },
    });

    // Send verification email
    if (process.env.NODE_ENV === 'production') {
      const emailService = new EmailService();
      await emailService.sendVerificationEmail(newEmail, token);
    }

    // Log email update initiation
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'update',
      'user_email',
      true,
      req,
      {
        metadata: {
          emailUpdateInitiated: true,
          newEmail: newEmail,
          tokenGenerated: true,
          tokenId: token.substring(0, 8) + '...',
          expiresAt: expires.toISOString()
        }
      }
    );

    // In development, return the token in the response (SECURITY RISK - should be removed in production)
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json({
        message: 'Verification email sent',
        token: token,
        email: newEmail
      });
    }

    return NextResponse.json({ message: 'Verification email sent. Please check your inbox.' });
  } catch (error) {
    console.error('Error updating email:', error);

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    // Handle unknown error types safely
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

    return NextResponse.json(
      { error: 'Internal Server Error', message: errorMessage },
      { status: 500 }
    );
  }
}