import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import crypto from 'crypto';
import QRCode from 'qrcode';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting for 2FA enable
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Generate a secret for 2FA
    const secret = crypto.randomBytes(20).toString('hex');
    
    // Update user with 2FA secret
    const user = await prisma.user.update({
      where: { id: (session.user as any).id },
      data: { 
        twoFactorSecret: secret,
        twoFactorEnabled: true
      },
    });
    
    // Generate QR code URL for the user to scan
    const email = user.email || '<EMAIL>';
    const issuer = 'NWA Portal';
    const encodedIssuer = encodeURIComponent(issuer);
    const encodedEmail = encodeURIComponent(email);
    const encodedSecret = encodeURIComponent(secret);
    
    const otpAuthUrl = `otpauth://totp/${encodedIssuer}:${encodedEmail}?secret=${encodedSecret}&issuer=${encodedIssuer}`;
    
    // Generate QR code as data URL
    const qrCodeDataUrl = await QRCode.toDataURL(otpAuthUrl, {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#ffffff'
      }
    });
    
    return NextResponse.json({ 
      message: '2FA enabled',
      secret: secret,
      qrCodeUrl: qrCodeDataUrl
    });
  } catch (error) {
    console.error('Error enabling 2FA:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get current 2FA status
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: {
        twoFactorEnabled: true,
        twoFactorSecret: true
      }
    });

    return NextResponse.json({ 
      twoFactorEnabled: user?.twoFactorEnabled || false,
      hasSecret: !!user?.twoFactorSecret
    });
  } catch (error) {
    console.error('Error fetching 2FA status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting for 2FA disable
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Disable 2FA by removing the secret
    await prisma.user.update({
      where: { id: (session.user as any).id },
      data: { 
        twoFactorSecret: null,
        twoFactorEnabled: false
      },
    });
    
    return NextResponse.json({ message: '2FA disabled' });
  } catch (error) {
    console.error('Error disabling 2FA:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}