import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import crypto from 'crypto';

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { backupCodes } = await req.json();
    
    if (!backupCodes || !Array.isArray(backupCodes)) {
      return NextResponse.json({ error: 'Backup codes are required' }, { status: 400 });
    }

    // Hash the backup codes before storing them
    const hashedBackupCodes = backupCodes.map(code => 
      crypto.createHash('sha256').update(code).digest('hex')
    );

    // Store backup codes for the user
    await prisma.user.update({
      where: { id: (session.user as any).id },
      data: {
        backupCodes: hashedBackupCodes,
        // Mark 2FA as fully enabled (with backup codes)
        twoFactorEnabled: true,
      },
    });

    return NextResponse.json({ 
      message: 'Backup codes saved successfully',
      codesCount: backupCodes.length
    });

  } catch (error) {
    console.error('Error saving backup codes:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { backupCodes: true }
    });

    return NextResponse.json({ 
      hasBackupCodes: !!user?.backupCodes?.length,
      codesCount: user?.backupCodes?.length || 0
    });

  } catch (error) {
    console.error('Error fetching backup codes status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}