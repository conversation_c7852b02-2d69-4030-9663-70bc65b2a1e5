import { NextRequest, NextResponse } from 'next/server'
import { permissionSyncCron } from '@/lib/services/permission-sync-cron'
import { PermissionSyncService } from '@/lib/services/permission-sync-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const serverId = searchParams.get('serverId')
    const action = searchParams.get('action') || 'status'

    if (action === 'sync-server' && serverId) {
      // Sync specific server
      const result = await permissionSyncCron.triggerServerSync(serverId)
      return NextResponse.json(result)
    } else if (action === 'sync-all') {
      // Sync all servers
      const result = await permissionSyncCron.triggerAllServersSync()
      return NextResponse.json(result)
    } else if (action === 'status') {
      // Get sync status
      if (serverId) {
        const syncService = new PermissionSyncService()
        const stats = await syncService.getSyncStats(serverId)
        const lastSync = await syncService.getLastSyncTime(serverId)
        return NextResponse.json({ serverId, stats, lastSync })
      } else {
        const statuses = permissionSyncCron.getAllJobStatuses()
        return NextResponse.json({ jobs: statuses })
      }
    } else if (action === 'start') {
      // Start cron jobs
      permissionSyncCron.start()
      return NextResponse.json({ success: true, message: 'Cron jobs started' })
    } else if (action === 'stop') {
      // Stop cron jobs
      permissionSyncCron.stop()
      return NextResponse.json({ success: true, message: 'Cron jobs stopped' })
    } else {
      return NextResponse.json(
        { error: 'Invalid action' },
        { status: 400 }
      )
    }

  } catch (error: any) {
    console.error('Permission sync API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { serverId, action = 'sync' } = body

    if (action === 'sync' && serverId) {
      // Sync specific server
      const result = await permissionSyncCron.triggerServerSync(serverId)
      return NextResponse.json(result)
    } else if (action === 'sync-all') {
      // Sync all servers
      const result = await permissionSyncCron.triggerAllServersSync()
      return NextResponse.json(result)
    } else {
      return NextResponse.json(
        { error: 'Invalid action or missing serverId' },
        { status: 400 }
      )
    }

  } catch (error: any) {
    console.error('Permission sync API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}