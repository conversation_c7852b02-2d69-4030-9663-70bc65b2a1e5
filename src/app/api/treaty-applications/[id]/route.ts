import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { ApplicationStatus } from '@prisma/client';

// GET /api/treaty-applications/[id] - Get specific application details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    // Get application details
    const application = await prisma.userTreatyType.findFirst({
      where: {
        id,
        OR: [
          { userId: (session.user as any).id }, // User can see their own applications
          // Admins can see all applications (will add admin check later)
        ],
      },
      include: {
        treatyType: {
          select: {
            id: true,
            name: true,
            description: true,
            price: true,
            requiresPayment: true,
            paymentDeadlineDays: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        payments: {
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    // For non-admin users, verify they own the application
    if (application.userId !== (session.user as any).id) {
      // TODO: Add admin role check here
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    return NextResponse.json({
      application: {
        id: application.id,
        userId: application.userId,
        treatyTypeId: application.treatyTypeId,
        status: application.status,
        paymentStatus: application.paymentStatus,
        appliedAt: application.appliedAt,
        approvedAt: application.approvedAt,
        rejectedAt: application.rejectedAt,
        approvedBy: application.approvedBy,
        rejectedBy: application.rejectedBy,
        rejectionReason: application.rejectionReason,
        notes: application.notes,
        treatyType: application.treatyType,
        user: application.userId === (session.user as any).id ? application.user : null,
        payments: application.payments,
      },
    });
  } catch (error) {
    console.error('Error fetching treaty application:', error);
    return NextResponse.json(
      { error: 'Failed to fetch treaty application' },
      { status: 500 }
    );
  }
}

// PUT /api/treaty-applications/[id] - Update application (cancel/resubmit)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const body = await request.json();
    const { action, notes } = body;

    // Get existing application
    const existingApplication = await prisma.userTreatyType.findUnique({
      where: { id },
      include: { user: true },
    });

    if (!existingApplication) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    // Verify user owns the application
    if (existingApplication.userId !== (session.user as any).id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    let updatedApplication;
    const auditData = {
      userId: (session.user as any).id,
      action: '',
      resource: 'UserTreatyType',
      resourceId: id,
      oldValues: {
        status: existingApplication.status,
        notes: existingApplication.notes,
      },
      newValues: {},
      success: true,
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    };

    switch (action) {
      case 'cancel':
        // Only allow cancellation of APPLIED or UNDER_REVIEW applications
        const cancelableStatuses: ApplicationStatus[] = [
          ApplicationStatus.APPLIED,
          ApplicationStatus.UNDER_REVIEW,
          ApplicationStatus.REJECTED
        ];
        if (!cancelableStatuses.includes(existingApplication.status)) {
          return NextResponse.json(
            { error: 'Cannot cancel application in current status' },
            { status: 400 }
          );
        }

        updatedApplication = await prisma.userTreatyType.update({
          where: { id },
          data: {
            status: ApplicationStatus.REJECTED,
            rejectedAt: new Date(),
            rejectedBy: (session.user as any).id,
            rejectionReason: notes || 'Cancelled by user',
          },
          include: {
            treatyType: true,
            payments: true,
          },
        });

        auditData.action = 'CANCEL_TREATY_APPLICATION';
        auditData.newValues = {
          status: ApplicationStatus.REJECTED,
          rejectionReason: notes || 'Cancelled by user',
        };
        break;

      case 'resubmit':
        // Only allow resubmission of REJECTED applications
        if (existingApplication.status !== ApplicationStatus.REJECTED) {
          return NextResponse.json(
            { error: 'Can only resubmit rejected applications' },
            { status: 400 }
          );
        }

        updatedApplication = await prisma.userTreatyType.update({
          where: { id },
          data: {
            status: ApplicationStatus.APPLIED,
            appliedAt: new Date(),
            rejectedAt: null,
            rejectedBy: null,
            rejectionReason: null,
            notes: notes || existingApplication.notes,
          },
          include: {
            treatyType: true,
            payments: true,
          },
        });

        auditData.action = 'RESUBMIT_TREATY_APPLICATION';
        auditData.newValues = {
          status: ApplicationStatus.APPLIED,
          notes: notes || existingApplication.notes,
        };
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    // Create audit log
    await prisma.auditLog.create({
      data: auditData,
    });

    return NextResponse.json({
      success: true,
      application: updatedApplication,
    });
  } catch (error) {
    console.error('Error updating treaty application:', error);
    return NextResponse.json(
      { error: 'Failed to update treaty application' },
      { status: 500 }
    );
  }
}