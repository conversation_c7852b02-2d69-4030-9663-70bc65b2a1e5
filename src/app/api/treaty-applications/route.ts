import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { ApplicationStatus, PaymentStatus } from '@prisma/client';

// GET /api/treaty-applications - List user's treaty applications
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') as ApplicationStatus | null;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    const skip = (page - 1) * limit;

    // Build where clause
    const where = {
      userId: (session.user as any).id,
      ...(status && { status }),
    };

    // Get applications with pagination
    const [applications, total] = await Promise.all([
      prisma.userTreatyType.findMany({
        where,
        include: {
          treatyType: {
            select: {
              id: true,
              name: true,
              description: true,
              price: true,
              requiresPayment: true,
              paymentDeadlineDays: true,
            },
          },
          payments: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 1, // Get latest payment
          },
        },
        orderBy: {
          appliedAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.userTreatyType.count({ where }),
    ]);

    // Transform data for frontend
    const transformedApplications = applications.map(app => ({
      id: app.id,
      userId: app.userId,
      treatyTypeId: app.treatyTypeId,
      status: app.status,
      paymentStatus: app.paymentStatus,
      appliedAt: app.appliedAt,
      approvedAt: app.approvedAt,
      rejectedAt: app.rejectedAt,
      rejectionReason: app.rejectionReason,
      notes: app.notes,
      treatyType: app.treatyType,
      latestPayment: app.payments[0] || null,
    }));

    return NextResponse.json({
      applications: transformedApplications,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching treaty applications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch treaty applications' },
      { status: 500 }
    );
  }
}

// POST /api/treaty-applications - Submit new treaty application
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { treatyTypeId, submissionReason, notes } = body;

    // Validate required fields
    if (!treatyTypeId) {
      return NextResponse.json(
        { error: 'Treaty type ID is required' },
        { status: 400 }
      );
    }

    // Check if user has Peace Ambassador status
    const userProfile = await prisma.userProfile.findUnique({
      where: { userId: (session.user as any).id },
    });

    if (!userProfile || !userProfile.peaceAmbassadorNumber) {
      return NextResponse.json(
        { error: 'Peace Ambassador status is required to apply for treaties' },
        { status: 403 }
      );
    }

    // Check if treaty type exists and is available
    const treatyType = await prisma.treatyType.findUnique({
      where: { id: treatyTypeId },
    });

    if (!treatyType) {
      return NextResponse.json(
        { error: 'Treaty type not found' },
        { status: 404 }
      );
    }

    // Check if user already has an active or pending application for this treaty type
    const existingApplication = await prisma.userTreatyType.findFirst({
      where: {
        userId: (session.user as any).id,
        treatyTypeId,
        status: {
          in: [ApplicationStatus.APPLIED, ApplicationStatus.UNDER_REVIEW, ApplicationStatus.APPROVED, ApplicationStatus.ACTIVE],
        },
      },
    });

    if (existingApplication) {
      return NextResponse.json(
        { error: 'You already have an active or pending application for this treaty type' },
        { status: 409 }
      );
    }

    // Create the application
    const application = await prisma.userTreatyType.create({
      data: {
        userId: (session.user as any).id,
        treatyTypeId,
        status: ApplicationStatus.APPLIED,
        appliedAt: new Date(),
        notes: notes || null,
      },
      include: {
        treatyType: {
          select: {
            id: true,
            name: true,
            description: true,
            price: true,
            requiresPayment: true,
            paymentDeadlineDays: true,
          },
        },
      },
    });

    // Log the application submission
    await prisma.auditLog.create({
      data: {
        userId: (session.user as any).id,
        action: 'CREATE_TREATY_APPLICATION',
        resource: 'UserTreatyType',
        resourceId: application.id,
        oldValues: { id: null, userId: null, treatyTypeId: null },
        newValues: {
          treatyTypeId,
          status: ApplicationStatus.APPLIED,
          submissionReason,
        },
        success: true,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    });

    return NextResponse.json({
      success: true,
      application: {
        id: application.id,
        userId: application.userId,
        treatyTypeId: application.treatyTypeId,
        status: application.status,
        paymentStatus: application.paymentStatus,
        appliedAt: application.appliedAt,
        notes: application.notes,
        treatyType: application.treatyType,
      },
    });
  } catch (error) {
    console.error('Error creating treaty application:', error);
    return NextResponse.json(
      { error: 'Failed to create treaty application' },
      { status: 500 }
    );
  }
}