import { NextRequest, NextResponse } from 'next/server';
import { apiKeyValidationMiddleware } from '@/lib/middleware/api-key-validation';
import { checkRateLimit } from '@/lib/middleware/rate-limiting';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  try {
    // Validate API key authentication
    const apiKeyValidation = await apiKeyValidationMiddleware.validateApiKey(req);
    
    if (!apiKeyValidation.isValid) {
      return NextResponse.json({ 
        error: 'Unauthorized', 
        message: apiKeyValidation.error || 'Authentication required' 
      }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '100', 10);
    const userId = searchParams.get('userId');
    
    const skip = (page - 1) * limit;

  // Find the NWATracker remote server
    const nwaTrackerServer = await prisma.remoteServer.findFirst({
      where: {
        name: 'NWATracker',
        isActive: true,
      },
    });

    if (!nwaTrackerServer) {
      return NextResponse.json({ 
        error: 'Configuration error', 
        message: 'NWATracker remote server not configured' 
      }, { status: 500 });
    }

    // Rate limiting for service tokens: project-based key with client IP
    try {
      const rateLimitResult = await checkRateLimit(req, {
        windowMs: 60 * 1000,
        maxRequests: 100,
        keyGenerator: (r) => {
          const projectId = nwaTrackerServer.id;
          const ip = r.headers.get('x-forwarded-for')?.split(',')[0] || r.headers.get('x-real-ip') || 'unknown';
          return `project:${projectId}:${ip}`;
        },
      });

      if (!rateLimitResult.allowed) {
        return NextResponse.json({
          error: 'Too Many Requests',
          message: 'Rate limit exceeded',
          retryAfter: rateLimitResult.retryAfter,
        }, {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter || 60),
          },
        });
      }
    } catch (e) {
      console.error('Rate limit check failed for NWATracker route:', e);
      // allow the request if rate limiting check fails
    }

    if (userId) {
      // Single user request - get specific user with NWATracker permissions
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          profile: {
            include: {
              title: true,
              country: true,
              city: true,
            },
          },
          userPositions: {
            where: { isActive: true },
            include: { position: true },
            // 'isPrimary' does not exist on UserPosition; order by startDate as a reasonable default
            orderBy: { startDate: 'desc' },
            take: 1,
          },
        },
      });

      if (!user) {
        return NextResponse.json({ 
          error: 'User not found', 
          message: 'User account does not exist' 
        }, { status: 404 });
      }

      // Get user permissions specific to NWATracker
      // Fetch user permissions from the user_remote_server_permissions table
      const userPermissions = await prisma.user_remote_server_permissions.findMany({
        where: {
          user_id: user.id,
          remote_server_id: nwaTrackerServer.id,
          is_active: true,
        },
      });

      const permissions = userPermissions.map(up => up.permission_name);

      // Fetch user roles by looking up UserRole entries where the linked Role has a role_remote_server_access for this server
      const userRemoteRoles = await prisma.userRole.findMany({
        where: {
          userId: user.id,
          role: {
            role_remote_server_access: {
              some: { remote_server_id: nwaTrackerServer.id },
            },
          },
        },
        include: { role: true },
      });

      const roles = userRemoteRoles.map(ur => ur.role.name);

      return NextResponse.json({
        user: {
          id: user.id,
          firstName: user.profile?.firstName || '',
          surname: user.profile?.lastName || '',
          email: user.email || '',
          title: user.userPositions[0]?.position?.title || user.profile?.title?.name || '',
          role: roles,
          permissions,
        },
        timestamp: new Date().toISOString(),
      });
    } else {
      // Bulk users request - get paginated users with NWATracker access
      const [users, totalCount] = await Promise.all([
        prisma.user.findMany({
          skip,
          take: limit,
          include: {
            profile: {
              include: {
                country: true,
                city: true,
                title: true,
              },
            },
            userPositions: {
              where: { isActive: true },
              include: { position: true },
              orderBy: { startDate: 'desc' },
              take: 1,
            },
          },
          orderBy: { createdAt: 'desc' },
        }),
        prisma.user.count(),
      ]);

      const totalPages = Math.ceil(totalCount / limit);

          // Batch fetch permissions and roles for the users to avoid per-user queries
          const userIds = users.map(u => u.id);

          const allPermissions = await prisma.user_remote_server_permissions.findMany({
            where: { remote_server_id: nwaTrackerServer.id, user_id: { in: userIds }, is_active: true },
          });

          const allUserRoles = await prisma.userRole.findMany({
            where: {
              userId: { in: userIds },
              role: { role_remote_server_access: { some: { remote_server_id: nwaTrackerServer.id } } },
            },
            include: { role: true },
          });

          const permsByUser: Record<string, string[]> = {};
          for (const p of allPermissions) {
            permsByUser[p.user_id] = permsByUser[p.user_id] || [];
            permsByUser[p.user_id].push(p.permission_name);
          }

          const rolesByUser: Record<string, string[]> = {};
          for (const r of allUserRoles) {
            rolesByUser[r.userId] = rolesByUser[r.userId] || [];
            if (r.role) rolesByUser[r.userId].push(r.role.name);
          }

          const formattedUsers = users.map(user => {
            const permissions = permsByUser[user.id] || [];
            const roles = rolesByUser[user.id] || [];

            return {
              id: user.id,
              firstName: user.profile?.firstName || '',
              surname: user.profile?.lastName || '',
              email: user.email || '',
              title: user.userPositions[0]?.position?.title || user.profile?.title?.name || '',
              role: roles,
              permissions,
            };
          });

      return NextResponse.json({
        users: formattedUsers,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          pageSize: limit,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
        remoteServer: {
          id: nwaTrackerServer.id,
          name: nwaTrackerServer.name,
        },
        timestamp: new Date().toISOString(),
      });
    }

  } catch (error) {
    console.error('NWATracker API error:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      message: 'Failed to retrieve user information' 
    }, { status: 500 });
  }
}