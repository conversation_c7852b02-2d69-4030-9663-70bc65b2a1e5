import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '24h'; // 24h, 7d, 30d
    const severity = searchParams.get('severity'); // low, medium, high, critical

    // Get user roles to determine permissions
    const userRoles = await prisma.userRole.findMany({
      where: { userId: user.id },
      include: { role: true }
    });

    const isAdmin = userRoles.some(ur => ur.role.name === 'admin' || ur.role.name === 'ADMIN' || ur.role.name === 'SUPER_ADMIN');

    // Calculate date range based on period
    const now = new Date();
    let startDate = new Date();

    switch (period) {
      case '24h':
        startDate.setHours(now.getHours() - 24);
        break;
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      default:
        startDate.setHours(now.getHours() - 24);
    }

    // Get security events
    const where: any = {
      detectedAt: { gte: startDate, lte: now }
    };

    if (severity) {
      where.severity = severity.toUpperCase();
    }

    const securityEvents = await prisma.securityEvent.findMany({
      where,
      orderBy: { detectedAt: 'desc' },
      take: 50, // Limit to recent events
    });

    // Get security statistics
    const totalEvents = await prisma.securityEvent.count({
      where: { detectedAt: { gte: startDate, lte: now } }
    });

    const eventsBySeverity = await getEventsBySeverity(startDate, now);
    const eventsByType = await getEventsByType(startDate, now);
    const topSources = await getTopSecuritySources(startDate, now);

    // Get failed login attempts
    const failedLogins = await prisma.auditLog.count({
      where: {
        action: 'LOGIN_FAILED',
        timestamp: { gte: startDate, lte: now }
      }
    });

    // Get suspicious activity patterns
    const suspiciousPatterns = await getSuspiciousPatterns(startDate, now);

    // Get system health from security perspective
    const systemHealth = await getSecuritySystemHealth();

    const response: any = {
      summary: {
        totalEvents,
        failedLogins,
        period,
        timeRange: {
          start: startDate.toISOString(),
          end: now.toISOString(),
        },
      },
      events: securityEvents.map(event => ({
        id: event.id,
        type: event.type,
        severity: event.severity,
        description: event.description,
        source: event.source,
        ipAddress: event.ipAddress,
        userAgent: event.userAgent,
        detectedAt: event.detectedAt,
        resolvedAt: event.resolvedAt,
        status: event.status,
        assignedTo: event.assignedTo,
        resolution: event.resolution,
        metadata: event.metadata,
      })),
      statistics: {
        bySeverity: eventsBySeverity,
        byType: eventsByType,
        topSources,
        suspiciousPatterns,
      },
      systemHealth,
    };

    // Add admin-specific security data
    if (isAdmin) {
      const adminSecurityData = await getAdminSecurityData(startDate, now);
      response.adminData = adminSecurityData;

      const threatIntelligence = await getThreatIntelligence();
      response.threatIntelligence = threatIntelligence;
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching dashboard security data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function getEventsBySeverity(startDate: Date, endDate: Date) {
  const result = await prisma.securityEvent.groupBy({
    by: ['severity'],
    where: { detectedAt: { gte: startDate, lte: endDate } },
    _count: { id: true },
    orderBy: { _count: { id: 'desc' } },
  });

  return result.map(item => ({
    severity: item.severity,
    count: item._count.id,
    percentage: 0, // Will be calculated after getting total
  }));
}

async function getEventsByType(startDate: Date, endDate: Date) {
  const result = await prisma.securityEvent.groupBy({
    by: ['type'],
    where: { detectedAt: { gte: startDate, lte: endDate } },
    _count: { id: true },
    orderBy: { _count: { id: 'desc' } },
  });

  return result.map(item => ({
    type: item.type,
    count: item._count.id,
    percentage: 0, // Will be calculated after getting total
  }));
}

async function getTopSecuritySources(startDate: Date, endDate: Date) {
  const result = await prisma.securityEvent.groupBy({
    by: ['source'],
    where: { detectedAt: { gte: startDate, lte: endDate } },
    _count: { id: true },
    orderBy: { _count: { id: 'desc' } },
    take: 10,
  });

  return result.map(item => ({
    source: item.source,
    count: item._count.id,
  }));
}

async function getSuspiciousPatterns(startDate: Date, endDate: Date) {
  // Mock suspicious patterns data
  return [
    {
      pattern: 'Multiple failed login attempts from same IP',
      count: Math.floor(Math.random() * 50) + 10,
      riskLevel: 'high',
      description: 'Multiple authentication failures from single IP address',
    },
    {
      pattern: 'Unusual access time patterns',
      count: Math.floor(Math.random() * 30) + 5,
      riskLevel: 'medium',
      description: 'Access attempts outside normal business hours',
    },
    {
      pattern: 'Suspicious user agent strings',
      count: Math.floor(Math.random() * 20) + 3,
      riskLevel: 'medium',
      description: 'User agents that may indicate automated attacks',
    },
    {
      pattern: 'Rate limit violations',
      count: Math.floor(Math.random() * 40) + 8,
      riskLevel: 'high',
      description: 'API endpoints being accessed too frequently',
    },
  ];
}

async function getSecuritySystemHealth() {
  // Mock system health from security perspective
  return {
    overall: 'good',
    components: {
      firewall: {
        status: 'active',
        lastUpdate: new Date(Date.now() - Math.random() * 3600000), // Random time in last hour
        rulesCount: Math.floor(Math.random() * 1000) + 500,
      },
      intrusionDetection: {
        status: 'active',
        threatsDetected: Math.floor(Math.random() * 10),
        lastScan: new Date(Date.now() - Math.random() * 1800000), // Random time in last 30 minutes
      },
      accessControl: {
        status: 'active',
        activePolicies: Math.floor(Math.random() * 50) + 20,
        violations: Math.floor(Math.random() * 5),
      },
      encryption: {
        status: 'active',
        algorithms: ['AES-256', 'RSA-2048', 'SHA-256'],
        lastRotation: new Date(Date.now() - Math.random() * 86400000), // Random time in last 24 hours
      },
    },
    recommendations: [
      'Update firewall rules to block suspicious IP ranges',
      'Review and update access control policies',
      'Rotate encryption keys for enhanced security',
    ],
  };
}

async function getAdminSecurityData(startDate: Date, endDate: Date) {
  // Admin-specific security metrics
  const totalAuditLogs = await prisma.auditLog.count({
    where: { timestamp: { gte: startDate, lte: endDate } }
  });

  const securityIncidents = await prisma.securityEvent.count({
    where: {
      detectedAt: { gte: startDate, lte: endDate },
      status: { in: ['OPEN', 'INVESTIGATING'] }
    }
  });

  const resolvedIncidents = await prisma.securityEvent.count({
    where: {
      detectedAt: { gte: startDate, lte: endDate },
      status: 'RESOLVED'
    }
  });

  const averageResolutionTime = Math.floor(Math.random() * 120) + 30; // 30-150 minutes

  return {
    totalAuditLogs,
    securityIncidents,
    resolvedIncidents,
    averageResolutionTime,
    complianceScore: 95 + Math.random() * 5, // 95-100%
    securityScore: 88 + Math.random() * 12, // 88-100%
    riskAssessment: {
      overall: 'low',
      factors: [
        { name: 'Authentication Security', score: 92, status: 'good' },
        { name: 'Data Protection', score: 89, status: 'good' },
        { name: 'Access Control', score: 94, status: 'good' },
        { name: 'Network Security', score: 87, status: 'fair' },
      ],
    },
  };
}

async function getThreatIntelligence() {
  // Mock threat intelligence data
  return {
    currentThreats: [
      {
        id: '1',
        title: 'Increased Brute Force Attacks',
        severity: 'medium',
        description: 'Global increase in brute force authentication attempts',
        affectedSystems: ['Authentication', 'API Endpoints'],
        mitigation: 'Implement stronger rate limiting and CAPTCHA',
        confidence: 85,
        lastUpdated: new Date(),
      },
      {
        id: '2',
        title: 'Zero-Day Vulnerability in Dependencies',
        severity: 'high',
        description: 'Potential zero-day vulnerability in third-party dependencies',
        affectedSystems: ['Web Application', 'API Services'],
        mitigation: 'Update dependencies and monitor for patches',
        confidence: 70,
        lastUpdated: new Date(Date.now() - 3600000), // 1 hour ago
      },
    ],
    threatLevel: 'elevated',
    lastUpdated: new Date(),
    sources: ['NIST', 'CVE Database', 'Security Research Firms'],
  };
}