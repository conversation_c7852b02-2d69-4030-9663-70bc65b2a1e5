import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const type = searchParams.get('type');
    const priority = searchParams.get('priority');
    const unreadOnly = searchParams.get('unread') === 'true';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      recipients: { has: user.email! }
    };

    if (type) {
      where.type = type;
    }

    if (priority) {
      where.priority = priority;
    }

    // Get notifications with pagination
    const notifications = await prisma.notification.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await prisma.notification.count({ where });

    // Get notification statistics
    const unreadWhere = {
      recipients: { has: user.email! },
      readBy: { not: { has: user.id } }
    };

    // Filter notifications to find unread ones (client-side filtering for now)
    const unreadNotifications = notifications.filter(notification =>
      !notification.readBy.includes(user.id)
    );
    const unreadCount = unreadNotifications.length;

    const response = {
      notifications: notifications.map(notification => ({
        id: notification.id,
        type: notification.type,
        message: notification.message,
        data: notification.data,
        priority: notification.priority,
        category: notification.category,
        trigger: notification.trigger,
        triggeredBy: notification.triggeredBy,
        createdAt: notification.createdAt,
        updatedAt: notification.updatedAt,
        isRead: notification.readBy.includes(user.id),
      })),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1,
      },
      stats: {
        total: totalCount,
        unread: unreadCount,
        byType: await getNotificationsByType(user.email!),
        byPriority: await getNotificationsByPriority(user.email!),
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching dashboard notifications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function getNotificationsByType(userEmail: string) {
  const result = await prisma.notification.groupBy({
    by: ['type'],
    where: { recipients: { has: userEmail } },
    _count: { id: true },
    orderBy: { _count: { id: 'desc' } },
  });

  return result.map(item => ({
    type: item.type,
    count: item._count.id,
  }));
}

async function getNotificationsByPriority(userEmail: string) {
  const result = await prisma.notification.groupBy({
    by: ['priority'],
    where: { recipients: { has: userEmail } },
    _count: { id: true },
    orderBy: { _count: { id: 'desc' } },
  });

  return result.map(item => ({
    priority: item.priority,
    count: item._count.id,
  }));
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { notificationIds, action } = body;

    if (!notificationIds || !Array.isArray(notificationIds)) {
      return NextResponse.json(
        { error: 'Invalid notification IDs' },
        { status: 400 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    let result;

    switch (action) {
      case 'mark_read':
        result = await markNotificationsAsRead(notificationIds, user.id);
        break;
      case 'mark_unread':
        result = await markNotificationsAsUnread(notificationIds, user.id);
        break;
      case 'delete':
        result = await deleteNotifications(notificationIds, user.email!);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating notifications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function markNotificationsAsRead(notificationIds: string[], userId: string) {
  const notifications = await prisma.notification.findMany({
    where: { id: { in: notificationIds } },
    select: { id: true, readBy: true },
  });

  const updates = notifications.map(notification => {
    if (!notification.readBy.includes(userId)) {
      return prisma.notification.update({
        where: { id: notification.id },
        data: { readBy: { push: userId } },
      });
    }
    return Promise.resolve(notification);
  });

  await Promise.all(updates);

  return { success: true, message: 'Notifications marked as read' };
}

async function markNotificationsAsUnread(notificationIds: string[], userId: string) {
  const notifications = await prisma.notification.findMany({
    where: { id: { in: notificationIds } },
    select: { id: true, readBy: true },
  });

  const updates = notifications.map(notification => {
    const updatedReadBy = notification.readBy.filter(id => id !== userId);
    return prisma.notification.update({
      where: { id: notification.id },
      data: { readBy: updatedReadBy },
    });
  });

  await Promise.all(updates);

  return { success: true, message: 'Notifications marked as unread' };
}

async function deleteNotifications(notificationIds: string[], userEmail: string) {
  // Note: In a real implementation, you might want to implement soft delete
  // or move to a deleted_notifications table instead of hard delete
  const result = await prisma.notification.deleteMany({
    where: {
      id: { in: notificationIds },
      recipients: { has: userEmail },
    },
  });

  return {
    success: true,
    message: `${result.count} notifications deleted`,
    deletedCount: result.count
  };
}