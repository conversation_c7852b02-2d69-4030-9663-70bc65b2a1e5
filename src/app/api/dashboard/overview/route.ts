import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get user roles to determine permissions
    const userRoles = await prisma.userRole.findMany({
      where: { userId: user.id },
      include: { role: true }
    });

    const isAdmin = userRoles.some(ur => ur.role.name === 'admin' || ur.role.name === 'ADMIN' || ur.role.name === 'SUPER_ADMIN');

    // Get basic statistics
    const totalUsers = await prisma.user.count();
    const totalTreaties = await prisma.treaty.count();
    const totalNationTreaties = await prisma.nationTreaty.count();
    const totalServers = await prisma.remoteServer.count();

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentUsers = await prisma.user.count({
      where: { createdAt: { gte: thirtyDaysAgo } }
    });

    const recentTreaties = await prisma.treaty.count({
      where: { createdAt: { gte: thirtyDaysAgo } }
    });

    // Get user's personal statistics
    const userTreaties = await prisma.treaty.count({
      where: { userId: user.id }
    });

    const userNationTreaties = await prisma.nationTreaty.count({
      where: {
        OR: [
          { members: { some: { userId: user.id } } },
          { envoys: { some: { userId: user.id } } }
        ]
      }
    });

    // Get user profile for completion calculation
    let userProfile = null;
    try {
      userProfile = await prisma.userProfile.findUnique({
        where: { userId: user.id }
      });

      // If no profile exists, create one with basic info
      if (!userProfile) {
        console.log('Creating user profile for user:', user.id);
        userProfile = await prisma.userProfile.create({
          data: {
            userId: user.id,
            firstName: user.name?.split(' ')[0] || '',
            lastName: user.name?.split(' ').slice(1).join(' ') || '',
            personalEmail: user.email || '',
          },
        });
        console.log('Created user profile:', userProfile);
      } else {
        console.log('Found existing user profile:', userProfile);
      }
    } catch (error) {
      console.log('Error with user profile for user:', user.id, error);
    }

    // Calculate profile completion percentage
    // Map form field names to database field names
    const fieldMapping = {
      'firstName': 'firstName',
      'lastName': 'lastName',
      'streetName': 'streetAddress1', // Form uses streetName, DB uses streetAddress1
      'townCity': 'town',            // Form uses townCity, DB uses town
      'personalEmail': 'personalEmail'
    };

    const requiredFormFields = Object.keys(fieldMapping);
    let completedFields = 0;
    const totalFields = requiredFormFields.length;

    // Check required fields using the form field names
    if (userProfile) {
      // Check firstName and lastName (direct mapping)
      if (userProfile.firstName && userProfile.firstName.trim() !== '') completedFields++;
      if (userProfile.lastName && userProfile.lastName.trim() !== '') completedFields++;

      // Check streetName (maps to streetAddress1 in DB)
      if (userProfile.streetAddress1 && userProfile.streetAddress1.trim() !== '') completedFields++;

      // Check townCity (maps to town in DB)
      if (userProfile.town && userProfile.town.trim() !== '') completedFields++;

      // Check personalEmail (direct mapping)
      if (userProfile.personalEmail && userProfile.personalEmail.trim() !== '') completedFields++;
    }

    // Ensure we don't divide by zero and handle NaN cases
    const profileCompletionPercentage = totalFields > 0 && !isNaN(completedFields)
      ? Math.round((completedFields / totalFields) * 100)
      : 0;

    // Debug logging
    console.log('Profile completion calculation:', {
      userId: user.id,
      userProfileExists: !!userProfile,
      completedFields,
      totalFields,
      profileCompletionPercentage,
      fieldMapping,
      userProfileData: userProfile ? {
        firstName: userProfile.firstName,
        lastName: userProfile.lastName,
        streetAddress1: userProfile.streetAddress1,
        town: userProfile.town,
        personalEmail: userProfile.personalEmail,
        // Show what the form would see
        streetName: userProfile.streetAddress1, // Form field name
        townCity: userProfile.town,            // Form field name
      } : null
    });

    // Get system health metrics
    const activeSessions = await prisma.session.count({
      where: { expires: { gt: new Date() } }
    });

    const failedLogins = await prisma.auditLog.count({
      where: {
        action: 'LOGIN_FAILED',
        timestamp: { gte: thirtyDaysAgo }
      }
    });

    // Build response based on user permissions
    const response: any = {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        treaties: userTreaties,
        nationTreaties: userNationTreaties,
        createdAt: user.createdAt,
        profileCompletion: isNaN(profileCompletionPercentage) ? 0 : profileCompletionPercentage,
      },
      stats: {
        totalUsers,
        totalTreaties,
        totalNationTreaties,
        totalServers,
        activeSessions,
        failedLogins,
      },
      recentActivity: {
        newUsers: recentUsers,
        newTreaties: recentTreaties,
        period: '30 days',
      },
      quickActions: [],
    };

    // Add admin-specific data
    if (isAdmin) {
      const totalOrdinances = await prisma.ordinance.count();
      const pendingTreaties = await prisma.treaty.count({
        where: { status: 'PENDING' }
      });

      response.stats.totalOrdinances = totalOrdinances;
      response.stats.pendingTreaties = pendingTreaties;

      response.adminStats = {
        pendingTreaties,
        systemHealth: {
          database: 'healthy',
          cache: 'healthy',
          storage: 'healthy',
        },
        securityMetrics: {
          failedLogins,
          suspiciousActivity: 0, // Would be calculated from audit logs
        },
      };

      response.quickActions = [
        { id: 'create-user', label: 'Create New User', icon: 'UserPlus', href: '/admin/manage/createuser' },
        { id: 'manage-treaties', label: 'Manage Treaties', icon: 'FileText', href: '/admin/manage/treaties' },
        { id: 'system-settings', label: 'System Settings', icon: 'Settings', href: '/settings' },
        { id: 'security-audit', label: 'Security Audit', icon: 'Shield', href: '/admin/security' },
      ];
    } else {
      // Regular user actions
      response.quickActions = [
        { id: 'view-profile', label: 'View Profile', icon: 'User', href: '/profile' },
        { id: 'my-treaties', label: 'My Treaties', icon: 'FileText', href: '/treaties' },
        { id: 'notifications', label: 'Notifications', icon: 'Bell', href: '/notifications' },
        { id: 'settings', label: 'Settings', icon: 'Settings', href: '/settings' },
      ];
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching dashboard overview:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}