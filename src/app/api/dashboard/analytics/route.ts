import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30d'; // 7d, 30d, 90d, 1y
    const metric = searchParams.get('metric'); // users, treaties, revenue, etc.

    // Get user roles to determine permissions
    const userRoles = await prisma.userRole.findMany({
      where: { userId: user.id },
      include: { role: true }
    });

    const isAdmin = userRoles.some(ur => ur.role.name === 'admin' || ur.role.name === 'ADMIN' || ur.role.name === 'SUPER_ADMIN');

    // Calculate date range based on period
    const now = new Date();
    let startDate = new Date();

    switch (period) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(now.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // Get basic analytics data
    const totalUsers = await prisma.user.count();
    const totalTreaties = await prisma.treaty.count();
    const totalNationTreaties = await prisma.nationTreaty.count();
    const totalOrdinances = await prisma.ordinance.count();

    // Get time-series data for charts
    const userGrowth = await getUserGrowthData(startDate, now);
    const treatyGrowth = await getTreatyGrowthData(startDate, now);
    const revenueData = await getRevenueData(startDate, now);
    const geographicData = await getGeographicData();

    // Get user engagement metrics
    const activeUsers = await prisma.session.count({
      where: {
        expires: { gt: now },
        lastActive: { gte: new Date(now.getTime() - 24 * 60 * 60 * 1000) } // Last 24 hours
      }
    });

    const newUsersThisMonth = await prisma.user.count({
      where: {
        createdAt: {
          gte: new Date(now.getFullYear(), now.getMonth(), 1),
          lt: new Date(now.getFullYear(), now.getMonth() + 1, 1)
        }
      }
    });

    // Get treaty statistics
    const treatyStats = await getTreatyStatistics();

    // Get system performance metrics
    const systemMetrics = await getSystemMetrics(startDate, now);

    const response: any = {
      summary: {
        totalUsers,
        totalTreaties,
        totalNationTreaties,
        totalOrdinances,
        activeUsers,
        newUsersThisMonth,
        period,
      },
      charts: {
        userGrowth,
        treatyGrowth,
        revenueData,
        geographicData,
      },
      metrics: {
        treatyStats,
        systemMetrics,
      },
    };

    // Add admin-specific analytics
    if (isAdmin) {
      const adminMetrics = await getAdminMetrics(startDate, now);
      response.adminMetrics = adminMetrics;

      const securityMetrics = await getSecurityMetrics(startDate, now);
      response.securityMetrics = securityMetrics;

      const financialMetrics = await getFinancialMetrics(startDate, now);
      response.financialMetrics = financialMetrics;
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching dashboard analytics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function getUserGrowthData(startDate: Date, endDate: Date) {
  // Mock user growth data - in real implementation, this would aggregate from user creation dates
  const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  const data = [];
  let cumulativeUsers = 0;

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);

    // Mock daily user growth
    const newUsers = Math.floor(Math.random() * 10) + 1;
    cumulativeUsers += newUsers;

    data.push({
      date: date.toISOString().split('T')[0],
      newUsers,
      totalUsers: cumulativeUsers,
    });
  }

  return data;
}

async function getTreatyGrowthData(startDate: Date, endDate: Date) {
  // Mock treaty growth data
  const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  const data = [];
  let cumulativeTreaties = 0;

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);

    const newTreaties = Math.floor(Math.random() * 5) + 1;
    cumulativeTreaties += newTreaties;

    data.push({
      date: date.toISOString().split('T')[0],
      newTreaties,
      totalTreaties: cumulativeTreaties,
    });
  }

  return data;
}

async function getRevenueData(startDate: Date, endDate: Date) {
  // Mock revenue data
  const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  const data = [];

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);

    // Mock daily revenue
    const revenue = Math.floor(Math.random() * 1000) + 100;

    data.push({
      date: date.toISOString().split('T')[0],
      revenue,
      transactions: Math.floor(Math.random() * 20) + 1,
    });
  }

  return data;
}

async function getGeographicData() {
  // Mock geographic distribution data
  return [
    { country: 'United States', users: 1250, percentage: 35.2 },
    { country: 'United Kingdom', users: 890, percentage: 25.1 },
    { country: 'Canada', users: 445, percentage: 12.5 },
    { country: 'Australia', users: 320, percentage: 9.0 },
    { country: 'Germany', users: 280, percentage: 7.9 },
    { country: 'France', users: 190, percentage: 5.3 },
    { country: 'Japan', users: 120, percentage: 3.4 },
    { country: 'Others', users: 55, percentage: 1.6 },
  ];
}

async function getTreatyStatistics() {
  const totalTreaties = await prisma.treaty.count();
  const activeTreaties = await prisma.treaty.count({ where: { status: 'ACTIVE' } });
  const expiredTreaties = await prisma.treaty.count({ where: { status: 'EXPIRED' } });
  const pendingTreaties = await prisma.treaty.count({ where: { status: 'PENDING' } });

  return {
    total: totalTreaties,
    active: activeTreaties,
    expired: expiredTreaties,
    pending: pendingTreaties,
    activePercentage: totalTreaties > 0 ? (activeTreaties / totalTreaties) * 100 : 0,
    expiredPercentage: totalTreaties > 0 ? (expiredTreaties / totalTreaties) * 100 : 0,
    pendingPercentage: totalTreaties > 0 ? (pendingTreaties / totalTreaties) * 100 : 0,
  };
}

async function getSystemMetrics(startDate: Date, endDate: Date) {
  // Mock system performance metrics
  return {
    averageResponseTime: Math.floor(Math.random() * 200) + 100, // ms
    uptimePercentage: 99.9 + (Math.random() * 0.1), // 99.9% - 100%
    errorRate: Math.random() * 0.1, // 0% - 0.1%
    throughput: Math.floor(Math.random() * 1000) + 500, // requests per minute
    databaseConnections: Math.floor(Math.random() * 50) + 10,
    cacheHitRate: 95 + (Math.random() * 5), // 95% - 100%
  };
}

async function getAdminMetrics(startDate: Date, endDate: Date) {
  // Admin-specific metrics
  const totalSessions = await prisma.session.count();
  const activeSessions = await prisma.session.count({
    where: { expires: { gt: new Date() } }
  });

  const auditLogs = await prisma.auditLog.count({
    where: { timestamp: { gte: startDate, lte: endDate } }
  });

  const securityEvents = await prisma.securityEvent.count({
    where: { detectedAt: { gte: startDate, lte: endDate } }
  });

  return {
    totalSessions,
    activeSessions,
    auditLogs,
    securityEvents,
    systemHealth: {
      overall: 'good',
      database: 'healthy',
      cache: 'healthy',
      api: 'healthy',
    },
  };
}

async function getSecurityMetrics(startDate: Date, endDate: Date) {
  const failedLogins = await prisma.auditLog.count({
    where: {
      action: 'LOGIN_FAILED',
      timestamp: { gte: startDate, lte: endDate }
    }
  });

  const securityEvents = await prisma.securityEvent.count({
    where: { detectedAt: { gte: startDate, lte: endDate } }
  });

  const highSeverityEvents = await prisma.securityEvent.count({
    where: {
      detectedAt: { gte: startDate, lte: endDate },
      severity: 'HIGH'
    }
  });

  return {
    failedLogins,
    totalSecurityEvents: securityEvents,
    highSeverityEvents,
    averageSeverity: 'medium',
    topThreats: [
      { type: 'Failed Login Attempts', count: failedLogins },
      { type: 'Suspicious Activity', count: Math.floor(securityEvents * 0.3) },
      { type: 'Rate Limit Exceeded', count: Math.floor(securityEvents * 0.1) },
    ],
  };
}

async function getFinancialMetrics(startDate: Date, endDate: Date) {
  // Mock financial data
  const totalRevenue = 125000; // Mock total revenue
  const monthlyRevenue = 15000; // Mock monthly revenue
  const pendingPayments = 2500; // Mock pending payments
  const completedPayments = 12000; // Mock completed payments

  return {
    totalRevenue,
    monthlyRevenue,
    pendingPayments,
    completedPayments,
    currency: 'USD',
    growthRate: 12.5, // Mock growth rate percentage
    topRevenueSources: [
      { source: 'Treaty Applications', amount: 85000, percentage: 68 },
      { source: 'Premium Features', amount: 25000, percentage: 20 },
      { source: 'Custom Services', amount: 15000, percentage: 12 },
    ],
  };
}