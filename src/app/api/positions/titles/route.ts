import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/positions/titles - Get all titles
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    console.log('DEBUG: Session data:', { session: session ? 'exists' : 'null', userId: session?.user ? (session.user as any).id : null, userEmail: session?.user?.email });

    if (!session?.user) {
      console.log('DEBUG: No session or user found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    console.log('DEBUG: User lookup result:', {
      userFound: user ? 'yes' : 'no',
      userId: user?.id,
      userEmail: user?.email,
      userRolesCount: user?.userRoles?.length || 0,
      userRoles: user?.userRoles?.map(ur => ({ roleId: ur.role.id, roleName: ur.role.name })) || []
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');
    console.log('DEBUG: Admin check result:', { isAdmin, adminRoleFound: user?.userRoles.some(ur => ur.role.name.toLowerCase() === 'admin') });

    if (!isAdmin) {
      console.log('DEBUG: User is not admin, returning 403');
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const titles = await prisma.title.findMany({
      where: { 
        isActive: true,
        isAmbassadorial: true 
      },
      orderBy: { name: 'asc' }
    });

    return NextResponse.json(titles);
  } catch (error) {
    console.error('Error fetching titles:', error);
    return NextResponse.json({ error: 'Failed to fetch titles' }, { status: 500 });
  }
}

// POST /api/positions/titles - Create a new title
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { name, description } = body;

    // Validate input
    if (!name || name.trim().length === 0) {
      return NextResponse.json({ error: 'Title name is required' }, { status: 400 });
    }

    // Check if title already exists
    const existingTitle = await prisma.title.findUnique({
      where: { name: name.trim() }
    });

    if (existingTitle) {
      return NextResponse.json({ error: 'A title with this name already exists' }, { status: 400 });
    }

    const title = await prisma.title.create({
      data: {
        name: name.trim(),
        description: description?.trim() || null
      }
    });

    return NextResponse.json(title, { status: 201 });
  } catch (error) {
    console.error('Error creating title:', error);
    return NextResponse.json({ error: 'Failed to create title' }, { status: 500 });
  }
}