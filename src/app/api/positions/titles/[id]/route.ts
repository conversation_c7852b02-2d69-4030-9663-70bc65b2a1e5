import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// PUT /api/positions/titles/[id] - Update a title
export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { name, description, isActive } = body;

    // Validate input
    if (!name || name.trim().length === 0) {
      return NextResponse.json({ error: 'Title name is required' }, { status: 400 });
    }

    // Check if another title already exists with this name
    const existingTitle = await prisma.title.findFirst({
      where: {
        name: name.trim(),
        NOT: { id }
      }
    });

    if (existingTitle) {
      return NextResponse.json({ error: 'A title with this name already exists' }, { status: 400 });
    }

    const title = await prisma.title.update({
      where: { id },
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        isActive: typeof isActive === 'boolean' ? isActive : undefined
      }
    });

    return NextResponse.json(title);
  } catch (error) {
    console.error('Error updating title:', error);
    return NextResponse.json({ error: 'Failed to update title' }, { status: 500 });
  }
}

// DELETE /api/positions/titles/[id] - Delete a title
export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;

    // Check if title is being used by any users
    const userProfileCount = await prisma.userProfile.count({
      where: { titleId: id }
    });

    if (userProfileCount > 0) {
      return NextResponse.json({ 
        error: 'Cannot delete title because it is assigned to users' 
      }, { status: 400 });
    }

    // Delete the title
    await prisma.title.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Title deleted successfully' });
  } catch (error: any) {
    if (error.code === 'P2025') {
      return NextResponse.json({ error: 'Title not found' }, { status: 404 });
    }
    
    console.error('Error deleting title:', error);
    return NextResponse.json({ error: 'Failed to delete title' }, { status: 500 });
  }
}