import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';

// GET /api/positions/positions - Get all positions
// Optional query param: titleId to filter positions by title
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    console.log('DEBUG: Session data:', { session: session ? 'exists' : 'null', userId: session?.user ? (session.user as any).id : null, userEmail: session?.user?.email });

    if (!session?.user) {
      console.log('DEBUG: No session or user found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting for autocomplete usage
    const rateLimitResult = await predefinedRateLimiters.api(request as NextRequest);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    console.log('DEBUG: User lookup result:', {
      userFound: user ? 'yes' : 'no',
      userId: user?.id,
      userEmail: user?.email,
      userRolesCount: user?.userRoles?.length || 0,
      userRoles: user?.userRoles?.map(ur => ({ roleId: ur.role.id, roleName: ur.role.name })) || []
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');
    console.log('DEBUG: Admin check result:', { isAdmin, adminRoleFound: user?.userRoles.some(ur => ur.role.name.toLowerCase() === 'admin') });

    if (!isAdmin) {
      console.log('DEBUG: User is not admin, returning 403');
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const titleId = searchParams.get('titleId');
    const includeInactive = searchParams.get('includeInactive') === 'true';
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;
    const sortBy = searchParams.get('sortBy') || 'title';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Additional filtering parameters
    const level = searchParams.get('level');
    const levelMin = searchParams.get('levelMin');
    const levelMax = searchParams.get('levelMax');
    const parentId = searchParams.get('parentId');
    const isActive = searchParams.get('isActive');
    const createdAfter = searchParams.get('createdAfter');
    const createdBefore = searchParams.get('createdBefore');
    const updatedAfter = searchParams.get('updatedAfter');
    const updatedBefore = searchParams.get('updatedBefore');

    let whereClause: any = {};
    if (titleId) {
      whereClause = {
        titlePositions: {
          some: {
            titleId
          }
        }
      };
    }

    // Filter out inactive positions by default unless includeInactive is true
    if (!includeInactive) {
      whereClause.isActive = true;
    }

    // Additional filtering options
    if (level) {
      whereClause.level = parseInt(level);
    }

    if (levelMin || levelMax) {
      whereClause.level = {};
      if (levelMin) whereClause.level.gte = parseInt(levelMin);
      if (levelMax) whereClause.level.lte = parseInt(levelMax);
    }

    if (parentId) {
      if (parentId === 'null') {
        whereClause.parentId = null;
      } else {
        whereClause.parentId = parentId;
      }
    }

    if (isActive !== null && isActive !== undefined) {
      whereClause.isActive = isActive === 'true';
    }

    if (createdAfter) {
      whereClause.createdAt = { ...whereClause.createdAt, gte: new Date(createdAfter) };
    }

    if (createdBefore) {
      whereClause.createdAt = { ...whereClause.createdAt, lte: new Date(createdBefore) };
    }

    if (updatedAfter) {
      whereClause.updatedAt = { ...whereClause.updatedAt, gte: new Date(updatedAfter) };
    }

    if (updatedBefore) {
      whereClause.updatedAt = { ...whereClause.updatedAt, lte: new Date(updatedBefore) };
    }

    // Add search functionality - optimized for autocomplete
    if (search && search.trim()) {
      const searchTerm = search.trim();
      
      // For autocomplete, require at least 2 characters for search
      if (searchTerm.length < 2) {
        return NextResponse.json({
          error: 'Bad Request',
          message: 'Search query must be at least 2 characters long'
        }, { status: 400 });
      }
      
      // Enhanced search for autocomplete - prioritize title matches
      whereClause = {
        ...whereClause,
        OR: [
          // Title matches get higher priority (will be ordered first)
          { title: { contains: searchTerm, mode: 'insensitive' } },
          // Description matches as secondary option
          { description: { contains: searchTerm, mode: 'insensitive' } }
        ]
      };
    }

    // Get total count for pagination
    const totalCount = await prisma.position.count({
      where: whereClause
    });

    // Build orderBy clause
    const validSortFields = ['title', 'description', 'level', 'createdAt', 'updatedAt'];
    const validSortOrders = ['asc', 'desc'];

    const orderBy: any = {};
    if (validSortFields.includes(sortBy)) {
      orderBy[sortBy] = validSortOrders.includes(sortOrder) ? sortOrder : 'asc';
    } else {
      // Default to title ascending if invalid sort field
      orderBy.title = 'asc';
    }

    // Get paginated positions
    const positions = await prisma.position.findMany({
      where: whereClause,
      include: {
        parent: true,
        children: true,
        titlePositions: {
          include: {
            title: true
          }
        }
      },
      orderBy,
      skip: offset,
      take: limit
    });

    const totalPages = Math.ceil(totalCount / limit);

    // Format response for autocomplete compatibility
    const response = {
      success: true,
      data: positions,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching positions:', error);
    return NextResponse.json({ error: 'Failed to fetch positions' }, { status: 500 });
  }
}

// POST /api/positions/positions - Create a new position
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  let requestData: any = {};

  try {
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { title, description, level, parentId } = body;

    // Store request data for audit logging in case of error
    requestData = {
      title: title?.trim(),
      description: description?.trim(),
      level: level || 1,
      parentId: parentId || null
    };

    // Validate input
    if (!title || title.trim().length === 0) {
      return NextResponse.json({ error: 'Position title is required' }, { status: 400 });
    }

    // Check if position already exists
    const existingPosition = await prisma.position.findUnique({
      where: { title: title.trim() }
    });

    if (existingPosition) {
      return NextResponse.json({ error: 'A position with this title already exists' }, { status: 400 });
    }

    // Validate parentId exists if provided
    if (parentId && parentId !== null) {
      const parentExists = await prisma.position.findUnique({
        where: { id: parentId }
      });

      if (!parentExists) {
        return NextResponse.json({ error: 'Parent position does not exist' }, { status: 400 });
      }
    }

    const position = await prisma.position.create({
      data: {
        title: title.trim(),
        description: description?.trim() || null,
        level: level || 1,
        parentId: parentId || null
      }
    });

    // Log the creation
    try {
      await prisma.auditLog.create({
        data: {
          userId: (session.user as any).id,
          action: 'CREATE',
          resource: 'positions',
          resourceId: position.id,
          newValues: {
            title: position.title,
            description: position.description,
            level: position.level,
            parentId: position.parentId
          },
          success: true,
          statusCode: 201,
          apiEndpoint: '/api/positions/positions',
          requestMethod: 'POST'
        }
      });
    } catch (auditError) {
      console.error('Failed to create audit log for position creation:', auditError);
      // Don't fail the main operation if audit logging fails
    }

    return NextResponse.json(position, { status: 201 });
  } catch (error) {
    console.error('Error creating position:', error);

    // Log the failed creation attempt
    try {
      if (session?.user) {
        await prisma.auditLog.create({
          data: {
            userId: (session.user as any).id,
            action: 'CREATE',
            resource: 'positions',
            resourceId: null,
            oldValues: undefined,
            newValues: requestData,
            success: false,
            statusCode: 500,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            apiEndpoint: '/api/positions/positions',
            requestMethod: 'POST'
          }
        });
      }
    } catch (auditError) {
      console.error('Failed to create audit log for failed position creation:', auditError);
    }

    return NextResponse.json({ error: 'Failed to create position' }, { status: 500 });
  }
}