import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// PATCH /api/positions/positions/[id] - Partially update a position
export async function PATCH(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { title, description, level, parentId, isActive } = body;

    // Validate input - only validate title if provided
    if (title !== undefined && (!title || title.trim().length === 0)) {
      return NextResponse.json({ error: 'Position title cannot be empty' }, { status: 400 });
    }

    // Check if another position already exists with this title (if title is being updated)
    if (title !== undefined) {
      const existingPosition = await prisma.position.findFirst({
        where: {
          title: title.trim(),
          NOT: { id }
        }
      });

      if (existingPosition) {
        return NextResponse.json({ error: 'A position with this title already exists' }, { status: 400 });
      }
    }

    // Validate parentId exists if provided
    if (parentId !== undefined && parentId !== null) {
      const parentExists = await prisma.position.findUnique({
        where: { id: parentId }
      });

      if (!parentExists) {
        return NextResponse.json({ error: 'Parent position does not exist' }, { status: 400 });
      }

      // Prevent circular reference
      if (parentId === id) {
        return NextResponse.json({ error: 'Position cannot be its own parent' }, { status: 400 });
      }
    }

    // Build update data object with only provided fields
    const updateData: any = {};
    if (title !== undefined) updateData.title = title.trim();
    if (description !== undefined) updateData.description = description?.trim() || null;
    if (level !== undefined) updateData.level = level;
    if (parentId !== undefined) updateData.parentId = parentId;
    if (isActive !== undefined) updateData.isActive = isActive;

    // Get the current position before updating for audit logging
    const currentPosition = await prisma.position.findUnique({
      where: { id }
    });

    const position = await prisma.position.update({
      where: { id },
      data: updateData
    });

    // Log the partial update
    try {
      await prisma.auditLog.create({
        data: {
          userId: (session.user as any).id,
          action: 'UPDATE',
          resource: 'positions',
          resourceId: position.id,
          oldValues: currentPosition ? {
            title: currentPosition.title,
            description: currentPosition.description,
            level: currentPosition.level,
            parentId: currentPosition.parentId,
            isActive: currentPosition.isActive
          } : undefined,
          newValues: {
            title: position.title,
            description: position.description,
            level: position.level,
            parentId: position.parentId,
            isActive: position.isActive
          },
          success: true,
          statusCode: 200,
          apiEndpoint: `/api/positions/positions/${id}`,
          requestMethod: 'PATCH'
        }
      });
    } catch (auditError) {
      console.error('Failed to create audit log for position partial update:', auditError);
      // Don't fail the main operation if audit logging fails
    }

    return NextResponse.json(position);
  } catch (error: any) {
    if (error.code === 'P2025') {
      return NextResponse.json({ error: 'Position not found' }, { status: 404 });
    }

    console.error('Error updating position:', error);
    return NextResponse.json({ error: 'Failed to update position' }, { status: 500 });
  }
}

// PUT /api/positions/positions/[id] - Update a position
export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { title, description, level, parentId, isActive } = body;

    // Validate input
    if (!title || title.trim().length === 0) {
      return NextResponse.json({ error: 'Position title is required' }, { status: 400 });
    }

    // Check if another position already exists with this title
    const existingPosition = await prisma.position.findFirst({
      where: {
        title: title.trim(),
        NOT: { id }
      }
    });

    if (existingPosition) {
      return NextResponse.json({ error: 'A position with this title already exists' }, { status: 400 });
    }

    // Validate parentId exists if provided
    if (parentId !== undefined && parentId !== null) {
      const parentExists = await prisma.position.findUnique({
        where: { id: parentId }
      });

      if (!parentExists) {
        return NextResponse.json({ error: 'Parent position does not exist' }, { status: 400 });
      }

      // Prevent circular reference
      if (parentId === id) {
        return NextResponse.json({ error: 'Position cannot be its own parent' }, { status: 400 });
      }
    }

    // Get the current position before updating for audit logging
    const currentPosition = await prisma.position.findUnique({
      where: { id }
    });

    const position = await prisma.position.update({
      where: { id },
      data: {
        title: title.trim(),
        description: description?.trim() || null,
        level: level || 1,
        parentId: parentId || null,
        isActive: typeof isActive === 'boolean' ? isActive : undefined
      }
    });

    // Log the update
    try {
      await prisma.auditLog.create({
        data: {
          userId: (session.user as any).id,
          action: 'UPDATE',
          resource: 'positions',
          resourceId: position.id,
          oldValues: currentPosition ? {
            title: currentPosition.title,
            description: currentPosition.description,
            level: currentPosition.level,
            parentId: currentPosition.parentId,
            isActive: currentPosition.isActive
          } : undefined,
          newValues: {
            title: position.title,
            description: position.description,
            level: position.level,
            parentId: position.parentId,
            isActive: position.isActive
          },
          success: true,
          statusCode: 200,
          apiEndpoint: `/api/positions/positions/${id}`,
          requestMethod: 'PUT'
        }
      });
    } catch (auditError) {
      console.error('Failed to create audit log for position update:', auditError);
      // Don't fail the main operation if audit logging fails
    }

    return NextResponse.json(position);
  } catch (error) {
    console.error('Error updating position:', error);
    return NextResponse.json({ error: 'Failed to update position' }, { status: 500 });
  }
}

// DELETE /api/positions/positions/[id] - Delete a position
export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;

    // Check if position is being used by any users
    const userPositionCount = await prisma.userPosition.count({
      where: { positionId: id }
    });

    if (userPositionCount > 0) {
      return NextResponse.json({ 
        error: 'Cannot delete position because it is assigned to users' 
      }, { status: 400 });
    }

    // Get the position before soft deleting for audit logging
    const positionToDelete = await prisma.position.findUnique({
      where: { id }
    });

    if (!positionToDelete) {
      return NextResponse.json({ error: 'Position not found' }, { status: 404 });
    }

    // Soft delete the position by setting isActive to false
    const updatedPosition = await prisma.position.update({
      where: { id },
      data: { isActive: false }
    });

    // Log the soft deletion
    try {
      await prisma.auditLog.create({
        data: {
          userId: (session.user as any).id,
          action: 'DELETE',
          resource: 'positions',
          resourceId: id,
          oldValues: {
            title: positionToDelete.title,
            description: positionToDelete.description,
            level: positionToDelete.level,
            parentId: positionToDelete.parentId,
            isActive: positionToDelete.isActive
          },
          newValues: {
            title: updatedPosition.title,
            description: updatedPosition.description,
            level: updatedPosition.level,
            parentId: updatedPosition.parentId,
            isActive: updatedPosition.isActive
          },
          success: true,
          statusCode: 200,
          apiEndpoint: `/api/positions/positions/${id}`,
          requestMethod: 'DELETE'
        }
      });
    } catch (auditError) {
      console.error('Failed to create audit log for position soft deletion:', auditError);
      // Don't fail the main operation if audit logging fails
    }

    return NextResponse.json({
      message: 'Position deactivated successfully',
      position: updatedPosition
    });
  } catch (error: any) {
    if (error.code === 'P2025') {
      return NextResponse.json({ error: 'Position not found' }, { status: 404 });
    }

    console.error('Error deactivating position:', error);
    return NextResponse.json({ error: 'Failed to deactivate position' }, { status: 500 });
  }
}
