import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/positions/positions/[id]/assignments - Get assignment count for a position
export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;

    // Find the active assignment for this position
    const assignment = await prisma.userPosition.findFirst({
      where: { 
        positionId: id,
        isActive: true // Only count active assignments
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            profile: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        }
      }
    });

    const count = assignment ? 1 : 0;
    
    return NextResponse.json({ 
      count,
      assignedUserId: assignment?.user?.id || null,
      assignedUserName: assignment?.user?.profile 
        ? `${assignment.user.profile.firstName} ${assignment.user.profile.lastName}`.trim()
        : assignment?.user?.name || null
    });
  } catch (error) {
    console.error('Error fetching position assignments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch position assignments' },
      { status: 500 }
    );
  }
}