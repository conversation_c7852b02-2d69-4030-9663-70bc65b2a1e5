import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// PATCH /api/positions/positions/[id]/restore - Restore a soft-deleted position
export async function PATCH(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;

    // Get the position before restoring for audit logging
    const positionToRestore = await prisma.position.findUnique({
      where: { id }
    });

    if (!positionToRestore) {
      return NextResponse.json({ error: 'Position not found' }, { status: 404 });
    }

    if (positionToRestore.isActive) {
      return NextResponse.json({ error: 'Position is already active' }, { status: 400 });
    }

    // Restore the position by setting isActive to true
    const restoredPosition = await prisma.position.update({
      where: { id },
      data: { isActive: true }
    });

    // Log the restoration
    try {
      await prisma.auditLog.create({
        data: {
          userId: (session.user as any).id,
          action: 'UPDATE',
          resource: 'positions',
          resourceId: id,
          oldValues: {
            title: positionToRestore.title,
            description: positionToRestore.description,
            level: positionToRestore.level,
            parentId: positionToRestore.parentId,
            isActive: positionToRestore.isActive
          },
          newValues: {
            title: restoredPosition.title,
            description: restoredPosition.description,
            level: restoredPosition.level,
            parentId: restoredPosition.parentId,
            isActive: restoredPosition.isActive
          },
          success: true,
          statusCode: 200,
          apiEndpoint: `/api/positions/positions/${id}/restore`,
          requestMethod: 'PATCH'
        }
      });
    } catch (auditError) {
      console.error('Failed to create audit log for position restoration:', auditError);
      // Don't fail the main operation if audit logging fails
    }

    return NextResponse.json({
      message: 'Position restored successfully',
      position: restoredPosition
    });
  } catch (error: any) {
    console.error('Error restoring position:', error);
    return NextResponse.json({ error: 'Failed to restore position' }, { status: 500 });
  }
}