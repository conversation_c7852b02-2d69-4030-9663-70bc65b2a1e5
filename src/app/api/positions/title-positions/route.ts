import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/positions/title-positions?titleId={id} - Get positions for a title
// GET /api/positions/title-positions?positionId={id} - Get titles for a position
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const titleId = searchParams.get('titleId');
    const positionId = searchParams.get('positionId');

    let titlePositions;
    if (titleId) {
      // Get all positions associated with a specific title
      titlePositions = await prisma.titlePosition.findMany({
        where: { titleId },
        include: {
          position: true
        }
      });
    } else if (positionId) {
      // Get all titles associated with a specific position
      titlePositions = await prisma.titlePosition.findMany({
        where: { positionId },
        include: {
          title: true
        }
      });
    } else {
      // Get all title-position relationships
      titlePositions = await prisma.titlePosition.findMany({
        include: {
          title: true,
          position: true
        }
      });
    }

    return NextResponse.json(titlePositions);
  } catch (error) {
    console.error('Error fetching title-position relationships:', error);
  return NextResponse.json({ error: 'Failed to fetch title-position relationships' }, { status: 500 });
  }
}

// POST /api/positions/title-positions - Associate a title with a position
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { titleId, positionId } = body;

    // Validate input
    if (!titleId || !positionId) {
      return NextResponse.json({ error: 'Both titleId and positionId are required' }, { status: 400 });
    }

    // Check if relationship already exists
    const existingRelationship = await prisma.titlePosition.findUnique({
      where: {
        titleId_positionId: {
          titleId,
          positionId
        }
      }
    });

    if (existingRelationship) {
      return NextResponse.json({ error: 'This title-position relationship already exists' }, { status: 400 });
    }

    // Verify that both title and position exist
    const title = await prisma.title.findUnique({ where: { id: titleId } });
    const position = await prisma.position.findUnique({ where: { id: positionId } });

    if (!title) {
      return NextResponse.json({ error: 'Title not found' }, { status: 404 });
    }

    if (!position) {
      return NextResponse.json({ error: 'Position not found' }, { status: 404 });
    }

    const titlePosition = await prisma.titlePosition.create({
      data: {
        titleId,
        positionId
      },
      include: {
        title: true,
        position: true
      }
    });

    return NextResponse.json(titlePosition, { status: 201 });
  } catch (error) {
    console.error('Error creating title-position relationship:', error);
    return NextResponse.json({ error: 'Failed to create title-position relationship' }, { status: 500 });
  }
}

// DELETE /api/positions/title-positions - Remove a title-position relationship
export async function DELETE(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name.toLowerCase() === 'admin');

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { titleId, positionId } = body;

    // Validate input
    if (!titleId || !positionId) {
      return NextResponse.json({ error: 'Both titleId and positionId are required' }, { status: 400 });
    }

    // Check if relationship exists
    const existingRelationship = await prisma.titlePosition.findUnique({
      where: {
        titleId_positionId: {
          titleId,
          positionId
        }
      }
    });

    if (!existingRelationship) {
      return NextResponse.json({ error: 'Title-position relationship not found' }, { status: 404 });
    }

    // Delete the relationship
    await prisma.titlePosition.delete({
      where: {
        titleId_positionId: {
          titleId,
          positionId
        }
      }
    });

    return NextResponse.json({ message: 'Title-position relationship removed successfully' });
  } catch (error) {
    console.error('Error deleting title-position relationship:', error);
    return NextResponse.json({ error: 'Failed to delete title-position relationship' }, { status: 500 });
  }
}