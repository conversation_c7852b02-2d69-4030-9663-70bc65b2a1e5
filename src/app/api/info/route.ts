import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/middleware/require-auth'
import { checkRateLimit } from '@/lib/middleware/rate-limiting'
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging'
import { AuthenticationError, AuthorizationError } from '@/lib/errors'

export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting for info endpoint
    const rateLimitResult = await checkRateLimit(request, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 60, // 60 requests per minute for info
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        request,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Check if user is authenticated
    let authContext;
    try {
      authContext = await requireAuth(request, {
        requirePermissions: [], // No specific permissions required for basic info
        auditLogging: false, // Don't log basic info requests
      });
    } catch (error) {
      // User is not authenticated, provide public information only
      const publicInfo = {
        name: 'NWA User Portal',
        description: 'A comprehensive user management system for the NWA Alliance',
        version: '1.0.0',
        features: [
          'User Management',
          'Role-Based Access Control',
          'Remote Server Integration',
          'Audit Logging',
          'File Management'
        ],
        endpoints: {
          public: [
            '/api/info',
            '/api/oauth/authorize',
            '/api/oauth/token',
            '/api/oauth/userinfo'
          ]
        },
        authentication: {
          required: true,
          methods: ['OAuth 2.0', 'JWT'],
          oauth: {
            authorization_endpoint: '/api/oauth/authorize',
            token_endpoint: '/api/oauth/token',
            userinfo_endpoint: '/api/oauth/userinfo'
          }
        },
        security: {
          rate_limiting: true,
          audit_logging: true,
          input_validation: true
        }
      };

      // Log public access
      await auditLoggingMiddleware.logApiAccess(
        undefined,
        'read',
        'public_info',
        true,
        request,
        {
          metadata: {
            accessLevel: 'public',
            infoType: 'basic'
          }
        }
      );

      return NextResponse.json(publicInfo);
    }

    // User is authenticated, provide authenticated user information
    const authenticatedInfo = {
      name: 'NWA User Portal',
      description: 'A comprehensive user management system for the NWA Alliance',
      version: '1.0.0',
      user: {
        id: authContext.userId,
        roles: authContext.roles || [],
        permissions: authContext.permissions || []
      },
      features: [
        'User Management',
        'Role-Based Access Control',
        'Remote Server Integration',
        'Audit Logging',
        'File Management',
        'Permission Management',
        'User Authentication',
        'API Security'
      ],
      endpoints: {
        authenticated: [
          '/api/user/profile',
          '/api/user/notifications',
          '/api/files/*',
          '/api/audit/logs'
        ],
        admin: [
          '/api/admin/*',
          '/api/permissions',
          '/api/remote-servers/*'
        ]
      },
      security: {
        rate_limiting: true,
        audit_logging: true,
        input_validation: true,
        authentication_required: true,
        authorization_enforced: true
      },
      system: {
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString(),
        request_id: request.headers.get('x-request-id') || 'unknown'
      }
    };

    // Log authenticated access
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'read',
      'authenticated_info',
      true,
      request,
      {
        metadata: {
          accessLevel: 'authenticated',
          infoType: 'detailed',
          userRoles: authContext.roles,
          userPermissions: authContext.permissions
        }
      }
    );

    return NextResponse.json(authenticatedInfo);
  } catch (error) {
    console.error('Info endpoint error:', error);

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    // Handle unknown error types safely
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

    return NextResponse.json(
      { error: 'Internal Server Error', message: errorMessage },
      { status: 500 }
    );
  }
}