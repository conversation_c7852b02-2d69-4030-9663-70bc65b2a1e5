import { NextRequest, NextResponse } from 'next/server';
import { Client } from 'minio';
import { requireAuth } from '@/lib/middleware/require-auth';
import { checkRateLimit } from '@/lib/middleware/rate-limiting';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';
import { AuthenticationError, AuthorizationError } from '@/lib/errors';

// Allowed file types and their MIME types
const ALLOWED_FILE_TYPES = {
  'jpg': 'image/jpeg',
  'jpeg': 'image/jpeg',
  'png': 'image/png',
  'gif': 'image/gif',
  'webp': 'image/webp',
  'svg': 'image/svg+xml',
  'pdf': 'application/pdf',
  'txt': 'text/plain',
  'json': 'application/json',
  'csv': 'text/csv',
  'xml': 'application/xml',
  'doc': 'application/msword',
  'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'xls': 'application/vnd.ms-excel',
  'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'ppt': 'application/vnd.ms-powerpoint',
  'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
} as const;

const ALLOWED_EXTENSIONS = Object.keys(ALLOWED_FILE_TYPES);
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB limit

function sanitizeFilename(filename: string): string {
  // Remove path traversal attempts
  const sanitized = filename.replace(/\.\./g, '').replace(/[\/\\]/g, '');
  return sanitized;
}

function getFileExtension(filename: string): string {
  const lastDot = filename.lastIndexOf('.');
  if (lastDot === -1) return '';
  return filename.substring(lastDot + 1).toLowerCase();
}

function isAllowedFileType(filename: string): boolean {
  const extension = getFileExtension(filename);
  return ALLOWED_EXTENSIONS.includes(extension as keyof typeof ALLOWED_FILE_TYPES);
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ filename: string }> }
) {
  try {
    const { filename } = await params;

    // Sanitize filename to prevent path traversal
    const sanitizedFilename = sanitizeFilename(filename);

    if (sanitizedFilename !== filename) {
      await auditLoggingMiddleware.logApiAccess(
        undefined,
        'security_event',
        'file_access',
        false,
        request,
        {
          statusCode: 400,
          errorMessage: 'Path traversal detected',
          metadata: {
            attemptedFilename: filename,
            sanitizedFilename: sanitizedFilename,
            severity: 'high',
            eventType: 'path_traversal_attempt'
          }
        }
      );

      return NextResponse.json(
        { error: 'Invalid filename', message: 'Path traversal detected' },
        { status: 400 }
      );
    }

    // Check if file type is allowed
    if (!isAllowedFileType(sanitizedFilename)) {
      await auditLoggingMiddleware.logApiAccess(
        undefined,
        'security_event',
        'file_access',
        false,
        request,
        {
          statusCode: 403,
          errorMessage: 'File type not allowed',
          metadata: {
            filename: sanitizedFilename,
            extension: getFileExtension(sanitizedFilename),
            severity: 'medium',
            eventType: 'disallowed_file_type'
          }
        }
      );

      return NextResponse.json(
        { error: 'File type not allowed', message: 'This file type is not permitted' },
        { status: 403 }
      );
    }

    // Apply rate limiting for file access
    const rateLimitResult = await checkRateLimit(request, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100, // 100 file requests per minute
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        request,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Require authentication for file access
    const authContext = await requireAuth(request, {
      requirePermissions: ['files:read'],
      auditLogging: true,
    });

    // Initialize MinIO client
    const endpoint = process.env.MINIO_ENDPOINT || 'localhost';
    const port = parseInt(process.env.MINIO_PORT || '9000', 10);
    const accessKey = process.env.MINIO_ACCESS_KEY || 'minioadmin';
    const secretKey = process.env.MINIO_SECRET_KEY || 'minioadmin123';
    const bucketName = process.env.MINIO_BUCKET_NAME || 'nwa-uploads';

    const client = new Client({
      endPoint: endpoint,
      port: port,
      useSSL: process.env.MINIO_USE_SSL === 'true',
      accessKey: accessKey,
      secretKey: secretKey,
    });

    // Check if file exists and get metadata
    let stat;
    try {
      stat = await client.statObject(bucketName, sanitizedFilename);
    } catch (error: any) {
      if (error.code === 'NoSuchKey' || error.code === 'NotFound') {
        await auditLoggingMiddleware.logApiAccess(
          authContext.userId,
          'read',
          'file',
          false,
          request,
          {
            statusCode: 404,
            errorMessage: 'File not found',
            metadata: { filename: sanitizedFilename },
          }
        );

        return NextResponse.json({ error: 'File not found' }, { status: 404 });
      }
      throw error;
    }

    // Check file size limit
    if (stat.size > MAX_FILE_SIZE) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'security_event',
        'file_access',
        false,
        request,
        {
          statusCode: 413,
          errorMessage: 'File too large',
          metadata: {
            filename: sanitizedFilename,
            fileSize: stat.size,
            maxSize: MAX_FILE_SIZE,
            severity: 'medium',
            eventType: 'file_too_large'
          }
        }
      );

      return NextResponse.json(
        { error: 'File too large', message: 'File exceeds maximum size limit' },
        { status: 413 }
      );
    }

    // Get the object stream
    const objectStream = await client.getObject(bucketName, sanitizedFilename);

    // Convert stream to buffer
    const chunks: Buffer[] = [];
    for await (const chunk of objectStream) {
      chunks.push(chunk);
    }
    const buffer = Buffer.concat(chunks);

    // Determine content type
    const extension = getFileExtension(sanitizedFilename);
    const contentType = ALLOWED_FILE_TYPES[extension as keyof typeof ALLOWED_FILE_TYPES] || 'application/octet-stream';

    // Log successful file access
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'read',
      'file',
      true,
      request,
      {
        metadata: {
          filename: sanitizedFilename,
          fileSize: stat.size,
          contentType: contentType,
          extension: extension,
        },
      }
    );

    // Return the file with appropriate content type and security headers
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Length': String(buffer.length),
        'Cache-Control': 'public, max-age=3600', // 1 hour cache for security
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'Content-Security-Policy': "default-src 'none'",
      },
    });
  } catch (error) {
    console.error('Error serving file:', error);

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    // Handle unknown error types safely
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

    return NextResponse.json(
      { error: 'Internal Server Error', message: errorMessage },
      { status: 500 }
    );
  }
}