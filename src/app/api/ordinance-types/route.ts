import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const categoriesOnly = searchParams.get('categories') === 'true';

    if (categoriesOnly) {
      const categories = await prisma.ordinanceType.findMany({
        select: {
          category: true,
        },
        distinct: ['category'],
      });

      return NextResponse.json({
        categories: categories.map(c => c.category),
      });
    }

    // Build where clause for search
    const where: any = {
      isActive: true,
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (category) {
      where.category = category;
    }

    // Fetch ordinance types
    const ordinanceTypes = await prisma.ordinanceType.findMany({
      where,
      orderBy: {
        name: 'asc',
      },
    });

    // If no ordinance types found, return empty array
    if (ordinanceTypes.length === 0) {
      return NextResponse.json({
        ordinanceTypes: [],
      });
    }

    return NextResponse.json({
      ordinanceTypes: ordinanceTypes.map(type => ({
        id: type.id,
        name: type.name,
        description: type.description,
        category: type.category,
        isActive: type.isActive,
        createdAt: type.createdAt,
        updatedAt: type.updatedAt,
      })),
    });
  } catch (error) {
    console.error('Error fetching ordinance types:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}