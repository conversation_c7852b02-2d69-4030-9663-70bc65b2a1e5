import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { unifiedPermissions } from '@/lib/services/unified-permissions'
import { ServerAssignmentAuditService } from '@/lib/services/server-assignment-audit-service'

// GET /api/roles/[id]/remote-servers - Get role's assigned remote servers
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: roleId } = await params

    // Check if role exists
    const role = await prisma.role.findUnique({
      where: { id: roleId }
    })

    if (!role) {
      return Response.json({ error: 'Role not found' }, { status: 404 })
    }

    // Get role's remote server access
    const roleServerAccess = await prisma.role_remote_server_access.findMany({
      where: {
        role_id: roleId,
        is_active: true
      },
      include: {
        remote_servers: {
          select: {
            id: true,
            name: true,
            url: true,
            description: true,
            isActive: true
          }
        },
        users: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    const result = roleServerAccess.map(access => ({
      id: access.id,
      roleId: access.role_id,
      serverId: access.remote_server_id,
      server: access.remote_servers,
      autoGrantPermissions: access.auto_grant_permissions,
      createdAt: access.created_at,
      createdBy: access.users
    }))

    return Response.json({ success: true, data: result })
  } catch (error) {
    console.error('Error fetching role remote servers:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/roles/[id]/remote-servers - Assign role to remote server
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: roleId } = await params
    const body = await request.json()
    const { serverId, autoGrantPermissions = [] } = body

    if (!serverId) {
      return Response.json(
        { error: 'Server ID is required' },
        { status: 400 }
      )
    }

    // Verify role exists
    const role = await prisma.role.findUnique({
      where: { id: roleId }
    })

    if (!role) {
      return Response.json({ error: 'Role not found' }, { status: 404 })
    }

    // Verify server exists
    const server = await prisma.remoteServer.findUnique({
      where: { id: serverId }
    })

    if (!server) {
      return Response.json({ error: 'Remote server not found' }, { status: 404 })
    }

    // Check if assignment already exists
    const existingAccess = await prisma.role_remote_server_access.findUnique({
      where: {
        role_id_remote_server_id: {
          role_id: roleId,
          remote_server_id: serverId
        }
      }
    })

    if (existingAccess) {
      return Response.json(
        { error: 'Role already has access to this remote server' },
        { status: 400 }
      )
    }

    // Verify permissions exist on the remote server if auto-grant is specified
    if (autoGrantPermissions.length > 0) {
      const serverPermissions = await prisma.remote_server_permissions.findMany({
        where: {
          remote_server_id: serverId,
          permission_name: { in: autoGrantPermissions },
          is_active: true
        }
      })

      const validPermissions = serverPermissions.map(p => p.permission_name)
      const invalidPermissions = autoGrantPermissions.filter((p: string) => !validPermissions.includes(p))

      if (invalidPermissions.length > 0) {
        return Response.json(
          { 
            error: 'Invalid permissions for this server',
            invalidPermissions 
          },
          { status: 400 }
        )
      }
    }

    // Create role remote server access
    const roleAccess = await prisma.role_remote_server_access.create({
      data: {
        role_id: roleId,
        remote_server_id: serverId,
        auto_grant_permissions: autoGrantPermissions,
        created_by: (session.user as any).id
      }
    })

    // Log the role server assignment audit
    try {
      const auditService = new ServerAssignmentAuditService()
      await auditService.logServerAssignment({
        userId: '', // Empty string for role assignments
        roleId,
        serverId,
        serverName: server.name,
        action: 'assign',
        autoGrantPermissions,
        performedBy: (session.user as any).id,
        metadata: {
          accessId: roleAccess.id,
          autoGrantPermissionsCount: autoGrantPermissions.length
        }
      })
    } catch (auditError) {
      console.error('Failed to log role server assignment audit:', auditError)
      // Don't fail the assignment if audit logging fails
    }

    return Response.json({
      success: true,
      data: {
        accessId: roleAccess.id,
        autoGrantPermissions: autoGrantPermissions.length
      }
    })
  } catch (error) {
    console.error('Error assigning role to remote server:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/roles/[id]/remote-servers - Remove role from remote server
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: roleId } = await params
    const { searchParams } = new URL(request.url)
    const serverId = searchParams.get('serverId')

    if (!serverId) {
      return Response.json(
        { error: 'Server ID is required' },
        { status: 400 }
      )
    }

    // Get server info for audit logging before removal
    const server = await prisma.remoteServer.findUnique({
      where: { id: serverId },
      select: { name: true }
    })

    if (!server) {
      return Response.json({ error: 'Remote server not found' }, { status: 404 })
    }

    // Log the role server removal audit before actually removing
    try {
      const auditService = new ServerAssignmentAuditService()
      await auditService.logServerAssignment({
        userId: '', // Empty string for role assignments
        roleId,
        serverId,
        serverName: server.name,
        action: 'remove',
        performedBy: (session.user as any).id,
        metadata: {
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown'
        }
      })
    } catch (auditError) {
      console.error('Failed to log role server removal audit:', auditError)
      // Don't fail the removal if audit logging fails
    }

    // Remove role access
    await prisma.role_remote_server_access.deleteMany({
      where: {
        role_id: roleId,
        remote_server_id: serverId
      }
    })

    return Response.json({ success: true })
  } catch (error) {
    console.error('Error removing role from remote server:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}