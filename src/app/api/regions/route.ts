import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { performanceMonitor } from '@/lib/performance';

// Simple in-memory cache for regions
const regionsCache = new Map<string, any>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

interface CacheEntry {
  data: any;
  timestamp: number;
}

function getCacheKey(countryId: number, active: string): string {
  return `${countryId}-${active}`;
}

function getFromCache(key: string): any | null {
  const entry = regionsCache.get(key) as CacheEntry;
  if (entry && (Date.now() - entry.timestamp) < CACHE_DURATION) {
    return entry.data;
  }
  return null;
}

function setCache(key: string, data: any): void {
  regionsCache.set(key, {
    data,
    timestamp: Date.now()
  });
}

export async function GET(request: NextRequest) {
  performanceMonitor.startTimer('api_regions_get', { endpoint: '/api/regions' });
  try {
    const { searchParams } = new URL(request.url);
    const countryIdParam = searchParams.get('countryId');
    const activeParam = searchParams.get('active') || 'true';

    // Validate countryId parameter
    if (!countryIdParam) {
      return NextResponse.json(
        {
          error: 'Bad Request',
          message: 'countryId parameter is required'
        },
        { status: 400 }
      );
    }

    const countryId = parseInt(countryIdParam, 10);
    if (isNaN(countryId) || countryId <= 0) {
      return NextResponse.json(
        {
          error: 'Bad Request',
          message: 'countryId must be a valid integer'
        },
        { status: 400 }
      );
    }

    // Check if country exists
    const country = await prisma.country.findUnique({
      where: { id: countryId }
    });

    if (!country) {
      return NextResponse.json(
        {
          error: 'Not Found',
          message: 'Country not found'
        },
        { status: 404 }
      );
    }

    // Build where clause for regions query
    const whereClause: any = {
      countryId: countryId
    };

    // Handle active parameter
    if (activeParam !== 'all') {
      const isActive = activeParam === 'true';
      whereClause.isActive = isActive;
    }

    // Check cache first
    const cacheKey = getCacheKey(countryId, activeParam);
    const cachedRegions = getFromCache(cacheKey);
    if (cachedRegions) {
      return NextResponse.json({ regions: cachedRegions }, { status: 200 });
    }

    // Fetch regions for the country
    const regions = await prisma.region.findMany({
      where: whereClause,
      orderBy: {
        name: 'asc'
      }
    });

    // Cache the result
    setCache(cacheKey, regions);

    return NextResponse.json({ regions }, { status: 200 });

  } catch (error) {
    console.error('Error fetching regions:', error);
    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'An error occurred while fetching regions'
      },
      { status: 500 }
    );
  } finally {
    performanceMonitor.endTimer('api_regions_get');
  }
}
