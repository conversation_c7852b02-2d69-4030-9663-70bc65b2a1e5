import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/middleware/require-auth';
import { checkRateLimit } from '@/lib/middleware/rate-limiting';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';
import { AuthenticationError, AuthorizationError } from '@/lib/errors';

export async function GET(req: NextRequest) {
  try {
    // Apply strict rate limiting for audit log access
    const rateLimitResult = await checkRateLimit(req, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 20, // 20 audit log requests per minute
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        req,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Require admin authentication for audit log access
    const authContext = await requireAuth(req, {
      requirePermissions: ['audit:read'],
      auditLogging: true,
    });

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '50', 10);
    const resource = searchParams.get('resource') || '';
    const action = searchParams.get('action') || '';
    const userId = searchParams.get('userId') || '';
    const success = searchParams.get('success');

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'read',
        'audit_logs',
        false,
        req,
        {
          statusCode: 400,
          errorMessage: 'Invalid pagination parameters',
          metadata: { page, limit, resource, action, userId, success }
        }
      );

      return NextResponse.json(
        { error: 'Bad Request', message: 'Invalid pagination parameters' },
        { status: 400 }
      );
    }

    const skip = (page - 1) * limit;

    // Sanitize search inputs to prevent injection attacks
    const sanitizedResource = resource.replace(/[<>]/g, '').trim();
    const sanitizedAction = action.replace(/[<>]/g, '').trim();
    const sanitizedUserId = userId.replace(/[<>]/g, '').trim();

    // Build where clause with sanitized inputs
    const where: any = {};

    if (sanitizedResource) {
      where.resource = { contains: sanitizedResource, mode: 'insensitive' };
    }

    if (sanitizedAction) {
      where.action = { contains: sanitizedAction, mode: 'insensitive' };
    }

    if (sanitizedUserId) {
      where.userId = sanitizedUserId;
    }

    if (success !== null) {
      where.success = success === 'true';
    }

    // Fetch audit logs with pagination
    const [logs, totalCount] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        skip,
        take: limit,
      }),
      prisma.auditLog.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    // Process logs to include user information and sanitize sensitive data
    const processedLogs = logs.map(log => ({
      id: log.id,
      userId: log.userId,
      userName: log.user?.name || 'Unknown User',
      userEmail: log.user?.email || '',
      action: log.action,
      resource: log.resource,
      resourceId: log.resourceId || '',
      success: log.success,
      ipAddress: log.ipAddress,
      timestamp: log.timestamp,
      // Sanitize sensitive data in audit logs
      oldValues: log.oldValues ? sanitizeAuditData(log.oldValues) : null,
      newValues: log.newValues ? sanitizeAuditData(log.newValues) : null,
    }));

    // Log successful audit log access (meta-audit)
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'read',
      'audit_logs',
      true,
      req,
      {
        metadata: {
          pagination: { page, limit, totalCount, totalPages },
          filters: {
            resource: sanitizedResource,
            action: sanitizedAction,
            userId: sanitizedUserId,
            success: success
          },
          logsReturned: processedLogs.length
        }
      }
    );

    return NextResponse.json({
      logs: processedLogs,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        pageSize: limit,
      },
    });
  } catch (error) {
    console.error('Error fetching audit logs:', error);

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    // Handle unknown error types safely
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

    return NextResponse.json(
      { error: 'Internal Server Error', message: errorMessage },
      { status: 500 }
    );
  }
}

// Helper function to sanitize sensitive data in audit logs
function sanitizeAuditData(data: any): any {
  if (typeof data !== 'object' || data === null) {
    return data;
  }

  const sensitiveFields = ['password', 'passwordHash', 'token', 'secret', 'key', 'apiKey', 'accessToken', 'refreshToken'];
  const sanitized = { ...data };

  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }

  // Recursively sanitize nested objects
  for (const key in sanitized) {
    if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
      sanitized[key] = sanitizeAuditData(sanitized[key]);
    }
  }

  return sanitized;
}

export async function POST(req: NextRequest) {
  try {
    // Apply rate limiting for audit log creation
    const rateLimitResult = await checkRateLimit(req, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100, // 100 audit log entries per minute
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        req,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Require admin authentication for audit log creation
    const authContext = await requireAuth(req, {
      requirePermissions: ['audit:write'],
      auditLogging: true,
    });

    const body = await req.json();
    const {
      projectId,
      userId,
      action,
      resource,
      success,
      method,
      endpoint,
      statusCode,
      errorMessage,
      ipAddress,
      userAgent,
      duration,
      metadata
    } = body;

    // Validate required fields
    if (!action || !method || !endpoint || !ipAddress || !userAgent) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'create',
        'audit_log',
        false,
        req,
        {
          statusCode: 400,
          errorMessage: 'Missing required fields',
          metadata: {
            missingFields: {
              action: !action,
              method: !method,
              endpoint: !endpoint,
              ipAddress: !ipAddress,
              userAgent: !userAgent
            }
          }
        }
      );

      return NextResponse.json(
        { error: 'Missing required fields: action, method, endpoint, ipAddress, userAgent' },
        { status: 400 }
      );
    }

    // Create audit log entry - validate userId and projectId before including them
    const auditLogData: any = {
      action,
      resource: resource || '',
      requestMethod: method,
      apiEndpoint: endpoint,
      success: Boolean(success),
      statusCode: statusCode || null,
      errorMessage: errorMessage || null,
      ipAddress,
      userAgent,
      duration: duration || null,
      metadata: metadata || {},
    };

    // Validate userId exists before including it (required field)
    if (userId && userId.trim() !== '') {
      const existingUser = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (existingUser) {
        auditLogData.userId = userId;
      } else {
        // User doesn't exist, use the current authenticated user as fallback
        console.warn(`User with ID ${userId} does not exist, using authenticated user ${authContext.userId} for audit log`);
        auditLogData.userId = authContext.userId;
      }
    } else {
      // No userId provided, use the current authenticated user
      auditLogData.userId = authContext.userId;
    }

    // Only include projectId if provided and valid (it's optional in the schema)
    if (projectId && projectId.trim() !== '') {
      // Check if the project actually exists before including it
      const existingProject = await prisma.project.findUnique({
        where: { id: projectId }
      });

      if (existingProject) {
        auditLogData.projectId = projectId;
      } else {
        // Project doesn't exist, don't include projectId to avoid foreign key constraint
        console.warn(`Project with ID ${projectId} does not exist, skipping projectId in audit log`);
      }
    }

    // Sanitize metadata to prevent sensitive data storage
    if (auditLogData.metadata) {
      auditLogData.metadata = sanitizeAuditData(auditLogData.metadata);
    }

    const auditLog = await prisma.auditLog.create({
      data: auditLogData,
    });

    // Log successful audit log creation (meta-audit)
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'create',
      'audit_log',
      true,
      req,
      {
        metadata: {
          auditLogId: auditLog.id,
          action: action,
          resource: resource,
          success: Boolean(success),
          hasMetadata: !!metadata
        }
      }
    );

    return NextResponse.json({ success: true, id: auditLog.id });
  } catch (error) {
    console.error('Error creating audit log:', error);

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    // Handle unknown error types safely
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

    return NextResponse.json(
      { error: 'Internal Server Error', message: errorMessage },
      { status: 500 }
    );
  }
}