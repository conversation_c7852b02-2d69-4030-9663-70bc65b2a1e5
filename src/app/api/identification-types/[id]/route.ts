import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/middleware/require-auth';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const user = await requireAuth(request, { requireRoles: ['admin'] });

    const { id } = await params;
    const body = await request.json();
    const { name, description, category, isActive } = body;

    // Find the identification type
    const identificationType = await prisma.identificationType.findUnique({
      where: { id }
    });

    if (!identificationType) {
      return NextResponse.json(
        { error: 'Identification type not found' },
        { status: 404 }
      );
    }

    // Validate category if provided
    if (category) {
      const validCategories = ['government', 'military', 'professional', 'education', 'diplomatic'];
      if (!validCategories.includes(category)) {
        return NextResponse.json(
          { error: 'Invalid category' },
          { status: 400 }
        );
      }
    }

    // Check if new name conflicts with existing types (if name is being changed)
    if (name && name !== identificationType.name) {
      const existingType = await prisma.identificationType.findUnique({
        where: { name: name.trim() }
      });

      if (existingType) {
        return NextResponse.json(
          { error: 'Identification type with this name already exists' },
          { status: 409 }
        );
      }
    }

    const updatedIdentificationType = await prisma.identificationType.update({
      where: { id },
      data: {
        ...(name && { name: name.trim() }),
        ...(description !== undefined && { description: description?.trim() }),
        ...(category && { category }),
        ...(isActive !== undefined && { isActive })
      }
    });

    // Log the audit event
    await 
auditLoggingMiddleware.logApiAccess(
      
      user.userId,
      'update',
      'identification_types',
      true,
      request,
      {
        statusCode: 200,
        metadata: { identificationTypeId: id, changes: { name, description, category, isActive } }
      }
    );

    return NextResponse.json({ identificationType: updatedIdentificationType });
  } catch (error) {
    console.error('Error updating identification type:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const user = await requireAuth(request, { requireRoles: ['admin'] });

    const { id } = await params;

    // Check if identification type exists
    const identificationType = await prisma.identificationType.findUnique({
      where: { id }
    });

    if (!identificationType) {
      return NextResponse.json(
        { error: 'Identification type not found' },
        { status: 404 }
      );
    }

    // Check if identification type is in use
    const usageCount = await prisma.userIdentification.count({
      where: { identificationTypeId: id }
    });

    if (usageCount > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete identification type that is in use',
          code: 'ID_TYPE_IN_USE',
          details: { usageCount }
        },
        { status: 409 }
      );
    }

    await prisma.identificationType.delete({
      where: { id }
    });

    // Log the audit event
    await 
auditLoggingMiddleware.logApiAccess(
      
      user.userId,
      'delete',
      'identification_types',
      true,
      request,
      {
        statusCode: 200,
        metadata: { identificationTypeId: id, name: identificationType.name }
      }
    );

    return NextResponse.json({ message: 'Identification type deleted successfully' });
  } catch (error) {
    console.error('Error deleting identification type:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
