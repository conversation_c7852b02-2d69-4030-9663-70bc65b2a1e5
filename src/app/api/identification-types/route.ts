import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/middleware/require-auth';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth(request);

    const { searchParams } = new URL(request.url);
    const active = searchParams.get('active');
    const category = searchParams.get('category');

    const where: any = {};
    
    // Filter by active status (default to true)
    if (active !== null) {
      where.isActive = active === 'true';
    } else {
      where.isActive = true;
    }

    // Filter by category if provided
    if (category) {
      where.category = category;
    }

    const identificationTypes = await prisma.identificationType.findMany({
      where,
      orderBy: [
        { category: 'asc' },
        { name: 'asc' }
      ]
    });

    // Log the audit event
    await 
auditLoggingMiddleware.logApiAccess(
      
      user.userId,
      'read',
      'identification_types',
      true,
      request,
      {
        statusCode: 200,
        metadata: { count: identificationTypes.length, filters: { active, category } }
      }
    );

    return NextResponse.json({ identificationTypes });
  } catch (error) {
    console.error('Error fetching identification types:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth(request, { requireRoles: ['admin'] });

    const body = await request.json();
    const { name, description, category = 'government' } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // Validate category
    const validCategories = ['government', 'military', 'professional', 'education', 'diplomatic'];
    if (!validCategories.includes(category)) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      );
    }

    // Check if identification type already exists
    const existingType = await prisma.identificationType.findUnique({
      where: { name }
    });

    if (existingType) {
      return NextResponse.json(
        { error: 'Identification type with this name already exists' },
        { status: 409 }
      );
    }

    const identificationType = await prisma.identificationType.create({
      data: {
        name: name.trim(),
        description: description?.trim(),
        category
      }
    });

    // Log the audit event
    await 
auditLoggingMiddleware.logApiAccess(
      
      user.userId,
      'create',
      'identification_types',
      true,
      request,
      {
        statusCode: 201,
        metadata: { identificationTypeId: identificationType.id, name }
      }
    );

    return NextResponse.json({ identificationType }, { status: 201 });
  } catch (error) {
    console.error('Error creating identification type:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
