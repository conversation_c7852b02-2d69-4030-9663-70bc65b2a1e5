import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const ordinanceTypes = await prisma.ordinanceType.count();
    const activeAssignments = await prisma.ordinance.count({
      where: { status: 'ACTIVE' },
    });
    const recentDocuments = await prisma.ordinanceDocument.count({
      where: {
        uploadedAt: {
          gte: new Date(new Date().setDate(new Date().getDate() - 30)),
        },
      },
    });

    return NextResponse.json({
      ordinanceTypes,
      activeAssignments,
      recentDocuments,
    });
  } catch (error) {
    console.error('Error fetching stats:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

