import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';

// Initialize S3 client for MinIO
const s3Client = new S3Client({
  endpoint: `${process.env.MINIO_ENDPOINT}:${process.env.MINIO_PORT}`,
  region: process.env.MINIO_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.MINIO_ACCESS_KEY || '',
    secretAccessKey: process.env.MINIO_SECRET_KEY || '',
  },
  forcePathStyle: true, // Required for MinIO
});

export async function POST(req: NextRequest) {
  try {
    console.log('Starting document upload process...');
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      console.log('Unauthorized access attempt');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const ordinanceId = searchParams.get('ordinanceId');
    
    if (!ordinanceId) {
      console.log('Ordinance ID is missing');
      return NextResponse.json({ error: 'Ordinance ID is required' }, { status: 400 });
    }
    
    console.log('Checking if ordinance exists for user:', (session.user as any).id);
    // Check if the ordinance belongs to the user
    const ordinance = await prisma.ordinance.findUnique({
      where: {
        id: ordinanceId,
        userId: (session.user as any).id,
      },
    });
    
    if (!ordinance) {
      console.log('Ordinance not found for user');
      return NextResponse.json({ error: 'Ordinance not found' }, { status: 404 });
    }
    
    console.log('Parsing form data...');
    const formData = await req.formData();
    const file = formData.get('document') as File;
    
    if (!file) {
      console.log('No document file provided');
      return NextResponse.json({ error: 'Document file is required' }, { status: 400 });
    }
    
    console.log('File received:', file.name, file.size, file.type);
    
    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      console.log('File size exceeds limit');
      return NextResponse.json({ error: 'File size exceeds 5MB limit' }, { status: 400 });
    }
    
    // Validate file type
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
      console.log('Invalid file type:', file.type);
      return NextResponse.json({ error: 'Invalid file type. Please upload a PDF, DOC, or DOCX file.' }, { status: 400 });
    }
    
    // Check if the ordinance already has documents (we'll allow multiple)
    const existingDocuments = await prisma.ordinanceDocument.count({
      where: { ordinanceId }
    });
    
    // Optional: You can set a limit on number of documents per ordinance
    const MAX_DOCUMENTS_PER_ORDINANCE = 10;
    if (existingDocuments >= MAX_DOCUMENTS_PER_ORDINANCE) {
      console.log('Ordinance has reached maximum document limit');
      return NextResponse.json({ error: 'Maximum document limit reached for this ordinance' }, { status: 400 });
    }
    
    // Generate a unique file name
    const fileExtension = file.name.split('.').pop();
    const uniqueFileName = `${uuidv4()}.${fileExtension}`;
    console.log('Generated unique file name:', uniqueFileName);
    
    // Upload file to MinIO
    const bucketName = process.env.MINIO_BUCKET_NAME || 'nwa-files';
    const fileKey = `ordinances/${ordinanceId}/${uniqueFileName}`;
    console.log('Uploading to bucket:', bucketName, 'with key:', fileKey);
    
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    console.log('File buffer created, size:', buffer.length);
    
    const uploadParams = {
      Bucket: bucketName,
      Key: fileKey,
      Body: buffer,
      ContentType: file.type,
      Metadata: {
        'original-name': file.name,
        'user-id': (session.user as any).id,
        'ordinance-id': ordinanceId,
      },
    };
    
    console.log('Sending file to MinIO...');
    await s3Client.send(new PutObjectCommand(uploadParams));
    console.log('File uploaded to MinIO successfully');
    
    // Create ordinance document record
    console.log('Creating ordinance document record...');
    const ordinanceDocument = await prisma.ordinanceDocument.create({
      data: {
        ordinanceId,
        filePath: fileKey,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
      },
    });
    console.log('Ordinance document record created successfully');
    
    return NextResponse.json({
      message: 'Document uploaded successfully',
      id: ordinanceDocument.id,
      document: {
        id: ordinanceDocument.id,
        ordinanceId: ordinanceDocument.ordinanceId,
        filePath: ordinanceDocument.filePath,
        fileName: ordinanceDocument.fileName,
        fileSize: ordinanceDocument.fileSize,
        fileType: ordinanceDocument.fileType,
        uploadedAt: ordinanceDocument.uploadedAt,
      },
    });
  } catch (error) {
    console.error('Error uploading document:', error);
    // Return proper JSON error response
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}