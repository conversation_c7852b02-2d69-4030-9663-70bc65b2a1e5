import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';

// Initialize S3 client for MinIO
const s3Client = new S3Client({
  endpoint: `${process.env.MINIO_ENDPOINT}:${process.env.MINIO_PORT}`,
  region: process.env.MINIO_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.MINIO_ACCESS_KEY || '',
    secretAccessKey: process.env.MINIO_SECRET_KEY || '',
  },
  forcePathStyle: true, // Required for Min<PERSON>
});

export async function POST(req: NextRequest) {
  try {
    console.log('Starting temporary document upload process...');
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      console.log('Unauthorized access attempt');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('Parsing form data...');
    const formData = await req.formData();
    const file = formData.get('document') as File;
    
    if (!file) {
      console.log('No document file provided');
      return NextResponse.json({ error: 'Document file is required' }, { status: 400 });
    }
    
    console.log('File received:', file.name, file.size, file.type);
    
    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      console.log('File size exceeds limit');
      return NextResponse.json({ error: 'File size exceeds 5MB limit' }, { status: 400 });
    }
    
    // Validate file type
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
      console.log('Invalid file type:', file.type);
      return NextResponse.json({ error: 'Invalid file type. Please upload a PDF, DOC, or DOCX file.' }, { status: 400 });
    }
    
    // Generate a unique file name and temporary path
    const fileExtension = file.name.split('.').pop();
    const uniqueFileName = `${uuidv4()}.${fileExtension}`;
    console.log('Generated unique file name:', uniqueFileName);
    
    // Upload file to MinIO in temporary location
    const bucketName = process.env.MINIO_BUCKET_NAME || 'nwa-uploads';
    const fileKey = `temp/${(session.user as any).id}/${uniqueFileName}`;
    console.log('Uploading to bucket:', bucketName, 'with key:', fileKey);
    
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    console.log('File buffer created, size:', buffer.length);
    
    const uploadParams = {
      Bucket: bucketName,
      Key: fileKey,
      Body: buffer,
      ContentType: file.type,
      Metadata: {
        'original-name': file.name,
        'user-id': (session.user as any).id,
        'temporary': 'true',
      },
    };
    
    console.log('Sending file to MinIO...');
    await s3Client.send(new PutObjectCommand(uploadParams));
    console.log('File uploaded to MinIO successfully');
    
    return NextResponse.json({
      message: 'Document uploaded successfully',
      fileId: uniqueFileName,
      fileKey: fileKey,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
    });
    
  } catch (error) {
    console.error('Error uploading temporary document:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}