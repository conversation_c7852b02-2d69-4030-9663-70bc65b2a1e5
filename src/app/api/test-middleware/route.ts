import { 
  createStandardizedHand<PERSON>, 
  with<PERSON>d<PERSON><PERSON><PERSON><PERSON>, 
  with<PERSON>ole<PERSON><PERSON><PERSON>,
  createJsonResponse,
  createSuccessResponse,
  StandardizedHandlerContext
} from '@/lib/middleware';

// Test public endpoint
async function publicTestHandler(context: StandardizedHandlerContext) {
  return createJsonResponse({ 
    message: 'Public endpoint test successful', 
    timestamp: new Date().toISOString(),
    public: true
  });
}

// Test authenticated endpoint
async function authenticatedTestHand<PERSON>(context: StandardizedHandlerContext) {
  const { auth } = context;
  return createJsonResponse({
    message: 'Authenticated endpoint test successful',
    user: {
      id: auth.userId,
      email: auth.email,
      roles: auth.roles,
      permissions: auth.permissions
    },
    authenticated: true
  });
}

// Test admin endpoint
async function adminTestHandler(context: StandardizedHandlerContext) {
  const { auth } = context;
  return createSuccessResponse(
    'Admin endpoint test successful',
    {
      user: {
        id: auth.userId,
        email: auth.email,
        roles: auth.roles
      },
      admin: true,
      timestamp: new Date().toISOString()
    }
  );
}

// Test role-specific endpoint
async function moderatorTestHand<PERSON>(context: StandardizedHandlerContext) {
  const { auth } = context;
  return createJsonResponse({
    message: 'Moderator endpoint test successful',
    user: {
      id: auth.userId,
      roles: auth.roles,
      hasModeratorRole: auth.roles.includes('moderator')
    },
    moderator: true
  });
}

// Export handlers with different authentication levels
export const GET = createStandardizedHandler(publicTestHandler, {
  auth: { auditLogging: false }, // Public endpoint, minimal logging
  rateLimit: true
});

export const POST = createStandardizedHandler(authenticatedTestHandler, {
  auth: { auditLogging: true }, // Regular authentication
  rateLimit: true
});

export const PUT = withAdminHandler(adminTestHandler, {
  rateLimit: true,
  auditLogging: true
});

export const DELETE = withRoleHandler(moderatorTestHandler, ['moderator', 'admin'], {
  rateLimit: true,
  auditLogging: true
});