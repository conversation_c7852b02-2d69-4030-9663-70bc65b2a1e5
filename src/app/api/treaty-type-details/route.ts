import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for treaty type details
const treatyTypeDetailsSchema = z.object({
  treatyId: z.string().min(1, 'Treaty ID is required'),
  treatyTypeId: z.string().min(1, 'Treaty type ID is required'),
  businessDetails: z.object({
    businessName: z.string().min(1, 'Business name is required'),
    businessAddress: z.string().min(1, 'Business address is required'),
    businessEmail: z.string().email('Valid business email is required'),
    businessPhone: z.string().optional(),
    businessWebsite: z.string().optional(),
    businessDescription: z.string().optional(),
    categoryId: z.string().min(1, 'Business category is required'),
    subcategoryId: z.string().min(1, 'Business subcategory is required'),
    businessType: z.string().min(1, 'Business type is required'), // sole trader, small business, etc.
  }),
  fullLegalName: z.string().min(1, 'Full legal name is required'),
  dateOfBirth: z.string().optional(),
  genderIdentity: z.string().optional(),
  nationality: z.array(z.string()).optional(),
  phoneNumbers: z.array(z.string()).optional(),
  email: z.string().email('Valid email is required'),
  currentCountryId: z.string().min(1, 'Current country is required'),
  currentCityId: z.string().min(1, 'Current city is required'),
  residenceCountryId: z.string().optional(),
  residenceCityId: z.string().optional(),
  residentialAddress: z.string().optional(),
  identificationNumber: z.string().optional(),
  peaceProtectedPremises: z.boolean().default(false),
  protectionItems: z.object({}).optional(),
  declarationAccepted: z.boolean().default(false),
});

// POST /api/treaty-type-details - Create treaty type details
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    
    // Validate input
    const validation = treatyTypeDetailsSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const data = validation.data;

    // Check if user has access to this treaty
    const userTreaty = await prisma.userTreaty.findFirst({
      where: {
        userId: (session.user as any).id,
        treatyId: data.treatyId,
      },
    });

    if (!userTreaty) {
      return NextResponse.json({ error: 'User does not have access to this treaty' }, { status: 403 });
    }

    // Validate treaty type is associated with treaty
    const treatyTreatyType = await prisma.treatyTreatyType.findFirst({
      where: {
        treatyId: data.treatyId,
        treatyTypeId: data.treatyTypeId,
      },
    });

    if (!treatyTreatyType) {
      return NextResponse.json({ error: 'Treaty type is not associated with this treaty' }, { status: 400 });
    }

    // Validate countries and cities
    const currentCountry = await prisma.country.findUnique({
      where: { id: parseInt(data.currentCountryId) },
    });

    const currentCity = await prisma.city.findUnique({
      where: { id: parseInt(data.currentCityId) },
    });

    if (!currentCountry || !currentCity) {
      return NextResponse.json({ error: 'Invalid current country or city' }, { status: 400 });
    }

    // Validate business category and subcategory
    const category = data.businessDetails.categoryId ? 
      await prisma.category.findUnique({
        where: { id: parseInt(data.businessDetails.categoryId) },
      }) : null;

    const subcategory = data.businessDetails.subcategoryId ?
      await prisma.subcategory.findUnique({
        where: { id: parseInt(data.businessDetails.subcategoryId) },
      }) : null;

    if (data.businessDetails.categoryId && !category) {
      return NextResponse.json({ error: 'Invalid business category' }, { status: 400 });
    }

    if (data.businessDetails.subcategoryId && !subcategory) {
      return NextResponse.json({ error: 'Invalid business subcategory' }, { status: 400 });
    }

    // Check if treaty type details already exist for this combination
    const existingDetails = await prisma.treatyTypeDetails.findFirst({
      where: {
        userId: (session.user as any).id,
        treatyId: data.treatyId,
        treatyTypeId: data.treatyTypeId,
      },
    });

    if (existingDetails) {
      return NextResponse.json(
        { error: 'Treaty type details already exist for this treaty and type' },
        { status: 409 }
      );
    }

    // Create treaty type details
    const treatyTypeDetails = await prisma.treatyTypeDetails.create({
      data: {
        userId: (session.user as any).id,
        treatyId: data.treatyId,
        treatyTypeId: data.treatyTypeId,
        status: 'DRAFT',
        
        // Personal information
        fullLegalName: data.fullLegalName,
        dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth) : undefined,
        genderIdentity: data.genderIdentity,
        nationality: data.nationality,
        phoneNumbers: data.phoneNumbers ? JSON.stringify(data.phoneNumbers) : undefined,
        email: data.email,
        
        // Location information
        currentCountryId: parseInt(data.currentCountryId),
        currentCityId: parseInt(data.currentCityId),
        residenceCountryId: data.residenceCountryId ? parseInt(data.residenceCountryId) : null,
        residenceCityId: data.residenceCityId ? parseInt(data.residenceCityId) : null,
        residentialAddress: data.residentialAddress,
        identificationNumber: data.identificationNumber,
        
        // Business details
        businessDetails: JSON.stringify({
          ...data.businessDetails,
          categoryName: category?.name,
          subcategoryName: subcategory?.name,
        }),
        
        // Protection and declarations
        peaceProtectedPremises: data.peaceProtectedPremises,
        protectionItems: data.protectionItems ? JSON.stringify(data.protectionItems) : undefined,
        declarationAccepted: data.declarationAccepted,
      },
      include: {
        treaty: {
          select: {
            id: true,
            name: true,
          },
        },
        treatyType: {
          select: {
            id: true,
            name: true,
            category: true,
          },
        },
        currentCountry: {
          select: {
            id: true,
            name: true,
          },
        },
        currentCity: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Treaty type details created successfully',
      treatyTypeDetails: {
        id: treatyTypeDetails.id,
        treatyId: treatyTypeDetails.treatyId,
        treatyName: treatyTypeDetails.treaty.name,
        treatyTypeId: treatyTypeDetails.treatyTypeId,
        treatyTypeName: treatyTypeDetails.treatyType.name,
        treatyTypeCategory: treatyTypeDetails.treatyType.category,
        status: treatyTypeDetails.status,
        businessDetails: JSON.parse(treatyTypeDetails.businessDetails as string),
        createdAt: treatyTypeDetails.createdAt,
        updatedAt: treatyTypeDetails.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error creating treaty type details:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}