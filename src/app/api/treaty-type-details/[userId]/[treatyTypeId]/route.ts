import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ userId: string; treatyTypeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { userId, treatyTypeId } = await params;
    
    // Find treaty type details for this user and treaty type
    const treatyTypeDetails = await prisma.treatyTypeDetails.findFirst({
      where: {
        userId,
        treatyTypeId,
      },
      include: {
        user: true,
        treaty: true,
        treatyType: true,
        currentCountry: true,
        currentCity: true,
        residenceCountry: true,
        residenceCity: true,
      },
    });
    
    if (!treatyTypeDetails) {
      return NextResponse.json(
        { error: 'Treaty type details not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      treatyTypeDetails,
    });
  } catch (error) {
    console.error('Error fetching treaty type details:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}