import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { NationTreatyService } from '@/lib/services/nation-treaty';
import { 
  validateNationTreatyCreate, 
  validateNationTreatyQuery,
  NationTreatyError,
  ValidationError 
} from '@/lib/validation/nation-treaty';
import { logAudit } from '@/lib/audit';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
  role?: string;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

const nationTreatyService = new NationTreatyService(prisma);

export async function GET(request: NextRequest) {
  try {
    // Allow public read access for nation treaties (needed for forms)
    // Check authentication only for admin operations
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
      search: searchParams.get('search') || undefined,
      status: searchParams.get('status') || undefined,
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: searchParams.get('sortOrder') || 'desc',
      contactEmail: searchParams.get('contactEmail') || undefined,
      country: searchParams.get('country') || undefined
    };

    const query = validateNationTreatyQuery(queryParams);

    // Fetch nation treaties
    const result = await nationTreatyService.getNationTreaties(query);

    // Log audit (only if user is authenticated)
    if (session?.user?.id) {
      await logAudit(session.user.id, 'nation_treaty_read', {
        action: 'read',
        resource: 'nation_treaties',
        query: queryParams
      });
    }

    return NextResponse.json({
      success: true,
      data: result.treaties,
      pagination: result.pagination
    });

  } catch (error) {
    console.error('Error fetching nation treaties:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'NOT_FOUND' ? 404 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to fetch nation treaties' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has permission to create nation treaties
    if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = validateNationTreatyCreate(body);

    // Create nation treaty
    const nationTreaty = await nationTreatyService.createNationTreaty(
      validatedData, 
      session.user.id
    );

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_create', {
      action: 'create',
      resource: 'nation_treaty',
      resourceId: nationTreaty.id,
      data: {
        name: nationTreaty.name,
        officialName: nationTreaty.officialName,
        status: nationTreaty.status
      }
    });

    return NextResponse.json({
      success: true,
      data: nationTreaty,
      message: 'Nation treaty created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating nation treaty:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'DUPLICATE_NAME' ? 409 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create nation treaty' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      throw new ValidationError('Nation treaty ID is required', 'id');
    }

    const validatedData = validateNationTreatyCreate(updateData);

    // Update nation treaty
    const updatedTreaty = await nationTreatyService.updateNationTreaty(
      id,
      { ...validatedData, id },
      session.user.id
    );

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_update', {
      action: 'update',
      resource: 'nation_treaty',
      resourceId: id,
      data: updateData
    });

    return NextResponse.json({
      success: true,
      data: updatedTreaty,
      message: 'Nation treaty updated successfully'
    });

  } catch (error) {
    console.error('Error updating nation treaty:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'NOT_FOUND' ? 404 : 
                        error.code === 'DUPLICATE_NAME' ? 409 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to update nation treaty' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Parse nation treaty ID from request body or query params
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id') || (await request.json()).id;

    if (!id) {
      throw new ValidationError('Nation treaty ID is required', 'id');
    }

    // Delete nation treaty (soft delete)
    const deletedTreaty = await nationTreatyService.deleteNationTreaty(id, session.user.id);

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_delete', {
      action: 'delete',
      resource: 'nation_treaty',
      resourceId: id,
      data: {
        name: deletedTreaty.name,
        officialName: deletedTreaty.officialName
      }
    });

    return NextResponse.json({
      success: true,
      data: deletedTreaty,
      message: 'Nation treaty deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting nation treaty:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'NOT_FOUND' ? 404 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to delete nation treaty' },
      { status: 500 }
    );
  }
}