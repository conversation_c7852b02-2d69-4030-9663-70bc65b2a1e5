import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { NationTreatyService } from '@/lib/services/nation-treaty';
import { logAudit } from '@/lib/audit';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
  role?: string;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

const nationTreatyService = new NationTreatyService(prisma);

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Fetch nation treaty statistics
    const statistics = await nationTreatyService.getNationTreatyStatistics();

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_statistics_read', {
      action: 'read',
      resource: 'nation_treaty_statistics'
    });

    return NextResponse.json({
      success: true,
      data: statistics
    });

  } catch (error) {
    console.error('Error fetching nation treaty statistics:', error);

    return NextResponse.json(
      { success: false, error: 'Failed to fetch nation treaty statistics' },
      { status: 500 }
    );
  }
}