import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { NationTreatyService } from '@/lib/services/nation-treaty';
import { 
  validateNationTreatyOfficeCreate,
  validateNationTreatyOfficeUpdate,
  validateNationTreatyOfficeQuery,
  NationTreatyError,
  ValidationError 
} from '@/lib/validation/nation-treaty';
import { logAudit } from '@/lib/audit';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
  role?: string;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

const nationTreatyService = new NationTreatyService(prisma);

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    // Check authentication
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: nationTreatyId } = params;

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
      search: searchParams.get('search') || undefined,
      status: searchParams.get('status') || undefined,
      officeType: searchParams.get('officeType') || undefined,
      country: searchParams.get('country') || undefined,
      city: searchParams.get('city') || undefined,
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: searchParams.get('sortOrder') || 'desc'
    };

    const query = validateNationTreatyOfficeQuery(queryParams);

    // Fetch nation treaty offices
    const result = await nationTreatyService.getNationTreatyOffices(nationTreatyId, query);

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_offices_read', {
      action: 'read',
      resource: 'nation_treaty_offices',
      resourceId: nationTreatyId,
      query: queryParams
    });

    return NextResponse.json({
      success: true,
      data: result.offices,
      pagination: result.pagination
    });

  } catch (error) {
    console.error('Error fetching nation treaty offices:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'NOT_FOUND' ? 404 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to fetch nation treaty offices' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    // Check authentication and authorization
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const { id: nationTreatyId } = params;

    // Parse and validate request body
    const body = await request.json();
    const validatedData = validateNationTreatyOfficeCreate({
      ...body,
      nationTreatyId
    });

    // Add office to nation treaty
    const office = await nationTreatyService.addOfficeToNationTreaty(validatedData);

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_office_create', {
      action: 'create',
      resource: 'nation_treaty_office',
      resourceId: office.id,
      data: {
        nationTreatyId,
        city: office.city,
        officeType: office.officeType,
        status: office.status
      }
    });

    return NextResponse.json({
      success: true,
      data: office,
      message: 'Office added to nation treaty successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error adding office to nation treaty:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'NOT_FOUND' ? 404 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to add office to nation treaty' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    // Check authentication and authorization
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const { id: nationTreatyId } = params;

    // Parse and validate request body
    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      throw new ValidationError('Office ID is required', 'id');
    }

    const validatedData = validateNationTreatyOfficeUpdate(updateData);

    // Update nation treaty office
    const updatedOffice = await nationTreatyService.updateNationTreatyOffice(id, validatedData);

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_office_update', {
      action: 'update',
      resource: 'nation_treaty_office',
      resourceId: id,
      data: updateData
    });

    return NextResponse.json({
      success: true,
      data: updatedOffice,
      message: 'Nation treaty office updated successfully'
    });

  } catch (error) {
    console.error('Error updating nation treaty office:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'NOT_FOUND' ? 404 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to update nation treaty office' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    // Check authentication and authorization
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const { id: nationTreatyId } = params;

    // Get office ID from request body or query params
    const { searchParams } = new URL(request.url);
    const officeId = searchParams.get('officeId') || (await request.json()).officeId;

    if (!officeId) {
      throw new ValidationError('Office ID is required', 'officeId');
    }

    // Delete office from nation treaty
    const deletedOffice = await nationTreatyService.deleteNationTreatyOffice(officeId);

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_office_delete', {
      action: 'delete',
      resource: 'nation_treaty_office',
      resourceId: officeId,
      data: {
        nationTreatyId,
        city: deletedOffice.city
      }
    });

    return NextResponse.json({
      success: true,
      data: deletedOffice,
      message: 'Office removed from nation treaty successfully'
    });

  } catch (error) {
    console.error('Error removing office from nation treaty:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'OFFICE_NOT_FOUND' ? 404 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to remove office from nation treaty' },
      { status: 500 }
    );
  }
}