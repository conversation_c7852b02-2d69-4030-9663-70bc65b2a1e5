import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { NationTreatyService } from '@/lib/services/nation-treaty';
import { 
  validateNationTreatyEnvoyCreate,
  validateNationTreatyEnvoyQuery,
  NationTreatyError,
  ValidationError 
} from '@/lib/validation/nation-treaty';
import { logAudit } from '@/lib/audit';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
  role?: string;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

const nationTreatyService = new NationTreatyService(prisma);

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    // Check authentication
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: nationTreatyId } = params;

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
      search: searchParams.get('search') || undefined,
      status: searchParams.get('status') || undefined,
      title: searchParams.get('title') || undefined,
      sortBy: searchParams.get('sortBy') || 'appointedAt',
      sortOrder: searchParams.get('sortOrder') || 'desc'
    };

    const query = validateNationTreatyEnvoyQuery(queryParams);

    // Fetch nation treaty envoys
    const result = await nationTreatyService.getNationTreatyEnvoys(nationTreatyId, query);

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_envoys_read', {
      action: 'read',
      resource: 'nation_treaty_envoys',
      resourceId: nationTreatyId,
      query: queryParams
    });

    return NextResponse.json({
      success: true,
      data: result.envoys,
      pagination: result.pagination
    });

  } catch (error) {
    console.error('Error fetching nation treaty envoys:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'NOT_FOUND' ? 404 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to fetch nation treaty envoys' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    // Check authentication and authorization
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const { id: nationTreatyId } = params;

    // Parse and validate request body
    const body = await request.json();
    const validatedData = validateNationTreatyEnvoyCreate({
      ...body,
      nationTreatyId
    });

    // Add envoy to nation treaty
    const envoy = await nationTreatyService.addEnvoyToNationTreaty(validatedData);

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_envoy_create', {
      action: 'create',
      resource: 'nation_treaty_envoy',
      resourceId: envoy.id,
      data: {
        nationTreatyId,
        userId: envoy.userId,
        title: envoy.title,
        status: envoy.status
      }
    });

    return NextResponse.json({
      success: true,
      data: envoy,
      message: 'Envoy added to nation treaty successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error adding envoy to nation treaty:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'ALREADY_ENVOY' ? 409 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to add envoy to nation treaty' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    // Check authentication and authorization
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const { id: nationTreatyId } = params;

    // Get envoy ID from request body or query params
    const { searchParams } = new URL(request.url);
    const envoyId = searchParams.get('envoyId') || (await request.json()).envoyId;

    if (!envoyId) {
      throw new ValidationError('Envoy ID is required', 'envoyId');
    }

    // Remove envoy from nation treaty
    const removedEnvoy = await nationTreatyService.removeEnvoyFromNationTreaty(envoyId);

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_envoy_delete', {
      action: 'delete',
      resource: 'nation_treaty_envoy',
      resourceId: envoyId,
      data: {
        nationTreatyId,
        userId: removedEnvoy.userId
      }
    });

    return NextResponse.json({
      success: true,
      data: removedEnvoy,
      message: 'Envoy removed from nation treaty successfully'
    });

  } catch (error) {
    console.error('Error removing envoy from nation treaty:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'ENVOY_NOT_FOUND' ? 404 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to remove envoy from nation treaty' },
      { status: 500 }
    );
  }
}