import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { NationTreatyService } from '@/lib/services/nation-treaty';
import { 
  validateNationTreatyUpdate,
  NationTreatyError,
  ValidationError 
} from '@/lib/validation/nation-treaty';
import { logAudit } from '@/lib/audit';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
  role?: string;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

const nationTreatyService = new NationTreatyService(prisma);

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    // Check authentication
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;

    // Fetch nation treaty by ID
    const nationTreaty = await nationTreatyService.getNationTreatyById(id, true);

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_read_single', {
      action: 'read',
      resource: 'nation_treaty',
      resourceId: id
    });

    return NextResponse.json({
      success: true,
      data: nationTreaty
    });

  } catch (error) {
    console.error('Error fetching nation treaty:', error);

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'NOT_FOUND' ? 404 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to fetch nation treaty' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    // Check authentication and authorization
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const { id } = params;

    // Parse and validate request body
    const body = await request.json();
    const validatedData = validateNationTreatyUpdate({ ...body, id });

    // Update nation treaty
    const updatedTreaty = await nationTreatyService.updateNationTreaty(
      id,
      validatedData,
      session.user.id
    );

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_update_single', {
      action: 'update',
      resource: 'nation_treaty',
      resourceId: id,
      data: body
    });

    return NextResponse.json({
      success: true,
      data: updatedTreaty,
      message: 'Nation treaty updated successfully'
    });

  } catch (error) {
    console.error('Error updating nation treaty:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'NOT_FOUND' ? 404 : 
                        error.code === 'DUPLICATE_NAME' ? 409 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to update nation treaty' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    // Check authentication and authorization
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const { id } = params;

    // Delete nation treaty (soft delete)
    const deletedTreaty = await nationTreatyService.deleteNationTreaty(id, session.user.id);

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_delete_single', {
      action: 'delete',
      resource: 'nation_treaty',
      resourceId: id,
      data: {
        name: deletedTreaty.name,
        officialName: deletedTreaty.officialName
      }
    });

    return NextResponse.json({
      success: true,
      data: deletedTreaty,
      message: 'Nation treaty deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting nation treaty:', error);

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'NOT_FOUND' ? 404 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to delete nation treaty' },
      { status: 500 }
    );
  }
}