import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { NationTreatyService } from '@/lib/services/nation-treaty';
import { 
  validateNationTreatyMemberCreate,
  validateNationTreatyMemberQuery,
  NationTreatyError,
  ValidationError 
} from '@/lib/validation/nation-treaty';
import { logAudit } from '@/lib/audit';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
  role?: string;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

const nationTreatyService = new NationTreatyService(prisma);

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    // Check authentication
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: nationTreatyId } = params;

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
      search: searchParams.get('search') || undefined,
      status: searchParams.get('status') || undefined,
      sortBy: searchParams.get('sortBy') || 'joinedAt',
      sortOrder: searchParams.get('sortOrder') || 'desc'
    };

    const query = validateNationTreatyMemberQuery(queryParams);

    // Fetch nation treaty members
    const result = await nationTreatyService.getNationTreatyMembers(nationTreatyId, query);

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_members_read', {
      action: 'read',
      resource: 'nation_treaty_members',
      resourceId: nationTreatyId,
      query: queryParams
    });

    return NextResponse.json({
      success: true,
      data: result.members,
      pagination: result.pagination
    });

  } catch (error) {
    console.error('Error fetching nation treaty members:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'NOT_FOUND' ? 404 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to fetch nation treaty members' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    // Check authentication and authorization
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const { id: nationTreatyId } = params;

    // Parse and validate request body
    const body = await request.json();
    const validatedData = validateNationTreatyMemberCreate({
      ...body,
      nationTreatyId
    });

    // Add member to nation treaty
    const member = await nationTreatyService.addMemberToNationTreaty(validatedData);

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_member_create', {
      action: 'create',
      resource: 'nation_treaty_member',
      resourceId: member.id,
      data: {
        nationTreatyId,
        userId: member.userId,
        status: member.status
      }
    });

    return NextResponse.json({
      success: true,
      data: member,
      message: 'Member added to nation treaty successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error adding member to nation treaty:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'ALREADY_MEMBER' ? 409 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to add member to nation treaty' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    // Check authentication and authorization
    const session = await getServerSession(authOptions) as ExtendedSession | null;
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const { id: nationTreatyId } = params;

    // Get member ID from request body or query params
    const { searchParams } = new URL(request.url);
    const memberId = searchParams.get('memberId') || (await request.json()).memberId;

    if (!memberId) {
      throw new ValidationError('Member ID is required', 'memberId');
    }

    // Remove member from nation treaty
    const removedMember = await nationTreatyService.removeMemberFromNationTreaty(memberId);

    // Log audit
    await logAudit(session.user.id, 'nation_treaty_member_delete', {
      action: 'delete',
      resource: 'nation_treaty_member',
      resourceId: memberId,
      data: {
        nationTreatyId,
        userId: removedMember.userId
      }
    });

    return NextResponse.json({
      success: true,
      data: removedMember,
      message: 'Member removed from nation treaty successfully'
    });

  } catch (error) {
    console.error('Error removing member from nation treaty:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'MEMBER_NOT_FOUND' ? 404 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to remove member from nation treaty' },
      { status: 500 }
    );
  }
}