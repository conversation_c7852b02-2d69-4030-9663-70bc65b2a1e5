import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const titles = await prisma.title.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        name: 'asc',
      },
      select: {
        id: true,
        name: true,
        description: true,
        isAmbassadorial: true,
      },
    });

    return NextResponse.json(titles);
  } catch (error) {
    console.error('Error fetching titles:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, isAmbassadorial } = body;

    if (!name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    const title = await prisma.title.create({
      data: {
        name,
        description: description || null,
        isAmbassadorial: isAmbassadorial || false,
      },
    });

    return NextResponse.json(title, { status: 201 });
  } catch (error) {
    console.error('Error creating title:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}