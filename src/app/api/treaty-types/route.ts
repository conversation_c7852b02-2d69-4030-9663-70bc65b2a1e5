import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

// Validation schema for treaty type creation
const treatyTypeCreateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name must be less than 255 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
});

// Validation schema for treaty type update
const treatyTypeUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name must be less than 255 characters').optional(),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  isActive: z.boolean().optional(),
});

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const autocomplete = searchParams.get('autocomplete') === 'true';
    const sortBy = searchParams.get('sortBy') || '';
    const sortOrder = searchParams.get('sortOrder') || 'asc';
    
    // For autocomplete requests, use different logic
    if (autocomplete) {
      return handleAutocompleteRequest(search, limit);
    }

    const skip = (page - 1) * limit;

    // Build where clause for search
    const where: any = {
      isActive: true,
      status: 'APPROVED', // Only show approved types for regular users
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (category) {
      where.category = {
        contains: category,
        mode: 'insensitive'
      };
    }

    // Build orderBy clause based on sortBy parameter
    let orderBy: any = { name: 'asc' }; // default sorting
    
    if (sortBy === 'name') {
      orderBy = { name: sortOrder };
    }
    // Note: treatyCount sorting will be handled client-side since it requires aggregation

    // Fetch treaty types with pagination
    const [treatyTypes, totalCount] = await Promise.all([
      prisma.treatyType.findMany({
        where,
        orderBy,
        include: {
          treatyTreatyTypes: {
            include: {
              treaty: {
                select: {
                  id: true,
                  status: true,
                },
              },
            },
          },
          userTreatyNumbers: {
            select: {
              id: true,
              treatyNumber: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
        skip,
        take: limit,
      }),
      prisma.treatyType.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    // Process treaty types and add counts
    let processedTreatyTypes = treatyTypes.map(type => ({
      id: type.id,
      name: type.name,
      description: type.description,
      category: type.category,
      price: (type as any).price || 0,
      currency: (type as any).currency || 'USD',
      requiresPayment: (type as any).requiresPayment || false,
      isActive: type.isActive,
      createdAt: type.createdAt,
      updatedAt: type.updatedAt,
      treatyCount: type.treatyTreatyTypes.length,
      activeTreaties: type.treatyTreatyTypes.filter(ttt => ttt.treaty.status === 'ACTIVE').length,
      // Add total assigned treaties count (regardless of status) to match delete logic
      totalAssigned: type.treatyTreatyTypes.length,
      // Add user treaty numbers count for proper deletion checking
      userTreatyNumbersCount: type.userTreatyNumbers.length,
      assignedUsersCount: type.userTreatyNumbers.length,
    }));

    // Handle treatyCount sorting (post-processing since it requires aggregation)
    if (sortBy === 'treatyCount') {
      processedTreatyTypes.sort((a, b) => {
        const comparison = a.treatyCount - b.treatyCount;
        return sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    return NextResponse.json({
      treatyTypes: processedTreatyTypes,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        pageSize: limit,
      },
    });
  } catch (error) {
    console.error('Error fetching treaty types:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Handle autocomplete search requests
async function handleAutocompleteRequest(search: string, limit: number = 10) {
  if (!search || search.length < 2) {
    return NextResponse.json([]);
  }

  const treatyTypes = await prisma.treatyType.findMany({
    where: {
      isActive: true,
      status: 'APPROVED',
      OR: [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ],
    },
    orderBy: {
      name: 'asc',
    },
    take: limit,
    select: {
      id: true,
      name: true,
      description: true,
      category: true,
    },
  });

  return NextResponse.json(treatyTypes);
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();

    // Validate input
    const validation = treatyTypeCreateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { name, description } = validation.data;

    // Check if treaty type with same name already exists
    const existingType = await prisma.treatyType.findFirst({
      where: { name },
    });

    if (existingType) {
      return NextResponse.json(
        { error: 'Treaty type with this name already exists' },
        { status: 400 }
      );
    }

    // Get the default treaty ID (Peace & Trade Treaty)
    const defaultTreaty = await prisma.treaty.findFirst({
      where: { name: 'Peace & Trade Treaty' },
    });

    if (!defaultTreaty) {
      return NextResponse.json(
        { error: 'Default treaty not found' },
        { status: 500 }
      );
    }

    // Check if user is admin - if so, create as APPROVED, otherwise PENDING
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    const isAdmin = user?.userRoles.some(ur => ur.role.name === 'admin');
    const status = isAdmin ? 'APPROVED' : 'PENDING';

    // Create treaty type
    const treatyType = await prisma.treatyType.create({
      data: {
        name,
        description,
        category: 'General', // Default category
        isActive: true,
        status,
        requestedBy: session.user.id,
        ...(isAdmin && {
          reviewedBy: session.user.id,
          reviewedAt: new Date(),
        }),
      },
    });

    // Create the many-to-many relationship with the default treaty
    await prisma.treatyTreatyType.create({
      data: {
        treatyId: defaultTreaty.id,
        treatyTypeId: treatyType.id,
        assignedBy: session.user.id,
      },
    });

    // If this is a pending request, create notifications for admins
    if (status === 'PENDING') {
      await createTypeRequestNotification(treatyType, session.user.id);
      await broadcastTypeRequestNotification(treatyType, session.user.id);
    }

    return NextResponse.json({
      message: isAdmin ? 'Treaty type created successfully' : 'Treaty type request submitted for approval',
      treatyType: {
        id: treatyType.id,
        name: treatyType.name,
        description: treatyType.description,
        isActive: treatyType.isActive,
        status: treatyType.status,
        createdAt: treatyType.createdAt,
        updatedAt: treatyType.updatedAt,
      },
      requiresApproval: !isAdmin,
    });
  } catch (error) {
    console.error('Error creating treaty type:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const treatyTypeId = searchParams.get('id');

    if (!treatyTypeId) {
      return NextResponse.json({ error: 'Treaty type ID is required' }, { status: 400 });
    }

    const body = await req.json();

    // Validate input
    const validation = treatyTypeUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    // Check if the treaty type exists
    const existingType = await prisma.treatyType.findUnique({
      where: { id: treatyTypeId },
    });

    if (!existingType) {
      return NextResponse.json({ error: 'Treaty type not found' }, { status: 404 });
    }

    const updateData: any = {};
    const { name, description, isActive } = validation.data;

    if (name && name !== existingType.name) {
      // Check if new name conflicts with existing
      const nameConflict = await prisma.treatyType.findFirst({
        where: { name },
      });

      if (nameConflict) {
        return NextResponse.json(
          { error: 'Treaty type with this name already exists' },
          { status: 400 }
        );
      }
      updateData.name = name;
    }

    if (description !== undefined) updateData.description = description;
    if (isActive !== undefined) updateData.isActive = isActive;

    // Update treaty type
    const treatyType = await prisma.treatyType.update({
      where: { id: treatyTypeId },
      data: updateData,
    });

    return NextResponse.json({
      message: 'Treaty type updated successfully',
      treatyType: {
        id: treatyType.id,
        name: treatyType.name,
        description: treatyType.description,
        isActive: treatyType.isActive,
        createdAt: treatyType.createdAt,
        updatedAt: treatyType.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error updating treaty type:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  let treatyTypeId: string | null = null

  try {
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    treatyTypeId = searchParams.get('id');

    if (!treatyTypeId) {
      return NextResponse.json({ error: 'Treaty type ID is required' }, { status: 400 });
    }

    // Check if the treaty type exists and has associated data
    const existingType = await prisma.treatyType.findUnique({
      where: { id: treatyTypeId },
      include: {
        treatyTreatyTypes: {
          select: { 
            id: true,
            treaty: {
              select: { id: true, status: true, notes: true }
            }
          },
        },
        treatyTypeDetails: {
          select: { id: true }
        },
        userTreatyNumbers: {
          select: { 
            id: true,
            treatyNumber: true,
            user: {
              select: { id: true, name: true, email: true }
            }
          }
        }
      },
    });

    if (!existingType) {
      return NextResponse.json({ error: 'Treaty type not found' }, { status: 404 });
    }

    // Check if treaty type has associated treaties
    if (existingType.treatyTreatyTypes.length > 0) {
      const associatedTreaties = existingType.treatyTreatyTypes.map(ttt => 
        `Treaty ${ttt.treaty.id} (${ttt.treaty.status})`
      ).join(', ');
      
      return NextResponse.json(
        { 
          error: 'Cannot delete treaty type that has associated treaties',
          details: `Associated with treaties: ${associatedTreaties}`,
          associatedCount: existingType.treatyTreatyTypes.length
        },
        { status: 400 }
      );
    }

    // Check if treaty type has associated user treaty numbers
    if (existingType.userTreatyNumbers.length > 0) {
      const userTreatyNumbers = existingType.userTreatyNumbers.map(utn => 
        `${utn.treatyNumber} (${utn.user.name || utn.user.email})`
      ).join(', ');
      
      return NextResponse.json(
        { 
          error: 'Cannot delete treaty type with assigned user treaty numbers',
          details: `Users with treaty numbers: ${userTreatyNumbers}`,
          userTreatyNumbers: existingType.userTreatyNumbers,
          associatedUsersCount: existingType.userTreatyNumbers.length
        },
        { status: 400 }
      );
    }

    // Check if treaty type has associated details
    if (existingType.treatyTypeDetails.length > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete treaty type that has associated details',
          details: `Has ${existingType.treatyTypeDetails.length} details records`
        },
        { status: 400 }
      );
    }

    // Delete the treaty type (cascade should handle any remaining associations)
    await prisma.treatyType.delete({
      where: { id: treatyTypeId },
    });

    return NextResponse.json({
      message: 'Treaty type deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting treaty type:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      treatyTypeId,
    });

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
        treatyTypeId,
      },
      { status: 500 }
    );
  }
}

// Approval and rejection endpoints are now in separate route files:
// PUT /api/treaty-types/[id]/approve - Approve a treaty type request  
// PUT /api/treaty-types/[id]/reject - Reject a treaty type request

// Helper function to create notification for type requests
async function createTypeRequestNotification(treatyType: any, requestedById: string) {
  try {
    // Get all admin users
    const adminUsers = await prisma.user.findMany({
      where: {
        userRoles: {
          some: {
            role: {
              name: 'admin',
            },
          },
        },
      },
      select: {
        id: true,
      },
    });

    const adminIds = adminUsers.map(user => user.id);

    if (adminIds.length > 0) {
      // Get the requesting user's name
      const requestingUser = await prisma.user.findUnique({
        where: { id: requestedById },
        select: {
          name: true,
          email: true,
        },
      });

      await prisma.notification.create({
        data: {
          type: 'TREATY_TYPE_REQUEST',
          message: `New treaty type request: ${treatyType.name}`,
          data: {
            typeId: treatyType.id,
            typeName: treatyType.name,
            requestedBy: requestedById,
            requestedByName: requestingUser?.name || requestingUser?.email || 'Unknown user',
            requestedAt: new Date().toISOString(),
          },
          recipients: adminIds,
        },
      });
    }
  } catch (error) {
    console.error('Error creating type request notification:', error);
    // Don't throw error - notification failure shouldn't break the request
  }
}

// Helper function to broadcast type request notification
async function broadcastTypeRequestNotification(treatyType: any, requestedById: string) {
  try {
    const requestingUser = await prisma.user.findUnique({
      where: { id: requestedById },
      select: { name: true, email: true },
    });

    const notificationData = {
      type: 'TREATY_TYPE_REQUEST',
      message: `New treaty type request: ${treatyType.name}`,
      data: {
        typeId: treatyType.id,
        typeName: treatyType.name,
        requestedBy: requestedById,
        requestedByName: requestingUser?.name || requestingUser?.email || 'Unknown user',
        requestedAt: new Date().toISOString(),
      },
      recipients: ['admin'], // Broadcast to all admins
      createdAt: new Date(),
    };

    // Import and use the broadcast function
    const { broadcastNotification } = await import('@/lib/notifications');
    await broadcastNotification(notificationData);
  } catch (error) {
    console.error('Error broadcasting type request notification:', error);
  }
}
