import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { ApplicationStatus } from '@prisma/client';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get comprehensive treaty statistics
    const [
      totalApplications,
      activeTreaties,
      pendingApplications,
      totalRevenue,
      pendingPayments,
      rejectedApplications,
      treatyTypeStats,
      monthlyStats,
      paymentMethodStats
    ] = await Promise.all([
      // Total applications count
      prisma.userTreatyType.count(),

      // Active treaties count
      prisma.userTreatyType.count({
        where: { status: ApplicationStatus.ACTIVE }
      }),

      // Pending applications count
      prisma.userTreatyType.count({
        where: {
          status: {
            in: [ApplicationStatus.APPLIED, ApplicationStatus.UNDER_REVIEW, ApplicationStatus.APPROVED]
          }
        }
      }),

      // Total revenue from payments
      prisma.payment.aggregate({
        _sum: {
          amount: true
        },
        where: {
          status: 'PAID'
        }
      }),

      // Pending payments count
      prisma.payment.count({
        where: {
          status: {
            in: ['AWAITING_PAYMENT', 'PENDING']
          }
        }
      }),

      // Rejected applications count
      prisma.userTreatyType.count({
        where: { status: ApplicationStatus.REJECTED }
      }),

      // Treaty type statistics
      prisma.treatyType.findMany({
        include: {
          _count: {
            select: {
              userTreatyTypes: true,
              treatyTreatyTypes: true,
            }
          },
          userTreatyTypes: {
            select: {
              status: true,
              payments: {
                where: { status: 'PAID' },
                select: { amount: true }
              }
            }
          }
        }
      }),

      // Monthly application trends (last 12 months)
      prisma.$queryRaw`
        SELECT
          DATE_TRUNC('month', "applied_at") as month,
          COUNT(*) as applications,
          COUNT(CASE WHEN application_status = 'ACTIVE' THEN 1 END) as active_treaties,
          COUNT(CASE WHEN application_status = 'REJECTED' THEN 1 END) as rejected_applications
        FROM "user_treaty_types"
        WHERE "applied_at" >= DATE_TRUNC('month', NOW() - INTERVAL '12 months')
        GROUP BY DATE_TRUNC('month', "applied_at")
        ORDER BY month DESC
      `,

      // Payment method statistics
      prisma.payment.groupBy({
        by: ['paymentMethod'],
        _count: true,
        _sum: {
          amount: true
        },
        where: {
          status: 'PAID'
        }
      })
    ]);

    // Process treaty type statistics
    const processedTreatyTypeStats = treatyTypeStats.map((type: any) => {
      const activeApplications = type.userTreatyTypes.filter((app: any) => app.status === ApplicationStatus.ACTIVE).length;
      const totalRevenue = type.userTreatyTypes.reduce((sum: number, app: any) =>
        sum + app.payments.reduce((paymentSum: number, payment: any) => paymentSum + (payment.amount || 0), 0), 0
      );

      return {
        id: type.id,
        name: type.name,
        totalApplications: type._count.userTreatyTypes,
        activeTreaties: activeApplications,
        totalRevenue,
        conversionRate: type._count.userTreatyTypes > 0 ? (activeApplications / type._count.userTreatyTypes) * 100 : 0
      };
    });

    // Process monthly statistics
    const processedMonthlyStats = (monthlyStats as any[]).map(stat => ({
      month: stat.month,
      applications: Number(stat.applications),
      activeTreaties: Number(stat.active_treaties),
      rejectedApplications: Number(stat.rejected_applications)
    }));

    // Process payment method statistics
    const processedPaymentMethodStats = paymentMethodStats.map((method: any) => ({
      method: method.paymentMethod,
      count: method._count,
      totalAmount: method._sum.amount || 0
    }));

    const stats = {
      totalApplications,
      activeTreaties,
      pendingApplications,
      totalRevenue: totalRevenue._sum.amount || 0,
      pendingPayments,
      rejectedApplications,
      treatyTypeStats: processedTreatyTypeStats,
      monthlyStats: processedMonthlyStats,
      paymentMethodStats: processedPaymentMethodStats,
      // Additional calculated metrics
      approvalRate: totalApplications > 0 ? ((activeTreaties / totalApplications) * 100) : 0,
      averageRevenuePerTreaty: activeTreaties > 0 ? Number(totalRevenue._sum.amount || 0) / activeTreaties : 0,
      rejectionRate: totalApplications > 0 ? ((rejectedApplications / totalApplications) * 100) : 0
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching treaty statistics:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}