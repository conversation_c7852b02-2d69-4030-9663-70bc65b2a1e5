import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Debug endpoint to see all associations for a specific treaty type
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id: treatyTypeId } = await context.params;

    if (!treatyTypeId) {
      return NextResponse.json({ error: 'Treaty type ID is required' }, { status: 400 });
    }

    // Get the treaty type with ALL possible associations
    const treatyType = await prisma.treatyType.findUnique({
      where: { id: treatyTypeId },
      include: {
        treatyTreatyTypes: {
          include: {
            treaty: {
              select: { 
                id: true, 
                status: true, 
                notes: true, 
                userId: true,
                createdAt: true 
              }
            }
          }
        },
        treatyTypeDetails: {
          select: { 
            id: true, 
            userId: true, 
            treatyId: true, 
            status: true,
            createdAt: true
          }
        },
        userTreatyTypes: {
          select: { 
            id: true, 
            userId: true
          }
        },
        userTreatyNumbers: {
          select: { 
            id: true, 
            userId: true,
            treatyNumber: true
          }
        }
      },
    });

    if (!treatyType) {
      return NextResponse.json({ error: 'Treaty type not found' }, { status: 404 });
    }

    return NextResponse.json({
      treatyType: {
        id: treatyType.id,
        name: treatyType.name,
        description: treatyType.description,
        isActive: treatyType.isActive,
        status: treatyType.status,
        createdAt: treatyType.createdAt
      },
      associations: {
        treatyTreatyTypes: {
          count: treatyType.treatyTreatyTypes.length,
          items: treatyType.treatyTreatyTypes
        },
        treatyTypeDetails: {
          count: treatyType.treatyTypeDetails.length,
          items: treatyType.treatyTypeDetails
        },
        userTreatyTypes: {
          count: treatyType.userTreatyTypes.length,
          items: treatyType.userTreatyTypes
        },
        userTreatyNumbers: {
          count: treatyType.userTreatyNumbers.length,
          items: treatyType.userTreatyNumbers
        }
      },
      canDelete: treatyType.treatyTreatyTypes.length === 0 && 
                 treatyType.treatyTypeDetails.length === 0 &&
                 treatyType.userTreatyTypes.length === 0 &&
                 treatyType.userTreatyNumbers.length === 0
    });

  } catch (error) {
    console.error('Error debugging treaty type:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}