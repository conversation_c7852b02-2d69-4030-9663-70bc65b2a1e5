import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Force delete a treaty type and all its associations (for cleaning up seeded data)
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id: treatyTypeId } = await context.params;

    if (!treatyTypeId) {
      return NextResponse.json({ error: 'Treaty type ID is required' }, { status: 400 });
    }

    // Check if the treaty type exists
    const existingType = await prisma.treatyType.findUnique({
      where: { id: treatyTypeId },
      include: {
        treatyTreatyTypes: {
          select: { 
            id: true,
            treaty: {
              select: { id: true, status: true, notes: true }
            }
          },
        },
        treatyTypeDetails: {
          select: { id: true }
        },
        userTreatyTypes: {
          select: { id: true }
        },
        userTreatyNumbers: {
          select: { id: true }
        }
      },
    });

    if (!existingType) {
      return NextResponse.json({ error: 'Treaty type not found' }, { status: 404 });
    }

    // Use a transaction to safely delete all associations first, then the treaty type
    await prisma.$transaction(async (tx) => {
      // Delete treaty type details
      if (existingType.treatyTypeDetails.length > 0) {
        await tx.treatyTypeDetails.deleteMany({
          where: { treatyTypeId: treatyTypeId }
        });
      }

      // Delete user treaty types
      if (existingType.userTreatyTypes.length > 0) {
        await tx.userTreatyType.deleteMany({
          where: { treatyTypeId: treatyTypeId }
        });
      }

      // Delete user treaty numbers
      if (existingType.userTreatyNumbers.length > 0) {
        await tx.userTreatyNumber.deleteMany({
          where: { treatyTypeId: treatyTypeId }
        });
      }

      // Delete treaty-treaty type associations
      if (existingType.treatyTreatyTypes.length > 0) {
        await tx.treatyTreatyType.deleteMany({
          where: { treatyTypeId: treatyTypeId }
        });
      }

      // Finally delete the treaty type itself
      await tx.treatyType.delete({
        where: { id: treatyTypeId }
      });
    });

    return NextResponse.json({
      message: 'Treaty type and all associations deleted successfully',
      deletedAssociations: {
        treatyAssociations: existingType.treatyTreatyTypes.length,
        detailsRecords: existingType.treatyTypeDetails.length,
        userTreatyTypes: existingType.userTreatyTypes.length,
        userTreatyNumbers: existingType.userTreatyNumbers.length
      }
    });

  } catch (error) {
    console.error('Error force deleting treaty type:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}