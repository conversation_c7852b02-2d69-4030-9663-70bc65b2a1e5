import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    const isAdmin = user?.userRoles.some(ur => ur.role.name === 'admin');
    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const treatyTypeId = id;

    // Check if treaty type exists
    const existingType = await prisma.treatyType.findUnique({
      where: { id: treatyTypeId },
    });

    if (!existingType) {
      return NextResponse.json({ error: 'Treaty type not found' }, { status: 404 });
    }

    // Update treaty type status to APPROVED
    const treatyType = await prisma.treatyType.update({
      where: { id: treatyTypeId },
      data: {
        status: 'APPROVED',
        reviewedBy: session.user.id,
        reviewedAt: new Date(),
      },
    });

    // Create approval notification
    await createApprovalNotification(treatyType, session.user.id);
    
    // Broadcast notification to connected clients
    await broadcastApprovalNotification(treatyType, session.user.id);

    return NextResponse.json({
      message: 'Treaty type approved successfully',
      treatyType: {
        id: treatyType.id,
        name: treatyType.name,
        status: treatyType.status,
      },
    });
  } catch (error) {
    console.error('Error approving treaty type:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to create approval notification
async function createApprovalNotification(treatyType: any, approvedById: string) {
  try {
    if (treatyType.requestedBy) {
      const approvingUser = await prisma.user.findUnique({
        where: { id: approvedById },
        select: { name: true, email: true },
      });

      await prisma.notification.create({
        data: {
          type: 'TREATY_TYPE_APPROVED',
          message: `Your treaty type "${treatyType.name}" has been approved`,
          data: {
            typeId: treatyType.id,
            typeName: treatyType.name,
            approvedBy: approvedById,
            approvedByName: approvingUser?.name || approvingUser?.email || 'Admin',
            approvedAt: new Date().toISOString(),
          },
          recipients: [treatyType.requestedBy],
        },
      });
    }
  } catch (error) {
    console.error('Error creating approval notification:', error);
  }
}

// Helper function to broadcast approval notification
async function broadcastApprovalNotification(treatyType: any, approvedById: string) {
  try {
    const approvingUser = await prisma.user.findUnique({
      where: { id: approvedById },
      select: { name: true, email: true },
    });

    const notificationData = {
      type: 'TREATY_TYPE_APPROVED',
      message: `Treaty type "${treatyType.name}" has been approved`,
      data: {
        typeId: treatyType.id,
        typeName: treatyType.name,
        approvedBy: approvedById,
        approvedByName: approvingUser?.name || approvingUser?.email || 'Admin',
        approvedAt: new Date().toISOString(),
      },
      recipients: ['admin'], // Broadcast to all admins
      createdAt: new Date(),
    };

    // Import and use the broadcast function
    const { broadcastNotification } = await import('@/lib/notifications');
    await broadcastNotification(notificationData);
  } catch (error) {
    console.error('Error broadcasting approval notification:', error);
  }
}