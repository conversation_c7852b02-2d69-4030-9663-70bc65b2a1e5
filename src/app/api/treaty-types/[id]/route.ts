import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../../lib/auth';
import { prisma } from '../../../../lib/prisma';
import { z } from 'zod';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

// Validation schema for treaty type update
const treatyTypeUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name must be less than 255 characters').optional(),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  isActive: z.boolean().optional(),
});

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: treatyTypeId } = await params;
    const body = await req.json();

    // Validate input
    const validation = treatyTypeUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    // Check if the treaty type exists
    const existingType = await prisma.treatyType.findUnique({
      where: { id: treatyTypeId },
    });

    if (!existingType) {
      return NextResponse.json({ error: 'Treaty type not found' }, { status: 404 });
    }

    const updateData: any = {};
    const { name, description, isActive } = validation.data;

    if (name && name !== existingType.name) {
      // Check if new name conflicts with existing
      const nameConflict = await prisma.treatyType.findFirst({
        where: { 
          name,
          id: { not: treatyTypeId }
        },
      });

      if (nameConflict) {
        return NextResponse.json(
          { error: 'Treaty type with this name already exists' },
          { status: 400 }
        );
      }
      updateData.name = name;
    }

    if (description !== undefined) updateData.description = description;
    if (isActive !== undefined) updateData.isActive = isActive;

    // Update treaty type
    const treatyType = await prisma.treatyType.update({
      where: { id: treatyTypeId },
      data: updateData,
    });

    return NextResponse.json({
      message: 'Treaty type updated successfully',
      treatyType: {
        id: treatyType.id,
        name: treatyType.name,
        description: treatyType.description,
        isActive: treatyType.isActive,
        createdAt: treatyType.createdAt,
        updatedAt: treatyType.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error updating treaty type:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: treatyTypeId } = await params;

    // Check if the treaty type exists and has associated data
    const existingType = await prisma.treatyType.findUnique({
      where: { id: treatyTypeId },
      include: {
        treatyTreatyTypes: {
          select: { id: true },
        },
        userTreatyNumbers: {
          select: { id: true },
        },
        userTreatyTypes: {
          select: { id: true },
        },
      },
    });

    if (!existingType) {
      return NextResponse.json({ error: 'Treaty type not found' }, { status: 404 });
    }

    // Check if treaty type has associated treaties
    if (existingType.treatyTreatyTypes.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete treaty type that has associated treaties' },
        { status: 400 }
      );
    }

    // Check if treaty type has associated user treaty numbers
    if (existingType.userTreatyNumbers.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete treaty type that has associated user treaty numbers' },
        { status: 400 }
      );
    }

    // Check if treaty type has associated user treaty types
    if (existingType.userTreatyTypes.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete treaty type that has associated user treaty types' },
        { status: 400 }
      );
    }

    // Delete the treaty type
    await prisma.treatyType.delete({
      where: { id: treatyTypeId },
    });

    return NextResponse.json({
      message: 'Treaty type deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting treaty type:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}