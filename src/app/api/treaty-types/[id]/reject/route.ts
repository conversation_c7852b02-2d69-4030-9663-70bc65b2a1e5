import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    const isAdmin = user?.userRoles.some(ur => ur.role.name === 'admin');
    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const treatyTypeId = id;
    const { reason } = await req.json();

    // Check if treaty type exists
    const existingType = await prisma.treatyType.findUnique({
      where: { id: treatyTypeId },
    });

    if (!existingType) {
      return NextResponse.json({ error: 'Treaty type not found' }, { status: 404 });
    }

    // Update treaty type status to REJECTED
    const treatyType = await prisma.treatyType.update({
      where: { id: treatyTypeId },
      data: {
        status: 'REJECTED',
        reviewedBy: session.user.id,
        reviewedAt: new Date(),
        isActive: false, // Also deactivate rejected types
      },
    });

    // Create rejection notification
    await createRejectionNotification(treatyType, session.user.id, reason);
    
    // Broadcast notification to connected clients
    await broadcastRejectionNotification(treatyType, session.user.id, reason);

    return NextResponse.json({
      message: 'Treaty type rejected successfully',
      treatyType: {
        id: treatyType.id,
        name: treatyType.name,
        status: treatyType.status,
      },
    });
  } catch (error) {
    console.error('Error rejecting treaty type:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to create rejection notification
async function createRejectionNotification(treatyType: any, rejectedById: string, reason?: string) {
  try {
    if (treatyType.requestedBy) {
      const rejectingUser = await prisma.user.findUnique({
        where: { id: rejectedById },
        select: { name: true, email: true },
      });

      await prisma.notification.create({
        data: {
          type: 'TREATY_TYPE_REJECTED',
          message: `Your treaty type "${treatyType.name}" was rejected${reason ? `: ${reason}` : ''}`,
          data: {
            typeId: treatyType.id,
            typeName: treatyType.name,
            rejectedBy: rejectedById,
            rejectedByName: rejectingUser?.name || rejectingUser?.email || 'Admin',
            rejectedAt: new Date().toISOString(),
            reason: reason || 'No reason provided',
          },
          recipients: [treatyType.requestedBy],
        },
      });
    }
  } catch (error) {
    console.error('Error creating rejection notification:', error);
  }
}

// Helper function to broadcast rejection notification
async function broadcastRejectionNotification(treatyType: any, rejectedById: string, reason?: string) {
  try {
    const rejectingUser = await prisma.user.findUnique({
      where: { id: rejectedById },
      select: { name: true, email: true },
    });

    const notificationData = {
      type: 'TREATY_TYPE_REJECTED',
      message: `Treaty type "${treatyType.name}" was rejected${reason ? `: ${reason}` : ''}`,
      data: {
        typeId: treatyType.id,
        typeName: treatyType.name,
        rejectedBy: rejectedById,
        rejectedByName: rejectingUser?.name || rejectingUser?.email || 'Admin',
        rejectedAt: new Date().toISOString(),
        reason: reason || 'No reason provided',
      },
      recipients: ['admin'], // Broadcast to all admins
      createdAt: new Date(),
    };

    // Import and use the broadcast function
    const { broadcastNotification } = await import('@/lib/notifications');
    await broadcastNotification(notificationData);
  } catch (error) {
    console.error('Error broadcasting rejection notification:', error);
  }
}