import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { 
  createStandardizedHandler, 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  with<PERSON><PERSON><PERSON>andler,
  createJsonResponse,
  createSuccessResponse,
  StandardizedHandlerContext
} from '@/lib/middleware';

// Example 1: Public endpoint (no authentication required)
async function publicExampleHandler(context: StandardizedHandlerContext) {
  return createJsonResponse({ message: 'This is a public endpoint', timestamp: new Date().toISOString() });
}

export const GET = createStandardizedHandler(publicExampleHandler, { 
  auth: { auditLogging: false }, // No auth required, minimal logging
  rateLimit: true
});

// Example 2: Admin-only endpoint
async function adminExampleHandler(context: StandardizedHandlerContext) {
  const { auth } = context;
  
  // Fetch some admin data
  const users = await prisma.user.findMany({
    take: 10,
    select: {
      id: true,
      email: true,
      name: true,
      createdAt: true
    },
    orderBy: { createdAt: 'desc' }
  });

  return createJsonResponse({
    message: 'Admin data fetched successfully',
    users,
    authenticatedUser: {
      id: auth.userId,
      email: auth.email,
      roles: auth.roles
    }
  });
}

export const POST = withAdminHandler(adminExampleHandler, {
  rateLimit: true,
  auditLogging: true
});

// Example 3: Role-specific endpoint
async function moderatorExampleHandler(context: StandardizedHandlerContext) {
  const { auth } = context;
  
  return createSuccessResponse(
    'Moderator action completed successfully',
    {
      user: {
        id: auth.userId,
        roles: auth.roles,
        permissions: auth.permissions
      },
      action: 'moderator_task',
      timestamp: new Date().toISOString()
    }
  );
}

export const PUT = withRoleHandler(moderatorExampleHandler, ['moderator', 'admin'], {
  rateLimit: true
});