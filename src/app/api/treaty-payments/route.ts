import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { PaymentStatus, PaymentMethod, ApplicationStatus } from '@prisma/client';

// POST /api/treaty-payments - Submit payment for an application
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      applicationId,
      amount,
      paymentMethod,
      payerName,
      payerContact,
      notes,
    } = body;

    // Validate required fields
    if (!applicationId || !amount || !paymentMethod || !payerName || !payerContact) {
      return NextResponse.json(
        { error: 'All required fields must be provided' },
        { status: 400 }
      );
    }

    // Get the application
    const application = await prisma.userTreatyType.findUnique({
      where: { id: applicationId },
      include: { treatyType: true },
    });

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    // Verify user owns the application
    if (application.userId !== (session.user as any).id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Check if application is in a state that allows payment submission
    if (application.status !== ApplicationStatus.APPROVED) {
      return NextResponse.json(
        { error: 'Payment can only be submitted for approved applications' },
        { status: 400 }
      );
    }

    // Check if payment amount matches treaty type price
    if (amount !== application.treatyType.price) {
      return NextResponse.json(
        { error: `Payment amount must be exactly ${application.treatyType.price} ${application.treatyType.currency || 'USD'}` },
        { status: 400 }
      );
    }

    // Check if there's already a pending payment for this application
    const existingPayment = await prisma.payment.findFirst({
      where: {
        userTreatyTypeId: applicationId,
        status: {
          in: [PaymentStatus.PENDING, PaymentStatus.AWAITING_PAYMENT],
        },
      },
    });

    if (existingPayment) {
      return NextResponse.json(
        { error: 'A payment is already pending for this application' },
        { status: 409 }
      );
    }

    // Create the payment
    const payment = await prisma.payment.create({
      data: {
        userTreatyTypeId: applicationId,
        amount,
        currency: application.treatyType.currency || 'USD',
        paymentMethod: paymentMethod as PaymentMethod,
        status: PaymentStatus.AWAITING_PAYMENT,
        paymentDate: new Date(),
        notes: notes || null,
      },
      include: {
        userTreatyType: {
          include: {
            treatyType: true,
            user: true,
          },
        },
      },
    });

    // Update application payment status
    await prisma.userTreatyType.update({
      where: { id: applicationId },
      data: {
        paymentStatus: PaymentStatus.AWAITING_PAYMENT,
      },
    });

    // Log the payment submission
    await prisma.auditLog.create({
      data: {
        userId: (session.user as any).id,
        action: 'SUBMIT_PAYMENT',
        resource: 'Payment',
        resourceId: payment.id,
        oldValues: { id: null, userTreatyTypeId: null, amount: null },
        newValues: {
          userTreatyTypeId: applicationId,
          amount,
          paymentMethod,
          status: PaymentStatus.AWAITING_PAYMENT,
        },
        success: true,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    });

    return NextResponse.json({
      success: true,
      payment: {
        id: payment.id,
        userTreatyTypeId: payment.userTreatyTypeId,
        amount: payment.amount,
        currency: payment.currency,
        paymentMethod: payment.paymentMethod,
        status: payment.status,
        paymentDate: payment.paymentDate,
        notes: payment.notes,
        createdAt: payment.createdAt,
        application: {
          id: payment.userTreatyType.id,
          status: payment.userTreatyType.status,
          paymentStatus: payment.userTreatyType.paymentStatus,
          treatyType: payment.userTreatyType.treatyType,
        },
      },
    });
  } catch (error) {
    console.error('Error submitting payment:', error);
    return NextResponse.json(
      { error: 'Failed to submit payment' },
      { status: 500 }
    );
  }
}

// GET /api/treaty-payments - List user's payments
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') as PaymentStatus | null;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    const skip = (page - 1) * limit;

    // Build where clause
    const where = {
      userTreatyType: {
        userId: (session.user as any).id,
      },
      ...(status && { status }),
    };

    // Get payments with pagination
    const [payments, total] = await Promise.all([
      prisma.payment.findMany({
        where,
        include: {
          userTreatyType: {
            include: {
              treatyType: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.payment.count({ where }),
    ]);

    return NextResponse.json({
      payments,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching payments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payments' },
      { status: 500 }
    );
  }
}