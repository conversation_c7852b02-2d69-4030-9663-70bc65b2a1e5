import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/countries - Get all countries
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('q') || '';
    const limit = parseInt(searchParams.get('limit') || '100', 10);

    const where: any = {};

    if (search) {
      where.name = {
        contains: search,
        mode: 'insensitive'
      };
    }

    const countries = await prisma.country.findMany({
      where,
      orderBy: {
        name: 'asc',
      },
      take: limit,
      select: {
        id: true,
        name: true,
        code: true,
      },
    });

    return NextResponse.json(countries);
  } catch (error) {
    console.error('Error fetching countries:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}