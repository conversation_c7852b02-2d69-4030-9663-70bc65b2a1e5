import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

// Input sanitization function
function sanitizeInput(input: string): string {
  if (!input) return input;
  return input.trim().replace(/[<>]/g, '');
}

// Validation schema for country update
const countryUpdateSchema = z.object({
  name: z.string().min(1, 'Country name is required').max(255, 'Country name too long').optional(),
  code: z.string().length(3, 'Country code must be exactly 3 characters').regex(/^[A-Z]{3}$/, 'Country code must be uppercase letters only').optional(),
});

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const parsedId = parseInt(id);
    if (isNaN(parsedId)) {
      return NextResponse.json({ error: 'Invalid country ID' }, { status: 400 });
    }

    const body = await request.json();

    // Validate input
    const validation = countryUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { name, code } = validation.data;

    // Check if country exists
    const existingCountry = await prisma.country.findUnique({
      where: { id: parsedId },
    });

    if (!existingCountry) {
      return NextResponse.json({ error: 'Country not found' }, { status: 404 });
    }

    // Check for duplicates if name is being updated
    if (name && name !== existingCountry.name) {
      const duplicateName = await prisma.country.findFirst({
        where: { name: sanitizeInput(name) },
      });
      if (duplicateName) {
        return NextResponse.json({ error: 'Country with this name already exists' }, { status: 400 });
      }
    }

    // Check for duplicates if code is being updated
    if (code && code !== existingCountry.code) {
      const duplicateCode = await prisma.country.findFirst({
        where: { code: sanitizeInput(code).toUpperCase() },
      });
      if (duplicateCode) {
        return NextResponse.json({ error: 'Country with this code already exists' }, { status: 400 });
      }
    }

    // Update country
    const updatedCountry = await prisma.country.update({
      where: { id: parsedId },
      data: {
        ...(name && { name: sanitizeInput(name) }),
        ...(code && { code: sanitizeInput(code).toUpperCase() }),
      },
    });

    return NextResponse.json({
      message: 'Country updated successfully',
      country: updatedCountry,
    });
  } catch (error) {
    console.error('Error updating country:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const parsedId = parseInt(id);
    if (isNaN(parsedId)) {
      return NextResponse.json({ error: 'Invalid country ID' }, { status: 400 });
    }

    // Check if country exists
    const existingCountry = await prisma.country.findUnique({
      where: { id: parsedId },
      include: {
        cities: true,
      },
    });

    if (!existingCountry) {
      return NextResponse.json({ error: 'Country not found' }, { status: 404 });
    }

    // Check if country has cities
    if (existingCountry.cities.length > 0) {
      return NextResponse.json({
        error: 'Cannot delete country with existing cities. Please delete all cities first.'
      }, { status: 400 });
    }

    // Delete country
    await prisma.country.delete({
      where: { id: parsedId },
    });

    return NextResponse.json({
      message: 'Country deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting country:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}