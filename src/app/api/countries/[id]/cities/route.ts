import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/countries/[id]/cities - Get cities for a country
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('q') || '';
    const limit = parseInt(searchParams.get('limit') || '100', 10);

    // Verify the country exists
    const country = await prisma.country.findUnique({
      where: { id: parseInt(id) },
    });

    if (!country) {
      return NextResponse.json({ error: 'Country not found' }, { status: 404 });
    }

    const where: any = {
      countryId: parseInt(id),
    };

    if (search) {
      where.name = {
        contains: search,
        mode: 'insensitive'
      };
    }

    const cities = await prisma.city.findMany({
      where,
      orderBy: {
        name: 'asc',
      },
      take: limit,
      select: {
        id: true,
        name: true,
        countryId: true,
      },
    });

    return NextResponse.json(cities);
  } catch (error) {
    console.error('Error fetching cities:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}