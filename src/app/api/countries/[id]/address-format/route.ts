import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { performanceMonitor } from '@/lib/performance';

// Simple in-memory cache for address formats
const addressFormatCache = new Map<number, any>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

interface CacheEntry {
  data: any;
  timestamp: number;
}

interface FieldConfig {
  name: string;
  label: string;
  required: boolean;
  validation: string;
  placeholder?: string;
  pattern?: string;
}

interface CountryConfig {
  additionalFields: FieldConfig[];
  fieldOrder: string[];
  validationRules: Record<string, { pattern: string; message: string }>;
}

function getFromCache(countryId: number): any | null {
  const entry = addressFormatCache.get(countryId) as CacheEntry;
  if (entry && (Date.now() - entry.timestamp) < CACHE_DURATION) {
    return entry.data;
  }
  return null;
}

function setCache(countryId: number, data: any): void {
  addressFormatCache.set(countryId, {
    data,
    timestamp: Date.now()
  });
}

// Clear cache function for internal use
function clearCache(): void {
  addressFormatCache.clear();
}

function getEnhancedAddressFormat(countryId: number, countryName?: string) {
  // Base fields that all countries have
  const baseFields = [
    {
      name: 'aptSuiteUnit',
      label: 'Apt/Suite/Unit',
      required: false,
      validation: 'string',
      placeholder: 'Apartment, suite, unit, etc.'
    },
    {
      name: 'streetAddress',
      label: 'Street Address',
      required: true,
      validation: 'string',
      placeholder: 'Street address'
    },
    {
      name: 'country',
      label: 'Country',
      required: true,
      validation: 'string',
      placeholder: countryName || 'Country'
    }
  ];

  // Country-specific configurations
  const countryConfigs: Record<string, CountryConfig> = {
    'US': {
      additionalFields: [
        {
          name: 'townCity',
          label: 'City',
          required: true,
          validation: 'string',
          placeholder: 'City'
        },
        {
          name: 'stateProvince',
          label: 'State',
          required: true,
          validation: 'string',
          placeholder: 'State'
        },
        {
          name: 'postcodeZip',
          label: 'ZIP Code',
          required: true,
          validation: 'string',
          pattern: '^\\d{5}(-\\d{4})?$',
          placeholder: '12345 or 12345-6789'
        }
      ],
      fieldOrder: ['streetAddress', 'aptSuiteUnit', 'townCity', 'stateProvince', 'postcodeZip', 'country'],
      validationRules: {
        stateProvince: {
          pattern: '^[A-Z]{2}$',
          message: 'Please enter a valid 2-letter state code'
        },
        postcodeZip: {
          pattern: '^\\d{5}(-\\d{4})?$',
          message: 'Please enter a valid ZIP code'
        }
      }
    },
    'NZ': {
      additionalFields: [
        {
          name: 'townCity',
          label: 'Town/City',
          required: true,
          validation: 'string',
          placeholder: 'Town or city'
        },
        {
          name: 'postcodeZip',
          label: 'Postcode',
          required: true,
          validation: 'string',
          pattern: '^\\d{4}$',
          placeholder: '1234'
        }
      ],
      fieldOrder: ['streetAddress', 'aptSuiteUnit', 'townCity', 'postcodeZip', 'country'],
      validationRules: {
        postcodeZip: {
          pattern: '^\\d{4}$',
          message: 'Please enter a valid 4-digit postcode'
        }
      }
    },
    'GB': {
      additionalFields: [
        {
          name: 'townCity',
          label: 'Town/City',
          required: true,
          validation: 'string',
          placeholder: 'Town or city'
        },
        {
          name: 'stateProvince',
          label: 'County',
          required: false,
          validation: 'string',
          placeholder: 'County'
        },
        {
          name: 'postcodeZip',
          label: 'Postcode',
          required: true,
          validation: 'string',
          pattern: '^[A-Z]{1,2}\\d[A-Z\\d]? ?\\d[A-Z]{2}$',
          placeholder: 'SW1A 1AA'
        }
      ],
      fieldOrder: ['streetAddress', 'aptSuiteUnit', 'townCity', 'stateProvince', 'postcodeZip', 'country'],
      validationRules: {
        postcodeZip: {
          pattern: '^[A-Z]{1,2}\\d[A-Z\\d]? ?\\d[A-Z]{2}$',
          message: 'Please enter a valid UK postcode'
        }
      }
    }
  };

  // Get country code from database or use default
  let countryCode = '';
  // This would ideally come from the country record, but for now we'll use country name mapping
  const countryNameMap: Record<string, string> = {
    'United States': 'US',
    'New Zealand': 'NZ',
    'United Kingdom': 'GB',
    'Australia': 'AU',
    'Canada': 'CA'
  };

  if (countryName && countryNameMap[countryName]) {
    countryCode = countryNameMap[countryName];
  }

  const config = countryConfigs[countryCode] || countryConfigs['US']; // Default to US format

  return {
    id: null,
    countryId: countryId,
    fields: [...baseFields, ...config.additionalFields],
    fieldOrder: config.fieldOrder,
    validationRules: config.validationRules,
    postalCodeLabel: config.additionalFields.find((f: FieldConfig) => f.name === 'postcodeZip')?.label || 'Postal Code',
    postalCodeRequired: config.additionalFields.find((f: FieldConfig) => f.name === 'postcodeZip')?.required || false,
    regionLabel: config.additionalFields.find((f: FieldConfig) => f.name === 'stateProvince')?.label || 'Region',
    regionRequired: config.additionalFields.find((f: FieldConfig) => f.name === 'stateProvince')?.required || false,
    townLabel: config.additionalFields.find((f: FieldConfig) => f.name === 'townCity')?.label || 'City',
    townRequired: config.additionalFields.find((f: FieldConfig) => f.name === 'townCity')?.required || true,
    addressTemplate: '{street}\\n{town}\\n{country}',
    createdAt: null,
    updatedAt: null
  };
}

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  performanceMonitor.startTimer('api_country_address_format_get', { endpoint: '/api/countries/[id]/address-format' });
  try {
    const { id } = await params;

    // Validate id parameter
    if (!id || id.trim() === '') {
      return NextResponse.json(
        {
          error: 'Bad Request',
          message: 'Country ID is required'
        },
        { status: 400 }
      );
    }

    const countryId = parseInt(id, 10);
    if (isNaN(countryId) || countryId <= 0) {
      return NextResponse.json(
        {
          error: 'Bad Request',
          message: 'Country ID must be a valid integer'
        },
        { status: 400 }
      );
    }

    // Check cache first
    const cachedFormat = getFromCache(countryId);
    if (cachedFormat) {
      return NextResponse.json({ 
        success: true,
        data: cachedFormat 
      }, { status: 200 });
    }

    // Try to fetch address format from database
    let addressFormat = await prisma.countryAddressFormat.findUnique({
      where: {
        countryId: countryId,
      },
    });

    if (addressFormat) {
      // For existing database records, enhance them with field configurations
      const enhancedFormat = getEnhancedAddressFormat(countryId);
      
      // Cache the result
      setCache(countryId, enhancedFormat);
      return NextResponse.json({ 
        success: true,
        data: enhancedFormat 
      }, { status: 200 });
    }

    // If no address format found, check if country exists
    const country = await prisma.country.findUnique({
      where: { id: countryId }
    });

    if (!country) {
      return NextResponse.json(
        {
          error: 'Not Found',
          message: 'Country not found'
        },
        { status: 404 }
      );
    }

    // Return enhanced address format with country-specific configurations
    const enhancedFormat = getEnhancedAddressFormat(countryId, country.name);
    
    // Cache the enhanced format
    setCache(countryId, enhancedFormat);
    
    return NextResponse.json({ 
      success: true,
      data: enhancedFormat 
    }, { status: 200 });

  } catch (error) {
    console.error('Error fetching address format:', error);
    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'An error occurred while fetching address format'
      },
      { status: 500 }
    );
  } finally {
    performanceMonitor.endTimer('api_country_address_format_get');
  }
}
