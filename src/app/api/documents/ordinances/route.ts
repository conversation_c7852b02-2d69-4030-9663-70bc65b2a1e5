import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for ordinance creation
const ordinanceCreateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name must be less than 255 characters'),
  description: z.string().min(1, 'Description is required').max(1000, 'Description must be less than 1000 characters'),
  type: z.literal('ordinance').optional().default('ordinance'),
});

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const ordinanceId = searchParams.get('id');
    
    // If an ID is provided, return a specific ordinance
    if (ordinanceId) {
      const ordinance = await prisma.ordinance.findUnique({
        where: {
          id: ordinanceId,
          userId: (session.user as any).id,
        },
        include: {
          ordinanceType: true,
          documents: {
            orderBy: {
              uploadedAt: 'desc',
            },
          },
        },
      });
      
      if (!ordinance) {
        return NextResponse.json({ error: 'Ordinance not found' }, { status: 404 });
      }
      
      return NextResponse.json({
        id: ordinance.id,
        name: ordinance.notes || ordinance.ordinanceType?.name || 'Unnamed Ordinance',
        description: ordinance.notes || '',
        status: ordinance.status,
        completedDate: ordinance.completedDate,
        expirationDate: ordinance.expirationDate,
        documents: ordinance.documents.map(doc => ({
          id: doc.id,
          filePath: doc.filePath,
          fileName: doc.fileName,
          fileSize: doc.fileSize,
          fileType: doc.fileType,
          uploadedAt: doc.uploadedAt,
        })),
        createdAt: ordinance.createdAt,
        updatedAt: ordinance.updatedAt,
      });
    }
    
    const skip = (page - 1) * limit;
    
    // Build where clause for search
    const where: any = {
      userId: (session.user as any).id,
    };
    
    if (search) {
      where.OR = [
        { ordinanceType: { name: { contains: search, mode: 'insensitive' } } },
        { notes: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    // Fetch ordinances with pagination
    const [ordinances, totalCount] = await Promise.all([
      prisma.ordinance.findMany({
        where,
        include: {
          ordinanceType: true,
          documents: {
            orderBy: {
              uploadedAt: 'desc',
            },
          },
        },
        orderBy: {
          createdAt: 'desc', // Most recent first
        },
        skip,
        take: limit,
      }),
      prisma.ordinance.count({ where }),
    ]);
    
    const totalPages = Math.ceil(totalCount / limit);
    
    return NextResponse.json({
      ordinances: ordinances.map(ordinance => ({
        id: ordinance.id,
        name: ordinance.notes || ordinance.ordinanceType?.name || 'Unnamed Ordinance',
        description: ordinance.notes || '',
        status: ordinance.status,
        completedDate: ordinance.completedDate,
        expirationDate: ordinance.expirationDate,
        documents: ordinance.documents.map(doc => ({
          id: doc.id,
          filePath: doc.filePath,
          fileName: doc.fileName,
          fileSize: doc.fileSize,
          fileType: doc.fileType,
          uploadedAt: doc.uploadedAt,
        })),
        createdAt: ordinance.createdAt,
        updatedAt: ordinance.updatedAt,
      })),
      totalPages,
      totalCount,
      currentPage: page,
      pageSize: limit,
    });
  } catch (error) {
    console.error('Error fetching ordinances:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Handle both FormData (with files) and JSON (without files)
    const contentType = req.headers.get('content-type');
    let validatedData;
    let files: File[] = [];

    if (contentType?.includes('multipart/form-data')) {
      // Handle FormData with files
      const formData = await req.formData();
      const name = formData.get('name') as string;
      const description = formData.get('description') as string;
      const type = formData.get('type') as string;
      
      // Get all files
      const fileEntries = formData.getAll('documents') as File[];
      files = fileEntries.filter(file => file.size > 0);

      // Validate form data
      const validation = ordinanceCreateSchema.safeParse({ name, description, type });
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Validation error', details: validation.error.flatten() },
          { status: 400 }
        );
      }
      validatedData = validation.data;
    } else {
      // Handle JSON
      const body = await req.json();
      const validation = ordinanceCreateSchema.safeParse(body);
      
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Validation error', details: validation.error.flatten() },
          { status: 400 }
        );
      }
      validatedData = validation.data;
    }

    const { name, description } = validatedData;
    const userId = (session.user as any).id;
    
    // Get or create a default ordinance type
    let ordinanceType = await prisma.ordinanceType.findFirst({
      where: { 
        name: 'General',
        isActive: true 
      },
    });
    
    if (!ordinanceType) {
      ordinanceType = await prisma.ordinanceType.create({
        data: {
          name: 'General',
          description: 'General ordinance type',
          category: 'general',
          isActive: true,
        },
      });
    }
    
    // Check for duplicate ordinance
    const existingOrdinance = await prisma.ordinance.findFirst({
      where: {
        userId,
        ordinanceTypeId: ordinanceType.id,
        notes: name,
      },
    });
    
    if (existingOrdinance) {
      return NextResponse.json(
        { error: 'An ordinance with the same name already exists' },
        { status: 400 }
      );
    }
    
    // Determine status based on user role
    const userRoles = await prisma.userRole.findMany({
      where: { userId },
      include: { role: true }
    });
    
    const isAdmin = userRoles.some(userRole => userRole.role.name === 'admin');
    const status = isAdmin ? 'COMPLETED' : 'PENDING';
    
    // Create ordinance record
    const ordinance = await prisma.ordinance.create({
      data: {
        userId,
        ordinanceTypeId: ordinanceType.id,
        notes: name, // Using name as notes for now
        status,
      },
    });
    
    // Handle file uploads if present
    if (files.length > 0) {
      // For now, we'll create document records without actual file storage
      // You can implement MinIO storage here later
      for (const file of files) {
        await prisma.ordinanceDocument.create({
          data: {
            ordinanceId: ordinance.id,
            fileName: file.name,
            fileSize: file.size,
            fileType: file.type,
            filePath: `ordinances/${ordinance.id}/${file.name}`, // Placeholder path
          },
        });
      }
    }
    
    return NextResponse.json({
      message: 'Ordinance created successfully',
      id: ordinance.id,
      ordinance: {
        id: ordinance.id,
        name: name,
        description: description,
        status: ordinance.status,
        createdAt: ordinance.createdAt,
        updatedAt: ordinance.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error creating ordinance:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
