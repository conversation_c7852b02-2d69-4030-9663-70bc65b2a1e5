import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for directive creation
const directiveCreateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name must be less than 255 characters'),
  description: z.string().min(1, 'Description is required').max(1000, 'Description must be less than 1000 characters'),
  type: z.literal('directive').optional().default('directive'),
});

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const directiveId = searchParams.get('id');
    
    // Since we don't have a directives table yet, we'll use ordinances with a special type
    // or return empty for now. Let's return empty results for now.
    
    if (directiveId) {
      // For now, return 404 since we don't have directives table yet
      return NextResponse.json({ error: 'Directive not found' }, { status: 404 });
    }
    
    // Return empty results for now - you can implement actual storage later
    return NextResponse.json({
      directives: [],
      totalPages: 1,
      totalCount: 0,
      currentPage: page,
      pageSize: limit,
    });
  } catch (error) {
    console.error('Error fetching directives:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Handle both FormData (with files) and JSON (without files)
    const contentType = req.headers.get('content-type');
    let validatedData;
    let files: File[] = [];

    if (contentType?.includes('multipart/form-data')) {
      // Handle FormData with files
      const formData = await req.formData();
      const name = formData.get('name') as string;
      const description = formData.get('description') as string;
      const type = formData.get('type') as string;
      
      // Get all files
      const fileEntries = formData.getAll('documents') as File[];
      files = fileEntries.filter(file => file.size > 0);

      // Validate form data
      const validation = directiveCreateSchema.safeParse({ name, description, type });
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Validation error', details: validation.error.flatten() },
          { status: 400 }
        );
      }
      validatedData = validation.data;
    } else {
      // Handle JSON
      const body = await req.json();
      const validation = directiveCreateSchema.safeParse(body);
      
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Validation error', details: validation.error.flatten() },
          { status: 400 }
        );
      }
      validatedData = validation.data;
    }

    const { name, description } = validatedData;
    const userId = (session.user as any).id;
    
    // For now, we'll create directives using the ordinances table with a special type
    // You can create a separate directives table later if needed
    
    // Get or create a directive ordinance type
    let directiveType = await prisma.ordinanceType.findFirst({
      where: { 
        name: 'Directive',
        isActive: true 
      },
    });
    
    if (!directiveType) {
      directiveType = await prisma.ordinanceType.create({
        data: {
          name: 'Directive',
          description: 'Directive document type',
          category: 'directive',
          isActive: true,
        },
      });
    }
    
    // Check for duplicate directive
    const existingDirective = await prisma.ordinance.findFirst({
      where: {
        userId,
        ordinanceTypeId: directiveType.id,
        notes: name,
      },
    });
    
    if (existingDirective) {
      return NextResponse.json(
        { error: 'A directive with the same name already exists' },
        { status: 400 }
      );
    }
    
    // Determine status based on user role
    const userRoles = await prisma.userRole.findMany({
      where: { userId },
      include: { role: true }
    });
    
    const isAdmin = userRoles.some(userRole => userRole.role.name === 'admin');
    const status = isAdmin ? 'COMPLETED' : 'PENDING';
    
    // Create directive record (using ordinance table)
    const directive = await prisma.ordinance.create({
      data: {
        userId,
        ordinanceTypeId: directiveType.id,
        notes: name, // Using name as notes for now
        status,
      },
    });
    
    // Handle file uploads if present
    if (files.length > 0) {
      // For now, we'll create document records without actual file storage
      // You can implement MinIO storage here later
      for (const file of files) {
        await prisma.ordinanceDocument.create({
          data: {
            ordinanceId: directive.id,
            fileName: file.name,
            fileSize: file.size,
            fileType: file.type,
            filePath: `directives/${directive.id}/${file.name}`, // Placeholder path
          },
        });
      }
    }
    
    return NextResponse.json({
      message: 'Directive created successfully',
      id: directive.id,
      directive: {
        id: directive.id,
        name: name,
        description: description,
        status: directive.status,
        createdAt: directive.createdAt,
        updatedAt: directive.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error creating directive:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
