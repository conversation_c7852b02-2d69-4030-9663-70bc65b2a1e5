import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/treaties/[treatyId]/types - Get treaty types for a specific treaty
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ treatyId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { treatyId } = await params;

    // Get treaty types associated with this treaty
    const treatyTreatyTypes = await prisma.treatyTreatyType.findMany({
      where: { treatyId },
      include: {
        treatyType: {
          select: {
            id: true,
            name: true,
            description: true,
            category: true,
            price: true,
            isActive: true,
            status: true
          }
        }
      },
      orderBy: {
        treatyType: {
          name: 'asc'
        }
      }
    });

    // Transform data for frontend
    const treatyTypes = treatyTreatyTypes
      .filter(ttt => ttt.treatyType.isActive && ttt.treatyType.status === 'APPROVED')
      .map(ttt => ({
        id: ttt.treatyType.id,
        name: ttt.treatyType.name,
        description: ttt.treatyType.description,
        category: ttt.treatyType.category,
        price: ttt.treatyType.price,
        assignedAt: ttt.assignedAt
      }));

    return NextResponse.json({ treatyTypes });
  } catch (error) {
    console.error('Error fetching treaty types:', error);
    return NextResponse.json(
      { error: 'Failed to fetch treaty types' },
      { status: 500 }
    );
  }
}