import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';

// Initialize S3 client for MinIO
const s3Client = new S3Client({
  endpoint: process.env.MINIO_ENDPOINT,
  region: process.env.MINIO_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.MINIO_ACCESS_KEY || '',
    secretAccessKey: process.env.MINIO_SECRET_KEY || '',
  },
  forcePathStyle: true, // Required for MinIO
});

// Validation schema for file attachment
const fileAttachmentSchema = z.object({
  fileName: z.string().min(1, 'File name is required'),
  fileType: z.string().min(1, 'File type is required'),
  fileSize: z.number().min(1, 'File size must be greater than 0'),
});

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const treatyId = searchParams.get('treatyId');
    
    if (!treatyId) {
      return NextResponse.json({ error: 'Treaty ID is required' }, { status: 400 });
    }
    
    // Check if the treaty exists
    const treaty = await prisma.treaty.findUnique({
      where: {
        id: treatyId,
      },
    });

    if (!treaty) {
      return NextResponse.json({ error: 'Treaty not found' }, { status: 404 });
    }
    
    const formData = await req.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json({ error: 'File is required' }, { status: 400 });
    }
    
    // Validate file
    const validation = fileAttachmentSchema.safeParse({
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
    });
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    // Generate a unique file name
    const fileExtension = file.name.split('.').pop();
    const uniqueFileName = `${uuidv4()}.${fileExtension}`;
    
    // Upload file to MinIO
    const bucketName = process.env.MINIO_BUCKET_NAME || 'nwa-files';
    const fileKey = `treaties/${treatyId}/${uniqueFileName}`;
    
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    const uploadParams = {
      Bucket: bucketName,
      Key: fileKey,
      Body: buffer,
      ContentType: file.type,
      Metadata: {
        'original-name': file.name,
        'user-id': (session.user as any).id,
        'treaty-id': treatyId,
      },
    };
    
    await s3Client.send(new PutObjectCommand(uploadParams));
    
    // Create file attachment record
    const fileAttachment = await prisma.fileAttachment.create({
      data: {
        treatyId,
        fileName: file.name,
        filePath: fileKey,
        mimeType: file.type,
        fileSize: file.size,
      },
    });
    
    return NextResponse.json({
      message: 'File uploaded successfully',
      id: fileAttachment.id,
      fileAttachment: {
        id: fileAttachment.id,
        fileName: fileAttachment.fileName,
        filePath: fileAttachment.filePath,
        mimeType: fileAttachment.mimeType,
        fileSize: fileAttachment.fileSize,
        uploadedAt: fileAttachment.uploadedAt,
      },
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const attachmentId = searchParams.get('id');
    
    if (!attachmentId) {
      return NextResponse.json({ error: 'Attachment ID is required' }, { status: 400 });
    }
    
    // Check if the file attachment belongs to a treaty owned by the user
    const fileAttachment = await prisma.fileAttachment.findUnique({
      where: {
        id: attachmentId,
      },
      include: {
        treaty: true,
      },
    });
    
    if (!fileAttachment) {
      return NextResponse.json({ error: 'File attachment not found' }, { status: 404 });
    }
    
    // Delete file from MinIO
    // Note: In a real implementation, you would also delete the file from MinIO
    // For now, we'll just delete the database record
    
    // Delete the file attachment record
    await prisma.fileAttachment.delete({
      where: {
        id: attachmentId,
      },
    });
    
    return NextResponse.json({
      message: 'File attachment deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting file attachment:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}