import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for treaty creation
const treatyCreateSchema = z.object({
  name: z.string().min(1, 'Treaty name is required').max(255, 'Name must be less than 255 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  treatyTypeIds: z.array(z.string()).optional(), // Array of treaty type IDs for many-to-many
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
  signedDate: z.string().optional(),
  expirationDate: z.string().optional(),
  renewalDate: z.string().optional(),
});

// Validation schema for treaty update
const treatyUpdateSchema = z.object({
  name: z.string().min(1, 'Treaty name is required').max(255, 'Name must be less than 255 characters').optional(),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  treatyTypeIds: z.array(z.string()).optional(), // Array of treaty type IDs for many-to-many
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
  status: z.enum(['ACTIVE', 'EXPIRED', 'TERMINATED', 'PENDING_RENEWAL']).optional(),
  signedDate: z.string().optional(),
  expirationDate: z.string().optional(),
  renewalDate: z.string().optional(),
});

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const treatyId = searchParams.get('id');

    // Resolve the correct user ID (same logic as POST)
    const userId = (session.user as any).id;
    let user = await prisma.user.findUnique({
      where: { id: userId },
    });

    // If user not found by ID, try to find by email
    if (!user && (session.user as any).email) {
      user = await prisma.user.findUnique({
        where: { email: (session.user as any).email },
      });
    }

    // If still no user, return empty results
    if (!user) {
      return NextResponse.json({
        treaties: [],
        pagination: {
          currentPage: page,
          totalPages: 0,
          totalCount: 0,
          pageSize: limit,
        },
      });
    }

    const finalUserId = user.id;

    // If an ID is provided, return a specific treaty
    if (treatyId) {
      const treaty = await prisma.treaty.findUnique({
        where: {
          id: treatyId,
        },
        include: {
          treatyTreatyTypes: {
            include: {
              treatyType: true
            }
          }
        }
      });

      if (!treaty) {
        return NextResponse.json({ error: 'Treaty not found' }, { status: 404 });
      }

      return NextResponse.json({
        id: treaty.id,
        userId: treaty.userId,
        treatyTypeIds: treaty.treatyTreatyTypes.map(tt => tt.treatyTypeId),
        treatyTypes: treaty.treatyTreatyTypes.map(tt => tt.treatyType),
        status: treaty.status,
        notes: treaty.notes,
        signedDate: treaty.signedDate,
        expirationDate: treaty.expirationDate,
        renewalDate: treaty.renewalDate,
        documentPath: treaty.documentPath,
        createdAt: treaty.createdAt,
        updatedAt: treaty.updatedAt,
      });
    }

    const skip = (page - 1) * limit;

    // Build where clause for search
    const where: any = {
      userId: finalUserId,
    };
    
    if (search) {
      where.OR = [
        { treatyType: { name: { contains: search, mode: 'insensitive' } } },
        { notes: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    // Fetch treaties with pagination
    const [treaties, totalCount] = await Promise.all([
      prisma.treaty.findMany({
        where,
        include: {
          treatyTreatyTypes: {
            include: {
              treatyType: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          signedDate: 'desc', // Most recent first
        },
        skip,
        take: limit,
      }),
      prisma.treaty.count({ where }),
    ]);
    
    const totalPages = Math.ceil(totalCount / limit);
    
    return NextResponse.json({
      treaties: treaties.map(treaty => ({
        id: treaty.id,
        treatyTypeIds: treaty.treatyTreatyTypes.map(tt => tt.treatyTypeId),
        treatyTypes: treaty.treatyTreatyTypes.map(tt => ({
          id: tt.treatyTypeId,
          name: tt.treatyType.name,
        })),
        // Prefer the treaty name stored in notes (first non-empty line), fall back to first treaty type name
        title:
          (treaty.notes && treaty.notes.split('\n').find(line => line.trim().length > 0)?.trim()) ||
          (treaty.treatyTreatyTypes.length > 0 ? treaty.treatyTreatyTypes[0].treatyType.name : 'Unnamed Treaty'),
        status: treaty.status,
        notes: treaty.notes,
        signedDate: treaty.signedDate,
        expirationDate: treaty.expirationDate,
        renewalDate: treaty.renewalDate,
        documentPath: treaty.documentPath,
        createdAt: treaty.createdAt,
        updatedAt: treaty.updatedAt,
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        pageSize: limit,
      },
    });
  } catch (error) {
    console.error('Error fetching treaties:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    
    // Validate input
    const validation = treatyCreateSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    const { name, description, treatyTypeIds, notes, signedDate, expirationDate, renewalDate } = validation.data;

    const userId = (session.user as any).id;
    console.log('Session user ID:', userId);
    console.log('Session user object:', session.user);

    // First try to find user by ID
    let user = await prisma.user.findUnique({
      where: { id: userId },
    });

    // If user not found by ID, try to find by email
    if (!user && (session.user as any).email) {
      console.log('User not found by ID, trying by email...');
      user = await prisma.user.findUnique({
        where: { email: (session.user as any).email },
      });

      if (user) {
        console.log('Found existing user by email:', user.id);
        // Update the user ID to match the session (in case they changed)
        // But we can't update the ID as it's the primary key, so we'll use the existing user
      }
    }

    // If still no user found, create a new one
    if (!user) {
      console.log('User not found, creating new user...');

      try {
        user = await prisma.user.create({
          data: {
            id: userId,
            name: (session.user as any).name || 'Unknown User',
            email: (session.user as any).email || `${userId}@temp.com`,
          },
        });
        console.log('Created new user:', user.id);
      } catch (createError: any) {
        console.error('Failed to create user:', createError);

        // If email already exists, try with a different email
        if (createError.code === 'P2002' && createError.meta?.target?.includes('email')) {
          console.log('Email already exists, trying with modified email...');
          try {
            user = await prisma.user.create({
              data: {
                id: userId,
                name: (session.user as any).name || 'Unknown User',
                email: `${userId}@temp.com`,
              },
            });
            console.log('Created new user with temp email:', user.id);
          } catch (secondCreateError) {
            console.error('Failed to create user with temp email:', secondCreateError);
            return NextResponse.json(
              { error: 'Failed to create user account' },
              { status: 500 }
            );
          }
        } else {
          return NextResponse.json(
            { error: 'Failed to create user account' },
            { status: 500 }
          );
        }
      }
    }

    // Use the found or created user
    const finalUserId = user.id;

    // Validate treaty types if provided
    const treatyTypesToAssign: string[] = [];
    
    if (treatyTypeIds && treatyTypeIds.length > 0) {
      // Validate all provided treaty types exist
      const treatyTypes = await prisma.treatyType.findMany({
        where: { 
          id: { in: treatyTypeIds },
          isActive: true,
          status: 'APPROVED'
        },
      });

      // Check if all requested types were found
      const foundIds = treatyTypes.map(type => type.id);
      const missingIds = treatyTypeIds.filter(id => !foundIds.includes(id));
      
      if (missingIds.length > 0) {
        return NextResponse.json(
          { error: 'Invalid treaty type(s)', missingTypes: missingIds },
          { status: 400 }
        );
      }

      treatyTypesToAssign.push(...treatyTypeIds);
    } else {
      // If no treaty types are provided, assign default "General" treaty type
      let defaultTreatyType = await prisma.treatyType.findFirst({
        where: { 
          name: { 
            equals: 'General',
            mode: 'insensitive'
          },
          isActive: true,
          status: 'APPROVED'
        },
      });

      if (!defaultTreatyType) {
        // Create a default treaty type if none exists
        defaultTreatyType = await prisma.treatyType.create({
          data: {
            name: 'General',
            description: 'General treaty type for treaties without specific types',
            category: 'General',
            isActive: true,
            status: 'APPROVED',
          },
        });
      }

      treatyTypesToAssign.push(defaultTreatyType.id);
    }

    // Create treaty record
    // Store name in dedicated column and include in notes for backward compatibility
    const combinedNotes = name + (description ? `\n\n${description}` : '');

    const treaty = await prisma.treaty.create({
      data: {
        userId: finalUserId,
        name: name, // Add the required name field
        notes: combinedNotes,
        signedDate: signedDate ? new Date(signedDate) : undefined,
        expirationDate: expirationDate ? new Date(expirationDate) : undefined,
        renewalDate: renewalDate ? new Date(renewalDate) : undefined,
        status: 'ACTIVE',
        treatyTreatyTypes: {
          create: treatyTypesToAssign.map(treatyTypeId => ({
            treatyTypeId: treatyTypeId,
            assignedBy: finalUserId,
            status: 'ACTIVE',
          })),
        },
      },
      include: {
        treatyTreatyTypes: {
          include: {
            treatyType: true,
          },
        },
      },
    });
    
    return NextResponse.json({
      message: 'Treaty created successfully',
      id: treaty.id,
      treaty: {
        id: treaty.id,
        treatyTypeIds: treaty.treatyTreatyTypes.map(tt => tt.treatyTypeId),
        treatyTypes: treaty.treatyTreatyTypes.map(tt => ({
          id: tt.treatyTypeId,
          name: tt.treatyType.name,
        })),
        // derive display title from notes
        title: treaty.notes ? (treaty.notes.split('\n').find(line => line.trim().length > 0) || '').trim() : 'Unnamed Treaty',
        status: treaty.status,
        notes: treaty.notes,
        signedDate: treaty.signedDate,
        expirationDate: treaty.expirationDate,
        renewalDate: treaty.renewalDate,
        documentPath: treaty.documentPath,
        createdAt: treaty.createdAt,
        updatedAt: treaty.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error creating treaty:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const treatyId = searchParams.get('id');

    if (!treatyId) {
      return NextResponse.json({ error: 'Treaty ID is required' }, { status: 400 });
    }

    const body = await req.json();

    // Validate input
    const validation = treatyUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    // Resolve the correct user ID
    const userId = (session.user as any).id;
    let user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user && (session.user as any).email) {
      user = await prisma.user.findUnique({
        where: { email: (session.user as any).email },
      });
    }

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const finalUserId = user.id;

    // Check if the treaty belongs to the user
    const existingTreaty = await prisma.treaty.findUnique({
      where: {
        id: treatyId,
        userId: finalUserId,
      },
    });

    if (!existingTreaty) {
      return NextResponse.json({ error: 'Treaty not found' }, { status: 404 });
    }
    
    const updateData: any = {};
    const { name, description, treatyTypeIds, notes, status, signedDate, expirationDate, renewalDate } = validation.data;

    // Handle name and description updates
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) {
      // Update notes to include the new description
      const currentNotes = existingTreaty.notes || '';
      const nameLine = name || (currentNotes.split('\n')[0] || '');
      const combinedNotes = nameLine + (description ? `\n\n${description}` : '');
      updateData.notes = combinedNotes;
    }
    
    // Handle treaty type updates if provided
    if (treatyTypeIds !== undefined) {
      // Validate all provided treaty types exist
      if (treatyTypeIds.length > 0) {
        const treatyTypes = await prisma.treatyType.findMany({
          where: { 
            id: { in: treatyTypeIds },
            isActive: true,
            status: 'APPROVED'
          },
        });

        // Check if all requested types were found
        const foundIds = treatyTypes.map(type => type.id);
        const missingIds = treatyTypeIds.filter(id => !foundIds.includes(id));
        
        if (missingIds.length > 0) {
          return NextResponse.json(
            { error: 'Invalid treaty type(s)', missingTypes: missingIds },
            { status: 400 }
          );
        }
      }

      // Update treaty types relationship
      await prisma.treatyTreatyType.deleteMany({
        where: { treatyId: treatyId },
      });

      if (treatyTypeIds.length > 0) {
        await prisma.treatyTreatyType.createMany({
          data: treatyTypeIds.map(treatyTypeId => ({
            treatyId: treatyId,
            treatyTypeId: treatyTypeId,
            assignedBy: finalUserId,
            status: 'ACTIVE',
          })),
        });
      }
    }
    
    if (notes !== undefined) updateData.notes = notes;
    if (status) updateData.status = status;
    if (signedDate !== undefined) {
      updateData.signedDate = signedDate ? new Date(signedDate) : null;
    }
    if (expirationDate !== undefined) {
      updateData.expirationDate = expirationDate ? new Date(expirationDate) : null;
    }
    if (renewalDate !== undefined) {
      updateData.renewalDate = renewalDate ? new Date(renewalDate) : null;
    }
    
    // Update treaty record
    const treaty = await prisma.treaty.update({
      where: {
        id: treatyId,
      },
      data: updateData,
      include: {
        treatyTreatyTypes: {
          include: {
            treatyType: true,
          },
        },
      },
    });
    
    return NextResponse.json({
      message: 'Treaty updated successfully',
      id: treaty.id,
      treaty: {
        id: treaty.id,
        treatyTypeIds: treaty.treatyTreatyTypes.map(tt => tt.treatyTypeId),
        treatyTypes: treaty.treatyTreatyTypes.map(tt => ({
          id: tt.treatyTypeId,
          name: tt.treatyType.name,
        })),
        status: treaty.status,
        notes: treaty.notes,
        signedDate: treaty.signedDate,
        expirationDate: treaty.expirationDate,
        renewalDate: treaty.renewalDate,
        documentPath: treaty.documentPath,
        createdAt: treaty.createdAt,
        updatedAt: treaty.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error updating treaty:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const treatyId = searchParams.get('id');

    if (!treatyId) {
      return NextResponse.json({ error: 'Treaty ID is required' }, { status: 400 });
    }

    // Resolve the correct user ID
    const userId = (session.user as any).id;
    let user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user && (session.user as any).email) {
      user = await prisma.user.findUnique({
        where: { email: (session.user as any).email },
      });
    }

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const finalUserId = user.id;

    // Check if the treaty exists and belongs to the user
    const existingTreaty = await prisma.treaty.findUnique({
      where: {
        id: treatyId,
        userId: finalUserId,
      },
    });

    if (!existingTreaty) {
      return NextResponse.json({ error: 'Treaty not found' }, { status: 404 });
    }

    // Check if the treaty is assigned to any users (users)
    // Since treaties are tied to users via userId, we consider it "assigned" if it exists
    // But we should allow deletion since the user owns it
    // The user might mean we shouldn't delete treaties that are referenced elsewhere

    // Delete the treaty
    await prisma.treaty.delete({
      where: {
        id: treatyId,
      },
    });
    
    return NextResponse.json({
      message: 'Treaty deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting treaty:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}