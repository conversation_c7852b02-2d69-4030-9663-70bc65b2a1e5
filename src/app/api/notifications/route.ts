import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const unreadOnly = searchParams.get('unreadOnly') === 'true';
    
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      OR: [
        { recipients: { has: session.user.id } }, // Direct recipient
        { recipients: { has: 'admin' } }, // Admin broadcasts
      ],
    };

    if (unreadOnly) {
      where.NOT = {
        readBy: { has: session.user.id },
      };
    }

    // Fetch notifications with pagination
    const [notifications, totalCount, unreadCount] = await Promise.all([
      prisma.notification.findMany({
        where,
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.notification.count({ where }),
      prisma.notification.count({
        where: {
          ...where,
          NOT: {
            readBy: { has: session.user.id },
          },
        },
      }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      notifications,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        pageSize: limit,
      },
      unreadCount,
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { notificationIds, markAll } = body;

    if (markAll) {
      // Mark all user's notifications as read
      const userNotifications = await prisma.notification.findMany({
        where: {
          OR: [
            { recipients: { has: session.user.id } },
            { recipients: { has: 'admin' } },
          ],
          NOT: {
            readBy: { has: session.user.id },
          },
        },
        select: { id: true },
      });

      const notificationIdsToUpdate = userNotifications.map(n => n.id);

      if (notificationIdsToUpdate.length > 0) {
        await prisma.$transaction(
          notificationIdsToUpdate.map(id =>
            prisma.notification.update({
              where: { id },
              data: {
                readBy: {
                  push: session.user.id,
                },
              },
            })
          )
        );
      }

      return NextResponse.json({ message: 'All notifications marked as read' });
    }

    if (!notificationIds || !Array.isArray(notificationIds)) {
      return NextResponse.json({ error: 'Notification IDs array required' }, { status: 400 });
    }

    // Mark specific notifications as read
    await prisma.$transaction(
      notificationIds.map(id =>
        prisma.notification.update({
          where: { id },
          data: {
            readBy: {
              push: session.user.id,
            },
          },
        })
      )
    );

    return NextResponse.json({ message: 'Notifications marked as read' });
  } catch (error) {
    console.error('Error updating notifications:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}