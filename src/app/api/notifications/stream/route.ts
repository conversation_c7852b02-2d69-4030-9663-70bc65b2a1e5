import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { addClient, removeClient } from '@/lib/notifications';

interface ExtendedUser {
  id: string;
  email?: string | null;
  name?: string | null;
  image?: string | null;
  nwaEmail?: string;
  twoFactorEnabled?: boolean;
  twoFactorRequired?: boolean;
}

interface ExtendedSession {
  user: ExtendedUser;
  expires: string;
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as ExtendedSession | null;

    if (!session || !session.user) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Set up SSE response
    const headers = new Headers({
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    });

    const stream = new ReadableStream({
      start(controller) {
        const clientId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
        
        // Send initial connection message
        const encoder = new TextEncoder();
        controller.enqueue(encoder.encode(`data: ${JSON.stringify({ type: 'connected', clientId })}\n\n`));

        // Store client connection
        addClient(clientId, {
          controller,
          userId: session.user.id,
        });

        // Handle client disconnect
        req.signal.addEventListener('abort', () => {
          removeClient(clientId);
          controller.close();
        });
      },
    });

    return new Response(stream, { headers });
  } catch (error) {
    console.error('Error setting up notification stream:', error);
    return new Response('Internal server error', { status: 500 });
  }
}