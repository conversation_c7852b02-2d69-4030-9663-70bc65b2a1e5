import { NextRequest } from 'next/server'
import { redirect } from 'next/navigation'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state')
    const error = searchParams.get('error')
    
    console.log('=== OAuth Callback Received ===')
    console.log('Code:', code)
    console.log('State:', state)
    console.log('Error:', error)
    
    // Handle OAuth errors
    if (error) {
      const errorDescription = searchParams.get('error_description')
      console.error('OAuth Error:', error, errorDescription)
      return redirect(`/auth/error?error=${error}&error_description=${errorDescription || ''}`)
    }
    
    // Validate required parameters
    if (!code) {
      console.error('Missing authorization code')
      return redirect('/auth/error?error=invalid_request&error_description=Missing authorization code')
    }
    
    // Exchange authorization code for access token
    const tokenResponse = await fetch('http://localhost:3001/api/oauth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: 'http://localhost:3002/api/auth/callback/member-portal',
        client_id: 'nwapromote-client-local',
        client_secret: 'da69150be2ae984ea232f144387add211844500d9d94de131ba78f9e2936fbff',
      }),
    })
    
    console.log('Token response status:', tokenResponse.status)
    
    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text()
      console.error('Token exchange failed:', tokenResponse.status, errorText)
      return redirect(`/auth/error?error=token_exchange_failed&error_description=${encodeURIComponent(errorText)}`)
    }
    
    const tokenData = await tokenResponse.json()
    console.log('Token data received:', tokenData)
    
    // Store the token in a cookie or session
    // For this example, we'll redirect to a page that can handle the token
    const redirectUrl = new URL('/dashboard', request.nextUrl.origin)
    redirectUrl.searchParams.set('access_token', tokenData.access_token)
    redirectUrl.searchParams.set('expires_in', tokenData.expires_in)
    redirectUrl.searchParams.set('token_type', tokenData.token_type)
    
    if (state) {
      redirectUrl.searchParams.set('state', state)
    }
    
    console.log('Redirecting to:', redirectUrl.toString())
    return redirect(redirectUrl.toString())
  } catch (error: any) {
    console.error('Callback error:', error)
    console.error('Error stack:', error.stack)
    return redirect(`/auth/error?error=server_error&error_description=${encodeURIComponent(error.message || 'Unknown error')}`)
  }
}