import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

// Input sanitization function
function sanitizeInput(input: string): string {
  if (!input) return input;
  return input.trim().replace(/[<>]/g, '');
}

// Validation schema for city update
const cityUpdateSchema = z.object({
  name: z.string().min(1, 'City name is required').max(255, 'City name too long').optional(),
  countryId: z.number().int().positive('Valid country ID is required').optional(),
});

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const parsedId = parseInt(id);
    if (isNaN(parsedId)) {
      return NextResponse.json({ error: 'Invalid city ID' }, { status: 400 });
    }

    const body = await request.json();

    // Validate input
    const validation = cityUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { name, countryId } = validation.data;

    // Check if city exists
    const existingCity = await prisma.city.findUnique({
      where: { id: parsedId },
    });

    if (!existingCity) {
      return NextResponse.json({ error: 'City not found' }, { status: 404 });
    }

    // Check if country exists if countryId is being updated
    if (countryId) {
      const country = await prisma.country.findUnique({
        where: { id: countryId },
      });
      if (!country) {
        return NextResponse.json({ error: 'Country not found' }, { status: 400 });
      }
    }

    // Check for duplicates if name is being updated
    if (name && name !== existingCity.name) {
      const duplicateCity = await prisma.city.findFirst({
        where: {
          name: sanitizeInput(name),
          countryId: countryId || existingCity.countryId,
        },
      });
      if (duplicateCity) {
        return NextResponse.json({
          error: 'City with this name already exists in the selected country'
        }, { status: 400 });
      }
    }

    // Update city
    const updatedCity = await prisma.city.update({
      where: { id: parsedId },
      data: {
        ...(name && { name: sanitizeInput(name) }),
        ...(countryId && { countryId }),
      },
      include: {
        country: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'City updated successfully',
      city: updatedCity,
    });
  } catch (error) {
    console.error('Error updating city:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const parsedId = parseInt(id);
    if (isNaN(parsedId)) {
      return NextResponse.json({ error: 'Invalid city ID' }, { status: 400 });
    }

    // Check if city exists
    const existingCity = await prisma.city.findUnique({
      where: { id: parsedId },
    });

    if (!existingCity) {
      return NextResponse.json({ error: 'City not found' }, { status: 404 });
    }

    // Delete city
    await prisma.city.delete({
      where: { id: parsedId },
    });

    return NextResponse.json({
      message: 'City deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting city:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}