import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

// Input sanitization function
function sanitizeInput(input: string): string {
  if (!input) return input;
  return input.trim().replace(/[<>]/g, '');
}

// Validation schema for city creation
const cityCreateSchema = z.object({
  name: z.string().min(1, 'City name is required').max(255, 'City name too long'),
  countryId: z.number().int().positive('Valid country ID is required'),
});

// Validation schema for city update
const cityUpdateSchema = z.object({
  name: z.string().min(1, 'City name is required').max(255, 'City name too long').optional(),
  countryId: z.number().int().positive('Valid country ID is required').optional(),
});

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get search query and pagination parameters
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const countryIdParam = searchParams.get('countryId') || '';
    const all = searchParams.get('all') === 'true';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    // Validate pagination parameters
    const validPage = Math.max(1, page);
    const validLimit = Math.min(Math.max(1, limit), 100); // Max 100 items per page
    const skip = (validPage - 1) * validLimit;

    // If 'all' parameter is true, return all cities with country info and pagination
    if (all) {
      const [cities, totalCount] = await Promise.all([
        prisma.city.findMany({
          where: {
            name: {
              contains: query,
              mode: 'insensitive',
            },
          },
          include: {
            country: {
              select: {
                id: true,
                name: true,
                code: true,
              },
            },
          },
          orderBy: {
            name: 'asc',
          },
          skip: skip,
          take: validLimit,
        }),
        prisma.city.count({
          where: {
            name: {
              contains: query,
              mode: 'insensitive',
            },
          },
        }),
      ]);

      const totalPages = Math.ceil(totalCount / validLimit);

      return NextResponse.json({
        cities,
        pagination: {
          currentPage: validPage,
          totalPages,
          totalCount,
          limit: validLimit,
          hasNextPage: validPage < totalPages,
          hasPrevPage: validPage > 1,
        },
      });
    }

    // Original logic for filtered cities
    if (!countryIdParam) {
      return NextResponse.json({ error: 'Country ID is required' }, { status: 400 });
    }

    // Convert countryId to number
    const countryId = parseInt(countryIdParam, 10);
    if (isNaN(countryId)) {
      return NextResponse.json({ error: 'Invalid country ID format' }, { status: 400 });
    }

    // Fetch cities that match the query and belong to the specified country with pagination
    const [cities, totalCount] = await Promise.all([
      prisma.city.findMany({
        where: {
          AND: [
            {
              countryId: countryId,
            },
            {
              name: {
                contains: query,
                mode: 'insensitive',
              },
            },
          ],
        },
        select: {
          id: true,
          name: true,
        },
        orderBy: {
          name: 'asc',
        },
        skip: skip,
        take: validLimit,
      }),
      prisma.city.count({
        where: {
          AND: [
            {
              countryId: countryId,
            },
            {
              name: {
                contains: query,
                mode: 'insensitive',
              },
            },
          ],
        },
      }),
    ]);

    const totalPages = Math.ceil(totalCount / validLimit);

    return NextResponse.json({
      cities,
      pagination: {
        currentPage: validPage,
        totalPages,
        totalCount,
        limit: validLimit,
        hasNextPage: validPage < totalPages,
        hasPrevPage: validPage > 1,
      },
    });
  } catch (error) {
    console.error('Error fetching cities:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();

    // Validate input
    const validation = cityCreateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { name, countryId } = validation.data;

    // Sanitize inputs
    const sanitizedName = sanitizeInput(name);

    // Check if country exists
    const country = await prisma.country.findUnique({
      where: { id: countryId },
    });

    if (!country) {
      return NextResponse.json({ error: 'Country not found' }, { status: 400 });
    }

    // Check if city with same name already exists in the same country
    const existingCity = await prisma.city.findFirst({
      where: {
        name: sanitizedName,
        countryId: countryId,
      },
    });

    if (existingCity) {
      return NextResponse.json({ error: 'City with this name already exists in the selected country' }, { status: 400 });
    }

    // Create city
    const city = await prisma.city.create({
      data: {
        name: sanitizedName,
        countryId: countryId,
      },
      include: {
        country: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'City created successfully',
      city,
    });
  } catch (error) {
    console.error('Error creating city:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}