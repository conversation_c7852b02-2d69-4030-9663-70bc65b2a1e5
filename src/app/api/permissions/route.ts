import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/middleware/require-auth'
import { checkRateLimit } from '@/lib/middleware/rate-limiting'
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging'
import { AuthenticationError, AuthorizationError } from '@/lib/errors'

export async function GET(request: NextRequest) {
  try {
    // Apply strict rate limiting for permissions endpoint
    const rateLimitResult = await checkRateLimit(request, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30, // 30 requests per minute for permissions
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        request,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Require authentication and admin permissions for accessing permissions
    const authContext = await requireAuth(request, {
      requirePermissions: ['permissions:read'],
      auditLogging: true,
    });

    // Log successful access
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'read',
      'permissions',
      true,
      request,
      {
        metadata: {
          accessLevel: 'admin',
          permissionsRequested: true,
        },
      }
    );

    // Return comprehensive permissions and roles information for admin users
    return NextResponse.json({
      name: 'NWA User Portal',
      description: 'A comprehensive user management system for the NWA Alliance',
      permissions: [
        {
          name: 'permissions:read',
          description: 'Read system permissions and roles',
          category: 'system'
        },
        {
          name: 'permissions:write',
          description: 'Modify system permissions and roles',
          category: 'system'
        },
        {
          name: 'remote_servers:read',
          description: 'Read remote server configurations',
          category: 'remote_servers'
        },
        {
          name: 'remote_servers:write',
          description: 'Create and modify remote server configurations',
          category: 'remote_servers'
        },
        {
          name: 'remote_servers:delete',
          description: 'Delete remote server configurations',
          category: 'remote_servers'
        },
        {
          name: 'remote_servers:sync',
          description: 'Synchronize with remote servers',
          category: 'remote_servers'
        },
        {
          name: 'users:read',
          description: 'Read user information',
          category: 'users'
        },
        {
          name: 'users:write',
          description: 'Create and modify user accounts',
          category: 'users'
        },
        {
          name: 'users:delete',
          description: 'Delete user accounts',
          category: 'users'
        },
        {
          name: 'roles:read',
          description: 'Read role definitions',
          category: 'roles'
        },
        {
          name: 'roles:write',
          description: 'Create and modify roles',
          category: 'roles'
        },
        {
          name: 'roles:delete',
          description: 'Delete roles',
          category: 'roles'
        },
        {
          name: 'audit:read',
          description: 'Read audit logs',
          category: 'audit'
        },
        {
          name: 'audit:write',
          description: 'Create audit log entries',
          category: 'audit'
        },
        {
          name: 'files:read',
          description: 'Read files and documents',
          category: 'files'
        },
        {
          name: 'files:write',
          description: 'Upload and modify files',
          category: 'files'
        },
        {
          name: 'files:delete',
          description: 'Delete files',
          category: 'files'
        }
      ],
      roles: [
        {
          name: 'super_admin',
          description: 'Super administrator with full system access',
          permissions: [
            'permissions:read',
            'permissions:write',
            'remote_servers:read',
            'remote_servers:write',
            'remote_servers:delete',
            'remote_servers:sync',
            'users:read',
            'users:write',
            'users:delete',
            'roles:read',
            'roles:write',
            'roles:delete',
            'audit:read',
            'audit:write',
            'files:read',
            'files:write',
            'files:delete'
          ],
          hierarchy: 100
        },
        {
          name: 'admin',
          description: 'Administrator with elevated access',
          permissions: [
            'remote_servers:read',
            'remote_servers:write',
            'remote_servers:sync',
            'users:read',
            'users:write',
            'roles:read',
            'roles:write',
            'audit:read',
            'files:read',
            'files:write'
          ],
          hierarchy: 80
        },
        {
          name: 'moderator',
          description: 'Moderator with user and content management access',
          permissions: [
            'users:read',
            'users:write',
            'files:read',
            'files:write'
          ],
          hierarchy: 60
        },
        {
          name: 'user',
          description: 'Regular user with basic access',
          permissions: [
            'remote_servers:read',
            'files:read'
          ],
          hierarchy: 20
        },
        {
          name: 'guest',
          description: 'Guest user with minimal access',
          permissions: [],
          hierarchy: 10
        }
      ],
      permissionCategories: [
        {
          name: 'system',
          description: 'System-level permissions for administration'
        },
        {
          name: 'remote_servers',
          description: 'Remote server management permissions'
        },
        {
          name: 'users',
          description: 'User account management permissions'
        },
        {
          name: 'roles',
          description: 'Role and permission management'
        },
        {
          name: 'audit',
          description: 'Audit logging and monitoring permissions'
        },
        {
          name: 'files',
          description: 'File and document management permissions'
        }
      ]
    });
  } catch (error) {
    console.error('Permissions endpoint error:', error);

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    // Handle unknown error types safely
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

    return NextResponse.json(
      { error: 'Internal Server Error', message: errorMessage },
      { status: 500 }
    );
  }
}