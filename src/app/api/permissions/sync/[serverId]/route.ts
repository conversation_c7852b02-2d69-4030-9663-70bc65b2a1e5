import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { permissionInheritance } from '@/lib/services/permission-inheritance'

// POST /api/permissions/sync/[serverId] - Sync permissions from remote server
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ serverId: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { serverId } = await params

    const result = await permissionInheritance.syncServerPermissions(serverId)

    if (result.errors.length > 0) {
      return Response.json({
        success: false,
        error: 'Sync completed with errors',
        data: result
      }, { status: 207 }) // Multi-status
    }

    return Response.json({
      success: true,
      message: `Successfully synced ${result.synced} permissions`,
      data: result
    })
  } catch (error) {
    console.error('Error syncing permissions:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}