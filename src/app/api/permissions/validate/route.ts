import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { permissionInheritance } from '@/lib/services/permission-inheritance'

// POST /api/permissions/validate - Validate permissions against server capabilities
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { serverId, permissions } = body

    if (!serverId || !Array.isArray(permissions)) {
      return Response.json(
        { error: 'Missing required parameters: serverId and permissions array' },
        { status: 400 }
      )
    }

    const validation = await permissionInheritance.validatePermissions(serverId, permissions)

    return Response.json({
      success: true,
      data: validation
    })
  } catch (error) {
    console.error('Error validating permissions:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}