import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { permissionInheritance } from '@/lib/services/permission-inheritance'

// POST /api/permissions/calculate - Calculate user permissions for a server
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { userId, serverId } = body

    if (!userId || !serverId) {
      return Response.json(
        { error: 'Missing required parameters: userId and serverId' },
        { status: 400 }
      )
    }

    const permissions = await permissionInheritance.calculateUserServerPermissions(userId, serverId)

    return Response.json({
      success: true,
      data: permissions
    })
  } catch (error) {
    console.error('Error calculating permissions:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/permissions/calculate?userId=X&serverId=Y - Alternative GET endpoint
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const serverId = searchParams.get('serverId')

    if (!userId) {
      return Response.json(
        { error: 'Missing required parameter: userId' },
        { status: 400 }
      )
    }

    if (serverId) {
      // Get permissions for specific server
      const permissions = await permissionInheritance.calculateUserServerPermissions(userId, serverId)
      return Response.json({
        success: true,
        data: permissions
      })
    } else {
      // Get permissions for all servers user has access to
      const allPermissions = await permissionInheritance.calculateAllUserServerPermissions(userId)
      return Response.json({
        success: true,
        data: allPermissions
      })
    }
  } catch (error) {
    console.error('Error calculating permissions:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}