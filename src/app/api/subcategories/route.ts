import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

// Input sanitization function
function sanitizeInput(input: string): string {
  if (!input) return input;
  return input.trim().replace(/[<>]/g, '');
}

// Validation schema for subcategory creation
const subcategoryCreateSchema = z.object({
  name: z.string().min(1, 'Subcategory name is required').max(255, 'Subcategory name too long'),
  description: z.string().optional(),
  categoryId: z.number().int().positive('Valid category ID is required'),
});

// Validation schema for subcategory update
const subcategoryUpdateSchema = z.object({
  name: z.string().min(1, 'Subcategory name is required').max(255, 'Subcategory name too long').optional(),
  description: z.string().optional(),
  categoryId: z.number().int().positive('Valid category ID is required').optional(),
});

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get search query and pagination parameters
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const categoryIdParam = searchParams.get('categoryId') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    // Validate pagination parameters
    const validPage = Math.max(1, page);
    const validLimit = Math.min(Math.max(1, limit), 100); // Max 100 items per page
    const skip = (validPage - 1) * validLimit;

    let whereCondition: any = {
      name: {
        contains: query,
        mode: 'insensitive',
      },
    };

    // If categoryId is provided, filter by category
    if (categoryIdParam) {
      const categoryId = parseInt(categoryIdParam, 10);
      if (!isNaN(categoryId)) {
        whereCondition.categoryId = categoryId;
      }
    }

    // Fetch subcategories that match the query with pagination
    const [subcategories, totalCount] = await Promise.all([
      prisma.subcategory.findMany({
        where: whereCondition,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
        },
        orderBy: {
          name: 'asc',
        },
        skip: skip,
        take: validLimit,
      }),
      prisma.subcategory.count({
        where: whereCondition,
      }),
    ]);

    const totalPages = Math.ceil(totalCount / validLimit);

    return NextResponse.json({
      subcategories,
      pagination: {
        currentPage: validPage,
        totalPages,
        totalCount,
        limit: validLimit,
        hasNextPage: validPage < totalPages,
        hasPrevPage: validPage > 1,
      },
    });
  } catch (error) {
    console.error('Error fetching subcategories:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();

    // Validate input
    const validation = subcategoryCreateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { name, description, categoryId } = validation.data;

    // Sanitize inputs
    const sanitizedName = sanitizeInput(name);
    const sanitizedDescription = description ? sanitizeInput(description) : undefined;

    // Check if category exists
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      return NextResponse.json({ error: 'Category not found' }, { status: 400 });
    }

    // Check if subcategory with same name already exists in the same category
    const existingSubcategory = await prisma.subcategory.findFirst({
      where: {
        name: sanitizedName,
        categoryId: categoryId,
      },
    });

    if (existingSubcategory) {
      return NextResponse.json({ error: 'Subcategory with this name already exists in the selected category' }, { status: 400 });
    }

    // Create subcategory
    const subcategory = await prisma.subcategory.create({
      data: {
        name: sanitizedName,
        description: sanitizedDescription,
        categoryId: categoryId,
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Subcategory created successfully',
      subcategory,
    });
  } catch (error) {
    console.error('Error creating subcategory:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}