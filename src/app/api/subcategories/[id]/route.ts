import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

// Input sanitization function
function sanitizeInput(input: string): string {
  if (!input) return input;
  return input.trim().replace(/[<>]/g, '');
}

// Validation schema for subcategory update
const subcategoryUpdateSchema = z.object({
  name: z.string().min(1, 'Subcategory name is required').max(255, 'Subcategory name too long').optional(),
  description: z.string().optional(),
  categoryId: z.number().int().positive('Valid category ID is required').optional(),
});

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const parsedId = parseInt(id);
    if (isNaN(parsedId)) {
      return NextResponse.json({ error: 'Invalid subcategory ID' }, { status: 400 });
    }

    const body = await request.json();

    // Validate input
    const validation = subcategoryUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { name, description, categoryId } = validation.data;

    // Check if subcategory exists
    const existingSubcategory = await prisma.subcategory.findUnique({
      where: { id: parsedId },
    });

    if (!existingSubcategory) {
      return NextResponse.json({ error: 'Subcategory not found' }, { status: 404 });
    }

    // Check if category exists if categoryId is being updated
    if (categoryId) {
      const category = await prisma.category.findUnique({
        where: { id: categoryId },
      });
      if (!category) {
        return NextResponse.json({ error: 'Category not found' }, { status: 400 });
      }
    }

    // Check for duplicates if name is being updated
    if (name && name !== existingSubcategory.name) {
      const duplicateSubcategory = await prisma.subcategory.findFirst({
        where: {
          name: sanitizeInput(name),
          categoryId: categoryId || existingSubcategory.categoryId,
        },
      });
      if (duplicateSubcategory) {
        return NextResponse.json({
          error: 'Subcategory with this name already exists in the selected category'
        }, { status: 400 });
      }
    }

    // Update subcategory
    const updatedSubcategory = await prisma.subcategory.update({
      where: { id: parsedId },
      data: {
        ...(name && { name: sanitizeInput(name) }),
        ...(description !== undefined && { description: description ? sanitizeInput(description) : null }),
        ...(categoryId && { categoryId }),
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Subcategory updated successfully',
      subcategory: updatedSubcategory,
    });
  } catch (error) {
    console.error('Error updating subcategory:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const parsedId = parseInt(id);
    if (isNaN(parsedId)) {
      return NextResponse.json({ error: 'Invalid subcategory ID' }, { status: 400 });
    }

    // Check if subcategory exists
    const existingSubcategory = await prisma.subcategory.findUnique({
      where: { id: parsedId },
    });

    if (!existingSubcategory) {
      return NextResponse.json({ error: 'Subcategory not found' }, { status: 404 });
    }

    // Delete subcategory
    await prisma.subcategory.delete({
      where: { id: parsedId },
    });

    return NextResponse.json({
      message: 'Subcategory deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting subcategory:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}