
import { NextRequest, NextResponse } from 'next/server';
import { checkRateLimit } from '@/lib/middleware/rate-limiting';
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging';

// JWKS configuration - in production, this should come from environment variables or a secure key management system
const JWKS_KEYS = [
  {
    kty: 'RSA',
    kid: 'lj0ZjkiW5bmU8Zw-pUoQ4MJqFDqYhkRCT3ToH_AFiaQ',
    n: 'ofue9e6feS2JiTMoPQtrOV22QmUIqQ9rGM1Wu4DN1uWqXRUTnC7ey0Lqgkps9bTR0UQFfbmHfVe6Yqt2NOOQ5Kie6sZ8XnoIEDTB1pC8QRhFZmxj73busjJuuiDL4sJzFzlHhNNIY0YOpnAZdv2-ezxrRvjYvadodCbip3JBsPo-RafZCOdASjxx0UIIREaNz6uORXKIX_TZaDBOofdkWlnB5Nugn52MjSVt7sIdeMhaLqSAH3LdK-UNcQzZFP96hHvLUGk3YH-UW7jRLvKIQjIbV1hFTfCq71sLByRjPOr35jVI2oDJt0P3gYsykj0JRb1QO2cQCbiS4-F_F9GIcw',
    e: 'AQAB',
  },
];

// Helper function to create CORS-enabled responses
function createCorsResponse(data: any, status: number = 200, request: NextRequest): NextResponse {
  const response = NextResponse.json(data, { status });

  // Add CORS headers for cross-origin requests
  const origin = request.headers.get('origin');

  if (origin) {
    // For development, allow localhost origins
    if (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002')) {
      response.headers.set('Access-Control-Allow-Origin', origin);
      response.headers.set('Access-Control-Allow-Credentials', 'true');
      response.headers.set('Vary', 'Origin');
    }
  }

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour

  return response;
}

export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting for JWKS requests (higher limit since this is a public endpoint)
    const rateLimitResult = await checkRateLimit(request, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100, // 100 JWKS requests per minute (higher than normal endpoints)
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        request,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return createCorsResponse(
        {
          error: 'too_many_requests',
          error_description: 'Rate limit exceeded. Please try again later.'
        },
        429,
        request
      );
    }

    // Handle OPTIONS preflight requests
    if (request.method === 'OPTIONS') {
      const response = new NextResponse(null, { status: 204 });
      const origin = request.headers.get('origin');

      if (origin && (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002'))) {
        response.headers.set('Access-Control-Allow-Origin', origin);
        response.headers.set('Access-Control-Allow-Credentials', 'true');
        response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
        response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        response.headers.set('Access-Control-Max-Age', '86400');
      }

      return response;
    }

    // Log successful JWKS access
    await auditLoggingMiddleware.logApiAccess(
      undefined,
      'jwks_access',
      'oauth/jwks',
      true,
      request,
      {
        statusCode: 200,
        metadata: {
          keyCount: JWKS_KEYS.length
        }
      }
    );

    // Return JWKS with security headers
    const jwks = {
      keys: JWKS_KEYS,
    };

    return createCorsResponse(jwks, 200, request);

  } catch (error: any) {
    // Log the error for audit purposes
    await auditLoggingMiddleware.logApiAccess(
      undefined,
      'jwks_error',
      'oauth/jwks',
      false,
      request,
      {
        statusCode: 500,
        errorMessage: error.message || 'Unknown error',
        metadata: {
          stack: error.stack || 'No stack trace'
        }
      }
    );

    console.error('OAuth JWKS error:', error);

    return createCorsResponse(
      {
        error: 'server_error',
        error_description: 'Internal server error'
      },
      500,
      request
    );
  }
}
