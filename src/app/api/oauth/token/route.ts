import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generateAccessToken, generateRefreshToken } from '@/lib/services/oauth'
import { headers } from 'next/headers'
import { requireAuth } from '@/lib/middleware/require-auth'
import { checkRateLimit } from '@/lib/middleware/rate-limiting'
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging'
import { AuthenticationError, AuthorizationError } from '@/lib/errors'

export async function POST(request: NextRequest) {
  try {
    // Apply strict rate limiting for OAuth token operations
    const rateLimitResult = await checkRateLimit(request, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10, // 10 token requests per minute
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        request,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // For OAuth token endpoint, we don't require user authentication
    // but we do need to validate client credentials
    const formData = await request.formData()
    const grantType = formData.get('grant_type') as string
    let clientId = formData.get('client_id') as string
    let clientSecret = formData.get('client_secret') as string
    const code = formData.get('code') as string
    const redirectUri = formData.get('redirect_uri') as string
    const refreshToken = formData.get('refresh_token') as string

    // Check for Basic Auth if client credentials not in form data
    if (!clientId || !clientSecret) {
      const authHeader = request.headers.get('authorization')
      if (authHeader && authHeader.startsWith('Basic ')) {
        const base64Credentials = authHeader.slice(6)
        const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii')
        const [basicClientId, basicClientSecret] = credentials.split(':')
        if (basicClientId && basicClientSecret) {
          clientId = basicClientId
          clientSecret = basicClientSecret
        }
      }
    }
    
    // Log OAuth token request for audit
    await auditLoggingMiddleware.logApiAccess(
      undefined,
      'token_request',
      'oauth',
      true,
      request,
      {
        metadata: {
          grantType: grantType,
          clientId: clientId ? clientId.substring(0, 8) + '...' : 'none',
          hasClientSecret: !!clientSecret,
          hasCode: !!code,
          hasRefreshToken: !!refreshToken,
          redirectUri: redirectUri ? redirectUri.substring(0, 20) + '...' : 'none'
        }
      }
    );

    // Validate grant_type
    if (!grantType) {
      await auditLoggingMiddleware.logApiAccess(
        undefined,
        'token_request',
        'oauth',
        false,
        request,
        {
          statusCode: 400,
          errorMessage: 'Missing grant_type parameter',
          metadata: { grantType: 'missing' }
        }
      );

      return NextResponse.json(
        {
          error: 'invalid_request',
          error_description: 'Missing grant_type parameter'
        },
        { status: 400 }
      )
    }
    
    // Handle authorization_code grant type
    if (grantType === 'authorization_code') {
      if (!code || !redirectUri || !clientId || !clientSecret) {
        await auditLoggingMiddleware.logApiAccess(
          undefined,
          'token_request',
          'oauth',
          false,
          request,
          {
            statusCode: 400,
            errorMessage: 'Missing required parameters for authorization_code grant',
            metadata: {
              grantType: 'authorization_code',
              hasCode: !!code,
              hasRedirectUri: !!redirectUri,
              hasClientId: !!clientId,
              hasClientSecret: !!clientSecret
            }
          }
        );

        return NextResponse.json(
          {
            error: 'invalid_request',
            error_description: 'Missing required parameters for authorization_code grant'
          },
          { status: 400 }
        )
      }
      
      // Find the authorization code with remote server info
      const authCodes: any = await prisma.$queryRawUnsafe(`
        SELECT ac.*, rs.client_id, rs.client_secret
        FROM authorization_codes ac
        JOIN remote_servers rs ON ac.remote_server_id = rs.id
        WHERE ac.code = $1
      `, code)
      const authCode = Array.isArray(authCodes) && authCodes.length > 0 ? authCodes[0] : null;

      if (!authCode) {
        await auditLoggingMiddleware.logApiAccess(
          undefined,
          'token_request',
          'oauth',
          false,
          request,
          {
            statusCode: 400,
            errorMessage: 'Invalid authorization code',
            metadata: {
              grantType: 'authorization_code',
              codePrefix: code.substring(0, 8) + '...',
              reason: 'code_not_found'
            }
          }
        );

        return NextResponse.json(
          {
            error: 'invalid_grant',
            error_description: 'Invalid authorization code'
          },
          { status: 400 }
        )
      }
      
      // Validate client credentials against the authorization code's remote server
      if (!authCode.client_id || authCode.client_id !== clientId) {
        await auditLoggingMiddleware.logApiAccess(
          undefined,
          'token_request',
          'oauth',
          false,
          request,
          {
            statusCode: 401,
            errorMessage: 'Invalid client credentials',
            metadata: {
              grantType: 'authorization_code',
              reason: 'client_id_mismatch',
              expectedClientId: authCode.client_id ? authCode.client_id.substring(0, 8) + '...' : 'none',
              receivedClientId: clientId.substring(0, 8) + '...'
            }
          }
        );

        return NextResponse.json(
          {
            error: 'invalid_client',
            error_description: 'Invalid client credentials'
          },
          { status: 401 }
        )
      }

      // Validate client secret
      if (!authCode.client_secret || authCode.client_secret !== clientSecret) {
        await auditLoggingMiddleware.logApiAccess(
          undefined,
          'token_request',
          'oauth',
          false,
          request,
          {
            statusCode: 401,
            errorMessage: 'Invalid client credentials',
            metadata: {
              grantType: 'authorization_code',
              reason: 'client_secret_mismatch',
              clientId: clientId.substring(0, 8) + '...'
            }
          }
        );

        return NextResponse.json(
          {
            error: 'invalid_client',
            error_description: 'Invalid client credentials'
          },
          { status: 401 }
        )
      }
      
      // Check if the authorization code has expired
      if (new Date(authCode.expires_at) < new Date()) {
        await auditLoggingMiddleware.logApiAccess(
          undefined,
          'token_request',
          'oauth',
          false,
          request,
          {
            statusCode: 400,
            errorMessage: 'Authorization code has expired',
            metadata: {
              grantType: 'authorization_code',
              codeId: authCode.id,
              expiresAt: authCode.expires_at,
              reason: 'code_expired'
            }
          }
        );

        return NextResponse.json(
          {
            error: 'invalid_grant',
            error_description: 'Authorization code has expired'
          },
          { status: 400 }
        )
      }

      // Check if the authorization code has already been used
      if (authCode.used_at) {
        await auditLoggingMiddleware.logApiAccess(
          undefined,
          'token_request',
          'oauth',
          false,
          request,
          {
            statusCode: 400,
            errorMessage: 'Authorization code has already been used',
            metadata: {
              grantType: 'authorization_code',
              codeId: authCode.id,
              usedAt: authCode.used_at,
              reason: 'code_already_used'
            }
          }
        );

        return NextResponse.json(
          {
            error: 'invalid_grant',
            error_description: 'Authorization code has already been used'
          },
          { status: 400 }
        )
      }

      // Validate redirect_uri
      if (authCode.redirect_uri !== redirectUri) {
        await auditLoggingMiddleware.logApiAccess(
          undefined,
          'token_request',
          'oauth',
          false,
          request,
          {
            statusCode: 400,
            errorMessage: 'Invalid redirect URI',
            metadata: {
              grantType: 'authorization_code',
              expectedRedirectUri: authCode.redirect_uri,
              receivedRedirectUri: redirectUri,
              reason: 'redirect_uri_mismatch'
            }
          }
        );

        return NextResponse.json(
          {
            error: 'invalid_grant',
            error_description: 'Invalid redirect URI'
          },
          { status: 400 }
        )
      }
      
      // Mark the authorization code as used
      await prisma.$queryRawUnsafe(`UPDATE authorization_codes SET used_at = $1 WHERE id = $2`, new Date(), authCode.id)

      // Generate access and refresh tokens
      const accessToken = await generateAccessToken(
        authCode.remote_server_id,
        authCode.user_id,
        authCode.scope || ''
      )

      const refreshTokenValue = await generateRefreshToken(
        authCode.remote_server_id,
        authCode.user_id,
        authCode.scope || ''
      )

      // Create OAuth tokens in database
      const oauthTokens: any = await prisma.$queryRawUnsafe(`
        INSERT INTO oauth_tokens (
          id, remote_server_id, user_id, access_token, refresh_token,
          token_type, scope, expires_at, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
        ) RETURNING *
      `,
        // Generate a unique ID (you might want to use a proper ID generation method)
        `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        authCode.remote_server_id,
        authCode.user_id,
        accessToken.token,
        refreshTokenValue.token,
        'Bearer',
        authCode.scope || '',
        accessToken.expiresAt,
        new Date(),
        new Date()
      )

      const oauthToken = Array.isArray(oauthTokens) && oauthTokens.length > 0 ? oauthTokens[0] : oauthTokens;

      // Log successful token generation
      await auditLoggingMiddleware.logApiAccess(
        authCode.user_id,
        'token_request',
        'oauth',
        true,
        request,
        {
          metadata: {
            grantType: 'authorization_code',
            tokenType: 'access_token',
            scope: authCode.scope || '',
            expiresIn: Math.floor((new Date(oauthToken.expires_at).getTime() - Date.now()) / 1000),
            hasRefreshToken: true,
            clientId: clientId.substring(0, 8) + '...',
            userId: authCode.user_id
          }
        }
      );

      // Create secure response with CORS and security headers
      const response = NextResponse.json({
        access_token: oauthToken.access_token,
        token_type: oauthToken.token_type,
        expires_in: Math.floor((new Date(oauthToken.expires_at).getTime() - Date.now()) / 1000),
        refresh_token: oauthToken.refresh_token,
        scope: oauthToken.scope
      });

      // Add CORS headers for cross-origin requests
      const origin = request.headers.get('origin');
      if (origin) {
        // For development, allow localhost origins
        if (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002')) {
          response.headers.set('Access-Control-Allow-Origin', origin);
          response.headers.set('Access-Control-Allow-Credentials', 'true');
          response.headers.set('Vary', 'Origin');
        }
      }

      // Add security headers
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-XSS-Protection', '1; mode=block');
      response.headers.set('Cache-Control', 'no-store'); // OAuth tokens should not be cached

      return response;
    }
    // Handle refresh_token grant type
    else if (grantType === 'refresh_token') {
      if (!refreshToken || !clientId || !clientSecret) {
        await auditLoggingMiddleware.logApiAccess(
          undefined,
          'token_request',
          'oauth',
          false,
          request,
          {
            statusCode: 400,
            errorMessage: 'Missing required parameters for refresh_token grant',
            metadata: {
              grantType: 'refresh_token',
              hasRefreshToken: !!refreshToken,
              hasClientId: !!clientId,
              hasClientSecret: !!clientSecret
            }
          }
        );

        return NextResponse.json(
          {
            error: 'invalid_request',
            error_description: 'Missing required parameters for refresh_token grant'
          },
          { status: 400 }
        )
      }

      // Find the remote server by client_id
      const remoteServers: any = await prisma.$queryRawUnsafe(`SELECT * FROM remote_servers WHERE client_id = $1`, clientId)
      const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null;

      if (!remoteServer) {
        await auditLoggingMiddleware.logApiAccess(
          undefined,
          'token_request',
          'oauth',
          false,
          request,
          {
            statusCode: 401,
            errorMessage: 'Invalid client credentials',
            metadata: {
              grantType: 'refresh_token',
              reason: 'client_not_found',
              clientId: clientId.substring(0, 8) + '...'
            }
          }
        );

        return NextResponse.json(
          {
            error: 'invalid_client',
            error_description: 'Invalid client credentials'
          },
          { status: 401 }
        )
      }

      // Validate client secret
      if (remoteServer.client_secret !== clientSecret) {
        await auditLoggingMiddleware.logApiAccess(
          undefined,
          'token_request',
          'oauth',
          false,
          request,
          {
            statusCode: 401,
            errorMessage: 'Invalid client credentials',
            metadata: {
              grantType: 'refresh_token',
              reason: 'client_secret_mismatch',
              clientId: clientId.substring(0, 8) + '...'
            }
          }
        );

        return NextResponse.json(
          {
            error: 'invalid_client',
            error_description: 'Invalid client credentials'
          },
          { status: 401 }
        )
      }
      
      // Find the refresh token
      const oauthTokens: any = await prisma.$queryRawUnsafe(`
        SELECT * FROM oauth_tokens
        WHERE refresh_token = $1 AND remote_server_id = $2
      `, refreshToken, remoteServer.id)

      const oauthToken = Array.isArray(oauthTokens) && oauthTokens.length > 0 ? oauthTokens[0] : null;

      if (!oauthToken) {
        await auditLoggingMiddleware.logApiAccess(
          undefined,
          'token_request',
          'oauth',
          false,
          request,
          {
            statusCode: 400,
            errorMessage: 'Invalid refresh token',
            metadata: {
              grantType: 'refresh_token',
              reason: 'refresh_token_not_found',
              clientId: clientId.substring(0, 8) + '...'
            }
          }
        );

        return NextResponse.json(
          {
            error: 'invalid_grant',
            error_description: 'Invalid refresh token'
          },
          { status: 400 }
        )
      }

      // Check if the refresh token has expired
      if (new Date(oauthToken.expires_at) < new Date()) {
        await auditLoggingMiddleware.logApiAccess(
          undefined,
          'token_request',
          'oauth',
          false,
          request,
          {
            statusCode: 400,
            errorMessage: 'Refresh token has expired',
            metadata: {
              grantType: 'refresh_token',
              tokenId: oauthToken.id,
              expiresAt: oauthToken.expires_at,
              reason: 'refresh_token_expired'
            }
          }
        );

        return NextResponse.json(
          {
            error: 'invalid_grant',
            error_description: 'Refresh token has expired'
          },
          { status: 400 }
        )
      }

      // Generate new access and refresh tokens
      const newAccessToken = await generateAccessToken(
        remoteServer.id,
        oauthToken.user_id,
        oauthToken.scope || ''
      )

      const newRefreshToken = await generateRefreshToken(
        remoteServer.id,
        oauthToken.user_id,
        oauthToken.scope || ''
      )

      // Update OAuth tokens in database
      const updatedTokens: any = await prisma.$queryRawUnsafe(`
        UPDATE oauth_tokens
        SET access_token = $1, refresh_token = $2, expires_at = $3, updated_at = $4
        WHERE id = $5
        RETURNING *
      `, newAccessToken.token, newRefreshToken.token, newAccessToken.expiresAt, new Date(), oauthToken.id)

      const updatedToken = Array.isArray(updatedTokens) && updatedTokens.length > 0 ? updatedTokens[0] : updatedTokens;

      // Log successful refresh token usage
      await auditLoggingMiddleware.logApiAccess(
        oauthToken.user_id,
        'token_request',
        'oauth',
        true,
        request,
        {
          metadata: {
            grantType: 'refresh_token',
            tokenType: 'access_token',
            scope: oauthToken.scope || '',
            expiresIn: Math.floor((new Date(updatedToken.expires_at).getTime() - Date.now()) / 1000),
            hasRefreshToken: true,
            clientId: clientId.substring(0, 8) + '...',
            userId: oauthToken.user_id,
            previousTokenId: oauthToken.id
          }
        }
      );

      // Create secure response with CORS and security headers
      const response = NextResponse.json({
        access_token: updatedToken.access_token,
        token_type: updatedToken.token_type,
        expires_in: Math.floor((new Date(updatedToken.expires_at).getTime() - Date.now()) / 1000),
        refresh_token: updatedToken.refresh_token,
        scope: updatedToken.scope
      });

      // Add CORS headers for cross-origin requests
      const origin = request.headers.get('origin');
      if (origin) {
        // For development, allow localhost origins
        if (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002')) {
          response.headers.set('Access-Control-Allow-Origin', origin);
          response.headers.set('Access-Control-Allow-Credentials', 'true');
          response.headers.set('Vary', 'Origin');
        }
      }

      // Add security headers
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-XSS-Protection', '1; mode=block');
      response.headers.set('Cache-Control', 'no-store'); // OAuth tokens should not be cached

      return response;
    }
    // Unsupported grant type
    else {
      await auditLoggingMiddleware.logApiAccess(
        undefined,
        'token_request',
        'oauth',
        false,
        request,
        {
          statusCode: 400,
          errorMessage: 'Unsupported grant type',
          metadata: {
            grantType: grantType,
            supportedGrantTypes: ['authorization_code', 'refresh_token']
          }
        }
      );

      return NextResponse.json(
        {
          error: 'unsupported_grant_type',
          error_description: 'Unsupported grant type'
        },
        { status: 400 }
      )
    }
  } catch (error: any) {
    console.error('OAuth token error:', error)
    console.error('Error stack:', error.stack)
    console.error('Error message:', error.message)

    // Log OAuth errors for security monitoring
    await auditLoggingMiddleware.logApiAccess(
      undefined,
      'token_request',
      'oauth',
      false,
      request,
      {
        statusCode: 500,
        errorMessage: 'Internal server error',
        metadata: {
          errorType: 'server_error',
          errorMessage: error.message || 'Unknown error',
          errorStack: error.stack?.substring(0, 500) + '...' // Truncate stack trace
        }
      }
    );

    // Create secure error response with CORS and security headers
    const response = NextResponse.json(
      {
        error: 'server_error',
        error_description: 'Internal server error'
      },
      { status: 500 }
    );

    // Add CORS headers for cross-origin requests
    const origin = request.headers.get('origin');
    if (origin) {
      // For development, allow localhost origins
      if (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002')) {
        response.headers.set('Access-Control-Allow-Origin', origin);
        response.headers.set('Access-Control-Allow-Credentials', 'true');
        response.headers.set('Vary', 'Origin');
      }
    }

    // Add security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Cache-Control', 'no-store');

    return response;
  }
}