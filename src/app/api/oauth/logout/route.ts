import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { unstable_getServerSession as getServerSessionNextAuth } from 'next-auth/next';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { client_id, user_id, access_token, logout_all } = body;
    
    // Validate required parameters
    if (!client_id) {
      return NextResponse.json(
        { error: 'invalid_request', error_description: 'Client ID is required' },
        { status: 400 }
      );
    }

    // Verify client exists and is active
    const client = await prisma.remoteServer.findFirst({
      where: { 
        clientId: client_id,
        isActive: true 
      }
    });

    if (!client) {
      return NextResponse.json(
        { error: 'invalid_client', error_description: 'Client not found or inactive' },
        { status: 400 }
      );
    }

    let tokensRevoked = 0;
    let codesRevoked = 0;
    const now = new Date();

    // Handle different logout scenarios
    if (logout_all && user_id) {
      // Logout user from all clients (admin/global logout)
      const userTokens = await prisma.oAuthToken.findMany({
        where: { userId: user_id }
      });

      if (userTokens.length > 0) {
        await prisma.oAuthToken.deleteMany({
          where: { userId: user_id }
        });
        tokensRevoked = userTokens.length;
        logger.info('Global logout - all user tokens revoked', { 
          userId: user_id, 
          count: userTokens.length 
        });
      }
    } else if (access_token) {
      // Targeted logout by specific access token
      const token = await prisma.oAuthToken.findFirst({
        where: { 
          accessToken: access_token,
          remoteServerId: client.id
        }
      });

      if (token) {
        await prisma.oAuthToken.delete({
          where: { id: token.id }
        });
        tokensRevoked++;
        logger.info('Specific access token revoked', { 
          tokenId: token.id, 
          clientId: client_id 
        });
      }
    } else if (user_id) {
      // Logout user from this specific client only
      const userTokens = await prisma.oAuthToken.findMany({
        where: {
          userId: user_id,
          remoteServerId: client.id
        }
      });

      if (userTokens.length > 0) {
        await prisma.oAuthToken.deleteMany({
          where: {
            userId: user_id,
            remoteServerId: client.id
          }
        });
        tokensRevoked = userTokens.length;
        logger.info('User logged out from client', { 
          userId: user_id, 
          clientId: client_id, 
          count: userTokens.length 
        });
      }
    } else {
      // Client-initiated logout without specific user/token
      // Clean up expired tokens and codes for this client
      const expiredTokens = await prisma.oAuthToken.deleteMany({
        where: { 
          expiresAt: { lt: now },
          remoteServerId: client.id
        }
      });
      tokensRevoked = expiredTokens.count;
    }

    // Always clean up expired authorization codes for this client
    const expiredCodes = await prisma.authorizationCode.deleteMany({
      where: { 
        expiresAt: { lt: now },
        remoteServerId: client.id
      }
    });
    codesRevoked = expiredCodes.count;

    logger.info('Logout completed successfully', {
      clientId: client_id,
      userId: user_id,
      tokensRevoked,
      codesRevoked,
      logoutAll: !!logout_all,
      expiredCodesCleaned: expiredCodes.count
    });

    // Create response with session cookie clearing for complete logout
    const response = NextResponse.json({
      success: true,
      tokens_revoked: tokensRevoked,
      codes_revoked: codesRevoked,
      message: 'OAuth logout completed successfully - session cleared',
      session_active: false // Session cookies will be cleared
    });

    // Clear NextAuth session cookies to ensure complete logout
    // Try both possible session token names (development vs production)
    const sessionTokenNames = [
      'next-auth.session-token',
      '__Secure-next-auth.session-token',
      'next-auth.session-token.0',
      'next-auth.session-token.1'
    ];
    
    sessionTokenNames.forEach(tokenName => {
      response.cookies.set({
        name: tokenName,
        value: '',
        expires: new Date(0),
        path: '/',
        httpOnly: true,
        secure: false, // Set to true in production with HTTPS
        sameSite: 'lax'
      });
    });

    // Also clear the CSRF token
    response.cookies.set({
      name: 'next-auth.csrf-token',
      value: '',
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: false, // Set to true in production with HTTPS
      sameSite: 'lax'
    });
    
    // Also try clearing callback URL cookie
    response.cookies.set({
      name: 'next-auth.callback-url',
      value: '',
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: false,
      sameSite: 'lax'
    });

    logger.info('User Portal session cookies cleared for complete logout', {
      clientId: client_id,
      userId: user_id
    });

    return response;

  } catch (error) {
    logger.error('Logout endpoint error', {}, error as Error);
    return NextResponse.json(
      { error: 'server_error', error_description: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint for browser-initiated logout (handles redirects from OAuth clients)
export async function GET(req: NextRequest) {
  try {
    // Extract parameters from query string
    const { searchParams } = new URL(req.url);
    const client_id = searchParams.get('client_id');
    const access_token = searchParams.get('access_token');
    const redirect_uri = searchParams.get('redirect_uri');
    const logout_all = searchParams.get('logout_all') === 'true';

    if (!client_id) {
      return NextResponse.json(
        { error: 'invalid_request', error_description: 'Client ID is required' },
        { status: 400 }
      );
    }

    // Verify client exists
    const client = await prisma.remoteServer.findFirst({
      where: { 
        clientId: client_id,
        isActive: true 
      }
    });

    if (!client) {
      return NextResponse.json(
        { error: 'invalid_client', error_description: 'Client not found' },
        { status: 400 }
      );
    }

    let tokensRevoked = 0;
    
    // If access token is provided, revoke specific token
    if (access_token) {
      const token = await prisma.oAuthToken.findFirst({
        where: { 
          accessToken: access_token,
          remoteServerId: client.id
        }
      });

      if (token) {
        await prisma.oAuthToken.delete({
          where: { id: token.id }
        });
        tokensRevoked++;
        logger.info('Access token revoked via browser logout', { 
          tokenId: token.id, 
          clientId: client_id 
        });
      }
    }

    // Determine redirect URL - use provided redirect_uri or default to login
    const redirectUrl = redirect_uri ? new URL(redirect_uri) : new URL('/login?message=logout_success', req.url);
    
    // Create response with session cookie clearing for complete logout
    const response = NextResponse.redirect(redirectUrl);
    
    // Clear all possible NextAuth session cookies
    const sessionTokenNames = [
      'next-auth.session-token',
      '__Secure-next-auth.session-token',
      'next-auth.session-token.0',
      'next-auth.session-token.1'
    ];
    
    sessionTokenNames.forEach(tokenName => {
      response.cookies.set({
        name: tokenName,
        value: '',
        expires: new Date(0),
        path: '/',
        httpOnly: true,
        secure: false, // Set to true in production with HTTPS
        sameSite: 'lax'
      });
    });

    // Also clear the CSRF token
    response.cookies.set({
      name: 'next-auth.csrf-token',
      value: '',
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: false, // Set to true in production with HTTPS
      sameSite: 'lax'
    });
    
    // Also clear callback URL cookie
    response.cookies.set({
      name: 'next-auth.callback-url',
      value: '',
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: false,
      sameSite: 'lax'
    });

    logger.info('Browser logout completed - session cookies cleared', {
      clientId: client_id,
      tokensRevoked,
      redirectUrl: redirectUrl.href
    });

    return response;

  } catch (error) {
    logger.error('GET logout endpoint error', {}, error as Error);
    return NextResponse.redirect(new URL('/login?error=logout_failed', req.url));
  }
}