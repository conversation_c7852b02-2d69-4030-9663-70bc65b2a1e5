import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getTokenFromRequest } from '@/lib/services/oauth'
import { checkRateLimit } from '@/lib/middleware/rate-limiting'
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging'
import { AuthenticationError, AuthorizationError } from '@/lib/errors'
import { z } from 'zod'

// Input validation schema
const UserInfoParamsSchema = z.object({
  access_token: z.string().optional(),
  schema: z.string().optional().default('openid')
})

// Helper function to create CORS-enabled responses
function createCorsResponse(data: any, status: number = 200, request: NextRequest): NextResponse {
  const response = NextResponse.json(data, { status });

  // Add CORS headers for cross-origin requests
  const origin = request.headers.get('origin');

  if (origin) {
    // For development, allow localhost origins
    if (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002')) {
      response.headers.set('Access-Control-Allow-Origin', origin);
      response.headers.set('Access-Control-Allow-Credentials', 'true');
      response.headers.set('Access-Control-Expose-Headers', 'WWW-Authenticate');
      response.headers.set('Vary', 'Origin');
    }
  }

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
}

export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting for OAuth userinfo requests
    const rateLimitResult = await checkRateLimit(request, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30, // 30 userinfo requests per minute
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        request,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return createCorsResponse(
        {
          error: 'too_many_requests',
          error_description: 'Rate limit exceeded. Please try again later.'
        },
        429,
        request
      );
    }

    // Handle OPTIONS preflight requests
    if (request.method === 'OPTIONS') {
      const response = new NextResponse(null, { status: 204 });
      const origin = request.headers.get('origin');

      if (origin && (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002'))) {
        response.headers.set('Access-Control-Allow-Origin', origin);
        response.headers.set('Access-Control-Allow-Credentials', 'true');
        response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
        response.headers.set('Access-Control-Allow-Headers', 'Authorization, Content-Type');
        response.headers.set('Access-Control-Max-Age', '86400');
      }

      return response;
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const accessToken = searchParams.get('access_token');

    try {
      const validatedParams = UserInfoParamsSchema.parse({
        access_token: accessToken,
        schema: searchParams.get('schema') || 'openid'
      });
    } catch (validationError: any) {
      await auditLoggingMiddleware.logApiAccess(
        undefined,
        'invalid_userinfo_request',
        'oauth/userinfo',
        false,
        request,
        {
          statusCode: 400,
          errorMessage: 'Invalid request parameters',
          metadata: {
            validationError: validationError.message
          }
        }
      );

      return createCorsResponse(
        {
          error: 'invalid_request',
          error_description: 'Invalid request parameters'
        },
        400,
        request
      );
    }

    // Extract token from Authorization header or query parameter
    const token = getTokenFromRequest(request) || accessToken;

    if (!token) {
      await auditLoggingMiddleware.logApiAccess(
        undefined,
        'missing_token',
        'oauth/userinfo',
        false,
        request,
        {
          statusCode: 401,
          errorMessage: 'Missing or invalid token'
        }
      );

      return createCorsResponse(
        {
          error: 'invalid_token',
          error_description: 'Missing or invalid token'
        },
        401,
        request
      );
    }

    // Find the OAuth token with user and remote server information using parameterized query
    const oauthTokens: any = await prisma.$queryRawUnsafe(
      `SELECT ot.*, u.name as user_name, u.email as user_email, rs.id as remote_server_id, rs.name as remote_server_name
       FROM oauth_tokens ot
       JOIN users u ON ot.user_id = u.id
       JOIN remote_servers rs ON ot.remote_server_id = rs.id
       WHERE ot.access_token = $1`,
      token
    );

    const oauthToken = Array.isArray(oauthTokens) && oauthTokens.length > 0 ? oauthTokens[0] : null;

    if (!oauthToken) {
      await auditLoggingMiddleware.logApiAccess(
        undefined,
        'invalid_token_lookup',
        'oauth/userinfo',
        false,
        request,
        {
          statusCode: 401,
          errorMessage: 'Token not found in database'
        }
      );

      return createCorsResponse(
        {
          error: 'invalid_token',
          error_description: 'Invalid token'
        },
        401,
        request
      );
    }

    // Check if the token has expired
    if (new Date(oauthToken.expires_at) < new Date()) {
      await auditLoggingMiddleware.logApiAccess(
        oauthToken.user_id,
        'expired_token',
        'oauth/userinfo',
        false,
        request,
        {
          statusCode: 401,
          errorMessage: 'Token has expired',
          metadata: {
            tokenId: oauthToken.id
          }
        }
      );

      return createCorsResponse(
        {
          error: 'invalid_token',
          error_description: 'Token has expired'
        },
        401,
        request
      );
    }

    // Get user permissions for the specific remote server using parameterized query
    const userRemoteServerPermissions: any = await prisma.$queryRawUnsafe(
      `SELECT ursp.permission_name, rsp.permission_description
       FROM user_remote_server_permissions ursp
       JOIN remote_server_permissions rsp ON ursp.remote_server_id = rsp.remote_server_id
           AND ursp.permission_name = rsp.permission_name
       WHERE ursp.user_id = $1
         AND ursp.remote_server_id = $2
         AND rsp.is_active = true`,
      oauthToken.user_id,
      oauthToken.remote_server_id
    );

    const permissions = userRemoteServerPermissions.map((ursp: any) => ursp.permission_name);

    // Fetch user roles using parameterized query
    const userRoles: any = await prisma.$queryRawUnsafe(
      `SELECT r.name as role_name
       FROM user_roles ur
       JOIN roles r ON ur.role_id = r.id
       WHERE ur.user_id = $1`,
      oauthToken.user_id
    );

    const roles = userRoles.map((ur: any) => ur.role_name);

    // Log successful userinfo access
    await auditLoggingMiddleware.logApiAccess(
      oauthToken.user_id,
      'userinfo_access',
      'oauth/userinfo',
      true,
      request,
      {
        statusCode: 200,
        metadata: {
          remoteServerId: oauthToken.remote_server_id,
          scopes: oauthToken.scope ? oauthToken.scope.split(' ') : []
        }
      }
    );

    // Return user information with security considerations
    const userInfo: any = {
      sub: oauthToken.user_id,
      roles,
      permissions
    };

    // Only include name and email if they exist and are not empty
    if (oauthToken.user_name && oauthToken.user_name.trim()) {
      userInfo.name = oauthToken.user_name.trim();
    }

    if (oauthToken.user_email && oauthToken.user_email.trim()) {
      userInfo.email = oauthToken.user_email.trim();
    }

    return createCorsResponse(userInfo, 200, request);

  } catch (error: any) {
    // Log the error for audit purposes
    await auditLoggingMiddleware.logApiAccess(
      undefined,
      'userinfo_error',
      'oauth/userinfo',
      false,
      request,
      {
        statusCode: 500,
        errorMessage: error.message || 'Unknown error',
        metadata: {
          stack: error.stack || 'No stack trace'
        }
      }
    );

    console.error('OAuth userinfo error:', error);

    return createCorsResponse(
      {
        error: 'server_error',
        error_description: 'Internal server error'
      },
      500,
      request
    );
  }
}