import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generateAuthorizationCode } from '@/lib/services/oauth'
import { redirect } from 'next/navigation'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { requireAuth } from '@/lib/middleware/require-auth'
import { checkRateLimit } from '@/lib/middleware/rate-limiting'
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging'
import { AuthenticationError, AuthorizationError } from '@/lib/errors'

// Helper function to create CORS-enabled redirect responses
function corsRedirect(url: string, request: NextRequest, allowedOrigins: string[] = []): NextResponse {
  // Create a response with Location header instead of using NextResponse.redirect()
  const response = new NextResponse(null, {
    status: 302,
    headers: new Headers({
      'Location': url,
    })
  });
  
  // Add CORS headers to allow cross-origin redirects
  const origin = request.headers.get('origin');
  
  if (origin) {
    // For development, allow localhost origins even if allowedOrigins is empty
    if (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002')) {
      response.headers.set('Access-Control-Allow-Origin', origin);
      response.headers.set('Access-Control-Allow-Credentials', 'true');
      response.headers.set('Access-Control-Expose-Headers', 'Location');
      response.headers.set('Vary', 'Origin');
    }
    // Also check if origin is in allowed origins (if provided)
    else if (allowedOrigins.length > 0) {
      const normalizedOrigin = origin.toLowerCase().replace(/\/$/, '');
      const isAllowed = allowedOrigins.some(allowedOrigin => {
        const normalizedAllowed = allowedOrigin.toLowerCase().replace(/\/$/, '');
        return normalizedOrigin === normalizedAllowed;
      });
      
      if (isAllowed) {
        response.headers.set('Access-Control-Allow-Origin', origin);
        response.headers.set('Access-Control-Allow-Credentials', 'true');
        response.headers.set('Access-Control-Expose-Headers', 'Location');
        response.headers.set('Vary', 'Origin');
      }
    }
  }
  
  return response;
}

export async function GET(request: NextRequest) {
  try {
    // Apply strict rate limiting for OAuth authorization requests
    const rateLimitResult = await checkRateLimit(request, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 20, // 20 authorization requests per minute
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        request,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      // For rate limit errors, redirect with error
      const { searchParams } = new URL(request.url);
      const redirectUri = searchParams.get('redirect_uri') || '/';
      const state = searchParams.get('state');

      const errorParams = new URLSearchParams({
        error: 'too_many_requests',
        error_description: 'Rate limit exceeded. Please try again later.'
      });

      if (state) errorParams.append('state', state);

      return corsRedirect(`${redirectUri}?${errorParams.toString()}`, request, []);
    }

    // Handle OPTIONS preflight requests
    if (request.method === 'OPTIONS') {
      const response = new NextResponse(null, { status: 204 });
      const origin = request.headers.get('origin');

      if (origin) {
        // For OPTIONS requests, we need to check the client_id parameter
        const { searchParams } = new URL(request.url);
        const clientId = searchParams.get('client_id');

        if (clientId) {
          try {
            // Find the remote server to get allowed origins
            const remoteServers: any = await prisma.$queryRawUnsafe(`SELECT allowed_origins FROM remote_servers WHERE client_id = $1`, clientId);
            const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null;

            if (remoteServer && remoteServer.allowed_origins) {
              let allowedOrigins: string[] = [];
              if (Array.isArray(remoteServer.allowed_origins)) {
                allowedOrigins = remoteServer.allowed_origins;
              } else if (typeof remoteServer.allowed_origins === 'string') {
                try {
                  allowedOrigins = JSON.parse(remoteServer.allowed_origins);
                } catch (e) {
                  allowedOrigins = [remoteServer.allowed_origins];
                }
              }

              // Check if origin is in allowed origins
              const normalizedOrigin = origin.toLowerCase().replace(/\/$/, '');
              const isAllowed = allowedOrigins.some(allowedOrigin => {
                const normalizedAllowed = allowedOrigin.toLowerCase().replace(/\/$/, '');
                return normalizedOrigin === normalizedAllowed;
              });

              if (isAllowed) {
                response.headers.set('Access-Control-Allow-Origin', origin);
                response.headers.set('Access-Control-Allow-Credentials', 'true');
                response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
                response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
                response.headers.set('Access-Control-Max-Age', '86400');
                return response;
              }
            }
          } catch (error) {
            console.error('Error checking allowed origins for OPTIONS:', error);
          }
        }

        // If we get here, either no client_id or origin not allowed
        // For development, allow localhost origins
        if (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002')) {
          response.headers.set('Access-Control-Allow-Origin', origin);
          response.headers.set('Access-Control-Allow-Credentials', 'true');
          response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
          response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
          response.headers.set('Access-Control-Max-Age', '86400');
        }
      }

      return response;
    }
  
  // Handle actual GET requests
  try {
    const { searchParams } = new URL(request.url)
    const clientId = searchParams.get('client_id')
    const redirectUri = searchParams.get('redirect_uri')
    const responseType = searchParams.get('response_type')
    const scope = searchParams.get('scope')
    const state = searchParams.get('state')
    
    // Validate required parameters
    if (!clientId || !redirectUri || !responseType) {
      const errorParams = new URLSearchParams({
        error: 'invalid_request',
        error_description: 'Missing required parameters'
      })
      if (state) errorParams.append('state', state)
      return corsRedirect(`${redirectUri}?${errorParams.toString()}`, request, [])
    }
    
    // Validate response_type
    if (responseType !== 'code') {
      const errorParams = new URLSearchParams({
        error: 'unsupported_response_type',
        error_description: 'Only authorization code flow is supported'
      })
      if (state) errorParams.append('state', state)
      return corsRedirect(`${redirectUri}?${errorParams.toString()}`, request, [])
    }
    
    // Find the remote server by client_id
    const remoteServers: any = await prisma.$queryRawUnsafe(`SELECT * FROM remote_servers WHERE client_id = $1`, clientId)
    const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null;
    
    if (!remoteServer) {
      const errorParams = new URLSearchParams({
        error: 'unauthorized_client',
        error_description: 'Invalid client ID'
      })
      if (state) errorParams.append('state', state)
      return corsRedirect(`${redirectUri}?${errorParams.toString()}`, request, [])
    }
    
    // Validate redirect_uri
    let redirectUris: string[] = [];
    if (Array.isArray(remoteServer.redirect_uris)) {
      redirectUris = remoteServer.redirect_uris;
    } else if (typeof remoteServer.redirect_uris === 'string') {
      try {
        redirectUris = JSON.parse(remoteServer.redirect_uris);
      } catch (e) {
        redirectUris = [remoteServer.redirect_uris];
      }
    }
    
    if (!redirectUris.includes(redirectUri)) {
      const errorParams = new URLSearchParams({
        error: 'invalid_request',
        error_description: 'Invalid redirect URI'
      });
      if (state) errorParams.append('state', state);
      
      // Get allowed origins for CORS
      let allowedOrigins: string[] = [];
      if (remoteServer.allowed_origins) {
        if (Array.isArray(remoteServer.allowed_origins)) {
          allowedOrigins = remoteServer.allowed_origins;
        } else if (typeof remoteServer.allowed_origins === 'string') {
          try {
            allowedOrigins = JSON.parse(remoteServer.allowed_origins);
          } catch (e) {
            allowedOrigins = [remoteServer.allowed_origins];
          }
        }
      }
      
      return corsRedirect(`${redirectUri}?${errorParams.toString()}`, request, allowedOrigins);
    }
    
    // Validate scope (if provided)
    if (scope) {
      const requestedScopes = scope.split(' ')
      const validScopes = remoteServer.default_scopes || ['read:profile']
      
      console.log('Requested scopes:', requestedScopes);
      console.log('Valid scopes:', validScopes);
      
      // Check if all requested scopes are valid
      const invalidScopes = requestedScopes.filter(s => !validScopes.includes(s))
      if (invalidScopes.length > 0) {
        console.log('Invalid scopes found:', invalidScopes);
        const errorParams = new URLSearchParams({
          error: 'invalid_scope',
          error_description: `Invalid scopes: ${invalidScopes.join(', ')}`
        })
        if (state) errorParams.append('state', state)
        return corsRedirect(`${redirectUri}?${errorParams.toString()}`, request, [])
      }
    }
    
    // First, check if user is authenticated
    const session = await getServerSession(authOptions)
    
    // Log session for debugging
    console.log('Session object:', JSON.stringify(session, null, 2));
    
    // If user is not authenticated, redirect to login page
    if (!session || !session.user) {
      console.log('User not authenticated, redirecting to login');
      // Get the current URL to use as callback after login
      const currentUrl = request.url
      const loginUrl = new URL('/login', process.env.NEXTAUTH_URL)
      loginUrl.searchParams.set('callbackUrl', currentUrl)
      return corsRedirect(loginUrl.toString(), request)
    }
    
    // Now that user is authenticated, use the actual authenticated user ID
    // NWAPromote will receive this user info and create/update the user in its own database
    const userId = (session.user as any).id;
    
    // Log user ID for debugging
    console.log('Using authenticated user ID:', userId);
    console.log('Session user:', session.user);
    
    // Verify user exists in database
    if (!userId) {
      console.error('No user ID found in session');
      const response = NextResponse.json({ error: 'Invalid session' }, { status: 401 });
      const origin = request.headers.get('origin');
      if (origin && (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002'))) {
        response.headers.set('Access-Control-Allow-Origin', origin);
        response.headers.set('Access-Control-Allow-Credentials', 'true');
      }
      return response;
    }
    
    const dbUser = await prisma.user.findUnique({
      where: { id: userId }
    });
    
    if (!dbUser) {
      console.error('User not found in database:', userId);
      const response = NextResponse.json({ error: 'User not found' }, { status: 401 });
      const origin = request.headers.get('origin');
      if (origin && (origin.includes('localhost:3002') || origin.includes('127.0.0.1:3002'))) {
        response.headers.set('Access-Control-Allow-Origin', origin);
        response.headers.set('Access-Control-Allow-Credentials', 'true');
      }
      return response;
    }
    
    console.log('Generating authorization code for:', {
      remoteServerId: remoteServer.id,
      userId: userId,
      redirectUri,
      scope: scope || (remoteServer.default_scopes || ['read:profile']).join(' ')
    });
    
    console.log('About to generate authorization code with:', {
      remoteServerId: remoteServer.id,
      userId: userId,
      redirectUri,
      scope: scope || (remoteServer.default_scopes || ['read:profile']).join(' ')
    });
    
    // Generate authorization code
    const authorizationCode = await generateAuthorizationCode(
      remoteServer.id,
      userId, // Use actual authenticated user ID from session
      redirectUri,
      scope || (remoteServer.default_scopes || ['read:profile']).join(' ')
    )
    
    console.log('Authorization code generated successfully:', authorizationCode);
    
    console.log('Generated authorization code:', authorizationCode);
    
    // Redirect back to client with authorization code
    const successParams = new URLSearchParams({
      code: authorizationCode.code
    })
    
    if (state) successParams.append('state', state)
    
    const redirectUrl = `${redirectUri}?${successParams.toString()}`;
    
    // For OAuth flows, we need to allow cross-origin redirects
    // Get allowed origins for CORS
    let allowedOrigins: string[] = [];
    if (remoteServer.allowed_origins) {
      if (Array.isArray(remoteServer.allowed_origins)) {
        allowedOrigins = remoteServer.allowed_origins;
      } else if (typeof remoteServer.allowed_origins === 'string') {
        try {
          allowedOrigins = JSON.parse(remoteServer.allowed_origins);
        } catch (e) {
          allowedOrigins = [remoteServer.allowed_origins];
        }
      }
    }

    const response = corsRedirect(redirectUrl, request, allowedOrigins);

    return response;
  } catch (error: any) {
    // Check if this is a NEXT_REDIRECT error (expected behavior)
    if (error?.digest?.includes('NEXT_REDIRECT')) {
      // Re-throw NEXT_REDIRECT errors as they are expected
      throw error;
    }

    console.error('OAuth authorize error:', error)
    console.error('Error type:', typeof error)
    console.error('Error message:', error.message || 'No message')
    console.error('Error stack:', error.stack || 'No stack trace')

    // Parse redirect_uri from request
    const { searchParams } = new URL(request.url)
    const redirectUri = searchParams.get('redirect_uri') || '/'
    const state = searchParams.get('state')

    const errorParams = new URLSearchParams({
      error: 'server_error',
      error_description: 'Internal server error'
    })

    if (state) errorParams.append('state', state)

    return corsRedirect(`${redirectUri}?${errorParams.toString()}`, request, [])
  }
} catch (error: any) {
  // Outer catch block for any errors in the main function
  console.error('Critical OAuth authorize error:', error)
  console.error('Error type:', typeof error)
  console.error('Error message:', error.message || 'No message')
  console.error('Error stack:', error.stack || 'No stack trace')

  // Return a generic error response
  return NextResponse.json(
    {
      error: 'server_error',
      error_description: 'Internal server error'
    },
    { status: 500 }
  );
}
}