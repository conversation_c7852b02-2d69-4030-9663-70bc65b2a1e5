import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { generateRandomString } from '@/lib/utils'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, url, description } = body
    
    if (!name || !url) {
      return Response.json(
        { error: 'Missing required parameters: name and url' },
        { status: 400 }
      )
    }

    // Check if remote server with this URL already exists
    const existingServer = await prisma.remoteServer.findFirst({
      where: { url }
    })
    
    if (existingServer) {
      return Response.json(
        { error: 'Remote server with this URL already exists' },
        { status: 409 }
      )
    }

    // Create the remote server directly in database
    const remoteServer = await prisma.remoteServer.create({
      data: {
        name,
        url,
        description: description || null,
        apiKey: generateRandomString(64),
        clientId: generateRandomString(32),
        clientSecret: generateRandomString(64),
        isActive: true,
        redirectUris: [`${url}/callback`],
        defaultScopes: ['read:profile', 'read:documents', 'write:documents']
      }
    })

    // Create some test permissions for this server
    const testPermissions = [
      { name: 'read:documents', description: 'Read documents' },
      { name: 'write:documents', description: 'Create and edit documents' },
      { name: 'delete:documents', description: 'Delete documents' },
      { name: 'read:users', description: 'Read user information' },
      { name: 'write:users', description: 'Create and edit users' },
      { name: 'admin:settings', description: 'Access admin settings' }
    ]

    await prisma.remote_server_permissions.createMany({
      data: testPermissions.map(perm => ({
        remote_server_id: remoteServer.id,
        permission_name: perm.name,
        permission_description: perm.description,
        is_active: true
      }))
    })

    return Response.json({
      success: true,
      data: {
        id: remoteServer.id,
        name: remoteServer.name,
        url: remoteServer.url,
        description: remoteServer.description,
        permissionsCreated: testPermissions.length
      }
    })
  } catch (error) {
    console.error('Error creating test remote server:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}