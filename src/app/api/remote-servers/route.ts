import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generateRandomString } from '@/lib/utils'

export async function GET(request: NextRequest) {
  try {
    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = parseInt(url.searchParams.get('limit') || '25', 10);
    const active = url.searchParams.get('active');

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return Response.json(
        { error: 'Bad Request', message: 'Invalid pagination parameters' },
        { status: 400 }
      );
    }

    // Build where clause
    const where: any = {};
    if (active === 'true') {
      where.is_active = true;
    } else if (active === 'false') {
      where.is_active = false;
    }

    // Fetch remote servers with pagination and their permissions
    const [remoteServers, total] = await Promise.all([
      prisma.remoteServer.findMany({
        where,
        select: {
          id: true,
          name: true,
          url: true,
          description: true,
          isActive: true,
          clientId: true,
          createdAt: true,
          updatedAt: true,
          // Include permissions
          remote_server_permissions: {
            where: { is_active: true },
            select: {
              permission_name: true,
              permission_description: true,
              last_synced_at: true
            }
          },
          // Include roles with access to this server
          role_remote_server_access: {
            where: { is_active: true },
            select: {
              role_id: true,
              auto_grant_permissions: true,
              created_at: true,
              roles: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                  isSystem: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.remoteServer.count({ where }),
    ]);

    // Transform the response to include permissions and roles in a more accessible format
    const serversWithPermissions = remoteServers.map((server: any) => ({
      ...server,
      permissions: server.remote_server_permissions || [],
      roles: server.role_remote_server_access?.map((roleAccess: any) => ({
        id: roleAccess.roles.id,
        name: roleAccess.roles.name,
        description: roleAccess.roles.description,
        isSystem: roleAccess.roles.isSystem,
        autoGrantPermissions: roleAccess.auto_grant_permissions,
        accessGrantedAt: roleAccess.created_at
      })) || []
    }));

    // Return paginated response
    return Response.json({
      remoteServers: serversWithPermissions,
      pagination: {
        page,
        limit,
        total,
      },
    });
  } catch (error) {
    console.error('Error fetching remote servers:', error);
    
    return Response.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { url } = body
    
    // Validate required parameters
    if (!url) {
      return Response.json(
        { error: 'Missing required parameter: url' },
        { status: 400 }
      )
    }
    
    // Validate URL format
    try {
      new URL(url)
    } catch (error) {
      return Response.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      )
    }
    
    // Check if remote server is already registered
    const existingServers: any = await prisma.$queryRawUnsafe(
      `SELECT * FROM remote_servers WHERE url = $1`,
      url
    )
    const existingServer = existingServers.length > 0 ? existingServers[0] : null
    
    if (existingServer) {
      return Response.json(
        { error: 'Remote server already registered' },
        { status: 409 }
      )
    }
    
    // Fetch information from the remote server
    const permissionUrl = `${url}/api/permissions`
    console.log('Registering remote server, fetching permissions from:', permissionUrl)
    
    // For registration, we might not have an API key yet, but let's try with a generic one
    // In a real implementation, this would be handled differently
    const infoResponse = await fetch(permissionUrl, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    console.log('Registration response status:', infoResponse.status)
    
    if (!infoResponse.ok) {
      return Response.json(
        { error: 'Error connecting to remote server' },
        { status: 502 }
      )
    }
    
    const info = await infoResponse.json()
    
    // Generate client credentials
    const clientId = generateRandomString(32)
    const clientSecret = generateRandomString(64)
    
    // Create remote server in database using raw query
    const remoteServers: any = await prisma.$queryRawUnsafe(`
      INSERT INTO remote_servers (
        id, name, url, "apiKey", description, client_id, client_secret, 
        redirect_uris, default_scopes, is_active, created_at, updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
      ) RETURNING *
    `, [
      `server_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // id
      info.name || 'Remote Server', // name
      url, // url
      generateRandomString(64), // apiKey
      info.description, // description
      clientId, // client_id
      clientSecret, // client_secret
      [], // redirect_uris
      info.permissions?.map((p: any) => p.name) || ['read:profile'], // default_scopes
      true, // is_active
      new Date(), // created_at
      new Date() // updated_at
    ])
    
    const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : remoteServers;
    
    return Response.json({
      id: remoteServer.id,
      name: remoteServer.name,
      url: remoteServer.url,
      description: remoteServer.description,
      isActive: remoteServer.isActive,
      createdAt: remoteServer.createdAt,
      updatedAt: remoteServer.updatedAt
    })
  } catch (error) {
    console.error('Remote server registration error:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}