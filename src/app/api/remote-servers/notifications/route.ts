import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { broadcastNotification } from '@/lib/notifications'

interface PermissionFetchFailureData {
  serverId: string
  serverName: string
  error: string
  timestamp: string
  context?: string
}

interface HealthCheckFailureData {
  serverId: string
  serverName: string
  status: 'offline' | 'error'
  error: string
  responseTime?: number
  timestamp: string
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, data } = body

    if (type === 'permission_fetch_failure') {
      const failureData: PermissionFetchFailureData = data

      console.log('Creating permission fetch failure notification:', failureData)

      // Create notification in database
      const notification = await prisma.notification.create({
        data: {
          type: 'PERMISSION_FETCH_FAILED',
          message: `Failed to fetch permissions from server "${failureData.serverName}"`,
          data: JSON.stringify({
            serverId: failureData.serverId,
            serverName: failureData.serverName,
            error: failureData.error,
            timestamp: failureData.timestamp,
            context: failureData.context || 'unknown'
          }),
          recipients: ['admin'], // Target all admins
          readBy: [] // No one has read it yet
        }
      })

      // Broadcast notification to connected admin clients
      await broadcastNotification({
        type: 'PERMISSION_FETCH_FAILED',
        message: notification.message,
        data: {
          id: notification.id,
          serverId: failureData.serverId,
          serverName: failureData.serverName,
          error: failureData.error,
          timestamp: failureData.timestamp,
          context: failureData.context
        },
        recipients: ['admin'],
        timestamp: failureData.timestamp
      })

      console.log('Permission fetch failure notification created and broadcasted')
      return NextResponse.json({ success: true, notificationId: notification.id })

    } else if (type === 'health_check_failure') {
      const failureData: HealthCheckFailureData = data

      console.log('Creating health check failure notification:', failureData)

      // Create notification in database
      const notification = await prisma.notification.create({
        data: {
          type: 'SERVER_HEALTH_CHECK_FAILED',
          message: `Server "${failureData.serverName}" is ${failureData.status}`,
          data: JSON.stringify({
            serverId: failureData.serverId,
            serverName: failureData.serverName,
            status: failureData.status,
            error: failureData.error,
            responseTime: failureData.responseTime,
            timestamp: failureData.timestamp
          }),
          recipients: ['admin'], // Target all admins
          readBy: [] // No one has read it yet
        }
      })

      // Broadcast notification to connected admin clients
      await broadcastNotification({
        type: 'SERVER_HEALTH_CHECK_FAILED',
        message: notification.message,
        data: {
          id: notification.id,
          serverId: failureData.serverId,
          serverName: failureData.serverName,
          status: failureData.status,
          error: failureData.error,
          responseTime: failureData.responseTime,
          timestamp: failureData.timestamp
        },
        recipients: ['admin'],
        timestamp: failureData.timestamp
      })

      console.log('Health check failure notification created and broadcasted')
      return NextResponse.json({ success: true, notificationId: notification.id })

    } else {
      return NextResponse.json({ success: false, error: 'Invalid notification type' }, { status: 400 })
    }

  } catch (error) {
    console.error('Failed to create notification:', error)
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}