import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAuth, AuthenticationError, AuthorizationError } from '@/lib/middleware/require-auth'
import { checkRateLimit } from '@/lib/middleware/rate-limiting'
import { auditLoggingMiddleware } from '@/lib/middleware/audit-logging'
import { z } from 'zod'

// Input validation schemas
const remoteServerUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name must be less than 255 characters').optional(),
  url: z.string().url('Invalid URL format').optional(),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  isActive: z.boolean().optional(),
  clientId: z.string().min(1, 'Client ID is required').optional(),
  clientSecret: z.string().min(1, 'Client Secret is required').optional(),
  apiEndpoint: z.string().url('Invalid API endpoint URL').optional(),
  apiKey: z.string().min(1, 'API Key is required').optional(),
})

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: serverId } = await params;

    // Apply rate limiting
    const rateLimitResult = await checkRateLimit(request, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100, // 100 requests per minute
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        request,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Require authentication and admin permissions
    const authContext = await requireAuth(request, {
      requirePermissions: ['remote_servers:read'],
      auditLogging: true,
    });

    // Fetch remote server by ID with authorization check
    const remoteServer = await prisma.remoteServer.findFirst({
      where: {
        id: serverId,
        // Additional authorization: users can only access servers they have permissions for
        // This would be implemented based on your permission model
      },
      select: {
        id: true,
        name: true,
        url: true,
        description: true,
        isActive: true,
        clientId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!remoteServer) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'read',
        'remote_server',
        false,
        request,
        {
          statusCode: 404,
          errorMessage: 'Remote server not found',
          metadata: { serverId },
        }
      );

      return NextResponse.json(
        { error: 'Not Found', message: 'Remote server not found' },
        { status: 404 }
      );
    }

    // Log successful access
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'read',
      'remote_server',
      true,
      request,
      {
        metadata: { serverId, serverName: remoteServer.name },
      }
    );

    // Return remote server details (excluding sensitive data)
    return NextResponse.json({
      remoteServer: {
        id: remoteServer.id,
        name: remoteServer.name,
        url: remoteServer.url,
        description: remoteServer.description,
        isActive: remoteServer.isActive,
        clientId: remoteServer.clientId,
        createdAt: remoteServer.createdAt,
        updatedAt: remoteServer.updatedAt,
      }
    });
  } catch (error) {
    console.error('Error fetching remote server:', error);

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: serverId } = await params;

    // Apply rate limiting (stricter for write operations)
    const rateLimitResult = await checkRateLimit(request, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30, // 30 requests per minute for writes
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        request,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Require authentication and write permissions
    const authContext = await requireAuth(request, {
      requirePermissions: ['remote_servers:write'],
      auditLogging: true,
    });

    const body = await request.json();

    // Validate input using Zod schema
    const validation = remoteServerUpdateSchema.safeParse(body);

    if (!validation.success) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'update',
        'remote_server',
        false,
        request,
        {
          statusCode: 400,
          errorMessage: 'Validation error',
          metadata: { validationErrors: validation.error.flatten() },
        }
      );

      return NextResponse.json(
        {
          error: 'Validation Error',
          message: 'Invalid input data',
          details: validation.error.flatten()
        },
        { status: 400 }
      );
    }

    const updateData = validation.data;

    // Check if remote server exists and user has permission to modify it
    const existingServer = await prisma.remoteServer.findFirst({
      where: {
        id: serverId,
        // Additional authorization: users can only modify servers they have permissions for
        // This would be implemented based on your permission model
      },
    });

    if (!existingServer) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'update',
        'remote_server',
        false,
        request,
        {
          statusCode: 404,
          errorMessage: 'Remote server not found',
          metadata: { serverId },
        }
      );

      return NextResponse.json(
        { error: 'Not Found', message: 'Remote server not found' },
        { status: 404 }
      );
    }

    // Validate URL format if provided
    if (updateData.url) {
      try {
        new URL(updateData.url);
      } catch (error) {
        await auditLoggingMiddleware.logApiAccess(
          authContext.userId,
          'update',
          'remote_server',
          false,
          request,
          {
            statusCode: 400,
            errorMessage: 'Invalid URL format',
            metadata: { serverId, invalidUrl: updateData.url },
          }
        );

        return NextResponse.json(
          { error: 'Bad Request', message: 'Invalid URL format' },
          { status: 400 }
        );
      }
    }

    // Validate API endpoint format if provided
    if (updateData.apiEndpoint) {
      try {
        new URL(updateData.apiEndpoint);
      } catch (error) {
        await auditLoggingMiddleware.logApiAccess(
          authContext.userId,
          'update',
          'remote_server',
          false,
          request,
          {
            statusCode: 400,
            errorMessage: 'Invalid API endpoint format',
            metadata: { serverId, invalidApiEndpoint: updateData.apiEndpoint },
          }
        );

        return NextResponse.json(
          { error: 'Bad Request', message: 'Invalid API endpoint format' },
          { status: 400 }
        );
      }
    }

    // Prepare update data with only provided fields
    const updatePayload: any = {
      updatedAt: new Date(),
    };

    if (updateData.name !== undefined) updatePayload.name = updateData.name;
    if (updateData.url !== undefined) updatePayload.url = updateData.url;
    if (updateData.description !== undefined) updatePayload.description = updateData.description;
    if (updateData.isActive !== undefined) updatePayload.isActive = updateData.isActive;
    if (updateData.clientId !== undefined) updatePayload.clientId = updateData.clientId;
    if (updateData.clientSecret !== undefined) updatePayload.clientSecret = updateData.clientSecret;
    if (updateData.apiEndpoint !== undefined) updatePayload.apiEndpoint = updateData.apiEndpoint;
    if (updateData.apiKey !== undefined) updatePayload.apiKey = updateData.apiKey;

    // Update remote server
    const remoteServer = await prisma.remoteServer.update({
      where: { id: serverId },
      data: updatePayload,
      select: {
        id: true,
        name: true,
        url: true,
        description: true,
        isActive: true,
        clientId: true,
        clientSecret: true,
        apiEndpoint: true,
        apiKey: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Log successful update
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'update',
      'remote_server',
      true,
      request,
      {
        metadata: {
          serverId,
          serverName: remoteServer.name,
          updatedFields: Object.keys(updatePayload).filter(key => key !== 'updatedAt'),
        },
      }
    );

    // Return updated remote server (excluding sensitive data in response)
    return NextResponse.json({
      remoteServer: {
        id: remoteServer.id,
        name: remoteServer.name,
        url: remoteServer.url,
        description: remoteServer.description,
        isActive: remoteServer.isActive,
        clientId: remoteServer.clientId,
        apiEndpoint: remoteServer.apiEndpoint,
        createdAt: remoteServer.createdAt,
        updatedAt: remoteServer.updatedAt,
      }
    });
  } catch (error) {
    console.error('Error updating remote server:', error);

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: serverId } = await params;
    const urlObj = new URL(request.url);
    const permanent = urlObj.searchParams.get('permanent') === 'true';

    // Apply strict rate limiting for delete operations
    const rateLimitResult = await checkRateLimit(request, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10, // 10 requests per minute for deletes
    });

    if (!rateLimitResult.allowed) {
      await auditLoggingMiddleware.logRateLimitEvent(
        undefined,
        false,
        request,
        {
          limit: rateLimitResult.limit,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        }
      );

      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': String(rateLimitResult.limit),
            'X-RateLimit-Remaining': String(rateLimitResult.remaining),
            'X-RateLimit-Reset': String(rateLimitResult.resetTime),
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Require authentication and admin permissions for delete operations
    const authContext = await requireAuth(request, {
      requirePermissions: ['remote_servers:delete'],
      auditLogging: true,
    });

    // Check if remote server exists and user has permission to delete it
    const existingServer = await prisma.remoteServer.findFirst({
      where: {
        id: serverId,
        // Additional authorization: users can only delete servers they have permissions for
        // This would be implemented based on your permission model
      },
    });

    if (!existingServer) {
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'delete',
        'remote_server',
        false,
        request,
        {
          statusCode: 404,
          errorMessage: 'Remote server not found',
          metadata: { serverId },
        }
      );

      return NextResponse.json(
        { error: 'Not Found', message: 'Remote server not found' },
        { status: 404 }
      );
    }

    if (permanent) {
      // Permanently delete remote server (admin only operation)
      await prisma.remoteServer.delete({
        where: { id: serverId },
      });

      // Also delete related records
      await prisma.authorizationCode.deleteMany({
        where: { remoteServerId: serverId },
      });

      await prisma.oAuthToken.deleteMany({
        where: { remoteServerId: serverId },
      });

      await prisma.auditLog.updateMany({
        where: { remote_server_id: serverId },
        data: { remote_server_id: null },
      });

      // Log successful permanent deletion
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'delete',
        'remote_server',
        true,
        request,
        {
          metadata: {
            serverId,
            serverName: existingServer.name,
            deletionType: 'permanent',
          },
        }
      );

      return NextResponse.json({
        success: true,
        message: 'Remote server deleted permanently',
      });
    } else {
      // Deactivate remote server
      const remoteServer = await prisma.remoteServer.update({
        where: { id: serverId },
        data: {
          isActive: false,
          updatedAt: new Date(),
        },
        select: {
          id: true,
          name: true,
          isActive: true,
        },
      });

      // Log successful deactivation
      await auditLoggingMiddleware.logApiAccess(
        authContext.userId,
        'deactivate',
        'remote_server',
        true,
        request,
        {
          metadata: {
            serverId,
            serverName: remoteServer.name,
            previousStatus: existingServer.isActive,
            newStatus: false,
          },
        }
      );

      return NextResponse.json({
        success: true,
        message: 'Remote server deactivated successfully',
        remoteServer,
      });
    }
  } catch (error) {
    console.error('Error deleting remote server:', error);

    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}