import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: remoteServerId } = await params
    
    // Find the remote server
    const remoteServers: any = await prisma.$queryRawUnsafe(`SELECT * FROM remote_servers WHERE id = $1`, remoteServerId)
    const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null;
    
    if (!remoteServer) {
      return Response.json(
        { error: 'Remote server not found' },
        { status: 404 }
      )
    }
    
    // First, try to get local roles from database
    // Note: This assumes there's a role_remote_server_access table or similar
    // For now, we'll return empty array since roles might not be implemented locally yet
    console.log('Checking for local roles for server:', remoteServerId)

    // TODO: Implement local roles storage if needed
    // For now, try to sync from remote server
    try {
      const permissionUrl = `${remoteServer.url}/api/permissions`
      console.log('Fetching roles from remote server:', permissionUrl)

      const infoResponse = await fetch(permissionUrl, {
        headers: {
          'Authorization': `Bearer ${remoteServer.apiKey}`,
          'Content-Type': 'application/json'
        }
      })

      console.log('Remote roles response status:', infoResponse.status)

      if (infoResponse.ok) {
        const info = await infoResponse.json()
        console.log('Received remote roles data:', info)

        // Transform the response to match the expected format
        const roles = info.roles?.map((role: any, index: number) => ({
          id: `role-${index}`,
          name: role.name,
          description: role.description,
          permissions: (role.permissions || []).map((permName: string, permIndex: number) => ({
            id: `perm-${index}-${permIndex}`,
            name: permName
          })),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        })) || []

        console.log('Transformed roles:', roles)
        return Response.json(roles)
      }
    } catch (syncError) {
      console.log('Failed to sync roles from remote server:', syncError)
    }

    // If no roles found, return some default roles for testing
    console.log('No roles available for this server, returning default roles')

    // Return some default roles for testing purposes
    const defaultRoles = [
      {
        id: 'role-admin',
        name: 'Administrator',
        description: 'Full administrative access',
        permissions: [
          { id: 'perm-admin-1', name: 'admin' },
          { id: 'perm-admin-2', name: 'read' },
          { id: 'perm-admin-3', name: 'write' }
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'role-user',
        name: 'User',
        description: 'Standard user access',
        permissions: [
          { id: 'perm-user-1', name: 'read' }
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]

    return Response.json(defaultRoles)
  } catch (error) {
    console.error('Get roles error:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}