import { NextRequest, NextResponse } from 'next/server'
import { PermissionSyncService } from '@/lib/services/permission-sync-service'

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: remoteServerId } = await params
    
    const syncService = new PermissionSyncService()
    const result = await syncService.syncServerPermissions(remoteServerId)
    
    if (!result.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: result.errors.join(', '),
          serverId: remoteServerId 
        },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      success: true,
      message: 'Remote server permissions synced successfully',
      data: {
        serverId: result.serverId,
        serverName: result.serverName,
        permissionsSynced: result.permissionsSynced,
        rolesSynced: result.rolesSynced,
        availablePermissionsSynced: result.availablePermissionsSynced,
        timestamp: result.timestamp,
        errors: result.errors,
        // Include the actual permissions data for the frontend
        permissions: result.permissions || [],
        roles: result.roles || [],
        availablePermissions: result.availablePermissions || [],
        rolePermissions: result.rolePermissions || {},
        summary: result.summary || {}
      }
    })
  } catch (error: any) {
    console.error('Sync remote server error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    )
  }
}