import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: remoteServerId } = await params

    console.log('=== FETCHING PERMISSIONS FOR REMOTE SERVER ===')
    console.log('Remote Server ID:', remoteServerId)

    // Find the remote server
    const remoteServer = await prisma.remoteServer.findUnique({
      where: { id: remoteServerId }
    })

    console.log('Remote Server Data:', remoteServer)

    if (!remoteServer) {
      return NextResponse.json(
        { success: false, error: 'Remote server not found' },
        { status: 404 }
      )
    }

    // Get permissions from database
    const permissions = await prisma.remote_server_permissions.findMany({
      where: {
        remote_server_id: remoteServerId,
        is_active: true
      },
      select: {
        permission_name: true,
        permission_description: true
      }
    })

    // Get roles from database - include roles that have remote server access OR are non-system roles
    const roles = await prisma.role.findMany({
      where: {
        OR: [
          {
            role_remote_server_access: {
              some: {
                remote_server_id: remoteServerId
              }
            }
          },
          {
            isSystem: false
          }
        ]
      },
      select: {
        name: true,
        description: true,
        isSystem: true,
        rolePermissions: {
          select: {
            permission: {
              select: {
                name: true
              }
            }
          }
        }
      }
    })

    // Transform roles to include permissions array
    const transformedRoles = roles.map(role => ({
      name: role.name,
      description: role.description || '',
      permissions: role.rolePermissions.map(rp => rp.permission.name)
    }))

    // Get role permissions mapping
    const rolePermissions: Record<string, string[]> = {}
    transformedRoles.forEach(role => {
      rolePermissions[role.name] = role.permissions
    })

    const result = {
      success: true,
      data: {
        permissions: permissions.map(p => ({
          name: p.permission_name,
          description: p.permission_description || p.permission_name
        })),
        roles: transformedRoles,
        availablePermissions: permissions.map(p => ({
          name: p.permission_name,
          description: p.permission_description || p.permission_name
        })),
        rolePermissions,
        summary: {
          totalPermissions: permissions.length,
          totalRoles: transformedRoles.length
        }
      }
    }

    console.log('Returning permissions data:', result)
    return NextResponse.json(result)
  } catch (error) {
    console.error('Get permissions error:', error)
    console.error('Error type:', typeof error)
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace')
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}