import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { localPermissionService } from '@/lib/services/local-permission-service'
import { prisma } from '@/lib/prisma'

// GET /api/local-roles - Get available local roles
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const roles = await localPermissionService.getAvailableRoles()

    // Transform the response to include permission details
    const transformedRoles = roles.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description,
      isSystem: role.isSystem,
      permissions: role.rolePermissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource,
        action: rp.permission.action,
        description: rp.permission.description
      })),
      permissionCount: role.rolePermissions.length
    }))

    return Response.json({
      success: true,
      data: transformedRoles
    })
  } catch (error) {
    console.error('Error fetching local roles:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/local-roles - Assign/remove local roles to/from users
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { userId, roleId, action } = body

    console.log('Local role assignment request:', { userId, roleId, action, body })

    if (!userId || !roleId || !action) {
      console.log('Missing required fields:', { userId, roleId, action })
      return Response.json(
        { error: 'Missing required fields: userId, roleId, action' },
        { status: 400 }
      )
    }

    if (action === 'assign') {
      // Check if role exists
      console.log('Looking for role:', roleId)
      const role = await prisma.role.findUnique({
        where: { id: roleId }
      })

      console.log('Found role:', role)

      if (!role) {
        // List all available roles for debugging
        const allRoles = await prisma.role.findMany({
          select: { id: true, name: true }
        })
        console.log('Available roles:', allRoles)

        return Response.json(
          { error: `Role with ID '${roleId}' not found. Available roles: ${allRoles.map(r => `${r.id} (${r.name})`).join(', ')}` },
          { status: 404 }
        )
      }

      // Check if user already has this role
      const existingUserRole = await prisma.userRole.findUnique({
        where: {
          userId_roleId: {
            userId: userId,
            roleId: role.id
          }
        }
      })

      if (existingUserRole) {
        console.log('User already has role:', { userId, roleId, existingUserRole })
        return Response.json(
          { error: `User already has the '${role.name}' role` },
          { status: 400 }
        )
      }

      // Assign role to user
      await prisma.userRole.create({
        data: {
          userId: userId,
          roleId: role.id,
          assignedBy: (session.user as any).id
        }
      })

      return Response.json({
        success: true,
        message: 'Role assigned successfully'
      })

    } else if (action === 'remove') {
      // Check if role exists
      const role = await prisma.role.findUnique({
        where: { id: roleId }
      })

      if (!role) {
        return Response.json(
          { error: `Role with ID '${roleId}' not found` },
          { status: 404 }
        )
      }

      // Check if user has this role
      const existingUserRole = await prisma.userRole.findUnique({
        where: {
          userId_roleId: {
            userId: userId,
            roleId: role.id
          }
        }
      })

      if (!existingUserRole) {
        return Response.json(
          { error: 'User does not have this role' },
          { status: 400 }
        )
      }

      // Remove role from user
      await prisma.userRole.delete({
        where: {
          userId_roleId: {
            userId: userId,
            roleId: role.id
          }
        }
      })

      return Response.json({
        success: true,
        message: 'Role removed successfully'
      })
    } else {
      return Response.json(
        { error: 'Invalid action. Use "assign" or "remove"' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Error managing local role:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}