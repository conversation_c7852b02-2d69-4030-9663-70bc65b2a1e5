'use server'

import { prisma } from '@/lib/prisma'
import { randomUUID } from 'crypto'
import { ApiKeyService } from '@/lib/services/api-key'

/**
 * Fetch all remote servers with OAuth information
 */
export async function getAllRemoteServers() {
  try {
    // Use raw SQL query to fetch from remote_servers table
    const remoteServers: any = await prisma.$queryRawUnsafe(`
      SELECT 
        id,
        name,
        url,
        api_endpoint as "apiEndpoint",
        oauth_redirect_uris as "oauthRedirectUris",
        development_redirect_uris as "developmentRedirectUris",
        "apiKey",
        description,
        "isActive",
        "createdAt",
        "updatedAt",
        client_id as "clientId",
        client_secret as "clientSecret",
        redirect_uris as "redirectUris"
      FROM remote_servers
      ORDER BY "createdAt" DESC
    `)
    
    return { success: true, data: remoteServers }
  } catch (error: any) {
    console.error('Failed to fetch remote servers:', error)
    return { success: false, error: 'Failed to fetch remote servers' }
  }
}

/**
 * Create a new remote server with OAuth information
 */
export async function createRemoteServer(data: {
  name: string
  url: string
  apiEndpoint?: string | null
  oauthRedirectUris?: string[] | null
  developmentRedirectUris?: string[] | null
  description?: string | null
}) {
  try {
    // Generate API key
    const apiKeyService = new ApiKeyService()
    const apiKey = apiKeyService.generateApiKey()
    
    // Generate OAuth client credentials
    const clientId = `client_${randomUUID().replace(/-/g, '')}`
    const clientSecret = `secret_${randomUUID().replace(/-/g, '')}`
    
    // Create new remote server
    const newServer: any = await prisma.$queryRawUnsafe(`
      INSERT INTO remote_servers (
        id, 
        name, 
        url, 
        api_endpoint,
        oauth_redirect_uris,
        development_redirect_uris,
        "apiKey", 
        description, 
        "isActive", 
        "createdAt", 
        "updatedAt",
        client_id,
        client_secret,
        redirect_uris,
        grant_types,
        default_scopes
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW(), $10, $11, $12, $13, $14
      )
      RETURNING 
        id,
        name,
        url,
        api_endpoint as "apiEndpoint",
        oauth_redirect_uris as "oauthRedirectUris",
        development_redirect_uris as "developmentRedirectUris",
        "apiKey",
        description,
        "isActive",
        "createdAt",
        "updatedAt",
        client_id as "clientId",
        client_secret as "clientSecret",
        redirect_uris as "redirectUris"
    `,
    randomUUID(),
    data.name,
    data.url,
    data.apiEndpoint || null,
    data.oauthRedirectUris || [],
    data.developmentRedirectUris || [],
    apiKey,
    data.description || null,
    true, // isActive
    clientId,
    clientSecret,
    ['http://localhost:3002/api/auth/callback/member-portal-custom'], // redirect_uris
    ['authorization_code', 'refresh_token'], // grant_types
    ['read:profile'] // default_scopes
    )
    
    const server = Array.isArray(newServer) && newServer.length > 0 ? newServer[0] : null
    
    if (!server) {
      return { success: false, error: 'Failed to create remote server' }
    }
    
    return { success: true, data: server }
  } catch (error: any) {
    console.error('Failed to create remote server:', error)
    return { success: false, error: 'Failed to create remote server' }
  }
}

/**
 * Fetch a specific remote server with OAuth information
 */
export async function getRemoteServerById(id: string) {
  try {
    // Use raw SQL query to fetch specific remote server
    const remoteServers: any = await prisma.$queryRawUnsafe(`
      SELECT 
        id,
        name,
        url,
        api_endpoint as "apiEndpoint",
        oauth_redirect_uris as "oauthRedirectUris",
        development_redirect_uris as "developmentRedirectUris",
        "apiKey",
        description,
        "isActive",
        "createdAt",
        "updatedAt",
        client_id as "clientId",
        client_secret as "clientSecret",
        redirect_uris as "redirectUris"
      FROM remote_servers
      WHERE id = $1
    `, id)
    
    const server = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null
    
    if (!server) {
      return { success: false, error: 'Remote server not found' }
    }
    
    return { success: true, data: server }
  } catch (error: any) {
    console.error('Failed to fetch remote server:', error)
    return { success: false, error: 'Failed to fetch remote server' }
  }
}

/**
 * Update a remote server's OAuth configuration
 */
export async function updateRemoteServerConfig(id: string, data: {
  name?: string
  url?: string
  apiEndpoint?: string | null
  oauthRedirectUris?: string[] | null
  developmentRedirectUris?: string[] | null
  apiKey?: string
  description?: string
  isActive?: boolean
  clientId?: string
  clientSecret?: string
  redirectUris?: string[]
}) {
  try {
    // First check if remote server exists
    const existingServers: any = await prisma.$queryRawUnsafe(
      `SELECT id FROM remote_servers WHERE id = $1`,
      id
    )
    
    const existingServer = Array.isArray(existingServers) && existingServers.length > 0 ? existingServers[0] : null
    
    if (!existingServer) {
      return { success: false, error: 'Remote server not found' }
    }
    
    // Build update query dynamically
    const updates: string[] = []
    const values: any[] = [id] // $1 is for id
    let valueIndex = 2 // Start from $2
    
    if (data.name !== undefined) {
      updates.push(`name = $${valueIndex}`)
      values.push(data.name)
      valueIndex++
    }
    
    if (data.url !== undefined) {
      updates.push(`url = $${valueIndex}`)
      values.push(data.url)
      valueIndex++
    }
    
    if (data.description !== undefined) {
      updates.push(`description = $${valueIndex}`)
      values.push(data.description)
      valueIndex++
    }
    
    if (data.isActive !== undefined) {
      updates.push(`"isActive" = $${valueIndex}`)
      values.push(data.isActive)
      valueIndex++
    }
    
    if (data.clientId !== undefined) {
      updates.push(`client_id = $${valueIndex}`)
      values.push(data.clientId)
      valueIndex++
    }
    
    if (data.clientSecret !== undefined) {
      updates.push(`client_secret = $${valueIndex}`)
      values.push(data.clientSecret)
      valueIndex++
    }
    
    if (data.redirectUris !== undefined) {
      updates.push(`redirect_uris = $${valueIndex}`)
      values.push(data.redirectUris)
      valueIndex++
    }
    
    if (data.apiKey !== undefined) {
      updates.push(`"apiKey" = $${valueIndex}`)
      values.push(data.apiKey)
      valueIndex++
    }
    
    if (data.apiEndpoint !== undefined) {
      updates.push(`api_endpoint = $${valueIndex}`)
      values.push(data.apiEndpoint)
      valueIndex++
    }
    
    if (data.oauthRedirectUris !== undefined) {
      updates.push(`oauth_redirect_uris = $${valueIndex}`)
      values.push(data.oauthRedirectUris)
      valueIndex++
    }
    
    if (data.developmentRedirectUris !== undefined) {
      updates.push(`development_redirect_uris = $${valueIndex}`)
      values.push(data.developmentRedirectUris)
      valueIndex++
    }
    
    // Always update the updated_at timestamp
    updates.push(`"updatedAt" = NOW()`)
    
    if (updates.length === 1) {
      // Only timestamp update, no other changes
      return { success: true, message: 'No changes to update' }
    }
    
    // Build the final query
    const query = `
      UPDATE remote_servers 
      SET ${updates.join(', ')}
      WHERE id = $1
      RETURNING 
        id,
        name,
        url,
        api_endpoint as "apiEndpoint",
        oauth_redirect_uris as "oauthRedirectUris",
        development_redirect_uris as "developmentRedirectUris",
        "apiKey",
        description,
        "isActive",
        "createdAt",
        "updatedAt",
        client_id as "clientId",
        client_secret as "clientSecret",
        redirect_uris as "redirectUris"
    `
    
    const updatedServers: any = await prisma.$queryRawUnsafe(query, ...values)
    const updatedServer = Array.isArray(updatedServers) && updatedServers.length > 0 ? updatedServers[0] : null
    
    return { success: true, data: updatedServer }
  } catch (error: any) {
    console.error('Failed to update remote server:', error)
    return { success: false, error: 'Failed to update remote server' }
  }
}

/**
 * Delete a remote server
 */
export async function deleteRemoteServerById(id: string) {
  try {
    // First check if remote server exists
    const existingServers: any = await prisma.$queryRawUnsafe(
      `SELECT id FROM remote_servers WHERE id = $1`,
      id
    )
    
    const existingServer = Array.isArray(existingServers) && existingServers.length > 0 ? existingServers[0] : null
    
    if (!existingServer) {
      return { success: false, error: 'Remote server not found' }
    }
    
    // Delete the remote server
    await prisma.$executeRawUnsafe(
      `DELETE FROM remote_servers WHERE id = $1`,
      id
    )
    
    return { success: true }
  } catch (error: any) {
    console.error('Failed to delete remote server:', error)
    return { success: false, error: 'Failed to delete remote server' }
  }
}