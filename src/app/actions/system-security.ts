'use server'

import { prisma } from '@/lib/prisma'

/**
 * Fetch system settings
 */
export async function getSystemSettings() {
  try {
    const settings: any = await prisma.$queryRaw`SELECT * FROM system_settings LIMIT 1;`
    
    if (!settings || (Array.isArray(settings) && settings.length === 0)) {
      // Create default settings if none exist
      await prisma.$executeRaw`
        INSERT INTO system_settings (
          "security_level", "maintenance_mode", "max_login_attempts", "lockout_duration", "session_timeout",
          "password_min_length", "password_require_upper", "password_require_lower", "password_require_number",
          "password_require_special", "two_factor_auth_enabled", "ip_whitelist_enabled", "ip_whitelist",
          "audit_log_retention", "api_rate_limit", "cors_origins", "email_verification_required",
          "password_reset_token_expiry", "created_at", "updated_at"
        ) VALUES (
          'HIGH', false, 5, 30, 1800,
          12, true, true, true,
          true, false, false, '',
          365, 1000, 'https://example.com', true,
          2, NOW(), NOW()
        );
      `
      const newSettings: any = await prisma.$queryRaw`SELECT * FROM system_settings LIMIT 1;`
      return { success: true, data: Array.isArray(newSettings) ? newSettings[0] : newSettings }
    }
    
    return { success: true, data: Array.isArray(settings) ? settings[0] : settings }
  } catch (error: any) {
    console.error('Failed to fetch system settings:', error)
    return { success: false, error: 'Failed to fetch system settings' }
  }
}

/**
 * Update system settings
 */
export async function updateSystemSettings(data: {
  securityLevel?: string
  maintenanceMode?: boolean
  maxLoginAttempts?: number
  lockoutDuration?: number
  sessionTimeout?: number
  passwordMinLength?: number
  passwordRequireUpper?: boolean
  passwordRequireLower?: boolean
  passwordRequireNumber?: boolean
  passwordRequireSpecial?: boolean
  twoFactorAuthEnabled?: boolean
  ipWhitelistEnabled?: boolean
  ipWhitelist?: string
  auditLogRetention?: number
  apiRateLimit?: number
  corsOrigins?: string
  emailVerificationRequired?: boolean
  passwordResetTokenExpiry?: number
}) {
  try {
    // Get existing settings or create new ones
    let settings: any = await prisma.$queryRaw`SELECT * FROM system_settings LIMIT 1;`
    
    if (!settings || (Array.isArray(settings) && settings.length === 0)) {
      // Create default settings if none exist
      await prisma.$executeRaw`
        INSERT INTO system_settings (
          "security_level", "maintenance_mode", "max_login_attempts", "lockout_duration", "session_timeout",
          "password_min_length", "password_require_upper", "password_require_lower", "password_require_number",
          "password_require_special", "two_factor_auth_enabled", "ip_whitelist_enabled", "ip_whitelist",
          "audit_log_retention", "api_rate_limit", "cors_origins", "email_verification_required",
          "password_reset_token_expiry", "created_at", "updated_at"
        ) VALUES (
          'HIGH', false, 5, 30, 1800,
          12, true, true, true,
          true, false, false, '',
          365, 1000, 'https://example.com', true,
          2, NOW(), NOW()
        );
      `
      settings = await prisma.$queryRaw`SELECT * FROM system_settings LIMIT 1;`
    }
    
    const setting = Array.isArray(settings) ? settings[0] : settings;
    
    // Build the update query
    const updateFields: string[] = []
    const updateValues: any[] = []
    let paramIndex = 1
    
    if (data.securityLevel !== undefined) {
      updateFields.push(`"security_level" = ${paramIndex}`)
      updateValues.push(data.securityLevel)
      paramIndex++
    }
    if (data.maintenanceMode !== undefined) {
      updateFields.push(`"maintenance_mode" = ${paramIndex}`)
      updateValues.push(data.maintenanceMode)
      paramIndex++
    }
    if (data.maxLoginAttempts !== undefined) {
      updateFields.push(`"max_login_attempts" = ${paramIndex}`)
      updateValues.push(data.maxLoginAttempts)
      paramIndex++
    }
    if (data.sessionTimeout !== undefined) {
      updateFields.push(`"session_timeout" = ${paramIndex}`)
      updateValues.push(data.sessionTimeout)
      paramIndex++
    }
    if (data.passwordMinLength !== undefined) {
      updateFields.push(`"password_min_length" = ${paramIndex}`)
      updateValues.push(data.passwordMinLength)
      paramIndex++
    }
    if (data.passwordRequireUpper !== undefined) {
      updateFields.push(`"password_require_upper" = ${paramIndex}`)
      updateValues.push(data.passwordRequireUpper)
      paramIndex++
    }
    if (data.passwordRequireLower !== undefined) {
      updateFields.push(`"password_require_lower" = ${paramIndex}`)
      updateValues.push(data.passwordRequireLower)
      paramIndex++
    }
    if (data.passwordRequireNumber !== undefined) {
      updateFields.push(`"password_require_number" = ${paramIndex}`)
      updateValues.push(data.passwordRequireNumber)
      paramIndex++
    }
    if (data.passwordRequireSpecial !== undefined) {
      updateFields.push(`"password_require_special" = ${paramIndex}`)
      updateValues.push(data.passwordRequireSpecial)
      paramIndex++
    }
    if (data.twoFactorAuthEnabled !== undefined) {
      updateFields.push(`"two_factor_auth_enabled" = ${paramIndex}`)
      updateValues.push(data.twoFactorAuthEnabled)
      paramIndex++
    }
    if (data.ipWhitelistEnabled !== undefined) {
      updateFields.push(`"ip_whitelist_enabled" = ${paramIndex}`)
      updateValues.push(data.ipWhitelistEnabled)
      paramIndex++
    }
    if (data.ipWhitelist !== undefined) {
      updateFields.push(`"ip_whitelist" = ${paramIndex}`)
      updateValues.push(data.ipWhitelist)
      paramIndex++
    }
    if (data.auditLogRetention !== undefined) {
      updateFields.push(`"audit_log_retention" = ${paramIndex}`)
      updateValues.push(data.auditLogRetention)
      paramIndex++
    }
    if (data.apiRateLimit !== undefined) {
      updateFields.push(`"api_rate_limit" = ${paramIndex}`)
      updateValues.push(data.apiRateLimit)
      paramIndex++
    }
    if (data.corsOrigins !== undefined) {
      updateFields.push(`"cors_origins" = ${paramIndex}`)
      updateValues.push(data.corsOrigins)
      paramIndex++
    }
    if (data.emailVerificationRequired !== undefined) {
      updateFields.push(`"email_verification_required" = ${paramIndex}`)
      updateValues.push(data.emailVerificationRequired)
      paramIndex++
    }
    if (data.passwordResetTokenExpiry !== undefined) {
      updateFields.push(`"password_reset_token_expiry" = ${paramIndex}`)
      updateValues.push(data.passwordResetTokenExpiry)
      paramIndex++
    }

    updateFields.push(`"updated_at" = ${paramIndex}`)
    updateValues.push(new Date())
    
    if (updateFields.length > 1) { // More than just updated_at
      const updateQuery = `
        UPDATE system_settings 
        SET ${updateFields.join(', ')} 
        WHERE id = ${paramIndex + 1}
        RETURNING *;
      `
      
      updateValues.push(setting.id)
      
      const updatedSettings: any = await prisma.$queryRawUnsafe(updateQuery, ...updateValues)
      return { success: true, data: Array.isArray(updatedSettings) ? updatedSettings[0] : updatedSettings }
    }
    
    return { success: true, data: setting }
  } catch (error: any) {
    console.error('Failed to update system settings:', error)
    return { success: false, error: 'Failed to update system settings' }
  }
}

/**
 * Fetch audit logs with optional filtering
 */
export async function getAuditLogs(filters?: {
  search?: string
  action?: string
  resource?: string
  startDate?: Date
  endDate?: Date
  success?: boolean
  page?: number
  limit?: number
}) {
  try {
    const page = filters?.page || 1
    const limit = filters?.limit || 50
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    // Apply filters
    if (filters?.search) {
      where.OR = [
        { action: { contains: filters.search, mode: 'insensitive' } },
        { resource: { contains: filters.search, mode: 'insensitive' } }
      ]
    }
    
    if (filters?.action) {
      where.action = filters.action
    }
    
    if (filters?.resource) {
      where.resource = filters.resource
    }
    
    if (filters?.startDate || filters?.endDate) {
      where.timestamp = {}
      if (filters.startDate) {
        where.timestamp.gte = filters.startDate
      }
      if (filters.endDate) {
        where.timestamp.lte = filters.endDate
      }
    }
    
    if (filters?.success !== undefined) {
      where.success = filters.success
    }
    
    const [logs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        orderBy: {
          timestamp: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.auditLog.count({ where })
    ])
    
    return { 
      success: true, 
      data: {
        logs,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    }
  } catch (error: any) {
    console.error('Failed to fetch audit logs:', error)
    return { success: false, error: 'Failed to fetch audit logs' }
  }
}

/**
 * Export audit logs
 */
export async function exportAuditLogs(filters?: {
  startDate?: Date
  endDate?: Date
  format?: 'csv' | 'json'
}) {
  try {
    const where: any = {}
    
    if (filters?.startDate || filters?.endDate) {
      where.timestamp = {}
      if (filters.startDate) {
        where.timestamp.gte = filters.startDate
      }
      if (filters.endDate) {
        where.timestamp.lte = filters.endDate
      }
    }
    
    const logs = await prisma.auditLog.findMany({
      where,
      orderBy: {
        timestamp: 'desc'
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })
    
    // In a real implementation, we would generate the export file
    // For now, we'll just return the data
    return { 
      success: true, 
      data: {
        logs,
        format: filters?.format || 'json',
        count: logs.length
      }
    }
  } catch (error: any) {
    console.error('Failed to export audit logs:', error)
    return { success: false, error: 'Failed to export audit logs' }
  }
}