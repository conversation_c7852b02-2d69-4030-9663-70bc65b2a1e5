'use server'

import { prisma } from '@/lib/prisma'

/**
 * Fetch permissions from a remote server
 */
export async function fetchRemoteServerPermissions(serverId: string) {
  try {
    console.log('=== FETCHING REMOTE SERVER PERMISSIONS ===')
    console.log('Server ID:', serverId)
    
    // Get the remote server
    const server = await prisma.remoteServer.findUnique({
      where: { id: serverId }
    })
    
    console.log('Server Data:', server)
    
    if (!server) {
      return { success: false, error: 'Remote server not found' }
    }
    
    // Make an HTTP request to the remote server's /api/permissions endpoint to fetch permissions and roles
    // Use the first allowed origin as the base URL if server.url is not available
    const baseUrl = server.url || (server.allowedOrigins && server.allowedOrigins.length > 0 ? server.allowedOrigins[0] : null)

    if (!baseUrl) {
      return { success: false, error: 'Remote server URL not configured' }
    }

    // Remove trailing slash from base URL and ensure proper URL construction
    const cleanBaseUrl = baseUrl.replace(/\/$/, '')
    const permissionUrl = `${cleanBaseUrl}/api/permissions`
    console.log('Fetching permissions from remote server:', permissionUrl)
    console.log('Using API Key:', server.apiKey)
    
    const infoResponse = await fetch(permissionUrl, {
      headers: {
        'Authorization': `Bearer ${server.apiKey}`,
        'Content-Type': 'application/json'
      }
    })
    
    console.log('Remote server response status:', infoResponse.status)
    
    if (!infoResponse.ok) {
      return { success: false, error: 'Error connecting to remote server' }
    }
    
    const info = await infoResponse.json()
    console.log('Remote server permissions response:', info)
    
    // Handle the new comprehensive structure from NWAPromote
    let permissions: Array<{ name: string; description: string }> = []
    let roles: Array<{ name: string; description: string; permissions: string[] }> = []
    let availablePermissions: Array<{ name: string; description: string }> = []
    let rolePermissions: Record<string, string[]> = {}

    // Extract data from the new structure
    if (info.roles && Array.isArray(info.roles)) {
      roles = info.roles.map((role: any) => ({
        name: role.name || '',
        description: role.description || '',
        permissions: role.permissions || []
      })).filter((role: any) => role.name && typeof role.name === 'string' && role.name.trim() !== '')
    }

    if (info.availablePermissions && Array.isArray(info.availablePermissions)) {
      availablePermissions = info.availablePermissions.map((perm: any) => ({
        name: perm.name || '',
        description: perm.description || ''
      })).filter((perm: any) => perm.name && typeof perm.name === 'string' && perm.name.trim() !== '')
    }

    if (info.rolePermissions && typeof info.rolePermissions === 'object') {
      rolePermissions = info.rolePermissions
    }

    // Combine roles and available permissions for backward compatibility
    // Include all permissions from roles and available permissions
    const allPermissions = new Map<string, { name: string; description: string }>()

    // Add permissions from roles
    roles.forEach(role => {
      role.permissions.forEach(permName => {
        if (permName && typeof permName === 'string' && permName.trim() !== '') {
          allPermissions.set(permName, { name: permName, description: `Permission from ${role.name} role` })
        }
      })
    })

    // Add available permissions
    availablePermissions.forEach(perm => {
      if (perm.name && typeof perm.name === 'string' && perm.name.trim() !== '') {
        allPermissions.set(perm.name, perm)
      }
    })

    permissions = Array.from(allPermissions.values())
    
    return {
      success: true,
      data: {
        permissions,
        roles,
        availablePermissions,
        rolePermissions,
        summary: info.summary || {}
      }
    }
  } catch (error: any) {
    console.error('Failed to fetch remote server permissions:', error)
    console.error('Error type:', typeof error)
    console.error('Error message:', error.message || 'No message')
    return { success: false, error: 'Failed to fetch permissions' }
  }
}

/**
 * Assign permissions to a user for a specific remote server
 */
export async function assignUserPermissions(
  userId: string,
  serverId: string,
  permissionNames: string[]
) {
  try {
    // Get the remote server and user
    const [server, user] = await Promise.all([
      prisma.remoteServer.findUnique({ where: { id: serverId } }),
      prisma.user.findUnique({ where: { id: userId } })
    ])
    
    if (!server) {
      return { success: false, error: 'Remote server not found' }
    }
    
    if (!user) {
      return { success: false, error: 'User not found' }
    }
    
    // Get the scopes that match the permission names
    const scopes = await prisma.scope.findMany({
      where: {
        name: {
          in: permissionNames
        }
      }
    })
    
    // Create UserProjectScope entries for each permission
    // Note: We're reusing the UserProjectScope model for remote server permissions
    const userProjectScopes = await Promise.all(
      scopes.map(scope => 
        prisma.userProjectScope.upsert({
          where: {
            userId_projectId_scopeId: {
              userId,
              projectId: serverId,
              scopeId: scope.id
            }
          },
          update: {},
          create: {
            userId,
            projectId: serverId,
            scopeId: scope.id
          }
        })
      )
    )
    
    return { success: true, data: userProjectScopes }
  } catch (error: any) {
    console.error('Failed to assign user permissions:', error)
    return { success: false, error: 'Failed to assign permissions' }
  }
}

/**
 * Remove permissions from a user for a specific remote server
 */
export async function removeUserPermissions(
  userId: string,
  serverId: string,
  permissionNames: string[]
) {
  try {
    // Get the scopes that match the permission names
    const scopes = await prisma.scope.findMany({
      where: {
        name: {
          in: permissionNames
        }
      }
    })
    
    // Delete UserProjectScope entries for each permission
    // Note: We're reusing the UserProjectScope model for remote server permissions
    await Promise.all(
      scopes.map(scope => 
        prisma.userProjectScope.deleteMany({
          where: {
            userId,
            projectId: serverId,
            scopeId: scope.id
          }
        })
      )
    )
    
    return { success: true }
  } catch (error: any) {
    console.error('Failed to remove user permissions:', error)
    return { success: false, error: 'Failed to remove permissions' }
  }
}

/**
 * Get user permissions for a specific remote server
 */
export async function getUserPermissions(userId: string, serverId: string) {
  try {
    const userProjectScopes = await prisma.userProjectScope.findMany({
      where: {
        userId,
        projectId: serverId
      },
      include: {
        scope: true
      }
    })
    
    const permissions = userProjectScopes.map(ups => ups.scope)
    
    return { success: true, data: permissions }
  } catch (error: any) {
    console.error('Failed to get user permissions:', error)
    return { success: false, error: 'Failed to get permissions' }
  }
}