'use client';

import React, { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';

interface IdentificationData {
  user: {
    id: string;
    name: string;
    email: string;
  };
  profile: {
    peaceAmbassadorNumber: string | null;
    title: string | null;
    country: string | null;
    city: string | null;
    dateOfBirth: Date | null;
  } | null;
  identifications: Array<{
    id: string;
    type: string;
    number: string;
    isActive: boolean;
    createdAt: Date;
  }>;
  treatyNumbers: Array<{
    id: string;
    treatyType: string;
    number: string;
    isActive: boolean;
    createdAt: Date;
  }>;
  positions: Array<{
    id: string;
    title: string;
    description: string | null;
    level: number;
    isActive: boolean;
    startDate: Date;
    endDate: Date | null;
    notes: string | null;
  }>;
  nationTreatyMemberships: Array<{
    id: string;
    nationTreaty: string;
    role: string;
    status: string;
    joinDate: Date;
    notes: string | null;
  }>;
  nationTreatyEnvoys: Array<{
    id: string;
    nationTreaty: string;
    envoyType: string;
    title: string | null;
    status: string;
    address: string | null;
    city: string | null;
    country: string | null;
    phone: string | null;
    mobile: string | null;
    email: string | null;
  }>;
}

export default function IdentificationTestPage() {
  const [identificationData, setIdentificationData] = useState<IdentificationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [testUserId, setTestUserId] = useState<string>('');

  useEffect(() => {
    const fetchData = async () => {
      if (!testUserId) {
        setError('Please enter a test user ID');
        setLoading(false);
        return;
      }

      try {
        console.log('🔄 Fetching identification data for user:', testUserId);
        const response = await fetch(`/api/users/${testUserId}/identification`);

        if (!response.ok) {
          throw new Error(`Failed to fetch data: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('✅ Data received:', data);
        setIdentificationData(data);
      } catch (error) {
        console.error('❌ Error:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [testUserId]);

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading identification data...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">🧪 Identification API Test Page</h1>

        {/* Test User ID Input */}
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <label htmlFor="testUserId" className="block text-sm font-medium text-gray-700 mb-2">
            Test User ID:
          </label>
          <input
            type="text"
            id="testUserId"
            value={testUserId}
            onChange={(e) => setTestUserId(e.target.value)}
            placeholder="Enter a user ID to test (e.g., cmfxq46ee003epp3k3fof2onx)"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <p className="text-xs text-gray-500 mt-1">
            ⚠️ This is a test page - enter a valid user ID to fetch identification data
          </p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="text-red-500 font-medium">❌ Error</div>
            <p className="text-red-600 mt-1">{error}</p>
          </div>
        )}

        {loading && testUserId && (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading identification data...</p>
            </div>
          </div>
        )}

        {identificationData && (
          <div className="space-y-6">
            <div className="mb-6">
              <h1 className="text-3xl font-bold text-gray-900">Identification Data Test</h1>
              <p className="mt-2 text-gray-600">Testing live database integration</p>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <h3 className="text-sm font-medium text-green-800 mb-2">✅ Data Loaded Successfully</h3>
              <p className="text-xs text-green-600">
                User: {identificationData.user.name} ({identificationData.user.id})
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Profile Information */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4">Profile Information</h3>
                <div className="space-y-2">
                  <p><strong>Title:</strong> {identificationData.profile?.title || 'N/A'}</p>
                  <p><strong>Peace Ambassador Number:</strong> {identificationData.profile?.peaceAmbassadorNumber || 'N/A'}</p>
                  <p><strong>Country:</strong> {identificationData.profile?.country || 'N/A'}</p>
                  <p><strong>City:</strong> {identificationData.profile?.city || 'N/A'}</p>
                </div>
              </div>

              {/* Identifications */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4">Identifications ({identificationData.identifications.length})</h3>
                {identificationData.identifications.length > 0 ? (
                  <div className="space-y-2">
                    {identificationData.identifications.map((id) => (
                      <div key={id.id} className="border rounded p-3">
                        <p><strong>{id.type}:</strong> {id.number}</p>
                        <p className="text-sm text-gray-500">Created: {new Date(id.createdAt).toLocaleDateString()}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No identifications found</p>
                )}
              </div>

              {/* Treaty Numbers */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4">Treaty Numbers ({identificationData.treatyNumbers.length})</h3>
                {identificationData.treatyNumbers.length > 0 ? (
                  <div className="space-y-2">
                    {identificationData.treatyNumbers.map((treaty) => (
                      <div key={treaty.id} className="border rounded p-3">
                        <p><strong>{treaty.treatyType}:</strong> {treaty.number}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No treaty numbers found</p>
                )}
              </div>

              {/* Positions */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4">Positions ({identificationData.positions.length})</h3>
                {identificationData.positions.length > 0 ? (
                  <div className="space-y-2">
                    {identificationData.positions.map((position) => (
                      <div key={position.id} className="border rounded p-3">
                        <p><strong>{position.title}</strong></p>
                        <p className="text-sm text-gray-500">Level: {position.level}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No positions found</p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}