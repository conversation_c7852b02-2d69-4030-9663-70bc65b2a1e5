// Shared type definitions for user treaty assignment
export interface UserTreatyAssignmentData {
  // Applicant Information
  fullLegalName?: string;
  dateOfBirth?: string;
  genderIdentity?: string;
  nationality?: string[];
  phoneNumbers?: Array<{ number: string; type: string; primary: boolean }>;
  email?: string;
  currentCountryId?: number;
  currentCityId?: number;
  identificationNumber?: string;
  photographPath?: string;

  // Residential Details
  residentialAddress?: string;
  residenceStatus?: string;
  residenceStatusOther?: string;
  residenceUseDesignation?: string;
  residenceCountryId?: number;
  residenceCityId?: number;
  peaceProtectedPremises?: boolean;

  // Business Details
  businessDetails?: any[];

  // Protection Items
  protectionItems?: any[];

  // Final Confirmation
  declarationAccepted?: boolean;
  signatureData?: string;
  signatureDate?: string;
  attachmentPaths?: string[];
}

// Legacy alias for backward compatibility
export type TreatyTypeDetailsData = UserTreatyAssignmentData;