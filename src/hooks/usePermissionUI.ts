'use client'

import { useMemo } from 'react'
import { usePermissions } from '@/components/providers/PermissionProvider'

export interface UIStateConfig {
  permissions?: string[]
  allPermissions?: string[]
  serverId?: string
  requireAuth?: boolean
}

export interface UIElementState {
  visible: boolean
  enabled: boolean
  loading: boolean
  disabledReason?: string
}

export interface FormFieldState {
  visible: boolean
  editable: boolean
  required: boolean
  disabledReason?: string
}

export interface ButtonState {
  visible: boolean
  enabled: boolean
  loading: boolean
  disabledReason?: string
}

export interface TabState {
  visible: boolean
  enabled: boolean
  disabledReason?: string
}

export interface MenuItemState {
  visible: boolean
  enabled: boolean
  disabledReason?: string
}

export function usePermissionUI() {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading, userId } = usePermissions()

  const checkUIAccess = (config: UIStateConfig): UIElementState => {
    const { permissions, allPermissions, serverId, requireAuth = true } = config

    // Check authentication requirement
    if (requireAuth && !userId) {
      return {
        visible: false,
        enabled: false,
        loading: false,
        disabledReason: 'Authentication required'
      }
    }

    // Check permissions
    let hasAccess = true
    let disabledReason: string | undefined

    if (permissions && permissions.length > 0) {
      // Convert string array to object array format expected by hasAnyPermission
      const permissionObjects = permissions.map(perm => {
        const [resource, action] = perm.split('.');
        return { resource, action };
      });
      hasAccess = hasAnyPermission(permissionObjects, serverId)
      if (!hasAccess) {
        disabledReason = 'Insufficient permissions'
      }
    }

    if (allPermissions && allPermissions.length > 0) {
      // Convert string array to object array format expected by hasAllPermissions
      const permissionObjects = allPermissions.map(perm => {
        const [resource, action] = perm.split('.');
        return { resource, action };
      });
      hasAccess = hasAccess && hasAllPermissions(permissionObjects, serverId)
      if (!hasAccess) {
        disabledReason = 'Missing required permissions'
      }
    }

    // If no specific permissions required, show content (but still respect auth requirement)
    if (!permissions && !allPermissions) {
      hasAccess = true
    }

    return {
      visible: hasAccess,
      enabled: hasAccess,
      loading: loading,
      disabledReason
    }
  }

  const getButtonState = (config: UIStateConfig): ButtonState => {
    const uiState = checkUIAccess(config)
    return {
      visible: uiState.visible,
      enabled: uiState.enabled && !uiState.loading,
      loading: uiState.loading,
      disabledReason: uiState.disabledReason
    }
  }

  const getFormFieldState = (config: UIStateConfig): FormFieldState => {
    const uiState = checkUIAccess(config)
    return {
      visible: uiState.visible,
      editable: uiState.enabled && !uiState.loading,
      required: uiState.enabled, // Fields are required if user has access
      disabledReason: uiState.disabledReason
    }
  }

  const getTabState = (config: UIStateConfig): TabState => {
    const uiState = checkUIAccess(config)
    return {
      visible: uiState.visible,
      enabled: uiState.enabled && !uiState.loading,
      disabledReason: uiState.disabledReason
    }
  }

  const getMenuItemState = (config: UIStateConfig): MenuItemState => {
    const uiState = checkUIAccess(config)
    return {
      visible: uiState.visible,
      enabled: uiState.enabled && !uiState.loading,
      disabledReason: uiState.disabledReason
    }
  }

  return {
    checkUIAccess,
    getButtonState,
    getFormFieldState,
    getTabState,
    getMenuItemState,
    loading
  }
}

// Convenience hooks for specific UI elements
export function useButtonPermission(config: UIStateConfig): ButtonState {
  const { getButtonState } = usePermissionUI()
  return getButtonState(config)
}

export function useFormFieldPermission(config: UIStateConfig): FormFieldState {
  const { getFormFieldState } = usePermissionUI()
  return getFormFieldState(config)
}

export function useTabPermission(config: UIStateConfig): TabState {
  const { getTabState } = usePermissionUI()
  return getTabState(config)
}

export function useMenuItemPermission(config: UIStateConfig): MenuItemState {
  const { getMenuItemState } = usePermissionUI()
  return getMenuItemState(config)
}

// Hook for managing multiple UI elements
export function useUIGroup(elements: Record<string, UIStateConfig>) {
  const { checkUIAccess, loading } = usePermissionUI()

  const states = useMemo(() => {
    const result: Record<string, UIElementState> = {}
    Object.entries(elements).forEach(([key, config]) => {
      result[key] = checkUIAccess(config)
    })
    return result
  }, [elements, checkUIAccess])

  return { states, loading }
}

// Hook for conditional rendering based on permissions
export function useConditionalRender(config: UIStateConfig) {
  const { checkUIAccess } = usePermissionUI()
  return checkUIAccess(config)
}

// Hook for managing form state based on permissions
export function usePermissionForm(initialFields: Record<string, any>) {
  const { getFormFieldState } = usePermissionUI()

  const fieldStates = useMemo(() => {
    const states: Record<string, FormFieldState> = {}
    Object.keys(initialFields).forEach(fieldName => {
      // Default config - can be customized per field
      states[fieldName] = getFormFieldState({
        permissions: [`forms.${fieldName}.edit`, 'admin.forms']
      })
    })
    return states
  }, [initialFields, getFormFieldState])

  return { fieldStates }
}