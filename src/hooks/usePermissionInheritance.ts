import { useState, useEffect } from 'react'

export interface UserServerPermissions {
  userId: string
  serverId: string
  serverName: string
  hasAccess: boolean
  permissions: {
    name: string
    source: 'role' | 'individual'
    roleId?: string
    roleName?: string
    grantedBy?: string
    grantedAt: Date
    expiresAt?: Date | null
  }[]
  roles: {
    roleId: string
    roleName: string
    autoGrantPermissions: string[]
  }[]
}

export interface PermissionValidationResult {
  valid: boolean
  invalidPermissions: string[]
  reason?: string
}

export interface EffectivePermissionsResult {
  current: string[]
  additional: string[]
  final: string[]
  duplicates: string[]
}

export function useUserServerPermissions(userId: string | null, serverId: string | null) {
  const [permissions, setPermissions] = useState<UserServerPermissions | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!userId || !serverId) {
      setPermissions(null)
      return
    }

    const fetchPermissions = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const response = await fetch(`/api/permissions/calculate?userId=${userId}&serverId=${serverId}`)
        
        if (response.ok) {
          const data = await response.json()
          setPermissions(data.data)
        } else {
          const errorData = await response.json()
          setError(errorData.error || 'Failed to load permissions')
        }
      } catch (err) {
        setError('Failed to load permissions')
        console.error('Error fetching permissions:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchPermissions()
  }, [userId, serverId])

  const refetch = () => {
    if (userId && serverId) {
      // Trigger re-fetch by updating dependencies
      setPermissions(null)
      setError(null)
    }
  }

  return { permissions, loading, error, refetch }
}

export function useAllUserServerPermissions(userId: string | null) {
  const [allPermissions, setAllPermissions] = useState<UserServerPermissions[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!userId) {
      setAllPermissions([])
      return
    }

    const fetchAllPermissions = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const response = await fetch(`/api/permissions/calculate?userId=${userId}`)
        
        if (response.ok) {
          const data = await response.json()
          setAllPermissions(data.data || [])
        } else {
          const errorData = await response.json()
          setError(errorData.error || 'Failed to load permissions')
        }
      } catch (err) {
        setError('Failed to load permissions')
        console.error('Error fetching all permissions:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchAllPermissions()
  }, [userId])

  return { allPermissions, loading, error }
}

export function usePermissionValidation() {
  const [validating, setValidating] = useState(false)

  const validatePermissions = async (
    serverId: string, 
    permissions: string[]
  ): Promise<PermissionValidationResult | null> => {
    setValidating(true)
    
    try {
      const response = await fetch('/api/permissions/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ serverId, permissions })
      })
      
      if (response.ok) {
        const data = await response.json()
        return data.data
      } else {
        console.error('Permission validation failed')
        return null
      }
    } catch (error) {
      console.error('Error validating permissions:', error)
      return null
    } finally {
      setValidating(false)
    }
  }

  return { validatePermissions, validating }
}

export function useEffectivePermissions() {
  const [calculating, setCalculating] = useState(false)

  const getEffectivePermissions = async (
    userId: string,
    serverId: string,
    additionalPermissions: string[]
  ): Promise<EffectivePermissionsResult | null> => {
    setCalculating(true)
    
    try {
      const response = await fetch('/api/permissions/calculate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          userId, 
          serverId, 
          action: 'getEffective',
          additionalPermissions 
        })
      })
      
      if (response.ok) {
        const data = await response.json()
        return data.data
      } else {
        console.error('Effective permissions calculation failed')
        return null
      }
    } catch (error) {
      console.error('Error calculating effective permissions:', error)
      return null
    } finally {
      setCalculating(false)
    }
  }

  return { getEffectivePermissions, calculating }
}

export function usePermissionSync() {
  const [syncing, setSyncing] = useState(false)
  const [lastSync, setLastSync] = useState<Record<string, Date>>({})

  const syncServerPermissions = async (serverId: string) => {
    setSyncing(true)
    
    try {
      const response = await fetch(`/api/permissions/sync/${serverId}`, {
        method: 'POST'
      })
      
      if (response.ok) {
        const data = await response.json()
        setLastSync(prev => ({ ...prev, [serverId]: new Date() }))
        return data
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Sync failed')
      }
    } catch (error) {
      console.error('Error syncing permissions:', error)
      throw error
    } finally {
      setSyncing(false)
    }
  }

  return { syncServerPermissions, syncing, lastSync }
}