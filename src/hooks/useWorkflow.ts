import { useState, useCallback } from 'react';
import { ApplicationStatus, PaymentStatus } from '@prisma/client';
import { ApplicationStateMachine, PaymentStateMachine } from '@/lib/workflow/StateMachine';

/**
 * Hook for managing application workflow state in React components
 */
export function useApplicationWorkflow(initialStatus: ApplicationStatus) {
  const [currentStatus, setCurrentStatus] = useState<ApplicationStatus>(initialStatus);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getValidTransitions = useCallback(() => {
    return ApplicationStateMachine.getValidTransitions(currentStatus);
  }, [currentStatus]);

  const canTransitionTo = useCallback((targetStatus: ApplicationStatus) => {
    const validation = ApplicationStateMachine.validateApplicationTransition(
      currentStatus,
      targetStatus
    );
    return validation.valid;
  }, [currentStatus]);

  const getStatusDescription = useCallback(() => {
    return ApplicationStateMachine.getStatusDescription(currentStatus);
  }, [currentStatus]);

  const getNextExpectedStatuses = useCallback(() => {
    return ApplicationStateMachine.getNextExpectedStatus(currentStatus);
  }, [currentStatus]);

  const isTerminalState = useCallback(() => {
    return ApplicationStateMachine.isTerminalState(currentStatus);
  }, [currentStatus]);

  const getStatusColor = useCallback(() => {
    switch (currentStatus) {
      case ApplicationStatus.APPLIED:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case ApplicationStatus.UNDER_REVIEW:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case ApplicationStatus.APPROVED:
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case ApplicationStatus.ACTIVE:
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case ApplicationStatus.REJECTED:
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  }, [currentStatus]);

  const getStatusIcon = useCallback(() => {
    switch (currentStatus) {
      case ApplicationStatus.APPLIED:
        return '📝';
      case ApplicationStatus.UNDER_REVIEW:
        return '🔍';
      case ApplicationStatus.APPROVED:
        return '✅';
      case ApplicationStatus.ACTIVE:
        return '🎯';
      case ApplicationStatus.REJECTED:
        return '❌';
      default:
        return '❓';
    }
  }, [currentStatus]);

  const transitionTo = useCallback(async (targetStatus: ApplicationStatus, context?: any) => {
    if (!canTransitionTo(targetStatus)) {
      setError(`Cannot transition from ${currentStatus} to ${targetStatus}`);
      return false;
    }

    setIsTransitioning(true);
    setError(null);

    try {
      // This would typically make an API call
      // For now, we'll just update the local state
      setCurrentStatus(targetStatus);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Transition failed');
      return false;
    } finally {
      setIsTransitioning(false);
    }
  }, [currentStatus, canTransitionTo]);

  return {
    currentStatus,
    setCurrentStatus,
    isTransitioning,
    error,
    getValidTransitions,
    canTransitionTo,
    getStatusDescription,
    getNextExpectedStatuses,
    isTerminalState,
    getStatusColor,
    getStatusIcon,
    transitionTo,
  };
}

/**
 * Hook for managing payment workflow state in React components
 */
export function usePaymentWorkflow(initialStatus: PaymentStatus) {
  const [currentStatus, setCurrentStatus] = useState<PaymentStatus>(initialStatus);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getValidTransitions = useCallback(() => {
    // For now, return basic transitions - this could be enhanced
    const transitions: Record<PaymentStatus, PaymentStatus[]> = {
      [PaymentStatus.NOT_REQUIRED]: [],
      [PaymentStatus.AWAITING_PAYMENT]: [PaymentStatus.PENDING],
      [PaymentStatus.PENDING]: [PaymentStatus.PAID, PaymentStatus.FAILED],
      [PaymentStatus.PAID]: [PaymentStatus.REFUNDED],
      [PaymentStatus.FAILED]: [PaymentStatus.PENDING],
      [PaymentStatus.REFUNDED]: [],
    };
    return transitions[currentStatus] || [];
  }, [currentStatus]);

  const canTransitionTo = useCallback((targetStatus: PaymentStatus) => {
    const validTransitions = getValidTransitions();
    return validTransitions.includes(targetStatus);
  }, [currentStatus, getValidTransitions]);

  const getStatusDescription = useCallback(() => {
    return PaymentStateMachine.getPaymentStatusDescription(currentStatus);
  }, [currentStatus]);

  const getStatusColor = useCallback(() => {
    switch (currentStatus) {
      case PaymentStatus.NOT_REQUIRED:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      case PaymentStatus.AWAITING_PAYMENT:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case PaymentStatus.PENDING:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case PaymentStatus.PAID:
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case PaymentStatus.FAILED:
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case PaymentStatus.REFUNDED:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  }, [currentStatus]);

  const getStatusIcon = useCallback(() => {
    switch (currentStatus) {
      case PaymentStatus.NOT_REQUIRED:
        return '💳';
      case PaymentStatus.AWAITING_PAYMENT:
        return '⏳';
      case PaymentStatus.PENDING:
        return '📤';
      case PaymentStatus.PAID:
        return '✅';
      case PaymentStatus.FAILED:
        return '❌';
      case PaymentStatus.REFUNDED:
        return '💰';
      default:
        return '❓';
    }
  }, [currentStatus]);

  const transitionTo = useCallback(async (targetStatus: PaymentStatus, context?: any) => {
    if (!canTransitionTo(targetStatus)) {
      setError(`Cannot transition from ${currentStatus} to ${targetStatus}`);
      return false;
    }

    setIsTransitioning(true);
    setError(null);

    try {
      // This would typically make an API call
      setCurrentStatus(targetStatus);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Transition failed');
      return false;
    } finally {
      setIsTransitioning(false);
    }
  }, [currentStatus, canTransitionTo]);

  return {
    currentStatus,
    setCurrentStatus,
    isTransitioning,
    error,
    getValidTransitions,
    canTransitionTo,
    getStatusDescription,
    getStatusColor,
    getStatusIcon,
    transitionTo,
  };
}

/**
 * Combined hook for application and payment workflow
 */
export function useTreatyWorkflow(
  initialApplicationStatus: ApplicationStatus,
  initialPaymentStatus: PaymentStatus | null
) {
  const applicationWorkflow = useApplicationWorkflow(initialApplicationStatus);
  const paymentWorkflow = usePaymentWorkflow(initialPaymentStatus || PaymentStatus.NOT_REQUIRED);

  const canSubmitPayment = useCallback(() => {
    return (
      applicationWorkflow.currentStatus === ApplicationStatus.APPROVED &&
      paymentWorkflow.currentStatus === PaymentStatus.AWAITING_PAYMENT
    );
  }, [applicationWorkflow.currentStatus, paymentWorkflow.currentStatus]);

  const canViewDetails = useCallback(() => {
    return true; // Always can view details
  }, []);

  const canCancel = useCallback(() => {
    return ([
      ApplicationStatus.APPLIED,
      ApplicationStatus.UNDER_REVIEW,
      ApplicationStatus.REJECTED,
    ] as ApplicationStatus[]).includes(applicationWorkflow.currentStatus);
  }, [applicationWorkflow.currentStatus]);

  const canResubmit = useCallback(() => {
    return applicationWorkflow.currentStatus === ApplicationStatus.REJECTED;
  }, [applicationWorkflow.currentStatus]);

  const getWorkflowProgress = useCallback(() => {
    const steps = [
      { status: ApplicationStatus.APPLIED, label: 'Applied' },
      { status: ApplicationStatus.UNDER_REVIEW, label: 'Under Review' },
      { status: ApplicationStatus.APPROVED, label: 'Approved' },
      { status: ApplicationStatus.ACTIVE, label: 'Active' },
    ];

    const currentStepIndex = steps.findIndex(step => step.status === applicationWorkflow.currentStatus);
    
    return {
      steps,
      currentStepIndex: Math.max(0, currentStepIndex),
      isComplete: applicationWorkflow.currentStatus === ApplicationStatus.ACTIVE,
    };
  }, [applicationWorkflow.currentStatus]);

  return {
    application: applicationWorkflow,
    payment: paymentWorkflow,
    canSubmitPayment,
    canViewDetails,
    canCancel,
    canResubmit,
    getWorkflowProgress,
  };
}