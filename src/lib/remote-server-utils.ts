/**
 * Utility functions for handling remote server URLs and OAuth configurations
 */

/**
 * Get the API endpoint for a remote server
 * @param remoteServer - The remote server object
 * @returns The API endpoint URL
 */
export function getApiEndpoint(remoteServer: any): string {
  // If specific API endpoint is configured, use it
  if (remoteServer.apiEndpoint) {
    return remoteServer.apiEndpoint;
  }
  // Otherwise fall back to main URL (current behavior)
  return remoteServer.url;
}

/**
 * Get the OAuth redirect URI for a remote server based on environment
 * @param remoteServer - The remote server object
 * @param isDevelopment - Whether we're in development environment
 * @returns The OAuth redirect URI
 */
export function getOAuthRedirectUri(remoteServer: any, isDevelopment: boolean): string {
  // If in development and dev redirects configured
  if (isDevelopment && remoteServer.developmentRedirectUris?.length > 0) {
    return remoteServer.developmentRedirectUris[0];
  }
  // If production redirects configured
  if (remoteServer.oauthRedirectUris?.length > 0) {
    return remoteServer.oauthRedirectUris[0];
  }
  // If legacy redirect URIs configured
  if (remoteServer.redirectUris?.length > 0) {
    return remoteServer.redirectUris[0];
  }
  // Otherwise fall back to main URL (current behavior)
  return remoteServer.url;
}

/**
 * Get all allowed redirect URIs for a remote server
 * @param remoteServer - The remote server object
 * @param isDevelopment - Whether we're in development environment
 * @returns Array of allowed redirect URIs
 */
export function getAllowedRedirectUris(remoteServer: any, isDevelopment: boolean): string[] {
  // If in development and dev redirects configured
  if (isDevelopment && remoteServer.developmentRedirectUris?.length > 0) {
    return remoteServer.developmentRedirectUris;
  }
  // If production redirects configured
  if (remoteServer.oauthRedirectUris?.length > 0) {
    return remoteServer.oauthRedirectUris;
  }
  // If legacy redirect URIs configured
  if (remoteServer.redirectUris?.length > 0) {
    return remoteServer.redirectUris;
  }
  // Otherwise fall back to main URL (current behavior)
  return [remoteServer.url];
}

/**
 * Check if a redirect URI is allowed for a remote server
 * @param remoteServer - The remote server object
 * @param redirectUri - The redirect URI to check
 * @param isDevelopment - Whether we're in development environment
 * @returns Whether the redirect URI is allowed
 */
export function isRedirectUriAllowed(remoteServer: any, redirectUri: string, isDevelopment: boolean): boolean {
  const allowedUris = getAllowedRedirectUris(remoteServer, isDevelopment);
  return allowedUris.includes(redirectUri);
}