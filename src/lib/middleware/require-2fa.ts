import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { prisma } from '@/lib/prisma';

/**
 * Middleware to enforce 2FA requirements
 * Redirects users to 2FA setup if required but not enabled
 */
export async function require2FAMiddleware(request: NextRequest) {
  const { pathname } = new URL(request.url);
  
  // Skip 2FA check for public endpoints and 2FA-related endpoints
  const skip2FACheck = [
    '/api/auth',
    '/api/user/2fa',
    '/login',
    '/setup-2fa',
    '/_next',
    '/favicon.ico'
  ].some(path => pathname.startsWith(path));
  
  if (skip2FACheck) {
    return NextResponse.next();
  }
  
  try {
    // Resolve user identity from the NextAuth JWT (default session strategy)
    const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });
    let userId = typeof token?.id === 'string' ? token.id : undefined;

    // Fallback to legacy session lookup if the app ever switches back to database sessions
    if (!userId) {
      const sessionToken = request.cookies.get('next-auth.session-token')?.value ||
                           request.cookies.get('__Secure-next-auth.session-token')?.value;

      if (sessionToken) {
        const session = await prisma.session.findUnique({
          where: { sessionToken },
          select: { userId: true },
        });

        userId = session?.userId ?? undefined;
      }
    }

    if (!userId) {
      return NextResponse.next();
    }

    // Check if 2FA is required system-wide
    const systemSetting = await prisma.systemSetting.findFirst({
      select: { twoFactorAuthEnabled: true },
    });

    const twoFactorAuthEnabled = !!systemSetting?.twoFactorAuthEnabled;

    if (!twoFactorAuthEnabled) {
      return NextResponse.next();
    }

    // Check if user has 2FA enabled
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { twoFactorEnabled: true },
    });

    if (twoFactorAuthEnabled && !user?.twoFactorEnabled && !pathname.startsWith('/setup-2fa')) {
      return NextResponse.redirect(new URL('/setup-2fa', request.url));
    }

    return NextResponse.next();
  } catch (error) {
    console.error('2FA middleware error:', error);
    return NextResponse.next();
  }
}

/**
 * Higher-order function to wrap handlers with 2FA enforcement
 */
export function with2FAEnforcement(handler: (request: NextRequest) => Promise<NextResponse>) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const response = await require2FAMiddleware(request);
    
    // If middleware returned a redirect, return it
    if (response.status === 307 || response.status === 308) {
      return response;
    }
    
    // Continue with the original handler
    return handler(request);
  };
}
