import { NextRequest, NextResponse } from 'next/server';
import { securityHeadersMiddleware, developmentSecurityHeadersMiddleware, productionSecurityHeadersMiddleware } from './security-headers';
import { createRateLimitingMiddleware, predefinedRateLimiters } from './rate-limiting';

/**
 * Public endpoints that should not require authentication
 */
const PUBLIC_ENDPOINTS = [
  '/api/auth/health',
  '/api/auth/signup',
  '/api/auth/verify-email',
  '/api/auth/reset-password',
  '/api/oauth/authorize',
  '/api/oauth/token',
  '/api/oauth/userinfo',
  '/api/info',
];

/**
 * Endpoints that require special rate limiting
 */
const RATE_LIMITED_ENDPOINTS: Record<string, any> = {
  '/api/auth/signup': predefinedRateLimiters.signup,
  '/api/auth/verify-email': predefinedRateLimiters.auth,
  '/api/auth/reset-password': predefinedRateLimiters.passwordReset,
  '/api/oauth/authorize': predefinedRateLimiters.auth,
  '/api/oauth/token': predefinedRateLimiters.auth,
  '/api/user/password': predefinedRateLimiters.auth,
  '/api/user/email': predefinedRateLimiters.auth,
  '/api/user/2fa': predefinedRateLimiters.auth,
};

/**
 * Update session tracking information for authenticated requests
 * Only runs in Node.js runtime, not Edge Runtime
 */
async function updateSessionTracking(request: NextRequest) {
  try {
    // Check if we're in Edge Runtime or browser environment - if so, skip session tracking
    // Edge Runtime doesn't have access to Node.js APIs like process
    if (typeof process === 'undefined' || typeof window !== 'undefined') {
      return;
    }

    // Additional check: ensure we're in a Node.js environment
    if (process.env.NEXT_RUNTIME !== 'nodejs') {
      return;
    }

    // Import Prisma only in Node.js runtime to avoid Edge Runtime warnings
    let prisma: any = null;
    try {
      const prismaModule = await import('@/lib/prisma');
      prisma = prismaModule.prisma;
    } catch (error) {
      // If Prisma import fails (e.g., in browser environment), skip session tracking
      console.warn('Prisma import failed in session tracking, skipping:', error);
      return;
    }

    if (!prisma) {
      return;
    }

    // Get the session token from cookies
    const sessionToken = request.cookies.get('next-auth.session-token')?.value ||
                         request.cookies.get('__Secure-next-auth.session-token')?.value;

    if (!sessionToken) return;

    // Extract IP address and user agent
    const ipAddress = request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
                      request.headers.get('x-real-ip') ||
                      'unknown';

    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Update session with IP and user agent information
    await prisma.session.updateMany({
      where: {
        sessionToken: sessionToken,
        expires: {
          gt: new Date()
        }
      },
      data: {
        ipAddress: ipAddress,
        userAgent: userAgent,
        lastActive: new Date()
      }
    });
  } catch (error) {
    // Don't fail the request if session tracking fails
    console.error('Error updating session tracking:', error);
  }
}

/**
 * Global security middleware that applies to all requests
 */
export async function globalSecurityMiddleware(request: NextRequest) {
  const { pathname } = new URL(request.url);

  // Apply security headers based on environment
  const securityHeaders = process.env.NODE_ENV === 'production'
    ? productionSecurityHeadersMiddleware()
    : developmentSecurityHeadersMiddleware();

  let response = securityHeaders(request);

  // Apply rate limiting for specific endpoints
  const rateLimiter = RATE_LIMITED_ENDPOINTS[pathname];
  if (rateLimiter) {
    const rateLimitResult = await rateLimiter(request);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          error: 'Too Many Requests',
          message: 'Rate limit exceeded',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: rateLimitResult.headers,
        }
      );
    }
  }

  // Update session tracking for authenticated requests
  // Only do this for API routes and dashboard pages (not static assets)
  if (pathname.startsWith('/api/') || pathname.startsWith('/dashboard') || pathname.startsWith('/admin') || pathname.startsWith('/settings')) {
    await updateSessionTracking(request);
  }

  // Audit logging is disabled in Edge Runtime due to Prisma compatibility issues
  // For production environments, consider using a different logging solution
  // that works with Edge Runtime, such as logging to an external service

  return response;
}

/**
 * Middleware to check if endpoint should be publicly accessible
 */
export function isPublicEndpoint(pathname: string): boolean {
  return PUBLIC_ENDPOINTS.some(endpoint => pathname.startsWith(endpoint));
}

/**
 * Middleware to check if endpoint requires authentication
 */
export function requiresAuthentication(pathname: string): boolean {
  return !isPublicEndpoint(pathname) && pathname.startsWith('/api/');
}

/**
 * Apply global security to all API routes
 */
export function withGlobalSecurity(handler: (request: NextRequest) => Promise<NextResponse>) {
  return async (request: NextRequest): Promise<NextResponse> => {
    // Apply global security middleware
    const securityResponse = await globalSecurityMiddleware(request);
    
    // If security middleware returned a response (like rate limit), return it
    if (securityResponse.status !== 200) {
      return securityResponse;
    }
    
    // Continue with the original handler
    const response = await handler(request);
    
    // Merge security headers with handler response
    securityResponse.headers.forEach((value, key) => {
      response.headers.set(key, value);
    });
    
    return response;
  };
}