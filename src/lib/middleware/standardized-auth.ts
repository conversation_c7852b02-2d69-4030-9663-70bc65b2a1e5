import { NextRequest, NextResponse } from 'next/server';
import { predefinedRateLimiters } from './rate-limiting';
import { withAuthHandler, RequireAuthOptions, AuthContext } from './require-auth';

export interface StandardizedHandlerOptions {
  auth?: RequireAuthOptions;
  rateLimit?: boolean;
  auditLogging?: boolean;
}

export interface StandardizedHandlerContext {
  auth: AuthContext;
  request: NextRequest;
}

/**
 * Creates a standardized API handler with built-in authentication, rate limiting, and error handling
 */
export function createStandardizedHandler<
  T extends (context: StandardizedHandlerContext, ...args: any[]) => Promise<NextResponse>
>(
  handler: T,
  options: StandardizedHandlerOptions = {}
) {
  const {
    auth = {},
    rateLimit = true,
    auditLogging = true
  } = options;

  return withAuthHandler(async (request: NextRequest, authContext: AuthContext) => {
    try {
      // Apply rate limiting if enabled
      if (rateLimit) {
        const rateLimitResult = await predefinedRateLimiters.api(request);
        if (!rateLimitResult.allowed) {
          return NextResponse.json(
            { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
            { 
              status: 429,
              headers: rateLimitResult.headers
            }
          );
        }
      }

      // Create handler context
      const context: StandardizedHandlerContext = {
        auth: authContext,
        request
      };

      // Execute the handler
      return await handler(context);

    } catch (error) {
      console.error('Error in standardized handler:', error);
      
      // Handle known error types
      if (error instanceof Error && error.name === 'PrismaClientKnownRequestError') {
        return NextResponse.json(
          { error: 'Database Error', message: 'A database operation failed' },
          { status: 500 }
        );
      }

      // Handle validation errors
      if (error instanceof Error && error.name === 'ZodError') {
        return NextResponse.json(
          { error: 'Validation Error', message: 'Invalid input data', details: (error as any).errors },
          { status: 400 }
        );
      }

      // Generic error handling
      return NextResponse.json(
        { error: 'Internal Server Error', message: 'An unexpected error occurred' },
        { status: 500 }
      );
    }
  }, { ...auth, auditLogging });
}

// Pre-configured handlers for common scenarios

export const withAdminHandler = <
  T extends (context: StandardizedHandlerContext, ...args: any[]) => Promise<NextResponse>
>(handler: T, options: StandardizedHandlerOptions = {}) => {
  return createStandardizedHandler(handler, {
    ...options,
    auth: { requireAdmin: true, ...options.auth }
  });
};

export const withRoleHandler = <
  T extends (context: StandardizedHandlerContext, ...args: any[]) => Promise<NextResponse>
>(handler: T, role: string | string[], options: StandardizedHandlerOptions = {}) => {
  return createStandardizedHandler(handler, {
    ...options,
    auth: { 
      requireRoles: Array.isArray(role) ? role : [role],
      ...options.auth 
    }
  });
};

export const withPublicHandler = <
  T extends (context: StandardizedHandlerContext, ...args: any[]) => Promise<NextResponse>
>(handler: T, options: Omit<StandardizedHandlerOptions, 'auth'> = {}) => {
  return createStandardizedHandler(handler, {
    ...options,
    auth: { auditLogging: false }
  });
};

// Utility functions for common response patterns
export const createJsonResponse = (data: any, status: number = 200) => {
  return NextResponse.json(data, { status });
};

export const createErrorResponse = (message: string, status: number = 400, details?: any) => {
  return NextResponse.json(
    { error: message, ...(details && { details }) },
    { status }
  );
};

export const createSuccessResponse = (message: string, data?: any) => {
  return NextResponse.json(
    { success: true, message, ...(data && { data }) },
    { status: 200 }
  );
};