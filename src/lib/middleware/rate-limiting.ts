import { NextRequest, NextResponse } from 'next/server';

// Simple in-memory rate limiting store for development/fallback
class MemoryRateLimiter {
  private store: Map<string, { count: number; resetTime: number }> = new Map();
  
  async consume(key: string, points: number, duration: number): Promise<any> {
    const now = Date.now();
    const record = this.store.get(key);
    
    if (!record || now >= record.resetTime) {
      // New or expired record
      this.store.set(key, {
        count: points,
        resetTime: now + duration * 1000
      });
      return { remainingPoints: points - 1 };
    }
    
    if (record.count <= 0) {
      // Rate limit exceeded
      throw { msBeforeNext: record.resetTime - now };
    }
    
    // Decrement count
    record.count--;
    return { remainingPoints: record.count };
  }
}

// Create in-memory rate limiter for development
const memoryLimiter = new MemoryRateLimiter();

interface RateLimitConfig {
  windowMs?: number;
  maxRequests?: number;
  keyGenerator?: (req: NextRequest) => string;
}

interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter: number;
  headers?: Headers;
}

/**
 * Create rate limiting middleware for API routes
 */
export function createRateLimitingMiddleware(config: RateLimitConfig = {}) {
  const { windowMs = 60 * 1000, maxRequests = 100, keyGenerator } = config;
  
  return async function rateLimitingMiddleware(req: NextRequest) {
    try {
      // Generate key for rate limiting
      const key = keyGenerator 
        ? keyGenerator(req) 
        : `${req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'}:${req.url}`;
      
      try {
        const rateLimitRes = await memoryLimiter.consume(key, maxRequests, Math.floor(windowMs / 1000));
        
        // Add rate limit headers to response
        const headers = new Headers();
        headers.set('X-RateLimit-Limit', maxRequests.toString());
        headers.set('X-RateLimit-Remaining', rateLimitRes.remainingPoints.toString());
        headers.set('X-RateLimit-Reset', new Date(Date.now() + windowMs).toISOString());
        
        return {
          allowed: true,
          limit: maxRequests,
          remaining: rateLimitRes.remainingPoints,
          resetTime: Date.now() + windowMs,
          retryAfter: 0,
          headers,
        };
      } catch (rateLimiterRes: any) {
        if (rateLimiterRes instanceof Error) {
          console.error('Rate limiter error:', rateLimiterRes);
          // Allow request if rate limiter fails
          return {
            allowed: true,
            limit: maxRequests,
            remaining: maxRequests,
            resetTime: Date.now() + windowMs,
            retryAfter: 0,
          };
        }
        
        // Rate limit exceeded
        const headers = new Headers();
        headers.set('X-RateLimit-Limit', maxRequests.toString());
        headers.set('X-RateLimit-Remaining', '0');
        headers.set('X-RateLimit-Reset', new Date(Date.now() + rateLimiterRes.msBeforeNext).toISOString());
        headers.set('Retry-After', Math.floor(rateLimiterRes.msBeforeNext / 1000).toString());
        
        return { 
          allowed: false,
          limit: maxRequests,
          remaining: 0,
          resetTime: Date.now() + rateLimiterRes.msBeforeNext,
          retryAfter: Math.floor(rateLimiterRes.msBeforeNext / 1000),
          headers,
        };
      }
    } catch (error) {
      console.error('Rate limiting error:', error);
      // Allow request if rate limiting fails
      return {
        allowed: true,
        limit: maxRequests,
        remaining: maxRequests,
        resetTime: Date.now() + windowMs,
        retryAfter: 0,
      };
    }
  };
}

/**
 * Check rate limit for a request
 */
export async function checkRateLimit(
  req: NextRequest, 
  config: RateLimitConfig = {}
): Promise<RateLimitResult> {
  try {
    const rateLimiterMiddleware = createRateLimitingMiddleware(config);
    const result = await rateLimiterMiddleware(req);
    return result;
  } catch (error) {
    console.error('Rate limiting check error:', error);
    // Allow request if rate limiting check fails
    const { windowMs = 60 * 1000, maxRequests = 100 } = config;
    return {
      allowed: true,
      limit: maxRequests,
      remaining: maxRequests,
      resetTime: Date.now() + windowMs,
      retryAfter: 0,
    };
  }
}

/**
 * Predefined rate limiters for common use cases
 */
export const predefinedRateLimiters = {
  // Strict rate limiting for authentication endpoints
  auth: createRateLimitingMiddleware({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 requests per minute
  }),
  
  // Standard rate limiting for API endpoints
  api: createRateLimitingMiddleware({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
  }),
  
  // Very strict rate limiting for sensitive endpoints
  strict: createRateLimitingMiddleware({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // 5 requests per minute
  }),
  
  // Rate limiting for signup endpoints
  signup: createRateLimitingMiddleware({
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 5, // 5 requests per 5 minutes
  }),
  
  // Rate limiting for password reset endpoints
  passwordReset: createRateLimitingMiddleware({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3, // 3 requests per hour
  }),
};