import { NextRequest, NextResponse } from 'next/server';
import { auditLoggingMiddleware } from './audit-logging';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export interface AuthContext {
  userId: string;
  email: string;
  name: string;
  roles: string[];
  permissions: string[];
}

export interface RequireAuthOptions {
  requireRoles?: string[];
  requirePermissions?: string[];
  requireAdmin?: boolean;
  auditLogging?: boolean;
  customErrorMessage?: string;
}

export class AuthenticationError extends Error {
  statusCode: number;
  
  constructor(message: string, statusCode: number = 401) {
    super(message);
    this.statusCode = statusCode;
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends Error {
  statusCode: number;
  
  constructor(message: string, statusCode: number = 403) {
    super(message);
    this.statusCode = statusCode;
    this.name = 'AuthorizationError';
  }
}

/**
 * Middleware to require authentication for API endpoints
 * Throws AuthenticationError if user is not authenticated
 */
export async function requireAuth(request: NextRequest, options: RequireAuthOptions = {}): Promise<AuthContext> {
  const {
    requireRoles = [],
    requirePermissions = [],
    requireAdmin = false,
    auditLogging = false, // Disable audit logging temporarily
    customErrorMessage
  } = options;

  try {
    // Get server session
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      if (auditLogging) {
        await auditLoggingMiddleware.logAuthenticationEvent(
          'jwt_verification',
          false,
          request,
          { errorMessage: 'No session found' }
        );
      }
      
      throw new AuthenticationError(customErrorMessage || 'Authentication required');
    }

    // Extract user information from session
    const user = session.user as any;
    const userId = user.id;
    const email = user.email;
    const name = user.name;
    
    // Fetch complete user data with roles and permissions from database
    const userWithRoles = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: {
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!userWithRoles) {
      throw new AuthenticationError('User not found in database');
    }

    // Extract roles and permissions from database
    const roles = userWithRoles.userRoles.map(ur => ur.role.name);
    const permissions = userWithRoles.userRoles.flatMap(ur => 
      ur.role.rolePermissions.map(rp => rp.permission.name)
    );

    // Check if admin role is required
    if (requireAdmin && !roles.includes('admin')) {
      if (auditLogging) {
        await auditLoggingMiddleware.logApiAccess(
          userId,
          'require_admin',
          'authorization',
          false,
          request,
          { metadata: { requiredRole: 'admin', userRoles: roles } }
        );
      }
      
      throw new AuthorizationError(customErrorMessage || 'Admin access required');
    }

    // Check required roles
    if (requireRoles.length > 0) {
      const hasRequiredRole = requireRoles.some(role => roles.includes(role));
      if (!hasRequiredRole) {
        if (auditLogging) {
          await auditLoggingMiddleware.logApiAccess(
            userId,
            'require_role',
            'authorization',
            false,
            request,
            { metadata: { requiredRoles: requireRoles, userRoles: roles } }
          );
        }
        
        throw new AuthorizationError(
          customErrorMessage || `Required roles: ${requireRoles.join(', ')}`
        );
      }
    }

    // Check required permissions
    if (requirePermissions.length > 0) {
      const hasRequiredPermission = requirePermissions.some(permission => 
        permissions.includes(permission)
      );
      if (!hasRequiredPermission) {
        if (auditLogging) {
          await auditLoggingMiddleware.logApiAccess(
            userId,
            'require_permission',
            'authorization',
            false,
            request,
            { metadata: { requiredPermissions: requirePermissions, userPermissions: permissions } }
          );
        }
        
        throw new AuthorizationError(
          customErrorMessage || `Required permissions: ${requirePermissions.join(', ')}`
        );
      }
    }

    // Log successful authentication
    if (auditLogging) {
      await auditLoggingMiddleware.logAuthenticationEvent(
        'jwt_verification',
        true,
        request,
        { userId, metadata: { roles, permissions } }
      );
    }

    return {
      userId,
      email,
      name,
      roles,
      permissions
    };
  } catch (error) {
    if (error instanceof AuthenticationError || error instanceof AuthorizationError) {
      throw error;
    }
    
    // Log unexpected errors
    if (auditLogging) {
      await auditLoggingMiddleware.logAuthenticationEvent(
        'jwt_verification',
        false,
        request,
        { errorMessage: error instanceof Error ? error.message : 'Unknown error' }
      );
    }
    
    throw new AuthenticationError('Authentication failed');
  }
}

/**
 * Higher-order function to create authenticated route handlers
 */
export function withAuth(
  handler: (request: NextRequest, authContext: AuthContext) => Promise<NextResponse>,
  options: RequireAuthOptions = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      const authContext = await requireAuth(request, options);
      return await handler(request, authContext);
    } catch (error) {
      if (error instanceof AuthenticationError) {
        return NextResponse.json(
          { error: 'Unauthorized', message: error.message },
          { status: error.statusCode }
        );
      }
      
      if (error instanceof AuthorizationError) {
        return NextResponse.json(
          { error: 'Forbidden', message: error.message },
          { status: error.statusCode }
        );
      }
      
      console.error('Unexpected auth error:', error);
      return NextResponse.json(
        { error: 'Internal Server Error', message: 'Authentication failed' },
        { status: 500 }
      );
    }
  };
}

/**
 * Middleware factory for requiring specific roles
 */
export function requireRole(role: string | string[], options: Omit<RequireAuthOptions, 'requireRoles'> = {}) {
  const roles = Array.isArray(role) ? role : [role];
  return (request: NextRequest) => requireAuth(request, { ...options, requireRoles: roles });
}

/**
 * Middleware factory for requiring specific permissions
 */
export function requirePermission(permission: string | string[], options: Omit<RequireAuthOptions, 'requirePermissions'> = {}) {
  const permissions = Array.isArray(permission) ? permission : [permission];
  return (request: NextRequest) => requireAuth(request, { ...options, requirePermissions: permissions });
}

/**
 * Middleware factory for requiring admin access
 */
export function requireAdmin(options: Omit<RequireAuthOptions, 'requireAdmin'> = {}) {
  return (request: NextRequest) => requireAuth(request, { ...options, requireAdmin: true });
}

// Simplified wrapper functions for common use cases
export const withAuthHandler = (
  handler: (request: NextRequest, auth: AuthContext) => Promise<NextResponse>,
  options: RequireAuthOptions = {}
) => {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      const authContext = await requireAuth(request, options);
      return await handler(request, authContext);
    } catch (error) {
      if (error instanceof AuthenticationError) {
        return NextResponse.json(
          { error: 'Unauthorized', message: error.message },
          { status: error.statusCode }
        );
      }
      
      if (error instanceof AuthorizationError) {
        return NextResponse.json(
          { error: 'Forbidden', message: error.message },
          { status: error.statusCode }
        );
      }
      
      console.error('Unexpected auth error:', error);
      return NextResponse.json(
        { error: 'Internal Server Error', message: 'Authentication failed' },
        { status: 500 }
      );
    }
  };
};

// Pre-configured middleware for common scenarios
export const requireAdminAuth = (options: Omit<RequireAuthOptions, 'requireAdmin'> = {}) => 
  withAuthHandler(async (request, auth) => NextResponse.next(), { ...options, requireAdmin: true });

export const requireRoleAuth = (role: string | string[], options: Omit<RequireAuthOptions, 'requireRoles'> = {}) =>
  withAuthHandler(async (request, auth) => NextResponse.next(), { 
    ...options, 
    requireRoles: Array.isArray(role) ? role : [role] 
  });

// Export types
// AuthContext is already exported above