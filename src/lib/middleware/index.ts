import { NextRequest, NextResponse } from 'next/server';
import { apiKeyValidationMiddleware } from './api-key-validation';
import { jwtVerificationMiddleware } from './jwt-verification';
import { corsValidationMiddleware } from './cors-validation';
import { scopeAuthorizationMiddleware } from './scope-authorization';
import { createRateLimitingMiddleware, checkRateLimit, predefinedRateLimiters } from './rate-limiting';
import { auditLoggingMiddleware } from './audit-logging';
import { requireAuth, withAuth, requireRole, requirePermission, requireAdmin, AuthContext } from './require-auth';
import { securityHeadersMiddleware, applySecurityHeaders, withSecurityHeaders, developmentSecurityHeadersMiddleware, productionSecurityHeadersMiddleware } from './security-headers';
import { globalSecurityMiddleware, withGlobalSecurity, isPublicEndpoint, requiresAuthentication } from './global-security';

export interface AuthenticationChainOptions {
  requireApiKey?: boolean;
  requireJwt?: boolean;
  requireCors?: boolean;
  requiredScopes?: string[];
  rateLimitConfig?: Parameters<typeof createRateLimitingMiddleware>[0];
  auditLogging?: boolean;
  onError?: (error: string, step: string, request: NextRequest) => NextResponse;
  onSuccess?: (context: AuthenticationContext, request: NextRequest) => void;
}

export interface AuthenticationContext {
  projectId: string;
  projectName?: string;
  userId?: string;
  scopes: string[];
  allowedOrigins: string[];
  isServiceToken: boolean;
}

export class AuthenticationMiddlewareChain {
  /**
   * Creates a complete authentication middleware chain
   */
  static createChain(options: AuthenticationChainOptions = {}) {
    return async (request: NextRequest): Promise<NextResponse | null> => {
      const {
        requireApiKey = true,
        requireJwt = true,
        requireCors = true,
        requiredScopes = [],
        rateLimitConfig,
        auditLogging = true,
        onError,
        onSuccess,
      } = options;

      let context: Partial<AuthenticationContext> = {};

      try {
        // Step 1: API Key Validation
        if (requireApiKey) {
          const apiKeyResult = await apiKeyValidationMiddleware.validateApiKey(request);
          
          if (!apiKeyResult.isValid) {
            if (auditLogging) {
              await auditLoggingMiddleware.logAuthenticationEvent(
                'api_key_validation',
                false,
                request,
                { errorMessage: apiKeyResult.error }
              );
            }
            
            if (onError) {
              return onError(apiKeyResult.error!, 'api_key', request);
            }
            
            return NextResponse.json(
              { error: 'Unauthorized', message: apiKeyResult.error },
              { status: 401 }
            );
          }

          if (apiKeyResult.remoteServer) {
            // Treat remote server API keys as service tokens
            context.projectId = apiKeyResult.remoteServer.id;
            context.projectName = apiKeyResult.remoteServer.name;
            context.allowedOrigins = apiKeyResult.remoteServer.allowedOrigins;
            context.isServiceToken = true;
          }
        }

        // Step 2: CORS Validation
        if (requireCors && context.projectId) {
          const corsResult = await corsValidationMiddleware.validateOrigin(request, context.projectId);
          
          if (!corsResult.isValid) {
            if (auditLogging) {
              await auditLoggingMiddleware.logCorsValidationEvent(
                request.headers.get('origin') || '',
                false,
                request,
                undefined,
                corsResult.reason
              );
            }
            
            if (onError) {
              return onError(corsResult.reason || 'Origin not allowed', 'cors', request);
            }
            
            return NextResponse.json(
              { error: 'Forbidden', message: corsResult.reason || 'Origin not allowed' },
              { status: 403 }
            );
          }
        }

        // Step 3: JWT Token Verification
        if (requireJwt) {
          const jwtResult = await jwtVerificationMiddleware.verifyToken(request);
          
          if (!jwtResult.isValid) {
            if (auditLogging && context.projectId) {
              await auditLoggingMiddleware.logAuthenticationEvent(
                'jwt_verification',
                false,
                request,
                { errorMessage: jwtResult.error }
              );
            }
            
            if (onError) {
              return onError(jwtResult.error!, 'jwt', request);
            }
            
            return NextResponse.json(
              { error: 'Unauthorized', message: jwtResult.error },
              { status: 401 }
            );
          }

          context.userId = jwtResult.payload!.userId;
          context.scopes = jwtResult.payload!.scopes;
          context.isServiceToken = !jwtResult.payload!.userId;
        }

        // Step 4: Scope Authorization
        if (requiredScopes.length > 0 && context.projectId) {
          const scopeResult = await scopeAuthorizationMiddleware.validateScopes(request, {
            requiredScopes,
            allowServiceTokens: true,
          });
          
          if (!scopeResult.isValid) {
            if (auditLogging) {
              await auditLoggingMiddleware.logScopeValidationEvent(
                context.userId,
                requiredScopes,
                scopeResult.grantedScopes,
                false,
                request,
                `Missing required permissions: ${scopeResult.missingScopes.join(', ')}`
              );
            }
            
            if (onError) {
              return onError(
                `Missing required permissions: ${scopeResult.missingScopes.join(', ')}`,
                'scope',
                request
              );
            }
            
            return NextResponse.json(
              {
                error: 'Forbidden',
                message: `Missing required permissions: ${scopeResult.missingScopes.join(', ')}`,
                details: {
                  requiredScopes,
                  grantedScopes: scopeResult.grantedScopes,
                  missingScopes: scopeResult.missingScopes,
                },
              },
              { status: 403 }
            );
          }
        }

        // Step 5: Rate Limiting
        if (rateLimitConfig && context.projectId) {
          const rateLimitResult = await checkRateLimit(request, {
            ...rateLimitConfig,
            keyGenerator: (req) => {
              const projectId = context.projectId!;
              const userId = context.userId;
              const ip = req.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown';
              
              return userId ? `user:${userId}:${projectId}` : `project:${projectId}:${ip}`;
            },
          });
          
          if (!rateLimitResult.allowed) {
            if (auditLogging) {
              await auditLoggingMiddleware.logRateLimitEvent(
                context.userId,
                false,
                request,
                {
                  limit: rateLimitResult.limit,
                  remaining: rateLimitResult.remaining,
                  resetTime: rateLimitResult.resetTime,
                }
              );
            }
            
            if (onError) {
              return onError('Rate limit exceeded', 'rate_limit', request);
            }
            
            return NextResponse.json(
              {
                error: 'Too Many Requests',
                message: 'Rate limit exceeded',
                retryAfter: rateLimitResult.retryAfter,
              },
              {
                status: 429,
                headers: {
                  'X-RateLimit-Limit': rateLimitResult.limit.toString(),
                  'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
                  'X-RateLimit-Reset': rateLimitResult.resetTime.toString(),
                  'Retry-After': rateLimitResult.retryAfter?.toString() || '60',
                },
              }
            );
          }
        }

        // Step 6: Success Logging
        if (auditLogging && context.projectId) {
          await 
auditLoggingMiddleware.logApiAccess(
            context.userId || undefined,
            'authenticate',
            'authentication',
            true,
            request
          );
        }

        // Success callback
        if (onSuccess) {
          onSuccess(context as AuthenticationContext, request);
        }

        // Add context to request headers for downstream handlers
        const response = NextResponse.next();
        response.headers.set('x-project-id', context.projectId || '');
        response.headers.set('x-project-name', context.projectName || '');
        response.headers.set('x-user-id', context.userId || '');
        response.headers.set('x-scopes', JSON.stringify(context.scopes || []));
        response.headers.set('x-allowed-origins', JSON.stringify(context.allowedOrigins || []));
        response.headers.set('x-is-service-token', context.isServiceToken ? 'true' : 'false');
        
        return response;
      } catch (error: any) {
        console.error('Authentication chain error:', error);
        
        if (auditLogging && context.projectId) {
          await auditLoggingMiddleware.logAuthenticationEvent(
            'api_key_validation',
            false,
            request,
            { errorMessage: error.message || 'Unknown error' }
          );
        }
        
        if (onError) {
          return onError('Authentication failed', 'chain', request);
        }
        
        return NextResponse.json(
          { error: 'Internal Server Error', message: 'Authentication failed' },
          { status: 500 }
        );
      }
    };
  }

  /**
   * Creates a lightweight authentication chain for service-to-service communication
   */
  static createServiceChain(options: Omit<AuthenticationChainOptions, 'requireJwt'> = {}) {
    return this.createChain({
      ...options,
      requireJwt: false,
      requiredScopes: options.requiredScopes || ['service:access'],
    });
  }

  /**
   * Creates a user authentication chain that requires JWT tokens
   */
  static createUserChain(options: AuthenticationChainOptions = {}) {
    return this.createChain({
      ...options,
      requireJwt: true,
    });
  }

  /**
   * Creates a development-friendly authentication chain with relaxed security
   */
  static createDevelopmentChain(allowedOrigins: string[] = ['http://localhost:3000']) {
    return async (request: NextRequest): Promise<NextResponse | null> => {
      // Add CORS headers for development
      const response = NextResponse.next();
      const origin = request.headers.get('origin');
      
      if (origin && allowedOrigins.includes(origin)) {
        response.headers.set('Access-Control-Allow-Origin', origin);
        response.headers.set('Access-Control-Allow-Credentials', 'true');
        response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key');
      }
      
      // Handle preflight requests
      if (request.method === 'OPTIONS') {
        return new NextResponse(null, { status: 204, headers: response.headers });
      }
      
      return response;
    };
  }
}

// Export individual middleware components
export {
  apiKeyValidationMiddleware,
  jwtVerificationMiddleware,
  corsValidationMiddleware,
  scopeAuthorizationMiddleware,
  createRateLimitingMiddleware,
  checkRateLimit,
  predefinedRateLimiters,
  auditLoggingMiddleware,
  requireAuth,
  withAuth,
  requireRole,
  requirePermission,
  requireAdmin,
  securityHeadersMiddleware,
  applySecurityHeaders,
  withSecurityHeaders,
  developmentSecurityHeadersMiddleware,
  productionSecurityHeadersMiddleware,
  globalSecurityMiddleware,
  withGlobalSecurity,
  isPublicEndpoint,
  requiresAuthentication,
};

// Export utility functions
export const createAuthChain = AuthenticationMiddlewareChain.createChain;
export const createServiceAuthChain = AuthenticationMiddlewareChain.createServiceChain;
export const createUserAuthChain = AuthenticationMiddlewareChain.createUserChain;
export const createDevAuthChain = AuthenticationMiddlewareChain.createDevelopmentChain;

// Export standardized auth handlers
export {
  createStandardizedHandler,
  withAdminHandler,
  withRoleHandler,
  withPublicHandler,
  createJsonResponse,
  createErrorResponse,
  createSuccessResponse
} from './standardized-auth';

export type { StandardizedHandlerContext, StandardizedHandlerOptions } from './standardized-auth';

// Export types
export type { AuthContext };