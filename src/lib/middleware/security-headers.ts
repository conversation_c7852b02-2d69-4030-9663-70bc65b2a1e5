import { NextRequest, NextResponse } from 'next/server';

interface SecurityHeadersConfig {
  csp?: {
    defaultSrc?: string[];
    scriptSrc?: string[];
    styleSrc?: string[];
    imgSrc?: string[];
    connectSrc?: string[];
    fontSrc?: string[];
    objectSrc?: string[];
    mediaSrc?: string[];
    frameSrc?: string[];
    reportUri?: string;
  };
  hsts?: {
    maxAge?: number;
    includeSubDomains?: boolean;
    preload?: boolean;
  };
  xFrameOptions?: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';
  xContentTypeOptions?: boolean;
  xXSSProtection?: boolean;
  referrerPolicy?: 'no-referrer' | 'no-referrer-when-downgrade' | 'origin' | 'origin-when-cross-origin' | 'same-origin' | 'strict-origin' | 'strict-origin-when-cross-origin' | 'unsafe-url';
  permissionsPolicy?: {
    camera?: string[];
    microphone?: string[];
    geolocation?: string[];
    payment?: string[];
    usb?: string[];
    autoplay?: string[];
    fullscreen?: string[];
  };
}

const defaultSecurityHeadersConfig: SecurityHeadersConfig = {
  csp: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
    styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
    imgSrc: ["'self'", "data:", "https:"],
    connectSrc: ["'self'", "https:"],
    fontSrc: ["'self'", "https:", "data:", "https://fonts.gstatic.com"],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    frameSrc: ["'self'"],
  },
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: false,
  },
  xFrameOptions: 'DENY',
  xContentTypeOptions: true,
  xXSSProtection: true,
  referrerPolicy: 'strict-origin-when-cross-origin',
  permissionsPolicy: {
    camera: ['self'],
    microphone: ['self'],
    geolocation: ['self'],
    payment: ['self'],
    usb: ['self'],
    autoplay: ['self'],
    fullscreen: ['self'],
  },
};

/**
 * Generate Content Security Policy header
 */
function generateCSP(config: SecurityHeadersConfig['csp']): string {
  if (!config) return '';

  const directives: string[] = [];

  if (config.defaultSrc) {
    directives.push(`default-src ${config.defaultSrc.join(' ')}`);
  }
  if (config.scriptSrc) {
    directives.push(`script-src ${config.scriptSrc.join(' ')}`);
  }
  if (config.styleSrc) {
    directives.push(`style-src ${config.styleSrc.join(' ')}`);
  }
  if (config.imgSrc) {
    directives.push(`img-src ${config.imgSrc.join(' ')}`);
  }
  if (config.connectSrc) {
    directives.push(`connect-src ${config.connectSrc.join(' ')}`);
  }
  if (config.fontSrc) {
    directives.push(`font-src ${config.fontSrc.join(' ')}`);
  }
  if (config.objectSrc) {
    directives.push(`object-src ${config.objectSrc.join(' ')}`);
  }
  if (config.mediaSrc) {
    directives.push(`media-src ${config.mediaSrc.join(' ')}`);
  }
  if (config.frameSrc) {
    directives.push(`frame-src ${config.frameSrc.join(' ')}`);
  }
  if (config.reportUri) {
    directives.push(`report-uri ${config.reportUri}`);
  }

  return directives.join('; ');
}

/**
 * Generate HSTS header
 */
function generateHSTS(config: SecurityHeadersConfig['hsts']): string {
  if (!config) return '';

  const parts: string[] = [`max-age=${config.maxAge}`];
  
  if (config.includeSubDomains) {
    parts.push('includeSubDomains');
  }
  
  if (config.preload) {
    parts.push('preload');
  }

  return parts.join('; ');
}

/**
 * Generate Permissions Policy header
 */
function generatePermissionsPolicy(config: SecurityHeadersConfig['permissionsPolicy']): string {
  if (!config) return '';

  const directives: string[] = [];

  if (config.camera) {
    directives.push(`camera=(${config.camera.join(' ')})`);
  }
  if (config.microphone) {
    directives.push(`microphone=(${config.microphone.join(' ')})`);
  }
  if (config.geolocation) {
    directives.push(`geolocation=(${config.geolocation.join(' ')})`);
  }
  if (config.payment) {
    directives.push(`payment=(${config.payment.join(' ')})`);
  }
  if (config.usb) {
    directives.push(`usb=(${config.usb.join(' ')})`);
  }
  if (config.autoplay) {
    directives.push(`autoplay=(${config.autoplay.join(' ')})`);
  }
  if (config.fullscreen) {
    directives.push(`fullscreen=(${config.fullscreen.join(' ')})`);
  }

  return directives.join(', ');
}

/**
 * Apply security headers to response
 */
export function applySecurityHeaders(
  response: NextResponse,
  config: SecurityHeadersConfig = defaultSecurityHeadersConfig
): NextResponse {
  const headers = response.headers;

  // Content Security Policy
  if (config.csp) {
    const cspHeader = generateCSP(config.csp);
    if (cspHeader) {
      headers.set('Content-Security-Policy', cspHeader);
      headers.set('X-Content-Security-Policy', cspHeader); // Legacy support
    }
  }

  // HTTP Strict Transport Security
  if (config.hsts && process.env.NODE_ENV === 'production') {
    const hstsHeader = generateHSTS(config.hsts);
    if (hstsHeader) {
      headers.set('Strict-Transport-Security', hstsHeader);
    }
  }

  // X-Frame-Options
  if (config.xFrameOptions) {
    headers.set('X-Frame-Options', config.xFrameOptions);
  }

  // X-Content-Type-Options
  if (config.xContentTypeOptions) {
    headers.set('X-Content-Type-Options', 'nosniff');
  }

  // X-XSS-Protection
  if (config.xXSSProtection) {
    headers.set('X-XSS-Protection', '1; mode=block');
  }

  // Referrer Policy
  if (config.referrerPolicy) {
    headers.set('Referrer-Policy', config.referrerPolicy);
  }

  // Permissions Policy
  if (config.permissionsPolicy) {
    const permissionsPolicy = generatePermissionsPolicy(config.permissionsPolicy);
    if (permissionsPolicy) {
      headers.set('Permissions-Policy', permissionsPolicy);
    }
  }

  // Additional security headers
  headers.set('X-Permitted-Cross-Domain-Policies', 'none');
  headers.set('Expect-CT', 'max-age=86400, enforce');

  return response;
}

/**
 * Security headers middleware
 */
export function securityHeadersMiddleware(
  config: SecurityHeadersConfig = defaultSecurityHeadersConfig
) {
  return (request: NextRequest) => {
    const response = NextResponse.next();
    return applySecurityHeaders(response, config);
  };
}

/**
 * Development-friendly security headers (relaxed for local development)
 */
export function developmentSecurityHeadersMiddleware() {
  const devConfig: SecurityHeadersConfig = {
    ...defaultSecurityHeadersConfig,
    csp: {
      ...defaultSecurityHeadersConfig.csp,
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      connectSrc: ["'self'", "http:", "https:", "ws:", "wss:"],
    },
    hsts: undefined, // No HSTS in development
  };

  return securityHeadersMiddleware(devConfig);
}

/**
 * Production security headers (strict)
 */
export function productionSecurityHeadersMiddleware() {
  const prodConfig: SecurityHeadersConfig = {
    ...defaultSecurityHeadersConfig,
    csp: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https:"],
      fontSrc: ["'self'", "https:", "data:", "https://fonts.gstatic.com"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true,
    },
  };

  return securityHeadersMiddleware(prodConfig);
}

/**
 * Apply security headers to API responses
 */
export function withSecurityHeaders(
  handler: (request: NextRequest) => Promise<NextResponse>,
  config?: SecurityHeadersConfig
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const response = await handler(request);
    return applySecurityHeaders(response, config);
  };
}