import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../prisma';

export interface ApiKeyValidationResult {
  isValid: boolean;
  remoteServer?: {
    id: string;
    name: string;
    url?: string;
    apiEndpoint?: string | null;
    allowedOrigins: string[];
    isActive: boolean;
  };
  error?: string;
}

export class ApiKeyValidationMiddleware {
  constructor() {
    // Projects table removed; remote-server API keys are stored in the DB and matched directly
  }

  /**
   * Validates API key from request headers and returns project information
   */
  async validateApiKey(request: NextRequest): Promise<ApiKeyValidationResult> {
    try {
      // Extract API key from headers
      const apiKey = this.extractApiKey(request);
      
      if (!apiKey) {
        return {
          isValid: false,
          error: 'API key is required',
        };
      }
      // Only remote-server API keys are supported now. Match directly against remote_server.apiKey
      const remoteServer = await this.findRemoteServerByApiKey(apiKey);

      if (!remoteServer) {
        return {
          isValid: false,
          error: 'Invalid API key',
        };
      }

      if (!remoteServer.isActive) {
        return {
          isValid: false,
          error: 'Remote server is inactive',
        };
      }

      // Enforce whitelist based on hosts/origins present for the remote server
      const allowed = this.isRequestAllowedForRemoteServer(request, remoteServer.allowedOrigins || []);

      if (!allowed) {
        return {
          isValid: false,
          error: 'Remote server not allowed from this host/origin',
        };
      }

      return {
        isValid: true,
        remoteServer: {
          id: remoteServer.id,
          name: remoteServer.name,
          url: remoteServer.url,
          apiEndpoint: remoteServer.apiEndpoint,
          allowedOrigins: remoteServer.allowedOrigins || [],
          isActive: remoteServer.isActive,
        },
      };
    } catch (error) {
      console.error('API key validation error:', error);
      return {
        isValid: false,
        error: 'Authentication failed',
      };
    }
  }

  /**
   * Middleware function for Next.js API routes
   */
  async middleware(request: NextRequest): Promise<NextResponse | null> {
    const result = await this.validateApiKey(request);
    
    if (!result.isValid) {
      return NextResponse.json(
        {
          error: 'Unauthorized',
          message: result.error,
        },
        { status: 401 }
      );
    }

    // Add project or remote-server information to request headers for downstream middleware
    const response = NextResponse.next();

    if (result.remoteServer) {
      response.headers.set('x-remote-server-id', result.remoteServer.id);
      response.headers.set('x-remote-server-name', result.remoteServer.name);
      if (result.remoteServer.apiEndpoint) response.headers.set('x-remote-server-api-endpoint', String(result.remoteServer.apiEndpoint));
      response.headers.set('x-remote-server-allowed-origins', JSON.stringify(result.remoteServer.allowedOrigins || []));
    }

    // Also set project headers to preserve compatibility with existing audit/rate-limit code
    if (result.remoteServer) {
      response.headers.set('x-project-id', result.remoteServer.id);
      response.headers.set('x-project-name', result.remoteServer.name);
      response.headers.set('x-allowed-origins', JSON.stringify(result.remoteServer.allowedOrigins || []));
    }

    return response;
  }

  /**
   * Creates a middleware function with custom error handling
   */
  createMiddleware(options?: {
    onError?: (error: string, request: NextRequest) => NextResponse;
    onSuccess?: (remoteServer: any, request: NextRequest) => void;
  }) {
    return async (request: NextRequest): Promise<NextResponse | null> => {
      const result = await this.validateApiKey(request);
      
      if (!result.isValid) {
        if (options?.onError) {
          return options.onError(result.error!, request);
        }
        
        return NextResponse.json(
          {
            error: 'Unauthorized',
            message: result.error,
          },
          { status: 401 }
        );
      }

      if (options?.onSuccess) {
        options.onSuccess(result.remoteServer, request);
      }

      // Continue to next middleware
      return null;
    };
  }

  /**
   * Extracts API key from request headers
   */
  private extractApiKey(request: NextRequest): string | null {
    // Check x-api-key header (preferred)
    const apiKeyHeader = request.headers.get('x-api-key');
    if (apiKeyHeader) {
      return apiKeyHeader.trim();
    }

    // Check authorization header as fallback (API-Key scheme)
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('API-Key ')) {
      return authHeader.slice(8).trim(); // Remove 'API-Key ' prefix
    }

    return null;
  }

  /**
   * Attempt to find a RemoteServer by a plain apiKey (server-to-server key)
   */
  private async findRemoteServerByApiKey(apiKey: string) {
    try {
      const server = await prisma.remoteServer.findFirst({
        where: { apiKey, isActive: true },
        select: {
          id: true,
          name: true,
          url: true,
          apiEndpoint: true,
          allowedOrigins: true,
          isActive: true,
        },
      });

      return server;
    } catch (error) {
      console.error('Error finding remote server by API key:', error);
      return null;
    }
  }

  /**
   * Determines whether the incoming request is allowed for a remote server
   * by checking origin header and common IP headers against the allowed list.
   */
  private isRequestAllowedForRemoteServer(request: NextRequest, allowedOrigins: string[] | null): boolean {
    // Deny by default if no allowed hosts are configured
    if (!allowedOrigins || allowedOrigins.length === 0) return false;

    const host = request.headers.get('host')?.toLowerCase(); // hostname[:port]
    const origin = request.headers.get('origin'); // may be full origin with scheme

    // Check each allowed entry in a forgiving way:
    // - If allowed entry equals host (hostname[:port])
    // - If allowed entry equals full origin (e.g. https://example.com)
    // - If allowed entry is a full origin, compare its host to the request host
    for (const allowed of allowedOrigins) {
      if (!allowed) continue;
      const a = String(allowed).toLowerCase();

      // Direct match against host header
      if (host && a === host) return true;

      // Direct match against origin header
      if (origin && a === origin) return true;

      // If allowed looks like a URL, compare its host portion
      try {
        const parsed = new URL(allowed);
        if (host && parsed.host.toLowerCase() === host) return true;
      } catch (e) {
        // not a URL - already compared as plain string
      }
    }

    return false;
  }

  /**
   * Finds project by API key hash
   */
  // project-related helpers removed

  /**
   * Validates API key for a specific project (utility method)
   */
  async validateApiKeyForProject(apiKey: string, projectId: string): Promise<boolean> {
  // Projects table removed; project-scoped validation is not supported
  return false;
  }

  /**
   * Gets project information from validated API key
   */
  async getProjectFromApiKey(apiKey: string) {
  // Projects table removed; this helper is no longer supported
  return null;
  }
}

// Export singleton instance
export const apiKeyValidationMiddleware = new ApiKeyValidationMiddleware();

// Export utility functions
export const validateApiKey = (request: NextRequest) => 
  apiKeyValidationMiddleware.validateApiKey(request);

export const createApiKeyMiddleware = (options?: Parameters<ApiKeyValidationMiddleware['createMiddleware']>[0]) =>
  apiKeyValidationMiddleware.createMiddleware(options);