import { prisma } from '@/lib/prisma'

export interface UserServerPermissions {
  userId: string
  serverId: string
  serverName: string
  hasAccess: boolean
  permissions: {
    name: string
    source: 'role' | 'individual' | 'inheritance'
    roleId?: string
    roleName?: string
    grantedBy?: string
    grantedAt: Date
    expiresAt?: Date | null
    inheritedFrom?: string
    hierarchyLevel?: number
  }[]
  roles: {
    roleId: string
    roleName: string
    autoGrantPermissions: string[]
  }[]
}

export interface PermissionValidationResult {
  valid: boolean
  invalidPermissions: string[]
  reason?: string
}

export class PermissionInheritanceService {
  /**
   * Calculate all permissions a user has on a specific remote server
   * Combines role-based auto-grant permissions with individual user permissions
   * Uses database-stored permission hierarchy for inheritance logic
   */
  async calculateUserServerPermissions(userId: string, serverId: string): Promise<UserServerPermissions> {
    // 1. Check if user has access to the server
    const userAccess = await prisma.user_remote_server_access.findFirst({
      where: {
        user_id: userId,
        remote_server_id: serverId,
        is_active: true
      },
      include: {
        remote_servers: {
          select: {
            name: true
          }
        }
      }
    })

    if (!userAccess) {
      return {
        userId,
        serverId,
        serverName: 'Unknown Server',
        hasAccess: false,
        permissions: [],
        roles: []
      }
    }

    // 2. Get user's roles
    const userRoles = await prisma.userRole.findMany({
      where: {
        userId: userId
      },
      include: {
        role: true
      }
    })

    // 3. Get role-based server access and auto-grant permissions
    const roleServerAccess = await prisma.role_remote_server_access.findMany({
      where: {
        role_id: { in: userRoles.map(ur => ur.roleId) },
        remote_server_id: serverId,
        is_active: true
      },
      include: {
        roles: {
          select: {
            name: true
          }
        }
      }
    })

    // 4. Get individual user permissions for this server
    const userPermissions = await prisma.user_remote_server_permissions.findMany({
      where: {
        user_id: userId,
        remote_server_id: serverId,
        is_active: true,
        OR: [
          { expires_at: null },
          { expires_at: { gt: new Date() } }
        ]
      },
      include: {
        users_user_remote_server_permissions_granted_byTousers: {
          select: {
            name: true,
            email: true
          }
        }
      }
    })

    // 5. Get all valid permissions for this server
    const serverPermissions = await prisma.remote_server_permissions.findMany({
      where: {
        remote_server_id: serverId,
        is_active: true
      },
      select: {
        permission_name: true
      }
    })

    const validPermissionNames = new Set(serverPermissions.map(p => p.permission_name))

    // 6. Combine all permissions with inheritance logic
    const allPermissions: UserServerPermissions['permissions'] = []

    // Add role-based permissions
    roleServerAccess.forEach(roleAccess => {
      roleAccess.auto_grant_permissions.forEach(permissionName => {
        if (validPermissionNames.has(permissionName)) {
          // Check if permission doesn't already exist
          if (!allPermissions.some(p => p.name === permissionName)) {
            allPermissions.push({
              name: permissionName,
              source: 'role',
              roleId: roleAccess.role_id,
              roleName: roleAccess.roles.name,
              grantedAt: roleAccess.created_at
            })
          }
        }
      })
    })

    // Add individual user permissions
    userPermissions.forEach(userPerm => {
      if (validPermissionNames.has(userPerm.permission_name)) {
        // Check if permission doesn't already exist (role-based takes precedence)
        if (!allPermissions.some(p => p.name === userPerm.permission_name)) {
          allPermissions.push({
            name: userPerm.permission_name,
            source: 'individual',
            grantedBy: userPerm.users_user_remote_server_permissions_granted_byTousers?.name ||
                      userPerm.users_user_remote_server_permissions_granted_byTousers?.email ||
                      'Unknown',
            grantedAt: userPerm.granted_at,
            expiresAt: userPerm.expires_at
          })
        }
      }
    })

    // 7. Apply permission inheritance logic using hardcoded hierarchy
    const inheritedPermissions = await this.calculateInheritedPermissions(allPermissions, serverId)

    return {
      userId,
      serverId,
      serverName: userAccess.remote_servers.name,
      hasAccess: true,
      permissions: inheritedPermissions.sort((a, b) => a.name.localeCompare(b.name)),
      roles: roleServerAccess.map(ra => ({
        roleId: ra.role_id,
        roleName: ra.roles.name,
        autoGrantPermissions: ra.auto_grant_permissions
      }))
    }
  }

  /**
   * Calculate inherited permissions based on hardcoded hierarchy
   * TODO: Replace with database-driven hierarchy after migration is applied
   */
  private async calculateInheritedPermissions(
    basePermissions: UserServerPermissions['permissions'],
    serverId: string
  ): Promise<UserServerPermissions['permissions']> {
    const result = [...basePermissions]
    const processedPermissions = new Set(basePermissions.map(p => p.name))

    // Get all valid permissions for this server
    const serverPermissions = await prisma.remote_server_permissions.findMany({
      where: {
        remote_server_id: serverId,
        is_active: true
      },
      select: {
        permission_name: true
      }
    })

    const validPermissionNames = new Set(serverPermissions.map(p => p.permission_name))

    // For each base permission, find what it inherits using hardcoded logic
    for (const basePerm of basePermissions) {
      await this.addInheritedPermissions(
        basePerm.name,
        result,
        processedPermissions,
        validPermissionNames,
        serverId,
        basePerm
      )
    }

    return result
  }

  /**
   * Recursively add inherited permissions using hardcoded hierarchy
   * TODO: Replace with database-driven hierarchy after migration is applied
   */
  private async addInheritedPermissions(
    permissionName: string,
    result: UserServerPermissions['permissions'],
    processed: Set<string>,
    validPermissions: Set<string>,
    serverId: string,
    sourcePermission: UserServerPermissions['permissions'][0]
  ): Promise<void> {
    // Hardcoded permission hierarchy - replace with database query after migration
    const hierarchyMap: Record<string, string[]> = {
      'admin': ['admin:users:read', 'admin:users:write', 'admin:users:delete'],
      'admin:users:write': ['admin:users:read'],
      'admin:users:delete': ['admin:users:write', 'admin:users:read'],
      'write': ['read'],
      'delete': ['write', 'read'],
      'upload': ['read'],
      'manage': ['read', 'write', 'upload', 'delete']
    }

    const inheritedPermissions = hierarchyMap[permissionName] || []

    for (const childPermission of inheritedPermissions) {
      if (!processed.has(childPermission) &&
          validPermissions.has(childPermission)) {

        processed.add(childPermission)

        result.push({
          name: childPermission,
          source: 'inheritance',
          inheritedFrom: permissionName,
          hierarchyLevel: 1,
          grantedAt: sourcePermission.grantedAt,
          roleId: sourcePermission.roleId,
          roleName: sourcePermission.roleName
        })

        // Recursively add permissions this child inherits
        await this.addInheritedPermissions(
          childPermission,
          result,
          processed,
          validPermissions,
          serverId,
          result[result.length - 1]
        )
      }
    }
  }

  /**
   * Calculate permissions for all servers a user has access to
   */
  async calculateAllUserServerPermissions(userId: string): Promise<UserServerPermissions[]> {
    // Get all servers user has access to
    const userServerAccess = await prisma.user_remote_server_access.findMany({
      where: {
        user_id: userId,
        is_active: true
      },
      select: {
        remote_server_id: true
      }
    })

    const results: UserServerPermissions[] = []
    
    for (const access of userServerAccess) {
      const permissions = await this.calculateUserServerPermissions(userId, access.remote_server_id)
      results.push(permissions)
    }

    return results
  }

  /**
   * Validate that permissions exist on the remote server
   */
  async validatePermissions(serverId: string, permissions: string[]): Promise<PermissionValidationResult> {
    if (permissions.length === 0) {
      return { valid: true, invalidPermissions: [] }
    }

    // Get all valid permissions for this server
    const serverPermissions = await prisma.remote_server_permissions.findMany({
      where: {
        remote_server_id: serverId,
        is_active: true
      },
      select: {
        permission_name: true
      }
    })

    const validPermissionNames = new Set(serverPermissions.map(p => p.permission_name))
    const invalidPermissions = permissions.filter(perm => !validPermissionNames.has(perm))

    return {
      valid: invalidPermissions.length === 0,
      invalidPermissions,
      reason: invalidPermissions.length > 0 
        ? `Invalid permissions for this server: ${invalidPermissions.join(', ')}`
        : undefined
    }
  }

  /**
   * Check if a user has a specific permission on a server
   */
  async hasPermission(userId: string, serverId: string, permissionName: string): Promise<boolean> {
    const userPermissions = await this.calculateUserServerPermissions(userId, serverId)
    
    if (!userPermissions.hasAccess) {
      return false
    }

    return userPermissions.permissions.some(p => p.name === permissionName)
  }

  /**
   * Get effective permissions when assigning new permissions to a user
   * (Helps UI show what user will have after assignment)
   */
  async getEffectivePermissions(
    userId: string, 
    serverId: string, 
    additionalPermissions: string[]
  ): Promise<{
    current: string[]
    additional: string[]
    final: string[]
    duplicates: string[]
  }> {
    const currentPerms = await this.calculateUserServerPermissions(userId, serverId)
    const currentPermissionNames = currentPerms.permissions.map(p => p.name)
    
    const duplicates = additionalPermissions.filter(perm => currentPermissionNames.includes(perm))
    const newPermissions = additionalPermissions.filter(perm => !currentPermissionNames.includes(perm))
    
    return {
      current: currentPermissionNames,
      additional: newPermissions,
      final: [...currentPermissionNames, ...newPermissions].sort(),
      duplicates
    }
  }

  /**
   * Sync permissions from remote server
   */
  async syncServerPermissions(serverId: string): Promise<{ 
    synced: number
    errors: string[]
  }> {
    try {
      // Get server details
      const server = await prisma.remoteServer.findUnique({
        where: { id: serverId }
      })

      if (!server) {
        return { synced: 0, errors: ['Server not found'] }
      }

      // Fetch permissions from remote server
      const response = await fetch(`${server.url}/api/permissions`, {
        headers: {
          'Authorization': `Bearer ${server.apiKey}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        return { synced: 0, errors: [`Failed to fetch permissions: HTTP ${response.status}`] }
      }

      const permissionsData = await response.json()
      const permissions = permissionsData.permissions || permissionsData || []

      // Update database with current permissions
      await prisma.$transaction(async (tx) => {
        // Mark all existing permissions as inactive
        await tx.remote_server_permissions.updateMany({
          where: { remote_server_id: serverId },
          data: { is_active: false }
        })

        // Upsert current permissions
        for (const perm of permissions) {
          await tx.remote_server_permissions.upsert({
            where: {
              remote_server_id_permission_name: {
                remote_server_id: serverId,
                permission_name: perm.name
              }
            },
            update: {
              permission_description: perm.description,
              is_active: true,
              last_synced_at: new Date()
            },
            create: {
              remote_server_id: serverId,
              permission_name: perm.name,
              permission_description: perm.description,
              is_active: true,
              last_synced_at: new Date()
            }
          })
        }
      })

      return { synced: permissions.length, errors: [] }
    } catch (error) {
      console.error('Error syncing server permissions:', error)
      return { 
        synced: 0, 
        errors: [error instanceof Error ? error.message : 'Unknown error'] 
      }
    }
  }

  /**
   * Clean up expired permissions
   */
  async cleanupExpiredPermissions(): Promise<number> {
    const result = await prisma.user_remote_server_permissions.updateMany({
      where: {
        expires_at: { lt: new Date() },
        is_active: true
      },
      data: {
        is_active: false
      }
    })

    return result.count
  }
}

// Export singleton instance
export const permissionInheritance = new PermissionInheritanceService()