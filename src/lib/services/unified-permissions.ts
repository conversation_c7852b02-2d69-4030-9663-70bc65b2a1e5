import { prisma } from '@/lib/prisma'
import { permissionInheritance } from './permission-inheritance'

export interface PermissionContext {
  userId: string
  permission: string
  serverId?: string // undefined = local permissions, string = remote server permissions
}

export interface PermissionResult {
  allowed: boolean
  source: 'local' | 'remote' | 'none'
  serverId?: string
  reason?: string
  details?: {
    roleBased?: boolean
    individual?: boolean
    expiresAt?: Date
    grantedBy?: string
  }
}

export class UnifiedPermissionService {
  /**
   * Check if user has a specific permission in the given context
   * @param context - Permission check context
   * @returns Permission result with details
   */
  async checkPermission(context: PermissionContext): Promise<PermissionResult> {
    const { userId, permission, serverId } = context

    try {
      if (serverId) {
        // Remote server permission check
        return await this.checkRemotePermission(userId, permission, serverId)
      } else {
        // Local permission check
        return await this.checkLocalPermission(userId, permission)
      }
    } catch (error) {
      console.error('Error checking permission:', error)
      return {
        allowed: false,
        source: 'none',
        reason: 'Error checking permission'
      }
    }
  }

  /**
   * Check local (central server) permissions
   */
  private async checkLocalPermission(userId: string, permission: string): Promise<PermissionResult> {
    // Get user's roles and their permissions
    const userRoles = await prisma.userRole.findMany({
      where: { userId },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    })

    // Check if any role has the required permission
    for (const userRole of userRoles) {
      const hasPermission = userRole.role.rolePermissions.some(
        rp => rp.permission.name === permission
      )

      if (hasPermission) {
        return {
          allowed: true,
          source: 'local',
          reason: `Granted by role: ${userRole.role.name}`,
          details: {
            roleBased: true,
            grantedBy: userRole.assignedBy || 'System'
          }
        }
      }
    }

    return {
      allowed: false,
      source: 'local',
      reason: 'Permission not found in user roles'
    }
  }

  /**
   * Check remote server permissions
   */
  private async checkRemotePermission(userId: string, permission: string, serverId: string): Promise<PermissionResult> {
    // Use existing permission inheritance service
    const hasPermission = await permissionInheritance.hasPermission(userId, serverId, permission)

    if (!hasPermission) {
      return {
        allowed: false,
        source: 'remote',
        serverId,
        reason: 'Permission not granted on this server'
      }
    }

    // Get detailed permission info
    const userPermissions = await permissionInheritance.calculateUserServerPermissions(userId, serverId)
    const permissionDetail = userPermissions.permissions.find(p => p.name === permission)

    if (!permissionDetail) {
      return {
        allowed: false,
        source: 'remote',
        serverId,
        reason: 'Permission details not found'
      }
    }

    return {
      allowed: true,
      source: 'remote',
      serverId,
      reason: `Granted ${permissionDetail.source === 'role' ? 'by role' : 'individually'} on ${userPermissions.serverName}`,
      details: {
        roleBased: permissionDetail.source === 'role',
        individual: permissionDetail.source === 'individual',
        expiresAt: permissionDetail.expiresAt || undefined,
        grantedBy: permissionDetail.grantedBy || permissionDetail.roleName || 'System'
      }
    }
  }

  /**
   * Get all permissions for a user in all contexts
   */
  async getAllUserPermissions(userId: string): Promise<{
    local: PermissionResult[]
    remote: { [serverId: string]: PermissionResult[] }
  }> {
    const result = {
      local: [] as PermissionResult[],
      remote: {} as { [serverId: string]: PermissionResult[] }
    }

    // Get local permissions
    const userRoles = await prisma.userRole.findMany({
      where: { userId },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    })

    // Collect all unique local permissions
    const localPermissions = new Set<string>()
    for (const userRole of userRoles) {
      for (const rolePermission of userRole.role.rolePermissions) {
        localPermissions.add(rolePermission.permission.name)
      }
    }

    // Convert to PermissionResult format
    for (const permission of localPermissions) {
      result.local.push(await this.checkLocalPermission(userId, permission))
    }

    // Get remote server permissions
    const userServerAccess = await prisma.user_remote_server_access.findMany({
      where: {
        user_id: userId,
        is_active: true
      },
      select: {
        remote_server_id: true
      }
    })

    for (const access of userServerAccess) {
      const serverPermissions = await permissionInheritance.calculateUserServerPermissions(
        userId,
        access.remote_server_id
      )

      result.remote[access.remote_server_id] = []

      for (const perm of serverPermissions.permissions) {
        result.remote[access.remote_server_id].push({
          allowed: true,
          source: 'remote',
          serverId: access.remote_server_id,
          reason: `Granted ${perm.source === 'role' ? 'by role' : 'individually'}`,
          details: {
            roleBased: perm.source === 'role',
            individual: perm.source === 'individual',
            expiresAt: perm.expiresAt || undefined,
            grantedBy: perm.grantedBy || perm.roleName || 'System'
          }
        })
      }
    }

    return result
  }

  /**
   * Validate permission context
   */
  validateContext(context: PermissionContext): { valid: boolean; reason?: string } {
    if (!context.userId) {
      return { valid: false, reason: 'User ID is required' }
    }

    if (!context.permission) {
      return { valid: false, reason: 'Permission name is required' }
    }

    // serverId is optional - undefined means local, string means remote
    return { valid: true }
  }
}

// Export singleton instance
export const unifiedPermissions = new UnifiedPermissionService()

// Convenience functions for common use cases
export const checkPermission = (context: PermissionContext) =>
  unifiedPermissions.checkPermission(context)

export const checkLocalPermission = (userId: string, permission: string) =>
  unifiedPermissions.checkPermission({ userId, permission })

export const checkRemotePermission = (userId: string, permission: string, serverId: string) =>
  unifiedPermissions.checkPermission({ userId, permission, serverId })

export const getAllUserPermissions = (userId: string) =>
  unifiedPermissions.getAllUserPermissions(userId)