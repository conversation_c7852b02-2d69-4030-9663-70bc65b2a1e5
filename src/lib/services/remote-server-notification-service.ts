import { prisma } from '@/lib/prisma'
import { broadcastNotification } from '@/lib/notifications'

interface PermissionFetchFailureData {
  serverId: string
  serverName: string
  error: string
  timestamp: string
  context?: string // e.g., 'server_creation', 'manual_fetch', 'assignment_check'
}

interface HealthCheckFailureData {
  serverId: string
  serverName: string
  status: 'offline' | 'error'
  error: string
  responseTime?: number
  timestamp: string
}

export class RemoteServerNotificationService {
  /**
   * Notify admins about permission fetch failures
   */
  async notifyPermissionFetchFailure(failureData: PermissionFetchFailureData) {
    try {
      console.log('Creating permission fetch failure notification:', failureData)

      // Create notification in database
      const notification = await prisma.notification.create({
        data: {
          type: 'PERMISSION_FETCH_FAILED',
          message: `Failed to fetch permissions from server "${failureData.serverName}"`,
          data: {
            serverId: failureData.serverId,
            serverName: failureData.serverName,
            error: failureData.error,
            timestamp: failureData.timestamp,
            context: failureData.context || 'unknown'
          },
          recipients: ['admin'], // Target all admins
          readBy: [] // No one has read it yet
        }
      })

      // Broadcast notification to connected admin clients
      await this.broadcastPermissionFailureNotification({
        id: notification.id,
        type: 'PERMISSION_FETCH_FAILED',
        message: notification.message,
        serverId: failureData.serverId,
        serverName: failureData.serverName,
        error: failureData.error,
        timestamp: failureData.timestamp,
        context: failureData.context
      })

      console.log('Permission fetch failure notification created and broadcasted')
      return { success: true, notificationId: notification.id }

    } catch (error) {
      console.error('Failed to create permission fetch failure notification:', error)
      return { success: false, error: error instanceof Error ? error.message : String(error) }
    }
  }

  /**
   * Notify admins about health check failures
   */
  async notifyHealthCheckFailure(failureData: HealthCheckFailureData) {
    try {
      console.log('Creating health check failure notification:', failureData)

      // Create notification in database
      const notification = await prisma.notification.create({
        data: {
          type: 'SERVER_HEALTH_CHECK_FAILED',
          message: `Server "${failureData.serverName}" is ${failureData.status}`,
          data: {
            serverId: failureData.serverId,
            serverName: failureData.serverName,
            status: failureData.status,
            error: failureData.error,
            responseTime: failureData.responseTime,
            timestamp: failureData.timestamp
          },
          recipients: ['admin'], // Target all admins
          readBy: [] // No one has read it yet
        }
      })

      // Broadcast notification to connected admin clients
      await this.broadcastHealthCheckFailureNotification({
        id: notification.id,
        type: 'SERVER_HEALTH_CHECK_FAILED',
        message: notification.message,
        serverId: failureData.serverId,
        serverName: failureData.serverName,
        status: failureData.status,
        error: failureData.error,
        responseTime: failureData.responseTime,
        timestamp: failureData.timestamp
      })

      console.log('Health check failure notification created and broadcasted')
      return { success: true, notificationId: notification.id }

    } catch (error) {
      console.error('Failed to create health check failure notification:', error)
      return { success: false, error: error instanceof Error ? error.message : String(error) }
    }
  }

  /**
   * Notify admins about successful permission sync
   */
  async notifyPermissionSyncSuccess(serverId: string, serverName: string, permissionsCount: number) {
    try {
      console.log('Creating permission sync success notification:', { serverId, serverName, permissionsCount })

      // Create notification in database
      const notification = await prisma.notification.create({
        data: {
          type: 'PERMISSION_SYNC_SUCCESS',
          message: `Successfully synced ${permissionsCount} permissions from server "${serverName}"`,
          data: {
            serverId,
            serverName,
            permissionsCount,
            timestamp: new Date().toISOString()
          },
          recipients: ['admin'], // Target all admins
          readBy: [] // No one has read it yet
        }
      })

      // Broadcast notification to connected admin clients
      await this.broadcastPermissionSyncSuccessNotification({
        id: notification.id,
        type: 'PERMISSION_SYNC_SUCCESS',
        message: notification.message,
        serverId,
        serverName,
        permissionsCount,
        timestamp: new Date().toISOString()
      })

      console.log('Permission sync success notification created and broadcasted')
      return { success: true, notificationId: notification.id }

    } catch (error) {
      console.error('Failed to create permission sync success notification:', error)
      return { success: false, error: error instanceof Error ? error.message : String(error) }
    }
  }

  /**
   * Get recent notifications for a specific server
   */
  async getServerNotifications(serverId: string, limit: number = 10) {
    try {
      const notifications = await prisma.notification.findMany({
        where: {
          data: {
            path: ['serverId'],
            equals: serverId
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: limit
      })

      return { success: true, notifications }
    } catch (error) {
      console.error('Failed to get server notifications:', error)
      return { success: false, error: error instanceof Error ? error.message : String(error), notifications: [] }
    }
  }

  /**
   * Mark notifications as read for a user
   */
  async markNotificationsAsRead(notificationIds: string[], userId: string) {
    try {
      await prisma.notification.updateMany({
        where: {
          id: {
            in: notificationIds
          }
        },
        data: {
          readBy: {
            push: userId
          }
        }
      })

      return { success: true }
    } catch (error) {
      console.error('Failed to mark notifications as read:', error)
      return { success: false, error: error instanceof Error ? error.message : String(error) }
    }
  }

  // Private methods for broadcasting notifications

  private async broadcastPermissionFailureNotification(notificationData: any) {
    try {
      await broadcastNotification({
        type: 'PERMISSION_FETCH_FAILED',
        message: notificationData.message,
        data: notificationData,
        recipients: ['admin'],
        timestamp: notificationData.timestamp
      })
    } catch (error) {
      console.error('Failed to broadcast permission failure notification:', error)
    }
  }

  private async broadcastHealthCheckFailureNotification(notificationData: any) {
    try {
      await broadcastNotification({
        type: 'SERVER_HEALTH_CHECK_FAILED',
        message: notificationData.message,
        data: notificationData,
        recipients: ['admin'],
        timestamp: notificationData.timestamp
      })
    } catch (error) {
      console.error('Failed to broadcast health check failure notification:', error)
    }
  }

  private async broadcastPermissionSyncSuccessNotification(notificationData: any) {
    try {
      await broadcastNotification({
        type: 'PERMISSION_SYNC_SUCCESS',
        message: notificationData.message,
        data: notificationData,
        recipients: ['admin'],
        timestamp: notificationData.timestamp
      })
    } catch (error) {
      console.error('Failed to broadcast permission sync success notification:', error)
    }
  }
}