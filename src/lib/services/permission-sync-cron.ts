import { PermissionSyncService } from './permission-sync-service'

interface CronJob {
  id: string
  interval: number // milliseconds
  lastRun: Date | null
  isRunning: boolean
  run: () => Promise<void>
}

export class PermissionSyncCron {
  private jobs: Map<string, CronJob> = new Map()
  private syncService: PermissionSyncService

  constructor() {
    this.syncService = new PermissionSyncService()
    this.initializeJobs()
  }

  private initializeJobs() {
    // Daily sync job (runs every 24 hours)
    this.jobs.set('daily-sync', {
      id: 'daily-sync',
      interval: 24 * 60 * 60 * 1000, // 24 hours
      lastRun: null,
      isRunning: false,
      run: async () => {
        console.log('Starting daily permission sync...')
        const results = await this.syncService.syncAllServers()
        console.log('Daily permission sync completed:', results)
      }
    })

    // Hourly sync job (runs every hour)
    this.jobs.set('hourly-sync', {
      id: 'hourly-sync',
      interval: 60 * 60 * 1000, // 1 hour
      lastRun: null,
      isRunning: false,
      run: async () => {
        console.log('Starting hourly permission sync...')
        const results = await this.syncService.syncAllServers()
        console.log('Hourly permission sync completed:', results)
      }
    })
  }

  /**
   * Start all cron jobs
   */
  start() {
    console.log('Starting permission sync cron jobs...')
    
    for (const job of this.jobs.values()) {
      this.startJob(job.id)
    }

    console.log('All permission sync cron jobs started')
  }

  /**
   * Start a specific cron job
   */
  startJob(jobId: string) {
    const job = this.jobs.get(jobId)
    if (!job) {
      console.warn(`Cron job ${jobId} not found`)
      return
    }

    const runJob = async () => {
      if (job.isRunning) {
        console.log(`Job ${jobId} is already running, skipping...`)
        return
      }

      job.isRunning = true
      try {
        await job.run()
        job.lastRun = new Date()
      } catch (error) {
        console.error(`Job ${jobId} failed:`, error)
      } finally {
        job.isRunning = false
      }
    }

    // Run immediately on startup
    runJob()

    // Set up interval
    setInterval(runJob, job.interval)

    console.log(`Cron job ${jobId} started with interval ${job.interval}ms`)
  }

  /**
   * Stop all cron jobs
   */
  stop() {
    console.log('Stopping permission sync cron jobs...')
    // Note: In a real implementation, we'd need to track and clear intervals
    this.jobs.clear()
    console.log('All permission sync cron jobs stopped')
  }

  /**
   * Get job status
   */
  getJobStatus(jobId: string) {
    const job = this.jobs.get(jobId)
    if (!job) {
      return null
    }

    return {
      id: job.id,
      interval: job.interval,
      lastRun: job.lastRun,
      isRunning: job.isRunning,
      nextRun: job.lastRun ? new Date(job.lastRun.getTime() + job.interval) : null
    }
  }

  /**
   * Get all job statuses
   */
  getAllJobStatuses() {
    const statuses: any[] = []
    for (const job of this.jobs.values()) {
      statuses.push(this.getJobStatus(job.id))
    }
    return statuses
  }

  /**
   * Manually trigger a sync job
   */
  async triggerJob(jobId: string) {
    const job = this.jobs.get(jobId)
    if (!job) {
      throw new Error(`Cron job ${jobId} not found`)
    }

    await job.run()
    return { success: true, jobId, triggeredAt: new Date() }
  }

  /**
   * Manually trigger sync for a specific server
   */
  async triggerServerSync(serverId: string) {
    try {
      const result = await this.syncService.syncServerPermissions(serverId)
      return { success: result.success, result }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }

  /**
   * Manually trigger sync for all servers
   */
  async triggerAllServersSync() {
    try {
      const results = await this.syncService.syncAllServers()
      return { success: true, results }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }
}

// Singleton instance
export const permissionSyncCron = new PermissionSyncCron()