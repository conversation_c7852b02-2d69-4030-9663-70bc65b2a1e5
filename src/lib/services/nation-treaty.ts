import { PrismaClient, NationTreatyStatus, NationTreatyMemberStatus, NationTreatyEnvoyStatus } from '@prisma/client';
import { 
  NationTreatyCreate, 
  NationTreatyUpdate, 
  NationTreatyMemberCreate, 
  NationTreatyMemberUpdate,
  NationTreatyEnvoyCreate,
  NationTreatyEnvoyUpdate,
  NationTreatyQuery,
  NationTreatyMemberQuery,
  NationTreatyEnvoyQuery,
  ValidationError,
  NationTreatyError,
  validateStatusTransition,
  validateContactInfo,
  NationTreatyMemberRole,
  NationTreatyEnvoyType
} from '@/lib/validation/nation-treaty';

export class NationTreatyService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  // Nation Treaty CRUD Operations
  async createNationTreaty(data: NationTreatyCreate, creatorId: string) {
    try {
      // Check for duplicate treaty name
      const existingTreaty = await this.prisma.nationTreaty.findFirst({
        where: { 
          name: data.name,
          isDeleted: false 
        }
      });

      if (existingTreaty) {
        throw new NationTreatyError(
          'A nation treaty with this name already exists',
          'DUPLICATE_NAME'
        );
      }

      // Validate contact information if provided
      if (data.contactEmail || data.contactPhone || data.contactAddress) {
        validateContactInfo({
          email: data.contactEmail || '',
          phone: data.contactPhone || '',
          address: data.contactAddress || '',
          website: data.website || ''
        });
      }

      // Validate emergency contact if provided
      if (data.emergencyContactName && data.emergencyContactPhone) {
        // Additional emergency contact validation can be added here
      }

      const nationTreaty = await this.prisma.nationTreaty.create({
        data: {
          ...data,
          status: data.status as NationTreatyStatus,
          metadata: data.metadata || {}
        },
        include: {
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            }
          },
          envoys: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      });

      return nationTreaty;
    } catch (error) {
      if (error instanceof NationTreatyError || error instanceof ValidationError) {
        throw error;
      }
      throw new NationTreatyError('Failed to create nation treaty', 'CREATE_FAILED');
    }
  }

  async getNationTreaties(query: NationTreatyQuery) {
    try {
      const { page, limit, search, status, sortBy, sortOrder, contactEmail, country } = query;
      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {
        isDeleted: false
      };

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { officialName: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { notes: { contains: search, mode: 'insensitive' } }
        ];
      }

      if (status) {
        where.status = status;
      }

      if (contactEmail) {
        where.contactEmail = { contains: contactEmail, mode: 'insensitive' };
      }

      if (country) {
        // Search in metadata or contact address for country information
        where.OR = [
          ...where.OR || [],
          { contactAddress: { contains: country, mode: 'insensitive' } },
          { metadata: { path: ['country'], string_contains: country } }
        ];
      }

      const [treaties, total] = await Promise.all([
        this.prisma.nationTreaty.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            _count: {
              select: {
                members: {
                  where: { status: NationTreatyMemberStatus.ACTIVE }
                },
                envoys: {
                  where: { status: NationTreatyEnvoyStatus.ACTIVE }
                }
              }
            }
          }
        }),
        this.prisma.nationTreaty.count({ where })
      ]);

      return {
        treaties,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new NationTreatyError('Failed to fetch nation treaties', 'FETCH_FAILED');
    }
  }

  async getNationTreatyById(id: string, includeRelations = true) {
    try {
      const treaty = await this.prisma.nationTreaty.findFirst({
        where: { id, isDeleted: false },
        include: includeRelations ? {
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            }
          },
          envoys: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            }
          },
          _count: {
            select: {
              members: true,
              envoys: true
            }
          }
        } : undefined
      });

      if (!treaty) {
        throw new NationTreatyError('Nation treaty not found', 'NOT_FOUND');
      }

      return treaty;
    } catch (error) {
      if (error instanceof NationTreatyError) throw error;
      throw new NationTreatyError('Failed to fetch nation treaty', 'FETCH_FAILED');
    }
  }

  async updateNationTreaty(id: string, data: NationTreatyUpdate, updaterId: string) {
    try {
      // Check if treaty exists
      const existingTreaty = await this.prisma.nationTreaty.findFirst({
        where: { id, isDeleted: false }
      });

      if (!existingTreaty) {
        throw new NationTreatyError('Nation treaty not found', 'NOT_FOUND');
      }

      // Validate status transition
      if (data.status && data.status !== existingTreaty.status) {
        validateStatusTransition(existingTreaty.status, data.status);
      }

      // Check for name conflict if updating name
      if (data.name && data.name !== existingTreaty.name) {
        const nameConflict = await this.prisma.nationTreaty.findFirst({
          where: { 
            name: data.name,
            isDeleted: false,
            id: { not: id }
          }
        });

        if (nameConflict) {
          throw new NationTreatyError(
            'A nation treaty with this name already exists',
            'DUPLICATE_NAME'
          );
        }
      }

      const updatedTreaty = await this.prisma.nationTreaty.update({
        where: { id },
        data: {
          ...data,
          status: data.status as NationTreatyStatus,
          updatedAt: new Date()
        },
        include: {
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            }
          },
          envoys: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      });

      return updatedTreaty;
    } catch (error) {
      if (error instanceof NationTreatyError || error instanceof ValidationError) {
        throw error;
      }
      throw new NationTreatyError('Failed to update nation treaty', 'UPDATE_FAILED');
    }
  }

  async deleteNationTreaty(id: string, deleterId: string) {
    try {
      // Check if treaty exists
      const treaty = await this.prisma.nationTreaty.findFirst({
        where: { id, isDeleted: false },
        include: {
          _count: {
            select: {
              members: {
                where: { status: NationTreatyMemberStatus.ACTIVE }
              },
              envoys: {
                where: { status: NationTreatyEnvoyStatus.ACTIVE }
              }
            }
          }
        }
      });

      if (!treaty) {
        throw new NationTreatyError('Nation treaty not found', 'NOT_FOUND');
      }

      // Prevent deletion if there are active members or envoys
      if (treaty._count.members > 0 || treaty._count.envoys > 0) {
        throw new NationTreatyError(
          'Cannot delete nation treaty with active members or envoys',
          'ACTIVE_MEMBERS_EXIST'
        );
      }

      // Soft delete
      const deletedTreaty = await this.prisma.nationTreaty.update({
        where: { id },
        data: {
          isDeleted: true,
          deletedAt: new Date(),
          status: NationTreatyStatus.SUSPENDED
        }
      });

      return deletedTreaty;
    } catch (error) {
      if (error instanceof NationTreatyError) throw error;
      throw new NationTreatyError('Failed to delete nation treaty', 'DELETE_FAILED');
    }
  }

  // Member Management
  async addMemberToNationTreaty(data: NationTreatyMemberCreate) {
    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id: data.userId }
      });

      if (!user) {
        throw new ValidationError('User not found', 'userId');
      }

      // Check if treaty exists
      const treaty = await this.prisma.nationTreaty.findFirst({
        where: { id: data.nationTreatyId, isDeleted: false }
      });

      if (!treaty) {
        throw new ValidationError('Nation treaty not found', 'nationTreatyId');
      }

      // Check if user is already a member
      const existingMember = await this.prisma.nationTreatyMember.findFirst({
        where: {
          userId: data.userId,
          nationTreatyId: data.nationTreatyId,
          status: { not: NationTreatyMemberStatus.INACTIVE }
        }
      });

      if (existingMember) {
        throw new NationTreatyError(
          'User is already a member of this nation treaty',
          'ALREADY_MEMBER'
        );
      }

      const member = await this.prisma.nationTreatyMember.create({
        data: {
          ...data,
          status: data.status as NationTreatyMemberStatus,
          role: data.role as keyof typeof NationTreatyMemberRole,
          joinDate: new Date()
        },
        include: {
          user: {
                      },
          nationTreaty: {
            select: {
              id: true,
              name: true,
              officialName: true
            }
          }
        }
      });

      return member;
    } catch (error) {
      if (error instanceof ValidationError || error instanceof NationTreatyError) {
        throw error;
      }
      throw new NationTreatyError('Failed to add member to nation treaty', 'MEMBER_CREATE_FAILED');
    }
  }

  async getNationTreatyMembers(nationTreatyId: string, query: NationTreatyMemberQuery) {
    try {
      const { page, limit, search, status, sortBy, sortOrder } = query;
      const skip = (page - 1) * limit;

      const where: any = { nationTreatyId };

      if (search) {
        where.user = {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } }
          ]
        };
      }

      if (status) {
        where.status = status;
      }

      const [members, total] = await Promise.all([
        this.prisma.nationTreatyMember.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            user: {
                          }
          }
        }),
        this.prisma.nationTreatyMember.count({ where })
      ]);

      return {
        members,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new NationTreatyError('Failed to fetch nation treaty members', 'MEMBER_FETCH_FAILED');
    }
  }

  async removeMemberFromNationTreaty(memberId: string) {
    try {
      const member = await this.prisma.nationTreatyMember.findUnique({
        where: { id: memberId }
      });

      if (!member) {
        throw new NationTreatyError('Member not found', 'MEMBER_NOT_FOUND');
      }

      // Soft delete by setting status to INACTIVE
      const updatedMember = await this.prisma.nationTreatyMember.update({
        where: { id: memberId },
        data: { status: NationTreatyMemberStatus.INACTIVE }
      });

      return updatedMember;
    } catch (error) {
      if (error instanceof NationTreatyError) throw error;
      throw new NationTreatyError('Failed to remove member from nation treaty', 'MEMBER_REMOVE_FAILED');
    }
  }

  // Envoy Management
  async addEnvoyToNationTreaty(data: NationTreatyEnvoyCreate) {
    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id: data.userId }
      });

      if (!user) {
        throw new ValidationError('User not found', 'userId');
      }

      // Check if treaty exists
      const treaty = await this.prisma.nationTreaty.findFirst({
        where: { id: data.nationTreatyId, isDeleted: false }
      });

      if (!treaty) {
        throw new ValidationError('Nation treaty not found', 'nationTreatyId');
      }

      // Check if user is already an envoy
      const existingEnvoy = await this.prisma.nationTreatyEnvoy.findFirst({
        where: {
          userId: data.userId,
          nationTreatyId: data.nationTreatyId,
          status: { not: NationTreatyEnvoyStatus.INACTIVE }
        }
      });

      if (existingEnvoy) {
        throw new NationTreatyError(
          'User is already an envoy for this nation treaty',
          'ALREADY_ENVOY'
        );
      }

      const envoy = await this.prisma.nationTreatyEnvoy.create({
        data: {
          ...data,
          status: data.status as NationTreatyEnvoyStatus,
          envoyType: data.envoyType as keyof typeof NationTreatyEnvoyType
        },
        include: {
          user: {
                      },
          nationTreaty: {
            select: {
              id: true,
              name: true,
              officialName: true
            }
          }
        }
      });

      return envoy;
    } catch (error) {
      if (error instanceof ValidationError || error instanceof NationTreatyError) {
        throw error;
      }
      throw new NationTreatyError('Failed to add envoy to nation treaty', 'ENVOY_CREATE_FAILED');
    }
  }

  async getNationTreatyEnvoys(nationTreatyId: string, query: NationTreatyEnvoyQuery) {
    try {
      const { page, limit, search, status, title, sortBy, sortOrder } = query;
      const skip = (page - 1) * limit;

      const where: any = { nationTreatyId };

      if (search) {
        where.user = {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } }
          ]
        };
      }

      if (status) {
        where.status = status;
      }

      if (title) {
        where.title = { contains: title, mode: 'insensitive' };
      }

      const [envoys, total] = await Promise.all([
        this.prisma.nationTreatyEnvoy.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            user: {
                          }
          }
        }),
        this.prisma.nationTreatyEnvoy.count({ where })
      ]);

      return {
        envoys,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new NationTreatyError('Failed to fetch nation treaty envoys', 'ENVOY_FETCH_FAILED');
    }
  }

  async removeEnvoyFromNationTreaty(envoyId: string) {
    try {
      const envoy = await this.prisma.nationTreatyEnvoy.findUnique({
        where: { id: envoyId }
      });

      if (!envoy) {
        throw new NationTreatyError('Envoy not found', 'ENVOY_NOT_FOUND');
      }

      // Soft delete by setting status to INACTIVE
      const updatedEnvoy = await this.prisma.nationTreatyEnvoy.update({
        where: { id: envoyId },
        data: { status: NationTreatyEnvoyStatus.INACTIVE }
      });

      return updatedEnvoy;
    } catch (error) {
      if (error instanceof NationTreatyError) throw error;
      throw new NationTreatyError('Failed to remove envoy from nation treaty', 'ENVOY_REMOVE_FAILED');
    }
  }

  // Office Management
  async addOfficeToNationTreaty(data: any) {
    try {
      // Check if treaty exists
      const treaty = await this.prisma.nationTreaty.findFirst({
        where: { id: data.nationTreatyId, isDeleted: false }
      });

      if (!treaty) {
        throw new ValidationError('Nation treaty not found', 'nationTreatyId');
      }

      const office = await this.prisma.nationTreatyOffice.create({
        data: {
          ...data,
          status: data.status as any,
          officeType: data.officeType as any
        },
        include: {
          nationTreaty: {
            select: {
              id: true,
              name: true,
              officialName: true
            }
          }
        }
      });

      return office;
    } catch (error) {
      if (error instanceof ValidationError || error instanceof NationTreatyError) {
        throw error;
      }
      throw new NationTreatyError('Failed to add office to nation treaty', 'OFFICE_CREATE_FAILED');
    }
  }

  async getNationTreatyOffices(nationTreatyId: string, query: any) {
    try {
      const { page, limit, search, status, officeType, country, city, sortBy, sortOrder } = query;
      const skip = (page - 1) * limit;

      const where: any = { nationTreatyId };

      if (search) {
        where.OR = [
          { city: { contains: search, mode: 'insensitive' } },
          { country: { contains: search, mode: 'insensitive' } },
          { region: { contains: search, mode: 'insensitive' } },
          { streetAddress: { contains: search, mode: 'insensitive' } }
        ];
      }

      if (status) {
        where.status = status;
      }

      if (officeType) {
        where.officeType = officeType;
      }

      if (country) {
        where.country = { contains: country, mode: 'insensitive' };
      }

      if (city) {
        where.city = { contains: city, mode: 'insensitive' };
      }

      const [offices, total] = await Promise.all([
        this.prisma.nationTreatyOffice.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder }
        }),
        this.prisma.nationTreatyOffice.count({ where })
      ]);

      return {
        offices,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new NationTreatyError('Failed to fetch nation treaty offices', 'OFFICE_FETCH_FAILED');
    }
  }

  async updateNationTreatyOffice(id: string, data: any) {
    try {
      const office = await this.prisma.nationTreatyOffice.findUnique({
        where: { id }
      });

      if (!office) {
        throw new NationTreatyError('Office not found', 'OFFICE_NOT_FOUND');
      }

      const updatedOffice = await this.prisma.nationTreatyOffice.update({
        where: { id },
        data: {
          ...data,
          status: data.status as any,
          officeType: data.officeType as any,
          updatedAt: new Date()
        }
      });

      return updatedOffice;
    } catch (error) {
      if (error instanceof NationTreatyError) throw error;
      throw new NationTreatyError('Failed to update nation treaty office', 'OFFICE_UPDATE_FAILED');
    }
  }

  async deleteNationTreatyOffice(id: string) {
    try {
      const office = await this.prisma.nationTreatyOffice.findUnique({
        where: { id }
      });

      if (!office) {
        throw new NationTreatyError('Office not found', 'OFFICE_NOT_FOUND');
      }

      await this.prisma.nationTreatyOffice.delete({
        where: { id }
      });

      return office;
    } catch (error) {
      if (error instanceof NationTreatyError) throw error;
      throw new NationTreatyError('Failed to delete nation treaty office', 'OFFICE_DELETE_FAILED');
    }
  }

  // Statistics and Analytics
  async getNationTreatyStatistics() {
    try {
      const [
        totalTreaties,
        activeTreaties,
        totalMembers,
        activeMembers,
        totalEnvoys,
        activeEnvoys,
        statusCounts
      ] = await Promise.all([
        this.prisma.nationTreaty.count({ where: { isDeleted: false } }),
        this.prisma.nationTreaty.count({ 
          where: { isDeleted: false, status: NationTreatyStatus.ACTIVE } 
        }),
        this.prisma.nationTreatyMember.count(),
        this.prisma.nationTreatyMember.count({ 
          where: { status: NationTreatyMemberStatus.ACTIVE } 
        }),
        this.prisma.nationTreatyEnvoy.count(),
        this.prisma.nationTreatyEnvoy.count({ 
          where: { status: NationTreatyEnvoyStatus.ACTIVE } 
        }),
        this.prisma.nationTreaty.groupBy({
          by: ['status'],
          where: { isDeleted: false },
          _count: { status: true }
        })
      ]);

      return {
        totalTreaties,
        activeTreaties,
        totalMembers,
        activeMembers,
        totalEnvoys,
        activeEnvoys,
        statusBreakdown: statusCounts.reduce((acc, item) => {
          acc[item.status] = item._count.status;
          return acc;
        }, {} as Record<string, number>)
      };
    } catch (error) {
      throw new NationTreatyError('Failed to fetch nation treaty statistics', 'STATISTICS_FETCH_FAILED');
    }
  }
}