'use client';

import { SecurityEventType, SecurityEventSeverity } from './security-event-detection';

export interface SecurityMetric {
  id: string;
  name: string;
  description: string;
  category: 'EVENTS' | 'RESPONSES' | 'ALERTS' | 'INCIDENTS' | 'PERFORMANCE';
  type: 'COUNTER' | 'GAUGE' | 'HISTOGRAM' | 'SUMMARY';
  value: number;
  labels: Record<string, string>;
  timestamp: Date;
}

export interface KPI {
  id: string;
  name: string;
  description: string;
  target: number;
  current: number;
  trend: 'UP' | 'DOWN' | 'STABLE';
  status: 'GOOD' | 'WARNING' | 'CRITICAL';
  lastUpdated: Date;
  category: 'SECURITY' | 'RESPONSE' | 'MONITORING' | 'COMPLIANCE';
}

export interface MetricTimeSeries {
  metric: string;
  labels: Record<string, string>;
  data: Array<{ timestamp: Date; value: number }>;
}

export class SecurityMetricsService {
  private metrics: SecurityMetric[] = [];
  private kpis: KPI[] = [];

  constructor() {
    this.initializeKPIs();
  }

  private initializeKPIs() {
    this.kpis = [
      {
        id: 'mean_time_to_detect',
        name: 'Mean Time to Detect',
        description: 'Average time from security event occurrence to detection',
        target: 5, // 5 minutes
        current: 0,
        trend: 'STABLE',
        status: 'GOOD',
        lastUpdated: new Date(),
        category: 'SECURITY'
      },
      {
        id: 'mean_time_to_respond',
        name: 'Mean Time to Respond',
        description: 'Average time from detection to response initiation',
        target: 15, // 15 minutes
        current: 0,
        trend: 'STABLE',
        status: 'GOOD',
        lastUpdated: new Date(),
        category: 'RESPONSE'
      },
      {
        id: 'incident_resolution_rate',
        name: 'Incident Resolution Rate',
        description: 'Percentage of incidents resolved within SLA',
        target: 95, // 95%
        current: 0,
        trend: 'STABLE',
        status: 'GOOD',
        lastUpdated: new Date(),
        category: 'RESPONSE'
      },
      {
        id: 'alert_accuracy_rate',
        name: 'Alert Accuracy Rate',
        description: 'Percentage of alerts that are not false positives',
        target: 90, // 90%
        current: 0,
        trend: 'STABLE',
        status: 'GOOD',
        lastUpdated: new Date(),
        category: 'MONITORING'
      },
      {
        id: 'system_availability',
        name: 'Security System Availability',
        description: 'Uptime percentage of security monitoring systems',
        target: 99.9, // 99.9%
        current: 0,
        trend: 'STABLE',
        status: 'GOOD',
        lastUpdated: new Date(),
        category: 'MONITORING'
      },
      {
        id: 'critical_event_rate',
        name: 'Critical Event Rate',
        description: 'Number of critical security events per day',
        target: 0, // Target is 0 critical events
        current: 0,
        trend: 'STABLE',
        status: 'GOOD',
        lastUpdated: new Date(),
        category: 'SECURITY'
      }
    ];
  }

  async recordMetric(
    name: string,
    value: number,
    labels: Record<string, string> = {},
    type: SecurityMetric['type'] = 'COUNTER'
  ): Promise<void> {
    const metric: SecurityMetric = {
      id: `${name}_${Date.now()}`,
      name,
      description: `Metric for ${name}`,
      category: this.inferCategory(name),
      type,
      value,
      labels,
      timestamp: new Date()
    };

    this.metrics.push(metric);

    // Keep only last 1000 metrics in memory
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Update relevant KPIs
    await this.updateKPIs(metric);
  }

  private inferCategory(metricName: string): SecurityMetric['category'] {
    const name = metricName.toLowerCase();

    if (name.includes('event') || name.includes('incident')) {
      return 'EVENTS';
    }
    if (name.includes('response') || name.includes('action')) {
      return 'RESPONSES';
    }
    if (name.includes('alert')) {
      return 'ALERTS';
    }
    if (name.includes('performance') || name.includes('time') || name.includes('rate')) {
      return 'PERFORMANCE';
    }

    return 'EVENTS';
  }

  private async updateKPIs(metric: SecurityMetric): Promise<void> {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Filter relevant metrics for KPI calculations
    const recentMetrics = this.metrics.filter(m => m.timestamp >= oneHourAgo);
    const dailyMetrics = this.metrics.filter(m => m.timestamp >= oneDayAgo);

    // Update Mean Time to Detect KPI
    const detectionTimeMetrics = recentMetrics.filter(m =>
      m.name.includes('detection_time') || m.labels.eventType === 'DETECTION'
    );

    if (detectionTimeMetrics.length > 0) {
      const avgDetectionTime = detectionTimeMetrics.reduce((sum, m) => sum + m.value, 0) / detectionTimeMetrics.length;
      const kpi = this.kpis.find(k => k.id === 'mean_time_to_detect');
      if (kpi) {
        kpi.current = avgDetectionTime;
        kpi.lastUpdated = now;
        kpi.status = avgDetectionTime <= kpi.target ? 'GOOD' : avgDetectionTime <= kpi.target * 1.5 ? 'WARNING' : 'CRITICAL';
      }
    }

    // Update Mean Time to Respond KPI
    const responseTimeMetrics = recentMetrics.filter(m =>
      m.name.includes('response_time') || m.labels.eventType === 'RESPONSE'
    );

    if (responseTimeMetrics.length > 0) {
      const avgResponseTime = responseTimeMetrics.reduce((sum, m) => sum + m.value, 0) / responseTimeMetrics.length;
      const kpi = this.kpis.find(k => k.id === 'mean_time_to_respond');
      if (kpi) {
        kpi.current = avgResponseTime;
        kpi.lastUpdated = now;
        kpi.status = avgResponseTime <= kpi.target ? 'GOOD' : avgResponseTime <= kpi.target * 1.5 ? 'WARNING' : 'CRITICAL';
      }
    }

    // Update Incident Resolution Rate KPI
    const resolvedIncidents = dailyMetrics.filter(m =>
      m.name.includes('incident_resolved') && m.value > 0
    ).length;

    const totalIncidents = dailyMetrics.filter(m =>
      m.name.includes('incident_')
    ).length;

    if (totalIncidents > 0) {
      const resolutionRate = (resolvedIncidents / totalIncidents) * 100;
      const kpi = this.kpis.find(k => k.id === 'incident_resolution_rate');
      if (kpi) {
        kpi.current = resolutionRate;
        kpi.lastUpdated = now;
        kpi.status = resolutionRate >= kpi.target ? 'GOOD' : resolutionRate >= kpi.target * 0.8 ? 'WARNING' : 'CRITICAL';
      }
    }

    // Update Alert Accuracy Rate KPI
    const truePositiveAlerts = dailyMetrics.filter(m =>
      m.name.includes('alert_true_positive') && m.value > 0
    ).length;

    const totalAlerts = dailyMetrics.filter(m =>
      m.name.includes('alert_')
    ).length;

    if (totalAlerts > 0) {
      const accuracyRate = (truePositiveAlerts / totalAlerts) * 100;
      const kpi = this.kpis.find(k => k.id === 'alert_accuracy_rate');
      if (kpi) {
        kpi.current = accuracyRate;
        kpi.lastUpdated = now;
        kpi.status = accuracyRate >= kpi.target ? 'GOOD' : accuracyRate >= kpi.target * 0.8 ? 'WARNING' : 'CRITICAL';
      }
    }

    // Update System Availability KPI
    const uptimeMetrics = recentMetrics.filter(m =>
      m.name.includes('system_uptime') || m.name.includes('availability')
    );

    if (uptimeMetrics.length > 0) {
      const avgUptime = uptimeMetrics.reduce((sum, m) => sum + m.value, 0) / uptimeMetrics.length;
      const kpi = this.kpis.find(k => k.id === 'system_availability');
      if (kpi) {
        kpi.current = avgUptime;
        kpi.lastUpdated = now;
        kpi.status = avgUptime >= kpi.target ? 'GOOD' : avgUptime >= kpi.target * 0.99 ? 'WARNING' : 'CRITICAL';
      }
    }

    // Update Critical Event Rate KPI
    const criticalEvents = dailyMetrics.filter(m =>
      m.labels.severity === 'CRITICAL' || m.name.includes('critical')
    ).length;

    const kpi = this.kpis.find(k => k.id === 'critical_event_rate');
    if (kpi) {
      kpi.current = criticalEvents;
      kpi.lastUpdated = now;
      kpi.status = criticalEvents <= kpi.target ? 'GOOD' : criticalEvents <= 5 ? 'WARNING' : 'CRITICAL';
    }
  }

  async getMetrics(filters: {
    category?: SecurityMetric['category'];
    type?: SecurityMetric['type'];
    name?: string;
    startTime?: Date;
    endTime?: Date;
    limit?: number;
  }): Promise<SecurityMetric[]> {
    let filteredMetrics = [...this.metrics];

    if (filters.category) {
      filteredMetrics = filteredMetrics.filter(m => m.category === filters.category);
    }

    if (filters.type) {
      filteredMetrics = filteredMetrics.filter(m => m.type === filters.type);
    }

    if (filters.name) {
      filteredMetrics = filteredMetrics.filter(m =>
        m.name.toLowerCase().includes(filters.name!.toLowerCase())
      );
    }

    if (filters.startTime) {
      filteredMetrics = filteredMetrics.filter(m => m.timestamp >= filters.startTime!);
    }

    if (filters.endTime) {
      filteredMetrics = filteredMetrics.filter(m => m.timestamp <= filters.endTime!);
    }

    // Sort by timestamp descending
    filteredMetrics.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    if (filters.limit) {
      filteredMetrics = filteredMetrics.slice(0, filters.limit);
    }

    return filteredMetrics;
  }

  async getTimeSeries(
    metricName: string,
    labels: Record<string, string> = {},
    startTime: Date,
    endTime: Date,
    intervalMinutes: number = 60
  ): Promise<MetricTimeSeries> {
    const relevantMetrics = this.metrics.filter(m =>
      m.name === metricName &&
      m.timestamp >= startTime &&
      m.timestamp <= endTime &&
      Object.entries(labels).every(([key, value]) => m.labels[key] === value)
    );

    // Group metrics by time intervals
    const intervalMs = intervalMinutes * 60 * 1000;
    const intervals: Array<{ timestamp: Date; value: number }> = [];

    let currentTime = new Date(startTime);
    while (currentTime <= endTime) {
      const intervalEnd = new Date(currentTime.getTime() + intervalMs);
      const intervalMetrics = relevantMetrics.filter(m =>
        m.timestamp >= currentTime && m.timestamp < intervalEnd
      );

      const avgValue = intervalMetrics.length > 0
        ? intervalMetrics.reduce((sum, m) => sum + m.value, 0) / intervalMetrics.length
        : 0;

      intervals.push({
        timestamp: new Date(currentTime),
        value: avgValue
      });

      currentTime = intervalEnd;
    }

    return {
      metric: metricName,
      labels,
      data: intervals
    };
  }

  async getKPIs(category?: KPI['category']): Promise<KPI[]> {
    let filteredKPIs = [...this.kpis];

    if (category) {
      filteredKPIs = filteredKPIs.filter(k => k.category === category);
    }

    return filteredKPIs;
  }

  async getKPI(kpiId: string): Promise<KPI | null> {
    return this.kpis.find(k => k.id === kpiId) || null;
  }

  async updateKPI(kpiId: string, updates: Partial<KPI>): Promise<void> {
    const kpi = this.kpis.find(k => k.id === kpiId);
    if (kpi) {
      Object.assign(kpi, updates, { lastUpdated: new Date() });
    }
  }

  async getMetricSummary(timeframe: 'hour' | 'day' | 'week' | 'month'): Promise<any> {
    const now = new Date();
    const startDate = new Date();

    switch (timeframe) {
      case 'hour':
        startDate.setHours(now.getHours() - 1);
        break;
      case 'day':
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
    }

    const timeframeMetrics = this.metrics.filter(m => m.timestamp >= startDate);

    // Calculate summary statistics
    const totalEvents = timeframeMetrics.filter(m => m.category === 'EVENTS').length;
    const totalResponses = timeframeMetrics.filter(m => m.category === 'RESPONSES').length;
    const totalAlerts = timeframeMetrics.filter(m => m.category === 'ALERTS').length;

    const eventsByType = timeframeMetrics
      .filter(m => m.category === 'EVENTS')
      .reduce((acc, m) => {
        const type = m.labels.eventType || 'UNKNOWN';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    const eventsBySeverity = timeframeMetrics
      .filter(m => m.category === 'EVENTS')
      .reduce((acc, m) => {
        const severity = m.labels.severity || 'UNKNOWN';
        acc[severity] = (acc[severity] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    const avgResponseTime = timeframeMetrics
      .filter(m => m.name.includes('response_time'))
      .reduce((sum, m, _, arr) => sum + m.value, 0) / timeframeMetrics.length || 0;

    return {
      timeframe,
      totalEvents,
      totalResponses,
      totalAlerts,
      eventsByType,
      eventsBySeverity,
      avgResponseTime,
      totalMetrics: timeframeMetrics.length
    };
  }

  // Helper method to record common security metrics
  async recordSecurityEvent(eventType: SecurityEventType, severity: SecurityEventSeverity, userId?: string, serverId?: string): Promise<void> {
    await this.recordMetric('security_event', 1, {
      eventType,
      severity,
      userId: userId || 'anonymous',
      serverId: serverId || 'unknown'
    });
  }

  async recordResponseTime(responseTimeMs: number, eventType: string): Promise<void> {
    await this.recordMetric('response_time', responseTimeMs, {
      eventType,
      unit: 'milliseconds'
    });
  }

  async recordAlertSent(channel: string, success: boolean): Promise<void> {
    await this.recordMetric('alert_sent', 1, {
      channel,
      success: success.toString()
    });
  }

  async recordIncidentResolved(resolutionTimeMs: number, severity: SecurityEventSeverity): Promise<void> {
    await this.recordMetric('incident_resolved', 1, {
      severity,
      resolutionTimeMs: resolutionTimeMs.toString()
    });
  }
}

export const securityMetrics = new SecurityMetricsService();