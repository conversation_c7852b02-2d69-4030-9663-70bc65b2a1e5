import { prisma } from '@/lib/prisma'
import { randomBytes } from 'crypto'
import { NextRequest } from 'next/server'

/**
 * Generate a random string of specified length
 */
function generateRandomString(length: number): string {
  return randomBytes(length).toString('hex')
}

/**
 * Generate an authorization code
 */
export async function generateAuthorizationCode(
  remoteServerId: string,
  userId: string,
  redirectUri: string,
  scope: string
): Promise<{ code: string; expiresAt: Date }> {
  console.log('=== Generating Authorization Code ===');
  console.log('Parameters:', { remoteServerId, userId, redirectUri, scope });
  
  const code = generateRandomString(32)
  const expiresAt = new Date(Date.now() + 30 * 60 * 1000) // 30 minutes
  
  console.log('Generated code:', code);
  console.log('Expires at:', expiresAt);
  
  try {
    // Store the authorization code in the database using raw query
    console.log('Inserting authorization code into database...');
    console.log('Parameters:', { remoteServerId, userId, code, redirectUri, scope, expiresAt });
    
    // Use a static ID for testing to avoid potential issues with gen_random_uuid()
    const staticId = 'test-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    console.log('Generated static ID:', staticId);
    
    const result = await prisma.authorizationCode.create({
      data: {
        id: staticId,
        remoteServerId,
        userId,
        code,
        redirectUri: redirectUri || null,
        scope: scope || null,
        expiresAt,
      }
    })
    console.log('Authorization code inserted successfully, result:', result);
    return { code, expiresAt }
  } catch (error: any) {
    console.error('Error inserting authorization code:', error);
    console.error('Error type:', typeof error);
    console.error('Error message:', error.message || 'No message');
    console.error('Error code:', error.code || 'No code');
    console.error('Error detail:', error.detail || 'No detail');
    console.error('Error hint:', error.hint || 'No hint');
    throw error;
  }
}

/**
 * Generate an access token
 */
export async function generateAccessToken(
  remoteServerId: string,
  userId: string,
  scope: string
): Promise<{ token: string; expiresAt: Date }> {
  const token = generateRandomString(32)
  const expiresAt = new Date(Date.now() + 60 * 60 * 1000) // 1 hour
  
  return { token, expiresAt }
}

/**
 * Generate a refresh token
 */
export async function generateRefreshToken(
  remoteServerId: string,
  userId: string,
  scope: string
): Promise<{ token: string; expiresAt: Date }> {
  const token = generateRandomString(32)
  const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
  
  return { token, expiresAt }
}

/**
 * Extract token from Authorization header
 */
export function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization')
  
  if (!authHeader) {
    return null
  }
  
  const [type, token] = authHeader.split(' ')
  
  if (type !== 'Bearer' || !token) {
    return null
  }
  
  return token
}

/**
 * Validate an access token
 */
export async function validateAccessToken(token: string): Promise<boolean> {
  try {
    const oauthTokens: any = await prisma.$queryRaw`
      SELECT * FROM oauth_tokens WHERE access_token = ${token}
    `
    const oauthToken = oauthTokens.length > 0 ? oauthTokens[0] : null
    
    if (!oauthToken) {
      return false
    }
    
    // Check if the token has expired
    if (oauthToken.expires_at < new Date()) {
      return false
    }
    
    return true
  } catch (error) {
    console.error('Error validating access token:', error)
    return false
  }
}