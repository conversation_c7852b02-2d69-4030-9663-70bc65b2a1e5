import { prisma } from '@/lib/prisma'

interface ServerAssignmentAuditData {
  userId: string
  roleId?: string
  serverId: string
  serverName: string
  action: 'assign' | 'remove' | 'bulk_assign' | 'bulk_remove'
  permissions?: string[]
  autoGrantPermissions?: string[]
  notes?: string
  performedBy: string
  metadata?: Record<string, any>
}

interface AuditLogEntry {
  id: string
  userId?: string
  action: string
  resource: string
  resourceId?: string
  oldValues?: any
  newValues?: any
  ipAddress?: string
  userAgent?: string
  timestamp: Date
  success: boolean
  statusCode?: number
  errorMessage?: string
  requestId?: string
  duration?: number
  requestSize?: number
  responseSize?: number
  metadata?: any
  projectId?: string
  apiEndpoint?: string
  requestMethod?: string
  country_code?: string
  city?: string
  remote_server_id?: string
  project?: any
  remote_servers?: any
  user?: any
}

export class ServerAssignmentAuditService {
  /**
   * Log a server assignment operation
   */
  async logServerAssignment(auditData: ServerAssignmentAuditData) {
    try {
      const auditEntry = await prisma.auditLog.create({
        data: {
          userId: auditData.userId || auditData.roleId,
          action: `server_${auditData.action}`,
          resource: auditData.roleId ? 'role_remote_server_access' : 'user_remote_server_access',
          resourceId: auditData.serverId,
          oldValues: undefined, // For assignments, we don't track old values
          newValues: {
            serverId: auditData.serverId,
            serverName: auditData.serverName,
            permissions: auditData.permissions,
            autoGrantPermissions: auditData.autoGrantPermissions,
            notes: auditData.notes,
            assignedTo: auditData.roleId ? 'role' : 'user',
            roleId: auditData.roleId,
            userId: auditData.userId
          },
          timestamp: new Date(),
          success: true,
          metadata: {
            operation: auditData.action,
            entityType: auditData.roleId ? 'role' : 'user',
            entityId: auditData.roleId || auditData.userId,
            ...auditData.metadata
          },
          remote_server_id: auditData.serverId
        }
      })

      console.log(`Server assignment audit logged: ${auditData.action} for ${auditData.roleId ? 'role' : 'user'} ${auditData.roleId || auditData.userId} to server ${auditData.serverName}`)
      return { success: true, auditId: auditEntry.id }

    } catch (error: any) {
      console.error('Failed to log server assignment audit:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Log a server removal operation
   */
  async logServerRemoval(auditData: ServerAssignmentAuditData) {
    try {
      const auditEntry = await prisma.auditLog.create({
        data: {
          userId: auditData.userId || auditData.roleId,
          action: 'server_remove',
          resource: auditData.roleId ? 'role_remote_server_access' : 'user_remote_server_access',
          resourceId: auditData.serverId,
          oldValues: {
            serverId: auditData.serverId,
            serverName: auditData.serverName,
            removedFrom: auditData.roleId ? 'role' : 'user',
            roleId: auditData.roleId,
            userId: auditData.userId
          },
          newValues: undefined, // For removals, we don't track new values
          timestamp: new Date(),
          success: true,
          metadata: {
            operation: 'remove',
            entityType: auditData.roleId ? 'role' : 'user',
            entityId: auditData.roleId || auditData.userId,
            ...auditData.metadata
          },
          remote_server_id: auditData.serverId
        }
      })

      console.log(`Server removal audit logged: removed ${auditData.roleId ? 'role' : 'user'} ${auditData.roleId || auditData.userId} from server ${auditData.serverName}`)
      return { success: true, auditId: auditEntry.id }

    } catch (error) {
      console.error('Failed to log server removal audit:', error)
      return { success: false, error: error instanceof Error ? error.message : String(error) }
    }
  }

  /**
   * Log a bulk server assignment operation
   */
  async logBulkServerAssignment(auditData: ServerAssignmentAuditData & { serverCount: number }) {
    try {
      const auditEntry = await prisma.auditLog.create({
        data: {
          userId: auditData.userId || auditData.roleId,
          action: 'server_bulk_assign',
          resource: 'bulk_server_assignment',
          resourceId: `bulk-${Date.now()}`, // Generate a unique ID for bulk operations
          oldValues: undefined,
          newValues: {
            serverCount: auditData.serverCount,
            servers: auditData.metadata?.serverIds || [],
            permissions: auditData.permissions,
            autoGrantPermissions: auditData.autoGrantPermissions,
            assignedTo: auditData.roleId ? 'role' : 'user',
            roleId: auditData.roleId,
            userId: auditData.userId
          },
          timestamp: new Date(),
          success: true,
          metadata: {
            operation: 'bulk_assign',
            entityType: auditData.roleId ? 'role' : 'user',
            entityId: auditData.roleId || auditData.userId,
            bulkOperation: true,
            ...auditData.metadata
          }
        }
      })

      console.log(`Bulk server assignment audit logged: assigned ${auditData.roleId ? 'role' : 'user'} ${auditData.roleId || auditData.userId} to ${auditData.serverCount} servers`)
      return { success: true, auditId: auditEntry.id }

    } catch (error) {
      console.error('Failed to log bulk server assignment audit:', error)
      return { success: false, error: error instanceof Error ? error.message : String(error) }
    }
  }

  /**
   * Log a bulk server removal operation
   */
  async logBulkServerRemoval(auditData: ServerAssignmentAuditData & { serverCount: number }) {
    try {
      const auditEntry = await prisma.auditLog.create({
        data: {
          userId: auditData.userId || auditData.roleId,
          action: 'server_bulk_remove',
          resource: 'bulk_server_removal',
          resourceId: `bulk-${Date.now()}`, // Generate a unique ID for bulk operations
          oldValues: {
            serverCount: auditData.serverCount,
            servers: auditData.metadata?.serverIds || [],
            removedFrom: auditData.roleId ? 'role' : 'user',
            roleId: auditData.roleId,
            userId: auditData.userId
          },
          newValues: undefined,
          timestamp: new Date(),
          success: true,
          metadata: {
            operation: 'bulk_remove',
            entityType: auditData.roleId ? 'role' : 'user',
            entityId: auditData.roleId || auditData.userId,
            bulkOperation: true,
            ...auditData.metadata
          }
        }
      })

      console.log(`Bulk server removal audit logged: removed ${auditData.roleId ? 'role' : 'user'} ${auditData.roleId || auditData.userId} from ${auditData.serverCount} servers`)
      return { success: true, auditId: auditEntry.id }

    } catch (error) {
      console.error('Failed to log bulk server removal audit:', error)
      return { success: false, error: error instanceof Error ? error.message : String(error) }
    }
  }

  /**
   * Get audit logs for server assignments
   */
  async getServerAssignmentAuditLogs(filters: {
    userId?: string
    roleId?: string
    serverId?: string
    action?: string
    limit?: number
    offset?: number
  } = {}) {
    try {
      const where: any = {
        action: {
          in: ['server_assign', 'server_remove', 'server_bulk_assign', 'server_bulk_remove']
        }
      }

      if (filters.userId) {
        where.userId = filters.userId
      }

      if (filters.serverId) {
        where.remote_server_id = filters.serverId
      }

      if (filters.action) {
        where.action = filters.action
      }

      const [logs, total] = await Promise.all([
        prisma.auditLog.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            remote_servers: {
              select: {
                id: true,
                name: true,
                url: true
              }
            }
          },
          orderBy: {
            timestamp: 'desc'
          },
          take: filters.limit || 50,
          skip: filters.offset || 0
        }),
        prisma.auditLog.count({ where })
      ])

      return {
        success: true,
        logs,
        total,
        pagination: {
          limit: filters.limit || 50,
          offset: filters.offset || 0,
          hasMore: (filters.offset || 0) + (filters.limit || 50) < total
        }
      }

    } catch (error: any) {
      console.error('Failed to get server assignment audit logs:', error)
      return { success: false, error: error.message, logs: [], total: 0 }
    }
  }

  /**
   * Get audit statistics for server assignments
   */
  async getServerAssignmentStats(timeframe: 'day' | 'week' | 'month' = 'week') {
    try {
      const dateFilter = new Date()
      switch (timeframe) {
        case 'day':
          dateFilter.setDate(dateFilter.getDate() - 1)
          break
        case 'week':
          dateFilter.setDate(dateFilter.getDate() - 7)
          break
        case 'month':
          dateFilter.setMonth(dateFilter.getMonth() - 1)
          break
      }

      const stats = await prisma.auditLog.groupBy({
        by: ['action', 'success'],
        where: {
          action: {
            in: ['server_assign', 'server_remove', 'server_bulk_assign', 'server_bulk_remove']
          },
          timestamp: {
            gte: dateFilter
          }
        },
        _count: {
          action: true
        }
      })

      const total = stats.reduce((sum, stat) => sum + stat._count.action, 0)
      const successful = stats.filter(s => s.success).reduce((sum, stat) => sum + stat._count.action, 0)
      const failed = stats.filter(s => !s.success).reduce((sum, stat) => sum + stat._count.action, 0)

      return {
        success: true,
        stats: {
          total,
          successful,
          failed,
          successRate: total > 0 ? Math.round((successful / total) * 100) : 0
        },
        timeframe,
        period: {
          from: dateFilter,
          to: new Date()
        }
      }

    } catch (error: any) {
      console.error('Failed to get server assignment stats:', error)
      return { success: false, error: error.message, stats: null }
    }
  }
}