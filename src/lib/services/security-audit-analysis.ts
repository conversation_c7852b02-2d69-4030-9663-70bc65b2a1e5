'use client';

import { SecurityEventType, SecurityEventSeverity } from './security-event-detection';

export interface AuditPattern {
  id: string;
  name: string;
  description: string;
  pattern: string;
  severity: SecurityEventSeverity;
  confidence: number;
  occurrences: number;
  firstSeen: Date;
  lastSeen: Date;
  affectedUsers: string[];
  affectedIPs: string[];
  recommendations: string[];
}

export interface AnomalyDetectionResult {
  id: string;
  type: 'TEMPORAL' | 'SPATIAL' | 'BEHAVIORAL' | 'VOLUME';
  description: string;
  severity: SecurityEventSeverity;
  confidence: number;
  timestamp: Date;
  data: Record<string, any>;
  threshold: number;
  actualValue: number;
  expectedValue: number;
}

export interface TrendAnalysis {
  metric: string;
  timeframe: string;
  trend: 'INCREASING' | 'DECREASING' | 'STABLE' | 'VOLATILE';
  changePercent: number;
  dataPoints: Array<{ timestamp: Date; value: number }>;
  prediction?: {
    nextValue: number;
    confidence: number;
    timeframe: string;
  };
}

export interface SecurityReport {
  id: string;
  title: string;
  description: string;
  type: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'INCIDENT' | 'COMPLIANCE';
  generatedAt: Date;
  generatedBy: string;
  timeframe: {
    start: Date;
    end: Date;
  };
  sections: ReportSection[];
  summary: string;
  recommendations: string[];
  metadata: Record<string, any>;
}

export interface ReportSection {
  title: string;
  type: 'SUMMARY' | 'METRICS' | 'EVENTS' | 'TRENDS' | 'RECOMMENDATIONS';
  content: any;
  charts?: ChartData[];
}

export interface ChartData {
  type: 'LINE' | 'BAR' | 'PIE' | 'AREA';
  title: string;
  data: any;
  options?: Record<string, any>;
}

export class SecurityAuditAnalysisService {
  private patterns: AuditPattern[] = [];
  private anomalies: AnomalyDetectionResult[] = [];

  constructor() {
    this.initializePatterns();
  }

  private initializePatterns() {
    this.patterns = [
      {
        id: 'suspicious_login_pattern',
        name: 'Suspicious Login Pattern',
        description: 'Multiple failed login attempts followed by successful login',
        pattern: 'FAILED_LOGIN+ -> SUCCESSFUL_LOGIN',
        severity: SecurityEventSeverity.HIGH,
        confidence: 0.8,
        occurrences: 0,
        firstSeen: new Date(),
        lastSeen: new Date(),
        affectedUsers: [],
        affectedIPs: [],
        recommendations: [
          'Review user account activity',
          'Consider implementing additional authentication factors',
          'Monitor for similar patterns across other accounts'
        ]
      },
      {
        id: 'data_exfiltration_pattern',
        name: 'Data Exfiltration Pattern',
        description: 'Large data downloads followed by account termination',
        pattern: 'LARGE_DATA_ACCESS -> ACCOUNT_TERMINATION',
        severity: SecurityEventSeverity.CRITICAL,
        confidence: 0.9,
        occurrences: 0,
        firstSeen: new Date(),
        lastSeen: new Date(),
        affectedUsers: [],
        affectedIPs: [],
        recommendations: [
          'Immediately investigate data access',
          'Review data retention policies',
          'Implement data loss prevention measures'
        ]
      },
      {
        id: 'privilege_escalation_pattern',
        name: 'Privilege Escalation Pattern',
        description: 'User accessing resources above their permission level',
        pattern: 'NORMAL_ACCESS -> PRIVILEGED_ACCESS',
        severity: SecurityEventSeverity.CRITICAL,
        confidence: 0.85,
        occurrences: 0,
        firstSeen: new Date(),
        lastSeen: new Date(),
        affectedUsers: [],
        affectedIPs: [],
        recommendations: [
          'Review user role assignments',
          'Audit permission changes',
          'Implement least privilege principle'
        ]
      }
    ];
  }

  async analyzeAuditTrail(auditEntries: any[]): Promise<{
    patterns: AuditPattern[];
    anomalies: AnomalyDetectionResult[];
    trends: TrendAnalysis[];
    insights: string[];
  }> {
    const patterns = await this.detectPatterns(auditEntries);
    const anomalies = await this.detectAnomalies(auditEntries);
    const trends = await this.analyzeTrends(auditEntries);
    const insights = await this.generateInsights(patterns, anomalies, trends);

    return {
      patterns,
      anomalies,
      trends,
      insights
    };
  }

  private async detectPatterns(auditEntries: any[]): Promise<AuditPattern[]> {
    const detectedPatterns: AuditPattern[] = [];

    for (const pattern of this.patterns) {
      const matches = this.findPatternMatches(auditEntries, pattern);
      if (matches.length > 0) {
        const updatedPattern = {
          ...pattern,
          occurrences: matches.length,
          lastSeen: new Date(),
          affectedUsers: [...new Set(matches.map(m => m.userId).filter(Boolean))],
          affectedIPs: [...new Set(matches.map(m => m.ipAddress).filter(Boolean))]
        };
        detectedPatterns.push(updatedPattern);
      }
    }

    return detectedPatterns;
  }

  private findPatternMatches(auditEntries: any[], pattern: AuditPattern): any[] {
    // Simple pattern matching logic
    // In a real implementation, this would use more sophisticated pattern recognition
    const matches: any[] = [];

    // For demonstration, we'll look for sequences of events
    for (let i = 0; i < auditEntries.length - 1; i++) {
      const current = auditEntries[i];
      const next = auditEntries[i + 1];

      // Check for suspicious login pattern
      if (pattern.id === 'suspicious_login_pattern') {
        if (current.action === 'LOGIN_FAILED' && next.action === 'LOGIN_SUCCESS' &&
            current.ipAddress === next.ipAddress &&
            Math.abs(new Date(current.timestamp).getTime() - new Date(next.timestamp).getTime()) < 300000) { // 5 minutes
          matches.push({ ...current, relatedEvent: next });
        }
      }

      // Check for data exfiltration pattern
      if (pattern.id === 'data_exfiltration_pattern') {
        if (current.action === 'DATA_ACCESS' && current.metadata?.dataSize > 1000000 && // 1MB
            next.action === 'ACCOUNT_TERMINATION' && current.userId === next.userId) {
          matches.push({ ...current, relatedEvent: next });
        }
      }

      // Check for privilege escalation pattern
      if (pattern.id === 'privilege_escalation_pattern') {
        if (current.resource !== 'admin' && next.resource === 'admin' &&
            current.userId === next.userId &&
            Math.abs(new Date(current.timestamp).getTime() - new Date(next.timestamp).getTime()) < 3600000) { // 1 hour
          matches.push({ ...current, relatedEvent: next });
        }
      }
    }

    return matches;
  }

  private async detectAnomalies(auditEntries: any[]): Promise<AnomalyDetectionResult[]> {
    const anomalies: AnomalyDetectionResult[] = [];

    // Temporal anomaly detection
    const hourlyActivity = this.groupByHour(auditEntries);
    const avgHourlyActivity = hourlyActivity.reduce((sum, count) => sum + count, 0) / hourlyActivity.length;

    for (let i = 0; i < hourlyActivity.length; i++) {
      if (hourlyActivity[i] > avgHourlyActivity * 3) { // 3x normal activity
        anomalies.push({
          id: `temporal_anomaly_${i}`,
          type: 'TEMPORAL',
          description: `Unusual activity spike in hour ${i}: ${hourlyActivity[i]} events vs average ${avgHourlyActivity.toFixed(1)}`,
          severity: SecurityEventSeverity.HIGH,
          confidence: 0.8,
          timestamp: new Date(),
          data: { hour: i, actual: hourlyActivity[i], expected: avgHourlyActivity },
          threshold: avgHourlyActivity * 3,
          actualValue: hourlyActivity[i],
          expectedValue: avgHourlyActivity
        });
      }
    }

    // Spatial anomaly detection (unusual IP addresses)
    const ipCounts: Record<string, number> = auditEntries.reduce((acc, entry) => {
      acc[entry.ipAddress] = (acc[entry.ipAddress] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const avgIPActivity = Object.values(ipCounts).reduce((sum: number, count: number) => sum + count, 0) / Object.keys(ipCounts).length;

    for (const [ip, count] of Object.entries(ipCounts)) {
      if (count > avgIPActivity * 5) { // 5x normal activity from single IP
        anomalies.push({
          id: `spatial_anomaly_${ip}`,
          type: 'SPATIAL',
          description: `Unusual activity from IP ${ip}: ${count} events vs average ${avgIPActivity.toFixed(1)}`,
          severity: SecurityEventSeverity.MEDIUM,
          confidence: 0.7,
          timestamp: new Date(),
          data: { ip, actual: count, expected: avgIPActivity },
          threshold: avgIPActivity * 5,
          actualValue: count,
          expectedValue: avgIPActivity
        });
      }
    }

    // Behavioral anomaly detection
    const userActivity: Record<string, number> = auditEntries.reduce((acc, entry) => {
      if (entry.userId) {
        acc[entry.userId] = (acc[entry.userId] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const avgUserActivity = Object.values(userActivity).reduce((sum, count) => sum + count, 0) / Object.keys(userActivity).length;

    for (const [userId, count] of Object.entries(userActivity)) {
      if (count > avgUserActivity * 4) { // 4x normal activity for user
        anomalies.push({
          id: `behavioral_anomaly_${userId}`,
          type: 'BEHAVIORAL',
          description: `Unusual user activity for ${userId}: ${count} events vs average ${avgUserActivity.toFixed(1)}`,
          severity: SecurityEventSeverity.HIGH,
          confidence: 0.75,
          timestamp: new Date(),
          data: { userId, actual: count, expected: avgUserActivity },
          threshold: avgUserActivity * 4,
          actualValue: count,
          expectedValue: avgUserActivity
        });
      }
    }

    return anomalies;
  }

  private groupByHour(auditEntries: any[]): number[] {
    const hourlyCounts = new Array(24).fill(0);

    auditEntries.forEach(entry => {
      const hour = new Date(entry.timestamp).getHours();
      hourlyCounts[hour]++;
    });

    return hourlyCounts;
  }

  private async analyzeTrends(auditEntries: any[]): Promise<TrendAnalysis[]> {
    const trends: TrendAnalysis[] = [];

    // Analyze event volume trend
    const dailyCounts = this.groupByDay(auditEntries);
    const trend = this.calculateTrend(dailyCounts.map(d => d.count));

    trends.push({
      metric: 'security_events',
      timeframe: 'daily',
      trend: trend.direction,
      changePercent: trend.changePercent,
      dataPoints: dailyCounts.map((item, index) => ({
        timestamp: new Date(Date.now() - (dailyCounts.length - 1 - index) * 24 * 60 * 60 * 1000),
        value: item.count
      })),
      prediction: this.predictNextValue(dailyCounts.map(d => d.count))
    });

    // Analyze weekly trends
    const weeklyCounts = this.groupByWeek(auditEntries);
    const weeklyTrend = this.calculateTrend(weeklyCounts.map(d => d.count));

    trends.push({
      metric: 'security_events',
      timeframe: 'weekly',
      trend: weeklyTrend.direction,
      changePercent: weeklyTrend.changePercent,
      dataPoints: weeklyCounts.map((item, index) => ({
        timestamp: new Date(Date.now() - (weeklyCounts.length - 1 - index) * 7 * 24 * 60 * 60 * 1000),
        value: item.count
      })),
      prediction: this.predictNextValue(weeklyCounts.map(d => d.count))
    });

    // Analyze severity trend
    const severityCounts = this.groupBySeverity(auditEntries);
    const severityTrend = this.calculateTrend(Object.values(severityCounts).flat());

    trends.push({
      metric: 'high_severity_events',
      timeframe: 'daily',
      trend: severityTrend.direction,
      changePercent: severityTrend.changePercent,
      dataPoints: Object.entries(severityCounts).map(([severity, counts]) => ({
        timestamp: new Date(),
        value: counts.length
      }))
    });

    return trends;
  }

  private groupByDay(auditEntries: any[]): Array<{ date: Date; count: number }> {
    const dailyMap = new Map<string, number>();

    auditEntries.forEach(entry => {
      const date = new Date(entry.timestamp).toDateString();
      dailyMap.set(date, (dailyMap.get(date) || 0) + 1);
    });

    return Array.from(dailyMap.entries())
      .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
      .map(([date, count]) => ({ date: new Date(date), count }));
  }

  private groupByWeek(auditEntries: any[]): Array<{ week: Date; count: number }> {
    const weeklyMap = new Map<string, number>();

    auditEntries.forEach(entry => {
      const date = new Date(entry.timestamp);
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay()); // Start of week (Sunday)
      weekStart.setHours(0, 0, 0, 0);
      const weekKey = weekStart.toDateString();
      weeklyMap.set(weekKey, (weeklyMap.get(weekKey) || 0) + 1);
    });

    return Array.from(weeklyMap.entries())
      .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
      .map(([week, count]) => ({ week: new Date(week), count }));
  }

  private groupBySeverity(auditEntries: any[]): Record<string, number[]> {
    const severityMap: Record<string, number[]> = {
      LOW: [],
      MEDIUM: [],
      HIGH: [],
      CRITICAL: []
    };

    auditEntries.forEach(entry => {
      const severity = entry.severity || 'MEDIUM';
      severityMap[severity].push(1);
    });

    return severityMap;
  }

  private calculateTrend(data: number[]): { direction: TrendAnalysis['trend']; changePercent: number } {
    if (data.length < 2) {
      return { direction: 'STABLE', changePercent: 0 };
    }

    const firstHalf = data.slice(0, Math.floor(data.length / 2));
    const secondHalf = data.slice(Math.floor(data.length / 2));

    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;

    const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100;

    let direction: TrendAnalysis['trend'] = 'STABLE';
    if (changePercent > 10) direction = 'INCREASING';
    else if (changePercent < -10) direction = 'DECREASING';
    else if (Math.abs(changePercent) > 25) direction = 'VOLATILE';

    return { direction, changePercent };
  }

  private predictNextValue(data: number[]): { nextValue: number; confidence: number; timeframe: string } | undefined {
    if (data.length < 3) return undefined;

    // Simple linear regression for prediction
    const n = data.length;
    const sumX = data.reduce((sum, _, i) => sum + i, 0);
    const sumY = data.reduce((sum, val) => sum + val, 0);
    const sumXY = data.reduce((sum, val, i) => sum + val * i, 0);
    const sumXX = data.reduce((sum, _, i) => sum + i * i, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    const nextValue = intercept + slope * n;
    const confidence = Math.min(0.8, data.length / 10); // Confidence increases with more data

    return {
      nextValue: Math.max(0, nextValue), // Ensure non-negative
      confidence,
      timeframe: 'next_period'
    };
  }

  private async generateInsights(
    patterns: AuditPattern[],
    anomalies: AnomalyDetectionResult[],
    trends: TrendAnalysis[]
  ): Promise<string[]> {
    const insights: string[] = [];

    // Pattern insights
    patterns.forEach(pattern => {
      insights.push(`Detected ${pattern.name} pattern with ${pattern.occurrences} occurrences. Severity: ${pattern.severity}`);
    });

    // Anomaly insights
    anomalies.forEach(anomaly => {
      insights.push(`Anomaly detected: ${anomaly.description}. Confidence: ${(anomaly.confidence * 100).toFixed(1)}%`);
    });

    // Trend insights
    trends.forEach(trend => {
      insights.push(`${trend.metric} shows ${trend.trend.toLowerCase()} trend with ${trend.changePercent.toFixed(1)}% change`);
    });

    // General insights
    if (patterns.some(p => p.severity === SecurityEventSeverity.CRITICAL)) {
      insights.push('Critical security patterns detected. Immediate attention required.');
    }

    if (anomalies.some(a => a.severity === SecurityEventSeverity.CRITICAL)) {
      insights.push('Critical anomalies detected. Investigation recommended.');
    }

    if (trends.some(t => t.trend === 'INCREASING' && t.metric.includes('event'))) {
      insights.push('Increasing security event trend detected. Monitor closely.');
    }

    return insights;
  }

  async generateReport(
    type: SecurityReport['type'],
    title: string,
    description: string,
    startDate: Date,
    endDate: Date,
    generatedBy: string
  ): Promise<SecurityReport> {
    // In a real implementation, this would gather data and generate a comprehensive report
    const report: SecurityReport = {
      id: `report_${Date.now()}`,
      title,
      description,
      type,
      generatedAt: new Date(),
      generatedBy,
      timeframe: { start: startDate, end: endDate },
      sections: [
        {
          title: 'Executive Summary',
          type: 'SUMMARY',
          content: 'This report provides an overview of security events and incidents during the specified period.'
        },
        {
          title: 'Key Metrics',
          type: 'METRICS',
          content: {
            totalEvents: 0,
            criticalEvents: 0,
            resolvedIncidents: 0,
            averageResponseTime: 0
          }
        },
        {
          title: 'Security Events',
          type: 'EVENTS',
          content: []
        },
        {
          title: 'Trends and Patterns',
          type: 'TRENDS',
          content: []
        },
        {
          title: 'Recommendations',
          type: 'RECOMMENDATIONS',
          content: [
            'Continue monitoring for unusual patterns',
            'Review access controls regularly',
            'Ensure timely incident response'
          ]
        }
      ],
      summary: 'Security report generated successfully',
      recommendations: [
        'Implement additional monitoring for detected patterns',
        'Review and update security policies as needed',
        'Conduct regular security awareness training'
      ],
      metadata: {
        reportType: type,
        generatedAt: new Date().toISOString()
      }
    };

    return report;
  }

  async getAnalysisHistory(filters: {
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }): Promise<any[]> {
    // In a real implementation, this would fetch from database
    return [];
  }

  async exportAnalysis(format: 'JSON' | 'CSV' | 'PDF', data: any): Promise<Blob> {
    // In a real implementation, this would generate the export file
    const exportData = JSON.stringify(data, null, 2);
    return new Blob([exportData], { type: 'application/json' });
  }
}

export const securityAuditAnalysis = new SecurityAuditAnalysisService();