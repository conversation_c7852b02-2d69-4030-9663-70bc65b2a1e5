import { prisma } from '@/lib/prisma'
import { RemoteServerNotificationService } from './remote-server-notification-service'

interface HealthCheckResult {
  success: boolean
  serverId: string
  serverName: string
  status: 'online' | 'offline' | 'error'
  responseTime?: number
  error?: string
  timestamp: string
}

interface ServerStats {
  cpuUsage: number
  memoryUsage: number
  totalMemory: number
  diskUsage: number
  totalDisk: number
  loadAverage: number[]
  network: {
    inbound: number
    outbound: number
  }
  uptime: string
  location: string
}

interface HealthCheckSummary {
  total: number
  online: number
  offline: number
  errors: number
  results: HealthCheckResult[]
}

export class RemoteServerHealthService {
  /**
   * Perform a health check on a specific remote server
   */
  async checkServerHealth(serverId: string): Promise<HealthCheckResult> {
    const startTime = Date.now()

    // Get the remote server
    const server = await prisma.remoteServer.findUnique({
      where: { id: serverId }
    })

    if (!server) {
      return {
        success: false,
        serverId,
        serverName: 'Unknown',
        status: 'error',
        error: 'Remote server not found',
        timestamp: new Date().toISOString()
      }
    }

    // Use a lightweight health endpoint for health checks
    const apiEndpoint = server.apiEndpoint || server.url
    const healthCheckUrl = `${apiEndpoint}/api/server-stats`

    console.log(`Performing health check for server: ${server.name} (${serverId})`)
    console.log(`Health check URL: ${healthCheckUrl}`)

    try {
       // Use HEAD request for lightweight health check (only check if endpoint is accessible)
       const response = await fetch(healthCheckUrl, {
         method: 'HEAD',
         headers: {
           'Authorization': `Bearer nwa_2b27afce1b778ef721b2097b2a8d13a2c29cebb60a81b2b0`,
         },
         // Set a reasonable timeout
         signal: AbortSignal.timeout(3000) // 3 second timeout for health checks
       })

      const responseTime = Date.now() - startTime

      if (response.ok) {
        return {
          success: true,
          serverId,
          serverName: server.name,
          status: 'online',
          responseTime,
          timestamp: new Date().toISOString()
        }
      } else {
        const result = {
          success: false,
          serverId,
          serverName: server.name,
          status: 'offline' as const,
          responseTime,
          error: `HTTP ${response.status}: ${response.statusText}`,
          timestamp: new Date().toISOString()
        }

        // Notify admins about the health check failure
        try {
          const notificationService = new RemoteServerNotificationService()
          await notificationService.notifyHealthCheckFailure({
            serverId,
            serverName: server.name,
            status: 'offline',
            error: result.error,
            responseTime,
            timestamp: result.timestamp
          })
        } catch (notificationError) {
          console.error('Failed to send health check failure notification:', notificationError)
        }

        return result
      }

    } catch (error: any) {
      const responseTime = Date.now() - startTime

      console.error(`Health check failed for server ${serverId}:`, error)

      let errorMessage = 'Unknown error'
      if (error.name === 'AbortError') {
        errorMessage = 'Request timeout'
      } else if (error.message) {
        errorMessage = error.message
      }

      const result = {
        success: false,
        serverId,
        serverName: server.name,
        status: 'error' as const,
        responseTime,
        error: errorMessage,
        timestamp: new Date().toISOString()
      }

      // Notify admins about the health check error
      try {
        const notificationService = new RemoteServerNotificationService()
        await notificationService.notifyHealthCheckFailure({
          serverId,
          serverName: server.name,
          status: 'error',
          error: errorMessage,
          responseTime,
          timestamp: result.timestamp
        })
      } catch (notificationError) {
        console.error('Failed to send health check error notification:', notificationError)
      }

      return result
    }
  }

  /**
   * Get server statistics from a remote server
   */
  async getServerStats(serverId: string): Promise<ServerStats | null> {
    // Get the remote server
    const server = await prisma.remoteServer.findUnique({
      where: { id: serverId }
    })

    if (!server) {
      console.error(`Server not found: ${serverId}`)
      return null
    }

    // Use the server-stats endpoint for detailed statistics
    const apiEndpoint = server.apiEndpoint || server.url
    const statsUrl = `${apiEndpoint}/api/server-stats`

    console.log(`Getting server stats for: ${server.name} (${serverId})`)
    console.log(`Stats URL: ${statsUrl}`)

    try {
      const response = await fetch(statsUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer nwa_2b27afce1b778ef721b2097b2a8d13a2c29cebb60a81b2b0`,
          'Content-Type': 'application/json'
        },
        signal: AbortSignal.timeout(5000) // 5 second timeout for stats
      })

      if (response.ok) {
        const statsData = await response.json()

        // Parse the server stats data
        const cpuUsage = parseFloat(statsData.cpu_usage?.replace('%', '') || '0')
        const memoryMatch = statsData.memory?.match(/(\d+\.?\d*)% \((\d+\.?\d*)GB total\)/)
        const memoryUsage = memoryMatch ? parseFloat(memoryMatch[1]) : 0
        const totalMemory = memoryMatch ? parseFloat(memoryMatch[2]) : 0
        const diskMatch = statsData.disk_usage?.match(/(\d+)% \((\d+)GB total\)/)
        const diskUsage = diskMatch ? parseInt(diskMatch[1]) : 0
        const totalDisk = diskMatch ? parseInt(diskMatch[2]) : 0
        const loadAverage = statsData.load_average?.split(',').map((load: string) => parseFloat(load.trim())) || [0, 0, 0]
        const networkMatch = statsData.network?.match(/(\d+)↓\/(\d+)↑/)
        const networkInbound = networkMatch ? parseInt(networkMatch[1]) : 0
        const networkOutbound = networkMatch ? parseInt(networkMatch[2]) : 0

        return {
          cpuUsage,
          memoryUsage,
          totalMemory,
          diskUsage,
          totalDisk,
          loadAverage,
          network: {
            inbound: networkInbound,
            outbound: networkOutbound
          },
          uptime: statsData.uptime || 'Unknown',
          location: statsData.location || 'Unknown'
        }
      } else {
        console.error(`Failed to get stats for server ${serverId}: HTTP ${response.status}`)
        return null
      }

    } catch (error: any) {
      console.error(`Error getting stats for server ${serverId}:`, error)
      return null
    }
  }

  /**
   * Perform health checks on all active remote servers
   */
  async checkAllServersHealth(): Promise<HealthCheckSummary> {
    try {
      // Get all active remote servers
      const servers = await prisma.remoteServer.findMany({
        where: { isActive: true }
      })

      console.log(`Performing health checks for ${servers.length} active servers`)

      const results: HealthCheckResult[] = []

      // Check each server sequentially to avoid overwhelming the system
      for (const server of servers) {
        const result = await this.checkServerHealth(server.id)
        results.push(result)

        // Add a small delay between checks to be respectful to remote servers
        await new Promise(resolve => setTimeout(resolve, 500))
      }

      const online = results.filter(r => r.status === 'online').length
      const offline = results.filter(r => r.status === 'offline').length
      const errors = results.filter(r => r.status === 'error').length

      console.log(`Health check completed: ${online} online, ${offline} offline, ${errors} errors`)

      return {
        total: servers.length,
        online,
        offline,
        errors,
        results
      }

    } catch (error: any) {
      console.error('Failed to perform health checks on all servers:', error)
      return {
        total: 0,
        online: 0,
        offline: 0,
        errors: 0,
        results: [{
          success: false,
          serverId: 'all',
          serverName: 'All Servers',
          status: 'error',
          error: error.message || 'Unknown error during bulk health check',
          timestamp: new Date().toISOString()
        }]
      }
    }
  }

  /**
   * Get the last health check result for a server
   */
  async getLastHealthCheck(serverId: string): Promise<HealthCheckResult | null> {
    // For now, we'll return null since we don't store health check history
    // This could be extended to store health check results in the database
    return null
  }

  /**
   * Get health check statistics for all servers
   */
  async getHealthStats() {
    try {
      const summary = await this.checkAllServersHealth()

      return {
        totalServers: summary.total,
        onlineServers: summary.online,
        offlineServers: summary.offline,
        errorServers: summary.errors,
        onlinePercentage: summary.total > 0 ? Math.round((summary.online / summary.total) * 100) : 0,
        lastChecked: new Date().toISOString()
      }
    } catch (error) {
      console.error('Failed to get health stats:', error)
      return null
    }
  }
}