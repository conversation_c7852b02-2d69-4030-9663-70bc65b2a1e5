'use client';

import { prisma } from '@/lib/prisma';

export enum SecurityEventType {
  AUTHENTICATION_FAILURE = 'AUTHENTICATION_FAILURE',
  AUTHORIZATION_FAILURE = 'AUTHORIZATION_FAILURE',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  BRUTE_FORCE_ATTACK = 'BRUTE_FORCE_ATTACK',
  UNUSUAL_ACCESS_PATTERN = 'UNUSUAL_ACCESS_PATTERN',
  DATA_EXFILTRATION = 'DATA_EXFILTRATION',
  PRI<PERSON>LEGE_ESCALATION = 'PRIVILEGE_ESCALATION',
  SESSION_HIJACKING = 'SESSION_HIJACKING',
  MALWARE_DETECTION = 'MALWARE_DETECTION'
}

export enum SecurityEventSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  severity: SecurityEventSeverity;
  description: string;
  source: string;
  userId?: string;
  serverId?: string;
  ipAddress: string;
  userAgent?: string;
  metadata: Record<string, any>;
  detectedAt: Date;
  resolvedAt?: Date;
  status: 'OPEN' | 'INVESTIGATING' | 'RESOLVED' | 'FALSE_POSITIVE';
  assignedTo?: string;
  resolution?: string;
}

export interface DetectionRule {
  id: string;
  name: string;
  description: string;
  eventType: SecurityEventType;
  severity: SecurityEventSeverity;
  conditions: DetectionCondition[];
  cooldownMinutes: number;
  enabled: boolean;
}

export interface DetectionCondition {
  field: string;
  operator: 'EQUALS' | 'NOT_EQUALS' | 'CONTAINS' | 'GREATER_THAN' | 'LESS_THAN' | 'REGEX';
  value: any;
  threshold?: number;
}

export class SecurityEventDetectionService {
  private rules: DetectionRule[] = [];
  private eventCooldowns = new Map<string, Date>();

  constructor() {
    this.initializeRules();
  }

  private initializeRules() {
    this.rules = [
      {
        id: 'auth_failure_brute_force',
        name: 'Authentication Brute Force',
        description: 'Multiple failed login attempts from same IP',
        eventType: SecurityEventType.BRUTE_FORCE_ATTACK,
        severity: SecurityEventSeverity.HIGH,
        conditions: [
          {
            field: 'event_type',
            operator: 'EQUALS',
            value: 'AUTHENTICATION_FAILURE'
          },
          {
            field: 'count',
            operator: 'GREATER_THAN',
            value: 5,
            threshold: 5
          }
        ],
        cooldownMinutes: 10,
        enabled: true
      },
      {
        id: 'rate_limit_exceeded',
        name: 'Rate Limit Exceeded',
        description: 'API rate limit exceeded',
        eventType: SecurityEventType.RATE_LIMIT_EXCEEDED,
        severity: SecurityEventSeverity.MEDIUM,
        conditions: [
          {
            field: 'event_type',
            operator: 'EQUALS',
            value: 'RATE_LIMIT_EXCEEDED'
          }
        ],
        cooldownMinutes: 5,
        enabled: true
      },
      {
        id: 'suspicious_ip',
        name: 'Suspicious IP Activity',
        description: 'Activity from known malicious IP ranges',
        eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
        severity: SecurityEventSeverity.HIGH,
        conditions: [
          {
            field: 'ip_address',
            operator: 'REGEX',
            value: '^(192\\.168\\.|10\\.|172\\.(1[6-9]|2[0-9]|3[0-1])\\.)'
          }
        ],
        cooldownMinutes: 15,
        enabled: true
      },
      {
        id: 'privilege_escalation',
        name: 'Privilege Escalation Attempt',
        description: 'User attempting to access resources above their permission level',
        eventType: SecurityEventType.PRIVILEGE_ESCALATION,
        severity: SecurityEventSeverity.CRITICAL,
        conditions: [
          {
            field: 'event_type',
            operator: 'EQUALS',
            value: 'AUTHORIZATION_FAILURE'
          },
          {
            field: 'resource',
            operator: 'CONTAINS',
            value: 'admin'
          }
        ],
        cooldownMinutes: 30,
        enabled: true
      }
    ];
  }

  async analyzeAuditLogEntry(entry: any): Promise<SecurityEvent | null> {
    for (const rule of this.rules) {
      if (!rule.enabled) continue;

      const isMatch = this.evaluateRule(entry, rule);
      if (isMatch) {
        const cooldownKey = `${rule.id}_${entry.ip_address}_${entry.user_id || 'anonymous'}`;
        const lastEvent = this.eventCooldowns.get(cooldownKey);

        if (lastEvent && (Date.now() - lastEvent.getTime()) < (rule.cooldownMinutes * 60 * 1000)) {
          continue; // Skip if still in cooldown
        }

        const securityEvent = await this.createSecurityEvent(entry, rule);
        this.eventCooldowns.set(cooldownKey, new Date());

        return securityEvent;
      }
    }

    return null;
  }

  private evaluateRule(entry: any, rule: DetectionRule): boolean {
    return rule.conditions.every(condition => {
      const fieldValue = this.getNestedValue(entry, condition.field);

      switch (condition.operator) {
        case 'EQUALS':
          return fieldValue === condition.value;
        case 'NOT_EQUALS':
          return fieldValue !== condition.value;
        case 'CONTAINS':
          return String(fieldValue).includes(String(condition.value));
        case 'GREATER_THAN':
          return Number(fieldValue) > Number(condition.value);
        case 'LESS_THAN':
          return Number(fieldValue) < Number(condition.value);
        case 'REGEX':
          try {
            const regex = new RegExp(condition.value);
            return regex.test(String(fieldValue));
          } catch {
            return false;
          }
        default:
          return false;
      }
    });
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private async createSecurityEvent(entry: any, rule: DetectionRule): Promise<SecurityEvent> {
    const securityEvent: Omit<SecurityEvent, 'id'> = {
      type: rule.eventType,
      severity: rule.severity,
      description: `${rule.name}: ${rule.description}`,
      source: 'AUDIT_LOG',
      userId: entry.user_id,
      serverId: entry.server_id,
      ipAddress: entry.ip_address,
      userAgent: entry.user_agent,
      metadata: {
        ruleId: rule.id,
        originalEntry: entry,
        confidence: this.calculateConfidence(entry, rule)
      },
      detectedAt: new Date(),
      status: 'OPEN'
    };

    // Store in database
    const createdEvent = await prisma.securityEvent.create({
      data: securityEvent
    });

    return { ...securityEvent, id: createdEvent.id };
  }

  private calculateConfidence(entry: any, rule: DetectionRule): number {
    // Simple confidence calculation based on rule conditions
    const baseConfidence = 0.7;
    const conditionBonus = rule.conditions.length * 0.1;
    return Math.min(baseConfidence + conditionBonus, 1.0);
  }

  async getSecurityEvents(filters: {
    type?: SecurityEventType;
    severity?: SecurityEventSeverity;
    status?: string;
    userId?: string;
    serverId?: string;
    limit?: number;
    offset?: number;
  }): Promise<SecurityEvent[]> {
    const where: any = {};

    if (filters.type) where.type = filters.type;
    if (filters.severity) where.severity = filters.severity;
    if (filters.status) where.status = filters.status;
    if (filters.userId) where.userId = filters.userId;
    if (filters.serverId) where.serverId = filters.serverId;

    const events = await prisma.securityEvent.findMany({
      where,
      orderBy: { detectedAt: 'desc' },
      take: filters.limit || 50,
      skip: filters.offset || 0
    });

    return events as SecurityEvent[];
  }

  async updateEventStatus(eventId: string, status: SecurityEvent['status'], resolution?: string, assignedTo?: string): Promise<void> {
    await prisma.securityEvent.update({
      where: { id: eventId },
      data: {
        status,
        resolution,
        assignedTo,
        resolvedAt: status === 'RESOLVED' ? new Date() : null
      }
    });
  }

  async getEventStatistics(timeframe: 'hour' | 'day' | 'week' | 'month'): Promise<any> {
    const now = new Date();
    const startDate = new Date();

    switch (timeframe) {
      case 'hour':
        startDate.setHours(now.getHours() - 1);
        break;
      case 'day':
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
    }

    const [totalEvents, eventsByType, eventsBySeverity, unresolvedEvents] = await Promise.all([
      prisma.securityEvent.count({
        where: { detectedAt: { gte: startDate } }
      }),
      prisma.securityEvent.groupBy({
        by: ['type'],
        where: { detectedAt: { gte: startDate } },
        _count: { id: true }
      }),
      prisma.securityEvent.groupBy({
        by: ['severity'],
        where: { detectedAt: { gte: startDate } },
        _count: { id: true }
      }),
      prisma.securityEvent.count({
        where: {
          detectedAt: { gte: startDate },
          status: { in: ['OPEN', 'INVESTIGATING'] }
        }
      })
    ]);

    return {
      totalEvents,
      eventsByType,
      eventsBySeverity,
      unresolvedEvents,
      timeframe
    };
  }
}

export const securityEventDetection = new SecurityEventDetectionService();