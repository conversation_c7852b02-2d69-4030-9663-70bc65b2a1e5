import { prisma } from '@/lib/prisma'

export interface LocalUserPermissions {
  userId: string
  user: {
    name: string | null
    email: string | null
    profile?: {
      firstName: string | null
      lastName: string | null
    } | null
  }
  roles: {
    id: string
    name: string
    description: string | null
    permissions: string[]
  }[]
  directPermissions: {
    id: string
    name: string
    resource: string
    action: string
    description: string | null
    assignedAt: Date
    assignedBy?: string
  }[]
  allPermissions: string[]
  projectPermissions: {
    projectId: string
    projectName: string
    scopes: string[]
  }[]
}

export interface PermissionDefinition {
  name: string
  resource: string
  action: string
  description: string
  category: string
}

export class LocalPermissionService {
  /**
   * Get all available local permissions for NWAPromote
   * These are manually defined since we can't fetch from the server yet
   */
  async getAvailablePermissions(): Promise<PermissionDefinition[]> {
    // Get permissions from database
    const dbPermissions = await prisma.permission.findMany({
      orderBy: [
        { resource: 'asc' },
        { action: 'asc' }
      ]
    })

    // Convert to our format
    const permissions: PermissionDefinition[] = dbPermissions.map(p => ({
      name: p.name,
      resource: p.resource,
      action: p.action,
      description: p.description || '',
      category: this.categorizePermission(p.resource)
    }))

    // Add any missing core permissions that should exist
    const corePermissions = this.getCorePermissions()
    const existingNames = new Set(permissions.map(p => p.name))
    
    for (const corePermission of corePermissions) {
      if (!existingNames.has(corePermission.name)) {
        permissions.push(corePermission)
      }
    }

    return permissions.sort((a, b) => a.category.localeCompare(b.category) || a.name.localeCompare(b.name))
  }

  /**
   * Get core permissions that should exist in NWAPromote
   */
  private getCorePermissions(): PermissionDefinition[] {
    return [
      // User Management
      { name: 'user:read', resource: 'user', action: 'read', description: 'View user profiles and information', category: 'User Management' },
      { name: 'user:write', resource: 'user', action: 'write', description: 'Create and edit user profiles', category: 'User Management' },
      { name: 'user:delete', resource: 'user', action: 'delete', description: 'Delete user accounts', category: 'User Management' },
      { name: 'user:assign-roles', resource: 'user', action: 'assign-roles', description: 'Assign roles to users', category: 'User Management' },
      
      // Profile Management
      { name: 'profile:read', resource: 'profile', action: 'read', description: 'View detailed user profile information', category: 'Profile Management' },
      { name: 'profile:write', resource: 'profile', action: 'write', description: 'Edit user profile information', category: 'Profile Management' },
      { name: 'profile:read-sensitive', resource: 'profile', action: 'read-sensitive', description: 'View sensitive profile data (SSN, etc.)', category: 'Profile Management' },
      
      // Role Management
      { name: 'role:read', resource: 'role', action: 'read', description: 'View roles and permissions', category: 'Role Management' },
      { name: 'role:write', resource: 'role', action: 'write', description: 'Create and modify roles', category: 'Role Management' },
      { name: 'role:delete', resource: 'role', action: 'delete', description: 'Delete roles', category: 'Role Management' },
      { name: 'role:assign', resource: 'role', action: 'assign', description: 'Assign roles to users', category: 'Role Management' },
      
      // Position Management
      { name: 'position:read', resource: 'position', action: 'read', description: 'View organizational positions', category: 'Position Management' },
      { name: 'position:write', resource: 'position', action: 'write', description: 'Create and modify positions', category: 'Position Management' },
      { name: 'position:assign', resource: 'position', action: 'assign', description: 'Assign positions to users', category: 'Position Management' },
      { name: 'position:reassign', resource: 'position', action: 'reassign', description: 'Reassign user positions', category: 'Position Management' },
      
      // Treaty Management
      { name: 'treaty:read', resource: 'treaty', action: 'read', description: 'View treaties and applications', category: 'Treaty Management' },
      { name: 'treaty:write', resource: 'treaty', action: 'write', description: 'Create and edit treaties', category: 'Treaty Management' },
      { name: 'treaty:delete', resource: 'treaty', action: 'delete', description: 'Delete treaties', category: 'Treaty Management' },
      { name: 'treaty:approve', resource: 'treaty', action: 'approve', description: 'Approve treaty applications', category: 'Treaty Management' },
      { name: 'treaty:assign', resource: 'treaty', action: 'assign', description: 'Assign treaties to users', category: 'Treaty Management' },
      
      // Document Management
      { name: 'document:read', resource: 'document', action: 'read', description: 'View documents and ordinances', category: 'Document Management' },
      { name: 'document:write', resource: 'document', action: 'write', description: 'Create and edit documents', category: 'Document Management' },
      { name: 'document:upload', resource: 'document', action: 'upload', description: 'Upload document files', category: 'Document Management' },
      { name: 'document:delete', resource: 'document', action: 'delete', description: 'Delete documents', category: 'Document Management' },
      
      // System Administration
      { name: 'admin:access', resource: 'admin', action: 'access', description: 'Access admin panel and functions', category: 'System Administration' },
      { name: 'admin:settings', resource: 'admin', action: 'settings', description: 'Modify system settings', category: 'System Administration' },
      { name: 'admin:audit', resource: 'admin', action: 'audit', description: 'View audit logs and security information', category: 'System Administration' },
      { name: 'admin:users', resource: 'admin', action: 'users', description: 'Full user management access', category: 'System Administration' },
      
      // Project Management
      { name: 'project:read', resource: 'project', action: 'read', description: 'View project information', category: 'Project Management' },
      { name: 'project:write', resource: 'project', action: 'write', description: 'Create and modify projects', category: 'Project Management' },
      { name: 'project:assign', resource: 'project', action: 'assign', description: 'Assign users to projects', category: 'Project Management' },
      { name: 'project:manage', resource: 'project', action: 'manage', description: 'Full project management access', category: 'Project Management' },
    ]
  }

  /**
   * Categorize permission based on resource
   */
  private categorizePermission(resource: string): string {
    const categoryMap: Record<string, string> = {
      'user': 'User Management',
      'profile': 'Profile Management', 
      'role': 'Role Management',
      'position': 'Position Management',
      'treaty': 'Treaty Management',
      'document': 'Document Management',
      'ordinance': 'Document Management',
      'admin': 'System Administration',
      'project': 'Project Management',
      'audit': 'System Administration'
    }
    
    return categoryMap[resource] || 'Other'
  }

  /**
   * Get comprehensive permissions for a user
   */
  async getUserPermissions(userId: string): Promise<LocalUserPermissions> {
    // Get user with roles
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: {
          select: {
            firstName: true,
            lastName: true
          }
        },
        userRoles: {
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        },
        userProjectScopes: {
          include: {
            project: {
              select: {
                id: true,
                name: true
              }
            },
            scope: {
              select: {
                name: true
              }
            }
          }
        }
      }
    })

    if (!user) {
      throw new Error('User not found')
    }

    // Process roles and their permissions
    const roles = user.userRoles.map(ur => ({
      id: ur.role.id,
      name: ur.role.name,
      description: ur.role.description,
      permissions: ur.role.rolePermissions.map(rp => rp.permission.name)
    }))

    // Get all role-based permissions
    const allRolePermissions = new Set<string>()
    roles.forEach(role => {
      role.permissions.forEach(perm => allRolePermissions.add(perm))
    })

    // Process project permissions
    const projectPermissions = user.userProjectScopes.reduce((acc, ups) => {
      const existing = acc.find(p => p.projectId === ups.project.id)
      if (existing) {
        existing.scopes.push(ups.scope.name)
      } else {
        acc.push({
          projectId: ups.project.id,
          projectName: ups.project.name,
          scopes: [ups.scope.name]
        })
      }
      return acc
    }, [] as LocalUserPermissions['projectPermissions'])

    return {
      userId: user.id,
      user: {
        name: user.name,
        email: user.email,
        profile: user.profile
      },
      roles,
      directPermissions: [], // TODO: Implement direct permission assignments
      allPermissions: Array.from(allRolePermissions),
      projectPermissions
    }
  }

  /**
   * Assign role to user
   */
  async assignRoleToUser(userId: string, roleId: string, assignedBy: string): Promise<void> {
    // Check if assignment already exists
    const existing = await prisma.userRole.findUnique({
      where: {
        userId_roleId: {
          userId,
          roleId
        }
      }
    })

    if (existing) {
      throw new Error('User already has this role')
    }

    await prisma.userRole.create({
      data: {
        userId,
        roleId,
        assignedBy
      }
    })
  }

  /**
   * Remove role from user
   */
  async removeRoleFromUser(userId: string, roleId: string): Promise<void> {
    await prisma.userRole.delete({
      where: {
        userId_roleId: {
          userId,
          roleId
        }
      }
    })
  }

  /**
   * Assign project scope to user
   */
  async assignProjectScope(userId: string, projectId: string, scopeId: string, assignedBy: string): Promise<void> {
    // Check if assignment already exists
    const existing = await prisma.userProjectScope.findUnique({
      where: {
        userId_projectId_scopeId: {
          userId,
          projectId,
          scopeId
        }
      }
    })

    if (existing) {
      throw new Error('User already has this project scope')
    }

    await prisma.userProjectScope.create({
      data: {
        userId,
        projectId,
        scopeId
      }
    })
  }

  /**
   * Remove project scope from user
   */
  async removeProjectScope(userId: string, projectId: string, scopeId: string): Promise<void> {
    await prisma.userProjectScope.delete({
      where: {
        userId_projectId_scopeId: {
          userId,
          projectId,
          scopeId
        }
      }
    })
  }

  /**
   * Get all available roles
   */
  async getAvailableRoles() {
    return await prisma.role.findMany({
      include: {
        rolePermissions: {
          include: {
            permission: true
          }
        }
      }
    })
  }

  /**
   * Get all available projects
   */
  async getAvailableProjects() {
    return await prisma.project.findMany({
      include: {
        userProjectScopes: {
          include: {
            scope: true
          }
        }
      }
    })
  }

  /**
   * Sync/create core permissions in database
   */
  async syncCorePermissions(): Promise<{ created: number; updated: number }> {
    const corePermissions = this.getCorePermissions()
    let created = 0
    let updated = 0

    for (const perm of corePermissions) {
      const existing = await prisma.permission.findUnique({
        where: { name: perm.name }
      })

      if (existing) {
        await prisma.permission.update({
          where: { name: perm.name },
          data: {
            resource: perm.resource,
            action: perm.action,
            description: perm.description
          }
        })
        updated++
      } else {
        await prisma.permission.create({
          data: {
            name: perm.name,
            resource: perm.resource,
            action: perm.action,
            description: perm.description
          }
        })
        created++
      }
    }

    return { created, updated }
  }
}

// Export singleton instance
export const localPermissionService = new LocalPermissionService()