import { prisma } from '@/lib/prisma'
import { fetchRemoteServerPermissions } from '@/app/actions/remote-servers'

interface SyncResult {
  success: boolean
  serverId: string
  serverName: string
  permissionsSynced: number
  rolesSynced: number
  availablePermissionsSynced: number
  errors: string[]
  timestamp: string
  // Include the actual permissions data for the frontend
  permissions?: Array<{ name: string; description: string }>
  roles?: Array<{ name: string; description: string; permissions: string[] }>
  availablePermissions?: Array<{ name: string; description: string }>
  rolePermissions?: Record<string, string[]>
  summary?: any
}

export class PermissionSyncService {
  /**
   * Sync permissions for a specific remote server
   */
  async syncServerPermissions(serverId: string): Promise<SyncResult> {
    const errors: string[] = []
    
    try {
      // Get the remote server
      const server = await prisma.remoteServer.findUnique({
        where: { id: serverId }
      })

      if (!server) {
        return {
          success: false,
          serverId,
          serverName: 'Unknown',
          permissionsSynced: 0,
          rolesSynced: 0,
          availablePermissionsSynced: 0,
          errors: ['Remote server not found'],
          timestamp: new Date().toISOString()
        }
      }

      console.log(`Starting permission sync for server: ${server.name} (${serverId})`)

      // Fetch permissions from the remote server
      const result = await fetchRemoteServerPermissions(serverId)

      if (!result.success) {
        return {
          success: false,
          serverId,
          serverName: server.name,
          permissionsSynced: 0,
          rolesSynced: 0,
          availablePermissionsSynced: 0,
          errors: [result.error || 'Failed to fetch permissions'],
          timestamp: new Date().toISOString()
        }
      }

      const { permissions, roles, availablePermissions, rolePermissions, summary } = result.data || {
        permissions: [],
        roles: [],
        availablePermissions: [],
        rolePermissions: {},
        summary: {}
      }

      // Sync permissions to database (includes both role permissions and individual permissions)
      const permissionSyncResult = await this.syncPermissions(serverId, permissions)

      // Sync roles with their permissions
      const roleSyncResult = await this.syncRoles(serverId, roles, rolePermissions)

      // Sync available permissions (individual permissions that can be assigned)
      const availablePermissionSyncResult = await this.syncAvailablePermissions(serverId, availablePermissions)

      console.log(`Permission sync completed for server: ${server.name}`)
      console.log(`- Permissions synced: ${permissionSyncResult.synced}`)
      console.log(`- Roles synced: ${roleSyncResult.synced}`)

      return {
        success: true,
        serverId,
        serverName: server.name,
        permissionsSynced: permissionSyncResult.synced,
        rolesSynced: roleSyncResult.synced,
        availablePermissionsSynced: availablePermissionSyncResult.synced,
        errors: [...permissionSyncResult.errors, ...roleSyncResult.errors, ...availablePermissionSyncResult.errors],
        timestamp: new Date().toISOString(),
        // Include the actual permissions data for the frontend
        permissions,
        roles,
        availablePermissions,
        rolePermissions,
        summary
      }

    } catch (error: any) {
      console.error(`Permission sync failed for server ${serverId}:`, error)
      errors.push(error.message || 'Unknown error during sync')
      
      return {
        success: false,
        serverId,
        serverName: 'Unknown',
        permissionsSynced: 0,
        rolesSynced: 0,
        availablePermissionsSynced: 0,
        errors,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Sync all permissions for all active remote servers
   */
  async syncAllServers(): Promise<SyncResult[]> {
    try {
      // Get all active remote servers
      const servers = await prisma.remoteServer.findMany({
        where: { isActive: true }
      })

      console.log(`Starting permission sync for ${servers.length} active servers`)

      const results: SyncResult[] = []

      // Sync each server sequentially to avoid overwhelming the system
      for (const server of servers) {
        const result = await this.syncServerPermissions(server.id)
        results.push(result)
        
        // Add a small delay between syncs to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      const successful = results.filter(r => r.success).length
      const failed = results.filter(r => !r.success).length

      console.log(`Permission sync completed: ${successful} successful, ${failed} failed`)

      return results

    } catch (error: any) {
      console.error('Failed to sync all servers:', error)
      return [{
        success: false,
        serverId: 'all',
        serverName: 'All Servers',
        permissionsSynced: 0,
        rolesSynced: 0,
        availablePermissionsSynced: 0,
        errors: [error.message || 'Unknown error during bulk sync'],
        timestamp: new Date().toISOString()
      }]
    }
  }

  /**
   * Sync permissions for a specific server
   */
  private async syncPermissions(serverId: string, permissions: Array<{ name: string; description: string }>) {
    const errors: string[] = []
    let synced = 0

    try {
      // Upsert each permission
      for (const permission of permissions) {
        try {
          await prisma.remote_server_permissions.upsert({
            where: {
              remote_server_id_permission_name: {
                remote_server_id: serverId,
                permission_name: permission.name
              }
            },
            update: {
              permission_description: permission.description,
              last_synced_at: new Date(),
              is_active: true
            },
            create: {
              remote_server_id: serverId,
              permission_name: permission.name,
              permission_description: permission.description,
              last_synced_at: new Date(),
              is_active: true
            }
          })
          synced++
        } catch (permissionError: any) {
          console.error(`Failed to sync permission ${permission.name}:`, permissionError)
          errors.push(`Permission ${permission.name}: ${permissionError.message}`)
        }
      }

      // Mark any permissions that weren't in this sync as inactive
      const syncedPermissionNames = permissions.map(p => p.name)
      await prisma.remote_server_permissions.updateMany({
        where: {
          remote_server_id: serverId,
          permission_name: { notIn: syncedPermissionNames }
        },
        data: { is_active: false }
      })

    } catch (error: any) {
      console.error(`Failed to sync permissions for server ${serverId}:`, error)
      errors.push(`Permission sync failed: ${error.message}`)
    }

    return { synced, errors }
  }

  /**
   * Sync roles for a specific server with their permission mappings
   */
  private async syncRoles(serverId: string, roles: Array<{ name: string; description: string; permissions: string[] }>, rolePermissions: Record<string, string[]>) {
    const errors: string[] = []
    let synced = 0

    try {
      // Sync each role with its permissions
      for (const role of roles) {
        try {
          // Check if role already exists (including system roles)
          let dbRole = await prisma.role.findFirst({
            where: {
              name: role.name
            }
          })

          // If role doesn't exist, create it as non-system
          if (!dbRole) {
            dbRole = await prisma.role.create({
              data: {
                name: role.name,
                description: role.description,
                isSystem: false
              }
            })
          } else {
            // If role exists but is a system role, skip permission updates
            // Only update permissions for non-system roles
            if (dbRole.isSystem) {
              console.log(`Skipping permission sync for system role: ${role.name}`)
              synced++
              continue
            }
          }

          // Sync role permissions using the rolePermissions mapping
          const rolePermissionNames = rolePermissions[role.name] || role.permissions || []

          // Get existing role permissions
          const existingRolePermissions = await prisma.rolePermission.findMany({
            where: { roleId: dbRole.id },
            include: { permission: true }
          })

          const existingPermissionNames = existingRolePermissions.map(rp => rp.permission.name)

          // Remove permissions that are no longer in the role
          for (const existingPerm of existingRolePermissions) {
            if (!rolePermissionNames.includes(existingPerm.permission.name)) {
              await prisma.rolePermission.delete({
                where: { id: existingPerm.id }
              })
            }
          }

          // Add new permissions to the role
          for (const permName of rolePermissionNames) {
            if (!existingPermissionNames.includes(permName)) {
              // Find or create the permission
              let permission = await prisma.permission.findFirst({
                where: { name: permName }
              })

              if (!permission) {
                permission = await prisma.permission.create({
                  data: {
                    name: permName,
                    resource: 'remote_server',
                    action: permName,
                    description: `Permission from remote server: ${permName}`
                  }
                })
              }

              // Create the role-permission relationship
              await prisma.rolePermission.upsert({
                where: {
                  roleId_permissionId: {
                    roleId: dbRole.id,
                    permissionId: permission.id
                  }
                },
                update: {},
                create: {
                  roleId: dbRole.id,
                  permissionId: permission.id
                }
              })
            }
          }

          // Create or update role-remote server access
          await prisma.role_remote_server_access.upsert({
            where: {
              role_id_remote_server_id: {
                role_id: dbRole.id,
                remote_server_id: serverId
              }
            },
            update: {
              auto_grant_permissions: rolePermissionNames,
              is_active: true
            },
            create: {
              role_id: dbRole.id,
              remote_server_id: serverId,
              auto_grant_permissions: rolePermissionNames,
              is_active: true
            }
          })

          synced++
        } catch (roleError: any) {
          console.error(`Failed to sync role ${role.name}:`, roleError)
          errors.push(`Role ${role.name}: ${roleError.message}`)
        }
      }

    } catch (error: any) {
      console.error(`Failed to sync roles for server ${serverId}:`, error)
      errors.push(`Role sync failed: ${error.message}`)
    }

    return { synced, errors }
  }

  /**
   * Sync available permissions (individual permissions that can be assigned)
   */
  private async syncAvailablePermissions(serverId: string, availablePermissions: Array<{ name: string; description: string }>) {
    const errors: string[] = []
    let synced = 0

    try {
      // Sync each available permission
      for (const permission of availablePermissions) {
        try {
          // Check if this permission already exists in the remote server permissions
          const existingPermission = await prisma.remote_server_permissions.findUnique({
            where: {
              remote_server_id_permission_name: {
                remote_server_id: serverId,
                permission_name: permission.name
              }
            }
          })

          if (!existingPermission) {
            // Create the permission in remote server permissions
            await prisma.remote_server_permissions.create({
              data: {
                remote_server_id: serverId,
                permission_name: permission.name,
                permission_description: permission.description,
                last_synced_at: new Date(),
                is_active: true
              }
            })
          } else {
            // Update the existing permission
            await prisma.remote_server_permissions.update({
              where: {
                remote_server_id_permission_name: {
                  remote_server_id: serverId,
                  permission_name: permission.name
                }
              },
              data: {
                permission_description: permission.description,
                last_synced_at: new Date(),
                is_active: true
              }
            })
          }

          synced++
        } catch (permissionError: any) {
          console.error(`Failed to sync available permission ${permission.name}:`, permissionError)
          errors.push(`Available permission ${permission.name}: ${permissionError.message}`)
        }
      }

    } catch (error: any) {
      console.error(`Failed to sync available permissions for server ${serverId}:`, error)
      errors.push(`Available permission sync failed: ${error.message}`)
    }

    return { synced, errors }
  }

  /**
   * Get the last sync time for a server
   */
  async getLastSyncTime(serverId: string): Promise<Date | null> {
    try {
      const latestPermission = await prisma.remote_server_permissions.findFirst({
        where: { remote_server_id: serverId },
        orderBy: { last_synced_at: 'desc' },
        select: { last_synced_at: true }
      })

      return latestPermission?.last_synced_at || null
    } catch (error) {
      console.error(`Failed to get last sync time for server ${serverId}:`, error)
      return null
    }
  }

  /**
   * Get sync statistics for a server
   */
  async getSyncStats(serverId: string) {
    try {
      const [totalPermissions, activePermissions, lastSync] = await Promise.all([
        prisma.remote_server_permissions.count({
          where: { remote_server_id: serverId }
        }),
        prisma.remote_server_permissions.count({
          where: { 
            remote_server_id: serverId,
            is_active: true 
          }
        }),
        this.getLastSyncTime(serverId)
      ])

      return {
        totalPermissions,
        activePermissions,
        inactivePermissions: totalPermissions - activePermissions,
        lastSync
      }
    } catch (error) {
      console.error(`Failed to get sync stats for server ${serverId}:`, error)
      return null
    }
  }
}