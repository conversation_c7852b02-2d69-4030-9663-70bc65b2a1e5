'use client';

import { prisma } from '@/lib/prisma';
import { SecurityEventType, SecurityEventSeverity } from './security-event-detection';

export interface AlertRule {
  id: string;
  name: string;
  description: string;
  eventTypes: SecurityEventType[];
  severities: SecurityEventSeverity[];
  conditions: AlertCondition[];
  channels: AlertChannel[];
  cooldownMinutes: number;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AlertCondition {
  field: string;
  operator: 'EQUALS' | 'NOT_EQUALS' | 'CONTAINS' | 'GREATER_THAN' | 'LESS_THAN' | 'REGEX';
  value: any;
}

export interface AlertChannel {
  type: 'EMAIL' | 'SLACK' | 'WEBHOOK' | 'SMS' | 'IN_APP';
  config: Record<string, any>;
}

export interface SecurityAlert {
  id: string;
  eventId: string;
  ruleId: string;
  channels: AlertChannel[];
  sentAt: Date;
  status: 'SENT' | 'FAILED' | 'PENDING';
  errorMessage?: string;
  metadata: Record<string, any>;
}

export class SecurityAlertingService {
  private rules: AlertRule[] = [];
  private alertCooldowns = new Map<string, Date>();

  constructor() {
    this.initializeRules();
  }

  private initializeRules() {
    this.rules = [
      {
        id: 'critical_security_alert',
        name: 'Critical Security Events',
        description: 'Alert for all critical security events',
        eventTypes: [
          SecurityEventType.BRUTE_FORCE_ATTACK,
          SecurityEventType.PRIVILEGE_ESCALATION,
          SecurityEventType.DATA_EXFILTRATION,
          SecurityEventType.SESSION_HIJACKING
        ],
        severities: [SecurityEventSeverity.CRITICAL],
        conditions: [],
        channels: [
          {
            type: 'EMAIL',
            config: {
              recipients: ['<EMAIL>', '<EMAIL>'],
              subject: 'CRITICAL: Security Alert - {event_type}',
              template: 'critical_security_alert'
            }
          },
          {
            type: 'SLACK',
            config: {
              webhookUrl: process.env.SLACK_SECURITY_WEBHOOK,
              channel: '#security-alerts'
            }
          }
        ],
        cooldownMinutes: 5,
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'high_severity_alert',
        name: 'High Severity Events',
        description: 'Alert for high severity security events',
        eventTypes: [
          SecurityEventType.AUTHENTICATION_FAILURE,
          SecurityEventType.AUTHORIZATION_FAILURE,
          SecurityEventType.SUSPICIOUS_ACTIVITY,
          SecurityEventType.UNUSUAL_ACCESS_PATTERN
        ],
        severities: [SecurityEventSeverity.HIGH],
        conditions: [],
        channels: [
          {
            type: 'EMAIL',
            config: {
              recipients: ['<EMAIL>'],
              subject: 'HIGH: Security Alert - {event_type}',
              template: 'high_security_alert'
            }
          }
        ],
        cooldownMinutes: 10,
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'rate_limit_alert',
        name: 'Rate Limit Exceeded',
        description: 'Alert when rate limits are exceeded',
        eventTypes: [SecurityEventType.RATE_LIMIT_EXCEEDED],
        severities: [SecurityEventSeverity.MEDIUM, SecurityEventSeverity.HIGH],
        conditions: [
          {
            field: 'count',
            operator: 'GREATER_THAN',
            value: 100
          }
        ],
        channels: [
          {
            type: 'EMAIL',
            config: {
              recipients: ['<EMAIL>'],
              subject: 'Rate Limit Alert',
              template: 'rate_limit_alert'
            }
          }
        ],
        cooldownMinutes: 15,
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
  }

  async processSecurityEvent(event: any): Promise<void> {
    for (const rule of this.rules) {
      if (!rule.enabled) continue;

      const shouldAlert = this.evaluateRule(event, rule);
      if (shouldAlert) {
        const cooldownKey = `${rule.id}_${event.id}`;
        const lastAlert = this.alertCooldowns.get(cooldownKey);

        if (lastAlert && (Date.now() - lastAlert.getTime()) < (rule.cooldownMinutes * 60 * 1000)) {
          continue; // Skip if still in cooldown
        }

        await this.sendAlert(event, rule);
        this.alertCooldowns.set(cooldownKey, new Date());
      }
    }
  }

  private evaluateRule(event: any, rule: AlertRule): boolean {
    // Check event type
    if (!rule.eventTypes.includes(event.type)) {
      return false;
    }

    // Check severity
    if (!rule.severities.includes(event.severity)) {
      return false;
    }

    // Check conditions
    for (const condition of rule.conditions) {
      const fieldValue = this.getNestedValue(event, condition.field);
      const matches = this.evaluateCondition(fieldValue, condition);

      if (!matches) {
        return false;
      }
    }

    return true;
  }

  private evaluateCondition(fieldValue: any, condition: AlertCondition): boolean {
    switch (condition.operator) {
      case 'EQUALS':
        return fieldValue === condition.value;
      case 'NOT_EQUALS':
        return fieldValue !== condition.value;
      case 'CONTAINS':
        return String(fieldValue).includes(String(condition.value));
      case 'GREATER_THAN':
        return Number(fieldValue) > Number(condition.value);
      case 'LESS_THAN':
        return Number(fieldValue) < Number(condition.value);
      case 'REGEX':
        try {
          const regex = new RegExp(condition.value);
          return regex.test(String(fieldValue));
        } catch {
          return false;
        }
      default:
        return false;
    }
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private async sendAlert(event: any, rule: AlertRule): Promise<void> {
    const alertData = {
      eventId: event.id,
      ruleId: rule.id,
      channels: JSON.stringify(rule.channels),
      sentAt: new Date(),
      status: 'PENDING' as const,
      metadata: JSON.stringify({
        event,
        rule
      })
    };

    // Store alert in database
    const alert = await prisma.securityAlert.create({
      data: alertData
    });

    // Send to each channel
    for (const channel of rule.channels) {
      try {
        await this.sendToChannel(alert, channel, event, rule);
        await this.updateAlertStatus(alert.id, 'SENT');
      } catch (error) {
        await this.updateAlertStatus(alert.id, 'FAILED', error instanceof Error ? error.message : 'Unknown error');
      }
    }
  }

  private async sendToChannel(alert: any, channel: AlertChannel, event: any, rule: AlertRule): Promise<void> {
    switch (channel.type) {
      case 'EMAIL':
        await this.sendEmailAlert(channel.config, event, rule);
        break;
      case 'SLACK':
        await this.sendSlackAlert(channel.config, event, rule);
        break;
      case 'WEBHOOK':
        await this.sendWebhookAlert(channel.config, event, rule);
        break;
      case 'SMS':
        await this.sendSMSAlert(channel.config, event, rule);
        break;
      case 'IN_APP':
        await this.sendInAppAlert(channel.config, event, rule);
        break;
      default:
        throw new Error(`Unsupported alert channel: ${channel.type}`);
    }
  }

  private async sendEmailAlert(config: any, event: any, rule: AlertRule): Promise<void> {
    // Implement email sending logic
    console.log('Sending email alert:', {
      to: config.recipients,
      subject: config.subject.replace('{event_type}', event.type),
      event,
      rule
    });

    // Here you would integrate with your email service (e.g., SendGrid, AWS SES)
  }

  private async sendSlackAlert(config: any, event: any, rule: AlertRule): Promise<void> {
    // Implement Slack webhook logic
    console.log('Sending Slack alert:', {
      webhookUrl: config.webhookUrl,
      channel: config.channel,
      event,
      rule
    });

    // Here you would send HTTP POST to Slack webhook
  }

  private async sendWebhookAlert(config: any, event: any, rule: AlertRule): Promise<void> {
    // Implement webhook logic
    console.log('Sending webhook alert:', {
      url: config.url,
      event,
      rule
    });

    // Here you would send HTTP POST to webhook URL
  }

  private async sendSMSAlert(config: any, event: any, rule: AlertRule): Promise<void> {
    // Implement SMS logic
    console.log('Sending SMS alert:', {
      to: config.recipients,
      message: `Security Alert: ${event.type} - ${event.severity}`,
      event,
      rule
    });

    // Here you would integrate with SMS service
  }

  private async sendInAppAlert(config: any, event: any, rule: AlertRule): Promise<void> {
    // Implement in-app notification logic
    console.log('Sending in-app alert:', {
      userIds: config.userIds,
      event,
      rule
    });

    // Here you would create in-app notifications
  }

  private async updateAlertStatus(alertId: string, status: SecurityAlert['status'], errorMessage?: string): Promise<void> {
    await prisma.securityAlert.update({
      where: { id: alertId },
      data: {
        status,
        errorMessage,
        sentAt: status === 'SENT' ? new Date() : undefined
      }
    });
  }

  async getAlertRules(): Promise<AlertRule[]> {
    // In a real implementation, you would fetch from database
    return this.rules;
  }

  async updateAlertRule(ruleId: string, updates: Partial<AlertRule>): Promise<void> {
    const ruleIndex = this.rules.findIndex(r => r.id === ruleId);
    if (ruleIndex !== -1) {
      this.rules[ruleIndex] = { ...this.rules[ruleIndex], ...updates, updatedAt: new Date() };
    }
  }

  async getAlertHistory(filters: {
    ruleId?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<SecurityAlert[]> {
    const where: any = {};

    if (filters.ruleId) where.ruleId = filters.ruleId;
    if (filters.status) where.status = filters.status;

    const alerts = await prisma.securityAlert.findMany({
      where,
      orderBy: { sentAt: 'desc' },
      take: filters.limit || 50,
      skip: filters.offset || 0
    });

    return alerts.map(alert => ({
      ...alert,
      channels: JSON.parse(alert.channels as string) as AlertChannel[],
      metadata: JSON.parse(alert.metadata as string) as Record<string, any>
    })) as SecurityAlert[];
  }

  async getAlertStatistics(timeframe: 'hour' | 'day' | 'week' | 'month'): Promise<any> {
    const now = new Date();
    const startDate = new Date();

    switch (timeframe) {
      case 'hour':
        startDate.setHours(now.getHours() - 1);
        break;
      case 'day':
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
    }

    const [totalAlerts, alertsByChannel, alertsByStatus, failedAlerts] = await Promise.all([
      prisma.securityAlert.count({
        where: { sentAt: { gte: startDate } }
      }),
      prisma.securityAlert.groupBy({
        by: ['channels'],
        where: { sentAt: { gte: startDate } },
        _count: { id: true }
      }),
      prisma.securityAlert.groupBy({
        by: ['status'],
        where: { sentAt: { gte: startDate } },
        _count: { id: true }
      }),
      prisma.securityAlert.count({
        where: {
          sentAt: { gte: startDate },
          status: 'FAILED'
        }
      })
    ]);

    return {
      totalAlerts,
      alertsByChannel,
      alertsByStatus,
      failedAlerts,
      timeframe
    };
  }
}

export const securityAlerting = new SecurityAlertingService();