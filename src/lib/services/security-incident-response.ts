'use client';

import { prisma } from '@/lib/prisma';
import { SecurityEventType, SecurityEventSeverity } from './security-event-detection';

export interface IncidentResponseRule {
  id: string;
  name: string;
  description: string;
  eventTypes: SecurityEventType[];
  severities: SecurityEventSeverity[];
  conditions: ResponseCondition[];
  actions: IncidentResponseAction[];
  priority: number;
  enabled: boolean;
  cooldownMinutes: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ResponseCondition {
  field: string;
  operator: 'EQUALS' | 'NOT_EQUALS' | 'CONTAINS' | 'GREATER_THAN' | 'LESS_THAN' | 'REGEX';
  value: any;
  threshold?: number;
}

export interface IncidentResponseAction {
  type: 'ACCOUNT_LOCKOUT' | 'SESSION_TERMINATION' | 'IP_BLOCK' | 'NOTIFICATION' | 'ESCALATION' | 'LOG_EVENT';
  config: Record<string, any>;
  delayMinutes?: number; // Delay before executing action
}

export interface IncidentResponse {
  id: string;
  eventId: string;
  ruleId: string;
  actions: IncidentResponseAction[];
  executedAt: Date;
  status: 'PENDING' | 'EXECUTING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  results: ResponseActionResult[];
  errorMessage?: string;
  metadata: Record<string, any>;
}

export interface ResponseActionResult {
  actionType: string;
  success: boolean;
  message: string;
  data?: any;
  executedAt: Date;
}

export class SecurityIncidentResponseService {
  private rules: IncidentResponseRule[] = [];
  private activeResponses = new Map<string, IncidentResponse>();

  constructor() {
    this.initializeRules();
  }

  private initializeRules() {
    this.rules = [
      {
        id: 'brute_force_response',
        name: 'Brute Force Attack Response',
        description: 'Automatically respond to brute force attacks',
        eventTypes: [SecurityEventType.BRUTE_FORCE_ATTACK],
        severities: [SecurityEventSeverity.HIGH, SecurityEventSeverity.CRITICAL],
        conditions: [
          {
            field: 'count',
            operator: 'GREATER_THAN',
            value: 5,
            threshold: 5
          }
        ],
        actions: [
          {
            type: 'ACCOUNT_LOCKOUT',
            config: {
              lockoutDurationMinutes: 30,
              notifyUser: true,
              notifyAdmins: true
            }
          },
          {
            type: 'IP_BLOCK',
            config: {
              blockDurationMinutes: 60,
              blockType: 'TEMPORARY'
            }
          },
          {
            type: 'SESSION_TERMINATION',
            config: {
              terminateAllUserSessions: true
            }
          },
          {
            type: 'NOTIFICATION',
            config: {
              recipients: ['<EMAIL>'],
              priority: 'HIGH',
              template: 'brute_force_incident'
            }
          }
        ],
        priority: 1,
        enabled: true,
        cooldownMinutes: 10,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'privilege_escalation_response',
        name: 'Privilege Escalation Response',
        description: 'Respond to privilege escalation attempts',
        eventTypes: [SecurityEventType.PRIVILEGE_ESCALATION],
        severities: [SecurityEventSeverity.CRITICAL],
        conditions: [],
        actions: [
          {
            type: 'ACCOUNT_LOCKOUT',
            config: {
              lockoutDurationMinutes: 60,
              notifyUser: true,
              notifyAdmins: true,
              requirePasswordReset: true
            }
          },
          {
            type: 'SESSION_TERMINATION',
            config: {
              terminateAllUserSessions: true
            }
          },
          {
            type: 'NOTIFICATION',
            config: {
              recipients: ['<EMAIL>', '<EMAIL>'],
              priority: 'CRITICAL',
              template: 'privilege_escalation_incident'
            }
          },
          {
            type: 'ESCALATION',
            config: {
              escalationLevel: 'IMMEDIATE',
              requireHumanReview: true
            }
          }
        ],
        priority: 1,
        enabled: true,
        cooldownMinutes: 30,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'rate_limit_response',
        name: 'Rate Limit Violation Response',
        description: 'Respond to rate limit violations',
        eventTypes: [SecurityEventType.RATE_LIMIT_EXCEEDED],
        severities: [SecurityEventSeverity.MEDIUM, SecurityEventSeverity.HIGH],
        conditions: [
          {
            field: 'count',
            operator: 'GREATER_THAN',
            value: 100,
            threshold: 100
          }
        ],
        actions: [
          {
            type: 'IP_BLOCK',
            config: {
              blockDurationMinutes: 15,
              blockType: 'TEMPORARY'
            }
          },
          {
            type: 'NOTIFICATION',
            config: {
              recipients: ['<EMAIL>'],
              priority: 'MEDIUM',
              template: 'rate_limit_incident'
            }
          }
        ],
        priority: 2,
        enabled: true,
        cooldownMinutes: 5,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'suspicious_activity_response',
        name: 'Suspicious Activity Response',
        description: 'Respond to suspicious activity patterns',
        eventTypes: [SecurityEventType.SUSPICIOUS_ACTIVITY],
        severities: [SecurityEventSeverity.HIGH],
        conditions: [],
        actions: [
          {
            type: 'SESSION_TERMINATION',
            config: {
              terminateCurrentSession: true
            }
          },
          {
            type: 'NOTIFICATION',
            config: {
              recipients: ['<EMAIL>'],
              priority: 'HIGH',
              template: 'suspicious_activity_incident'
            }
          }
        ],
        priority: 2,
        enabled: true,
        cooldownMinutes: 15,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
  }

  async processSecurityEvent(event: any): Promise<void> {
    // Find applicable rules
    const applicableRules = this.rules
      .filter(rule => rule.enabled)
      .filter(rule => this.evaluateRule(event, rule))
      .sort((a, b) => a.priority - b.priority);

    for (const rule of applicableRules) {
      const responseId = `${rule.id}_${event.id}`;

      // Check if response is already active
      if (this.activeResponses.has(responseId)) {
        continue;
      }

      // Check cooldown
      const cooldownKey = `${rule.id}_${event.userId || event.ipAddress}`;
      const lastResponse = await this.getLastResponseTime(rule.id, event.userId, event.ipAddress);

      if (lastResponse && (Date.now() - lastResponse.getTime()) < (rule.cooldownMinutes * 60 * 1000)) {
        continue;
      }

      // Create response record
      const response = await this.createIncidentResponse(event, rule);
      this.activeResponses.set(responseId, response);

      // Execute actions
      await this.executeResponseActions(response, event, rule);

      // Mark response as completed
      await this.updateResponseStatus(response.id, 'COMPLETED');
      this.activeResponses.delete(responseId);
    }
  }

  private evaluateRule(event: any, rule: IncidentResponseRule): boolean {
    // Check event type
    if (!rule.eventTypes.includes(event.type)) {
      return false;
    }

    // Check severity
    if (!rule.severities.includes(event.severity)) {
      return false;
    }

    // Check conditions
    for (const condition of rule.conditions) {
      const fieldValue = this.getNestedValue(event, condition.field);
      const matches = this.evaluateCondition(fieldValue, condition);

      if (!matches) {
        return false;
      }
    }

    return true;
  }

  private evaluateCondition(fieldValue: any, condition: ResponseCondition): boolean {
    switch (condition.operator) {
      case 'EQUALS':
        return fieldValue === condition.value;
      case 'NOT_EQUALS':
        return fieldValue !== condition.value;
      case 'CONTAINS':
        return String(fieldValue).includes(String(condition.value));
      case 'GREATER_THAN':
        return Number(fieldValue) > Number(condition.value);
      case 'LESS_THAN':
        return Number(fieldValue) < Number(condition.value);
      case 'REGEX':
        try {
          const regex = new RegExp(condition.value);
          return regex.test(String(fieldValue));
        } catch {
          return false;
        }
      default:
        return false;
    }
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private async createIncidentResponse(event: any, rule: IncidentResponseRule): Promise<IncidentResponse> {
    const response: Omit<IncidentResponse, 'id'> = {
      eventId: event.id,
      ruleId: rule.id,
      actions: rule.actions,
      executedAt: new Date(),
      status: 'PENDING',
      results: [],
      metadata: {
        event,
        rule
      }
    };

    // TODO: Replace with actual database call when model is available
    // const createdResponse = await prisma.incidentResponse.create({
    //   data: response
    // });

    return { ...response, id: `response_${Date.now()}` };
  }

  private async executeResponseActions(response: IncidentResponse, event: any, rule: IncidentResponseRule): Promise<void> {
    await this.updateResponseStatus(response.id, 'EXECUTING');

    for (const action of rule.actions) {
      try {
        const result = await this.executeAction(action, event, rule);
        response.results.push(result);
      } catch (error) {
        response.results.push({
          actionType: action.type,
          success: false,
          message: error instanceof Error ? error.message : 'Unknown error',
          executedAt: new Date()
        });
      }
    }

    // TODO: Replace with actual database call when model is available
    // await prisma.incidentResponse.update({
    //   where: { id: response.id },
    //   data: { results: response.results as any }
    // });
  }

  private async executeAction(action: IncidentResponseAction, event: any, rule: IncidentResponseRule): Promise<ResponseActionResult> {
    // Add delay if specified
    if (action.delayMinutes && action.delayMinutes > 0) {
      await new Promise(resolve => setTimeout(resolve, action.delayMinutes! * 60 * 1000));
    }

    switch (action.type) {
      case 'ACCOUNT_LOCKOUT':
        return await this.executeAccountLockout(action.config, event);
      case 'SESSION_TERMINATION':
        return await this.executeSessionTermination(action.config, event);
      case 'IP_BLOCK':
        return await this.executeIPBlock(action.config, event);
      case 'NOTIFICATION':
        return await this.executeNotification(action.config, event, rule);
      case 'ESCALATION':
        return await this.executeEscalation(action.config, event, rule);
      case 'LOG_EVENT':
        return await this.executeLogEvent(action.config, event, rule);
      default:
        throw new Error(`Unsupported action type: ${action.type}`);
    }
  }

  private async executeAccountLockout(config: any, event: any): Promise<ResponseActionResult> {
    if (!event.userId) {
      throw new Error('Cannot lockout account: no user ID provided');
    }

    // Update user account to locked status
    await prisma.user.update({
      where: { id: event.userId },
      data: {
        // In a real implementation, you would have a locked field or status
        // For now, we'll log this action
      }
    });

    console.log('Account lockout executed:', {
      userId: event.userId,
      duration: config.lockoutDurationMinutes,
      notifyUser: config.notifyUser,
      notifyAdmins: config.notifyAdmins
    });

    return {
      actionType: 'ACCOUNT_LOCKOUT',
      success: true,
      message: `Account locked for ${config.lockoutDurationMinutes} minutes`,
      executedAt: new Date()
    };
  }

  private async executeSessionTermination(config: any, event: any): Promise<ResponseActionResult> {
    // Terminate user sessions
    if (event.userId) {
      await prisma.session.deleteMany({
        where: { userId: event.userId }
      });
    }

    console.log('Session termination executed:', {
      userId: event.userId,
      terminateAll: config.terminateAllUserSessions
    });

    return {
      actionType: 'SESSION_TERMINATION',
      success: true,
      message: 'User sessions terminated',
      executedAt: new Date()
    };
  }

  private async executeIPBlock(config: any, event: any): Promise<ResponseActionResult> {
    // In a real implementation, you would add the IP to a blocklist
    console.log('IP block executed:', {
      ipAddress: event.ipAddress,
      duration: config.blockDurationMinutes,
      blockType: config.blockType
    });

    return {
      actionType: 'IP_BLOCK',
      success: true,
      message: `IP ${event.ipAddress} blocked for ${config.blockDurationMinutes} minutes`,
      executedAt: new Date()
    };
  }

  private async executeNotification(config: any, event: any, rule: IncidentResponseRule): Promise<ResponseActionResult> {
    console.log('Notification sent:', {
      recipients: config.recipients,
      priority: config.priority,
      template: config.template,
      event,
      rule
    });

    return {
      actionType: 'NOTIFICATION',
      success: true,
      message: `Notification sent to ${config.recipients.join(', ')}`,
      executedAt: new Date()
    };
  }

  private async executeEscalation(config: any, event: any, rule: IncidentResponseRule): Promise<ResponseActionResult> {
    console.log('Escalation executed:', {
      level: config.escalationLevel,
      requireHumanReview: config.requireHumanReview,
      event,
      rule
    });

    return {
      actionType: 'ESCALATION',
      success: true,
      message: `Incident escalated to ${config.escalationLevel} level`,
      executedAt: new Date()
    };
  }

  private async executeLogEvent(config: any, event: any, rule: IncidentResponseRule): Promise<ResponseActionResult> {
    console.log('Event logged:', {
      config,
      event,
      rule
    });

    return {
      actionType: 'LOG_EVENT',
      success: true,
      message: 'Incident logged for review',
      executedAt: new Date()
    };
  }

  private async updateResponseStatus(responseId: string, status: IncidentResponse['status'], errorMessage?: string): Promise<void> {
    // TODO: Replace with actual database call when model is available
    // await prisma.incidentResponse.update({
    //   where: { id: responseId },
    //   data: {
    //     status,
    //     errorMessage,
    //     executedAt: status === 'COMPLETED' ? new Date() : undefined
    //   }
    // });
  }

  private async getLastResponseTime(ruleId: string, userId?: string, ipAddress?: string): Promise<Date | null> {
    // TODO: Replace with actual database call when model is available
    // const response = await prisma.incidentResponse.findFirst({
    //   where: {
    //     ruleId,
    //     ...(userId && { metadata: { path: ['event', 'userId'], equals: userId } }),
    //     ...(ipAddress && { metadata: { path: ['event', 'ipAddress'], equals: ipAddress } })
    //   },
    //   orderBy: { executedAt: 'desc' }
    // });

    return null;
  }

  async getIncidentResponses(filters: {
    ruleId?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<IncidentResponse[]> {
    // TODO: Replace with actual database call when model is available
    // const where: any = {};

    // if (filters.ruleId) where.ruleId = filters.ruleId;
    // if (filters.status) where.status = filters.status;

    // const responses = await prisma.incidentResponse.findMany({
    //   where,
    //   orderBy: { executedAt: 'desc' },
    //   take: filters.limit || 50,
    //   skip: filters.offset || 0
    // });

    return [];
  }

  async getResponseStatistics(timeframe: 'hour' | 'day' | 'week' | 'month'): Promise<any> {
    // TODO: Replace with actual database call when model is available
    // const now = new Date();
    // const startDate = new Date();

    // switch (timeframe) {
    //   case 'hour':
    //     startDate.setHours(now.getHours() - 1);
    //     break;
    //   case 'day':
    //     startDate.setDate(now.getDate() - 1);
    //     break;
    //   case 'week':
    //     startDate.setDate(now.getDate() - 7);
    //     break;
    //   case 'month':
    //     startDate.setMonth(now.getMonth() - 1);
    //     break;
    // }

    // const [totalResponses, responsesByStatus, responsesByRule, failedResponses] = await Promise.all([
    //   prisma.incidentResponse.count({
    //     where: { executedAt: { gte: startDate } }
    //   }),
    //   prisma.incidentResponse.groupBy({
    //     by: ['status'],
    //     where: { executedAt: { gte: startDate } },
    //     _count: { id: true }
    //   }),
    //   prisma.incidentResponse.groupBy({
    //     by: ['ruleId'],
    //     where: { executedAt: { gte: startDate } },
    //     _count: { id: true }
    //   }),
    //   prisma.incidentResponse.count({
    //     where: {
    //       executedAt: { gte: startDate },
    //       status: 'FAILED'
    //     }
    //   })
    // ]);

    return {
      totalResponses: 0,
      responsesByStatus: [],
      responsesByRule: [],
      failedResponses: 0,
      timeframe
    };
  }
}

export const securityIncidentResponse = new SecurityIncidentResponseService();