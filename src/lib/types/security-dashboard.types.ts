export const SECURITY_DASHBOARD_TIMEFRAMES = ['hour', 'day', 'week', 'month'] as const;
export type SecurityDashboardTimeframe = typeof SECURITY_DASHBOARD_TIMEFRAMES[number];

export const SECURITY_EVENT_SEVERITIES = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'] as const;
export type SecurityEventSeverity = typeof SECURITY_EVENT_SEVERITIES[number];

export const SECURITY_EVENT_STATUSES = ['OPEN', 'INVESTIGATING', 'RESOLVED', 'FALSE_POSITIVE'] as const;
export type SecurityEventStatus = typeof SECURITY_EVENT_STATUSES[number];

export const SECURITY_ALERT_STATUSES = ['SENT', 'FAILED', 'PENDING'] as const;
export type SecurityAlertStatus = typeof SECURITY_ALERT_STATUSES[number];

export const SECURITY_ALERT_CHANNEL_TYPES = ['EMAIL', 'SLACK', 'WEBHOOK', 'SMS', 'IN_APP'] as const;
export type SecurityAlertChannelType = typeof SECURITY_ALERT_CHANNEL_TYPES[number];

export interface SecurityEventPayload {
  id: string;
  type: string;
  severity: SecurityEventSeverity;
  description: string;
  source: string;
  userId?: string | null;
  serverId?: string | null;
  ipAddress: string;
  userAgent?: string | null;
  metadata: Record<string, unknown>;
  detectedAt: string;
  resolvedAt?: string | null;
  status: SecurityEventStatus;
  assignedTo?: string | null;
  resolution?: string | null;
}

export interface SecurityAlertPayload {
  id: string;
  eventId: string;
  ruleId: string;
  channels: Array<{
    type: SecurityAlertChannelType;
    config: Record<string, unknown>;
  }>;
  sentAt: string;
  status: SecurityAlertStatus;
  errorMessage?: string | null;
  metadata: Record<string, unknown>;
}

export interface SecurityDashboardStatsPayload {
  totalEvents: number;
  unresolvedEvents: number;
  totalAlerts: number;
  failedAlerts: number;
  timeframe: SecurityDashboardTimeframe;
  eventsByType: Array<{ type: string; count: number }>;
  eventsBySeverity: Array<{ severity: SecurityEventSeverity; count: number }>;
}

export interface SecurityDashboardApiResponse {
  events: SecurityEventPayload[];
  alerts: SecurityAlertPayload[];
  stats: SecurityDashboardStatsPayload;
}
