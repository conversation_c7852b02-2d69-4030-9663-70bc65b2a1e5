import { ApplicationStatus, PaymentStatus } from '@prisma/client';

/**
 * Centralized application workflow state machine
 * Handles all status transitions and business rule validation
 */

export interface TransitionContext {
  userId: string;
  adminId?: string;
  notes?: string;
  paymentData?: {
    amount: number;
    method: string;
    transactionId?: string;
  };
}

export interface TransitionResult {
  success: boolean;
  newStatus?: ApplicationStatus | PaymentStatus;
  error?: string;
  requiresPayment?: boolean;
  autoActivate?: boolean;
}

export class ApplicationStateMachine {
  private static readonly validTransitions: Record<ApplicationStatus, ApplicationStatus[]> = {
    [ApplicationStatus.APPLIED]: [ApplicationStatus.UNDER_REVIEW, ApplicationStatus.REJECTED],
    [ApplicationStatus.UNDER_REVIEW]: [ApplicationStatus.APPROVED, ApplicationStatus.REJECTED],
    [ApplicationStatus.APPROVED]: [ApplicationStatus.ACTIVE],
    [ApplicationStatus.REJECTED]: [ApplicationStatus.APPLIED],
    [ApplicationStatus.ACTIVE]: [], // Terminal state
  };

  private static readonly userInitiatedTransitions: Record<string, ApplicationStatus[]> = {
    'CANCEL': [ApplicationStatus.APPLIED, ApplicationStatus.UNDER_REVIEW, ApplicationStatus.REJECTED],
    'RESUBMIT': [ApplicationStatus.REJECTED],
    'WITHDRAW': [ApplicationStatus.APPLIED, ApplicationStatus.UNDER_REVIEW],
  };

  /**
   * Validate if a status transition is allowed
   */
  static validateApplicationTransition(
    currentStatus: ApplicationStatus,
    newStatus: ApplicationStatus,
    isUserInitiated: boolean = false,
    action?: string
  ): { valid: boolean; reason?: string } {
    // Check if transition exists in valid transitions
    const validTransitions = this.validTransitions[currentStatus] || [];
    
    if (!validTransitions.includes(newStatus)) {
      return {
        valid: false,
        reason: `Cannot transition from ${currentStatus} to ${newStatus}. Valid transitions: ${validTransitions.join(', ')}`,
      };
    }

    // Additional validation for user-initiated transitions
    if (isUserInitiated && action) {
      const allowedStatuses = this.userInitiatedTransitions[action] || [];
      if (!allowedStatuses.includes(currentStatus)) {
        return {
          valid: false,
          reason: `Action '${action}' not allowed from status ${currentStatus}`,
        };
      }
    }

    return { valid: true };
  }

  /**
   * Get all valid transitions from current status
   */
  static getValidTransitions(currentStatus: ApplicationStatus): ApplicationStatus[] {
    return this.validTransitions[currentStatus] || [];
  }

  /**
   * Execute status transition with business rule validation
   */
  static async executeTransition(
    applicationId: string,
    currentStatus: ApplicationStatus,
    newStatus: ApplicationStatus,
    context: TransitionContext,
    treatyRequiresPayment: boolean = false
  ): Promise<TransitionResult> {
    // Validate transition
    const validation = this.validateApplicationTransition(currentStatus, newStatus);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.reason,
      };
    }

    // Business rule validation
    const businessRuleValidation = this.validateBusinessRules(
      currentStatus,
      newStatus,
      context,
      treatyRequiresPayment
    );

    if (!businessRuleValidation.valid) {
      return {
        success: false,
        error: businessRuleValidation.reason,
      };
    }

    // Determine post-transition actions
    const result: TransitionResult = {
      success: true,
      newStatus,
    };

    // Handle payment-related logic
    if (newStatus === ApplicationStatus.APPROVED && treatyRequiresPayment) {
      result.requiresPayment = true;
    } else if (newStatus === ApplicationStatus.APPROVED && !treatyRequiresPayment) {
      result.autoActivate = true;
    }

    return result;
  }

  /**
   * Validate business rules for transitions
   */
  private static validateBusinessRules(
    currentStatus: ApplicationStatus,
    newStatus: ApplicationStatus,
    context: TransitionContext,
    treatyRequiresPayment: boolean
  ): { valid: boolean; reason?: string } {
    // Rule: Cannot approve without peace ambassador status
    if (newStatus === ApplicationStatus.APPROVED) {
      // This would be checked against user data
      // For now, we assume this is validated elsewhere
    }

    // Rule: Payment requirements must be consistent
    if (newStatus === ApplicationStatus.APPROVED && !treatyRequiresPayment) {
      // Auto-activation for free treaties
      return { valid: true };
    }

    // Rule: Rejection requires reason
    if (newStatus === ApplicationStatus.REJECTED && !context.notes) {
      return {
        valid: false,
        reason: 'Rejection requires a reason to be provided',
      };
    }

    // Rule: Cannot transition from ACTIVE
    if (currentStatus === ApplicationStatus.ACTIVE) {
      return {
        valid: false,
        reason: 'Cannot modify active applications',
      };
    }

    return { valid: true };
  }

  /**
   * Get next expected status based on current state
   */
  static getNextExpectedStatus(currentStatus: ApplicationStatus): ApplicationStatus[] {
    switch (currentStatus) {
      case ApplicationStatus.APPLIED:
        return [ApplicationStatus.UNDER_REVIEW, ApplicationStatus.REJECTED];
      case ApplicationStatus.UNDER_REVIEW:
        return [ApplicationStatus.APPROVED, ApplicationStatus.REJECTED];
      case ApplicationStatus.APPROVED:
        return [ApplicationStatus.ACTIVE];
      case ApplicationStatus.REJECTED:
        return [ApplicationStatus.APPLIED];
      default:
        return [];
    }
  }

  /**
   * Check if application is in a terminal state
   */
  static isTerminalState(status: ApplicationStatus): boolean {
    return status === ApplicationStatus.ACTIVE;
  }

  /**
   * Get status description for user display
   */
  static getStatusDescription(status: ApplicationStatus): string {
    switch (status) {
      case ApplicationStatus.APPLIED:
        return 'Application submitted and awaiting review';
      case ApplicationStatus.UNDER_REVIEW:
        return 'Application is currently under review';
      case ApplicationStatus.APPROVED:
        return 'Application approved - payment may be required';
      case ApplicationStatus.REJECTED:
        return 'Application was rejected';
      case ApplicationStatus.ACTIVE:
        return 'Treaty is active and in good standing';
      default:
        return 'Unknown status';
    }
  }
}

/**
 * Payment status state machine
 */
export class PaymentStateMachine {
  private static readonly validTransitions: Record<PaymentStatus, PaymentStatus[]> = {
    [PaymentStatus.NOT_REQUIRED]: [], // Terminal state
    [PaymentStatus.AWAITING_PAYMENT]: [PaymentStatus.PENDING],
    [PaymentStatus.PENDING]: [PaymentStatus.PAID, PaymentStatus.FAILED],
    [PaymentStatus.PAID]: [PaymentStatus.REFUNDED],
    [PaymentStatus.FAILED]: [PaymentStatus.PENDING], // Allow retry
    [PaymentStatus.REFUNDED]: [], // Terminal state
  };

  /**
   * Validate payment status transition
   */
  static validatePaymentTransition(
    currentStatus: PaymentStatus,
    newStatus: PaymentStatus
  ): { valid: boolean; reason?: string } {
    const validTransitions = this.validTransitions[currentStatus] || [];
    
    if (!validTransitions.includes(newStatus)) {
      return {
        valid: false,
        reason: `Cannot transition from ${currentStatus} to ${newStatus}. Valid transitions: ${validTransitions.join(', ')}`,
      };
    }

    return { valid: true };
  }

  /**
   * Execute payment status transition
   */
  static async executePaymentTransition(
    paymentId: string,
    currentStatus: PaymentStatus,
    newStatus: PaymentStatus,
    context: {
      adminId: string;
      notes?: string;
      amount?: number;
    }
  ): Promise<TransitionResult> {
    // Validate transition
    const validation = this.validatePaymentTransition(currentStatus, newStatus);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.reason,
      };
    }

    // Business rule validation
    if (newStatus === PaymentStatus.PAID && !context.adminId) {
      return {
        success: false,
        error: 'Payment verification requires admin authorization',
      };
    }

    if (newStatus === PaymentStatus.FAILED && !context.notes) {
      return {
        success: false,
        error: 'Payment failure requires explanation',
      };
    }

    return {
      success: true,
      newStatus,
    };
  }

  /**
   * Get payment status description
   */
  static getPaymentStatusDescription(status: PaymentStatus): string {
    switch (status) {
      case PaymentStatus.NOT_REQUIRED:
        return 'No payment required';
      case PaymentStatus.AWAITING_PAYMENT:
        return 'Payment is required to activate treaty';
      case PaymentStatus.PENDING:
        return 'Payment submitted and awaiting verification';
      case PaymentStatus.PAID:
        return 'Payment verified and complete';
      case PaymentStatus.FAILED:
        return 'Payment verification failed';
      case PaymentStatus.REFUNDED:
        return 'Payment has been refunded';
      default:
        return 'Unknown payment status';
    }
  }
}

/**
 * Combined workflow coordinator
 */
export class WorkflowCoordinator {
  /**
   * Handle complete application workflow including payment logic
   */
  static async handleApplicationWorkflow(
    applicationId: string,
    currentApplicationStatus: ApplicationStatus,
    currentPaymentStatus: PaymentStatus | null,
    targetApplicationStatus: ApplicationStatus,
    context: TransitionContext,
    treatyRequiresPayment: boolean = false
  ): Promise<{
    applicationResult: TransitionResult;
    paymentResult?: TransitionResult;
    activationRequired: boolean;
  }> {
    const applicationResult = await ApplicationStateMachine.executeTransition(
      applicationId,
      currentApplicationStatus,
      targetApplicationStatus,
      context,
      treatyRequiresPayment
    );

    if (!applicationResult.success) {
      return {
        applicationResult,
        activationRequired: false,
      };
    }

    // Handle payment-related status changes
    let paymentResult: TransitionResult | undefined;
    let activationRequired = false;

    if (applicationResult.requiresPayment) {
      // Transition to AWAITING_PAYMENT
      paymentResult = {
        success: true,
        newStatus: PaymentStatus.AWAITING_PAYMENT,
      };
      activationRequired = false;
    } else if (applicationResult.autoActivate) {
      // Auto-activate free treaties
      activationRequired = true;
    } else if (targetApplicationStatus === ApplicationStatus.ACTIVE) {
      // Manual activation after payment
      activationRequired = true;
    }

    return {
      applicationResult,
      paymentResult,
      activationRequired,
    };
  }
}