import { prisma } from '@/lib/prisma';

export async function checkAdmin(userId: string): Promise<boolean> {
  try {
    if (!userId) {
      return false;
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user) {
      return false;
    }

    const isAdmin = user.userRoles.some(userRole =>
      userRole.role.name === 'admin'
    );

    return isAdmin;
  } catch (error) {
    console.error('Error checking admin permissions:', error);
    return false;
  }
}