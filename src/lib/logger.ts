export interface Logger {
  info(message: string, meta?: Record<string, any>): void;
  error(message: string, meta?: Record<string, any>, error?: Error): void;
  warn(message: string, meta?: Record<string, any>): void;
  debug(message: string, meta?: Record<string, any>): void;
}

class ConsoleLogger implements Logger {
  private formatMessage(level: string, message: string, meta?: Record<string, any>, error?: Error): string {
    const timestamp = new Date().toISOString();
    const metaStr = meta ? ` ${JSON.stringify(meta)}` : '';
    const errorStr = error ? ` Error: ${error.message}${error.stack ? `\n${error.stack}` : ''}` : '';
    return `[${timestamp}] [${level}] ${message}${metaStr}${errorStr}`;
  }

  info(message: string, meta?: Record<string, any>): void {
    if (process.env.NODE_ENV !== 'test') {
      console.log(this.formatMessage('INFO', message, meta));
    }
  }

  error(message: string, meta?: Record<string, any>, error?: Error): void {
    console.error(this.formatMessage('ERROR', message, meta, error));
  }

  warn(message: string, meta?: Record<string, any>): void {
    if (process.env.NODE_ENV !== 'test') {
      console.warn(this.formatMessage('WARN', message, meta));
    }
  }

  debug(message: string, meta?: Record<string, any>): void {
    if (process.env.NODE_ENV === 'development') {
      console.debug(this.formatMessage('DEBUG', message, meta));
    }
  }
}

export const logger: Logger = new ConsoleLogger();