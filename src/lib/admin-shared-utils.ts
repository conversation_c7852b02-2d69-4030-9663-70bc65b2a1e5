'use client';

import { toast } from 'sonner';

// Shared interfaces
export interface Title {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
}

export interface Position {
  id: string;
  title: string;
  description: string | null;
  level: number;
  parentId: string | null;
  isActive: boolean;
}

export interface Country {
  id: string;
  name: string;
}

export interface City {
  id: string;
  name: string;
}

export interface Ordinance {
  id: string;
  name: string;
  description: string;
}

// Shared API functions
export const fetchTitles = async (): Promise<Title[]> => {
  try {
    const response = await fetch('/api/positions/titles');

    if (!response.ok) {
      if (response.status === 403) {
        toast.info('Title selection is only available to administrators');
        return [];
      }
      throw new Error('Failed to fetch titles');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching titles:', error);
    toast.error('Failed to load titles');
    return [];
  }
};

export const fetchPositions = async (): Promise<Position[]> => {
  try {
    const response = await fetch('/api/positions/positions');

    if (!response.ok) {
      if (response.status === 403) {
        toast.info('Position selection is only available to administrators');
        return [];
      }
      throw new Error('Failed to fetch positions');
    }

    const data = await response.json();
    return data.positions || [];
  } catch (error) {
    console.error('Error fetching positions:', error);
    toast.error('Failed to load positions');
    return [];
  }
};

export const fetchOrdinances = async (): Promise<Ordinance[]> => {
  try {
    const response = await fetch('/api/ordinance-types');

    if (!response.ok) {
      if (response.status === 404) {
        toast.info('No ordinance types are currently available');
        return [];
      }
      throw new Error('Failed to fetch ordinances');
    }

    const data = await response.json();
    return data.ordinanceTypes || [];
  } catch (error) {
    console.error('Error fetching ordinances:', error);
    toast.error('Failed to load ordinances');
    return [];
  }
};

export const fetchCountries = async (query: string): Promise<Country[]> => {
  try {
    const response = await fetch(`/api/countries?q=${encodeURIComponent(query)}`);
    if (response.ok) {
      const data = await response.json();
      return data.countries || data || [];
    }
    return [];
  } catch (error) {
    console.error('Error fetching countries:', error);
    return [];
  }
};

export const fetchCities = async (query: string, countryId: string): Promise<City[]> => {
  try {
    const response = await fetch(`/api/cities?q=${encodeURIComponent(query)}&countryId=${encodeURIComponent(countryId)}`);
    if (response.ok) {
      const data = await response.json();
      return data.cities || data || [];
    }
    return [];
  } catch (error) {
    console.error('Error fetching cities:', error);
    return [];
  }
};

// Shared form validation
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validateRequired = (value: string, fieldName: string): boolean => {
  if (!value.trim()) {
    toast.error(`${fieldName} is required`);
    return false;
  }
  return true;
};

// Debounce utility
export const debounce = <T extends (...args: any[]) => void>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};
