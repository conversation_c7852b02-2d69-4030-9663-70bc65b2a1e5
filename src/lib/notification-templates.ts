// Notification Templates System
// Provides predefined templates for common notification types

export interface NotificationTemplate {
  type: string;
  message: string;
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  category: 'USER_MANAGEMENT' | 'TREATY' | 'REMOTE_SERVER' | 'SYSTEM' | 'SECURITY';
  trigger: string;
  variables?: string[]; // Variables that can be replaced in the message
}

export interface TemplateVariables {
  [key: string]: string | number | boolean;
}

// Predefined notification templates
export const NOTIFICATION_TEMPLATES: Record<string, NotificationTemplate> = {
  // User Management Templates
  USER_CREATED: {
    type: 'USER_CREATED',
    message: 'New user {userEmail} has registered and requires approval',
    priority: 'HIGH',
    category: 'USER_MANAGEMENT',
    trigger: 'user_registration',
    variables: ['userEmail'],
  },

  USER_APPROVED: {
    type: 'USER_APPROVED',
    message: 'User {userEmail} has been approved and can now access the system',
    priority: 'NORMAL',
    category: 'USER_MANAGEMENT',
    trigger: 'user_approval',
    variables: ['userEmail'],
  },

  USER_REJECTED: {
    type: 'USER_REJECTED',
    message: 'User {userEmail} registration has been rejected',
    priority: 'NORMAL',
    category: 'USER_MANAGEMENT',
    trigger: 'user_rejection',
    variables: ['userEmail'],
  },

  USER_SUSPENDED: {
    type: 'USER_SUSPENDED',
    message: 'User {userEmail} has been suspended due to policy violation',
    priority: 'HIGH',
    category: 'USER_MANAGEMENT',
    trigger: 'user_suspension',
    variables: ['userEmail'],
  },

  // Treaty Management Templates
  TREATY_CREATED: {
    type: 'TREATY_CREATED',
    message: 'New treaty "{treatyName}" has been created and requires review',
    priority: 'NORMAL',
    category: 'TREATY',
    trigger: 'treaty_creation',
    variables: ['treatyName'],
  },

  TREATY_APPROVED: {
    type: 'TREATY_APPROVED',
    message: 'Treaty "{treatyName}" has been approved and is now active',
    priority: 'NORMAL',
    category: 'TREATY',
    trigger: 'treaty_approval',
    variables: ['treatyName'],
  },

  TREATY_EXPIRED: {
    type: 'TREATY_EXPIRED',
    message: 'Treaty "{treatyName}" has expired and requires renewal',
    priority: 'HIGH',
    category: 'TREATY',
    trigger: 'treaty_expiry',
    variables: ['treatyName'],
  },

  TREATY_EXPIRING_SOON: {
    type: 'TREATY_EXPIRING_SOON',
    message: 'Treaty "{treatyName}" is expiring in {daysUntilExpiry} days',
    priority: 'NORMAL',
    category: 'TREATY',
    trigger: 'treaty_expiry_warning',
    variables: ['treatyName', 'daysUntilExpiry'],
  },

  // Security Templates
  FAILED_LOGIN_ATTEMPT: {
    type: 'FAILED_LOGIN_ATTEMPT',
    message: 'Multiple failed login attempts detected for user {userEmail} from IP {ipAddress}',
    priority: 'HIGH',
    category: 'SECURITY',
    trigger: 'security_monitoring',
    variables: ['userEmail', 'ipAddress'],
  },

  SUSPICIOUS_ACTIVITY: {
    type: 'SUSPICIOUS_ACTIVITY',
    message: 'Suspicious activity detected: {activityDescription}',
    priority: 'URGENT',
    category: 'SECURITY',
    trigger: 'security_monitoring',
    variables: ['activityDescription'],
  },

  PASSWORD_CHANGED: {
    type: 'PASSWORD_CHANGED',
    message: 'Password has been changed for user {userEmail}',
    priority: 'NORMAL',
    category: 'SECURITY',
    trigger: 'password_change',
    variables: ['userEmail'],
  },

  // Remote Server Templates
  SERVER_CONNECTION_FAILED: {
    type: 'SERVER_CONNECTION_FAILED',
    message: 'Failed to connect to remote server "{serverName}"',
    priority: 'HIGH',
    category: 'REMOTE_SERVER',
    trigger: 'server_connection_check',
    variables: ['serverName'],
  },

  SERVER_PERMISSION_SYNC_FAILED: {
    type: 'SERVER_PERMISSION_SYNC_FAILED',
    message: 'Permission synchronization failed for server "{serverName}"',
    priority: 'HIGH',
    category: 'REMOTE_SERVER',
    trigger: 'permission_sync',
    variables: ['serverName'],
  },

  SERVER_PERMISSION_SYNC_SUCCESS: {
    type: 'SERVER_PERMISSION_SYNC_SUCCESS',
    message: 'Permission synchronization completed successfully for server "{serverName}"',
    priority: 'LOW',
    category: 'REMOTE_SERVER',
    trigger: 'permission_sync',
    variables: ['serverName'],
  },

  // System Templates
  SYSTEM_MAINTENANCE: {
    type: 'SYSTEM_MAINTENANCE',
    message: 'System maintenance scheduled for {maintenanceDate} at {maintenanceTime}',
    priority: 'NORMAL',
    category: 'SYSTEM',
    trigger: 'system_maintenance',
    variables: ['maintenanceDate', 'maintenanceTime'],
  },

  BACKUP_COMPLETED: {
    type: 'BACKUP_COMPLETED',
    message: 'System backup completed successfully on {backupDate}',
    priority: 'LOW',
    category: 'SYSTEM',
    trigger: 'backup_process',
    variables: ['backupDate'],
  },

  BACKUP_FAILED: {
    type: 'BACKUP_FAILED',
    message: 'System backup failed on {backupDate}. Please check system logs.',
    priority: 'HIGH',
    category: 'SYSTEM',
    trigger: 'backup_process',
    variables: ['backupDate'],
  },
};

/**
 * Get a notification template by type
 */
export function getNotificationTemplate(type: string): NotificationTemplate | null {
  return NOTIFICATION_TEMPLATES[type] || null;
}

/**
 * Create a notification using a template with variable substitution
 */
export function createNotificationFromTemplate(
  templateType: string,
  variables: TemplateVariables,
  additionalData?: {
    recipients?: string[];
    triggeredBy?: string;
    data?: any;
  }
): NotificationTemplate & { recipients: string[]; triggeredBy?: string; data?: any } | null {
  const template = getNotificationTemplate(templateType);

  if (!template) {
    return null;
  }

  // Replace variables in the message
  let message = template.message;
  for (const variable of template.variables || []) {
    const value = variables[variable];
    if (value !== undefined) {
      message = message.replace(`{${variable}}`, String(value));
    }
  }

  return {
    ...template,
    message,
    recipients: additionalData?.recipients || ['admin'],
    triggeredBy: additionalData?.triggeredBy,
    data: additionalData?.data,
  };
}

/**
 * Get all available template types
 */
export function getAvailableTemplateTypes(): string[] {
  return Object.keys(NOTIFICATION_TEMPLATES);
}

/**
 * Get templates by category
 */
export function getTemplatesByCategory(category: string): NotificationTemplate[] {
  return Object.values(NOTIFICATION_TEMPLATES).filter(
    template => template.category === category
  );
}

/**
 * Validate template variables
 */
export function validateTemplateVariables(
  templateType: string,
  variables: TemplateVariables
): { valid: boolean; missing: string[]; extra: string[] } {
  const template = getNotificationTemplate(templateType);

  if (!template) {
    return { valid: false, missing: [], extra: [] };
  }

  const required = template.variables || [];
  const provided = Object.keys(variables);

  const missing = required.filter(varName => !provided.includes(varName));
  const extra = provided.filter(varName => !required.includes(varName));

  return {
    valid: missing.length === 0,
    missing,
    extra,
  };
}