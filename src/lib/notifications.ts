import { prisma } from './prisma';

// Store connected clients for SSE
const clients = new Map();

// Clean up disconnected clients periodically
setInterval(() => {
  const now = Date.now();
  clients.forEach((client, id) => {
    if (now - client.lastActivity > 30000) { // 30 seconds inactivity
      client.controller.abort();
      clients.delete(id);
    }
  });
}, 10000);

// Function to add client to SSE connections
export function addClient(clientId: string, clientData: any) {
  clients.set(clientId, { ...clientData, lastActivity: Date.now() });
}

// Function to remove client from SSE connections
export function removeClient(clientId: string) {
  clients.delete(clientId);
}

// Function to broadcast notifications to relevant clients
export async function broadcastNotification(notificationData: any) {
  try {
    const encoder = new TextEncoder();
    const message = `data: ${JSON.stringify({ type: 'notification', data: notificationData })}\n\n`;

    clients.forEach(async (client, clientId) => {
      // Check if client should receive this notification
      const shouldReceive = notificationData.recipients.includes(client.userId) || 
                           (notificationData.recipients.includes('admin') && await isUserAdmin(client.userId));
      
      if (shouldReceive) {
        try {
          client.controller.enqueue(encoder.encode(message));
          client.lastActivity = Date.now();
        } catch (error) {
          // Client disconnected, remove from map
          clients.delete(clientId);
        }
      }
    });
  } catch (error) {
    console.error('Error broadcasting notification:', error);
  }
}

// Helper function to check if user is admin
async function isUserAdmin(userId: string): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    return user?.userRoles.some(ur => ur.role.name === 'admin') || false;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// Function to send heartbeat to keep connections alive
export function sendHeartbeat() {
  const encoder = new TextEncoder();
  const message = `data: ${JSON.stringify({ type: 'heartbeat', timestamp: Date.now() })}\n\n`;

  clients.forEach((client, clientId) => {
    try {
      client.controller.enqueue(encoder.encode(message));
      client.lastActivity = Date.now();
    } catch (error) {
      clients.delete(clientId);
    }
  });
}

// Send heartbeat every 25 seconds to keep connections alive
setInterval(sendHeartbeat, 25000);