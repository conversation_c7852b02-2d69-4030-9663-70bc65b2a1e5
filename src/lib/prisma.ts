import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

// Environment detection for Edge Runtime compatibility
// Check for Edge Runtime by looking for global EdgeRuntime or Web APIs
const isEdgeRuntime = typeof globalThis !== 'undefined' &&
  ('EdgeRuntime' in globalThis ||
   // Additional checks for Edge Runtime environment
   (typeof WebAssembly !== 'undefined' && typeof globalThis.process === 'undefined'))

const isNodeEnvironment = !isEdgeRuntime &&
  typeof process !== 'undefined' &&
  typeof process === 'object' &&
  process !== null

// Create Prisma client with environment-aware configuration
const createPrismaClient = () => {
  const config: any = {}

  // Only add log configuration in Node.js environment
  if (isNodeEnvironment && process.env?.NODE_ENV === 'development') {
    config.log = ['query', 'info', 'warn', 'error']
  } else if (isNodeEnvironment) {
    config.log = ['error']
  }

  // Only add datasource configuration in Node.js environment
  if (isNodeEnvironment && process.env?.DATABASE_URL) {
    config.datasources = {
      db: {
        url: process.env.DATABASE_URL,
      },
    }
  }

  return new PrismaClient(config)
}

export const prisma =
  globalForPrisma.prisma ??
  createPrismaClient()

// Only set global instance in Node.js environment
if (isNodeEnvironment && process.env?.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma
}

// Graceful shutdown (only in Node.js environment)
if (isNodeEnvironment) {
  // Use a more definitive approach to prevent Edge Runtime bundling
  const loadShutdownModule = () => {
    // Comprehensive runtime check
    if (typeof window !== 'undefined' ||
        typeof process === 'undefined' ||
        process.env.NEXT_RUNTIME !== 'nodejs') {
      return;
    }

    try {
      // Use require with explicit check to avoid bundling
      if (typeof require !== 'undefined') {
        const setupModule = require('./prisma-shutdown.node');
        if (setupModule && typeof setupModule.setupGracefulShutdown === 'function') {
          setupModule.setupGracefulShutdown(prisma);
        }
      }
    } catch (error) {
      // Silently ignore any errors - this prevents console noise in production
    }
  };

  // Execute only if we're definitely in Node.js
  if (typeof window === 'undefined' && typeof process !== 'undefined') {
    loadShutdownModule();
  }
}