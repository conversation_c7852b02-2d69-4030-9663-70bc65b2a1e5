'use client';

export interface ClientAuditLogEntry {
  projectId: string;
  userId?: string;
  action: string;
  resource?: string;
  success: boolean;
  method: string;
  endpoint: string;
  statusCode?: number;
  errorMessage?: string;
  ipAddress: string;
  userAgent: string;
  duration?: number;
  metadata?: Record<string, any>;
}

export async function logClientAuditEvent(entry: ClientAuditLogEntry): Promise<void> {
  try {
    const response = await fetch('/api/audit/log', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(entry),
    });

    if (!response.ok) {
      console.warn('Failed to log audit event:', response.statusText);
    }
  } catch (error) {
    console.warn('Error sending audit log:', error);
    // Silently fail - audit logging should not break the application
  }
}

export async function logApiAccess(
  projectId: string,
  userId: string | undefined,
  action: string,
  resource: string,
  success: boolean,
  requestUrl: string,
  options?: {
    statusCode?: number;
    errorMessage?: string;
    duration?: number;
    metadata?: Record<string, any>;
  }
): Promise<void> {
  const url = new URL(requestUrl);
  
  const entry: ClientAuditLogEntry = {
    projectId,
    userId,
    action,
    resource,
    success,
    method: 'GET', // Default for client-side navigation
    endpoint: url.pathname,
    statusCode: options?.statusCode,
    errorMessage: options?.errorMessage,
    ipAddress: 'client', // Client-side doesn't have real IP
    userAgent: navigator.userAgent,
    duration: options?.duration,
    metadata: options?.metadata,
  };

  await logClientAuditEvent(entry);
}