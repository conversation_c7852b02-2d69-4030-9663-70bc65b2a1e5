'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';

export interface DashboardUser {
  id: string;
  name: string;
  email: string;
  role: 'PEACE' | 'MEMBER' | 'ADMIN';
  permissions: string[];
  avatar?: string;
}

export interface DashboardSettings {
  theme: 'light' | 'dark' | 'system';
  refreshInterval: number;
  notifications: {
    enabled: boolean;
    sound: boolean;
    desktop: boolean;
  };
  layout: {
    compact: boolean;
    sidebarCollapsed: boolean;
  };
}

export interface DashboardState {
  user: DashboardUser | null;
  settings: DashboardSettings;
  activeTab: string;
  loading: {
    user: boolean;
    settings: boolean;
    tabs: boolean;
  };
  error: string | null;
  lastUpdated: Date | null;
}

type DashboardAction =
  | { type: 'SET_USER'; payload: DashboardUser }
  | { type: 'SET_SETTINGS'; payload: Partial<DashboardSettings> }
  | { type: 'SET_ACTIVE_TAB'; payload: string }
  | { type: 'SET_LOADING'; payload: { key: keyof DashboardState['loading']; value: boolean } }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'UPDATE_LAST_UPDATED'; payload: Date }
  | { type: 'RESET_STATE' };

const initialState: DashboardState = {
  user: null,
  settings: {
    theme: 'system',
    refreshInterval: 30000, // 30 seconds
    notifications: {
      enabled: true,
      sound: true,
      desktop: false,
    },
    layout: {
      compact: false,
      sidebarCollapsed: false,
    },
  },
  activeTab: 'overview',
  loading: {
    user: true,
    settings: false,
    tabs: false,
  },
  error: null,
  lastUpdated: null,
};

function dashboardReducer(state: DashboardState, action: DashboardAction): DashboardState {
  switch (action.type) {
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        loading: { ...state.loading, user: false },
        error: null,
      };

    case 'SET_SETTINGS':
      return {
        ...state,
        settings: { ...state.settings, ...action.payload },
        loading: { ...state.loading, settings: false },
      };

    case 'SET_ACTIVE_TAB':
      return {
        ...state,
        activeTab: action.payload,
      };

    case 'SET_LOADING':
      return {
        ...state,
        loading: { ...state.loading, [action.payload.key]: action.payload.value },
      };

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        loading: { ...state.loading, user: false, settings: false, tabs: false },
      };

    case 'UPDATE_LAST_UPDATED':
      return {
        ...state,
        lastUpdated: action.payload,
      };

    case 'RESET_STATE':
      return initialState;

    default:
      return state;
  }
}

interface DashboardContextType {
  state: DashboardState;
  dispatch: React.Dispatch<DashboardAction>;
  // Convenience methods
  setUser: (user: DashboardUser) => void;
  setSettings: (settings: Partial<DashboardSettings>) => void;
  setActiveTab: (tab: string) => void;
  setLoading: (key: keyof DashboardState['loading'], value: boolean) => void;
  setError: (error: string | null) => void;
  updateLastUpdated: () => void;
  resetState: () => void;
  // Computed values
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  isAdmin: boolean;
  isMember: boolean;
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

export function DashboardProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(dashboardReducer, initialState);

  // Convenience methods
  const setUser = (user: DashboardUser) => {
    dispatch({ type: 'SET_USER', payload: user });
  };

  const setSettings = (settings: Partial<DashboardSettings>) => {
    dispatch({ type: 'SET_SETTINGS', payload: settings });
  };

  const setActiveTab = (tab: string) => {
    dispatch({ type: 'SET_ACTIVE_TAB', payload: tab });
  };

  const setLoading = (key: keyof DashboardState['loading'], value: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: { key, value } });
  };

  const setError = (error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  };

  const updateLastUpdated = () => {
    dispatch({ type: 'UPDATE_LAST_UPDATED', payload: new Date() });
  };

  const resetState = () => {
    dispatch({ type: 'RESET_STATE' });
  };

  // Computed values
  const hasPermission = (permission: string): boolean => {
    return state.user?.permissions.includes(permission) ?? false;
  };

  const hasRole = (role: string): boolean => {
    return state.user?.role === role;
  };

  const isAdmin = state.user?.role === 'ADMIN';
  const isMember = state.user?.role === 'MEMBER' || state.user?.role === 'ADMIN';

  // Load user data on mount
  useEffect(() => {
    const loadUserData = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: { key: 'user', value: true } });

        // TODO: Replace with actual API calls
        const userResponse = await fetch('/api/user/profile');
        if (userResponse.ok) {
          const userData = await userResponse.json();
          dispatch({ type: 'SET_USER', payload: userData });
        } else {
          dispatch({ type: 'SET_ERROR', payload: 'Failed to load user data' });
        }
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: 'Error loading user data' });
      }
    };

    loadUserData();
  }, []);

  // Load settings from localStorage
  useEffect(() => {
    const loadSettings = () => {
      try {
        const savedSettings = localStorage.getItem('dashboard-settings');
        if (savedSettings) {
          const parsedSettings = JSON.parse(savedSettings);
          dispatch({ type: 'SET_SETTINGS', payload: parsedSettings });
        }
      } catch (error) {
        console.error('Failed to load dashboard settings:', error);
      }
    };

    loadSettings();
  }, []);

  // Save settings to localStorage when they change
  useEffect(() => {
    if (state.settings) {
      localStorage.setItem('dashboard-settings', JSON.stringify(state.settings));
    }
  }, [state.settings]);

  const contextValue: DashboardContextType = {
    state,
    dispatch,
    setUser,
    setSettings,
    setActiveTab,
    setLoading,
    setError,
    updateLastUpdated,
    resetState,
    hasPermission,
    hasRole,
    isAdmin,
    isMember,
  };

  return (
    <DashboardContext.Provider value={contextValue}>
      {children}
    </DashboardContext.Provider>
  );
}

export function useDashboard(): DashboardContextType {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
}

// Hook for checking specific permissions
export function usePermission(permission: string): boolean {
  const { hasPermission } = useDashboard();
  return hasPermission(permission);
}

// Hook for checking specific roles
export function useRole(role: string): boolean {
  const { hasRole } = useDashboard();
  return hasRole(role);
}

// Hook for admin-only content
export function useAdmin(): boolean {
  const { isAdmin } = useDashboard();
  return isAdmin;
}

// Hook for member or admin content
export function useMember(): boolean {
  const { isMember } = useDashboard();
  return isMember;
}