'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useSession } from 'next-auth/react';

export interface UserPermissions {
  id: string;
  userId: string;
  permissions: Permission[];
  roles: Role[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
}

export interface PermissionContextType {
  permissions: UserPermissions | null;
  loading: boolean;
  error: string | null;
  hasPermission: (resource: string, action: string) => boolean;
  hasAnyPermission: (permissions: Array<{ resource: string; action: string }>) => boolean;
  hasAllPermissions: (permissions: Array<{ resource: string; action: string }>) => boolean;
  hasRole: (roleName: string) => boolean;
  refreshPermissions: () => Promise<void>;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  canAccessAdmin: boolean;
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined);

interface PermissionProviderProps {
  children: ReactNode;
}

export function PermissionProvider({ children }: PermissionProviderProps) {
  const [permissions, setPermissions] = useState<UserPermissions | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { data: session, status } = useSession();

  const fetchPermissions = async () => {
    try {
      console.log('PermissionContext: Fetching permissions...');
      setLoading(true);
      setError(null);

      const response = await fetch('/api/user/permissions');
      console.log('PermissionContext: Response status:', response.status);

      // Handle 401 (Unauthorized) gracefully - user is not logged in
      if (response.status === 401) {
        console.log('PermissionContext: User not authenticated, setting empty permissions');
        setPermissions(null);
        setLoading(false);
        return;
      }

      if (!response.ok) {
        console.error('PermissionContext: Failed to fetch permissions, status:', response.status);
        throw new Error('Failed to fetch permissions');
      }

      const data = await response.json();
      console.log('PermissionContext: Fetched permissions data:', data);
      setPermissions(data);
    } catch (err) {
      // Only set error for non-401 errors (network errors, server errors, etc.)
      if (err instanceof Error && !err.message.includes('401')) {
        console.error('PermissionContext: Error fetching permissions:', err);
        setError(err.message);
      } else {
        console.log('PermissionContext: Authentication error, setting empty permissions');
        setPermissions(null);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Only fetch permissions if user is authenticated
    if (status === 'authenticated') {
      fetchPermissions();
    } else if (status === 'unauthenticated') {
      // User is not authenticated, set empty permissions
      setPermissions(null);
      setLoading(false);
      setError(null);
    } else if (status === 'loading') {
      // Session is still loading, keep loading state
      setLoading(true);
    }
  }, [status]);

  const hasPermission = (resource: string, action: string): boolean => {
    if (!permissions) return false;

    return permissions.permissions.some(
      (permission) => permission.resource === resource && permission.action === action
    );
  };

  const hasAnyPermission = (permissionsToCheck: Array<{ resource: string; action: string }>): boolean => {
    if (!permissions) return false;

    return permissionsToCheck.some(({ resource, action }) => hasPermission(resource, action));
  };

  const hasAllPermissions = (permissionsToCheck: Array<{ resource: string; action: string }>): boolean => {
    if (!permissions) return false;

    return permissionsToCheck.every(({ resource, action }) => hasPermission(resource, action));
  };

  const hasRole = (roleName: string): boolean => {
    if (!permissions) return false;

    return permissions.roles.some((role) => role.name === roleName);
  };

  const refreshPermissions = async (): Promise<void> => {
    await fetchPermissions();
  };

  // Computed properties for common permission checks
  const isAdmin = hasRole('ADMIN') || hasRole('admin') || hasRole('SUPER_ADMIN');
  const isSuperAdmin = hasRole('SUPER_ADMIN');
  const canAccessAdmin = hasPermission('ADMIN_PANEL', 'ACCESS') || isAdmin;

  const value: PermissionContextType = {
    permissions,
    loading,
    error,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    refreshPermissions,
    isAdmin,
    isSuperAdmin,
    canAccessAdmin,
  };

  return (
    <PermissionContext.Provider value={value}>
      {children}
    </PermissionContext.Provider>
  );
}

export function usePermissions(): PermissionContextType {
  const context = useContext(PermissionContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionProvider');
  }
  return context;
}