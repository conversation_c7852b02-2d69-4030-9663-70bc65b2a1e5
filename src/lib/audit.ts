import { prisma } from './prisma';

export interface AuditLogData {
  userId: string;
  action: string;
  resource?: string;
  resourceId?: string;
  data?: Record<string, any>;
}

/**
 * Simple audit logging function for nation treaty operations
 */
export async function logAudit(
  userId: string, 
  action: string, 
  data: Record<string, any> = {}
): Promise<void> {
  try {
    // Verify the user exists before creating audit log
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      console.warn(`User with ID ${userId} not found. Skipping audit log for action: ${action}`);
      return;
    }

    await prisma.auditLog.create({
      data: {
        userId,
        action,
        resource: data.resource || 'nation_treaty',
        resourceId: data.resourceId,
        newValues: data.data || data,
        timestamp: new Date(),
        success: true,
        metadata: {
          ...data,
          userAgent: 'system',
          ipAddress: 'localhost'
        }
      }
    });
  } catch (error) {
    console.error('Failed to log audit entry:', error);
    // Don't throw error to avoid disrupting the main operation
  }
}