/**
 * Development utility to highlight components with unused imports
 * Add this to your development environment to visually identify issues
 */

export interface UnusedImportInfo {
  file: string;
  line: number;
  variable: string;
  type: 'import' | 'variable';
}

// Map of files with unused imports for visual highlighting
export const FILES_WITH_UNUSED_IMPORTS = new Set([
  '/src/app/actions/system-settings.ts',
  '/src/app/admin/manage/components/CreateUserTab.tsx',
  '/src/app/admin/manage/components/EnhancedTreatyCreation.tsx',
  '/src/app/admin/manage/components/NotificationManagement.tsx',
  '/src/app/admin/manage/components/SearchUserTab.tsx',
  '/src/app/admin/manage/components/TreatyTypeTab.tsx',
  '/src/app/admin/manage/components/UpdateUserTab.tsx',
  '/src/app/admin/manage/components/UserManagementTab.tsx',
  '/src/app/admin/manage/components/UserTreatyAssignmentForm.tsx',
  '/src/app/admin/manage/components/UserTreatyManagement.tsx',
  '/src/app/admin/manage/identification/page.tsx',
  '/src/app/admin/manage/layout.tsx',
  '/src/app/admin/manage/ordinances/components/OrdinanceManagementForm.tsx',
  '/src/app/admin/manage/ordinances/page.tsx',
  '/src/app/admin/manage/users/bulk/page.tsx',
  '/src/app/admin/roles/AdminRolesClient.tsx',
  '/src/app/admin/treaties/components/TreatyManagementForm.tsx',
  '/src/app/admin/treaties/page.tsx',
  '/src/app/admin/users/page.tsx',
  '/src/app/api/countries/[id]/address-format/route.ts',
  '/src/app/api/example-standardized/route.ts',
  '/src/app/dashboard/page.tsx',
  '/src/app/nation-treaties/page.tsx',
  '/src/app/nation-treaties/tabs/CreateTreatyTypeTab.tsx',
  '/src/app/nation-treaties/tabs/NationMembersTab.tsx',
  '/src/app/nation-treaties/tabs/NationTreatyTab.tsx',
  '/src/app/nation-treaties/[userId]/[treatyTypeId]/page.tsx',
  '/src/app/ordinances/page.tsx',
  '/src/app/positions/DiplomaticPositionsTab.tsx',
  '/src/app/positions/TitlePositionManager.tsx',
  '/src/app/profile/page.tsx',
  '/src/app/support/page.tsx',
  '/src/app/treaty-management/page.tsx',
  '/src/app/users/page.tsx',
  '/src/app/verify-email/page.tsx',
  '/src/components/admin/VerticalNav.tsx',
  '/src/components/dashboard/TabNavigation.tsx',
  '/src/components/dashboard/tabs/ServersTab.tsx',
  '/src/components/documents/CreateDirectiveTab.tsx',
  '/src/components/documents/CreateOrdinanceTab.tsx',
  '/src/components/layout/DashboardLayout.tsx',
  '/src/components/layout/Header.tsx',
  '/src/components/layout/Sidebar.tsx',
  '/src/components/location/CitySearchAutocomplete.tsx',
  '/src/components/location/CountrySearchAutocomplete.tsx',
  '/src/components/LoginPage.tsx',
  '/src/components/notifications/NotificationDropdown.tsx',
  '/src/components/notifications/UserNotificationCenter.tsx',
  '/src/components/ordinances/AddOrdinanceDocumentForm.tsx',
  '/src/components/ordinances/OrdinanceList.tsx',
  '/src/components/ordinances/OrdinanceSearch.tsx',
  '/src/components/ordinances/OrdinanceUploadForm.tsx',
  '/src/components/ordinances/OrdinanceViewModal.tsx',
  '/src/components/payments/PaymentSubmissionModal.tsx',
  '/src/components/payments/PaymentVerificationDashboard.tsx',
  '/src/components/permissions/PermissionInheritanceViewer.tsx',
  '/src/components/profile/ContactDetailsTab.tsx',
  '/src/components/profile/PersonalInfoTab.tsx',
  '/src/components/profile/ProfilePhotoTab.tsx',
  '/src/components/profile/SecurityTab.tsx',
  '/src/components/security/SecurityDashboard.tsx',
  '/src/components/settings/AssignUserPermissions.tsx',
  '/src/components/settings/AssignUserRoles.tsx',
  '/src/components/settings/LocalPermissionManager.tsx',
  '/src/components/settings/PermissionManager.tsx',
  '/src/components/settings/PlatformSettingsTab.tsx',
  '/src/components/settings/RemoteServerAssignmentForm.tsx',
  '/src/components/settings/RemoteServerPermissionSelector.tsx',
  '/src/components/settings/remote-server/RemoteServerCard.tsx',
  '/src/components/settings/remote-server/RemoteServerManagement.tsx',
  '/src/components/settings/remote-server/types.ts',
  '/src/components/settings/SecurityAuditTab.tsx',
  '/src/components/settings/server-assignment/ExpandableServerCard.tsx',
  '/src/components/settings/server-assignment/LocalPermissionManagement.tsx',
  '/src/components/settings/server-assignment/RemotePermissionManagement.tsx',
  '/src/components/settings/server-assignment/RemoteRoleManagement.tsx',
  '/src/components/settings/server-assignment/ServerAssignmentManager.tsx',
  '/src/components/settings/server-assignment/ServerAssignmentPanel.tsx',
  '/src/components/settings/server-assignment/UserSelectionPanel.tsx',
  '/src/components/settings/ServerPermissionsManager.tsx',
  '/src/components/settings/SessionManagementTab.tsx',
  '/src/components/settings/SystemSettingsTab.tsx',
  '/src/components/settings/TreatyAuditTab.tsx',
  '/src/components/settings/UserPermissions.tsx',
  '/src/components/test/LocalPermissionTest.tsx',
  '/src/components/test/PermissionInheritanceTest.tsx',
  '/src/components/treaties/BusinessDetailsSection.tsx',
  '/src/components/treaties/CountryCityAutocomplete.tsx',
  '/src/components/treaties/CreateNationTreatyTab.tsx',
  '/src/components/treaties/DigitalSignature.tsx',
  '/src/components/treaties/FileUpload.tsx',
  '/src/components/treaties/modals/EditNationTreatyModal.tsx',
  '/src/components/treaties/modals/NationTreatyEnvoyModal.tsx',
  '/src/components/treaties/modals/NationTreatyMemberModal.tsx',
  '/src/components/treaties/modals/NationTreatyOfficeModal.tsx',
  '/src/components/treaties/NationTreatyManagement.tsx',
  '/src/components/treaties/NationTreatySearchAutocomplete.tsx',
  '/src/components/treaties/TreatiesManagementTab.tsx',
  '/src/components/treaties/TreatyAuditTab.tsx',
  '/src/components/treaties/TreatyCard.tsx',
  '/src/components/treaties/TreatyEditModal.tsx',
  '/src/components/treaties/TreatyList.tsx',
  '/src/components/treaties/TreatyManagement.tsx',
  '/src/components/treaties/TreatySearch.tsx',
  '/src/components/treaties/TreatyTypeSelector.tsx',
  '/src/components/treaties/TreatyTypesManagement.tsx',
  '/src/components/treaties/VerticalMenu.tsx',
  '/src/components/ui/api-key-display-simple.tsx',
  '/src/components/ui/api-key-display.tsx',
  '/src/components/ui/checkbox.tsx',
  '/src/components/ui/PermissionFormField.tsx',
  '/src/components/ui/radio-group.tsx',
  '/src/components/ui/select.tsx',
  '/src/components/ui/switch.tsx',
  '/src/components/UserProfile.tsx',
  '/src/components/users/manage/CreateUserTabOld.tsx',
  '/src/components/users/manage/CreateUserTab.tsx',
  '/src/components/users/manage/EnhancedTreatyCreation.tsx',
  '/src/components/users/manage/SearchUserTab.tsx',
  '/src/components/users/manage/shared/ContactDetailsForm.tsx',
  '/src/components/users/manage/shared/PositionSearchAutocomplete.tsx',
  '/src/components/users/manage/shared/UserBasicInfoForm.tsx',
  '/src/components/users/manage/shared/UserContactForm.tsx',
  '/src/components/users/manage/shared/UserIdentificationForm.tsx',
  '/src/components/users/manage/shared/UserNationTribeForm.tsx',
  '/src/components/users/manage/shared/UserOrdinancesForm.tsx',
  '/src/components/users/manage/shared/UserPositionsForm.tsx',
  '/src/components/users/manage/shared/UserSearchAutocomplete.tsx',
  '/src/components/users/manage/shared/UserTreatiesForm.tsx',
  '/src/components/users/manage/treaties/TreatyFilters.tsx',
  '/src/components/users/manage/TreatyTypeTab.tsx',
  '/src/components/users/manage/UpdateUserTab.tsx',
  '/src/components/users/manage/UserManagementTab.tsx',
  '/src/components/users/manage/UserTreatyAssignmentForm.tsx',
  '/src/hooks/usePermissionUI.ts',
  '/src/hooks/useWorkflow.ts',
  '/src/lib/admin-shared-utils.ts',
  '/src/lib/hooks/usePermissions.ts',
  '/src/lib/logger.ts',
  '/src/lib/notifications.ts',
  '/src/lib/performance.ts',
  '/src/lib/prisma.ts',
  '/src/lib/services/local-permission-service.ts',
  '/src/lib/services/remote-server-health-service.ts',
  '/src/lib/services/security-audit-analysis.ts',
  '/src/lib/services/security-event-detection.ts',
  '/src/lib/services/security-incident-response.ts',
  '/src/lib/services/server-assignment-audit-service.ts',
  '/src/lib/utils/address-format-utils.ts',
  '/src/lib/utils/csv-mapping.ts',
  '/src/middleware.ts',
  '/src/scripts/verify-security-implementation.ts',
  '/src/scripts/verify-security-monitoring.ts',
]);

// Development utility to check if current component has unused imports
export const hasUnusedImports = (filePath: string): boolean => {
  return FILES_WITH_UNUSED_IMPORTS.has(filePath);
};

// Hook for visual debugging (development only)
export const useUnusedImportHighlighter = (filePath: string) => {
  if (process.env.NODE_ENV === 'development') {
    return hasUnusedImports(filePath);
  }
  return false;
};

// CSS classes for highlighting during development
export const getHighlightClasses = (hasUnused: boolean): string => {
  if (!hasUnused) return '';

  return process.env.NODE_ENV === 'development'
    ? 'ring-2 ring-orange-500 ring-opacity-50 shadow-orange-500/20 shadow-lg'
    : '';
};