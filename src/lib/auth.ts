import { NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import { prisma } from "./prisma"
import * as bcrypt from "bcryptjs"

const authSecret = process.env.NEXTAUTH_SECRET

if (!authSecret) {
  throw new Error("NEXTAUTH_SECRET environment variable must be set")
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  secret: authSecret,
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        // For development, allow login with test credentials
        if (process.env.NODE_ENV === "development") {
          if (credentials.email === "<EMAIL>" && credentials.password === "password") {
            // Get the actual test user from database to ensure correct ID
            const testUser = await prisma.user.findUnique({
              where: { email: "<EMAIL>" },
              include: { profile: true }
            })
            
            if (testUser) {
              return {
                id: testUser.id,
                email: testUser.email,
                name: testUser.name || "Test User",
                nwaEmail: testUser.profile?.nwaEmail || "<EMAIL>"
              }
            }
            
            // If database query fails, don't provide fallback
            return null
          }
        }

        try {
          // Find user in database with password hash
          const user = await prisma.user.findUnique({
            where: {
              email: credentials.email
            },
            include: {
              profile: true
            }
          })

          // If user doesn't exist or doesn't have a password hash
          if (!user || !user.passwordHash) {
            return null
          }

          // For test user in development, allow without profile
          if (process.env.NODE_ENV === "development" && user.email === "<EMAIL>") {
            // Allow login without profile
          } else if (!user.profile?.nwaEmail) {
            return null
          }

          // Check if the provided password matches the stored hash
          const isValid = await bcrypt.compare(credentials.password, user.passwordHash)
          
          if (!isValid) {
            return null
          }

          // Return user object without the password hash
          return {
            id: user.id,
            email: user.email,
            name: user.name,
            nwaEmail: user.profile?.nwaEmail || "<EMAIL>"
          }
        } catch (error) {
          // If database is not available, fall back to development credentials
          if (process.env.NODE_ENV === "development") {
            if (credentials.email === "<EMAIL>" && credentials.password === "password") {
              // Get the actual test user from database to ensure correct ID
              const testUser = await prisma.user.findUnique({
                where: { email: "<EMAIL>" },
                include: { profile: true }
              })
              
              if (testUser) {
                return {
                  id: testUser.id,
                  email: testUser.email,
                  name: testUser.name || "Test User",
                  nwaEmail: testUser.profile?.nwaEmail || "<EMAIL>"
                }
              }
              
              // Don't provide fallback - let the user login fail if database is not available
              return null
            }
          }
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    session: async ({ session, token }) => {
        if (session?.user && token) {
          // Always verify the user ID from database to ensure consistency
          try {
            const dbUser = await prisma.user.findUnique({
              where: { email: session.user.email! },
              select: { id: true }
            });

            if (dbUser) {
              (session.user as any).id = dbUser.id;
            } else {
              (session.user as any).id = token.id; // fallback to token if DB lookup fails
            }
          } catch (error) {
            console.error('Error verifying user ID from database:', error);
            (session.user as any).id = token.id; // fallback to token if DB lookup fails
          }

          (session.user as any).nwaEmail = token.nwaEmail;
          (session.user as any).twoFactorEnabled = token.twoFactorEnabled;
          (session.user as any).twoFactorRequired = token.twoFactorRequired;
          // Get all user roles and permissions for session
          try {
            const userWithRoles = await prisma.user.findUnique({
              where: { id: (session.user as any).id },
              include: {
                userRoles: {
                  include: {
                    role: {
                      include: {
                        rolePermissions: {
                          include: {
                            permission: true
                          }
                        }
                      }
                    }
                  }
                }
              }
            });

            if (userWithRoles?.userRoles) {
              const userRoles = userWithRoles.userRoles.map(ur => ur.role.name);
              const userPermissions = userWithRoles.userRoles.flatMap(ur => ur.role.rolePermissions.map(rp => rp.permission.name));

              // Set primary role (prefer admin roles over regular roles)
              const primaryRole = userRoles.find(role => ['admin', 'super_admin', 'SUPER_ADMIN'].includes(role)) || userRoles[0];
              (session.user as any).role = primaryRole;
              (session.user as any).permissions = userPermissions;
            }
          } catch (error) {
            console.error('Error fetching user roles and permissions for session:', error);
            (session.user as any).role = token.role;
            (session.user as any).permissions = token.permissions;
          }
        }
        return session;
      },
    signIn: async ({ user, account, profile }) => {
        // Create a database session for admin tracking when user signs in
        if (user?.id) {
          try {
            // Get client IP and user agent from headers (this would need to be passed from the client)
            // For now, we'll create a basic session record
            await prisma.session.create({
              data: {
                sessionToken: `tracking_${user.id}_${Date.now()}`, // Unique tracking token
                userId: user.id,
                expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
                ipAddress: null, // Will be populated by middleware if available
                userAgent: null, // Will be populated by middleware if available
              }
            });
          } catch (error) {
            console.error('Error creating tracking session:', error);
            // Don't fail the sign-in if session creation fails
          }
        }
        return true;
      },
    jwt: async ({ token, user }) => {
      if (user) {
        token.id = user.id;
        token.nwaEmail = (user as any).nwaEmail;

        // Add 2FA status to token
        if ((user as any).id) {
          const userData = await prisma.user.findUnique({
            where: { id: (user as any).id },
            select: { twoFactorEnabled: true }
          });

          // Check if 2FA is required system-wide
          const systemSettings: any = await prisma.$queryRaw`SELECT two_factor_auth_enabled FROM system_settings LIMIT 1;`;
          const twoFactorAuthEnabled = Array.isArray(systemSettings) ? systemSettings[0]?.two_factor_auth_enabled : systemSettings?.two_factor_auth_enabled;

          token.twoFactorEnabled = userData?.twoFactorEnabled || false;
          token.twoFactorRequired = twoFactorAuthEnabled || false;

          // Add role and permissions to token
          try {
            const userWithRoles = await prisma.user.findUnique({
              where: { id: (user as any).id },
              include: {
                userRoles: {
                  include: {
                    role: {
                      include: {
                        rolePermissions: {
                          include: {
                            permission: true
                          }
                        }
                      }
                    }
                  }
                }
              }
            });

            // Get all user roles and permissions
            const userRoles = userWithRoles?.userRoles?.map(ur => ur.role.name) || [];
            const userPermissions = userWithRoles?.userRoles?.flatMap(ur => ur.role.rolePermissions.map(rp => rp.permission.name)) || [];

            // Set primary role (prefer admin roles over regular roles)
            const primaryRole = userRoles.find(role => ['admin', 'super_admin', 'SUPER_ADMIN'].includes(role)) || userRoles[0];
            token.role = primaryRole;
            token.permissions = userPermissions;
          } catch (error) {
            console.error('Error fetching user roles and permissions for token:', error);
            token.role = null;
            token.permissions = [];
          }
        }
      }

      // Update session tracking on every token refresh
      if (token.id) {
        try {
          // Update the lastActive timestamp for the tracking session
          await prisma.session.updateMany({
            where: {
              userId: token.id,
              expires: {
                gt: new Date()
              }
            },
            data: {
              lastActive: new Date()
            }
          });
        } catch (error) {
          console.error('Error updating session lastActive:', error);
        }
      }

      return token;
    }
  },
  pages: {
    signIn: '/login',
  },
}