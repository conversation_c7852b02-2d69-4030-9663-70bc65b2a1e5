import { useSession } from 'next-auth/react';

export interface UserPermissions {
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
  isAdmin: boolean;
  isModerator: boolean;
  isUser: boolean;
  userRole: string | undefined;
  permissions: string[];
}

export function usePermissions(): UserPermissions {
  const { data: session } = useSession();

  const userRole = (session?.user as any)?.role;
  const permissions = (session?.user as any)?.permissions || [];

  const hasPermission = (permission: string): boolean => {
    return permissions.includes(permission);
  };

  const hasAnyPermission = (permissionList: string[]): boolean => {
    return permissionList.some(hasPermission);
  };

  const hasAllPermissions = (permissionList: string[]): boolean => {
    return permissionList.every(hasPermission);
  };

  const hasRole = (role: string): boolean => {
    return userRole === role;
  };

  const hasAnyRole = (roleList: string[]): boolean => {
    return roleList.some(hasRole);
  };

  const isAdmin = userRole === 'admin' || userRole === 'super_admin';
  const isModerator = userRole === 'moderator' || isAdmin;
  const isUser = userRole === 'user' || isModerator;

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    isAdmin,
    isModerator,
    isUser,
    userRole,
    permissions,
  };
}