import { useEffect, useState } from 'react';

/**
 * Custom hook that debounces a value
 * @param value The value to debounce
 * @param delay The debounce delay in milliseconds
 * @returns The debounced value
 */
export function useDebounced<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Custom hook for debounced region searches
 * @param searchTerm The search term to debounce
 * @param delay The debounce delay in milliseconds (default: 300ms)
 * @returns The debounced search term
 */
export function useDebouncedRegionSearch(searchTerm: string, delay: number = 300): string {
  return useDebounced(searchTerm, delay);
}
