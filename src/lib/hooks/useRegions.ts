import { useEffect, useState, useCallback } from 'react';

export interface Region {
  id: number;
  name: string;
  code: string;
  countryId: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UseRegionsOptions {
  countryId?: number | null;
  active?: boolean | 'all';
  enabled?: boolean;
}

export interface UseRegionsResult {
  regions: Region[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Custom hook for lazy loading regions
 * Only fetches regions when countryId is provided and enabled is true
 */
export function useRegions(options: UseRegionsOptions = {}): UseRegionsResult {
  const { countryId, active = true, enabled = true } = options;
  
  const [regions, setRegions] = useState<Region[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRegions = useCallback(async () => {
    if (!countryId || !enabled) {
      setRegions([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const activeParam = active === 'all' ? 'all' : active ? 'true' : 'false';
      const response = await fetch(`/api/regions?countryId=${countryId}&active=${activeParam}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch regions: ${response.statusText}`);
      }

      const data = await response.json();
      setRegions(data.regions || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch regions');
      setRegions([]);
    } finally {
      setLoading(false);
    }
  }, [countryId, active, enabled]);

  useEffect(() => {
    fetchRegions();
  }, [fetchRegions]);

  return {
    regions,
    loading,
    error,
    refetch: fetchRegions
  };
}
