/**
 * Performance monitoring utilities for API endpoints and components
 */

export interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private timers: Map<string, number> = new Map();
  private metadata: Map<string, Record<string, any>> = new Map();

  /**
   * Start timing an operation
   */
  startTimer(name: string, metadata?: Record<string, any>): void {
    this.timers.set(name, Date.now());
    if (metadata) {
      this.metadata.set(name, metadata);
    }
  }

  /**
   * End timing an operation and record the metric
   */
  endTimer(name: string): PerformanceMetric | null {
    const startTime = this.timers.get(name);
    if (!startTime) {
      console.warn(`Performance timer '${name}' was not started`);
      return null;
    }

    const duration = Date.now() - startTime;
    const metadata = this.metadata.get(name);
    
    const metric: PerformanceMetric = {
      name,
      duration,
      timestamp: Date.now(),
      metadata
    };

    this.metrics.push(metric);
    this.timers.delete(name);
    this.metadata.delete(name);

    // Log slow operations (>1 second)
    if (duration > 1000) {
      console.warn(`Slow operation detected: ${name} took ${duration}ms`, metadata);
    }

    return metric;
  }

  /**
   * Record a metric without timing
   */
  recordMetric(name: string, duration: number, metadata?: Record<string, any>): PerformanceMetric {
    const metric: PerformanceMetric = {
      name,
      duration,
      timestamp: Date.now(),
      metadata
    };

    this.metrics.push(metric);
    return metric;
  }

  /**
   * Get all recorded metrics
   */
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  /**
   * Get metrics for a specific operation
   */
  getMetricsFor(name: string): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.name === name);
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Get performance summary
   */
  getSummary(): Record<string, { count: number; avgDuration: number; maxDuration: number; minDuration: number }> {
    const summary: Record<string, { count: number; avgDuration: number; maxDuration: number; minDuration: number }> = {};

    this.metrics.forEach(metric => {
      if (!summary[metric.name]) {
        summary[metric.name] = {
          count: 0,
          avgDuration: 0,
          maxDuration: 0,
          minDuration: Infinity
        };
      }

      const stats = summary[metric.name];
      stats.count++;
      stats.maxDuration = Math.max(stats.maxDuration, metric.duration);
      stats.minDuration = Math.min(stats.minDuration, metric.duration);
      stats.avgDuration = (stats.avgDuration * (stats.count - 1) + metric.duration) / stats.count;
    });

    return summary;
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * Decorator function to monitor API endpoint performance
 */
export function withPerformanceMonitoring<T extends (...args: any[]) => Promise<any>>(
  name: string,
  fn: T
): T {
  return (async (...args: any[]) => {
    performanceMonitor.startTimer(name, { args: args.length });
    try {
      const result = await fn(...args);
      performanceMonitor.endTimer(name);
      return result;
    } catch (error) {
      performanceMonitor.endTimer(name);
      throw error;
    }
  }) as T;
}

/**
 * Hook to monitor React component performance
 */
export function usePerformanceMonitor(componentName: string) {
  const startRender = () => {
    performanceMonitor.startTimer(`${componentName}_render`);
  };

  const endRender = () => {
    performanceMonitor.endTimer(`${componentName}_render`);
  };

  return { startRender, endRender };
}
