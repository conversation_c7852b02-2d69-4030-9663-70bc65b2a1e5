/**
 * Permission Constants for NWA Alliance RBAC System
 *
 * This file defines all permission constants, categories, and descriptions
 * used throughout the application for role-based access control.
 */

// Permission Categories
export const PERMISSION_CATEGORIES = {
  USER_MANAGEMENT: 'user_management',
  PROFILE: 'profile',
  ROLES: 'roles',
  TREATIES: 'treaties',
  DOCUMENTS: 'documents',
  SYSTEM: 'system',
  REMOTE_SERVERS: 'remote_servers',
  ADMIN: 'admin'
} as const

// Permission Actions
export const PERMISSION_ACTIONS = {
  CREATE: 'create',
  READ: 'read',
  WRITE: 'write',
  DELETE: 'delete',
  MANAGE: 'manage',
  ASSIGN: 'assign',
  SYNC: 'sync',
  AUDIT: 'audit'
} as const

// Permission Resources
export const PERMISSION_RESOURCES = {
  // User Management
  USER: 'user',
  USER_PROFILE: 'user_profile',
  USER_ROLES: 'user_roles',
  USER_PERMISSIONS: 'user_permissions',

  // Profile
  PROFILE: 'profile',
  PROFILE_FIELD: 'profile_field',

  // Roles & Permissions
  ROLE: 'role',
  PERMISSION: 'permission',
  ROLE_ASSIGNMENT: 'role_assignment',

  // Treaties
  TREATY: 'treaty',
  TREATY_DOCUMENT: 'treaty_document',
  TREATY_PARTICIPANT: 'treaty_participant',

  // Documents
  DOCUMENT: 'document',
  DOCUMENT_VERSION: 'document_version',
  DOCUMENT_ACCESS: 'document_access',

  // System
  SYSTEM_CONFIG: 'system_config',
  SYSTEM_LOGS: 'system_logs',
  SYSTEM_BACKUP: 'system_backup',

  // Remote Servers
  REMOTE_SERVER: 'remote_server',
  SERVER_ASSIGNMENT: 'server_assignment',
  SERVER_PERMISSION: 'server_permission',

  // Admin
  ADMIN_PANEL: 'admin_panel',
  ADMIN_USERS: 'admin_users',
  ADMIN_ROLES: 'admin_roles',
  ADMIN_TREATIES: 'admin_treaties',
  ADMIN_DOCUMENTS: 'admin_documents',
  ADMIN_SYSTEM: 'admin_system'
} as const

// Combined Permission Constants
export const PERMISSIONS = {
  // User Management Permissions
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_WRITE: 'user:write',
  USER_DELETE: 'user:delete',
  USER_MANAGE: 'user:manage',

  // Profile Permissions
  PROFILE_READ: 'profile:read',
  PROFILE_WRITE: 'profile:write',
  PROFILE_DELETE: 'profile:delete',
  PROFILE_FIELD_READ: 'profile_field:read',
  PROFILE_FIELD_WRITE: 'profile_field:write',

  // Role Permissions
  ROLE_CREATE: 'role:create',
  ROLE_READ: 'role:read',
  ROLE_WRITE: 'role:write',
  ROLE_DELETE: 'role:delete',
  ROLE_ASSIGN: 'role:assign',
  ROLE_MANAGE: 'role:manage',

  // Treaty Permissions
  TREATY_CREATE: 'treaty:create',
  TREATY_READ: 'treaty:read',
  TREATY_WRITE: 'treaty:write',
  TREATY_DELETE: 'treaty:delete',
  TREATY_MANAGE: 'treaty:manage',

  // Document Permissions
  DOCUMENT_CREATE: 'document:create',
  DOCUMENT_READ: 'document:read',
  DOCUMENT_WRITE: 'document:write',
  DOCUMENT_DELETE: 'document:delete',
  DOCUMENT_MANAGE: 'document:manage',

  // System Permissions
  SYSTEM_CONFIG_READ: 'system_config:read',
  SYSTEM_CONFIG_WRITE: 'system_config:write',
  SYSTEM_LOGS_READ: 'system_logs:read',
  SYSTEM_BACKUP_CREATE: 'system_backup:create',
  SYSTEM_BACKUP_READ: 'system_backup:read',

  // Remote Server Permissions
  REMOTE_SERVER_CREATE: 'remote_server:create',
  REMOTE_SERVER_READ: 'remote_server:read',
  REMOTE_SERVER_WRITE: 'remote_server:write',
  REMOTE_SERVER_DELETE: 'remote_server:delete',
  REMOTE_SERVER_ASSIGN: 'remote_server:assign',
  REMOTE_SERVER_MANAGE: 'remote_server:manage',
  SERVER_PERMISSION_SYNC: 'server_permission:sync',

  // Admin Permissions
  ADMIN_PANEL_ACCESS: 'admin_panel:access',
  ADMIN_USERS_MANAGE: 'admin_users:manage',
  ADMIN_ROLES_MANAGE: 'admin_roles:manage',
  ADMIN_TREATIES_MANAGE: 'admin_treaties:manage',
  ADMIN_DOCUMENTS_MANAGE: 'admin_documents:manage',
  ADMIN_SYSTEM_MANAGE: 'admin_system:manage'
} as const

// Permission Descriptions
export const PERMISSION_DESCRIPTIONS = {
  [PERMISSIONS.USER_CREATE]: 'Create new user accounts',
  [PERMISSIONS.USER_READ]: 'View user information',
  [PERMISSIONS.USER_WRITE]: 'Edit user information',
  [PERMISSIONS.USER_DELETE]: 'Delete user accounts',
  [PERMISSIONS.USER_MANAGE]: 'Full user management access',

  [PERMISSIONS.PROFILE_READ]: 'View own profile information',
  [PERMISSIONS.PROFILE_WRITE]: 'Edit own profile information',
  [PERMISSIONS.PROFILE_DELETE]: 'Delete own profile',
  [PERMISSIONS.PROFILE_FIELD_READ]: 'View specific profile fields',
  [PERMISSIONS.PROFILE_FIELD_WRITE]: 'Edit specific profile fields',

  [PERMISSIONS.ROLE_CREATE]: 'Create new roles',
  [PERMISSIONS.ROLE_READ]: 'View role information',
  [PERMISSIONS.ROLE_WRITE]: 'Edit role permissions',
  [PERMISSIONS.ROLE_DELETE]: 'Delete roles',
  [PERMISSIONS.ROLE_ASSIGN]: 'Assign roles to users',
  [PERMISSIONS.ROLE_MANAGE]: 'Full role management access',

  [PERMISSIONS.TREATY_CREATE]: 'Create new treaties',
  [PERMISSIONS.TREATY_READ]: 'View treaty information',
  [PERMISSIONS.TREATY_WRITE]: 'Edit treaty content',
  [PERMISSIONS.TREATY_DELETE]: 'Delete treaties',
  [PERMISSIONS.TREATY_MANAGE]: 'Full treaty management access',

  [PERMISSIONS.DOCUMENT_CREATE]: 'Create new documents',
  [PERMISSIONS.DOCUMENT_READ]: 'View documents',
  [PERMISSIONS.DOCUMENT_WRITE]: 'Edit document content',
  [PERMISSIONS.DOCUMENT_DELETE]: 'Delete documents',
  [PERMISSIONS.DOCUMENT_MANAGE]: 'Full document management access',

  [PERMISSIONS.SYSTEM_CONFIG_READ]: 'View system configuration',
  [PERMISSIONS.SYSTEM_CONFIG_WRITE]: 'Modify system configuration',
  [PERMISSIONS.SYSTEM_LOGS_READ]: 'View system logs',
  [PERMISSIONS.SYSTEM_BACKUP_CREATE]: 'Create system backups',
  [PERMISSIONS.SYSTEM_BACKUP_READ]: 'View backup information',

  [PERMISSIONS.REMOTE_SERVER_CREATE]: 'Add new remote servers',
  [PERMISSIONS.REMOTE_SERVER_READ]: 'View remote server information',
  [PERMISSIONS.REMOTE_SERVER_WRITE]: 'Modify remote server settings',
  [PERMISSIONS.REMOTE_SERVER_DELETE]: 'Remove remote servers',
  [PERMISSIONS.REMOTE_SERVER_ASSIGN]: 'Assign users to remote servers',
  [PERMISSIONS.REMOTE_SERVER_MANAGE]: 'Full remote server management',
  [PERMISSIONS.SERVER_PERMISSION_SYNC]: 'Sync permissions with remote servers',

  [PERMISSIONS.ADMIN_PANEL_ACCESS]: 'Access admin panel',
  [PERMISSIONS.ADMIN_USERS_MANAGE]: 'Manage all users',
  [PERMISSIONS.ADMIN_ROLES_MANAGE]: 'Manage all roles and permissions',
  [PERMISSIONS.ADMIN_TREATIES_MANAGE]: 'Manage all treaties',
  [PERMISSIONS.ADMIN_DOCUMENTS_MANAGE]: 'Manage all documents',
  [PERMISSIONS.ADMIN_SYSTEM_MANAGE]: 'Full system administration access'
} as const

// Role-based Permission Groups
export const ROLE_PERMISSIONS = {
  PEACE: [
    PERMISSIONS.PROFILE_READ,
    PERMISSIONS.PROFILE_WRITE,
    PERMISSIONS.PROFILE_FIELD_READ,
    PERMISSIONS.PROFILE_FIELD_WRITE,
    PERMISSIONS.TREATY_READ,
    PERMISSIONS.DOCUMENT_READ
  ],

  MEMBER: [
    PERMISSIONS.PROFILE_READ,
    PERMISSIONS.PROFILE_WRITE,
    PERMISSIONS.PROFILE_FIELD_READ,
    PERMISSIONS.PROFILE_FIELD_WRITE,
    PERMISSIONS.TREATY_READ,
    PERMISSIONS.TREATY_WRITE,
    PERMISSIONS.DOCUMENT_READ,
    PERMISSIONS.DOCUMENT_WRITE,
    PERMISSIONS.USER_READ
  ],

  ADMIN: [
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_WRITE,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_MANAGE,
    PERMISSIONS.PROFILE_READ,
    PERMISSIONS.PROFILE_WRITE,
    PERMISSIONS.PROFILE_DELETE,
    PERMISSIONS.PROFILE_FIELD_READ,
    PERMISSIONS.PROFILE_FIELD_WRITE,
    PERMISSIONS.ROLE_CREATE,
    PERMISSIONS.ROLE_READ,
    PERMISSIONS.ROLE_WRITE,
    PERMISSIONS.ROLE_DELETE,
    PERMISSIONS.ROLE_ASSIGN,
    PERMISSIONS.ROLE_MANAGE,
    PERMISSIONS.TREATY_CREATE,
    PERMISSIONS.TREATY_READ,
    PERMISSIONS.TREATY_WRITE,
    PERMISSIONS.TREATY_DELETE,
    PERMISSIONS.TREATY_MANAGE,
    PERMISSIONS.DOCUMENT_CREATE,
    PERMISSIONS.DOCUMENT_READ,
    PERMISSIONS.DOCUMENT_WRITE,
    PERMISSIONS.DOCUMENT_DELETE,
    PERMISSIONS.DOCUMENT_MANAGE,
    PERMISSIONS.SYSTEM_CONFIG_READ,
    PERMISSIONS.SYSTEM_LOGS_READ,
    PERMISSIONS.REMOTE_SERVER_CREATE,
    PERMISSIONS.REMOTE_SERVER_READ,
    PERMISSIONS.REMOTE_SERVER_WRITE,
    PERMISSIONS.REMOTE_SERVER_DELETE,
    PERMISSIONS.REMOTE_SERVER_ASSIGN,
    PERMISSIONS.REMOTE_SERVER_MANAGE,
    PERMISSIONS.SERVER_PERMISSION_SYNC,
    PERMISSIONS.ADMIN_PANEL_ACCESS,
    PERMISSIONS.ADMIN_USERS_MANAGE,
    PERMISSIONS.ADMIN_ROLES_MANAGE,
    PERMISSIONS.ADMIN_TREATIES_MANAGE,
    PERMISSIONS.ADMIN_DOCUMENTS_MANAGE,
    PERMISSIONS.ADMIN_SYSTEM_MANAGE
  ]
} as const

// Field-level Permissions for Profile
export const PROFILE_FIELD_PERMISSIONS = {
  firstName: {
    peace: ['read', 'write'],
    member: ['read', 'write'],
    admin: ['read', 'write', 'delete']
  },
  lastName: {
    peace: ['read', 'write'],
    member: ['read', 'write'],
    admin: ['read', 'write', 'delete']
  },
  email: {
    peace: ['read'],
    member: ['read', 'write'],
    admin: ['read', 'write', 'delete']
  },
  role: {
    peace: ['read'],
    member: ['read'],
    admin: ['read', 'write']
  },
  createdAt: {
    peace: ['read'],
    member: ['read'],
    admin: ['read', 'write']
  },
  updatedAt: {
    peace: ['read'],
    member: ['read'],
    admin: ['read', 'write']
  }
} as const

// Export types
export type PermissionCategory = typeof PERMISSION_CATEGORIES[keyof typeof PERMISSION_CATEGORIES]
export type PermissionAction = typeof PERMISSION_ACTIONS[keyof typeof PERMISSION_ACTIONS]
export type PermissionResource = typeof PERMISSION_RESOURCES[keyof typeof PERMISSION_RESOURCES]
export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS]
export type RoleType = keyof typeof ROLE_PERMISSIONS