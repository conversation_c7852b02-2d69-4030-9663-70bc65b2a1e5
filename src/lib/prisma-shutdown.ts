/**
 * Node.js specific graceful shutdown setup for Prisma
 * This file is only loaded in Node.js environments, not Edge Runtime
 *
 * Note: This file uses Node.js APIs that are not compatible with Edge Runtime.
 * It should only be imported conditionally in Node.js environments.
 */

import { PrismaClient } from '@prisma/client';

// Type assertion to tell TypeScript this is Node.js only
declare const process: {
  on: (event: string, handler: (...args: any[]) => void) => void;
  exit: (code: number) => void;
};

export function setupGracefulShutdown(prisma: PrismaClient) {
  // This code only runs in Node.js environment
  if (typeof process === 'object' && process !== null) {
    const processOn = process.on;
    const processExit = process.exit;

    if (typeof processOn === 'function' && typeof processExit === 'function') {
      // Add graceful shutdown handler
      processOn('beforeExit', async () => {
        try {
          await prisma.$disconnect();
        } catch (error) {
          // Silently ignore disconnect errors
        }
      });

      // Handle SIGINT and SIGTERM
      processOn('SIGINT', async () => {
        try {
          await prisma.$disconnect();
          processExit(0);
        } catch (error) {
          processExit(1);
        }
      });

      processOn('SIGTERM', async () => {
        try {
          await prisma.$disconnect();
          processExit(0);
        } catch (error) {
          processExit(1);
        }
      });
    }
  }
}