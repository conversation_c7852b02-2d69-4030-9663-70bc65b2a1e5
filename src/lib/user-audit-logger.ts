import { prisma } from './prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth';

// Define a type for the session user that includes the id field
interface SessionUser {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  nwaEmail?: string | null;
}

export interface UserChangeAuditLog {
  userId: string;
  changedByUserId?: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE' | 'LOGIN' | 'LOGOUT';
  resource: string;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  metadata?: Record<string, any>; // Add metadata property
}

/**
 * Logs user data changes with detailed information about what changed
 */
export class UserAuditLogger {
  /**
   * Logs a user data change with detailed information
   */
  static async logUserChange(data: UserChangeAuditLog): Promise<void> {
    try {
      // Create audit log entry
      await prisma.auditLog.create({
        data: {
          userId: data.userId,
          action: data.action,
          resource: data.resource,
          resourceId: data.resourceId,
          oldValues: data.oldValues ? data.oldValues : undefined,
          newValues: data.newValues ? data.newValues : undefined,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          timestamp: data.timestamp || new Date(),
          success: true,
          metadata: {
            changedByUserId: data.changedByUserId,
          }
        }
      });
    } catch (error) {
      console.error('Failed to log user change:', error);
      // Don't throw to avoid breaking the main flow
    }
  }

  /**
   * Logs user creation
   */
  static async logUserCreation(
    userId: string,
    newValues: Record<string, any>,
    changedByUserId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logUserChange({
      userId,
      changedByUserId,
      action: 'CREATE',
      resource: 'user',
      resourceId: userId,
      newValues,
      timestamp: new Date(),
      metadata: {
        ...metadata,
        actionType: 'user_creation'
      }
    });
  }

  /**
   * Logs user update with detailed field changes
   */
  static async logUserUpdate(
    userId: string,
    oldValues: Record<string, any>,
    newValues: Record<string, any>,
    changedByUserId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    // Only log fields that actually changed
    const changedFields: Record<string, { from: any; to: any }> = {};
    
    Object.keys(newValues).forEach(key => {
      if (oldValues[key] !== newValues[key]) {
        changedFields[key] = {
          from: oldValues[key],
          to: newValues[key]
        };
      }
    });

    // Only log if there are actual changes
    if (Object.keys(changedFields).length > 0) {
      await this.logUserChange({
        userId,
        changedByUserId,
        action: 'UPDATE',
        resource: 'user',
        resourceId: userId,
        oldValues: changedFields,
        newValues: changedFields,
        timestamp: new Date(),
        metadata: {
          ...metadata,
          actionType: 'user_update',
          changedFields: Object.keys(changedFields)
        }
      });
    }
  }

  /**
   * Logs user deletion
   */
  static async logUserDeletion(
    userId: string,
    oldValues: Record<string, any>,
    changedByUserId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logUserChange({
      userId,
      changedByUserId,
      action: 'DELETE',
      resource: 'user',
      resourceId: userId,
      oldValues,
      timestamp: new Date(),
      metadata: {
        ...metadata,
        actionType: 'user_deletion'
      }
    });
  }

  /**
   * Gets the current user from session
   */
  static async getCurrentUserId(): Promise<string | undefined> {
    try {
      const session = await getServerSession(authOptions);
      return (session?.user as SessionUser | undefined)?.id;
    } catch (error) {
      console.error('Failed to get current user ID:', error);
      return undefined;
    }
  }

  /**
   * Gets client IP address from request headers
   */
  static getClientIP(headers: Headers): string {
    const forwarded = headers.get('x-forwarded-for');
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    const realIP = headers.get('x-real-ip');
    if (realIP) {
      return realIP.trim();
    }
    
    const cfConnectingIP = headers.get('cf-connecting-ip');
    if (cfConnectingIP) {
      return cfConnectingIP.trim();
    }
    
    return 'unknown';
  }

  /**
   * Gets user agent from request headers
   */
  static getUserAgent(headers: Headers): string {
    return headers.get('user-agent') || 'unknown';
  }

  /**
   * Generic user audit logging method
   */
  static async logUserAudit(data: {
    userId: string;
    action: string;
    targetUserId?: string;
    details: string;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<void> {
    try {
      // Create audit log entry
      await prisma.auditLog.create({
        data: {
          userId: data.userId,
          action: data.action,
          resource: 'user',
          resourceId: data.targetUserId || data.userId,
          oldValues: data.details,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          timestamp: new Date(),
          success: true,
        }
      });
    } catch (error) {
      console.error('Failed to log user audit:', error);
      // Don't throw to avoid breaking the main flow
    }
  }
}

// Export default instance for convenience
export const userAuditLogger = UserAuditLogger;

// Export individual functions for direct use
export const logUserCreation = UserAuditLogger.logUserCreation;
export const logUserUpdate = UserAuditLogger.logUserUpdate;
export const logUserDeletion = UserAuditLogger.logUserDeletion;
export const getCurrentUserId = UserAuditLogger.getCurrentUserId;
export const getClientIP = UserAuditLogger.getClientIP;
export const getUserAgent = UserAuditLogger.getUserAgent;

export const logUserAudit = UserAuditLogger.logUserAudit;