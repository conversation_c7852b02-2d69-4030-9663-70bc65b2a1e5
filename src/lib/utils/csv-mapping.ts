/**
 * CSV Header Mapping and Validation Utilities
 * Provides flexible mapping between CSV headers and database schema
 */

import { prisma } from '@/lib/prisma';

/**
 * Standard database field mappings for user bulk upload
 */
export const STANDARD_FIELD_MAPPINGS = {
  // Basic User Information
  name: ['name', 'full_name', 'fullname', 'display_name'],
  email: ['email', 'email_address', 'primary_email', 'user_email'],
  firstName: ['first_name', 'firstname', 'fname', 'given_name'],
  lastName: ['last_name', 'lastname', 'lname', 'surname', 'family_name'],
  personalEmail: ['personal_email', 'personal_email_address', 'private_email'],
  nwaEmail: ['nwa_email', 'nwa_email_address', 'work_email', 'organization_email'],
  phone: ['phone', 'phone_number', 'telephone', 'primary_phone'],
  mobile: ['mobile', 'mobile_phone', 'cell', 'cellular', 'cell_phone'],
  bio: ['bio', 'biography', 'description', 'about', 'profile_description'],
  dateOfBirth: ['date_of_birth', 'birth_date', 'dob', 'birthday'],
  
  // Address Information
  streetAddress1: ['street_address_1', 'address_1', 'street1', 'address_line_1', 'street_address'],
  streetAddress2: ['street_address_2', 'address_2', 'street2', 'address_line_2'],
  town: ['town', 'city', 'locality'],
  cityName: ['city_name', 'city', 'municipality'],
  regionName: ['region_name', 'state', 'province', 'region', 'state_name'],
  countryCode: ['country_code', 'country', 'country_iso', 'nation'],
  postalCode: ['postal_code', 'zip_code', 'zip', 'postcode'],
  
  // Identification
  peaceAmbassadorNumber: ['peace_ambassador_number', 'ambassador_number', 'pa_number'],
  identificationTypes: ['identification_types', 'id_types', 'document_types'],
  identificationNumbers: ['identification_numbers', 'id_numbers', 'document_numbers'],
  
  // Role and Position
  roles: ['roles', 'user_roles', 'role_names', 'permissions'],
  titleName: ['title_name', 'title', 'position_title', 'diplomatic_title'],
  positionNames: ['position_names', 'positions', 'job_positions'],
  
  // Treaty Information
  treatyTypeNames: ['treaty_type_names', 'treaty_types', 'agreement_types'],
  treatyNumbers: ['treaty_numbers', 'agreement_numbers', 'treaty_ids'],
  
  // Account Settings
  twoFactorEnabled: ['two_factor_enabled', 'mfa_enabled', '2fa_enabled', 'two_factor'],
  isActive: ['is_active', 'active', 'status', 'enabled'],
  
  // Additional
  notes: ['notes', 'comments', 'remarks', 'additional_info']
} as const;

/**
 * Required fields that must be present for user creation
 */
export const REQUIRED_FIELDS = ['email'] as const;

/**
 * Optional fields that enhance user profiles but aren't required
 */
export const OPTIONAL_FIELDS = Object.keys(STANDARD_FIELD_MAPPINGS).filter(
  field => !REQUIRED_FIELDS.includes(field as any)
) as string[];

/**
 * Result of CSV header analysis
 */
export interface HeaderAnalysisResult {
  exactMatches: string[];
  suggestedMappings: Array<{
    csvHeader: string;
    suggestedField: string;
    confidence: number;
    alternatives: string[];
  }>;
  unmappedHeaders: string[];
  missingRequired: string[];
  validationPassed: boolean;
  mode: 'strict' | 'smart' | 'custom';
}

/**
 * User's custom field mappings
 */
export interface CustomFieldMapping {
  csvHeader: string;
  databaseField: string;
  confirmed: boolean;
}

/**
 * Analyzes CSV headers and provides mapping suggestions
 */
export function analyzeCSVHeaders(
  csvHeaders: string[],
  mode: 'strict' | 'smart' | 'custom' = 'smart'
): HeaderAnalysisResult {
  const normalizedHeaders = csvHeaders.map(h => h.toLowerCase().trim());
  const exactMatches: string[] = [];
  const suggestedMappings: HeaderAnalysisResult['suggestedMappings'] = [];
  const unmappedHeaders: string[] = [];

  for (let i = 0; i < csvHeaders.length; i++) {
    const originalHeader = csvHeaders[i];
    const normalizedHeader = normalizedHeaders[i];
    
    // Check for exact matches first
    const exactMatch = Object.keys(STANDARD_FIELD_MAPPINGS).find(field => {
      const fieldKey = field as keyof typeof STANDARD_FIELD_MAPPINGS;
      const variations = STANDARD_FIELD_MAPPINGS[fieldKey] as readonly string[];
      return variations.includes(normalizedHeader);
    });

    if (exactMatch) {
      exactMatches.push(originalHeader);
      continue;
    }

    // For smart mode, provide suggestions based on similarity
    if (mode === 'smart' || mode === 'custom') {
      const suggestions = findSimilarFields(normalizedHeader);
      if (suggestions.length > 0) {
        suggestedMappings.push({
          csvHeader: originalHeader,
          suggestedField: suggestions[0].field,
          confidence: suggestions[0].confidence,
          alternatives: suggestions.slice(1, 4).map(s => s.field)
        });
      } else {
        unmappedHeaders.push(originalHeader);
      }
    } else {
      // Strict mode - no suggestions for non-exact matches
      unmappedHeaders.push(originalHeader);
    }
  }

  // Check for missing required fields
  const mappedFields = new Set([
    ...exactMatches.map(header => findFieldForHeader(header.toLowerCase())),
    ...suggestedMappings.map(m => m.suggestedField)
  ].filter(Boolean));

  const missingRequired = REQUIRED_FIELDS.filter(field => !mappedFields.has(field));
  const validationPassed = missingRequired.length === 0;

  return {
    exactMatches,
    suggestedMappings,
    unmappedHeaders,
    missingRequired,
    validationPassed,
    mode
  };
}

/**
 * Finds similar fields using fuzzy matching
 */
function findSimilarFields(header: string): Array<{field: string, confidence: number}> {
  const results: Array<{field: string, confidence: number}> = [];

  for (const [field, variations] of Object.entries(STANDARD_FIELD_MAPPINGS)) {
    for (const variation of variations) {
      const confidence = calculateSimilarity(header, variation);
      if (confidence > 0.5) { // Only suggest if confidence is above 50%
        results.push({ field, confidence });
        break; // Don't add the same field multiple times
      }
    }
  }

  return results.sort((a, b) => b.confidence - a.confidence);
}

/**
 * Calculates string similarity using Levenshtein distance
 */
function calculateSimilarity(str1: string, str2: string): number {
  const len1 = str1.length;
  const len2 = str2.length;
  
  if (len1 === 0) return len2 === 0 ? 1 : 0;
  if (len2 === 0) return 0;

  // Create matrix
  const matrix = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(null));

  // Initialize first row and column
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;

  // Fill the matrix
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,     // deletion
        matrix[i][j - 1] + 1,     // insertion
        matrix[i - 1][j - 1] + cost // substitution
      );
    }
  }

  const distance = matrix[len1][len2];
  const maxLength = Math.max(len1, len2);
  return (maxLength - distance) / maxLength;
}

/**
 * Finds the database field that corresponds to a CSV header
 */
function findFieldForHeader(header: string): string | null {
  for (const [field, variations] of Object.entries(STANDARD_FIELD_MAPPINGS)) {
    const variationList = variations as readonly string[];
    if (variationList.includes(header)) {
      return field;
    }
  }
  return null;
}

/**
 * Validates that custom mappings are valid
 */
export function validateCustomMappings(
  mappings: CustomFieldMapping[]
): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  const mappedFields = new Set<string>();
  const usedHeaders = new Set<string>();

  for (const mapping of mappings) {
    // Check for duplicate database fields
    if (mappedFields.has(mapping.databaseField)) {
      errors.push(`Database field '${mapping.databaseField}' is mapped multiple times`);
    } else {
      mappedFields.add(mapping.databaseField);
    }

    // Check for duplicate CSV headers
    if (usedHeaders.has(mapping.csvHeader)) {
      errors.push(`CSV header '${mapping.csvHeader}' is used multiple times`);
    } else {
      usedHeaders.add(mapping.csvHeader);
    }

    // Check if database field exists in our schema
    if (!Object.keys(STANDARD_FIELD_MAPPINGS).includes(mapping.databaseField)) {
      warnings.push(`Database field '${mapping.databaseField}' is not a standard field`);
    }

    // Check if mapping is not confirmed
    if (!mapping.confirmed) {
      warnings.push(`Mapping for '${mapping.csvHeader}' is not confirmed`);
    }
  }

  // Check for missing required fields
  const missingRequired = REQUIRED_FIELDS.filter(field => !mappedFields.has(field));
  if (missingRequired.length > 0) {
    errors.push(`Required fields are missing: ${missingRequired.join(', ')}`);
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Creates a mapping object from CSV headers to database fields
 */
export function createFieldMapping(
  csvHeaders: string[],
  customMappings?: CustomFieldMapping[]
): Record<string, string> {
  const mapping: Record<string, string> = {};

  if (customMappings) {
    // Use custom mappings
    for (const customMapping of customMappings) {
      if (customMapping.confirmed) {
        mapping[customMapping.csvHeader] = customMapping.databaseField;
      }
    }
  } else {
    // Use automatic mapping
    for (const header of csvHeaders) {
      const field = findFieldForHeader(header.toLowerCase());
      if (field) {
        mapping[header] = field;
      }
    }
  }

  return mapping;
}

/**
 * Validates and transforms a CSV row based on field mappings
 */
export async function validateAndTransformRow(
  row: Record<string, string>,
  fieldMapping: Record<string, string>,
  rowIndex: number
): Promise<{
  valid: boolean;
  transformedRow: Record<string, any>;
  errors: string[];
  warnings: string[];
}> {
  const errors: string[] = [];
  const warnings: string[] = [];
  const transformedRow: Record<string, any> = {};

  for (const [csvHeader, value] of Object.entries(row)) {
    const field = fieldMapping[csvHeader];
    if (!field) continue;

    try {
      const transformedValue = await transformFieldValue(field, value, rowIndex);
      if (transformedValue !== null) {
        transformedRow[field] = transformedValue;
      }
    } catch (error) {
      errors.push(`Row ${rowIndex + 1}: Invalid value for ${field}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Validate required fields
  for (const requiredField of REQUIRED_FIELDS) {
    if (!transformedRow[requiredField]) {
      errors.push(`Row ${rowIndex + 1}: Required field '${requiredField}' is missing or empty`);
    }
  }

  return {
    valid: errors.length === 0,
    transformedRow,
    errors,
    warnings
  };
}

/**
 * Transforms and validates field values based on their type
 */
async function transformFieldValue(
  field: string,
  value: string,
  rowIndex: number
): Promise<any> {
  const trimmedValue = value.trim();
  
  if (!trimmedValue) {
    return null;
  }

  switch (field) {
    case 'email':
    case 'personalEmail':
    case 'nwaEmail':
      if (!isValidEmail(trimmedValue)) {
        throw new Error('Invalid email format');
      }
      return trimmedValue.toLowerCase();

    case 'dateOfBirth':
      const date = new Date(trimmedValue);
      if (isNaN(date.getTime())) {
        throw new Error('Invalid date format. Use YYYY-MM-DD');
      }
      return date;

    case 'twoFactorEnabled':
    case 'isActive':
      if (['true', '1', 'yes', 'on'].includes(trimmedValue.toLowerCase())) {
        return true;
      } else if (['false', '0', 'no', 'off'].includes(trimmedValue.toLowerCase())) {
        return false;
      } else {
        throw new Error('Invalid boolean value. Use true/false, 1/0, yes/no, or on/off');
      }

    case 'roles':
    case 'identificationTypes':
    case 'identificationNumbers':
    case 'positionNames':
    case 'treatyTypeNames':
    case 'treatyNumbers':
      return trimmedValue.split(',').map(item => item.trim()).filter(item => item);

    case 'countryCode':
      // Validate country code exists
      const country = await prisma.country.findUnique({
        where: { code: trimmedValue.toUpperCase() }
      });
      if (!country) {
        throw new Error(`Country code '${trimmedValue}' not found`);
      }
      return trimmedValue.toUpperCase();

    default:
      return trimmedValue;
  }
}

/**
 * Simple email validation
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Estimates the processing time for a CSV upload based on row count
 */
export function estimateProcessingTime(rowCount: number): {
  estimated: number; // in seconds
  description: string;
} {
  // Base time per row (includes validation, database lookups, creation)
  const baseTimePerRow = 0.5; // 500ms per row
  const overhead = 5; // 5 seconds overhead
  
  const estimated = Math.ceil(rowCount * baseTimePerRow + overhead);
  
  let description = 'Quick processing';
  if (estimated > 60) {
    description = 'This may take a few minutes';
  } else if (estimated > 180) {
    description = 'This may take several minutes';
  } else if (estimated > 600) {
    description = 'This may take 10+ minutes';
  }

  return { estimated, description };
}
