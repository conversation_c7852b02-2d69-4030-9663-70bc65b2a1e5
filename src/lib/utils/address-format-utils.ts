/**
 * Address formatting utilities for handling country-specific address formats
 * Supports template parsing, placeholder replacement, postal code validation, and address formatting
 */

export interface AddressData {
  streetAddress1: string | null;
  streetAddress2?: string | null;
  town?: string | null;
  region?: string | null;
  postalCode?: string | null;
  country?: string | null;
}

export interface FormattedAddress {
  street: string | null;
  town: string | null;
  region: string | null;
  postalCode: string | null;
  country: string | null;
}

export interface CountryAddressFormat {
  postalCodeLabel?: string;
  postalCodeFormat?: string;
  postalCodeRequired?: boolean;
  regionLabel?: string;
  regionRequired?: boolean;
  townLabel?: string;
  townRequired?: boolean;
  addressTemplate?: string;
}

export interface FieldLabels {
  postalCodeLabel: string;
  regionLabel: string;
  townLabel: string;
}

/**
 * Parses an address template and extracts all placeholders
 * @param template - Address template with placeholders like {street}, {town}, etc.
 * @returns Array of unique placeholder names
 */
export function parseAddressTemplate(template: string): string[] {
  if (!template) {
    return [];
  }

  const placeholderRegex = /{([^}]+)}/g;
  const placeholders: string[] = [];
  let match;

  while ((match = placeholderRegex.exec(template)) !== null) {
    const placeholder = match[1].trim();
    if (!placeholders.includes(placeholder)) {
      placeholders.push(placeholder);
    }
  }

  return placeholders;
}

/**
 * Replaces placeholders in a template with actual address data
 * @param template - Address template with placeholders
 * @param addressData - Object containing address data for placeholder replacement
 * @returns Formatted string with placeholders replaced
 */
export function replacePlaceholders(
  template: string,
  addressData: Record<string, string | null | undefined>
): string {
  if (!template) {
    return '';
  }

  let result = template;

  // Replace placeholders with actual data or empty string
  Object.keys(addressData).forEach(key => {
    const placeholder = `{${key}}`;
    const value = addressData[key] || '';
    result = result.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value);
  });

  // Clean up the result by removing empty lines and extra whitespace
  const lines = result.split('\n').map(line => {
    // Trim whitespace
    let cleanedLine = line.trim();
    
    // Remove trailing commas and spaces that are left hanging
    cleanedLine = cleanedLine.replace(/[,\s]+$/, '');
    
    return cleanedLine;
  });
  
  // Remove lines that are empty or contain only punctuation/whitespace
  const cleanedLines = lines.filter(line => {
    const cleanLine = line.replace(/[,.\s]/g, '');
    return cleanLine.length > 0;
  });

  return cleanedLines.join('\n');
}

/**
 * Validates a postal code against a country-specific pattern
 * @param postalCode - The postal code to validate
 * @param pattern - Regular expression pattern for validation
 * @returns True if valid, false otherwise
 */
export function validatePostalCode(postalCode: string, pattern: string): boolean {
  if (!postalCode || !pattern) {
    return false;
  }

  try {
    const regex = new RegExp(pattern);
    return regex.test(postalCode);
  } catch (error) {
    // Invalid regex pattern
    return false;
  }
}

/**
 * Sanitizes an address component by trimming whitespace and converting empty strings to null
 * @param component - Address component to sanitize
 * @returns Sanitized string or null
 */
export function sanitizeAddressComponent(component: string | null | undefined): string | null {
  if (component === null || component === undefined) {
    return null;
  }

  const trimmed = component.trim();
  return trimmed === '' ? null : trimmed;
}

/**
 * Builds a formatted address object from raw address data
 * @param addressData - Raw address data
 * @returns FormattedAddress object with sanitized components
 */
export function buildFormattedAddress(addressData: AddressData): FormattedAddress {
  const street1 = sanitizeAddressComponent(addressData.streetAddress1);
  const street2 = sanitizeAddressComponent(addressData.streetAddress2);
  
  // Combine street address lines
  let street = street1;
  if (street1 && street2) {
    street = `${street1}\n${street2}`;
  }

  return {
    street,
    town: sanitizeAddressComponent(addressData.town),
    region: sanitizeAddressComponent(addressData.region),
    postalCode: sanitizeAddressComponent(addressData.postalCode),
    country: sanitizeAddressComponent(addressData.country)
  };
}

/**
 * Formats an address for display using a country-specific template
 * @param addressData - Raw address data
 * @param template - Address template for formatting
 * @returns Formatted address string
 */
export function formatAddressForDisplay(addressData: AddressData, template: string): string {
  if (!template) {
    return '';
  }

  const formattedAddress = buildFormattedAddress(addressData);
  const displayAddress = replacePlaceholders(template, formattedAddress as unknown as Record<string, string | null | undefined>);
  
  return displayAddress;
}

/**
 * Gets country-specific field labels
 * @param addressFormat - Country address format configuration
 * @returns Object containing field labels
 */
export function getCountryFieldLabels(addressFormat: CountryAddressFormat | null): FieldLabels {
  if (!addressFormat) {
    return {
      postalCodeLabel: 'Postal Code',
      regionLabel: 'Region',
      townLabel: 'City'
    };
  }

  return {
    postalCodeLabel: addressFormat.postalCodeLabel || 'Postal Code',
    regionLabel: addressFormat.regionLabel || 'Region',
    townLabel: addressFormat.townLabel || 'City'
  };
}
