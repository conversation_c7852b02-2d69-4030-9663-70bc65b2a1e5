import { z } from 'zod';

// Nation Treaty Status Enum
export const NationTreatyStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  SUSPENDED: 'SUSPENDED',
  PENDING: 'PENDING'
} as const;

// Nation Treaty Member Status Enum
export const NationTreatyMemberStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  SUSPENDED: 'SUSPENDED'
} as const;

// Nation Treaty Member Role Enum
export const NationTreatyMemberRole = {
  MEMBER: 'MEMBER',
  ENVOY: 'ENVOY',
  ADMIN: 'ADMIN'
} as const;

// Nation Treaty Envoy Status Enum
export const NationTreatyEnvoyStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  SUSPENDED: 'SUSPENDED'
} as const;

// Nation Treaty Envoy Type Enum
export const NationTreatyEnvoyType = {
  PRIMARY: 'PRIMARY',
  EMERGENCY: 'EMERGENCY',
  LEGAL: 'LEGAL'
} as const;

// Nation Treaty Envoy Role Enum
export const NationTreatyEnvoyRole = {
  SPECIAL_ENVOY: 'SPECIAL_ENVOY',
  AMBASSADOR: 'AMBASSADOR',
  CONSUL: 'CONSUL',
  DIPLOMAT: 'DIPLOMAT'
} as const;

// Nation Treaty Office Type Enum
export const NationTreatyOfficeType = {
  ENVOY_OFFICE: 'ENVOY_OFFICE',
  CONSULATE_GENERAL: 'CONSULATE_GENERAL',
  EMBASSY: 'EMBASSY',
  TRADE_OFFICE: 'TRADE_OFFICE',
  CULTURAL_CENTER: 'CULTURAL_CENTER'
} as const;

// Nation Treaty Office Status Enum
export const NationTreatyOfficeStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  UNDER_CONSTRUCTION: 'UNDER_CONSTRUCTION',
  PLANNED: 'PLANNED'
} as const;

// Phone number validation by country
const phoneValidation = {
  // New Zealand phone numbers - more flexible with formatting
  NZ: /^(?:\+64|64|0)\s*(?:2[0-9]|3[0-9]|4[0-9]|5[0-9]|6[0-9]|7[0-9]|8[0-9]|9[0-9])\s*\d{3}\s*\d{4}$/,
  // US phone numbers
  US: /^(?:\+1|1)?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/,
  // UK phone numbers
  UK: /^(?:\+44|44|0)(?:1[0-9]|2[0-9]|3[0-9]|7[0-9]|8[0-9])(?:\d{8}|\d{7})$/,
  // Australia phone numbers
  AU: /^(?:\+61|61|0)(?:2|3|4|7|8)(?:\d{8})$/,
  // Generic international format (less strict fallback)
  INTERNATIONAL: /^[\+]?[1-9][\d\s\-\(\)\.]{6,15}$/
};

function validatePhoneNumber(phone: string, countryCode?: string): boolean {
  if (!phone) return true; // Optional field

  // Remove all non-digit characters except +
  const digitsOnly = phone.replace(/[^\d+]/g, '');

  // Extract country code from number if present
  let detectedCountry = countryCode;
  if (!detectedCountry && digitsOnly.startsWith('+')) {
    if (digitsOnly.startsWith('+64')) detectedCountry = 'NZ';
    else if (digitsOnly.startsWith('+1')) detectedCountry = 'US';
    else if (digitsOnly.startsWith('+44')) detectedCountry = 'UK';
    else if (digitsOnly.startsWith('+61')) detectedCountry = 'AU';
  }

  // Use country-specific validation or fallback to international
  const regex = detectedCountry && phoneValidation[detectedCountry as keyof typeof phoneValidation]
    ? phoneValidation[detectedCountry as keyof typeof phoneValidation]
    : phoneValidation.INTERNATIONAL;

  return regex.test(phone);
}
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Custom phone number validator for Zod
const phoneValidator = z.string().refine((phone) => {
  if (!phone) return true; // Optional field
  return validatePhoneNumber(phone);
}, {
  message: 'Invalid phone number format'
});

// Base validation schemas
const contactInfoSchema = z.object({
  email: z.string().email('Invalid email format').optional().or(z.literal('')),
  phone: phoneValidator.optional().or(z.literal('')),
  address: z.string().optional().or(z.literal('')),
  website: z.string().url('Invalid website URL').optional().or(z.literal(''))
});

const emergencyContactSchema = z.object({
  name: z.string().min(1, 'Emergency contact name is required'),
  phone: phoneValidator,
  email: z.string().email('Invalid emergency contact email').optional().or(z.literal(''))
});

// Nation Treaty Creation Schema
export const nationTreatyCreateSchema = z.object({
  name: z.string().min(2, 'Nation treaty name must be at least 2 characters')
    .max(100, 'Nation treaty name cannot exceed 100 characters'),
  officialName: z.string().min(2, 'Official name must be at least 2 characters')
    .max(200, 'Official name cannot exceed 200 characters'),
  status: z.enum([NationTreatyStatus.ACTIVE, NationTreatyStatus.INACTIVE, NationTreatyStatus.SUSPENDED, NationTreatyStatus.PENDING]).optional().default('ACTIVE'),
  description: z.string().max(1000, 'Description cannot exceed 1000 characters').optional(),
  contactEmail: z.string().email('Invalid contact email').optional().or(z.literal('')),
  contactPhone: phoneValidator.optional().or(z.literal('')),
  contactAddress: z.string().max(500, 'Contact address cannot exceed 500 characters').optional(),
  website: z.string().url('Invalid website URL').optional().or(z.literal('')),
  emergencyContactName: z.string().min(1, 'Emergency contact name is required').optional(),
  emergencyContactPhone: phoneValidator.optional(),
  emergencyContactEmail: z.string().email('Invalid emergency contact email').optional().or(z.literal('')),
  notes: z.string().max(2000, 'Notes cannot exceed 2000 characters').optional(),
  metadata: z.record(z.any()).optional()
}).refine(data => {
  // If emergency contact name is provided, phone is required
  if (data.emergencyContactName && !data.emergencyContactPhone) {
    return false;
  }
  return true;
}, {
  message: 'Emergency contact phone is required when emergency contact name is provided',
  path: ['emergencyContactPhone']
});

// Nation Treaty Update Schema
export const nationTreatyUpdateSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(2, 'Nation treaty name must be at least 2 characters').max(100, 'Nation treaty name cannot exceed 100 characters').optional(),
  officialName: z.string().min(2, 'Official name must be at least 2 characters').max(200, 'Official name cannot exceed 200 characters').optional(),
  status: z.enum([NationTreatyStatus.ACTIVE, NationTreatyStatus.INACTIVE, NationTreatyStatus.SUSPENDED, NationTreatyStatus.PENDING]).optional(),
  description: z.string().max(1000, 'Description cannot exceed 1000 characters').optional(),
  contactEmail: z.string().email('Invalid contact email').optional().or(z.literal('')),
  contactPhone: phoneValidator.optional().or(z.literal('')),
  contactAddress: z.string().max(500, 'Contact address cannot exceed 500 characters').optional(),
  website: z.string().url('Invalid website URL').optional().or(z.literal('')),
  emergencyContactName: z.string().min(1, 'Emergency contact name is required').optional(),
  emergencyContactPhone: phoneValidator.optional(),
  emergencyContactEmail: z.string().email('Invalid emergency contact email').optional().or(z.literal('')),
  notes: z.string().max(2000, 'Notes cannot exceed 2000 characters').optional(),
  metadata: z.record(z.any()).optional(),
  documentPath: z.string().optional(),
  isDeleted: z.boolean().optional()
}).refine(data => {
  // If emergency contact name is provided, phone is required
  if (data.emergencyContactName && !data.emergencyContactPhone) {
    return false;
  }
  return true;
}, {
  message: 'Emergency contact phone is required when emergency contact name is provided',
  path: ['emergencyContactPhone']
});

// Nation Treaty Member Creation Schema
export const nationTreatyMemberCreateSchema = z.object({
  userId: z.string().cuid('Invalid user ID'),
  nationTreatyId: z.string().cuid('Invalid nation treaty ID'),
  role: z.enum([NationTreatyMemberRole.MEMBER, NationTreatyMemberRole.ENVOY, NationTreatyMemberRole.ADMIN]).optional().default('MEMBER'),
  status: z.enum([NationTreatyMemberStatus.ACTIVE, NationTreatyMemberStatus.INACTIVE, NationTreatyMemberStatus.SUSPENDED]).optional().default('ACTIVE'),
  notes: z.string().max(500, 'Notes cannot exceed 500 characters').optional()
});

// Nation Treaty Member Update Schema
export const nationTreatyMemberUpdateSchema = nationTreatyMemberCreateSchema.partial().extend({
  id: z.string().cuid()
});

// Nation Treaty Envoy Creation Schema
export const nationTreatyEnvoyCreateSchema = z.object({
  userId: z.string().cuid('Invalid user ID'),
  nationTreatyId: z.string().cuid('Invalid nation treaty ID'),
  envoyType: z.enum([NationTreatyEnvoyType.PRIMARY, NationTreatyEnvoyType.EMERGENCY, NationTreatyEnvoyType.LEGAL]).default('PRIMARY'),
  title: z.string().max(50, 'Title cannot exceed 50 characters').optional(),
  status: z.enum([NationTreatyEnvoyStatus.ACTIVE, NationTreatyEnvoyStatus.INACTIVE, NationTreatyEnvoyStatus.SUSPENDED]).optional().default('ACTIVE'),
  notes: z.string().max(500, 'Notes cannot exceed 500 characters').optional()
});

// Nation Treaty Envoy Update Schema
export const nationTreatyEnvoyUpdateSchema = nationTreatyEnvoyCreateSchema.partial().extend({
  id: z.string().cuid()
});

// Query parameter schemas for filtering and pagination
export const nationTreatyQuerySchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0, 'Page must be greater than 0').default('1'),
  limit: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0 && n <= 100, 'Limit must be between 1 and 100').default('10'),
  search: z.string().max(100, 'Search term cannot exceed 100 characters').optional(),
  status: z.enum([NationTreatyStatus.ACTIVE, NationTreatyStatus.INACTIVE, NationTreatyStatus.SUSPENDED, NationTreatyStatus.PENDING]).optional(),
  sortBy: z.enum(['name', 'officialName', 'status', 'createdAt', 'updatedAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  contactEmail: z.string().max(100, 'Contact email filter cannot exceed 100 characters').optional(),
  country: z.string().max(50, 'Country filter cannot exceed 50 characters').optional()
});

export const nationTreatyMemberQuerySchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0, 'Page must be greater than 0').default('1'),
  limit: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0 && n <= 100, 'Limit must be between 1 and 100').default('10'),
  search: z.string().max(100, 'Search term cannot exceed 100 characters').optional(),
  status: z.enum([NationTreatyMemberStatus.ACTIVE, NationTreatyMemberStatus.INACTIVE, NationTreatyMemberStatus.SUSPENDED]).optional(),
  sortBy: z.enum(['joinedAt', 'status', 'createdAt']).default('joinedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

export const nationTreatyEnvoyQuerySchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0, 'Page must be greater than 0').default('1'),
  limit: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0 && n <= 100, 'Limit must be between 1 and 100').default('10'),
  search: z.string().max(100, 'Search term cannot exceed 100 characters').optional(),
  status: z.enum([NationTreatyEnvoyStatus.ACTIVE, NationTreatyEnvoyStatus.INACTIVE, NationTreatyEnvoyStatus.SUSPENDED]).optional(),
  title: z.string().max(50, 'Title filter cannot exceed 50 characters').optional(),
  sortBy: z.enum(['appointedAt', 'title', 'status', 'createdAt']).default('appointedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

// Nation Treaty Office Creation Schema
export const nationTreatyOfficeCreateSchema = z.object({
  nationTreatyId: z.string().cuid('Invalid nation treaty ID'),
  officeType: z.enum([NationTreatyOfficeType.ENVOY_OFFICE, NationTreatyOfficeType.CONSULATE_GENERAL, NationTreatyOfficeType.EMBASSY, NationTreatyOfficeType.TRADE_OFFICE, NationTreatyOfficeType.CULTURAL_CENTER]).default('ENVOY_OFFICE'),
  status: z.enum([NationTreatyOfficeStatus.ACTIVE, NationTreatyOfficeStatus.INACTIVE, NationTreatyOfficeStatus.UNDER_CONSTRUCTION, NationTreatyOfficeStatus.PLANNED]).optional().default('PLANNED'),
  streetAddress: z.string().max(500, 'Street address cannot exceed 500 characters').optional(),
  city: z.string().max(100, 'City cannot exceed 100 characters').optional(),
  region: z.string().max(100, 'Region cannot exceed 100 characters').optional(),
  country: z.string().max(100, 'Country cannot exceed 100 characters').optional(),
  postalCode: z.string().max(20, 'Postal code cannot exceed 20 characters').optional(),
  phone: phoneValidator.optional().or(z.literal('')),
  email: z.string().email('Invalid email format').optional().or(z.literal('')),
  website: z.string().url('Invalid website URL').optional().or(z.literal('')),
  isPrimary: z.boolean().default(false),
  notes: z.string().max(2000, 'Notes cannot exceed 2000 characters').optional(),
  metadata: z.record(z.any()).optional()
});

// Nation Treaty Office Update Schema
export const nationTreatyOfficeUpdateSchema = nationTreatyOfficeCreateSchema.partial().extend({
  id: z.string().cuid()
});

// Nation Treaty Office Query Schema
export const nationTreatyOfficeQuerySchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0, 'Page must be greater than 0').default('1'),
  limit: z.string().regex(/^\d+$/).transform(Number).refine(n => n > 0 && n <= 100, 'Limit must be between 1 and 100').default('10'),
  search: z.string().max(100, 'Search term cannot exceed 100 characters').optional(),
  status: z.enum([NationTreatyOfficeStatus.ACTIVE, NationTreatyOfficeStatus.INACTIVE, NationTreatyOfficeStatus.UNDER_CONSTRUCTION, NationTreatyOfficeStatus.PLANNED]).optional(),
  officeType: z.enum([NationTreatyOfficeType.ENVOY_OFFICE, NationTreatyOfficeType.CONSULATE_GENERAL, NationTreatyOfficeType.EMBASSY, NationTreatyOfficeType.TRADE_OFFICE, NationTreatyOfficeType.CULTURAL_CENTER]).optional(),
  country: z.string().max(100, 'Country filter cannot exceed 100 characters').optional(),
  city: z.string().max(100, 'City filter cannot exceed 100 characters').optional(),
  sortBy: z.enum(['city', 'officeType', 'status', 'country', 'createdAt', 'updatedAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

// Type exports
export type NationTreatyCreate = z.infer<typeof nationTreatyCreateSchema>;
export type NationTreatyUpdate = z.infer<typeof nationTreatyUpdateSchema>;
export type NationTreatyMemberCreate = z.infer<typeof nationTreatyMemberCreateSchema>;
export type NationTreatyMemberUpdate = z.infer<typeof nationTreatyMemberUpdateSchema>;
export type NationTreatyEnvoyCreate = z.infer<typeof nationTreatyEnvoyCreateSchema>;
export type NationTreatyEnvoyUpdate = z.infer<typeof nationTreatyEnvoyUpdateSchema>;
export type NationTreatyOfficeCreate = z.infer<typeof nationTreatyOfficeCreateSchema>;
export type NationTreatyOfficeUpdate = z.infer<typeof nationTreatyOfficeUpdateSchema>;
export type NationTreatyQuery = z.infer<typeof nationTreatyQuerySchema>;
export type NationTreatyMemberQuery = z.infer<typeof nationTreatyMemberQuerySchema>;
export type NationTreatyEnvoyQuery = z.infer<typeof nationTreatyEnvoyQuerySchema>;
export type NationTreatyOfficeQuery = z.infer<typeof nationTreatyOfficeQuerySchema>;

// Helper functions for validation
export function validateNationTreatyCreate(data: any): NationTreatyCreate {
  return nationTreatyCreateSchema.parse(data);
}

export function validateNationTreatyUpdate(data: any): NationTreatyUpdate {
  return nationTreatyUpdateSchema.parse(data);
}

export function validateNationTreatyMemberCreate(data: any): NationTreatyMemberCreate {
  return nationTreatyMemberCreateSchema.parse(data);
}

export function validateNationTreatyMemberUpdate(data: any): NationTreatyMemberUpdate {
  return nationTreatyMemberUpdateSchema.parse(data);
}

export function validateNationTreatyEnvoyCreate(data: any): NationTreatyEnvoyCreate {
  return nationTreatyEnvoyCreateSchema.parse(data);
}

export function validateNationTreatyEnvoyUpdate(data: any): NationTreatyEnvoyUpdate {
  return nationTreatyEnvoyUpdateSchema.parse(data);
}

export function validateNationTreatyOfficeCreate(data: any): NationTreatyOfficeCreate {
  return nationTreatyOfficeCreateSchema.parse(data);
}

export function validateNationTreatyOfficeUpdate(data: any): NationTreatyOfficeUpdate {
  return nationTreatyOfficeUpdateSchema.parse(data);
}

export function validateNationTreatyQuery(data: any): NationTreatyQuery {
  return nationTreatyQuerySchema.parse(data);
}

export function validateNationTreatyMemberQuery(data: any): NationTreatyMemberQuery {
  return nationTreatyMemberQuerySchema.parse(data);
}

export function validateNationTreatyEnvoyQuery(data: any): NationTreatyEnvoyQuery {
  return nationTreatyEnvoyQuerySchema.parse(data);
}

export function validateNationTreatyOfficeQuery(data: any): NationTreatyOfficeQuery {
  return nationTreatyOfficeQuerySchema.parse(data);
}

// Error types
export class ValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NationTreatyError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'NationTreatyError';
  }
}

// Utility functions
export function sanitizeString(input: string): string {
  if (!input) return input;
  return input.trim().replace(/[<>]/g, '');
}

export function validateStatusTransition(
  currentStatus: string, 
  newStatus: string
): void {
  // Basic validation - allow all transitions for now
  // Add specific business rules as needed
  if (currentStatus === newStatus) {
    return; // No change needed
  }
}

export function validateContactInfo(info: Partial<typeof contactInfoSchema._type>): void {
  try {
    contactInfoSchema.parse(info);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError(
        'Invalid contact information',
        error.errors[0]?.path[0]?.toString()
      );
    }
    throw error;
  }
}