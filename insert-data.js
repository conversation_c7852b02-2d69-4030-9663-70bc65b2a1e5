import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function insertData() {
  try {
    console.log('Inserting countries data...');

    // Insert countries data
    const countries = [
      { id: 31, name: 'New Zealand', code: 'NZ' },
      { id: 32, name: 'Australia', code: 'AU' },
      { id: 33, name: 'United States', code: 'US' },
      { id: 34, name: 'Canada', code: 'CA' },
      { id: 35, name: 'United Kingdom', code: 'GB' },
      { id: 36, name: 'Germany', code: 'DE' },
      { id: 37, name: 'France', code: 'FR' },
      { id: 38, name: 'Japan', code: 'JP' },
      { id: 39, name: 'China', code: 'CN' },
      { id: 40, name: 'India', code: 'IN' },
      { id: 41, name: 'Brazil', code: 'BR' },
      { id: 42, name: 'Mexico', code: 'MX' },
      { id: 43, name: 'South Korea', code: 'KR' },
      { id: 44, name: 'Italy', code: 'IT' },
      { id: 45, name: 'Spain', code: 'ES' },
      { id: 46, name: 'Netherlands', code: 'NL' },
      { id: 47, name: 'Switzerland', code: 'CH' },
      { id: 48, name: 'Sweden', code: 'SE' },
      { id: 49, name: 'Norway', code: 'NO' },
      { id: 50, name: 'Denmark', code: 'DK' },
      { id: 51, name: 'Singapore', code: 'SG' },
      { id: 52, name: 'Hong Kong', code: 'HK' },
      { id: 53, name: 'South Africa', code: 'ZA' },
      { id: 54, name: 'Israel', code: 'IL' },
      { id: 55, name: 'United Arab Emirates', code: 'AE' },
      { id: 56, name: 'Afghanistan', code: 'AF' },
      { id: 57, name: 'Albania', code: 'AL' },
      { id: 58, name: 'Algeria', code: 'DZ' },
      { id: 59, name: 'Andorra', code: 'AD' },
      { id: 60, name: 'Angola', code: 'AO' },
      { id: 61, name: 'Antigua and Barbuda', code: 'AG' },
      { id: 62, name: 'Argentina', code: 'AR' },
      { id: 63, name: 'Armenia', code: 'AM' },
      { id: 64, name: 'Austria', code: 'AT' },
      { id: 65, name: 'Azerbaijan', code: 'AZ' },
      { id: 66, name: 'Bahamas', code: 'BS' },
      { id: 67, name: 'Bahrain', code: 'BH' },
      { id: 68, name: 'Bangladesh', code: 'BD' },
      { id: 69, name: 'Barbados', code: 'BB' },
      { id: 70, name: 'Belarus', code: 'BY' },
      { id: 71, name: 'Belgium', code: 'BE' },
      { id: 72, name: 'Belize', code: 'BZ' },
      { id: 73, name: 'Benin', code: 'BJ' },
      { id: 74, name: 'Bhutan', code: 'BT' },
      { id: 75, name: 'Bolivia', code: 'BO' },
      { id: 76, name: 'Bosnia and Herzegovina', code: 'BA' },
      { id: 77, name: 'Botswana', code: 'BW' },
      { id: 78, name: 'Brunei', code: 'BN' },
      { id: 79, name: 'Bulgaria', code: 'BG' },
      { id: 80, name: 'Burkina Faso', code: 'BF' },
      { id: 81, name: 'Burundi', code: 'BI' },
      { id: 82, name: 'Cambodia', code: 'KH' },
      { id: 83, name: 'Cameroon', code: 'CM' },
      { id: 84, name: 'Cape Verde', code: 'CV' },
      { id: 85, name: 'Central African Republic', code: 'CF' },
      { id: 86, name: 'Chad', code: 'TD' },
      { id: 87, name: 'Chile', code: 'CL' },
      { id: 88, name: 'Colombia', code: 'CO' },
      { id: 89, name: 'Comoros', code: 'KM' },
      { id: 90, name: 'Costa Rica', code: 'CR' },
      { id: 91, name: 'Croatia', code: 'HR' },
      { id: 92, name: 'Cuba', code: 'CU' },
      { id: 93, name: 'Cyprus', code: 'CY' },
      { id: 94, name: 'Czech Republic', code: 'CZ' },
      { id: 95, name: 'Democratic Republic of the Congo', code: 'CD' },
      { id: 97, name: 'Djibouti', code: 'DJ' },
      { id: 98, name: 'Dominica', code: 'DM' },
      { id: 99, name: 'Dominican Republic', code: 'DO' },
      { id: 100, name: 'East Timor', code: 'TL' },
      { id: 101, name: 'Ecuador', code: 'EC' },
      { id: 102, name: 'Egypt', code: 'EG' },
      { id: 103, name: 'El Salvador', code: 'SV' },
      { id: 104, name: 'Equatorial Guinea', code: 'GQ' },
      { id: 105, name: 'Eritrea', code: 'ER' },
      { id: 106, name: 'Estonia', code: 'EE' },
      { id: 107, name: 'Ethiopia', code: 'ET' },
      { id: 108, name: 'Fiji', code: 'FJ' },
      { id: 109, name: 'Finland', code: 'FI' },
      { id: 110, name: 'Gabon', code: 'GA' },
      { id: 111, name: 'Gambia', code: 'GM' },
      { id: 112, name: 'Georgia', code: 'GE' },
      { id: 113, name: 'Ghana', code: 'GH' },
      { id: 114, name: 'Greece', code: 'GR' },
      { id: 115, name: 'Grenada', code: 'GD' },
      { id: 116, name: 'Guatemala', code: 'GT' },
      { id: 117, name: 'Guinea', code: 'GN' },
      { id: 118, name: 'Guinea-Bissau', code: 'GW' },
      { id: 119, name: 'Guyana', code: 'GY' },
      { id: 120, name: 'Haiti', code: 'HT' },
      { id: 121, name: 'Honduras', code: 'HN' },
      { id: 122, name: 'Hungary', code: 'HU' },
      { id: 123, name: 'Iceland', code: 'IS' },
      { id: 124, name: 'Indonesia', code: 'ID' },
      { id: 125, name: 'Iran', code: 'IR' },
      { id: 126, name: 'Iraq', code: 'IQ' },
      { id: 127, name: 'Ireland', code: 'IE' },
      { id: 128, name: 'Jamaica', code: 'JM' },
      { id: 129, name: 'Jordan', code: 'JO' },
      { id: 130, name: 'Kazakhstan', code: 'KZ' },
      { id: 131, name: 'Kenya', code: 'KE' },
      { id: 132, name: 'Kiribati', code: 'KI' },
      { id: 133, name: 'Kosovo', code: 'XK' },
      { id: 134, name: 'Kuwait', code: 'KW' },
      { id: 135, name: 'Kyrgyzstan', code: 'KG' },
      { id: 136, name: 'Laos', code: 'LA' },
      { id: 137, name: 'Latvia', code: 'LV' },
      { id: 138, name: 'Lebanon', code: 'LB' },
      { id: 139, name: 'Lesotho', code: 'LS' },
      { id: 140, name: 'Liberia', code: 'LR' },
      { id: 141, name: 'Libya', code: 'LY' },
      { id: 142, name: 'Liechtenstein', code: 'LI' },
      { id: 143, name: 'Lithuania', code: 'LT' },
      { id: 144, name: 'Luxembourg', code: 'LU' },
      { id: 145, name: 'Madagascar', code: 'MG' },
      { id: 146, name: 'Malawi', code: 'MW' },
      { id: 147, name: 'Malaysia', code: 'MY' },
      { id: 148, name: 'Maldives', code: 'MV' },
      { id: 149, name: 'Mali', code: 'ML' },
      { id: 150, name: 'Malta', code: 'MT' },
      { id: 151, name: 'Marshall Islands', code: 'MH' },
      { id: 152, name: 'Mauritania', code: 'MR' },
      { id: 153, name: 'Mauritius', code: 'MU' },
      { id: 154, name: 'Micronesia', code: 'FM' },
      { id: 155, name: 'Moldova', code: 'MD' },
      { id: 156, name: 'Monaco', code: 'MC' },
      { id: 157, name: 'Mongolia', code: 'MN' },
      { id: 158, name: 'Montenegro', code: 'ME' },
      { id: 159, name: 'Morocco', code: 'MA' },
      { id: 160, name: 'Mozambique', code: 'MZ' },
      { id: 161, name: 'Myanmar', code: 'MM' },
      { id: 162, name: 'Namibia', code: 'NA' },
      { id: 163, name: 'Nauru', code: 'NR' },
      { id: 164, name: 'Nepal', code: 'NP' },
      { id: 165, name: 'Nicaragua', code: 'NI' },
      { id: 166, name: 'Niger', code: 'NE' },
      { id: 167, name: 'Nigeria', code: 'NG' },
      { id: 168, name: 'North Macedonia', code: 'MK' },
      { id: 169, name: 'Oman', code: 'OM' },
      { id: 170, name: 'Pakistan', code: 'PK' },
      { id: 171, name: 'Palau', code: 'PW' },
      { id: 172, name: 'Palestine', code: 'PS' },
      { id: 173, name: 'Panama', code: 'PA' },
      { id: 174, name: 'Papua New Guinea', code: 'PG' },
      { id: 175, name: 'Paraguay', code: 'PY' },
      { id: 176, name: 'Peru', code: 'PE' },
      { id: 177, name: 'Philippines', code: 'PH' },
      { id: 178, name: 'Poland', code: 'PL' },
      { id: 179, name: 'Portugal', code: 'PT' },
      { id: 180, name: 'Qatar', code: 'QA' },
      { id: 181, name: 'Republic of the Congo', code: 'CG' },
      { id: 182, name: 'Romania', code: 'RO' },
      { id: 183, name: 'Russia', code: 'RU' },
      { id: 184, name: 'Rwanda', code: 'RW' },
      { id: 185, name: 'Saint Kitts and Nevis', code: 'KN' },
      { id: 186, name: 'Saint Lucia', code: 'LC' },
      { id: 187, name: 'Saint Vincent and the Grenadines', code: 'VC' },
      { id: 188, name: 'Samoa', code: 'WS' },
      { id: 189, name: 'San Marino', code: 'SM' },
      { id: 190, name: 'Sao Tome and Principe', code: 'ST' },
      { id: 191, name: 'Saudi Arabia', code: 'SA' },
      { id: 192, name: 'Senegal', code: 'SN' },
      { id: 193, name: 'Serbia', code: 'RS' },
      { id: 194, name: 'Seychelles', code: 'SC' },
      { id: 195, name: 'Sierra Leone', code: 'SL' },
      { id: 196, name: 'Slovakia', code: 'SK' },
      { id: 197, name: 'Slovenia', code: 'SI' },
      { id: 198, name: 'Solomon Islands', code: 'SB' },
      { id: 199, name: 'Somalia', code: 'SO' },
      { id: 200, name: 'Sri Lanka', code: 'LK' },
      { id: 201, name: 'Sudan', code: 'SD' },
      { id: 202, name: 'Suriname', code: 'SR' },
      { id: 203, name: 'Swaziland', code: 'SZ' },
      { id: 204, name: 'Syria', code: 'SY' },
      { id: 205, name: 'Tajikistan', code: 'TJ' },
      { id: 206, name: 'Tanzania', code: 'TZ' },
      { id: 207, name: 'Thailand', code: 'TH' },
      { id: 208, name: 'Togo', code: 'TG' },
      { id: 209, name: 'Tonga', code: 'TO' },
      { id: 210, name: 'Trinidad and Tobago', code: 'TT' },
      { id: 211, name: 'Tunisia', code: 'TN' },
      { id: 212, name: 'Turkey', code: 'TR' },
      { id: 213, name: 'Turkmenistan', code: 'TM' },
      { id: 214, name: 'Tuvalu', code: 'TV' },
      { id: 215, name: 'Uganda', code: 'UG' },
      { id: 216, name: 'Ukraine', code: 'UA' },
      { id: 217, name: 'Uruguay', code: 'UY' },
      { id: 218, name: 'Uzbekistan', code: 'UZ' },
      { id: 219, name: 'Vanuatu', code: 'VU' },
      { id: 220, name: 'Vatican City', code: 'VA' },
      { id: 221, name: 'Venezuela', code: 'VE' },
      { id: 222, name: 'Vietnam', code: 'VN' },
      { id: 223, name: 'Yemen', code: 'YE' },
      { id: 224, name: 'Zambia', code: 'ZM' },
      { id: 225, name: 'Zimbabwe', code: 'ZW' }
    ];

    for (const country of countries) {
      await prisma.country.upsert({
        where: { id: country.id },
        update: country,
        create: country
      });
    }

    console.log(`Inserted ${countries.length} countries`);

    // Insert some sample cities for key countries
    const cities = [
      { id: 250, name: 'Auckland', countryId: 31 },
      { id: 251, name: 'Wellington', countryId: 31 },
      { id: 252, name: 'Christchurch', countryId: 31 },
      { id: 253, name: 'Hamilton', countryId: 31 },
      { id: 254, name: 'Tauranga', countryId: 31 },
      { id: 255, name: 'Dunedin', countryId: 31 },
      { id: 357, name: 'Mexico City', countryId: 42 },
      { id: 358, name: 'Guadalajara', countryId: 42 },
      { id: 359, name: 'Monterrey', countryId: 42 },
      { id: 360, name: 'Puebla', countryId: 42 },
      { id: 361, name: 'Tijuana', countryId: 42 },
      { id: 362, name: 'León', countryId: 42 },
      { id: 363, name: 'Juárez', countryId: 42 },
      { id: 364, name: 'Torreón', countryId: 42 },
      { id: 365, name: 'Querétaro', countryId: 42 },
      { id: 366, name: 'San Luis Potosí', countryId: 42 },
      { id: 367, name: 'Seoul', countryId: 43 },
      { id: 368, name: 'Busan', countryId: 43 },
      { id: 369, name: 'Incheon', countryId: 43 },
      { id: 370, name: 'Daegu', countryId: 43 },
      { id: 371, name: 'Daejeon', countryId: 43 },
      { id: 372, name: 'Gwangju', countryId: 43 },
      { id: 373, name: 'Suwon', countryId: 43 },
      { id: 374, name: 'Ulsan', countryId: 43 },
      { id: 375, name: 'Changwon', countryId: 43 },
      { id: 376, name: 'Goyang', countryId: 43 },
      { id: 377, name: 'Rome', countryId: 44 },
      { id: 378, name: 'Milan', countryId: 44 },
      { id: 379, name: 'Naples', countryId: 44 },
      { id: 380, name: 'Turin', countryId: 44 },
      { id: 381, name: 'Palermo', countryId: 44 },
      { id: 382, name: 'Genoa', countryId: 44 },
      { id: 383, name: 'Bologna', countryId: 44 },
      { id: 384, name: 'Florence', countryId: 44 },
      { id: 385, name: 'Bari', countryId: 44 },
      { id: 386, name: 'Catania', countryId: 44 },
      { id: 387, name: 'Madrid', countryId: 45 },
      { id: 388, name: 'Barcelona', countryId: 45 },
      { id: 389, name: 'Valencia', countryId: 45 },
      { id: 390, name: 'Seville', countryId: 45 },
      { id: 391, name: 'Zaragoza', countryId: 45 },
      { id: 392, name: 'Málaga', countryId: 45 },
      { id: 393, name: 'Murcia', countryId: 45 },
      { id: 394, name: 'Palma', countryId: 45 },
      { id: 395, name: 'Las Palmas', countryId: 45 },
      { id: 396, name: 'Bilbao', countryId: 45 },
      { id: 397, name: 'Amsterdam', countryId: 46 },
      { id: 398, name: 'Rotterdam', countryId: 46 },
      { id: 399, name: 'The Hague', countryId: 46 },
      { id: 400, name: 'Utrecht', countryId: 46 },
      { id: 401, name: 'Eindhoven', countryId: 46 },
      { id: 402, name: 'Tilburg', countryId: 46 },
      { id: 403, name: 'Groningen', countryId: 46 },
      { id: 404, name: 'Almere', countryId: 46 },
      { id: 405, name: 'Breda', countryId: 46 },
      { id: 406, name: 'Nijmegen', countryId: 46 },
      { id: 407, name: 'Zurich', countryId: 47 },
      { id: 408, name: 'Geneva', countryId: 47 },
      { id: 409, name: 'Basel', countryId: 47 },
      { id: 410, name: 'Lausanne', countryId: 47 },
      { id: 411, name: 'Bern', countryId: 47 },
      { id: 412, name: 'Winterthur', countryId: 47 },
      { id: 413, name: 'Lucerne', countryId: 47 },
      { id: 414, name: 'St. Gallen', countryId: 47 },
      { id: 415, name: 'Lugano', countryId: 47 },
      { id: 416, name: 'Biel', countryId: 47 },
      { id: 417, name: 'Stockholm', countryId: 48 },
      { id: 418, name: 'Gothenburg', countryId: 48 },
      { id: 419, name: 'Malmö', countryId: 48 },
      { id: 420, name: 'Uppsala', countryId: 48 },
      { id: 421, name: 'Västerås', countryId: 48 },
      { id: 422, name: 'Örebro', countryId: 48 },
      { id: 423, name: 'Linköping', countryId: 48 },
      { id: 424, name: 'Helsingborg', countryId: 48 },
      { id: 425, name: 'Jönköping', countryId: 48 },
      { id: 426, name: 'Norrköping', countryId: 48 },
      { id: 427, name: 'Oslo', countryId: 49 },
      { id: 428, name: 'Bergen', countryId: 49 },
      { id: 429, name: 'Stavanger', countryId: 49 },
      { id: 430, name: 'Trondheim', countryId: 49 },
      { id: 431, name: 'Drammen', countryId: 49 },
      { id: 432, name: 'Fredrikstad', countryId: 49 },
      { id: 433, name: 'Kristiansand', countryId: 49 },
      { id: 434, name: 'Sandnes', countryId: 49 },
      { id: 435, name: 'Tromsø', countryId: 49 },
      { id: 436, name: 'Sarpsborg', countryId: 49 },
      { id: 437, name: 'Copenhagen', countryId: 50 },
      { id: 438, name: 'Aarhus', countryId: 50 },
      { id: 439, name: 'Odense', countryId: 50 },
      { id: 440, name: 'Aalborg', countryId: 50 },
      { id: 441, name: 'Esbjerg', countryId: 50 },
      { id: 442, name: 'Randers', countryId: 50 },
      { id: 443, name: 'Kolding', countryId: 50 },
      { id: 444, name: 'Horsens', countryId: 50 },
      { id: 445, name: 'Vejle', countryId: 50 },
      { id: 446, name: 'Roskilde', countryId: 50 },
      { id: 447, name: 'Singapore', countryId: 51 },
      { id: 448, name: 'Hong Kong', countryId: 52 },
      { id: 449, name: 'Cape Town', countryId: 53 },
      { id: 450, name: 'Johannesburg', countryId: 53 },
      { id: 451, name: 'Durban', countryId: 53 },
      { id: 452, name: 'Pretoria', countryId: 53 },
      { id: 453, name: 'Port Elizabeth', countryId: 53 },
      { id: 454, name: 'Bloemfontein', countryId: 53 },
      { id: 455, name: 'East London', countryId: 53 },
      { id: 456, name: 'Pietermaritzburg', countryId: 53 },
      { id: 457, name: 'Polokwane', countryId: 53 },
      { id: 458, name: 'Rustenburg', countryId: 53 },
      { id: 459, name: 'Jerusalem', countryId: 54 },
      { id: 460, name: 'Tel Aviv', countryId: 54 },
      { id: 461, name: 'Haifa', countryId: 54 },
      { id: 462, name: 'Rishon LeZion', countryId: 54 },
      { id: 463, name: 'Petah Tikva', countryId: 54 },
      { id: 464, name: 'Ashdod', countryId: 54 },
      { id: 465, name: 'Netanya', countryId: 54 },
      { id: 466, name: 'Beer Sheva', countryId: 54 },
      { id: 467, name: 'Holon', countryId: 54 },
      { id: 468, name: 'Bnei Brak', countryId: 54 },
      { id: 469, name: 'Dubai', countryId: 55 },
      { id: 470, name: 'Abu Dhabi', countryId: 55 },
      { id: 471, name: 'Sharjah', countryId: 55 },
      { id: 472, name: 'Al Ain', countryId: 55 },
      { id: 473, name: 'Ajman', countryId: 55 },
      { id: 474, name: 'Ras Al Khaimah', countryId: 55 },
      { id: 475, name: 'Fujairah', countryId: 55 },
      { id: 476, name: 'Umm Al Quwain', countryId: 55 },
      { id: 477, name: 'Khor Fakkan', countryId: 55 },
      { id: 478, name: 'Dibba Al-Fujairah', countryId: 55 },
      { id: 599, name: 'Djibouti', countryId: 97 },
      { id: 600, name: 'Ali Sabieh', countryId: 97 },
      { id: 601, name: 'Roseau', countryId: 98 },
      { id: 602, name: 'Portsmouth', countryId: 98 },
      { id: 603, name: 'Santo Domingo', countryId: 99 },
      { id: 604, name: 'Santiago', countryId: 99 },
      { id: 605, name: 'La Romana', countryId: 99 },
      { id: 606, name: 'Dili', countryId: 100 },
      { id: 607, name: 'Maliana', countryId: 100 },
      { id: 608, name: 'Guayaquil', countryId: 101 },
      { id: 609, name: 'Quito', countryId: 101 },
      { id: 610, name: 'Cuenca', countryId: 101 },
      { id: 611, name: 'Cairo', countryId: 102 },
      { id: 612, name: 'Alexandria', countryId: 102 },
      { id: 613, name: 'Giza', countryId: 102 },
      { id: 614, name: 'Shubra El Kheima', countryId: 102 },
      { id: 615, name: 'San Salvador', countryId: 103 },
      { id: 616, name: 'Santa Ana', countryId: 103 },
      { id: 617, name: 'San Miguel', countryId: 103 },
      { id: 618, name: 'Malabo', countryId: 104 },
      { id: 619, name: 'Bata', countryId: 104 },
      { id: 620, name: 'Asmara', countryId: 105 },
      { id: 621, name: 'Keren', countryId: 105 },
      { id: 622, name: 'Tallinn', countryId: 106 },
      { id: 623, name: 'Tartu', countryId: 106 },
      { id: 624, name: 'Narva', countryId: 106 },
      { id: 625, name: 'Addis Ababa', countryId: 107 },
      { id: 626, name: 'Dire Dawa', countryId: 107 },
      { id: 627, name: 'Mekelle', countryId: 107 },
      { id: 628, name: 'Suva', countryId: 108 },
      { id: 629, name: 'Lautoka', countryId: 108 },
      { id: 630, name: 'Nadi', countryId: 108 },
      { id: 631, name: 'Helsinki', countryId: 109 },
      { id: 632, name: 'Espoo', countryId: 109 },
      { id: 633, name: 'Tampere', countryId: 109 },
      { id: 634, name: 'Vantaa', countryId: 109 },
      { id: 635, name: 'Libreville', countryId: 110 },
      { id: 636, name: 'Port-Gentil', countryId: 110 },
      { id: 637, name: 'Banjul', countryId: 111 },
      { id: 638, name: 'Serekunda', countryId: 111 },
      { id: 639, name: 'Tbilisi', countryId: 112 },
      { id: 640, name: 'Batumi', countryId: 112 },
      { id: 641, name: 'Kutaisi', countryId: 112 },
      { id: 642, name: 'Accra', countryId: 113 },
      { id: 643, name: 'Kumasi', countryId: 113 },
      { id: 644, name: 'Tamale', countryId: 113 },
      { id: 645, name: 'Athens', countryId: 114 },
      { id: 646, name: 'Thessaloniki', countryId: 114 },
      { id: 647, name: 'Patras', countryId: 114 },
      { id: 648, name: 'Heraklion', countryId: 114 },
      { id: 649, name: 'St. George\'s', countryId: 115 },
      { id: 650, name: 'Gouyave', countryId: 115 },
      { id: 651, name: 'Guatemala City', countryId: 116 },
      { id: 652, name: 'Mixco', countryId: 116 },
      { id: 653, name: 'Villa Nueva', countryId: 116 },
      { id: 654, name: 'Conakry', countryId: 117 },
      { id: 655, name: 'Camayenne', countryId: 117 },
      { id: 656, name: 'Nzérékoré', countryId: 117 },
      { id: 657, name: 'Bissau', countryId: 118 },
      { id: 658, name: 'Bafatá', countryId: 118 },
      { id: 659, name: 'Georgetown', countryId: 119 },
      { id: 660, name: 'Linden', countryId: 119 },
      { id: 661, name: 'Port-au-Prince', countryId: 120 },
      { id: 662, name: 'Cap-Haïtien', countryId: 120 },
      { id: 663, name: 'Tegucigalpa', countryId: 121 },
      { id: 664, name: 'San Pedro Sula', countryId: 121 },
      { id: 665, name: 'Choloma', countryId: 121 },
      { id: 666, name: 'Budapest', countryId: 122 },
      { id: 667, name: 'Debrecen', countryId: 122 },
      { id: 668, name: 'Szeged', countryId: 122 },
      { id: 669, name: 'Miskolc', countryId: 122 },
      { id: 670, name: 'Reykjavik', countryId: 123 },
      { id: 671, name: 'Kópavogur', countryId: 123 },
      { id: 672, name: 'Hafnarfjörður', countryId: 123 },
      { id: 673, name: 'Jakarta', countryId: 124 },
      { id: 674, name: 'Surabaya', countryId: 124 },
      { id: 675, name: 'Bandung', countryId: 124 },
      { id: 676, name: 'Medan', countryId: 124 },
      { id: 677, name: 'Tehran', countryId: 125 },
      { id: 678, name: 'Mashhad', countryId: 125 },
      { id: 679, name: 'Isfahan', countryId: 125 },
      { id: 680, name: 'Karaj', countryId: 125 },
      { id: 681, name: 'Baghdad', countryId: 126 },
      { id: 682, name: 'Basra', countryId: 126 },
      { id: 683, name: 'Mosul', countryId: 126 },
      { id: 684, name: 'Erbil', countryId: 126 },
      { id: 685, name: 'Dublin', countryId: 127 },
      { id: 686, name: 'Cork', countryId: 127 },
      { id: 687, name: 'Limerick', countryId: 127 },
      { id: 688, name: 'Galway', countryId: 127 },
      { id: 689, name: 'Kingston', countryId: 128 },
      { id: 690, name: 'Montego Bay', countryId: 128 },
      { id: 691, name: 'Spanish Town', countryId: 128 },
      { id: 692, name: 'Amman', countryId: 129 },
      { id: 693, name: 'Zarqa', countryId: 129 },
      { id: 694, name: 'Irbid', countryId: 129 },
      { id: 695, name: 'Almaty', countryId: 130 },
      { id: 696, name: 'Nur-Sultan', countryId: 130 },
      { id: 697, name: 'Shymkent', countryId: 130 },
      { id: 698, name: 'Karaganda', countryId: 130 },
      { id: 699, name: 'Nairobi', countryId: 131 },
      { id: 700, name: 'Mombasa', countryId: 131 },
      { id: 701, name: 'Kisumu', countryId: 131 },
      { id: 702, name: 'Nakuru', countryId: 131 },
      { id: 703, name: 'Tarawa', countryId: 132 },
      { id: 704, name: 'Betio', countryId: 132 },
      { id: 705, name: 'Pristina', countryId: 133 },
      { id: 706, name: 'Prizren', countryId: 133 },
      { id: 707, name: 'Kuwait City', countryId: 134 },
      { id: 708, name: 'Al Jahra', countryId: 134 },
      { id: 709, name: 'Bishkek', countryId: 135 },
      { id: 710, name: 'Osh', countryId: 135 },
      { id: 711, name: 'Jalal-Abad', countryId: 135 },
      { id: 712, name: 'Vientiane', countryId: 136 },
      { id: 713, name: 'Luang Prabang', countryId: 136 },
      { id: 714, name: 'Riga', countryId: 137 },
      { id: 715, name: 'Daugavpils', countryId: 137 },
      { id: 716, name: 'Liepāja', countryId: 137 },
      { id: 717, name: 'Beirut', countryId: 138 },
      { id: 718, name: 'Tripoli', countryId: 138 },
      { id: 719, name: 'Sidon', countryId: 138 },
      { id: 720, name: 'Maseru', countryId: 139 },
      { id: 721, name: 'Teyateyaneng', countryId: 139 },
      { id: 722, name: 'Monrovia', countryId: 140 },
      { id: 723, name: 'Gbarnga', countryId: 140 },
      { id: 724, name: 'Tripoli', countryId: 141 },
      { id: 725, name: 'Benghazi', countryId: 141 },
      { id: 726, name: 'Vaduz', countryId: 142 },
      { id: 727, name: 'Schaan', countryId: 142 },
      { id: 728, name: 'Vilnius', countryId: 143 },
      { id: 729, name: 'Kaunas', countryId: 143 },
      { id: 730, name: 'Klaipėda', countryId: 143 },
      { id: 731, name: 'Šiauliai', countryId: 143 },
      { id: 732, name: 'Luxembourg City', countryId: 144 },
      { id: 733, name: 'Esch-sur-Alzette', countryId: 144 },
      { id: 734, name: 'Antananarivo', countryId: 145 },
      { id: 735, name: 'Toamasina', countryId: 145 },
      { id: 736, name: 'Antsirabe', countryId: 145 },
      { id: 737, name: 'Lilongwe', countryId: 146 },
      { id: 738, name: 'Blantyre', countryId: 146 },
      { id: 739, name: 'Mzuzu', countryId: 146 },
      { id: 740, name: 'Kuala Lumpur', countryId: 147 },
      { id: 741, name: 'George Town', countryId: 147 },
      { id: 742, name: 'Johor Bahru', countryId: 147 },
      { id: 743, name: 'Kota Kinabalu', countryId: 147 },
      { id: 744, name: 'Malé', countryId: 148 },
      { id: 745, name: 'Addu City', countryId: 148 },
      { id: 746, name: 'Bamako', countryId: 149 },
      { id: 747, name: 'Ségou', countryId: 149 },
      { id: 748, name: 'Mopti', countryId: 149 },
      { id: 749, name: 'Valletta', countryId: 150 },
      { id: 750, name: 'Birkirkara', countryId: 150 },
      { id: 751, name: 'Mosta', countryId: 150 },
      { id: 752, name: 'Majuro', countryId: 151 },
      { id: 753, name: 'Ebeye', countryId: 151 },
      { id: 754, name: 'Nouakchott', countryId: 152 },
      { id: 755, name: 'Nouadhibou', countryId: 152 },
      { id: 756, name: 'Port Louis', countryId: 153 },
      { id: 757, name: 'Curepipe', countryId: 153 },
      { id: 758, name: 'Vacoas', countryId: 153 },
      { id: 759, name: 'Palikir', countryId: 154 },
      { id: 760, name: 'Weno', countryId: 154 },
      { id: 761, name: 'Chisinau', countryId: 155 },
      { id: 762, name: 'Tiraspol', countryId: 155 },
      { id: 763, name: 'Bălți', countryId: 155 },
      { id: 764, name: 'Monaco', countryId: 156 },
      { id: 765, name: 'Monte Carlo', countryId: 156 },
      { id: 766, name: 'Ulaanbaatar', countryId: 157 },
      { id: 767, name: 'Erdenet', countryId: 157 },
      { id: 768, name: 'Darkhan', countryId: 157 },
      { id: 769, name: 'Podgorica', countryId: 158 },
      { id: 770, name: 'Nikšić', countryId: 158 },
      { id: 771, name: 'Herceg Novi', countryId: 158 },
      { id: 772, name: 'Casablanca', countryId: 159 },
      { id: 773, name: 'Rabat', countryId: 159 },
      { id: 774, name: 'Fes', countryId: 159 },
      { id: 775, name: 'Marrakech', countryId: 159 },
      { id: 776, name: 'Maputo', countryId: 160 },
      { id: 777, name: 'Matola', countryId: 160 },
      { id: 778, name: 'Beira', countryId: 160 },
      { id: 779, name: 'Yangon', countryId: 161 },
      { id: 780, name: 'Mandalay', countryId: 161 },
      { id: 781, name: 'Naypyidaw', countryId: 161 },
      { id: 782, name: 'Windhoek', countryId: 162 },
      { id: 783, name: 'Rundu', countryId: 162 },
      { id: 784, name: 'Walvis Bay', countryId: 162 },
      { id: 785, name: 'Yaren', countryId: 163 },
      { id: 786, name: 'Baiti', countryId: 163 },
      { id: 787, name: 'Kathmandu', countryId: 164 },
      { id: 788, name: 'Pokhara', countryId: 164 },
      { id: 789, name: 'Lalitpur', countryId: 164 },
      { id: 790, name: 'Managua', countryId: 165 },
      { id: 791, name: 'León', countryId: 165 },
      { id: 792, name: 'Masaya', countryId: 165 },
      { id: 793, name: 'Niamey', countryId: 166 },
      { id: 794, name: 'Zinder', countryId: 166 },
      { id: 795, name: 'Maradi', countryId: 166 },
      { id: 796, name: 'Lagos', countryId: 167 },
      { id: 797, name: 'Kano', countryId: 167 },
      { id: 798, name: 'Ibadan', countryId: 167 },
      { id: 799, name: 'Abuja', countryId: 167 },
      { id: 800, name: 'Skopje', countryId: 168 },
      { id: 801, name: 'Bitola', countryId: 168 },
      { id: 802, name: 'Kumanovo', countryId: 168 },
      { id: 803, name: 'Muscat', countryId: 169 },
      { id: 804, name: 'Salalah', countryId: 169 },
      { id: 805, name: 'Barka', countryId: 169 },
      { id: 806, name: 'Karachi', countryId: 170 },
      { id: 807, name: 'Lahore', countryId: 170 },
      { id: 808, name: 'Faisalabad', countryId: 170 },
      { id: 809, name: 'Rawalpindi', countryId: 170 },
      { id: 810, name: 'Ngerulmud', countryId: 171 },
      { id: 811, name: 'Koror', countryId: 171 },
      { id: 812, name: 'Ramallah', countryId: 172 },
      { id: 813, name: 'Gaza City', countryId: 172 },
      { id: 814, name: 'Hebron', countryId: 172 },
      { id: 815, name: 'Panama City', countryId: 173 },
      { id: 816, name: 'Colón', countryId: 173 },
      { id: 817, name: 'David', countryId: 173 },
      { id: 818, name: 'Port Moresby', countryId: 174 },
      { id: 819, name: 'Lae', countryId: 174 },
      { id: 820, name: 'Mount Hagen', countryId: 174 },
      { id: 821, name: 'Asunción', countryId: 175 },
      { id: 822, name: 'Ciudad del Este', countryId: 175 },
      { id: 823, name: 'San Lorenzo', countryId: 175 },
      { id: 824, name: 'Lima', countryId: 176 },
      { id: 825, name: 'Arequipa', countryId: 176 },
      { id: 826, name: 'Trujillo', countryId: 176 },
      { id: 827, name: 'Chiclayo', countryId: 176 },
      { id: 828, name: 'Quezon City', countryId: 177 },
      { id: 829, name: 'Manila', countryId: 177 },
      { id: 830, name: 'Caloocan', countryId: 177 },
      { id: 831, name: 'Davao City', countryId: 177 },
      { id: 832, name: 'Warsaw', countryId: 178 },
      { id: 833, name: 'Kraków', countryId: 178 },
      { id: 834, name: 'Łódź', countryId: 178 },
      { id: 835, name: 'Wrocław', countryId: 178 },
      { id: 836, name: 'Lisbon', countryId: 179 },
      { id: 837, name: 'Porto', countryId: 179 },
      { id: 838, name: 'Vila Nova de Gaia', countryId: 179 },
      { id: 839, name: 'Braga', countryId: 179 },
      { id: 840, name: 'Doha', countryId: 180 },
      { id: 841, name: 'Al Rayyan', countryId: 180 },
      { id: 842, name: 'Umm Salal', countryId: 180 },
      { id: 843, name: 'Brazzaville', countryId: 181 },
      { id: 844, name: 'Pointe-Noire', countryId: 181 },
      { id: 845, name: 'Bucharest', countryId: 182 },
      { id: 846, name: 'Cluj-Napoca', countryId: 182 },
      { id: 847, name: 'Timișoara', countryId: 182 },
      { id: 848, name: 'Iași', countryId: 182 },
      { id: 849, name: 'Kigali', countryId: 184 },
      { id: 850, name: 'Butare', countryId: 184 },
      { id: 851, name: 'Gitarama', countryId: 184 },
      { id: 852, name: 'Basseterre', countryId: 185 },
      { id: 853, name: 'Charlestown', countryId: 185 },
      { id: 854, name: 'Castries', countryId: 186 },
      { id: 855, name: 'Gros Islet', countryId: 186 },
      { id: 856, name: 'Kingstown', countryId: 187 },
      { id: 857, name: 'Georgetown', countryId: 187 },
      { id: 858, name: 'Apia', countryId: 188 },
      { id: 859, name: 'Vaitele', countryId: 188 },
      { id: 860, name: 'San Marino', countryId: 189 },
      { id: 861, name: 'Serravalle', countryId: 189 },
      { id: 862, name: 'São Tomé', countryId: 190 },
      { id: 863, name: 'Guadalupe', countryId: 190 },
      { id: 864, name: 'Riyadh', countryId: 191 },
      { id: 865, name: 'Jeddah', countryId: 191 },
      { id: 866, name: 'Mecca', countryId: 191 },
      { id: 867, name: 'Medina', countryId: 191 },
      { id: 868, name: 'Dakar', countryId: 192 },
      { id: 869, name: 'Touba', countryId: 192 },
      { id: 870, name: 'Thies', countryId: 192 },
      { id: 871, name: 'Belgrade', countryId: 193 },
      { id: 872, name: 'Novi Sad', countryId: 193 },
      { id: 873, name: 'Niš', countryId: 193 },
      { id: 874, name: 'Kragujevac', countryId: 193 },
      { id: 875, name: 'Victoria', countryId: 194 },
      { id: 876, name: 'Anse Boileau', countryId: 194 },
      { id: 877, name: 'Freetown', countryId: 195 },
      { id: 878, name: 'Bo', countryId: 195 },
      { id: 879, name: 'Bratislava', countryId: 196 },
      { id: 880, name: 'Košice', countryId: 196 },
      { id: 881, name: 'Prešov', countryId: 196 },
      { id: 882, name: 'Žilina', countryId: 196 },
      { id: 883, name: 'Ljubljana', countryId: 197 },
      { id: 884, name: 'Maribor', countryId: 197 },
      { id: 885, name: 'Kranj', countryId: 197 },
      { id: 886, name: 'Celje', countryId: 197 },
      { id: 887, name: 'Honiara', countryId: 198 },
      { id: 888, name: 'Gizo', countryId: 198 },
      { id: 889, name: 'Mogadishu', countryId: 199 },
      { id: 890, name: 'Hargeisa', countryId: 199 },
      { id: 891, name: 'Berbera', countryId: 199 },
      { id: 892, name: 'Colombo', countryId: 200 },
      { id: 893, name: 'Kandy', countryId: 200 },
      { id: 894, name: 'Galle', countryId: 200 },
      { id: 895, name: 'Jaffna', countryId: 200 },
      { id: 896, name: 'Khartoum', countryId: 201 },
      { id: 897, name: 'Omdurman', countryId: 201 },
      { id: 898, name: 'Nyala', countryId: 201 },
      { id: 899, name: 'Paramaribo', countryId: 202 },
      { id: 900, name: 'Lelydorp', countryId: 202 },
      { id: 901, name: 'Mbabane', countryId: 203 },
      { id: 902, name: 'Manzini', countryId: 203 },
      { id: 903, name: 'Damascus', countryId: 204 },
      { id: 904, name: 'Aleppo', countryId: 204 },
      { id: 905, name: 'Homs', countryId: 204 },
      { id: 906, name: 'Latakia', countryId: 204 },
      { id: 907, name: 'Dushanbe', countryId: 205 },
      { id: 908, name: 'Khujand', countryId: 205 },
      { id: 909, name: 'Kulob', countryId: 205 },
      { id: 910, name: 'Dar es Salaam', countryId: 206 },
      { id: 911, name: 'Dodoma', countryId: 206 },
      { id: 912, name: 'Mwanza', countryId: 206 },
      { id: 913, name: 'Zanzibar City', countryId: 206 },
      { id: 914, name: 'Bangkok', countryId: 207 },
      { id: 915, name: 'Chiang Mai', countryId: 207 },
      { id: 916, name: 'Phuket', countryId: 207 },
      { id: 917, name: 'Pattaya', countryId: 207 },
      { id: 918, name: 'Lomé', countryId: 208 },
      { id: 919, name: 'Sokodé', countryId: 208 },
      { id: 920, name: 'Kara', countryId: 208 },
      { id: 921, name: 'Nuku\'alofa', countryId: 209 },
      { id: 922, name: 'Neiafu', countryId: 209 },
      { id: 923, name: 'Port of Spain', countryId: 210 },
      { id: 924, name: 'Chaguanas', countryId: 210 },
      { id: 925, name: 'San Fernando', countryId: 210 },
      { id: 926, name: 'Tunis', countryId: 211 },
      { id: 927, name: 'Sfax', countryId: 211 },
      { id: 928, name: 'Sousse', countryId: 211 },
      { id: 929, name: 'Kairouan', countryId: 211 },
      { id: 930, name: 'Istanbul', countryId: 212 },
      { id: 931, name: 'Ankara', countryId: 212 },
      { id: 932, name: 'Izmir', countryId: 212 },
      { id: 933, name: 'Bursa', countryId: 212 },
      { id: 934, name: 'Ashgabat', countryId: 213 },
      { id: 935, name: 'Türkmenabat', countryId: 213 },
      { id: 936, name: 'Dashoguz', countryId: 213 },
      { id: 937, name: 'Funafuti', countryId: 214 },
      { id: 938, name: 'Vaiaku', countryId: 214 },
      { id: 939, name: 'Kampala', countryId: 215 },
      { id: 940, name: 'Gulu', countryId: 215 },
      { id: 941, name: 'Mbarara', countryId: 215 },
      { id: 942, name: 'Kyiv', countryId: 216 },
      { id: 943, name: 'Kharkiv', countryId: 216 },
      { id: 944, name: 'Odesa', countryId: 216 },
      { id: 945, name: 'Dnipro', countryId: 216 },
      { id: 946, name: 'Montevideo', countryId: 217 },
      { id: 947, name: 'Salto', countryId: 217 },
      { id: 948, name: 'Paysandú', countryId: 217 },
      { id: 949, name: 'Tashkent', countryId: 218 },
      { id: 950, name: 'Samarkand', countryId: 218 },
      { id: 951, name: 'Namangan', countryId: 218 },
      { id: 952, name: 'Andijan', countryId: 218 },
      { id: 953, name: 'Port Vila', countryId: 219 },
      { id: 954, name: 'Luganville', countryId: 219 },
      { id: 955, name: 'Vatican City', countryId: 220 },
      { id: 956, name: 'Caracas', countryId: 221 },
      { id: 957, name: 'Maracaibo', countryId: 221 },
      { id: 958, name: 'Valencia', countryId: 221 },
      { id: 959, name: 'Barquisimeto', countryId: 221 },
      { id: 960, name: 'Ho Chi Minh City', countryId: 222 },
      { id: 961, name: 'Hanoi', countryId: 222 },
      { id: 962, name: 'Da Nang', countryId: 222 },
      { id: 963, name: 'Hai Phong', countryId: 222 },
      { id: 964, name: 'Sana\'a', countryId: 223 },
      { id: 965, name: 'Aden', countryId: 223 },
      { id: 966, name: 'Taiz', countryId: 223 },
      { id: 967, name: 'Lusaka', countryId: 224 },
      { id: 968, name: 'Kitwe', countryId: 224 },
      { id: 969, name: 'Ndola', countryId: 224 },
      { id: 970, name: 'Harare', countryId: 225 },
      { id: 971, name: 'Bulawayo', countryId: 225 },
      { id: 972, name: 'Chitungwiza', countryId: 225 }
    ];

    for (const city of cities) {
      await prisma.city.upsert({
        where: { id: city.id },
        update: city,
        create: city
      });
    }

    console.log(`Inserted ${cities.length} cities`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

insertData();