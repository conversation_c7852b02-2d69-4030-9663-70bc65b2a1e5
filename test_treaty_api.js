// Simple test to check treaty numbers API
const testUserId = 'cmfbrh2sv0025ppsjfy5npo3d'; // Use the actual user ID from the logs

async function testTreatyNumbersAPI() {
  try {
    console.log('Testing treaty numbers API...');
    
    // Test GET request
    const response = await fetch(`http://localhost:3001/api/users/${testUserId}/treaty-numbers`);
    console.log('Response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('Response data:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

// Run the test
testTreatyNumbersAPI();