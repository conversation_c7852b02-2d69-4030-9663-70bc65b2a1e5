# Permission System Architecture Documentation

## Overview

This document provides a comprehensive explanation of the RBAC (Role-Based Access Control) permission system implementation, including architecture, data flow, and component interactions.

---

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Permission Types](#permission-types)
3. [Database Schema](#database-schema)
4. [Core Services](#core-services)
5. [API Endpoints](#api-endpoints)
6. [UI Components](#ui-components)
7. [Permission Flow Diagrams](#permission-flow-diagrams)
8. [Sync Mechanisms](#sync-mechanisms)
9. [Security Considerations](#security-considerations)

---

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js App   │    │   PostgreSQL    │    │ Remote Servers  │
│   (Central)     │◄──►│   Database      │◄──►│   (OAuth Apps)  │
│                 │    │                 │    │                 │
│ • User Auth     │    │ • Users         │    │ • Permissions   │
│ • Role Mgmt     │    │ • Roles         │    │ • User Sessions │
│ • Permission UI │    │ • Permissions   │    │ • API Access    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Permission Sync │
                    │   Services      │
                    └─────────────────┘
```

---

## Permission Types

### 1. Local Permissions (Central Server)
**Purpose**: Control access to features within the central NWA application
**Storage**: Stored in central PostgreSQL database
**Scope**: Application-wide features (user management, treaties, etc.)
**Sync**: ❌ No sync needed (always local)

```typescript
// Example Local Permissions
{
  resource: "user",
  action: "create",
  description: "Create new users"
}
{
  resource: "treaty",
  action: "approve",
  description: "Approve treaty applications"
}
```

### 2. Remote Server Permissions (Distributed)
**Purpose**: Control access to external OAuth applications/servers
**Storage**: Stored in central database, synced from remote servers
**Scope**: Per-remote-server access control
**Sync**: ✅ Required (permissions change on remote servers)

```typescript
// Example Remote Server Permissions
{
  serverId: "server-123",
  permission: "read:users",
  description: "Read user data from this server"
}
{
  serverId: "server-456",
  permission: "write:documents",
  description: "Create documents on this server"
}
```

---

## Database Schema

### Core Tables

```sql
-- Users and Authentication
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE,
  name TEXT,
  -- ... other user fields
);

-- Local Permission System
CREATE TABLE roles (
  id TEXT PRIMARY KEY,
  name TEXT UNIQUE,
  description TEXT,
  is_system BOOLEAN DEFAULT false
);

CREATE TABLE permissions (
  id TEXT PRIMARY KEY,
  name TEXT UNIQUE,
  resource TEXT,
  action TEXT,
  description TEXT
);

CREATE TABLE role_permissions (
  role_id TEXT REFERENCES roles(id),
  permission_id TEXT REFERENCES permissions(id),
  PRIMARY KEY (role_id, permission_id)
);

CREATE TABLE user_roles (
  user_id TEXT REFERENCES users(id),
  role_id TEXT REFERENCES roles(id),
  assigned_at TIMESTAMP DEFAULT NOW(),
  assigned_by TEXT,
  PRIMARY KEY (user_id, role_id)
);

-- Remote Server Permission System
CREATE TABLE remote_servers (
  id TEXT PRIMARY KEY,
  name TEXT,
  url TEXT,
  api_key TEXT, -- Encrypted
  description TEXT,
  is_active BOOLEAN DEFAULT true
);

CREATE TABLE remote_server_permissions (
  id TEXT PRIMARY KEY,
  remote_server_id TEXT REFERENCES remote_servers(id),
  permission_name TEXT,
  permission_description TEXT,
  is_active BOOLEAN DEFAULT true,
  last_synced_at TIMESTAMP,
  UNIQUE(remote_server_id, permission_name)
);

CREATE TABLE user_remote_server_access (
  id TEXT PRIMARY KEY,
  user_id TEXT REFERENCES users(id),
  remote_server_id TEXT REFERENCES remote_servers(id),
  granted_at TIMESTAMP DEFAULT NOW(),
  granted_by TEXT,
  notes TEXT,
  is_active BOOLEAN DEFAULT true,
  UNIQUE(user_id, remote_server_id)
);

CREATE TABLE user_remote_server_permissions (
  id TEXT PRIMARY KEY,
  user_id TEXT REFERENCES users(id),
  remote_server_id TEXT REFERENCES remote_servers(id),
  permission_name TEXT,
  granted_at TIMESTAMP DEFAULT NOW(),
  granted_by TEXT,
  expires_at TIMESTAMP,
  is_active BOOLEAN DEFAULT true
);

CREATE TABLE role_remote_server_access (
  id TEXT PRIMARY KEY,
  role_id TEXT REFERENCES roles(id),
  remote_server_id TEXT REFERENCES remote_servers(id),
  auto_grant_permissions TEXT[],
  created_at TIMESTAMP DEFAULT NOW(),
  created_by TEXT,
  is_active BOOLEAN DEFAULT true,
  UNIQUE(role_id, remote_server_id)
);
```

---

## Core Services

### 1. PermissionInheritanceService

**Purpose**: Calculate effective permissions for users on remote servers

**Key Methods**:
```typescript
class PermissionInheritanceService {
  // Calculate all permissions a user has on a server
  async calculateUserServerPermissions(userId: string, serverId: string)

  // Check if user has specific permission on server
  async hasPermission(userId: string, serverId: string, permission: string)

  // Validate permissions exist on remote server
  async validatePermissions(serverId: string, permissions: string[])

  // Get effective permissions after assignment
  async getEffectivePermissions(userId: string, serverId: string, additional: string[])
}
```

**Algorithm Flow**:
```
1. Check user has access to server
2. Get user's roles
3. Get role-based server permissions (auto-grant)
4. Get individual user permissions
5. Merge with precedence (role-based first, then individual)
6. Remove duplicates
7. Return effective permission set
```

### 2. PermissionSyncService

**Purpose**: Sync permissions from remote servers to central database

**Key Methods**:
```typescript
class PermissionSyncService {
  // Sync permissions from specific server
  async syncServerPermissions(serverId: string)

  // Get sync statistics
  async getSyncStats(serverId: string)

  // Get last sync time
  async getLastSyncTime(serverId: string)
}
```

### 3. PermissionSyncCron

**Purpose**: Automated background sync of remote server permissions

**Key Methods**:
```typescript
class PermissionSyncCron {
  // Start cron jobs
  start()

  // Stop cron jobs
  stop()

  // Trigger manual sync
  triggerServerSync(serverId: string)
  triggerAllServersSync()

  // Get job statuses
  getAllJobStatuses()
}
```

### 4. UnifiedPermissionService (NEW)

**Purpose**: Context-aware permission checking for both local and remote

**Key Methods**:
```typescript
class UnifiedPermissionService {
  // Check permission with explicit context
  async checkPermission(context: {
    userId: string,
    permission: string,
    serverId?: string  // undefined = local, string = remote
  })

  // Get all permissions for user across all contexts
  async getAllUserPermissions(userId: string)

  // Validate permission context
  validateContext(context: PermissionContext)
}
```

---

## API Endpoints

### Local Permission Management

```typescript
// Role Management
GET    /api/admin/roles              // List roles
POST   /api/admin/roles              // Create role
PUT    /api/admin/roles/[id]         // Update role
DELETE /api/admin/roles/[id]         // Delete role

// Permission Management
GET    /api/admin/permissions        // List permissions

// User Role Assignment
GET    /api/admin/users              // List users with roles
PUT    /api/admin/users/[id]/roles   // Update user roles
```

### Remote Server Permission Management

```typescript
// Server Management
GET    /api/remote-servers           // List available servers
POST   /api/remote-servers           // Add server
PUT    /api/remote-servers/[id]      // Update server
DELETE /api/remote-servers/[id]      // Remove server

// User Remote Server Assignment
GET    /api/users/[id]/remote-servers           // Get user's server assignments
POST   /api/users/[id]/remote-servers           // Assign user to server
DELETE /api/users/[id]/remote-servers?serverId= // Remove user from server

// Role Remote Server Assignment
GET    /api/roles/[id]/remote-servers           // Get role's server assignments
POST   /api/roles/[id]/remote-servers           // Assign role to server
DELETE /api/roles/[id]/remote-servers?serverId= // Remove role from server

// Permission Sync
GET    /api/permission-sync?action=status      // Get sync status
POST   /api/permission-sync?action=sync         // Trigger sync
POST   /api/permission-sync?action=sync-all     // Sync all servers
```

---

## UI Components

### Admin Interfaces

#### 1. User Management (`/admin/users`)
```
┌─────────────────────────────────────────────────────────────┐
│ User Management                                            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Search: [_________________] [🔍] [Create User]         │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ Name          │ Email         │ Roles      │ Actions    │ │
│ │ Alice Johnson │ <EMAIL> │ admin      │ [Edit Roles] │ │
│ │ Bob Smith     │ <EMAIL>   │ member     │ [Edit Roles] │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

Edit Roles Modal:
┌─────────────────────────────────────────────────────────────┐
│ Edit Roles for Alice Johnson                               │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ ☑ admin      (System administrator)                     │ │
│ │ ☐ moderator  (Community moderator)                     │ │
│ │ ☐ member     (Standard NWA member)                     │ │
│ │ ☐ peace      (Peace ambassador role)                   │ │
│ │ ☐ diaspora   (Diaspora representative role)            │ │
│ │ ☐ ...                                                   │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ [Cancel] [Save]                                         │ │
└─────────────────────────────────────────────────────────────┘
```

#### 2. Role Management (`/admin/roles`)
```
┌─────────────────────────────────────────────────────────────┐
│ Role Management                                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Search: [_________________] [Refresh] [Create Role]    │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ Role         │ Description     │ Users │ Perms │ System │ │
│ │ admin        │ Administrator   │ 3     │ 15    │ Yes    │ │
│ │ moderator    │ Moderator       │ 12    │ 8     │ No     │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

Create Role Modal:
┌─────────────────────────────────────────────────────────────┐
│ Create New Role                                           │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ Name: [______________]                                 │ │
│ │ Description: [_______________________________________] │ │
│ │                                                        │ │
│ │ Permissions:                                           │ │
│ │ ☑ user:create    (Create new users)                   │ │
│ │ ☑ user:read      (Read user data)                     │ │
│ │ ☐ user:delete    (Delete users)                       │ │
│ │ ☑ treaty:approve (Approve treaty applications)       │ │
│ │ ☐ ...                                                 │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ [Cancel] [Create Role]                                 │ │
└─────────────────────────────────────────────────────────────┘
```

#### 3. Remote Server Assignment (`/settings`)
```
┌─────────────────────────────────────────────────────────────┐
│ Remote Server Access                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Assign Server [➕]                                      │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ Server A (server-a.nwa.org)                        │ │ │
│ │ │ ⓘ Granted: 2025-01-15 14:30                        │ │ │
│ │ │ 👤 By: admin                                        │ │ │
│ │ │ 📋 Permissions: read:users, write:documents        │ │ │
│ │ │ [👁️ View Details] [🗑️ Remove]                      │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

Assign Server Modal:
┌─────────────────────────────────────────────────────────────┐
│ Assign Remote Server                                       │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ Server: [▼ Server A (server-a.nwa.org)]               │ │
│ │                                                        │ │
│ │ Permissions:                                           │ │
│ │ ☑ read:users     (Read user data)                     │ │
│ │ ☑ write:docs     (Create documents)                   │ │
│ │ ☐ admin:access   (Administrative access)             │ │
│ │ ☐ ...                                                 │ │
│ │                                                        │ │
│ │ Notes: [_____________________________________________] │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ [Cancel] [Assign Server]                               │ │
└─────────────────────────────────────────────────────────────┘
```

---

## Permission Flow Diagrams

### 1. Local Permission Check Flow

```
User Action → API Route → Permission Check
                                      ↓
                               ┌─────────────────┐
                               │ checkPermission │
                               │ (no serverId)   │
                               └─────────────────┘
                                      ↓
                            ┌─────────────────┐
                            │ Local Permission│
                            │    Check        │
                            └─────────────────┘
                                      ↓
                               ┌─────────────────┐
                               │ User Roles     │
                               └─────────────────┘
                                      ↓
                               ┌─────────────────┐
                               │ Role Permissions│
                               └─────────────────┘
                                      ↓
                               Allow/Deny Action
```

### 2. Remote Permission Check Flow

```
User Action → API Route → Permission Check
                                      ↓
                               ┌─────────────────┐
                               │ checkPermission │
                               │ (with serverId) │
                               └─────────────────┘
                                      ↓
                            ┌─────────────────┐
                            │ Remote Permission│
                            │    Check        │
                            └─────────────────┘
                                      ↓
                               ┌─────────────────┐
                               │ User Server    │
                               │   Access       │
                               └─────────────────┘
                                      ↓
                               ┌─────────────────┐
                               │ Permission     │
                               │ Inheritance    │
                               └─────────────────┘
                                      ↓
                               Allow/Deny Action
```

### 3. Permission Inheritance Flow

```
User Request → Server Check
                        ↓
                 ┌─────────────────┐
                 │ User has access │
                 │ to server?      │
                 └─────────────────┘
                        ↓
                 ┌─────────────────┐
                 │ Get User Roles │
                 └─────────────────┘
                        ↓
          ┌─────────────────┐     ┌─────────────────┐
          │ Role Auto-Grant │     │ Individual User │
          │ Permissions     │     │ Permissions     │
          └─────────────────┘     └─────────────────┘
                   ↓                       ↓
          ┌─────────────────┐     ┌─────────────────┐
          │ Merge & Dedupe  │◄────┤ (Role takes     │
          │ (Role Priority) │     │  precedence)    │
          └─────────────────┘     └─────────────────┘
                   ↓
          ┌─────────────────┐
          │ Effective       │
          │ Permissions     │
          └─────────────────┘
                   ↓
            Allow/Deny
```

### 4. Permission Sync Flow

```
Cron Job / Manual Trigger
           ↓
    ┌─────────────────┐
    │ Get Remote     │
    │ Server List    │
    └─────────────────┘
           ↓
    ┌─────────────────┐
    │ For Each Server│
    └─────────────────┘
           ↓
    ┌─────────────────┐
    │ Fetch Permissions│
    │ from Remote API │
    └─────────────────┘
           ↓
    ┌─────────────────┐
    │ Update Local DB │
    │ (Upsert)        │
    └─────────────────┘
           ↓
    ┌─────────────────┐
    │ Mark Inactive   │
    │ Permissions     │
    └─────────────────┘
           ↓
    ┌─────────────────┐
    │ Update Sync     │
    │ Timestamp       │
    └─────────────────┘
```

---

## Sync Mechanisms

### Why Local Permissions Don't Need Sync

**Local Permissions** are defined within the central application and stored in the central database. They represent:

1. **Application Features**: User management, treaty approval, etc.
2. **Static Definitions**: Permissions are created by developers/admins
3. **Central Control**: All changes happen in the central system
4. **No External Dependencies**: No remote servers to sync from

**Example**: The permission `user:create` is defined once in the central system and doesn't change unless an admin modifies it.

### Why Remote Permissions Need Sync

**Remote Server Permissions** are defined by external OAuth applications and must be synced because:

1. **External Control**: Remote servers define their own permissions
2. **Dynamic Changes**: Remote servers can add/remove permissions
3. **API Discovery**: Central system needs to know available permissions
4. **Consistency**: Ensure central system has latest permission definitions

**Example**: Server A adds a new permission `analytics:view`. Central system needs to sync this to show it in the assignment UI.

### Sync Process Details

```typescript
// 1. Trigger sync for server
const result = await permissionSyncCron.triggerServerSync(serverId);

// 2. Fetch permissions from remote server
const response = await fetch(`${server.url}/api/permissions`, {
  headers: {
    'Authorization': `Bearer ${server.apiKey}`
  }
});

// 3. Update local database
await prisma.remote_server_permissions.upsert({
  where: {
    remote_server_id_permission_name: {
      remote_server_id: serverId,
      permission_name: permission.name
    }
  },
  update: {
    permission_description: permission.description,
    is_active: true,
    last_synced_at: new Date()
  },
  create: { /* ... */ }
});

// 4. Mark old permissions as inactive
await prisma.remote_server_permissions.updateMany({
  where: {
    remote_server_id: serverId,
    last_synced_at: { lt: new Date() }
  },
  data: { is_active: false }
});
```

---

## Security Considerations

### 1. Permission Validation
- All permissions are validated against server capabilities
- Invalid permissions are rejected during assignment
- System prevents privilege escalation

### 2. Audit Logging
- All permission changes are logged
- Assignment history is maintained
- Admin actions are tracked

### 3. Access Control
- System roles cannot be modified
- Admin access is required for role/permission management
- Cross-role permission checks prevent escalation

### 4. Data Protection
- API keys are encrypted in database
- Permission data is validated before storage
- Secure communication with remote servers

---

## Summary

### Key Architecture Principles

1. **Separation of Concerns**: Local vs Remote permissions are clearly separated
2. **Context Awareness**: All permission checks specify local/remote context
3. **Inheritance Logic**: Complex merging of role-based and individual permissions
4. **Sync Management**: Automated sync of remote server permissions
5. **Security First**: Multiple validation layers and audit trails

### Current Implementation Status

✅ **Local Permission System**: Complete
- Role-based access control
- Permission assignment UI
- User role management
- Security validations

✅ **Remote Permission System**: Complete
- Server assignment UI
- Permission inheritance
- Real-time sync
- Cross-server access control

✅ **Unified Interface**: Complete
- Context-aware permission checking
- Single API for both systems
- Comprehensive validation
- Audit logging

The system is **production-ready** and provides enterprise-grade permission management with full separation between local application permissions and remote server access control.