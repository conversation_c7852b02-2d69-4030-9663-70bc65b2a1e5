const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function populateDatabase() {
  try {
    console.log('Populating database with categories and subcategories...');

    // Categories data
    const categoriesData = [
      { id: 6, name: 'Agriculture', description: 'Farming, forestry, fishing and related activities' },
      { id: 7, name: 'Food & Beverage', description: 'Restaurants, cafes, food production and distribution' },
      { id: 8, name: 'Manufacturing', description: 'Production of goods from raw materials' },
      { id: 9, name: 'Construction', description: 'Building and infrastructure development' },
      { id: 10, name: 'Transport & Logistics', description: 'Movement of goods and people' },
      { id: 11, name: 'Technology', description: 'Software, IT services and digital solutions' },
      { id: 12, name: 'Professional Services', description: 'Consulting, legal, accounting and other specialized services' },
      { id: 13, name: 'Tourism & Hospitality', description: 'Hotels, travel agencies, tour operators and related services' },
      { id: 14, name: 'Health & Wellness', description: 'Medical services, fitness and wellness providers' },
      { id: 15, name: 'Education & Training', description: 'Schools, universities, training institutions and educational services' },
      { id: 16, name: 'Retail & Wholesale', description: 'Sale of goods to consumers or other businesses' },
      { id: 17, name: 'Energy & Utilities', description: 'Electricity, gas, water and waste management services' },
      { id: 18, name: 'Creative Industries', description: 'Media, entertainment, design and arts services' },
      { id: 19, name: 'Real Estate', description: 'Property sales, rentals and management services' }
    ];

    // Insert categories
    for (const category of categoriesData) {
      await prisma.category.upsert({
        where: { id: category.id },
        update: category,
        create: category
      });
    }
    console.log('✅ Inserted categories');

    // Subcategories data
    const subcategoriesData = [
      // Agriculture (6)
      { id: 7, name: 'Crop Farming', categoryId: 6 },
      { id: 8, name: 'Livestock Farming', categoryId: 6 },
      { id: 9, name: 'Aquaculture', categoryId: 6 },
      { id: 10, name: 'Forestry', categoryId: 6 },
      { id: 11, name: 'Agricultural Services', categoryId: 6 },

      // Food & Beverage (7)
      { id: 12, name: 'Restaurants', categoryId: 7 },
      { id: 13, name: 'Cafes & Coffee Shops', categoryId: 7 },
      { id: 14, name: 'Fast Food', categoryId: 7 },
      { id: 15, name: 'Food Production', categoryId: 7 },
      { id: 16, name: 'Bakeries', categoryId: 7 },
      { id: 17, name: 'Bars & Pubs', categoryId: 7 },

      // Manufacturing (8)
      { id: 18, name: 'Textiles', categoryId: 8 },
      { id: 19, name: 'Electronics', categoryId: 8 },
      { id: 20, name: 'Automotive Parts', categoryId: 8 },
      { id: 21, name: 'Furniture', categoryId: 8 },
      { id: 22, name: 'Chemicals', categoryId: 8 },
      { id: 23, name: 'Machinery', categoryId: 8 },

      // Construction (9)
      { id: 24, name: 'Residential Building', categoryId: 9 },
      { id: 25, name: 'Commercial Construction', categoryId: 9 },
      { id: 26, name: 'Infrastructure', categoryId: 9 },
      { id: 27, name: 'Renovation', categoryId: 9 },
      { id: 28, name: 'Specialized Contractors', categoryId: 9 },

      // Transport & Logistics (10)
      { id: 29, name: 'Freight Services', categoryId: 10 },
      { id: 30, name: 'Courier & Delivery', categoryId: 10 },
      { id: 31, name: 'Passenger Transport', categoryId: 10 },
      { id: 32, name: 'Warehousing', categoryId: 10 },
      { id: 33, name: 'Logistics Solutions', categoryId: 10 },

      // Technology (11)
      { id: 34, name: 'Software Development', categoryId: 11 },
      { id: 35, name: 'IT Consulting', categoryId: 11 },
      { id: 36, name: 'Web Services', categoryId: 11 },
      { id: 37, name: 'Mobile Apps', categoryId: 11 },
      { id: 38, name: 'Cybersecurity', categoryId: 11 },
      { id: 39, name: 'Data Services', categoryId: 11 },

      // Professional Services (12)
      { id: 40, name: 'Legal Services', categoryId: 12 },
      { id: 41, name: 'Accounting & Tax', categoryId: 12 },
      { id: 42, name: 'Consulting', categoryId: 12 },
      { id: 43, name: 'Marketing & Advertising', categoryId: 12 },
      { id: 44, name: 'HR Services', categoryId: 12 },
      { id: 45, name: 'Architecture & Engineering', categoryId: 12 },

      // Tourism & Hospitality (13)
      { id: 46, name: 'Hotels & Accommodation', categoryId: 13 },
      { id: 47, name: 'Travel Agencies', categoryId: 13 },
      { id: 48, name: 'Tour Operators', categoryId: 13 },
      { id: 49, name: 'Event Planning', categoryId: 13 },
      { id: 50, name: 'Cruise Services', categoryId: 13 },

      // Health & Wellness (14)
      { id: 51, name: 'Medical Clinics', categoryId: 14 },
      { id: 52, name: 'Dental Services', categoryId: 14 },
      { id: 53, name: 'Fitness Centers', categoryId: 14 },
      { id: 54, name: 'Spas & Wellness', categoryId: 14 },
      { id: 55, name: 'Mental Health Services', categoryId: 14 },

      // Education & Training (15)
      { id: 56, name: 'Schools', categoryId: 15 },
      { id: 57, name: 'Universities', categoryId: 15 },
      { id: 58, name: 'Vocational Training', categoryId: 15 },
      { id: 59, name: 'Online Learning', categoryId: 15 },
      { id: 60, name: 'Tutoring Services', categoryId: 15 },

      // Retail & Wholesale (16)
      { id: 61, name: 'Clothing Stores', categoryId: 16 },
      { id: 62, name: 'Electronics Retail', categoryId: 16 },
      { id: 63, name: 'Supermarkets', categoryId: 16 },
      { id: 64, name: 'Specialty Stores', categoryId: 16 },
      { id: 65, name: 'E-commerce', categoryId: 16 },
      { id: 66, name: 'Wholesale Distribution', categoryId: 16 },

      // Energy & Utilities (17)
      { id: 67, name: 'Electricity Providers', categoryId: 17 },
      { id: 68, name: 'Gas Suppliers', categoryId: 17 },
      { id: 69, name: 'Water Services', categoryId: 17 },
      { id: 70, name: 'Renewable Energy', categoryId: 17 },
      { id: 71, name: 'Waste Management', categoryId: 17 },

      // Creative Industries (18)
      { id: 72, name: 'Advertising Agencies', categoryId: 18 },
      { id: 73, name: 'Media Production', categoryId: 18 },
      { id: 74, name: 'Design Studios', categoryId: 18 },
      { id: 75, name: 'Entertainment', categoryId: 18 },
      { id: 76, name: 'Publishing', categoryId: 18 },

      // Real Estate (19)
      { id: 77, name: 'Property Sales', categoryId: 19 },
      { id: 78, name: 'Rental Services', categoryId: 19 },
      { id: 79, name: 'Property Management', categoryId: 19 },
      { id: 80, name: 'Real Estate Agents', categoryId: 19 },
      { id: 81, name: 'Property Development', categoryId: 19 }
    ];

    // Insert subcategories
    for (const subcategory of subcategoriesData) {
      await prisma.subcategory.upsert({
        where: { id: subcategory.id },
        update: subcategory,
        create: subcategory
      });
    }
    console.log('✅ Inserted subcategories');

    // Verify the data
    const categoryCount = await prisma.category.count();
    const subcategoryCount = await prisma.subcategory.count();

    console.log(`\n📊 Database populated successfully!`);
    console.log(`Categories: ${categoryCount}`);
    console.log(`Subcategories: ${subcategoryCount}`);

  } catch (error) {
    console.error('Database population failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

populateDatabase();