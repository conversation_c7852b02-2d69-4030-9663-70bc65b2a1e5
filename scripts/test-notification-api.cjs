const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testNotificationAPI() {
  try {
    console.log('🧪 Testing Notification API with enhanced schema...');

    // Test 1: Fetch all notifications
    console.log('\n1. Testing notification retrieval...');
    const notifications = await prisma.notification.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5,
    });

    console.log(`✅ Found ${notifications.length} notifications`);
    console.log('Sample notification structure:');
    if (notifications.length > 0) {
      const sample = notifications[0];
      console.log(JSON.stringify({
        id: sample.id,
        type: sample.type,
        message: sample.message,
        priority: sample.priority,
        category: sample.category,
        trigger: sample.trigger,
        recipients: sample.recipients,
        recipientTypes: sample.recipientTypes,
        createdAt: sample.createdAt,
      }, null, 2));
    }

    // Test 2: Verify all new fields are present
    console.log('\n2. Testing new schema fields...');
    const fieldTest = await prisma.notification.findFirst({
      select: {
        id: true,
        trigger: true,
        triggeredBy: true,
        priority: true,
        category: true,
        recipientTypes: true,
      },
    });

    if (fieldTest) {
      console.log('✅ All new fields are accessible:');
      console.log(`   - trigger: ${fieldTest.trigger || 'null'}`);
      console.log(`   - triggeredBy: ${fieldTest.triggeredBy || 'null'}`);
      console.log(`   - priority: ${fieldTest.priority}`);
      console.log(`   - category: ${fieldTest.category}`);
      console.log(`   - recipientTypes: ${fieldTest.recipientTypes ? 'present' : 'null'}`);
    }

    // Test 3: Verify field constraints
    console.log('\n3. Testing field constraints...');
    const priorityValues = await prisma.notification.findMany({
      select: { priority: true },
      distinct: ['priority'],
    });

    const categoryValues = await prisma.notification.findMany({
      select: { category: true },
      distinct: ['category'],
    });

    console.log('✅ Priority values found:', priorityValues.map(n => n.priority));
    console.log('✅ Category values found:', categoryValues.map(n => n.category));

    // Test 4: Verify recipientTypes structure
    console.log('\n4. Testing recipientTypes structure...');
    const notificationsWithRecipientTypes = await prisma.notification.findMany({
      where: {
        recipientTypes: {
          not: null,
        },
      },
      select: {
        id: true,
        recipients: true,
        recipientTypes: true,
      },
      take: 3,
    });

    if (notificationsWithRecipientTypes.length > 0) {
      console.log('✅ Found notifications with recipientTypes:');
      notificationsWithRecipientTypes.forEach(n => {
        console.log(`   - ID: ${n.id}`);
        console.log(`   - Recipients: ${JSON.stringify(n.recipients)}`);
        console.log(`   - RecipientTypes: ${JSON.stringify(n.recipientTypes)}`);
      });
    } else {
      console.log('ℹ️  No notifications with recipientTypes found (expected for existing data)');
    }

    console.log('\n🎉 All tests passed! Notification API is working correctly with enhanced schema.');

  } catch (error) {
    console.error('❌ Error testing notification API:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testNotificationAPI();