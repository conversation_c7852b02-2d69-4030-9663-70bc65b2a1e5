const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testNotificationManagement() {
  try {
    console.log('🧪 Testing Notification Management Interface...');

    // Test 1: Create test notifications for management interface
    console.log('\n1. Creating test notifications for management interface...');

    const testNotifications = [
      {
        type: 'USER_CREATED',
        message: 'New user registration: <EMAIL>',
        recipients: ['admin'],
        trigger: 'user_registration',
        triggeredBy: 'system',
        priority: 'HIGH',
        category: 'USER_MANAGEMENT',
        recipientTypes: { roles: ['admin'] },
        data: { userId: 'user123', userEmail: '<EMAIL>' },
      },
      {
        type: 'TREATY_EXPIRED',
        message: 'Peace Treaty Alpha has expired and requires renewal',
        recipients: ['admin', 'treaty_manager'],
        trigger: 'treaty_expiry',
        triggeredBy: 'system',
        priority: 'HIGH',
        category: 'TREATY',
        recipientTypes: { roles: ['admin'], users: ['treaty_manager'] },
        data: { treatyId: 'treaty1234', treatyName: 'Peace Treaty Alpha' },
      },
      {
        type: 'FAILED_LOGIN_ATTEMPT',
        message: 'Multiple failed login attempts detected from IP *************',
        recipients: ['admin'],
        trigger: 'security_monitoring',
        triggeredBy: 'system',
        priority: 'HIGH',
        category: 'SECURITY',
        recipientTypes: { roles: ['admin'] },
        data: { ipAddress: '*************', attemptCount: 5 },
      },
      {
        type: 'BACKUP_COMPLETED',
        message: 'Daily system backup completed successfully',
        recipients: ['admin'],
        trigger: 'backup_process',
        triggeredBy: 'system',
        priority: 'LOW',
        category: 'SYSTEM',
        recipientTypes: { roles: ['admin'] },
        data: { backupSize: '2.5GB', duration: '45 minutes' },
      },
      {
        type: 'SERVER_CONNECTION_FAILED',
        message: 'Failed to connect to remote server "NWA Promote Local"',
        recipients: ['admin'],
        trigger: 'server_connection_check',
        triggeredBy: 'system',
        priority: 'NORMAL',
        category: 'REMOTE_SERVER',
        recipientTypes: { roles: ['admin'] },
        data: { serverName: 'NWA Promote Local', error: 'Connection timeout' },
      },
    ];

    const createdNotifications = [];
    for (const notificationData of testNotifications) {
      const notification = await prisma.notification.create({
        data: notificationData,
      });
      createdNotifications.push(notification);
      console.log(`✅ Created: ${notification.type} (${notification.priority} ${notification.category})`);
    }

    // Test 2: Test notification retrieval with filters
    console.log('\n2. Testing notification retrieval with filters...');

    // Get all notifications
    const allNotifications = await prisma.notification.findMany({
      orderBy: { createdAt: 'desc' },
    });
    console.log(`✅ Retrieved ${allNotifications.length} notifications`);

    // Test priority filter
    const highPriorityNotifications = await prisma.notification.findMany({
      where: { priority: 'HIGH' },
    });
    console.log(`✅ High priority notifications: ${highPriorityNotifications.length}`);

    // Test category filter
    const securityNotifications = await prisma.notification.findMany({
      where: { category: 'SECURITY' },
    });
    console.log(`✅ Security notifications: ${securityNotifications.length}`);

    // Test search functionality
    const userNotifications = await prisma.notification.findMany({
      where: {
        OR: [
          { type: { contains: 'USER', mode: 'insensitive' } },
          { message: { contains: 'user', mode: 'insensitive' } },
        ],
      },
    });
    console.log(`✅ User-related notifications: ${userNotifications.length}`);

    // Test 3: Test statistics calculation
    console.log('\n3. Testing notification statistics...');

    const stats = await prisma.notification.groupBy({
      by: ['priority', 'category'],
      _count: {
        id: true,
      },
    });

    console.log('✅ Notification statistics:');
    stats.forEach(stat => {
      console.log(`   - ${stat.priority} ${stat.category}: ${stat._count.id} notifications`);
    });

    // Test 4: Test pagination
    console.log('\n4. Testing pagination...');

    const page1 = await prisma.notification.findMany({
      orderBy: { createdAt: 'desc' },
      take: 3,
    });

    const page2 = await prisma.notification.findMany({
      orderBy: { createdAt: 'desc' },
      skip: 3,
      take: 3,
    });

    console.log(`✅ Page 1: ${page1.length} notifications`);
    console.log(`✅ Page 2: ${page2.length} notifications`);

    // Test 5: Test date range filtering
    console.log('\n5. Testing date range filtering...');

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayNotifications = await prisma.notification.findMany({
      where: {
        createdAt: {
          gte: today,
        },
      },
    });

    console.log(`✅ Notifications from today: ${todayNotifications.length}`);

    // Test 6: Test read/unread status
    console.log('\n6. Testing read/unread status...');

    const unreadNotifications = await prisma.notification.findMany({
      where: {
        readBy: { isEmpty: true },
      },
    });

    const readNotifications = await prisma.notification.findMany({
      where: {
        readBy: { isEmpty: false },
      },
    });

    console.log(`✅ Unread notifications: ${unreadNotifications.length}`);
    console.log(`✅ Read notifications: ${readNotifications.length}`);

    // Test 7: Verify enhanced schema fields
    console.log('\n7. Testing enhanced schema fields...');

    const sampleNotification = await prisma.notification.findFirst({
      orderBy: { createdAt: 'desc' },
    });

    if (sampleNotification) {
      console.log('✅ Enhanced notification structure:');
      console.log(`   - ID: ${sampleNotification.id}`);
      console.log(`   - Type: ${sampleNotification.type}`);
      console.log(`   - Priority: ${sampleNotification.priority}`);
      console.log(`   - Category: ${sampleNotification.category}`);
      console.log(`   - Trigger: ${sampleNotification.trigger}`);
      console.log(`   - Triggered By: ${sampleNotification.triggeredBy}`);
      console.log(`   - Recipients: ${JSON.stringify(sampleNotification.recipients)}`);
      console.log(`   - Recipient Types: ${JSON.stringify(sampleNotification.recipientTypes)}`);
      console.log(`   - Data: ${JSON.stringify(sampleNotification.data)}`);
    }

    console.log('\n🎉 All notification management interface tests passed!');
    console.log(`📊 Total notifications in system: ${allNotifications.length}`);

    // Summary
    console.log('\n📋 Summary:');
    console.log(`   - Created: ${createdNotifications.length} test notifications`);
    console.log(`   - High Priority: ${highPriorityNotifications.length}`);
    console.log(`   - Security: ${securityNotifications.length}`);
    console.log(`   - User-related: ${userNotifications.length}`);
    console.log(`   - Unread: ${unreadNotifications.length}`);
    console.log(`   - Read: ${readNotifications.length}`);

  } catch (error) {
    console.error('❌ Error testing notification management:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testNotificationManagement();