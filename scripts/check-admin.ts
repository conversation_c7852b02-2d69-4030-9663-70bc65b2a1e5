import { PrismaClient } from '@prisma/client';

async function checkAdminUser() {
  const prisma = new PrismaClient();
  
  try {
    const user = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>'
      }
    });
    
    if (user) {
      console.log('Admin user exists');
    } else {
      console.log('Admin user does not exist');
    }
  } catch (error) {
    console.error('Error checking admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdminUser();