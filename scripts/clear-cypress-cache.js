#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import os from 'os';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧹 Clearing Cypress cache data (keeping binary intact)...');

// Get the Cypress cache directory
const cypressCacheDir = path.join(os.homedir(), '.cache', 'Cypress');

if (!fs.existsSync(cypressCacheDir)) {
    console.log('✅ No Cypress cache directory found - nothing to clear');
    process.exit(0);
}

// Find all version directories
const versionDirs = fs.readdirSync(cypressCacheDir)
    .filter(dir => fs.statSync(path.join(cypressCacheDir, dir)).isDirectory())
    .map(dir => path.join(cypressCacheDir, dir));

let clearedCount = 0;

versionDirs.forEach(versionDir => {
    const cypressDir = path.join(versionDir, 'Cypress');

    if (fs.existsSync(cypressDir)) {
        // Clear only cache data, not the binary
        const cacheDir = path.join(cypressDir, 'Cache');
        const indexedDBDir = path.join(cypressDir, 'IndexedDB');
        const gpuCacheDir = path.join(cypressDir, 'GPUCache');
        const localStorageDir = path.join(cypressDir, 'Local Storage');

        [cacheDir, indexedDBDir, gpuCacheDir, localStorageDir].forEach(dir => {
            if (fs.existsSync(dir)) {
                console.log(`  🗑️  Removing: ${path.relative(cypressCacheDir, dir)}`);
                try {
                    fs.rmSync(dir, { recursive: true, force: true });
                    clearedCount++;
                } catch (err) {
                    console.error(`Error removing ${dir}:`, err.message);
                }
            }
        });
    }
});

if (clearedCount === 0) {
    console.log('✅ No cache data found to clear');
} else {
    console.log(`✅ Cleared ${clearedCount} cache directories`);
    console.log('🚀 Cypress binary is intact - ready to run tests!');
}