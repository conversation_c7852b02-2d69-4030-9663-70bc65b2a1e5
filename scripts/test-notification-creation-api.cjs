const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testNotificationCreationAPI() {
  try {
    console.log('🧪 Testing Notification Creation API...');

    // Test 1: Create a basic notification
    console.log('\n1. Testing basic notification creation...');
    const basicNotification = await prisma.notification.create({
      data: {
        type: 'TEST_NOTIFICATION',
        message: 'This is a test notification created via API',
        recipients: ['admin'],
        trigger: 'api_test',
        triggeredBy: 'system', // Using 'system' as a placeholder since we don't have a real user ID
        priority: 'NORMAL',
        category: 'SYSTEM',
        recipientTypes: {
          roles: ['admin'],
        },
      },
    });

    console.log('✅ Basic notification created successfully:');
    console.log(`   - ID: ${basicNotification.id}`);
    console.log(`   - Type: ${basicNotification.type}`);
    console.log(`   - Priority: ${basicNotification.priority}`);
    console.log(`   - Category: ${basicNotification.category}`);
    console.log(`   - Trigger: ${basicNotification.trigger}`);

    // Test 2: Create a high-priority user management notification
    console.log('\n2. Testing high-priority user management notification...');
    const userNotification = await prisma.notification.create({
      data: {
        type: 'USER_CREATED',
        message: 'New user has been registered and requires approval',
        recipients: ['admin'],
        trigger: 'user_registration',
        triggeredBy: 'system',
        priority: 'HIGH',
        category: 'USER_MANAGEMENT',
        recipientTypes: {
          roles: ['admin'],
        },
        data: {
          userId: 'user123',
          userEmail: '<EMAIL>',
          registrationDate: new Date().toISOString(),
        },
      },
    });

    console.log('✅ User management notification created:');
    console.log(`   - Priority: ${userNotification.priority}`);
    console.log(`   - Category: ${userNotification.category}`);
    console.log(`   - Data: ${JSON.stringify(userNotification.data)}`);

    // Test 3: Create a treaty-related notification
    console.log('\n3. Testing treaty notification...');
    const treatyNotification = await prisma.notification.create({
      data: {
        type: 'TREATY_EXPIRING',
        message: 'Peace Treaty #1234 is expiring in 7 days',
        recipients: ['admin', 'treaty_manager'],
        trigger: 'treaty_expiry_check',
        triggeredBy: 'system',
        priority: 'NORMAL',
        category: 'TREATY',
        recipientTypes: {
          roles: ['admin'],
          users: ['treaty_manager'],
        },
        data: {
          treatyId: 'treaty1234',
          treatyName: 'Peace Treaty Alpha',
          expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          daysUntilExpiry: 7,
        },
      },
    });

    console.log('✅ Treaty notification created:');
    console.log(`   - Recipients: ${JSON.stringify(treatyNotification.recipients)}`);
    console.log(`   - RecipientTypes: ${JSON.stringify(treatyNotification.recipientTypes)}`);

    // Test 4: Create a security notification
    console.log('\n4. Testing security notification...');
    const securityNotification = await prisma.notification.create({
      data: {
        type: 'FAILED_LOGIN_ATTEMPT',
        message: 'Multiple failed login attempts detected for user admin',
        recipients: ['admin'],
        trigger: 'security_monitoring',
        triggeredBy: 'system',
        priority: 'HIGH',
        category: 'SECURITY',
        recipientTypes: {
          roles: ['admin'],
        },
        data: {
          ipAddress: '*************',
          attemptCount: 5,
          timeWindow: 'last_10_minutes',
          userAgent: 'Mozilla/5.0...',
        },
      },
    });

    console.log('✅ Security notification created:');
    console.log(`   - Priority: ${securityNotification.priority}`);
    console.log(`   - Category: ${securityNotification.category}`);

    // Test 5: Verify audit logging
    console.log('\n5. Testing audit logging...');
    const auditLogs = await prisma.auditLog.findMany({
      where: {
        action: 'NOTIFICATION_CREATED',
        resourceId: {
          in: [basicNotification.id, userNotification.id, treatyNotification.id, securityNotification.id],
        },
      },
      orderBy: { timestamp: 'desc' },
    });

    console.log(`✅ Found ${auditLogs.length} audit log entries for notification creation`);
    if (auditLogs.length > 0) {
      console.log('Sample audit log:');
      console.log(`   - Action: ${auditLogs[0].action}`);
      console.log(`   - Resource: ${auditLogs[0].resource}`);
      console.log(`   - User ID: ${auditLogs[0].userId}`);
      console.log(`   - Success: ${auditLogs[0].success}`);
    }

    // Test 6: Verify notification statistics
    console.log('\n6. Testing notification statistics...');
    const stats = await prisma.notification.groupBy({
      by: ['priority', 'category'],
      _count: {
        id: true,
      },
      orderBy: {
        priority: 'asc',
      },
    });

    console.log('✅ Notification statistics by priority and category:');
    stats.forEach(stat => {
      console.log(`   - ${stat.priority} ${stat.category}: ${stat._count.id} notifications`);
    });

    // Test 7: Test notification retrieval with new fields
    console.log('\n7. Testing notification retrieval with enhanced fields...');
    const allNotifications = await prisma.notification.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5,
    });

    console.log(`✅ Retrieved ${allNotifications.length} notifications with enhanced schema`);
    console.log('Sample enhanced notification:');
    const sample = allNotifications[0];
    console.log(JSON.stringify({
      id: sample.id,
      type: sample.type,
      message: sample.message,
      priority: sample.priority,
      category: sample.category,
      trigger: sample.trigger,
      triggeredBy: sample.triggeredBy,
      recipients: sample.recipients,
      recipientTypes: sample.recipientTypes,
      createdAt: sample.createdAt,
    }, null, 2));

    console.log('\n🎉 All notification creation API tests passed!');
    console.log(`📊 Total notifications in database: ${allNotifications.length}`);

  } catch (error) {
    console.error('❌ Error testing notification creation API:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testNotificationCreationAPI();