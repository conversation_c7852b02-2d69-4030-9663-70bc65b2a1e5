#!/usr/bin/env node

/**
 * Test script for automatic permission fetching workflow
 *
 * This script tests the complete workflow:
 * 1. Health check service
 * 2. Permission sync service
 * 3. Notification service
 * 4. UI integration
 */

import { PrismaClient } from '@prisma/client'
import { RemoteServerHealthService } from '../src/lib/services/remote-server-health-service.js'
import { PermissionSyncService } from '../src/lib/services/permission-sync-service.js'
import { RemoteServerNotificationService } from '../src/lib/services/remote-server-notification-service.js'

const prisma = new PrismaClient()

async function testAutomaticPermissionFetching() {
  console.log('🧪 Testing Automatic Permission Fetching Workflow')
  console.log('=' .repeat(50))

  try {
    // 1. Test Health Check Service
    console.log('\n1. Testing Health Check Service...')
    const healthService = new RemoteServerHealthService()

    // Get all active servers
    const servers = await prisma.remoteServer.findMany({
      where: { isActive: true }
    })

    if (servers.length === 0) {
      console.log('⚠️  No active remote servers found. Creating a test server...')

      // Create a test server for demonstration
      const testServer = await prisma.remoteServer.create({
        data: {
          name: 'Test Server',
          url: 'https://httpbin.org', // Using httpbin for testing
          apiKey: 'test-api-key',
          isActive: true,
          description: 'Test server for automatic permission fetching'
        }
      })

      servers.push(testServer)
      console.log(`✅ Created test server: ${testServer.name}`)
    }

    // Test health check on each server
    for (const server of servers) {
      console.log(`\n   Checking health for: ${server.name}`)
      const healthResult = await healthService.checkServerHealth(server.id)
      console.log(`   Status: ${healthResult.status}`)
      console.log(`   Response Time: ${healthResult.responseTime}ms`)

      if (healthResult.error) {
        console.log(`   Error: ${healthResult.error}`)
      }
    }

    // 2. Test Permission Sync Service
    console.log('\n2. Testing Permission Sync Service...')
    const syncService = new PermissionSyncService()

    for (const server of servers) {
      console.log(`\n   Syncing permissions for: ${server.name}`)
      const syncResult = await syncService.syncServerPermissions(server.id)

      console.log(`   Success: ${syncResult.success}`)
      console.log(`   Permissions Synced: ${syncResult.permissionsSynced}`)
      console.log(`   Roles Synced: ${syncResult.rolesSynced}`)

      if (syncResult.errors.length > 0) {
        console.log(`   Errors: ${syncResult.errors.join(', ')}`)
      }
    }

    // 3. Test Notification Service
    console.log('\n3. Testing Notification Service...')
    const notificationService = new RemoteServerNotificationService()

    // Test permission fetch failure notification
    console.log('\n   Testing permission fetch failure notification...')
    const failureResult = await notificationService.notifyPermissionFetchFailure({
      serverId: servers[0].id,
      serverName: servers[0].name,
      error: 'Test error: Unable to connect to remote server',
      timestamp: new Date().toISOString(),
      context: 'test'
    })

    console.log(`   Notification created: ${failureResult.success}`)

    // Test health check failure notification
    console.log('\n   Testing health check failure notification...')
    const healthFailureResult = await notificationService.notifyHealthCheckFailure({
      serverId: servers[0].id,
      serverName: servers[0].name,
      status: 'offline',
      error: 'Test error: Server is not responding',
      responseTime: 5000,
      timestamp: new Date().toISOString()
    })

    console.log(`   Notification created: ${healthFailureResult.success}`)

    // Test permission sync success notification
    console.log('\n   Testing permission sync success notification...')
    const successResult = await notificationService.notifyPermissionSyncSuccess(
      servers[0].id,
      servers[0].name,
      15 // Test with 15 permissions
    )

    console.log(`   Notification created: ${successResult.success}`)

    // 4. Test Database Integration
    console.log('\n4. Testing Database Integration...')

    // Check if permissions were stored
    const permissions = await prisma.remote_server_permissions.findMany({
      where: { remote_server_id: servers[0].id }
    })

    console.log(`   Permissions stored in database: ${permissions.length}`)

    // Check if notifications were created
    const notifications = await prisma.notification.findMany({
      where: {
        data: {
          path: ['serverId'],
          equals: servers[0].id
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })

    console.log(`   Notifications created: ${notifications.length}`)
    notifications.forEach((notification, index) => {
      console.log(`     ${index + 1}. ${notification.type}: ${notification.message}`)
    })

    console.log('\n✅ All tests completed successfully!')
    console.log('\n📊 Summary:')
    console.log(`   - Servers tested: ${servers.length}`)
    console.log(`   - Permissions in database: ${permissions.length}`)
    console.log(`   - Notifications created: ${notifications.length}`)
    console.log('\n🎉 Automatic permission fetching workflow is working correctly!')

  } catch (error) {
    console.error('\n❌ Test failed:', error)
    console.error(error.stack)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the test if this script is executed directly
testAutomaticPermissionFetching()
  .then(() => {
    console.log('\n✨ Test script completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Test script failed:', error)
    process.exit(1)
  })

export { testAutomaticPermissionFetching }