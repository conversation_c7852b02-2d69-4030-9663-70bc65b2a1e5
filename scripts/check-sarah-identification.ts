import { PrismaClient } from '@prisma/client';

async function checkSarahIdentification() {
  const prisma = new PrismaClient();

  try {
    console.log('🔍 Checking identification data for <PERSON>...');

    // First find <PERSON> user
    const sarahUser = await prisma.user.findFirst({
      where: {
        OR: [
          { name: { contains: '<PERSON>', mode: 'insensitive' } },
          { name: { contains: '<PERSON>', mode: 'insensitive' } }
        ]
      },
      include: {
        profile: {
          include: {
            country: true,
            city: true,
            title: true
          }
        },
        userIdentifications: {
          include: {
            identificationType: true
          }
        },
        userTreatyNumbers: {
          include: {
            treatyType: true
          }
        },
        userPositions: {
          include: {
            position: true
          }
        },
        nationTreatyMemberships: {
          include: {
            nationTreaty: true
          }
        },
        nationTreatyEnvoys: {
          include: {
            nationTreaty: true
          }
        }
      }
    });

    if (!sarahUser) {
      console.log('❌ <PERSON> Jones not found in database');
      return;
    }

    console.log(`✅ Found <PERSON> (ID: ${sarahUser.id})`);
    console.log(`   Name: ${sarahUser.name}`);
    console.log(`   Email: ${sarahUser.email}`);

    // 1. Check User Profile (Peace Ambassador Number)
    console.log('\n📋 USER PROFILE DATA:');
    if (sarahUser.profile) {
      console.log(`   Peace Ambassador Number: ${sarahUser.profile.peaceAmbassadorNumber || 'NULL'}`);
      console.log(`   Title: ${sarahUser.profile.title?.name || 'NULL'}`);
      console.log(`   Country: ${sarahUser.profile.country?.name || 'NULL'}`);
      console.log(`   City: ${sarahUser.profile.city?.name || 'NULL'}`);
    } else {
      console.log('   No profile data found');
    }

    // 2. Check User Identifications
    console.log('\n🆔 USER IDENTIFICATIONS:');
    if (sarahUser.userIdentifications.length > 0) {
      sarahUser.userIdentifications.forEach((identification, index) => {
        console.log(`   ${index + 1}. ${identification.identificationType.name}: ${identification.idNumber}`);
      });
    } else {
      console.log('   No identification records found');
    }

    // 3. Check User Treaty Numbers
    console.log('\n📜 USER TREATY NUMBERS:');
    if (sarahUser.userTreatyNumbers.length > 0) {
      sarahUser.userTreatyNumbers.forEach((treatyNumber, index) => {
        console.log(`   ${index + 1}. ${treatyNumber.treatyType.name}: ${treatyNumber.treatyNumber}`);
      });
    } else {
      console.log('   No treaty numbers found');
    }

    // 4. Check User Positions
    console.log('\n👤 USER POSITIONS:');
    if (sarahUser.userPositions.length > 0) {
      sarahUser.userPositions.forEach((position, index) => {
        console.log(`   ${index + 1}. ${position.position.title} (${position.isActive ? 'Active' : 'Inactive'})`);
      });
    } else {
      console.log('   No position records found');
    }

    // 5. Check Nation Treaty Memberships
    console.log('\n🌍 NATION TREATY MEMBERSHIPS:');
    if (sarahUser.nationTreatyMemberships.length > 0) {
      sarahUser.nationTreatyMemberships.forEach((membership, index) => {
        console.log(`   ${index + 1}. ${membership.nationTreaty.name} - Role: ${membership.role} (${membership.status})`);
      });
    } else {
      console.log('   No nation treaty memberships found');
    }

    // 6. Check Nation Treaty Envoys
    console.log('\n🕊️ NATION TREATY ENVOYS:');
    if (sarahUser.nationTreatyEnvoys.length > 0) {
      sarahUser.nationTreatyEnvoys.forEach((envoy, index) => {
        console.log(`   ${index + 1}. ${envoy.nationTreaty.name} - Type: ${envoy.envoyType} - Title: ${envoy.title || 'N/A'} (${envoy.status})`);
      });
    } else {
      console.log('   No envoy records found');
    }

    // Summary
    console.log('\n📊 SUMMARY:');
    console.log(`   User Identifications: ${sarahUser.userIdentifications.length}`);
    console.log(`   Treaty Numbers: ${sarahUser.userTreatyNumbers.length}`);
    console.log(`   Positions: ${sarahUser.userPositions.length}`);
    console.log(`   Nation Treaty Memberships: ${sarahUser.nationTreatyMemberships.length}`);
    console.log(`   Envoy Roles: ${sarahUser.nationTreatyEnvoys.length}`);

  } catch (error) {
    console.error('❌ Error checking Sarah Jones identification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSarahIdentification();