#!/usr/bin/env node
import { spawn } from 'node:child_process';

const browser = process.env.CYPRESS_BROWSER && process.env.CYPRESS_BROWSER.trim()
  ? process.env.CYPRESS_BROWSER.trim()
  : 'chromium';

const spec = process.env.CYPRESS_SPEC && process.env.CYPRESS_SPEC.trim()
  ? process.env.CYPRESS_SPEC.trim()
  : undefined;

const args = ['cypress', 'run', '--browser', browser];

if (spec) {
  args.push('--spec', spec);
}

console.log(`[run-cypress] Executing: npx ${args.join(' ')}`);

const child = spawn('npx', ['--node-arg=--require=ts-node/register', ...args], {
  stdio: 'inherit',
  shell: true,
});

child.on('exit', (code) => {
  process.exit(code ?? 1);
});

child.on('error', (error) => {
  console.error('[run-cypress] Failed to launch Cypress:', error);
  process.exit(1);
});
