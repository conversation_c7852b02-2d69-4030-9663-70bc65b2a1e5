import { prisma } from '../src/lib/prisma'

async function main() {
  try {
    const servers = await prisma.remoteServer.findMany({ select: { id: true, name: true, apiKey: true, allowedOrigins: true, isActive: true } });
    console.log(JSON.stringify(servers, null, 2));
  } catch (err: any) {
    console.error('Error querying remote servers:', err.message || err);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
