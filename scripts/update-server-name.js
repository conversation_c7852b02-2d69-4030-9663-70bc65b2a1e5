const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateServerName() {
  try {
    // First, let's find the server
    const server = await prisma.remote_servers.findFirst({
      where: { name: 'NWA Promote Local' }
    });

    if (!server) {
      console.log('Server "NWA Promote Local" not found');
      return;
    }

    console.log('Found server:', server.name, 'with ID:', server.id);

    // Update the server name
    const updatedServer = await prisma.remote_servers.update({
      where: { id: server.id },
      data: { name: 'NWA Member Portal' }
    });

    console.log('Successfully updated server name to:', updatedServer.name);
  } catch (error) {
    console.error('Error updating server name:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateServerName();