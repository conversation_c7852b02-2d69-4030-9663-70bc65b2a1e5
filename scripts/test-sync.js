#!/usr/bin/env node

import { PermissionSyncService } from '../src/lib/services/permission-sync-service.ts'

async function testSync() {
  console.log('🧪 Testing Permission Sync Service...')

  try {
    const syncService = new PermissionSyncService()

    // Test sync for the NWA Promote Local server
    const serverId = 'cmfudg3wz003mppcb9txnz9at'
    console.log(`\nSyncing permissions for server: ${serverId}`)

    const result = await syncService.syncServerPermissions(serverId)

    console.log('Sync Result:')
    console.log(`- Success: ${result.success}`)
    console.log(`- Server Name: ${result.serverName}`)
    console.log(`- Permissions Synced: ${result.permissionsSynced}`)
    console.log(`- Roles Synced: ${result.rolesSynced}`)
    console.log(`- Available Permissions Synced: ${result.availablePermissionsSynced}`)

    if (result.errors.length > 0) {
      console.log(`- Errors: ${result.errors.join(', ')}`)
    }

    if (result.success) {
      console.log('\n✅ Sync completed successfully!')
    } else {
      console.log('\n❌ Sync failed!')
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
    console.error(error.stack)
  }
}

testSync().catch(console.error)