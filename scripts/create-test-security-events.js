import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestSecurityEvents() {
  console.log('Creating test security events...');

  // Create test security events
  const events = [
    {
      type: 'AUTHENTICATION_FAILURE',
      severity: 'MEDIUM',
      description: 'Multiple failed login attempts detected',
      source: 'AUTH_SYSTEM',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      metadata: {
        attempts: 3,
        timeframe: '5 minutes'
      },
      status: 'OPEN'
    },
    {
      type: 'BRUTE_FORCE_ATTACK',
      severity: 'HIGH',
      description: 'Brute force attack detected from IP address',
      source: 'AUDIT_LOG',
      ipAddress: '*********',
      userAgent: 'Custom Scanner/1.0',
      metadata: {
        attempts: 15,
        timeframe: '2 minutes'
      },
      status: 'INVESTIGATING'
    },
    {
      type: 'SUSPICIOUS_ACTIVITY',
      severity: 'LOW',
      description: 'Unusual access pattern detected',
      source: 'AUDIT_LOG',
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0 (compatible; Googlebot/2.1)',
      metadata: {
        pattern: 'rapid_api_calls',
        endpoint: '/api/users'
      },
      status: 'RESOLVED'
    },
    {
      type: 'RATE_LIMIT_EXCEEDED',
      severity: 'MEDIUM',
      description: 'API rate limit exceeded',
      source: 'RATE_LIMITER',
      ipAddress: '************',
      userAgent: 'Python Requests/2.28.1',
      metadata: {
        limit: 100,
        window: '1 hour',
        requests: 150
      },
      status: 'OPEN'
    },
    {
      type: 'PRIVILEGE_ESCALATION',
      severity: 'CRITICAL',
      description: 'Attempted privilege escalation detected',
      source: 'AUTHORIZATION',
      ipAddress: '*********',
      userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
      metadata: {
        attemptedResource: '/admin/users',
        userRole: 'USER',
        requiredRole: 'ADMIN'
      },
      status: 'OPEN'
    }
  ];

  for (const eventData of events) {
    await prisma.securityEvent.create({
      data: {
        ...eventData,
        detectedAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000), // Random time within last 24 hours
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
  }

  // Create test security alerts
  const alerts = [
    {
      eventId: 'event-1', // This would be linked to actual events
      ruleId: 'critical_security_alert',
      channels: JSON.stringify([
        { type: 'EMAIL', config: { recipients: ['<EMAIL>'] } }
      ]),
      status: 'SENT',
      metadata: JSON.stringify({
        event: events[0],
        rule: { name: 'Critical Security Events' }
      })
    },
    {
      eventId: 'event-2',
      ruleId: 'high_severity_alert',
      channels: JSON.stringify([
        { type: 'EMAIL', config: { recipients: ['<EMAIL>'] } }
      ]),
      status: 'SENT',
      metadata: JSON.stringify({
        event: events[1],
        rule: { name: 'High Severity Events' }
      })
    },
    {
      eventId: 'event-3',
      ruleId: 'rate_limit_alert',
      channels: JSON.stringify([
        { type: 'EMAIL', config: { recipients: ['<EMAIL>'] } }
      ]),
      status: 'FAILED',
      errorMessage: 'SMTP server connection failed',
      metadata: JSON.stringify({
        event: events[3],
        rule: { name: 'Rate Limit Alert' }
      })
    }
  ];

  for (const alertData of alerts) {
    await prisma.securityAlert.create({
      data: {
        ...alertData,
        sentAt: new Date(Date.now() - Math.random() * 12 * 60 * 60 * 1000), // Random time within last 12 hours
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
  }

  console.log('✅ Created test security events and alerts');
  console.log(`Created ${events.length} security events`);
  console.log(`Created ${alerts.length} security alerts`);
}

async function main() {
  try {
    await createTestSecurityEvents();
  } catch (error) {
    console.error('Error creating test security events:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();