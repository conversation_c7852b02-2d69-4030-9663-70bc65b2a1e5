const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testTreatyWorkflowNotifications() {
  try {
    console.log('🧪 Testing Treaty Application Workflow Notifications...');

    // Test 1: Create test treaty applications to trigger notifications
    console.log('\n1. Creating test treaty applications...');

    // First, get a test user and treaty type
    const testUser = await prisma.user.findFirst({
      include: { profile: true },
    });

    const testTreatyType = await prisma.treatyType.findFirst();

    if (!testUser || !testTreatyType) {
      console.log('❌ No test user or treaty type found. Creating test data...');

      // Create a test user
      const timestamp = Date.now();
      const newTestUser = await prisma.user.create({
        data: {
          name: `Test Treaty User ${timestamp}`,
          email: `test.treaty.${timestamp}@example.com`,
          profile: {
            create: {
              firstName: 'Test',
              lastName: `Treaty User ${timestamp}`,
              nwaEmail: `test.treaty.${timestamp}@nwa.org`,
              phone: '+1234567890',
              mobile: '+1234567891',
              bio: 'Test user for treaty workflow notifications',
              peaceAmbassadorNumber: `PA${timestamp}`,
            },
          },
        },
      });

      // Create a test treaty type
      const newTestTreatyType = await prisma.treatyType.create({
        data: {
          name: `Test Treaty Type ${timestamp}`,
          description: 'Test treaty type for notification testing',
          category: 'PEACE',
          price: 100.00,
          currency: 'USD',
          requiresPayment: true,
          paymentDeadlineDays: 30,
        },
      });

      console.log('✅ Created test user and treaty type');
      testUser = newTestTreatyType;
      testTreatyType = newTestTreatyType;
    }

    // Create a test treaty application
    const testApplication = await prisma.userTreatyType.create({
      data: {
        userId: testUser.id,
        treatyTypeId: testTreatyType.id,
        status: 'APPLIED',
        appliedAt: new Date(),
      },
      include: {
        treatyType: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    console.log('✅ Test treaty application created:');
    console.log(`   - ID: ${testApplication.id}`);
    console.log(`   - User: ${testApplication.user.name}`);
    console.log(`   - Treaty: ${testApplication.treatyType.name}`);
    console.log(`   - Status: ${testApplication.status}`);

    // Test 2: Check if treaty application creation notification was created
    console.log('\n2. Checking treaty application creation notification...');

    const creationNotifications = await prisma.notification.findMany({
      where: {
        type: 'TREATY_APPLICATION_CREATED',
        data: {
          path: ['applicationId'],
          equals: testApplication.id,
        },
      },
    });

    if (creationNotifications.length > 0) {
      console.log('✅ Treaty application creation notification found:');
      const notification = creationNotifications[0];
      console.log(`   - Message: ${notification.message}`);
      console.log(`   - Priority: ${notification.priority}`);
      console.log(`   - Category: ${notification.category}`);
      console.log(`   - Data: ${JSON.stringify(notification.data)}`);
    } else {
      console.log('ℹ️  No treaty application creation notification found (expected if not running through API)');
    }

    // Test 3: Update the application status to trigger status change notification
    console.log('\n3. Testing treaty application status change notification...');

    const updatedApplication = await prisma.userTreatyType.update({
      where: { id: testApplication.id },
      data: {
        status: 'APPROVED',
        approvedAt: new Date(),
        approvedBy: testUser.id, // Using test user ID as approver for testing
      },
      include: {
        treatyType: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    console.log('✅ Test treaty application approved:');
    console.log(`   - New Status: ${updatedApplication.status}`);
    console.log(`   - Approved At: ${updatedApplication.approvedAt}`);

    // Test 4: Check if treaty application approval notification was created
    console.log('\n4. Checking treaty application approval notification...');

    const approvalNotifications = await prisma.notification.findMany({
      where: {
        type: 'TREATY_APPLICATION_APPROVED',
        data: {
          path: ['applicationId'],
          equals: testApplication.id,
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 1,
    });

    if (approvalNotifications.length > 0) {
      console.log('✅ Treaty application approval notification found:');
      const notification = approvalNotifications[0];
      console.log(`   - Message: ${notification.message}`);
      console.log(`   - Priority: ${notification.priority}`);
      console.log(`   - Category: ${notification.category}`);
      console.log(`   - Status Change: ${notification.data.oldStatus} → ${notification.data.newStatus}`);
    } else {
      console.log('ℹ️  No treaty application approval notification found (expected if not running through API)');
    }

    // Test 5: Test rejection notification
    console.log('\n5. Testing treaty application rejection notification...');

    const rejectedApplication = await prisma.userTreatyType.update({
      where: { id: testApplication.id },
      data: {
        status: 'REJECTED',
        rejectedAt: new Date(),
        rejectedBy: testUser.id,
        rejectionReason: 'Test rejection for notification testing',
      },
      include: {
        treatyType: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    console.log('✅ Test treaty application rejected:');
    console.log(`   - New Status: ${rejectedApplication.status}`);
    console.log(`   - Rejection Reason: ${rejectedApplication.rejectionReason}`);

    // Test 6: Check if treaty application rejection notification was created
    console.log('\n6. Checking treaty application rejection notification...');

    const rejectionNotifications = await prisma.notification.findMany({
      where: {
        type: 'TREATY_APPLICATION_REJECTED',
        data: {
          path: ['applicationId'],
          equals: testApplication.id,
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 1,
    });

    if (rejectionNotifications.length > 0) {
      console.log('✅ Treaty application rejection notification found:');
      const notification = rejectionNotifications[0];
      console.log(`   - Message: ${notification.message}`);
      console.log(`   - Priority: ${notification.priority}`);
      console.log(`   - Category: ${notification.category}`);
      console.log(`   - Rejection Reason: ${notification.data.rejectionReason}`);
    } else {
      console.log('ℹ️  No treaty application rejection notification found (expected if not running through API)');
    }

    // Test 7: Test treaty notification statistics
    console.log('\n7. Testing treaty notification statistics...');

    const treatyNotifications = await prisma.notification.findMany({
      where: {
        category: 'TREATY',
      },
    });

    console.log(`✅ Treaty notifications: ${treatyNotifications.length}`);

    const treatyNotificationStats = await prisma.notification.groupBy({
      by: ['type', 'priority'],
      where: {
        category: 'TREATY',
      },
      _count: {
        id: true,
      },
    });

    console.log('✅ Treaty notification breakdown:');
    treatyNotificationStats.forEach(stat => {
      console.log(`   - ${stat.type} (${stat.priority}): ${stat._count.id} notifications`);
    });

    // Test 8: Test notification search functionality
    console.log('\n8. Testing treaty notification search functionality...');

    const treatySearchResults = await prisma.notification.findMany({
      where: {
        OR: [
          { type: { contains: 'TREATY', mode: 'insensitive' } },
          { message: { contains: 'treaty', mode: 'insensitive' } },
        ],
      },
    });

    console.log(`✅ Treaty-related notifications found: ${treatySearchResults.length}`);

    console.log('\n🎉 All treaty workflow notification tests completed!');
    console.log(`📊 Total treaty notifications: ${treatyNotifications.length}`);

    // Summary
    console.log('\n📋 Summary:');
    console.log(`   - Test application created: ${testApplication.user.name} - ${testApplication.treatyType.name}`);
    console.log(`   - Application approved: ${updatedApplication.status}`);
    console.log(`   - Application rejected: ${rejectedApplication.status}`);
    console.log(`   - Creation notifications: ${creationNotifications.length}`);
    console.log(`   - Approval notifications: ${approvalNotifications.length}`);
    console.log(`   - Rejection notifications: ${rejectionNotifications.length}`);
    console.log(`   - Search results: ${treatySearchResults.length}`);

  } catch (error) {
    console.error('❌ Error testing treaty workflow notifications:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testTreatyWorkflowNotifications();