import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting nation treaties and special envoys seeding...')

  // Create or get the special_envoy role
  const specialEnvoyRole = await prisma.role.upsert({
    where: { name: 'special_envoy' },
    update: {},
    create: {
      name: 'special_envoy',
      description: 'Special Envoy for nation/tribe treaties',
      isSystem: true,
    },
  })

  // Create or get the envoy role
  const envoyR<PERSON> = await prisma.role.upsert({
    where: { name: 'envoy' },
    update: {},
    create: {
      name: 'envoy',
      description: 'Envoy for nation/tribe treaties',
      isSystem: true,
    },
  })

  console.log('✅ Created/updated special envoy roles')

  // Create or get New Zealand for United Tribes of Aotearoa
  const newZealand = await prisma.country.upsert({
    where: { code: 'NZ' },
    update: {},
    create: {
      name: 'New Zealand',
      code: 'NZ',
    },
  })

  // Create or get Australia for the second nation
  const australia = await prisma.country.upsert({
    where: { code: 'AU' },
    update: {},
    create: {
      name: 'Australia',
      code: 'AU',
    },
  })

  console.log('✅ Created/updated countries')

  // Create cities for New Zealand
  const auckland = await prisma.city.upsert({
    where: { 
      name_countryId: {
        name: 'Auckland',
        countryId: newZealand.id
      }
    },
    update: {},
    create: {
      name: 'Auckland',
      countryId: newZealand.id,
    },
  })

  const wellington = await prisma.city.upsert({
    where: { 
      name_countryId: {
        name: 'Wellington',
        countryId: newZealand.id
      }
    },
    update: {},
    create: {
      name: 'Wellington',
      countryId: newZealand.id,
    },
  })

  // Create cities for Australia
  const sydney = await prisma.city.upsert({
    where: { 
      name_countryId: {
        name: 'Sydney',
        countryId: australia.id
      }
    },
    update: {},
    create: {
      name: 'Sydney',
      countryId: australia.id,
    },
  })

  const canberra = await prisma.city.upsert({
    where: { 
      name_countryId: {
        name: 'Canberra',
        countryId: australia.id
      }
    },
    update: {},
    create: {
      name: 'Canberra',
      countryId: australia.id,
    },
  })

  console.log('✅ Created/updated cities')

  // Create or get the Plenipotentiary Ambassador title
  const plenipotentiaryTitle = await prisma.title.upsert({
    where: { name: 'Plenipotentiary Ambassador' },
    update: {},
    create: {
      name: 'Plenipotentiary Ambassador',
      description: 'Ambassador with full power to represent and make binding decisions',
      isAmbassadorial: true,
    },
  })

  console.log('✅ Created/updated titles')

  // Create United Tribes of Aotearoa nation treaty
  const unitedTribesAotearoa = await prisma.nationTreaty.upsert({
    where: { name: 'United Tribes of Aotearoa' },
    update: {},
    create: {
      name: 'United Tribes of Aotearoa',
      officialName: 'United Tribes of Aotearoa',
      description: 'Sovereign tribal nation representing the indigenous peoples of New Zealand',
      status: 'ACTIVE',
      contactEmail: '<EMAIL>',
      contactPhone: '+64-9-123-4567',
      website: 'https://unitedtribesaotearoa.nz',
    },
  })

  // Create Pacific Islands Alliance as second nation treaty
  const pacificIslandsAlliance = await prisma.nationTreaty.upsert({
    where: { name: 'Pacific Islands Alliance' },
    update: {},
    create: {
      name: 'Pacific Islands Alliance',
      officialName: 'Pacific Islands Alliance',
      description: 'Alliance of Pacific Island nations working together for peace and prosperity',
      status: 'ACTIVE',
      contactEmail: '<EMAIL>',
      contactPhone: '+************',
      website: 'https://pacificislandsalliance.org',
    },
  })

  console.log('✅ Created nation treaties')

  const defaultPassword = await bcrypt.hash('password123', 12)

  // Create Special Envoy users for United Tribes of Aotearoa
  const aotearoaSpecialEnvoy1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Manaia Tamaki',
      passwordHash: defaultPassword,
    },
  })

  const aotearoaSpecialEnvoy2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Tane Mahuta',
      passwordHash: defaultPassword,
    },
  })

  // Create Special Envoy users for Pacific Islands Alliance
  const pacificSpecialEnvoy1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Kalani Kai',
      passwordHash: defaultPassword,
    },
  })

  const pacificSpecialEnvoy2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Leilani Moe',
      passwordHash: defaultPassword,
    },
  })

  console.log('✅ Created special envoy users')

  // Create normal users for United Tribes of Aotearoa
  const aotearoaUser1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Hana Walker',
      passwordHash: defaultPassword,
    },
  })

  const aotearoaUser2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Kiri Taylor',
      passwordHash: defaultPassword,
    },
  })

  // Create normal users for Pacific Islands Alliance
  const pacificUser1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Makoa Nakamura',
      passwordHash: defaultPassword,
    },
  })

  const pacificUser2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Alana Chung',
      passwordHash: defaultPassword,
    },
  })

  console.log('✅ Created normal users')

  // Create profiles for special envoys
  const specialEnvoyData = [
    { user: aotearoaSpecialEnvoy1, email: '<EMAIL>', nation: unitedTribesAotearoa, city: auckland, title: plenipotentiaryTitle },
    { user: aotearoaSpecialEnvoy2, email: '<EMAIL>', nation: unitedTribesAotearoa, city: wellington, title: plenipotentiaryTitle },
    { user: pacificSpecialEnvoy1, email: '<EMAIL>', nation: pacificIslandsAlliance, city: sydney, title: plenipotentiaryTitle },
    { user: pacificSpecialEnvoy2, email: '<EMAIL>', nation: pacificIslandsAlliance, city: canberra, title: plenipotentiaryTitle },
  ]

  for (const { user, email, nation, city, title } of specialEnvoyData) {
    const userId = user.id as string
    const userName = user.name || ''
    const userEmail = email
    const nameParts = userName.split(' ')

    await prisma.userProfile.upsert({
      where: { userId: userId },
      update: {},
      create: {
        userId: userId,
        firstName: nameParts[0] || '',
        lastName: nameParts[1] || '',
        nwaEmail: userEmail,
        countryId: newZealand.id,
        cityId: city.id as number,
        titleId: title.id as string,
        bio: `Special Envoy for ${nation.name}`,
      },
    })

    // Assign special_envoy role
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: userId,
          roleId: specialEnvoyRole.id,
        }
      },
      update: {},
      create: {
        userId: userId,
        roleId: specialEnvoyRole.id,
      },
    })

    // Create nation treaty membership
    await prisma.nationTreatyMember.upsert({
      where: {
        userId_nationTreatyId: {
          userId: userId,
          nationTreatyId: nation.id as string,
        }
      },
      update: {},
      create: {
        userId: userId,
        nationTreatyId: nation.id as string,
        role: 'ENVOY',
        status: 'ACTIVE',
      },
    })
  }

  // Create profiles for normal users
  const normalUserData = [
    { user: aotearoaUser1, email: '<EMAIL>', nation: unitedTribesAotearoa, city: auckland },
    { user: aotearoaUser2, email: '<EMAIL>', nation: unitedTribesAotearoa, city: wellington },
    { user: pacificUser1, email: '<EMAIL>', nation: pacificIslandsAlliance, city: sydney },
    { user: pacificUser2, email: '<EMAIL>', nation: pacificIslandsAlliance, city: canberra },
  ]

  for (const { user, email, nation, city } of normalUserData) {
    const userId = user.id as string
    const userName = user.name || ''
    const userEmail = email
    const nameParts = userName.split(' ')

    await prisma.userProfile.upsert({
      where: { userId: userId },
      update: {},
      create: {
        userId: userId,
        firstName: nameParts[0] || '',
        lastName: nameParts[1] || '',
        nwaEmail: userEmail,
        countryId: newZealand.id,
        cityId: city.id as number,
        bio: `Member of ${nation.name}`,
      },
    })

    // Assign envoy role to normal users for testing
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: userId,
          roleId: envoyRole.id,
        }
      },
      update: {},
      create: {
        userId: userId,
        roleId: envoyRole.id,
      },
    })

    // Create nation treaty membership
    await prisma.nationTreatyMember.upsert({
      where: {
        userId_nationTreatyId: {
          userId: userId,
          nationTreatyId: nation.id as string,
        }
      },
      update: {},
      create: {
        userId: userId,
        nationTreatyId: nation.id as string,
        role: 'ENVOY',
        status: 'ACTIVE',
      },
    })
  }

  console.log('✅ Created user profiles and assigned roles')

  console.log('🎉 Nation treaties and special envoys seeding completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })