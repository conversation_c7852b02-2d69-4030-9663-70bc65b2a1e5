import { PrismaClient } from '@prisma/client';

async function checkSarahJones() {
  const prisma = new PrismaClient();

  try {
    console.log('🔍 Searching for <PERSON>...');

    // Search by name (case insensitive)
    const usersByName = await prisma.user.findMany({
      where: {
        OR: [
          { name: { contains: '<PERSON>', mode: 'insensitive' } },
          { name: { contains: '<PERSON>', mode: 'insensitive' } }
        ]
      },
      include: {
        profile: {
          include: {
            country: true,
            city: true,
            title: true
          }
        }
      }
    });

    console.log(`📊 Found ${usersByName.length} users matching "<PERSON>" or "Jones":`);

    usersByName.forEach((user, index) => {
      console.log(`\n👤 User ${index + 1}:`);
      console.log(`   ID: ${user.id}`);
      console.log(`   Name: ${user.name}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Has Profile: ${!!user.profile}`);

      if (user.profile) {
        console.log(`   Profile Data:`);
        console.log(`     - First Name: ${user.profile.firstName || 'NULL'}`);
        console.log(`     - Last Name: ${user.profile.lastName || 'NULL'}`);
        console.log(`     - Personal Email: ${user.profile.personalEmail || 'NULL'}`);
        console.log(`     - NWA Email: ${user.profile.nwaEmail || 'NULL'}`);
        console.log(`     - Phone: ${user.profile.phone || 'NULL'}`);
        console.log(`     - Mobile: ${user.profile.mobile || 'NULL'}`);
        console.log(`     - Date of Birth: ${user.profile.dateOfBirth || 'NULL'}`);
        console.log(`     - Peace Ambassador Number: ${user.profile.peaceAmbassadorNumber || 'NULL'}`);
        console.log(`     - Bio: ${user.profile.bio || 'NULL'}`);
        console.log(`     - Country: ${user.profile.country?.name || 'NULL'}`);
        console.log(`     - City: ${user.profile.city?.name || 'NULL'}`);
        console.log(`     - Title: ${user.profile.title?.name || 'NULL'}`);
      }
    });

    // Also search for exact name match
    const exactMatch = await prisma.user.findFirst({
      where: {
        name: {
          equals: 'Sarah Jones',
          mode: 'insensitive'
        }
      },
      include: {
        profile: {
          include: {
            country: true,
            city: true,
            title: true
          }
        }
      }
    });

    if (exactMatch) {
      console.log(`\n🎯 Exact match found for "Sarah Jones":`);
      console.log(`   ID: ${exactMatch.id}`);
      console.log(`   Email: ${exactMatch.email}`);
      console.log(`   Profile exists: ${!!exactMatch.profile}`);

      if (exactMatch.profile) {
        console.log(`   Profile emails:`);
        console.log(`     - Personal Email: ${exactMatch.profile.personalEmail || 'NULL/EMPTY'}`);
        console.log(`     - NWA Email: ${exactMatch.profile.nwaEmail || 'NULL/EMPTY'}`);
      }
    } else {
      console.log(`\n❌ No exact match found for "Sarah Jones"`);
    }

    // Check total user count
    const totalUsers = await prisma.user.count();
    console.log(`\n📈 Total users in database: ${totalUsers}`);

  } catch (error) {
    console.error('❌ Error searching for Sarah Jones:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSarahJones();