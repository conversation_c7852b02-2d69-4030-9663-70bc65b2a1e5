const { PrismaClient } = require('@prisma/client')\n\nconst prisma = new PrismaClient()\n\nasync function verifyTreatyTypes() {\n  const treatyTypes = await prisma.treatyType.findMany({\n    orderBy: {\n      category: 'asc'\n    }\n  })\n  \n  console.log(`Total treaty types: ${treatyTypes.length}`)\n  \n  // Group by category\n  const categories = {}\n  treatyTypes.forEach(type => {\n    if (!categories[type.category]) {\n      categories[type.category] = []\n    }\n    categories[type.category].push(type)\n  })\n  \n  // Display by category\n  Object.keys(categories).forEach(category => {\n    console.log(`\\n${category} (${categories[category].length} types):`)\n    categories[category].forEach(type => {\n      console.log(`  - ${type.name}`)\n    })\n  })\n}\n\nverifyTreatyTypes()\n  .catch(e => {\n    console.error(e)\n    process.exit(1)\n  })\n  .finally(async () => {\n    await prisma.$disconnect()\n  })