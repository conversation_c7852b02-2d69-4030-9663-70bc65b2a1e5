import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function assignAdminRole(userEmail: string) {
  try {
    console.log(`🔍 Looking for user with email: ${userEmail}`)
    
    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: userEmail },
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    })

    if (!user) {
      console.log(`❌ User with email ${userEmail} not found`)
      return
    }

    console.log(`✅ Found user: ${user.name} (ID: ${user.id})`)
    console.log(`Current roles: ${user.userRoles.map(ur => ur.role.name).join(', ') || 'None'}`)

    // Find admin role
    const adminRole = await prisma.role.findUnique({
      where: { name: 'admin' }
    })

    if (!adminRole) {
      console.log('❌ Admin role not found')
      return
    }

    // Check if user already has admin role
    const existingAdminRole = user.userRoles.find(ur => ur.role.name === 'admin')
    if (existingAdminRole) {
      console.log('✅ User already has admin role')
      return
    }

    // Assign admin role
    await prisma.userRole.create({
      data: {
        userId: user.id,
        roleId: adminRole.id
      }
    })

    console.log('✅ Admin role assigned successfully!')

    // Verify assignment
    const updatedUser = await prisma.user.findUnique({
      where: { email: userEmail },
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    })

    console.log(`Updated roles: ${updatedUser?.userRoles.map(ur => ur.role.name).join(', ') || 'None'}`)

  } catch (error) {
    console.error('❌ Error assigning admin role:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Get email from command line argument
const userEmail = process.argv[2]

if (!userEmail) {
  console.log('Usage: ts-node scripts/assign-admin-role.ts <user-email>')
  console.log('Example: ts-node scripts/assign-admin-role.ts <EMAIL>')
  process.exit(1)
}

assignAdminRole(userEmail)
