const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testUserWorkflowNotifications() {
  try {
    console.log('🧪 Testing User Workflow Notifications...');

    // Test 1: Create a test user to trigger user creation notification
    console.log('\n1. Testing user creation notification...');

    const timestamp = Date.now();
    const testUser = await prisma.user.create({
      data: {
        name: `Test User ${timestamp}`,
        email: `test.user.${timestamp}@example.com`,
        profile: {
          create: {
            firstName: 'Test',
            lastName: `User ${timestamp}`,
            nwaEmail: `test.user.${timestamp}@nwa.org`,
            phone: '+1234567890',
            mobile: '+1234567891',
            bio: 'Test user for notification system',
          },
        },
      },
    });

    console.log('✅ Test user created:');
    console.log(`   - ID: ${testUser.id}`);
    console.log(`   - Name: ${testUser.name}`);
    console.log(`   - Email: ${testUser.email}`);

    // Test 2: Check if user creation notification was created
    console.log('\n2. Checking user creation notification...');

    const userCreationNotifications = await prisma.notification.findMany({
      where: {
        type: 'USER_CREATED',
        data: {
          path: ['userId'],
          equals: testUser.id,
        },
      },
    });

    if (userCreationNotifications.length > 0) {
      console.log('✅ User creation notification found:');
      const notification = userCreationNotifications[0];
      console.log(`   - Message: ${notification.message}`);
      console.log(`   - Priority: ${notification.priority}`);
      console.log(`   - Category: ${notification.category}`);
      console.log(`   - Recipients: ${JSON.stringify(notification.recipients)}`);
      console.log(`   - Data: ${JSON.stringify(notification.data)}`);
    } else {
      console.log('ℹ️  No user creation notification found (expected if not running through API)');
    }

    // Test 3: Update the test user to trigger user update notification
    console.log('\n3. Testing user update notification...');

    const updatedUser = await prisma.user.update({
      where: { id: testUser.id },
      data: {
        name: 'Test User Updated',
        profile: {
          update: {
            phone: '+1987654321',
            bio: 'Updated test user for notification system',
          },
        },
      },
    });

    console.log('✅ Test user updated:');
    console.log(`   - New Name: ${updatedUser.name}`);
    console.log(`   - New Phone: ${updatedUser.profile?.phone}`);

    // Test 4: Check if user update notification was created
    console.log('\n4. Checking user update notification...');

    const userUpdateNotifications = await prisma.notification.findMany({
      where: {
        type: 'USER_UPDATED',
        data: {
          path: ['userId'],
          equals: testUser.id,
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 1,
    });

    if (userUpdateNotifications.length > 0) {
      console.log('✅ User update notification found:');
      const notification = userUpdateNotifications[0];
      console.log(`   - Message: ${notification.message}`);
      console.log(`   - Priority: ${notification.priority}`);
      console.log(`   - Category: ${notification.category}`);
      console.log(`   - Changed Fields: ${JSON.stringify(notification.data.changedFields)}`);
    } else {
      console.log('ℹ️  No user update notification found (expected if not running through API)');
    }

    // Test 5: Test notification statistics for user management
    console.log('\n5. Testing user management notification statistics...');

    const userManagementNotifications = await prisma.notification.findMany({
      where: {
        category: 'USER_MANAGEMENT',
      },
    });

    console.log(`✅ User management notifications: ${userManagementNotifications.length}`);

    const userNotificationStats = await prisma.notification.groupBy({
      by: ['type', 'priority'],
      where: {
        category: 'USER_MANAGEMENT',
      },
      _count: {
        id: true,
      },
    });

    console.log('✅ User management notification breakdown:');
    userNotificationStats.forEach(stat => {
      console.log(`   - ${stat.type} (${stat.priority}): ${stat._count.id} notifications`);
    });

    // Test 6: Test notification retrieval with user management filters
    console.log('\n6. Testing notification retrieval with user management filters...');

    const highPriorityUserNotifications = await prisma.notification.findMany({
      where: {
        category: 'USER_MANAGEMENT',
        priority: 'HIGH',
      },
    });

    console.log(`✅ High priority user management notifications: ${highPriorityUserNotifications.length}`);

    // Test 7: Verify notification data structure
    console.log('\n7. Testing notification data structure...');

    const sampleNotification = await prisma.notification.findFirst({
      where: {
        category: 'USER_MANAGEMENT',
      },
      orderBy: { createdAt: 'desc' },
    });

    if (sampleNotification) {
      console.log('✅ Sample user management notification structure:');
      console.log(`   - ID: ${sampleNotification.id}`);
      console.log(`   - Type: ${sampleNotification.type}`);
      console.log(`   - Priority: ${sampleNotification.priority}`);
      console.log(`   - Category: ${sampleNotification.category}`);
      console.log(`   - Trigger: ${sampleNotification.trigger}`);
      console.log(`   - Triggered By: ${sampleNotification.triggeredBy}`);
      console.log(`   - Recipients: ${JSON.stringify(sampleNotification.recipients)}`);
      console.log(`   - Recipient Types: ${JSON.stringify(sampleNotification.recipientTypes)}`);
      console.log(`   - Has Data: ${sampleNotification.data ? 'Yes' : 'No'}`);
    }

    // Test 8: Test notification search functionality
    console.log('\n8. Testing notification search functionality...');

    const searchResults = await prisma.notification.findMany({
      where: {
        OR: [
          { type: { contains: 'USER', mode: 'insensitive' } },
          { message: { contains: 'user', mode: 'insensitive' } },
        ],
      },
    });

    console.log(`✅ User-related notifications found: ${searchResults.length}`);

    console.log('\n🎉 All user workflow notification tests completed!');
    console.log(`📊 Total user management notifications: ${userManagementNotifications.length}`);

    // Summary
    console.log('\n📋 Summary:');
    console.log(`   - Test user created: ${testUser.name}`);
    console.log(`   - Test user updated: ${updatedUser.name}`);
    console.log(`   - User creation notifications: ${userCreationNotifications.length}`);
    console.log(`   - User update notifications: ${userUpdateNotifications.length}`);
    console.log(`   - High priority user notifications: ${highPriorityUserNotifications.length}`);
    console.log(`   - Search results: ${searchResults.length}`);

  } catch (error) {
    console.error('❌ Error testing user workflow notifications:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testUserWorkflowNotifications();