import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkServers() {
  try {
    const servers = await prisma.remote_servers.findMany({
      select: {
        id: true,
        name: true,
        url: true,
        isActive: true
      }
    });

    console.log('Current servers in database:');
    servers.forEach((server, index) => {
      console.log(`${index + 1}. ${server.name} (${server.id})`);
      console.log(`   URL: ${server.url}`);
      console.log(`   Active: ${server.isActive}`);
      console.log('');
    });
  } catch (error) {
    console.error('Error checking servers:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkServers();