const fetch = require('node-fetch');

async function testApiEndpoint() {
  try {
    const response = await fetch('http://localhost:3001/api/admin/users');
    console.log('Status:', response.status);
    console.log('Status Text:', response.statusText);
    
    const text = await response.text();
    console.log('Response:', text);
  } catch (error) {
    console.error('Error:', error);
  }
}

testApiEndpoint();