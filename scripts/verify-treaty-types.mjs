import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function verifyTreatyTypes() {
  const treatyTypes = await prisma.treatyType.findMany({
    orderBy: {
      category: 'asc'
    }
  })
  
  console.log(`Total treaty types: ${treatyTypes.length}`)
  
  // Group by category
  const categories = {}
  treatyTypes.forEach(type => {
    if (!categories[type.category]) {
      categories[type.category] = []
    }
    categories[type.category].push(type)
  })
  
  // Display by category
  Object.keys(categories).forEach(category => {
    console.log(`\n${category} (${categories[category].length} types):`)
    categories[category].forEach(type => {
      console.log(`  - ${type.name}`)
    })
  })
}

verifyTreatyTypes()
  .catch(e => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })