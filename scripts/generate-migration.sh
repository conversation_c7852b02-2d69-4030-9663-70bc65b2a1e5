#!/bin/bash

# This script generates a new Prisma migration
# Usage: ./scripts/generate-migration.sh "description of changes"

set -e  # Exit on any error

echo "Generating new Prisma migration..."

# Check if we're in the correct directory
if [ ! -f "package.json" ]; then
    echo "Error: Must be run from the project root directory"
    exit 1
fi

# Check if description was provided
if [ $# -eq 0 ]; then
    echo "Error: Please provide a migration description"
    echo "Usage: ./scripts/generate-migration.sh \"description of changes\""
    exit 1
fi

# Generate the migration
npx prisma migrate dev --name "$1"

echo "Migration generated successfully!"
echo "Don't forget to review the generated SQL and update the database_schema.sql file if needed."