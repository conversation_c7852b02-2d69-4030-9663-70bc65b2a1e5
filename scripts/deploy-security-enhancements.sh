#!/bin/bash
# deploy-security-enhancements.sh - Deploy NWA Alliance Security Enhancements to Production

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DEPLOYMENT_ENV="${1:-production}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/nwa-alliance"
LOG_FILE="/var/log/nwa-deployment-$TIMESTAMP.log"

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Pre-deployment checks
pre_deployment_checks() {
    log "🔍 Running pre-deployment security checks..."

    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root. Use a deployment user with sudo privileges."
    fi

    # Check environment
    if [[ "$DEPLOYMENT_ENV" != "production" && "$DEPLOYMENT_ENV" != "staging" ]]; then
        error "Invalid deployment environment: $DEPLOYMENT_ENV. Use 'production' or 'staging'."
    fi

    # Check required files
    local required_files=(".env.$DEPLOYMENT_ENV" "docker-compose.$DEPLOYMENT_ENV.yml" "Dockerfile.prod")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            error "Required file missing: $file"
        fi
    done

    # Check environment variables
    if grep -q "your-super-secret\|placeholder\|CHANGE-ME" ".env.$DEPLOYMENT_ENV"; then
        error "Environment file contains placeholder values. Please update all secrets before deployment."
    fi

    # Check disk space
    local required_space_gb=5
    local available_space=$(df /var/lib/docker | tail -1 | awk '{print int($4/1024/1024)}')
    if [[ $available_space -lt $required_space_gb ]]; then
        error "Insufficient disk space. Required: ${required_space_gb}GB, Available: ${available_space}GB"
    fi

    # Run security tests
    log "Running comprehensive security tests..."
    npm run security:complete
    if [[ $? -ne 0 ]]; then
        error "Security tests failed. Please fix all issues before deployment."
    fi

    success "Pre-deployment checks completed successfully"
}

# Create backup
create_backup() {
    log "💾 Creating production backup..."

    # Create backup directory
    sudo mkdir -p "$BACKUP_DIR"
    sudo chown $(whoami):$(whoami) "$BACKUP_DIR"

    # Backup database
    log "Backing up database..."
    docker exec nwa-${DEPLOYMENT_ENV}-postgres pg_dump \
        -U nwa_prod_user \
        -d nwa_portal_prod \
        -f "$BACKUP_DIR/db_backup_$TIMESTAMP.sql" \
        --no-owner --no-privileges --clean --if-exists

    # Backup Redis data
    log "Backing up Redis data..."
    docker exec nwa-${DEPLOYMENT_ENV}-redis redis-cli SAVE
    docker cp "nwa-${DEPLOYMENT_ENV}-redis:/data/dump.rdb" "$BACKUP_DIR/redis_backup_$TIMESTAMP.rdb"

    # Backup configuration
    cp ".env.$DEPLOYMENT_ENV" "$BACKUP_DIR/env_backup_$TIMESTAMP"
    cp "docker-compose.$DEPLOYMENT_ENV.yml" "$BACKUP_DIR/docker_compose_backup_$TIMESTAMP.yml"

    # Create compressed archive
    log "Creating compressed backup archive..."
    tar -czf "$BACKUP_DIR/nwa_backup_$TIMESTAMP.tar.gz" \
        "$BACKUP_DIR/db_backup_$TIMESTAMP.sql" \
        "$BACKUP_DIR/redis_backup_$TIMESTAMP.rdb" \
        "$BACKUP_DIR/env_backup_$TIMESTAMP" \
        "$BACKUP_DIR/docker_compose_backup_$TIMESTAMP.yml"

    # Clean up individual files
    rm "$BACKUP_DIR/db_backup_$TIMESTAMP.sql" \
       "$BACKUP_DIR/redis_backup_$TIMESTAMP.rdb" \
       "$BACKUP_DIR/env_backup_$TIMESTAMP" \
       "$BACKUP_DIR/docker_compose_backup_$TIMESTAMP.yml"

    # Upload to secure storage (if configured)
    if command -v aws &> /dev/null && [[ -n "${AWS_BACKUP_BUCKET:-}" ]]; then
        log "Uploading backup to secure cloud storage..."
        aws s3 cp "$BACKUP_DIR/nwa_backup_$TIMESTAMP.tar.gz" \
            "s3://$AWS_BACKUP_BUCKET/encrypted/"
    fi

    success "Backup created: $BACKUP_DIR/nwa_backup_$TIMESTAMP.tar.gz"
}

# Deploy database migrations
deploy_database_migrations() {
    log "🗄️  Deploying database migrations..."

    # Generate Prisma client
    log "Generating Prisma client..."
    npx prisma generate

    # Run database migrations
    log "Applying database migrations..."
    npx prisma migrate deploy

    # Verify migration success
    if ! npx prisma db execute --file <(echo "SELECT 1;") > /dev/null 2>&1; then
        error "Database migration verification failed"
    fi

    success "Database migrations deployed successfully"
}

# Build and deploy application
build_and_deploy_application() {
    log "🏗️  Building and deploying application..."

    # Stop existing containers
    log "Stopping existing containers..."
    docker-compose -f "docker-compose.$DEPLOYMENT_ENV.yml" down || true

    # Build new images
    log "Building Docker images..."
    docker-compose -f "docker-compose.$DEPLOYMENT_ENV.yml" build --no-cache

    # Start services
    log "Starting services..."
    docker-compose -f "docker-compose.$DEPLOYMENT_ENV.yml" up -d

    # Wait for services to be healthy
    log "Waiting for services to be ready..."
    sleep 30

    # Verify services are running
    if ! docker-compose -f "docker-compose.$DEPLOYMENT_ENV.yml" ps | grep -q "Up"; then
        error "Services failed to start properly"
    fi

    success "Application deployed successfully"
}

# Run post-deployment tests
run_post_deployment_tests() {
    log "🧪 Running post-deployment tests..."

    # Health check
    log "Performing health check..."
    local health_check=$(curl -s -o /dev/null -w "%{http_code}" \
        "https://localhost:3001/api/health" || echo "failed")

    if [[ "$health_check" != "200" ]]; then
        error "Health check failed with status: $health_check"
    fi

    # Security tests
    log "Running security validation tests..."
    npm run security:verify

    if [[ $? -ne 0 ]]; then
        warning "Some security tests failed. Review the results."
    fi

    # Load testing
    log "Running load tests..."
    npm run security:performance

    if [[ $? -ne 0 ]]; then
        warning "Load tests indicate performance issues. Monitor system closely."
    fi

    success "Post-deployment tests completed"
}

# Configure monitoring and alerting
configure_monitoring() {
    log "📊 Configuring monitoring and alerting..."

    # Verify monitoring endpoints
    local monitoring_check=$(curl -s -o /dev/null -w "%{http_code}" \
        "https://localhost:3001/api/security/status" || echo "failed")

    if [[ "$monitoring_check" != "200" ]]; then
        warning "Security monitoring endpoint not responding properly"
    fi

    # Test alert system
    log "Testing alert system..."
    curl -X POST "https://localhost:3001/api/security/test-alert" \
        -H "Content-Type: application/json" \
        -d '{"test": true}' > /dev/null 2>&1 || warning "Alert system test failed"

    success "Monitoring configuration completed"
}

# Update documentation
update_documentation() {
    log "📚 Updating deployment documentation..."

    # Update deployment log
    echo "# NWA Alliance Security Deployment - $TIMESTAMP" > "docs/deployment-log-$TIMESTAMP.md"
    echo "- Deployment Environment: $DEPLOYMENT_ENV" >> "docs/deployment-log-$TIMESTAMP.md"
    echo "- Deployment Time: $(date)" >> "docs/deployment-log-$TIMESTAMP.md"
    echo "- Backup Location: $BACKUP_DIR/nwa_backup_$TIMESTAMP.tar.gz" >> "docs/deployment-log-$TIMESTAMP.md"
    echo "- Security Tests: PASSED" >> "docs/deployment-log-$TIMESTAMP.md"
    echo "- Deployment Status: SUCCESS" >> "docs/deployment-log-$TIMESTAMP.md"

    success "Documentation updated"
}

# Main deployment process
main() {
    log "🚀 Starting NWA Alliance Security Deployment to $DEPLOYMENT_ENV"
    log "================================================================="

    # Pre-deployment checks
    pre_deployment_checks

    # Create backup
    create_backup

    # Deploy database migrations
    deploy_database_migrations

    # Build and deploy application
    build_and_deploy_application

    # Run post-deployment tests
    run_post_deployment_tests

    # Configure monitoring
    configure_monitoring

    # Update documentation
    update_documentation

    # Final status
    log "================================================================="
    success "🎉 NWA Alliance Security Deployment COMPLETED SUCCESSFULLY!"
    log "📅 Deployment Time: $(date)"
    log "🌍 Environment: $DEPLOYMENT_ENV"
    log "📁 Backup: $BACKUP_DIR/nwa_backup_$TIMESTAMP.tar.gz"
    log "📋 Log File: $LOG_FILE"
    log "📚 Documentation: docs/deployment-log-$TIMESTAMP.md"
    log ""
    log "🔐 Security enhancements deployed:"
    log "  ✅ Authentication and authorization system"
    log "  ✅ Role-based access control (RBAC)"
    log "  ✅ Two-factor authentication (2FA)"
    log "  ✅ Rate limiting and input validation"
    log "  ✅ Security event monitoring and alerting"
    log "  ✅ Audit logging and compliance features"
    log "  ✅ Penetration testing and validation"
    log ""
    log "📊 Next steps:"
    log "  1. Monitor system performance for the first 24 hours"
    log "  2. Review security logs regularly"
    log "  3. Test all security features"
    log "  4. Update runbooks and procedures"
    log "  5. Train operations team on new features"
    log ""
    log "For support: contact devops@nwa.<NAME_EMAIL>"
}

# Handle script arguments
case "${1:-}" in
    "production"|"staging")
        DEPLOYMENT_ENV="$1"
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [production|staging]"
        echo ""
        echo "Deploy NWA Alliance Security Enhancements to the specified environment."
        echo ""
        echo "Arguments:"
        echo "  production  Deploy to production environment (default)"
        echo "  staging     Deploy to staging environment"
        echo "  help        Show this help message"
        echo ""
        echo "Requirements:"
        echo "  - Docker and Docker Compose installed"
        echo "  - Production environment files configured"
        echo "  - Security tests passing"
        echo "  - Sufficient disk space"
        echo ""
        echo "The script will:"
        echo "  1. Run pre-deployment security checks"
        echo "  2. Create system backups"
        echo "  3. Deploy database migrations"
        echo "  4. Build and deploy the application"
        echo "  5. Run post-deployment tests"
        echo "  6. Configure monitoring and alerting"
        echo "  7. Update documentation"
        exit 0
        ;;
    *)
        if [[ -n "${1:-}" ]]; then
            error "Invalid argument: $1. Use 'production', 'staging', or 'help'."
        fi
        ;;
esac

# Run main deployment process
main "$@" 2>&1 | tee "$LOG_FILE"