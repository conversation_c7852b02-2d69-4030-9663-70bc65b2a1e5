#!/usr/bin/env node

/**
 * Dashboard Accessibility Testing Script
 * This script tests the dashboard for accessibility issues using axe-core
 */

import puppeteer from 'puppeteer';
import { AxePuppeteer } from '@axe-core/puppeteer';

const BASE_URL = 'http://localhost:3001';
const DASHBOARD_URL = `${BASE_URL}/dashboard`;

async function runAccessibilityTests() {
  console.log('♿ Starting Dashboard Accessibility Tests...\n');

  let browser;
  let passedTests = 0;
  let failedTests = 0;

  try {
    // Launch browser
    browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Configure axe
    const axe = new AxePuppeteer(page);

    // Set viewport for consistent testing
    await page.setViewport({ width: 1280, height: 720 });

    console.log('🔍 Testing dashboard accessibility...\n');

    // Navigate to dashboard
    let response = await page.goto(DASHBOARD_URL, { waitUntil: 'networkidle0' });

    // If redirected to login, we need to authenticate first
    if (response.url().includes('/login')) {
      console.log('🔐 Redirected to login page, attempting authentication...');

      // Wait for login form to load
      await page.waitForSelector('input[type="email"]', { timeout: 5000 });

      // Fill in login credentials
      await page.type('input[type="email"]', '<EMAIL>');
      await page.type('input[type="password"]', 'password');

      // Submit the form
      await page.click('button[type="submit"]');

      // Wait for navigation to dashboard
      await page.waitForNavigation({ waitUntil: 'networkidle0' });

      // Verify we're on the dashboard
      if (page.url().includes('/dashboard')) {
        console.log('✅ Successfully authenticated and navigated to dashboard');
      } else {
        console.log('❌ Authentication failed or unexpected redirect');
        return;
      }
    } else if (response.status() !== 200) {
      console.log(`❌ Dashboard not accessible (Status: ${response.status()})`);
      return;
    }

    // Wait for dashboard to load
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Run axe accessibility audit
    console.log('🔍 Running axe-core accessibility audit...\n');

    const results = await axe.analyze();

    // Process results
    if (results.violations.length === 0) {
      console.log('✅ No accessibility violations found!');
      passedTests++;
    } else {
      console.log(`❌ Found ${results.violations.length} accessibility violations:\n`);

      results.violations.forEach((violation, index) => {
        console.log(`${index + 1}. ${violation.id} (${violation.impact})`);
        console.log(`   Description: ${violation.description}`);
        console.log(`   Help: ${violation.help}`);
        console.log(`   Help URL: ${violation.helpUrl}`);

        if (violation.nodes.length > 0) {
          console.log(`   Affected elements: ${violation.nodes.length}`);
          violation.nodes.forEach((node, nodeIndex) => {
            console.log(`     ${nodeIndex + 1}. ${node.target.join(', ')}`);
            if (node.failureSummary) {
              console.log(`        ${node.failureSummary}`);
            }
          });
        }
        console.log('');
      });

      failedTests += results.violations.length;
    }

    // Test specific dashboard components
    console.log('🔍 Testing dashboard components...\n');

    // Check for common accessibility issues
    const issues = await page.evaluate(() => {
      const issues = [];

      // Check for missing alt text on images
      const images = document.querySelectorAll('img:not([alt])');
      if (images.length > 0) {
        issues.push({
          type: 'missing-alt-text',
          count: images.length,
          elements: Array.from(images).map(img => img.src || img.outerHTML.substring(0, 100))
        });
      }

      // Check for buttons without accessible names
      const buttons = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])');
      if (buttons.length > 0) {
        issues.push({
          type: 'button-no-name',
          count: buttons.length,
          elements: Array.from(buttons).map(btn => btn.textContent?.substring(0, 50) || btn.outerHTML.substring(0, 100))
        });
      }

      // Check for headings structure
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      const headingLevels = Array.from(headings).map(h => parseInt(h.tagName.charAt(1)));

      if (headingLevels.length > 0) {
        const hasH1 = headingLevels.includes(1);
        if (!hasH1) {
          issues.push({
            type: 'missing-h1',
            description: 'Page should have at least one H1 heading'
          });
        }

        // Check for skipped heading levels
        const sortedLevels = [...new Set(headingLevels)].sort();
        for (let i = 1; i < sortedLevels.length; i++) {
          if (sortedLevels[i] - sortedLevels[i-1] > 1) {
            issues.push({
              type: 'skipped-heading-level',
              description: `Heading levels skip from H${sortedLevels[i-1]} to H${sortedLevels[i]}`
            });
            break;
          }
        }
      }

      // Check for color contrast issues (basic check)
      const elements = document.querySelectorAll('*');
      const colorContrastIssues = [];

      elements.forEach(el => {
        const styles = window.getComputedStyle(el);
        const color = styles.color;
        const backgroundColor = styles.backgroundColor;

        // This is a basic check - in real scenarios you'd use a proper color contrast analyzer
        if (color !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
          // Add more sophisticated color contrast checking here if needed
        }
      });

      return issues;
    });

    if (issues.length === 0) {
      console.log('✅ No common accessibility issues found!');
      passedTests++;
    } else {
      console.log(`⚠️ Found ${issues.length} common accessibility issues:\n`);

      issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue.type}`);
        console.log(`   Description: ${issue.description || 'No description'}`);
        console.log(`   Count: ${issue.count || 'N/A'}`);

        if (issue.elements && issue.elements.length > 0) {
          console.log('   Examples:');
          issue.elements.slice(0, 3).forEach((element, elIndex) => {
            console.log(`     ${elIndex + 1}. ${element}`);
          });
        }
        console.log('');
      });

      failedTests += issues.length;
    }

    // Test keyboard navigation
    console.log('🔍 Testing keyboard navigation...\n');

    const keyboardIssues = await page.evaluate(() => {
      const issues = [];

      // Check for focusable elements
      const focusableElements = document.querySelectorAll(
        'a[href], button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
      );

      if (focusableElements.length === 0) {
        issues.push({
          type: 'no-focusable-elements',
          description: 'No focusable elements found'
        });
      }

      // Check for skip links
      const skipLinks = document.querySelectorAll('a[href^="#"]:not([href="#"])');
      if (skipLinks.length === 0) {
        issues.push({
          type: 'no-skip-links',
          description: 'No skip navigation links found'
        });
      }

      return issues;
    });

    if (keyboardIssues.length === 0) {
      console.log('✅ Keyboard navigation looks good!');
      passedTests++;
    } else {
      console.log(`⚠️ Found ${keyboardIssues.length} keyboard navigation issues:\n`);

      keyboardIssues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue.type}`);
        console.log(`   Description: ${issue.description}`);
        console.log('');
      });

      failedTests += keyboardIssues.length;
    }

  } catch (error) {
    console.log(`❌ Accessibility test error: ${error.message}`);
    failedTests++;
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  // Summary
  console.log('\n📊 Accessibility Test Summary:');
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📈 Success Rate: ${passedTests + failedTests > 0 ? Math.round((passedTests / (passedTests + failedTests)) * 100) : 0}%`);

  if (failedTests === 0) {
    console.log('\n🎉 All accessibility tests passed! Dashboard is accessible.');
    process.exit(0);
  } else {
    console.log('\n⚠️ Some accessibility issues found. Please review and fix them.');
    process.exit(1);
  }
}

// Handle script execution
if (import.meta.url === `file://${process.argv[1]}`) {
  runAccessibilityTests().catch(console.error);
}

export { runAccessibilityTests };