const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateExistingNotifications() {
  try {
    console.log('Starting update of existing notifications...');

    // Get all existing notifications
    const notifications = await prisma.notification.findMany();

    console.log(`Found ${notifications.length} notifications to update`);

    // Update each notification with enhanced fields
    for (const notification of notifications) {
      // Determine priority based on notification type
      let priority = 'NORMAL';
      let category = 'SYSTEM';
      let trigger = 'system_migration';

      // Set priority and category based on notification type
      if (notification.type.includes('USER') || notification.type.includes('ADMIN')) {
        priority = 'HIGH';
        category = 'USER_MANAGEMENT';
        trigger = 'user_management_event';
      } else if (notification.type.includes('TREATY')) {
        priority = 'NORMAL';
        category = 'TREATY';
        trigger = 'treaty_event';
      } else if (notification.type.includes('SECURITY') || notification.type.includes('LOGIN')) {
        priority = 'HIGH';
        category = 'SECURITY';
        trigger = 'security_event';
      } else if (notification.type.includes('SERVER') || notification.type.includes('REMOTE')) {
        priority = 'NORMAL';
        category = 'REMOTE_SERVER';
        trigger = 'server_event';
      }

      // Determine recipient types based on recipients array
      const recipientTypes = {};
      const userRecipients = [];
      const roleRecipients = [];

      for (const recipient of notification.recipients) {
        if (recipient === 'admin' || recipient === 'system') {
          roleRecipients.push(recipient);
        } else {
          userRecipients.push(recipient);
        }
      }

      if (userRecipients.length > 0) {
        recipientTypes.users = userRecipients;
      }
      if (roleRecipients.length > 0) {
        recipientTypes.roles = roleRecipients;
      }

      // Update the notification
      await prisma.notification.update({
        where: { id: notification.id },
        data: {
          trigger,
          triggeredBy: null, // Set to null for existing notifications (unknown origin)
          priority,
          category,
          recipientTypes: Object.keys(recipientTypes).length > 0 ? recipientTypes : null,
        },
      });

      console.log(`Updated notification ${notification.id} (${notification.type})`);
    }

    console.log('✅ Successfully updated all existing notifications');

    // Show summary statistics
    const updatedNotifications = await prisma.notification.findMany({
      select: {
        priority: true,
        category: true,
        trigger: true,
      },
    });

    const stats = {
      total: updatedNotifications.length,
      byPriority: {},
      byCategory: {},
      byTrigger: {},
    };

    updatedNotifications.forEach(notif => {
      stats.byPriority[notif.priority] = (stats.byPriority[notif.priority] || 0) + 1;
      stats.byCategory[notif.category] = (stats.byCategory[notif.category] || 0) + 1;
      stats.byTrigger[notif.trigger] = (stats.byTrigger[notif.trigger] || 0) + 1;
    });

    console.log('\n📊 Update Summary:');
    console.log('Priority distribution:', stats.byPriority);
    console.log('Category distribution:', stats.byCategory);
    console.log('Trigger distribution:', stats.byTrigger);

  } catch (error) {
    console.error('❌ Error updating notifications:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the update
updateExistingNotifications();