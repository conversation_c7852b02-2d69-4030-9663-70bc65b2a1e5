#!/bin/bash

# This script applies database migrations for the NWA Alliance portal
# It should be run after making changes to the database schema

set -e  # Exit on any error

echo "Starting database migration process..."

# Check if we're in the correct directory
if [ ! -f "package.json" ]; then
    echo "Error: Must be run from the project root directory"
    exit 1
fi

# Check if database connection is available
echo "Testing database connection..."
if ! psql -h localhost -p 5436 -U nwa_user -d nwa_portal -c "SELECT 1;" > /dev/null 2>&1; then
    echo "Error: Cannot connect to database. Please ensure PostgreSQL is running and accessible."
    echo "You may need to start the database with: docker-compose up -d"
    exit 1
fi

# Apply Prisma migrations
echo "Applying Prisma migrations..."
npx prisma migrate deploy

# If there are any additional SQL migrations, apply them
echo "Checking for additional SQL migrations..."
MIGRATION_DIR="prisma/migrations"
for migration in "$MIGRATION_DIR"/*/migration.sql; do
    if [ -f "$migration" ]; then
        echo "Applying migration: $migration"
        psql -h localhost -p 5436 -U nwa_user -d nwa_portal -f "$migration"
    fi
done

echo "Database migration completed successfully!"