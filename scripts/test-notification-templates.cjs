const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testNotificationTemplates() {
  try {
    console.log('🧪 Testing Notification Template System...');

    // Test 1: Create notification using template
    console.log('\n1. Testing template-based notification creation...');
    const templateNotification = await prisma.notification.create({
      data: {
        type: 'USER_CREATED',
        message: '<NAME_EMAIL> has registered and requires approval',
        recipients: ['admin'],
        trigger: 'user_registration',
        triggeredBy: 'system',
        priority: 'HIGH',
        category: 'USER_MANAGEMENT',
        recipientTypes: {
          roles: ['admin'],
        },
        data: {
          userId: 'user123',
          userEmail: '<EMAIL>',
          registrationDate: new Date().toISOString(),
        },
      },
    });

    console.log('✅ Template-based notification created:');
    console.log(`   - Type: ${templateNotification.type}`);
    console.log(`   - Message: ${templateNotification.message}`);
    console.log(`   - Priority: ${templateNotification.priority}`);
    console.log(`   - Category: ${templateNotification.category}`);

    // Test 2: Create treaty notification using template
    console.log('\n2. Testing treaty template notification...');
    const treatyNotification = await prisma.notification.create({
      data: {
        type: 'TREATY_EXPIRED',
        message: 'Treaty "Peace Treaty Alpha" has expired and requires renewal',
        recipients: ['admin', 'treaty_manager'],
        trigger: 'treaty_expiry',
        triggeredBy: 'system',
        priority: 'HIGH',
        category: 'TREATY',
        recipientTypes: {
          roles: ['admin'],
          users: ['treaty_manager'],
        },
        data: {
          treatyId: 'treaty1234',
          treatyName: 'Peace Treaty Alpha',
          expiryDate: new Date().toISOString(),
        },
      },
    });

    console.log('✅ Treaty notification created:');
    console.log(`   - Recipients: ${JSON.stringify(treatyNotification.recipients)}`);
    console.log(`   - RecipientTypes: ${JSON.stringify(treatyNotification.recipientTypes)}`);

    // Test 3: Create security notification using template
    console.log('\n3. Testing security template notification...');
    const securityNotification = await prisma.notification.create({
      data: {
        type: 'FAILED_LOGIN_ATTEMPT',
        message: 'Multiple failed login attempts detected for user admin from IP *************',
        recipients: ['admin'],
        trigger: 'security_monitoring',
        triggeredBy: 'system',
        priority: 'HIGH',
        category: 'SECURITY',
        recipientTypes: {
          roles: ['admin'],
        },
        data: {
          ipAddress: '*************',
          attemptCount: 5,
          timeWindow: 'last_10_minutes',
          userAgent: 'Mozilla/5.0...',
        },
      },
    });

    console.log('✅ Security notification created:');
    console.log(`   - Data: ${JSON.stringify(securityNotification.data)}`);

    // Test 4: Create system notification using template
    console.log('\n4. Testing system template notification...');
    const systemNotification = await prisma.notification.create({
      data: {
        type: 'BACKUP_FAILED',
        message: 'System backup failed on 2025-09-22. Please check system logs.',
        recipients: ['admin'],
        trigger: 'backup_process',
        triggeredBy: 'system',
        priority: 'HIGH',
        category: 'SYSTEM',
        recipientTypes: {
          roles: ['admin'],
        },
        data: {
          backupDate: '2025-09-22',
          errorMessage: 'Disk space insufficient',
          backupSize: '2.5GB',
        },
      },
    });

    console.log('✅ System notification created:');
    console.log(`   - Trigger: ${systemNotification.trigger}`);

    // Test 5: Verify template statistics
    console.log('\n5. Testing template statistics...');
    const templateStats = await prisma.notification.groupBy({
      by: ['type', 'priority', 'category'],
      _count: {
        id: true,
      },
      orderBy: {
        type: 'asc',
      },
    });

    console.log('✅ Template-based notification statistics:');
    templateStats.forEach(stat => {
      console.log(`   - ${stat.type} (${stat.priority} ${stat.category}): ${stat._count.id} notifications`);
    });

    // Test 6: Test notification retrieval with template data
    console.log('\n6. Testing notification retrieval with template fields...');
    const allNotifications = await prisma.notification.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10,
    });

    console.log(`✅ Retrieved ${allNotifications.length} notifications with template data`);
    console.log('Sample notifications with template information:');
    allNotifications.slice(0, 3).forEach((notification, index) => {
      console.log(`   ${index + 1}. ${notification.type} - ${notification.priority} - ${notification.category}`);
      console.log(`      Trigger: ${notification.trigger}`);
      console.log(`      Recipients: ${JSON.stringify(notification.recipients)}`);
      console.log(`      Message: ${notification.message.substring(0, 60)}...`);
    });

    // Test 7: Verify audit logging for template notifications
    console.log('\n7. Testing audit logging for template notifications...');
    const auditLogs = await prisma.auditLog.findMany({
      where: {
        action: 'NOTIFICATION_CREATED',
        resourceId: {
          in: [templateNotification.id, treatyNotification.id, securityNotification.id, systemNotification.id],
        },
      },
      orderBy: { timestamp: 'desc' },
    });

    console.log(`✅ Found ${auditLogs.length} audit log entries for template notifications`);
    if (auditLogs.length > 0) {
      console.log('Sample audit log entry:');
      console.log(`   - Action: ${auditLogs[0].action}`);
      console.log(`   - Resource: ${auditLogs[0].resource}`);
      console.log(`   - Success: ${auditLogs[0].success}`);
      console.log(`   - New Values: ${JSON.stringify(auditLogs[0].newValues, null, 2)}`);
    }

    console.log('\n🎉 All notification template system tests passed!');
    console.log(`📊 Total notifications created: ${allNotifications.length}`);

  } catch (error) {
    console.error('❌ Error testing notification template system:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testNotificationTemplates();