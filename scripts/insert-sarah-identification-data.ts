import { PrismaClient } from '@prisma/client';

async function insertSarahIdentificationData() {
  const prisma = new PrismaClient();

  try {
    console.log('🔄 Inserting comprehensive identification data for <PERSON>...');

    // First find <PERSON> user
    const sarahUser = await prisma.user.findFirst({
      where: {
        OR: [
          { name: { contains: '<PERSON>', mode: 'insensitive' } },
          { name: { contains: '<PERSON>', mode: 'insensitive' } }
        ]
      }
    });

    if (!sarahUser) {
      console.log('❌ <PERSON> not found in database');
      return;
    }

    console.log(`✅ Found Sarah Jones (ID: ${sarahUser.id})`);

    // 1. Insert Identification Types if they don't exist
    console.log('📝 Creating identification types...');
    const passportType = await prisma.identificationType.upsert({
      where: { name: 'Passport' },
      update: {},
      create: {
        name: 'Passport',
        description: 'Government-issued passport for international travel',
        category: 'government'
      }
    });

    const driversLicenseType = await prisma.identificationType.upsert({
      where: { name: 'Driver License' },
      update: {},
      create: {
        name: 'Driver License',
        description: 'Government-issued driver license',
        category: 'government'
      }
    });

    const nwaIdType = await prisma.identificationType.upsert({
      where: { name: 'NWA ID Card' },
      update: {},
      create: {
        name: 'NWA ID Card',
        description: 'New World Alliance identification card',
        category: 'organizational'
      }
    });

    // 2. Insert User Identifications for Sarah
    console.log('🆔 Inserting user identifications...');
    await prisma.userIdentification.upsert({
      where: {
        userId_identificationTypeId_idNumber: {
          userId: sarahUser.id,
          identificationTypeId: passportType.id,
          idNumber: 'P123456789'
        }
      },
      update: {},
      create: {
        userId: sarahUser.id,
        identificationTypeId: passportType.id,
        idNumber: 'P123456789'
      }
    });

    await prisma.userIdentification.upsert({
      where: {
        userId_identificationTypeId_idNumber: {
          userId: sarahUser.id,
          identificationTypeId: driversLicenseType.id,
          idNumber: 'DL987654321'
        }
      },
      update: {},
      create: {
        userId: sarahUser.id,
        identificationTypeId: driversLicenseType.id,
        idNumber: 'DL987654321'
      }
    });

    await prisma.userIdentification.upsert({
      where: {
        userId_identificationTypeId_idNumber: {
          userId: sarahUser.id,
          identificationTypeId: nwaIdType.id,
          idNumber: 'NWA-SARAH-001'
        }
      },
      update: {},
      create: {
        userId: sarahUser.id,
        identificationTypeId: nwaIdType.id,
        idNumber: 'NWA-SARAH-001'
      }
    });

    // 3. Insert Treaty Types if they don't exist
    console.log('📜 Creating treaty types...');
    const peaceTreatyType = await prisma.treatyType.upsert({
      where: { name: 'Peace Treaty 2024' },
      update: {},
      create: {
        name: 'Peace Treaty 2024',
        description: 'Global peace and cooperation treaty for 2024',
        category: 'PEACE',
        price: 0.00,
        currency: 'USD',
        requiresPayment: false
      }
    });

    const diplomaticTreatyType = await prisma.treatyType.upsert({
      where: { name: 'Diplomatic Immunity Treaty' },
      update: {},
      create: {
        name: 'Diplomatic Immunity Treaty',
        description: 'International diplomatic immunity and protection treaty',
        category: 'DIPLOMATIC',
        price: 0.00,
        currency: 'USD',
        requiresPayment: false
      }
    });

    // 4. Insert User Treaty Numbers for Sarah
    console.log('📋 Inserting user treaty numbers...');
    await prisma.userTreatyNumber.upsert({
      where: {
        userId_treatyTypeId: {
          userId: sarahUser.id,
          treatyTypeId: peaceTreatyType.id
        }
      },
      update: {},
      create: {
        userId: sarahUser.id,
        treatyTypeId: peaceTreatyType.id,
        treatyNumber: 'PT-2024-001'
      }
    });

    await prisma.userTreatyNumber.upsert({
      where: {
        userId_treatyTypeId: {
          userId: sarahUser.id,
          treatyTypeId: diplomaticTreatyType.id
        }
      },
      update: {},
      create: {
        userId: sarahUser.id,
        treatyTypeId: diplomaticTreatyType.id,
        treatyNumber: 'DIP-2024-002'
      }
    });

    // 5. Insert Positions if they don't exist
    console.log('👤 Creating positions...');
    const ambassadorPosition = await prisma.position.upsert({
      where: { title: 'Ambassador' },
      update: {},
      create: {
        title: 'Ambassador',
        description: 'Diplomatic ambassador position',
        level: 10,
        isActive: true
      }
    });

    const peaceEnvoyPosition = await prisma.position.upsert({
      where: { title: 'Peace Envoy' },
      update: {},
      create: {
        title: 'Peace Envoy',
        description: 'Peace envoy and diplomatic representative',
        level: 8,
        isActive: true
      }
    });

    // 6. Insert User Positions for Sarah
    console.log('👥 Inserting user positions...');
    await prisma.userPosition.upsert({
      where: {
        id: `${sarahUser.id}-${ambassadorPosition.id}`
      },
      update: {},
      create: {
        userId: sarahUser.id,
        positionId: ambassadorPosition.id,
        startDate: new Date('2024-01-01'),
        isActive: true,
        notes: 'Appointed as ambassador for peace initiatives'
      }
    });

    await prisma.userPosition.upsert({
      where: {
        id: `${sarahUser.id}-${peaceEnvoyPosition.id}`
      },
      update: {},
      create: {
        userId: sarahUser.id,
        positionId: peaceEnvoyPosition.id,
        startDate: new Date('2024-02-01'),
        isActive: true,
        notes: 'Special envoy for peace negotiations'
      }
    });

    // 7. Create a Nation Treaty for testing
    console.log('🌍 Creating nation treaty...');
    const ukNationTreaty = await prisma.nationTreaty.upsert({
      where: { name: 'United Kingdom Treaty Organization' },
      update: {},
      create: {
        name: 'United Kingdom Treaty Organization',
        officialName: 'United Kingdom Treaty Organization (UKTO)',
        status: 'ACTIVE',
        description: 'United Kingdom diplomatic treaty organization',
        contactEmail: '<EMAIL>',
        contactPhone: '+44-20-7946-0000',
        contactAddress: '10 Downing Street, London, UK'
      }
    });

    // 8. Insert Nation Treaty Membership for Sarah
    console.log('🌍 Inserting nation treaty membership...');
    await prisma.nationTreatyMember.upsert({
      where: {
        userId_nationTreatyId: {
          userId: sarahUser.id,
          nationTreatyId: ukNationTreaty.id
        }
      },
      update: {},
      create: {
        userId: sarahUser.id,
        nationTreatyId: ukNationTreaty.id,
        role: 'ENVOY',
        joinDate: new Date('2024-01-15'),
        status: 'ACTIVE'
      }
    });

    // 9. Insert Nation Treaty Envoy for Sarah
    console.log('🕊️ Inserting nation treaty envoy...');
    await prisma.nationTreatyEnvoy.upsert({
      where: {
        userId_nationTreatyId_envoyType: {
          userId: sarahUser.id,
          nationTreatyId: ukNationTreaty.id,
          envoyType: 'PRIMARY'
        }
      },
      update: {},
      create: {
        userId: sarahUser.id,
        nationTreatyId: ukNationTreaty.id,
        envoyType: 'PRIMARY',
        title: 'Senior Peace Ambassador',
        address: '123 Peace Avenue, London, UK',
        city: 'London',
        country: 'United Kingdom',
        phone: '+44-20-7946-0123',
        mobile: '+44-20-7946-0124',
        email: '<EMAIL>',
        status: 'ACTIVE'
      }
    });

    console.log('✅ Successfully inserted all identification data for Sarah Jones!');
    console.log('\n📊 Summary of inserted data:');
    console.log('   - 3 User Identifications (Passport, Driver License, NWA ID)');
    console.log('   - 2 User Treaty Numbers (Peace Treaty, Diplomatic Immunity)');
    console.log('   - 2 User Positions (Ambassador, Peace Envoy)');
    console.log('   - 1 Nation Treaty Membership (UK Treaty Organization)');
    console.log('   - 1 Nation Treaty Envoy (Primary Envoy)');

  } catch (error) {
    console.error('❌ Error inserting Sarah Jones identification data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

insertSarahIdentificationData();