import { unifiedPermissions, checkLocalPermission, checkRemotePermission } from '../src/lib/services/unified-permissions.ts'

async function testUnifiedPermissions() {
  console.log('🧪 Testing Unified Permission Service...\n')

  try {
    // Test 1: Check local permission (no server context)
    console.log('Test 1: Local Permission Check')
    const localResult = await checkLocalPermission('test-user-id', 'read:profile')
    console.log('Local permission result:', localResult)
    console.log()

    // Test 2: Check remote permission (with server context)
    console.log('Test 2: Remote Permission Check')
    const remoteResult = await checkRemotePermission('test-user-id', 'read:users', 'server-123')
    console.log('Remote permission result:', remoteResult)
    console.log()

    // Test 3: Check permission with full context object
    console.log('Test 3: Full Context Permission Check')
    const contextResult = await unifiedPermissions.checkPermission({
      userId: 'test-user-id',
      permission: 'write:documents',
      serverId: 'server-456'
    })
    console.log('Context permission result:', contextResult)
    console.log()

    // Test 4: Get all user permissions
    console.log('Test 4: Get All User Permissions')
    const allPermissions = await unifiedPermissions.getAllUserPermissions('test-user-id')
    console.log('All user permissions:', JSON.stringify(allPermissions, null, 2))
    console.log()

    // Test 5: Validate context
    console.log('Test 5: Context Validation')
    const validContext = unifiedPermissions.validateContext({
      userId: 'test-user-id',
      permission: 'read:profile'
    })
    console.log('Valid context result:', validContext)

    const invalidContext = unifiedPermissions.validateContext({
      userId: '',
      permission: 'read:profile'
    })
    console.log('Invalid context result:', invalidContext)

    console.log('\n✅ All tests completed successfully!')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

testUnifiedPermissions().catch(console.error)