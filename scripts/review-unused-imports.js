#!/usr/bin/env node

/**
 * <PERSON>ript to systematically review unused imports by UI area
 * Run with: node scripts/review-unused-imports.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Organize files by UI areas for systematic review
const FILES_BY_AREA = {
  'Admin Management': [
    'src/app/admin/manage/components/CreateUserTab.tsx',
    'src/app/admin/manage/components/UpdateUserTab.tsx',
    'src/app/admin/manage/components/SearchUserTab.tsx',
    'src/app/admin/manage/components/TreatyTypeTab.tsx',
    'src/app/admin/manage/components/UserManagementTab.tsx',
    'src/app/admin/manage/components/UserTreatyAssignmentForm.tsx',
    'src/app/admin/manage/components/UserTreatyManagement.tsx',
    'src/app/admin/manage/identification/page.tsx',
    'src/app/admin/manage/layout.tsx',
    'src/app/admin/roles/AdminRolesClient.tsx',
    'src/app/admin/treaties/components/TreatyManagementForm.tsx'
  ],
  'User Management': [
    'src/components/users/manage/CreateUserTab.tsx',
    'src/components/users/manage/UpdateUserTab.tsx',
    'src/components/users/manage/SearchUserTab.tsx',
    'src/components/users/manage/TreatyTypeTab.tsx',
    'src/components/users/manage/UserManagementTab.tsx',
    'src/components/users/manage/UserTreatyAssignmentForm.tsx',
    'src/components/profile/ContactDetailsTab.tsx',
    'src/components/profile/PersonalInfoTab.tsx',
    'src/components/profile/ProfilePhotoTab.tsx',
    'src/components/profile/SecurityTab.tsx'
  ],
  'Treaties & Documents': [
    'src/components/treaties/TreatyCard.tsx',
    'src/components/treaties/TreatyManagement.tsx',
    'src/components/treaties/TreatyList.tsx',
    'src/components/treaties/TreatySearch.tsx',
    'src/components/treaties/TreatyTypeSelector.tsx',
    'src/components/treaties/TreatyTypesManagement.tsx',
    'src/components/treaties/CreateNationTreatyTab.tsx',
    'src/components/treaties/DigitalSignature.tsx',
    'src/components/treaties/FileUpload.tsx',
    'src/components/documents/CreateDirectiveTab.tsx',
    'src/components/documents/CreateOrdinanceTab.tsx'
  ],
  'Settings & Permissions': [
    'src/components/settings/PermissionManager.tsx',
    'src/components/settings/AssignUserPermissions.tsx',
    'src/components/settings/AssignUserRoles.tsx',
    'src/components/settings/LocalPermissionManager.tsx',
    'src/components/settings/PlatformSettingsTab.tsx',
    'src/components/settings/SecurityAuditTab.tsx',
    'src/components/settings/SessionManagementTab.tsx',
    'src/components/settings/SystemSettingsTab.tsx',
    'src/components/settings/TreatyAuditTab.tsx',
    'src/components/settings/UserPermissions.tsx'
  ],
  'Layout & Navigation': [
    'src/components/layout/DashboardLayout.tsx',
    'src/components/layout/Header.tsx',
    'src/components/layout/Sidebar.tsx',
    'src/components/dashboard/TabNavigation.tsx',
    'src/components/admin/VerticalNav.tsx',
    'src/components/treaties/VerticalMenu.tsx'
  ],
  'UI Components': [
    'src/components/ui/checkbox.tsx',
    'src/components/ui/radio-group.tsx',
    'src/components/ui/select.tsx',
    'src/components/ui/switch.tsx',
    'src/components/ui/PermissionFormField.tsx',
    'src/components/ui/api-key-display.tsx'
  ]
};

function printReviewPlan() {
  console.log('\n🔍 UNUSED IMPORTS REVIEW PLAN\n');
  console.log('=====================================\n');

  let totalFiles = 0;

  Object.entries(FILES_BY_AREA).forEach(([area, files]) => {
    console.log(`📁 ${area} (${files.length} files)`);
    console.log('─'.repeat(50));

    files.forEach((file, index) => {
      console.log(`${index + 1:2}. ${file}`);
      totalFiles++;
    });
    console.log('');
  });

  console.log(`📊 Total: ${totalFiles} files to review\n`);
}

function generateESLintCommand(area, files) {
  const fileList = files.map(f => `"${f}"`).join(' ');
  return `npx eslint ${fileList} --rule 'no-unused-vars: error' --format compact`;
}

function printCommands() {
  console.log('🛠️  ESLINT COMMANDS BY AREA\n');
  console.log('===========================\n');

  Object.entries(FILES_BY_AREA).forEach(([area, files]) => {
    const command = generateESLintCommand(area, files);
    console.log(`\n${area}:`);
    console.log(command);
  });
}

function createReviewChecklist() {
  const checklist = `
# Unused Imports Review Checklist

## Instructions
1. Go through each area systematically
2. For each file, run: \`npx eslint <file> --rule 'no-unused-vars: error'\`
3. Review each unused import:
   - Is it actually unused? Remove it
   - Is it used dynamically? Mark as // @ts-ignore or use proper typing
   - Is it for future use? Add TODO comment with timeline

## Areas to Review

${Object.entries(FILES_BY_AREA).map(([area, files]) => `
### ${area}
${files.map(file => `- [ ] ${file}`).join('\n')}
`).join('\n')}

## Completion Checklist
- [ ] Admin Management (11 files)
- [ ] User Management (10 files)
- [ ] Treaties & Documents (11 files)
- [ ] Settings & Permissions (10 files)
- [ ] Layout & Navigation (6 files)
- [ ] UI Components (6 files)
- [ ] Run final lint check: \`npm run lint\`
- [ ] Run build check: \`npm run build\`

## Best Practices
1. **Remove truly unused imports** - Clean code is maintainable code
2. **Use dynamic imports** for conditional imports
3. **Add type-only imports** with \`import type\` for type definitions
4. **Document intentional unused imports** with TODO comments and timelines
5. **Test after changes** - Ensure functionality isn't broken

## Tailwind CSS Integration
After fixing imports, you can add visual indicators during development:
\`\`\`tsx
import { ImportDebugWrapper } from '@/components/dev/ImportDebugWrapper';

<ImportDebugWrapper filePath="/src/components/MyComponent.tsx">
  <MyComponent />
</ImportDebugWrapper>
\`\`\`
`;

  fs.writeFileSync('./UNUSED_IMPORTS_REVIEW.md', checklist);
  console.log('📝 Created UNUSED_IMPORTS_REVIEW.md checklist');
}

// Main execution
console.log('🚀 NWA Alliance Unused Imports Review Tool\n');

const args = process.argv.slice(2);

if (args.includes('--plan')) {
  printReviewPlan();
} else if (args.includes('--commands')) {
  printCommands();
} else if (args.includes('--checklist')) {
  createReviewChecklist();
} else {
  printReviewPlan();
  console.log('\n💡 Usage:');
  console.log('  node scripts/review-unused-imports.js --plan      # Show review plan');
  console.log('  node scripts/review-unused-imports.js --commands  # Show ESLint commands');
  console.log('  node scripts/review-unused-imports.js --checklist # Create markdown checklist');
  console.log('\n🎯 Recommended workflow:');
  console.log('  1. Review plan above');
  console.log('  2. Create checklist: node scripts/review-unused-imports.js --checklist');
  console.log('  3. Go through each area systematically');
  console.log('  4. Test: npm run lint && npm run build');
}