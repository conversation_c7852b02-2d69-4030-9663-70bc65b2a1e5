import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';

const prisma = new PrismaClient();

async function createTestAuthCode() {
  try {
    // Get test user and remote server
    const user = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });
    
    const remoteServer = await prisma.remoteServer.findFirst({
      where: { clientId: 'nwapromote-client-local' }
    });
    
    if (!user || !remoteServer) {
      console.error('User or remote server not found');
      return;
    }
    
    // Create authorization code
    const code = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
    
    const authCode = await prisma.authorizationCode.create({
      data: {
        id: crypto.randomUUID(),
        remoteServerId: remoteServer.id,
        userId: user.id,
        code: code,
        redirectUri: 'http://localhost:3002/api/auth/callback/member-portal',
        scope: 'read:profile',
        expiresAt: expiresAt,
      }
    });
    
    console.log('Authorization code created:');
    console.log('Code:', authCode.code);
    console.log('Expires at:', authCode.expiresAt);
    console.log('User:', user.email);
    console.log('Client:', remoteServer.name);
    
  } catch (error) {
    console.error('Error creating auth code:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestAuthCode();