#!/usr/bin/env node

/**
 * Alternative E2E Testing Script
 * This script provides E2E testing functionality without relying on Cypress
 * Uses Puppeteer for browser automation as a fallback
 */

import puppeteer from 'puppeteer';
import http from 'http';

const BASE_URL = 'http://localhost:3001';
const TIMEOUT = 30000;

async function waitForServer(url, timeout = 30000) {
  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    try {
      await new Promise((resolve, reject) => {
        const req = http.request(url, { method: 'HEAD' }, (res) => {
          if (res.statusCode === 200) {
            resolve();
          } else {
            reject(new Error(`Server responded with status ${res.statusCode}`));
          }
        });

        req.on('error', reject);
        req.setTimeout(5000, () => reject(new Error('Timeout')));
        req.end();
      });

      console.log('✅ Server is running');
      return true;
    } catch (error) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  throw new Error('Server did not start within timeout');
}

async function testWithPuppeteer() {
  console.log('🚀 Starting Puppeteer-based E2E tests...');

  let browser;
  try {
    browser = await puppeteer.launch({
      headless: 'new',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ],
      timeout: TIMEOUT
    });

    const page = await browser.newPage();

    // Set viewport
    await page.setViewport({ width: 1280, height: 720 });

    // Test 1: Homepage loads
    console.log('🧪 Testing homepage...');
    await page.goto(`${BASE_URL}/`, { waitUntil: 'networkidle0', timeout: TIMEOUT });
    await page.waitForSelector('body', { timeout: 10000 });

    const title = await page.title();
    console.log(`✅ Homepage loaded: ${title}`);

    // Test 2: Check for navigation elements
    console.log('🧪 Testing navigation...');
    const navExists = await page.$('nav') !== null;
    console.log(navExists ? '✅ Navigation found' : '❌ Navigation not found');

    // Test 3: Try to access dashboard
    console.log('🧪 Testing dashboard access...');
    try {
      await page.goto(`${BASE_URL}/dashboard`, { waitUntil: 'networkidle0', timeout: TIMEOUT });
      const currentUrl = page.url();
      console.log(`✅ Dashboard page accessed: ${currentUrl}`);
    } catch (error) {
      console.log(`⚠️ Dashboard access issue: ${error.message}`);
    }

    // Test 4: Check for dashboard content
    console.log('🧪 Testing dashboard content...');
    const dashboardContent = await page.evaluate(() => {
      return {
        hasOverview: document.querySelector('[data-testid="overview-tab"]') !== null,
        hasNotifications: document.querySelector('[data-testid="notifications-tab"]') !== null,
        hasServers: document.querySelector('[data-testid="servers-tab"]') !== null,
        hasAnalytics: document.querySelector('[data-testid="analytics-tab"]') !== null,
        hasSecurity: document.querySelector('[data-testid="security-tab"]') !== null,
        hasSystem: document.querySelector('[data-testid="system-tab"]') !== null,
      };
    });

    console.log('Dashboard content check:', dashboardContent);

    // Test 5: API endpoints
    console.log('🧪 Testing API endpoints...');
    const apiTests = await testAPIs();
    console.log('API test results:', apiTests);

    return {
      success: true,
      results: {
        homepage: title,
        navigation: navExists,
        dashboardContent,
        apiTests
      }
    };

  } catch (error) {
    console.error('❌ Puppeteer test failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

async function testAPIs() {
  const endpoints = [
    '/api/dashboard/overview',
    '/api/dashboard/notifications',
    '/api/dashboard/servers',
    '/api/dashboard/analytics',
    '/api/dashboard/security',
    '/api/dashboard/system'
  ];

  const results = {};

  for (const endpoint of endpoints) {
    try {
      const response = await new Promise((resolve, reject) => {
        const req = http.request(`${BASE_URL}${endpoint}`, { method: 'GET' }, (res) => {
          let data = '';
          res.on('data', chunk => data += chunk);
          res.on('end', () => {
            try {
              const parsed = JSON.parse(data);
              resolve({
                status: res.statusCode,
                data: parsed
              });
            } catch (e) {
              resolve({
                status: res.statusCode,
                data: data
              });
            }
          });
        });

        req.on('error', reject);
        req.setTimeout(10000, () => reject(new Error('Timeout')));
        req.end();
      });

      results[endpoint] = {
        status: response.status,
        success: response.status === 200,
        hasData: response.data && typeof response.data === 'object'
      };
    } catch (error) {
      results[endpoint] = {
        status: 0,
        success: false,
        error: error.message
      };
    }
  }

  return results;
}

async function runTests() {
  try {
    // Wait for server
    await waitForServer(`${BASE_URL}/api/users`);

    // Run Puppeteer tests
    const puppeteerResults = await testWithPuppeteer();

    // Run API tests
    const apiResults = await testAPIs();

    // Summary
    const totalTests = Object.keys(apiResults).length + (puppeteerResults.success ? 1 : 0);
    const passedTests = Object.values(apiResults).filter(r => r.success).length + (puppeteerResults.success ? 1 : 0);

    console.log('\n📊 E2E Test Summary:');
    console.log(`✅ Passed: ${passedTests}/${totalTests}`);
    console.log(`📈 Success Rate: ${totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%`);

    if (puppeteerResults.success) {
      console.log('\n🎉 Puppeteer tests passed!');
    } else {
      console.log('\n⚠️ Puppeteer tests failed, but API tests may still work');
    }

    console.log('\n🔍 API Test Details:');
    Object.entries(apiResults).forEach(([endpoint, result]) => {
      console.log(`${result.success ? '✅' : '❌'} ${endpoint}: ${result.status} ${result.error || ''}`);
    });

    return passedTests === totalTests;

  } catch (error) {
    console.error('❌ E2E tests failed:', error.message);
    return false;
  }
}

// Handle script execution
if (require.main === module) {
  runTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(console.error);
}

module.exports = { runTests, testAPIs, testWithPuppeteer };