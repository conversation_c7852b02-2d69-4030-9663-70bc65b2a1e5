#!/usr/bin/env node

/**
 * Cross-Browser Compatibility Testing Script
 * Tests the dashboard across multiple browsers to ensure compatibility
 */

import { chromium, firefox, webkit } from 'playwright';
import http from 'http';

const BROWSERS = [
  { name: 'Chrome', browser: chromium, headless: true },
  { name: 'Firefox', browser: firefox, headless: true },
  { name: 'Safari', browser: webkit, headless: true }
];

const BASE_URL = 'http://localhost:3001';
const TIMEOUT = 30000;

let passedTests = 0;
let failedTests = 0;
let totalTests = 0;

async function testBrowser(browserConfig) {
  console.log(`\n🌐 Testing with ${browserConfig.name}...`);

  const browser = await browserConfig.browser.launch({
    headless: browserConfig.headless
  });

  try {
    const context = await browser.newContext({
      viewport: { width: 1280, height: 720 },
      userAgent: browserConfig.name === 'Safari'
        ? 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15'
        : undefined
    });

    const page = await context.newPage();

    // Test 1: Basic page load
    await testPageLoad(page, 'Dashboard Load', '/dashboard');

    // Test 2: Navigation between tabs
    await testTabNavigation(page, 'Tab Navigation');

    // Test 3: Server health check display
    await testServerHealthDisplay(page, 'Server Health Display');

    // Test 4: Responsive design
    await testResponsiveDesign(page, 'Responsive Design');

    // Test 5: Form interactions
    await testFormInteractions(page, 'Form Interactions');

    await context.close();
  } catch (error) {
    console.log(`❌ ${browserConfig.name} - Browser test failed: ${error.message}`);
    failedTests++;
  } finally {
    await browser.close();
  }
}

async function testPageLoad(page, testName, path) {
  totalTests++;
  try {
    console.log(`  🧪 Testing ${testName}...`);

    const response = await page.goto(`${BASE_URL}${path}`, {
      waitUntil: 'networkidle',
      timeout: TIMEOUT
    });

    if (response.ok()) {
      // Check if page contains expected elements
      const title = await page.title();
      const hasMainContent = await page.locator('main').isVisible();

      if (title && hasMainContent) {
        console.log(`  ✅ ${testName} - Page loaded successfully`);
        passedTests++;
        return true;
      } else {
        console.log(`  ❌ ${testName} - Page missing expected content`);
        failedTests++;
        return false;
      }
    } else {
      console.log(`  ❌ ${testName} - HTTP ${response.status()}`);
      failedTests++;
      return false;
    }
  } catch (error) {
    console.log(`  ❌ ${testName} - Error: ${error.message}`);
    failedTests++;
    return false;
  }
}

async function testTabNavigation(page, testName) {
  totalTests++;
  try {
    console.log(`  🧪 Testing ${testName}...`);

    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-tabs"]', { timeout: 10000 });

    // Test clicking different tabs
    const tabs = [
      '[data-testid="overview-tab"]',
      '[data-testid="servers-tab"]',
      '[data-testid="notifications-tab"]'
    ];

    for (const tabSelector of tabs) {
      try {
        await page.click(tabSelector);
        await page.waitForTimeout(1000); // Wait for tab content to load

        // Verify tab content is visible
        const isVisible = await page.locator('[data-testid="tab-content"]').isVisible();
        if (!isVisible) {
          throw new Error(`Tab content not visible for ${tabSelector}`);
        }
      } catch (error) {
        throw new Error(`Failed to navigate to tab ${tabSelector}: ${error.message}`);
      }
    }

    console.log(`  ✅ ${testName} - Tab navigation works`);
    passedTests++;
    return true;
  } catch (error) {
    console.log(`  ❌ ${testName} - Error: ${error.message}`);
    failedTests++;
    return false;
  }
}

async function testServerHealthDisplay(page, testName) {
  totalTests++;
  try {
    console.log(`  🧪 Testing ${testName}...`);

    // Navigate to servers tab
    await page.click('[data-testid="servers-tab"]');
    await page.waitForTimeout(2000); // Wait for server data to load

    // Check if server cards are displayed
    const serverCards = await page.locator('[data-testid="server-card"]').count();

    if (serverCards > 0) {
      // Check if health status is displayed
      const healthStatus = await page.locator('[data-testid="server-health-status"]').first();
      const isHealthVisible = await healthStatus.isVisible();

      if (isHealthVisible) {
        console.log(`  ✅ ${testName} - Server health display works`);
        passedTests++;
        return true;
      } else {
        console.log(`  ❌ ${testName} - Health status not visible`);
        failedTests++;
        return false;
      }
    } else {
      console.log(`  ❌ ${testName} - No server cards found`);
      failedTests++;
      return false;
    }
  } catch (error) {
    console.log(`  ❌ ${testName} - Error: ${error.message}`);
    failedTests++;
    return false;
  }
}

async function testResponsiveDesign(page, testName) {
  totalTests++;
  try {
    console.log(`  🧪 Testing ${testName}...`);

    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' }
    ];

    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });

      // Check if layout adapts properly
      const mainContent = await page.locator('main');
      const boundingBox = await mainContent.boundingBox();

      if (boundingBox && boundingBox.width > 0) {
        // Verify content is still accessible
        const isVisible = await mainContent.isVisible();
        if (!isVisible) {
          throw new Error(`Content not visible on ${viewport.name}`);
        }
      } else {
        throw new Error(`Invalid layout on ${viewport.name}`);
      }
    }

    console.log(`  ✅ ${testName} - Responsive design works`);
    passedTests++;
    return true;
  } catch (error) {
    console.log(`  ❌ ${testName} - Error: ${error.message}`);
    failedTests++;
    return false;
  }
}

async function testFormInteractions(page, testName) {
  totalTests++;
  try {
    console.log(`  🧪 Testing ${testName}...`);

    // Test search functionality if available
    const searchInput = await page.locator('input[type="search"], input[placeholder*="search"]').first();
    if (await searchInput.isVisible()) {
      await searchInput.fill('test');
      await page.waitForTimeout(500);

      // Check if search triggers expected behavior
      const hasResults = await page.locator('[data-testid="search-results"], .search-results').count() > 0 ||
                        await page.locator('main').isVisible(); // Main content should still be visible

      if (hasResults) {
        console.log(`  ✅ ${testName} - Form interactions work`);
        passedTests++;
        return true;
      } else {
        console.log(`  ❌ ${testName} - Search functionality not working`);
        failedTests++;
        return false;
      }
    } else {
      // If no search input, just verify basic interactions work
      const buttons = await page.locator('button').count();
      if (buttons > 0) {
        console.log(`  ✅ ${testName} - Basic interactions available`);
        passedTests++;
        return true;
      } else {
        console.log(`  ❌ ${testName} - No interactive elements found`);
        failedTests++;
        return false;
      }
    }
  } catch (error) {
    console.log(`  ❌ ${testName} - Error: ${error.message}`);
    failedTests++;
    return false;
  }
}

async function checkServer() {
  console.log('🔍 Checking if development server is running...');

  return new Promise((resolve) => {
    const req = http.request(`${BASE_URL}/api/dashboard/overview`, {
      method: 'GET',
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json',
      }
    }, (res) => {
      if (res.statusCode === 200 || res.statusCode === 401) {
        console.log('✅ Development server is running');
        resolve(true);
      } else {
        console.log('❌ Development server is not responding correctly');
        resolve(false);
      }
    });

    req.on('error', () => {
      console.log('❌ Development server is not running');
      console.log('💡 Please start the server with: npm run dev');
      resolve(false);
    });

    req.on('timeout', () => {
      req.destroy();
      console.log('❌ Development server timeout');
      resolve(false);
    });

    req.end();
  });
}

async function runCrossBrowserTests() {
  console.log('🚀 Starting Cross-Browser Compatibility Tests...\n');

  const serverRunning = await checkServer();

  if (!serverRunning) {
    console.log('\n❌ Cannot run tests: Development server is not running');
    process.exit(1);
  }

  console.log('📋 Running tests across multiple browsers...\n');

  for (const browserConfig of BROWSERS) {
    await testBrowser(browserConfig);
  }

  // Summary
  console.log('\n📊 Cross-Browser Test Summary:');
  console.log(`🧪 Total Tests: ${totalTests}`);
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📈 Success Rate: ${totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%`);

  if (failedTests === 0) {
    console.log('\n🎉 All cross-browser tests passed! Dashboard is compatible across browsers.');
    process.exit(0);
  } else {
    console.log('\n⚠️  Some tests failed. Please check browser compatibility issues.');
    process.exit(1);
  }
}

// Handle script execution
if (import.meta.url === `file://${process.argv[1]}`) {
  runCrossBrowserTests().catch(console.error);
}

export { testBrowser, testPageLoad, testTabNavigation, testServerHealthDisplay, testResponsiveDesign, testFormInteractions };