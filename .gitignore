# NWA Alliance - Comprehensive .gitignore

# Dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
.agent-os/

# Testing
/coverage
/.nyc_output
junit.xml

# Next.js
/.next/
/out/
/.vercel

# Production builds
/build
/dist

# Environment variables (SECURITY CRITICAL)
.env*
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor files and directories
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Prisma
/prisma/migrations/*/migration.sql

# Cypress
/cypress/videos/
/cypress/screenshots/
/cypress/reports/
*.mp4
*.png
*.html
*.json

# Database files
*.db
*.sqlite
*.sqlite3

# Cache directories
.cache/
.parcel-cache/

# Docker
.dockerignore

# Security - Sensitive files
*.key
*.pem
*.crt
*.p12
*.pfx
id_rsa
id_dsa
*.pem
*.key

# Development tools
.qwen/
test-minio.js
test-minio.mjs
.claude/settings.local.json
cookies.txt
.claude/settings.local.json

# File watching
.watchmanconfig

# Backup files (exclude from version control)
/backup
*.backup
*.bak

# Package manager lock files (optional - remove if you want to commit lock files)
# package-lock.json
# yarn.lock

# Optional: if you use yarn workspaces
.yarn/install-state.gz

# Optional: if you use pnpm
pnpm-lock.yaml

# Temporary files and caches
*.tmp
*.temp
*.cache
*.log*

# System files
.fuse_hidden*

# Network files
.network-sync*

# Archive files
*.tar
*.zip
*.rar
*.7z

# Minified files
*.min.js
*.min.css

# Compiled binaries
*.exe
*.dll
*.dylib

# Python (if used)
__pycache__/
*.py[cod]
*$py.class
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Kubernetes
.kube/

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Ansible
*.retry

# Docker volumes
data/
volumes/

# Session storage
sessions/
*.session

# Upload directories
uploads/
storage/
