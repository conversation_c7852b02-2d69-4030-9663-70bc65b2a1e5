// ESLint v9+ flat config for TypeScript React with browser globals
import tseslint from '@typescript-eslint/eslint-plugin';
import tsParser from '@typescript-eslint/parser';
import next from '@next/eslint-plugin-next';
import reactHooks from 'eslint-plugin-react-hooks';

export default [
  // JavaScript files with basic ESLint rules
  {
    files: ['**/*.cjs'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'commonjs',
    },
    rules: {
      'no-unused-vars': 'warn',
      'no-undef': 'off', // Allow global variables in JS files
      'no-case-declarations': 'warn',
      'no-useless-escape': 'warn',
      'no-constant-condition': 'warn',
    },
  },
  // ES Module JavaScript files
  {
    files: ['**/*.js'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        fetch: true,
        console: true,
        setTimeout: true,
        clearTimeout: true,
        process: true,
        Buffer: true,
        setInterval: true,
        clearInterval: true,
        document: true,
        window: true,
        require: true,
        module: true,
        __dirname: true,
        __filename: true,
        performance: true,
        global: true,
      },
    },
    rules: {
      'no-unused-vars': 'warn',
      'no-undef': 'off', // Allow global variables in JS files
      'no-case-declarations': 'warn',
      'no-useless-escape': 'warn',
      'no-constant-condition': 'warn',
    },
  },
  // Cypress config with special globals
  {
    files: ['cypress.config.ts'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        fetch: true,
        console: true,
        setTimeout: true,
        clearTimeout: true,
        process: true,
        Buffer: true,
        setInterval: true,
        clearInterval: true,
        require: true,
        module: true,
        __dirname: true,
        __filename: true,
        performance: true,
        global: true,
        SEED_ORDINANCE_DOCUMENT_IDS: true,
        ProfileDefaultsEntry: true,
        ProfileDefaultsProfile: true,
        _stateProvince: true,
        _postcodeZip: true,
      },
    },
    rules: {
      'no-unused-vars': 'warn',
      'no-undef': 'off', // Allow Cypress globals
      'no-case-declarations': 'warn',
      'no-useless-escape': 'warn',
      'no-constant-condition': 'warn',
    },
  },
  // TypeScript files with comprehensive rules
  {
    files: ['**/*.{ts,tsx}'],
    plugins: {
      '@next/next': next,
      'react-hooks': reactHooks,
      '@typescript-eslint': tseslint
    },
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        project: './tsconfig.json',
      },
      globals: {
        fetch: true,
        console: true,
        setTimeout: true,
        clearTimeout: true,
        process: true,
        Buffer: true,
        setInterval: true,
        clearInterval: true,
        document: true,
        window: true,
        require: true,
        module: true,
        __dirname: true,
        __filename: true,
        performance: true,
        global: true,
      },
    },
    rules: {
      // Next.js recommended rules
      '@next/next/no-html-link-for-pages': 'error',
      '@next/next/no-img-element': 'warn',
      '@next/next/no-sync-scripts': 'error',
      '@next/next/google-font-display': 'warn',
      '@next/next/google-font-preconnect': 'warn',

      // React hooks rules
      'react-hooks/exhaustive-deps': 'warn',

      // TypeScript rules
      '@typescript-eslint/no-unused-vars': 'error',
      '@typescript-eslint/no-redeclare': 'error',
      '@typescript-eslint/no-unused-expressions': 'error',
      '@typescript-eslint/consistent-type-imports': 'warn',

      // General rules
      'no-undef': 'error',
      'no-case-declarations': 'error',
      'no-useless-escape': 'warn',
      'no-constant-condition': 'warn',
      'no-constant-binary-expression': 'error',
    },
  },
  // Cypress test files with relaxed rules
  {
    files: ['cypress/**/*.cy.ts', 'cypress/**/*.cy.js'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        fetch: true,
        console: true,
        setTimeout: true,
        clearTimeout: true,
        process: true,
        Buffer: true,
        setInterval: true,
        clearInterval: true,
        document: true,
        window: true,
        require: true,
        module: true,
        __dirname: true,
        __filename: true,
        performance: true,
        global: true,
        cy: true,
        Cypress: true,
        describe: true,
        it: true,
        beforeEach: true,
        afterEach: true,
        before: true,
        after: true,
      },
    },
    rules: {
      // Allow unused variables in test files (they may be used indirectly)
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      // Allow undeclared globals in test files
      'no-undef': 'off',
      // Allow case declarations in switch statements
      'no-case-declarations': 'off',
      // Allow unnecessary escape characters in regex
      'no-useless-escape': 'off',
      // Allow constant conditions and binary expressions
      'no-constant-condition': 'off',
      'no-constant-binary-expression': 'off',
      // Allow redeclare (TypeScript interfaces)
      'no-redeclare': 'off',
      '@typescript-eslint/no-redeclare': 'off',
      // Allow react-hooks/exhaustive-deps (disabled in code)
      'react-hooks/exhaustive-deps': 'off',
      // Allow unused expressions in Cypress tests
      '@typescript-eslint/no-unused-expressions': 'off',
    },
  },
  // Test files configuration with relaxed rules
  {
    files: ['**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts', '**/*.spec.tsx', '__tests__/**/*', 'tests/**/*', 'jest.config.*'],
    rules: {
      // Allow unused variables in test files (they may be used indirectly)
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      // Allow undeclared globals in test files
      'no-undef': 'off',
      // Allow case declarations in switch statements
      'no-case-declarations': 'off',
      // Allow unnecessary escape characters in regex
      'no-useless-escape': 'off',
      // Allow constant conditions and binary expressions
      'no-constant-condition': 'off',
      'no-constant-binary-expression': 'off',
      // Allow redeclare (TypeScript interfaces)
      'no-redeclare': 'off',
      '@typescript-eslint/no-redeclare': 'off',
      // Allow react-hooks/exhaustive-deps (disabled in code)
      'react-hooks/exhaustive-deps': 'off',
    },
  },
  {
    ignores: [
      'node_modules/',
      '.next/',
      'out/',
      'build/',
      'dist/',
      'backup/',
      '.env*',
      '*.log',
      'coverage/',
      '.nyc_output/',
      'tmp/',
      'temp/',
      '.DS_Store',
      'Thumbs.db',
      '.vscode/',
      '.idea/',
      'next-env.d.ts',
    ],
  },
];