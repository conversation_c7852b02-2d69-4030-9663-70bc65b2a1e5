// Simple test to verify authorization middleware functionality
const { requireAuth, AuthenticationError, AuthorizationError } = require('./src/lib/middleware/require-auth');

// Mock NextRequest
class MockNextRequest {
  constructor(url = 'http://localhost:3001/api/test') {
    this.url = url;
  }
}

// Mock session data
const mockSession = {
  user: {
    id: 'user-1',
    email: '<EMAIL>',
    name: 'Test User',
    roles: ['admin'],
    permissions: ['users:read', 'users:write']
  }
};

// Mock getServerSession
const mockGetServerSession = async () => mockSession;

// Test basic authentication
async function testAuth() {
  console.log('Testing authentication middleware...');
  
  try {
    // Mock the authOptions import
    const authOptions = {};
    
    // Test requireAuth with admin role
    const request = new MockNextRequest();
    
    // This should work since we have admin role
    const authContext = await requireAuth(request, {
      requireRoles: ['admin'],
      auditLogging: false
    });
    
    console.log('✅ Admin authentication test passed');
    console.log('Auth context:', authContext);
    
    // Test requireAuth with non-admin role (should fail)
    try {
      const nonAdminSession = {
        user: {
          id: 'user-2',
          email: '<EMAIL>',
          name: 'Regular User',
          roles: ['user'],
          permissions: ['users:read']
        }
      };
      
      // Temporarily replace the session
      const originalGetServerSession = require('next-auth/next').getServerSession;
      require('next-auth/next').getServerSession = async () => nonAdminSession;
      
      await requireAuth(request, {
        requireRoles: ['admin'],
        auditLogging: false
      });
      
      console.log('❌ Non-admin authentication test should have failed');
      
      // Restore original function
      require('next-auth/next').getServerSession = originalGetServerSession;
      
    } catch (error) {
      if (error instanceof AuthorizationError) {
        console.log('✅ Non-admin authorization test passed (correctly rejected)');
      } else {
        console.log('❌ Non-admin authorization test failed with unexpected error:', error.message);
      }
    }
    
  } catch (error) {
    console.log('❌ Authentication test failed:', error.message);
  }
}

// Run the test
testAuth();