/// <reference path="./cypress/support/cypress-task-types.d.ts" />
import { defineConfig } from 'cypress'
import { PrismaClient } from '@prisma/client'
import bcryptjs from 'bcryptjs'
import * as crypto from 'crypto'

const prisma = new PrismaClient()

const ensurePasswordResetTokenTable = async () => {
  await prisma.$executeRawUnsafe(`
    CREATE TABLE IF NOT EXISTS "public"."password_reset_tokens" (
      "id" TEXT NOT NULL,
      "user_id" TEXT NOT NULL,
      "token" TEXT NOT NULL,
      "expires_at" TIMESTAMP(3) NOT NULL,
      "used_at" TIMESTAMP(3),
      "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
      CONSTRAINT "password_reset_tokens_pkey" PRIMARY KEY ("id"),
      CONSTRAINT "password_reset_tokens_user_id_fkey"
        FOREIGN KEY ("user_id")
        REFERENCES "public"."users"("id")
        ON DELETE CASCADE
        ON UPDATE CASCADE
    );
  `)

  await prisma.$executeRawUnsafe(`
    CREATE UNIQUE INDEX IF NOT EXISTS "password_reset_tokens_token_key"
      ON "public"."password_reset_tokens"("token");
  `)

  await prisma.$executeRawUnsafe(`
    CREATE INDEX IF NOT EXISTS "password_reset_tokens_user_id_idx"
      ON "public"."password_reset_tokens"("user_id");
  `)

  await prisma.$executeRawUnsafe(`
    CREATE INDEX IF NOT EXISTS "password_reset_tokens_expires_at_idx"
      ON "public"."password_reset_tokens"("expires_at");
  `)
}

const SEED_TREATY_TYPE_IDS = ['seed-treaty-type-001']
const SEED_TREATY_IDS = ['seed-treaty-001']
const SEED_ORDINANCE_IDS = ['seed-ordinance-001']
const SEED_ORDINANCE_DOCUMENT_IDS = ['seed-ordinance-doc-001']
const SEED_NOTIFICATION_IDS = ['seed-notification-001', 'seed-notification-002']
const SEED_AUDIT_LOG_IDS = ['seed-audit-001', 'seed-audit-002']
const SEED_REMOTE_SERVER_CLIENT_IDS = ['nwapromote-client-local']



const PROFILE_DEFAULTS = {
  '<EMAIL>': {
    userName: 'Test User',
    profile: {
      firstName: 'Test',
      lastName: 'User',
      personalEmail: '<EMAIL>',
      nwaEmail: '<EMAIL>',
      phone: '******-0123',
      mobile: '******-0124',
      streetAddress1: '123 Main Street',
      streetAddress2: 'Apt 4B',
      town: 'Houston',
      stateProvince: null,
      postcodeZip: '77001',
      bio: 'Test user for development purposes with complete profile information.',
      peaceAmbassadorNumber: 'NWA-TEST-001'
    },
    countryCode: 'US',
    cityName: 'Houston',
    regionCode: 'TX'
  },
  '<EMAIL>': {
    userName: 'Cypress Admin',
    profile: {
      firstName: 'Cypress',
      lastName: 'Admin',
      personalEmail: '<EMAIL>',
      nwaEmail: '<EMAIL>',
      phone: '******-0101',
      mobile: '******-0199',
      streetAddress1: '450 Cypress Integration Way',
      streetAddress2: 'Suite 600',
      town: 'Austin',
      stateProvince: 'TX',
      postcodeZip: '73301',
      bio: 'System administrator account used for automated Cypress coverage.',
      peaceAmbassadorNumber: 'NWA-ADMIN-001'
    },
    countryCode: 'US',
    cityName: 'Austin',
    regionCode: 'TX'
  },
  '<EMAIL>': {
    userName: 'TwoFactor Tester',
    profile: {
      firstName: 'TwoFactor',
      lastName: 'Tester',
      personalEmail: null,
      nwaEmail: '<EMAIL>',
      phone: null,
      mobile: null,
      streetAddress1: null,
      streetAddress2: null,
      town: null,
      stateProvince: null,
      postcodeZip: null,
      bio: null,
      peaceAmbassadorNumber: null
    },
    countryCode: 'US',
    cityName: 'New York',
    regionCode: null
  }
}

export default defineConfig({
  e2e: {
    baseUrl: `http://localhost:${process.env.PORT || 3001}`,
    supportFile: 'cypress/support/e2e.ts',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    // Enhanced video and screenshot configuration
    video: true,
    videoCompression: 32,
    screenshotOnRunFailure: true,
    screenshotsFolder: 'cypress/screenshots',
    videosFolder: 'cypress/videos',
    // Enhanced viewport and device testing
    viewportWidth: 1280,
    viewportHeight: 720,
    // Increase timeouts for slower environments
    defaultCommandTimeout: 15000,
    requestTimeout: 20000,
    responseTimeout: 20000,
    pageLoadTimeout: 30000,
    // Retry configuration for flaky tests
    retries: {
      runMode: 2,
      openMode: 0,
    },
    // Enhanced browser security settings
    chromeWebSecurity: false,
    // Experimental features for better stability
    experimentalStudio: true,
    experimentalWebKitSupport: true,
    // Environment variables
    env: {
      apiUrl: process.env.API_URL || 'http://localhost:3001/api',
      testUser: process.env.CYPRESS_TEST_USER || '<EMAIL>',
      testPassword: process.env.CYPRESS_TEST_PASSWORD || 'password123',
    },
    setupNodeEvents(on, config) {
      // Filter out Electron browser and use only Chromium-based browsers
      on('before:browser:launch', (browser, launchOptions) => {
        // Log browser launch info for debugging
        console.log('Launching browser:', browser.name);
        console.log('Launch options:', launchOptions.args);

        // Add necessary flags for Chromium-based browsers
        if (browser.family === 'chromium' && browser.name !== 'electron') {
          launchOptions.args.push('--no-sandbox');
          launchOptions.args.push('--disable-dev-shm-usage');
          launchOptions.args.push('--disable-gpu');
        }

        return launchOptions;
      });

      // Return config with filtered browsers (exclude Electron)
      const filteredBrowsers = config.browsers.filter(
        (b) => b.family === 'chromium' && b.name !== 'electron'
      );

      config.browsers = filteredBrowsers;

      on('task', {
        'db:reset-twofactor': async (email) => {
          if (typeof email !== 'string') {
            throw new Error('Email is required for db:reset-twofactor task')
          }

          const user = await prisma.user.findUnique({ where: { email } })

          if (!user) {
            console.warn(`No user found for db:reset-twofactor with email ${email}`)
            return null
          }

          await prisma.user.update({
            where: { email },
            data: {
              twoFactorEnabled: false,
              twoFactorSecret: null,
              backupCodes: [],
            },
          })

          return true
        },
        'db:reset-user-profile': async (payload) => {
          const email = typeof payload === 'string' ? payload : payload?.email
          if (typeof email !== 'string') {
            throw new Error('Email is required for db:reset-user-profile task')
          }

          const defaults = PROFILE_DEFAULTS[email]
          if (!defaults) {
            console.warn(`No profile defaults registered for ${email}`)
            return null
          }

          const user = await prisma.user.findUnique({ where: { email }, include: { profile: true } })
          if (!user) {
            console.warn(`No user found for ${email}`)
            return null
          }

          if (defaults.userName) {
            await prisma.user.update({ where: { email }, data: { name: defaults.userName } })
          }

          const rawProfileDefaults = { ...defaults.profile }
          const stateProvinceValue = rawProfileDefaults.stateProvince ?? user.profile?.regionText ?? null
          const postalCodeValue = rawProfileDefaults.postcodeZip ?? user.profile?.postalCode ?? null

          const { stateProvince: _stateProvince, postcodeZip: _postcodeZip, ...profileFields } = rawProfileDefaults
          const profileUpdate = { ...profileFields }

          Object.keys(profileUpdate).forEach((key) => {
            if (profileUpdate[key] === undefined) {
              delete profileUpdate[key]
            }
          })

          if (stateProvinceValue !== undefined) {
            profileUpdate.regionText = stateProvinceValue
          }
          if (postalCodeValue !== undefined) {
            profileUpdate.postalCode = postalCodeValue
          }

          let countryId = user.profile?.countryId ?? null
          let cityId = user.profile?.cityId ?? null
          let regionId = user.profile?.regionId ?? null

          if (defaults.countryCode) {
            const country = await prisma.country.findUnique({ where: { code: defaults.countryCode } })
            countryId = country?.id ?? null
          }

          if (defaults.cityName && countryId) {
            const city = await prisma.city.findFirst({
              where: {
                name: defaults.cityName,
                countryId,
              },
            })
            cityId = city?.id ?? null
          }

          if (defaults.regionCode && countryId) {
            const region = await prisma.region.findFirst({
              where: {
                code: defaults.regionCode,
                countryId,
              },
            })
            regionId = region?.id ?? null
          }

          profileUpdate.countryId = countryId
          if (defaults.cityName !== undefined) {
            profileUpdate.cityId = cityId
          }
          if (defaults.regionCode !== undefined) {
            profileUpdate.regionId = regionId
          }

          const derivedFirstName = user.name?.split(' ')[0] ?? 'Test'
          const derivedLastName = (() => {
            const parts = user.name?.split(' ') ?? []
            const remainder = parts.slice(1).join(' ')
            return remainder || 'User'
          })()

          const prismaProfileUpdate = profileUpdate

          await prisma.userProfile.upsert({
            where: { userId: user.id },
            update: prismaProfileUpdate,
            create: {
              userId: user.id,
              firstName: profileUpdate.firstName ?? derivedFirstName,
              lastName: profileUpdate.lastName ?? derivedLastName,
              personalEmail: profileUpdate.personalEmail ?? null,
              nwaEmail: profileUpdate.nwaEmail ?? null,
              phone: profileUpdate.phone ?? null,
              mobile: profileUpdate.mobile ?? null,
              streetAddress1: profileUpdate.streetAddress1 ?? '',
              streetAddress2: profileUpdate.streetAddress2 ?? null,
              town: profileUpdate.town ?? null,
              regionText: profileUpdate.regionText ?? null,
              postalCode: profileUpdate.postalCode ?? null,
              bio: profileUpdate.bio ?? null,
              peaceAmbassadorNumber: profileUpdate.peaceAmbassadorNumber ?? null,
              countryId,
              cityId,
              regionId,
            },
          })

          return true
        },
        'db:cleanup-treaties': async () => {
          await prisma.userTreaty.deleteMany({ where: { treatyId: { notIn: SEED_TREATY_IDS } } })
          await prisma.treatyTreatyType.deleteMany({ where: { treatyId: { notIn: SEED_TREATY_IDS } } })
          await prisma.userTreatyType.deleteMany({ where: { treatyTypeId: { notIn: SEED_TREATY_TYPE_IDS } } })
          await prisma.treaty.deleteMany({ where: { id: { notIn: SEED_TREATY_IDS } } })
          return true
        },
        'db:cleanup-ordinances': async () => {
          await prisma.ordinanceDocument.deleteMany({ where: { ordinanceId: { notIn: SEED_ORDINANCE_IDS } } })
          await prisma.ordinance.deleteMany({ where: { id: { notIn: SEED_ORDINANCE_IDS } } })
          await prisma.ordinance.updateMany({
            where: { id: { in: SEED_ORDINANCE_IDS } },
            data: {
              status: 'ACTIVE',
              notes: 'Seed ordinance for test coverage.',
            },
          })
          return true
        },
        'db:reset-notifications': async () => {
          await prisma.notification.deleteMany({ where: { id: { notIn: SEED_NOTIFICATION_IDS } } })

          await prisma.notification.update({
            where: { id: 'seed-notification-001' },
            data: { readBy: [] },
          }).catch(() => null)

          const admin = await prisma.user.findUnique({ where: { email: '<EMAIL>' } })

          await prisma.notification.update({
            where: { id: 'seed-notification-002' },
            data: { readBy: admin ? [admin.id] : [] },
          }).catch(() => null)

          return true
        },
        'db:reset-audit-logs': async () => {
          await prisma.auditLog.deleteMany({ where: { id: { notIn: SEED_AUDIT_LOG_IDS } } })
          return true
        },
        'db:reset-system-settings': async () => {
          await prisma.systemSetting.upsert({
            where: { id: 1 },
            update: {
              securityLevel: 'MODERATE',
              maintenanceMode: false,
              maxLoginAttempts: 5,
              sessionTimeout: 3600,
              passwordMinLength: 8,
              passwordRequireUpper: true,
              passwordRequireLower: true,
              passwordRequireNumber: true,
              passwordRequireSpecial: true,
              twoFactorAuthEnabled: false,
            },
            create: {
              id: 1,
              securityLevel: 'MODERATE',
              maintenanceMode: false,
              maxLoginAttempts: 5,
              sessionTimeout: 3600,
              passwordMinLength: 8,
              passwordRequireUpper: true,
              passwordRequireLower: true,
              passwordRequireNumber: true,
              passwordRequireSpecial: true,
              twoFactorAuthEnabled: false,
            },
          })

          return true
        },
        'db:cleanup-remote-servers': async () => {
          await prisma.remoteServer.deleteMany({ where: { clientId: { notIn: SEED_REMOTE_SERVER_CLIENT_IDS } } })
          return true
        },
        'db:set-twofactor-required': async (enabled) => {
          const flag = typeof enabled === 'boolean' ? enabled : enabled === 'true' || enabled === 1
          await prisma.systemSetting.upsert({
            where: { id: 1 },
            update: { twoFactorAuthEnabled: flag },
            create: {
              id: 1,
              securityLevel: 'MODERATE',
              maintenanceMode: false,
              maxLoginAttempts: 5,
              sessionTimeout: 3600,
              passwordMinLength: 8,
              passwordRequireUpper: true,
              passwordRequireLower: true,
              passwordRequireNumber: true,
              passwordRequireSpecial: true,
              twoFactorAuthEnabled: flag,
            },
          })

          return true
        },
        'db:create-password-reset-token': async (email) => {
          if (typeof email !== 'string') {
            throw new Error('Email is required for db:create-password-reset-token task')
          }

          const user = await prisma.user.findUnique({ where: { email } })
          if (!user) {
            console.warn(`No user found for db:create-password-reset-token with email ${email}`)
            return null
          }

          await ensurePasswordResetTokenTable()

          await prisma.passwordResetToken.deleteMany({ where: { userId: user.id } })

          const token = crypto.randomBytes(32).toString('hex')
          const expiresAt = new Date(Date.now() + 2 * 60 * 60 * 1000)

          await prisma.passwordResetToken.create({
            data: {
              userId: user.id,
              token,
              expiresAt,
            },
          })

          return token
        },
        'db:create-email-verification-token': async (email) => {
          if (typeof email !== 'string') {
            throw new Error('Email is required for db:create-email-verification-token task')
          }

          const user = await prisma.user.findUnique({ where: { email } })
          if (!user) {
            console.warn(`No user found for db:create-email-verification-token with email ${email}`)
            return null
          }

          // Clean up any existing tokens for this user
          await prisma.verificationToken.deleteMany({
            where: { identifier: email }
          })

          const token = crypto.randomBytes(32).toString('hex')
          const expires = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

          await prisma.verificationToken.create({
            data: {
              identifier: email,
              token: token,
              expires: expires,
            },
          })

          return token
        },
        'db:reset-password': async (payload) => {
          const email = typeof payload === 'string' ? payload : payload?.email
          const password = typeof payload === 'object' && payload?.password ? payload.password : 'password123'

          if (typeof email !== 'string') {
            throw new Error('Email is required for db:reset-password task')
          }

          const hash = await bcryptjs.hash(password, 10)

          await prisma.user.update({
            where: { email },
            data: {
              passwordHash: hash,
            },
          }).catch((error) => {
            console.warn(`Unable to reset password for ${email}:`, error.message)
          })

          return true
        },
      })

      // Enhanced test reporting
      on('after:spec', (spec, results) => {
        if (results && results.video) {
          console.log('Video recorded for spec:', spec.name);
        }
      });

      // Enhanced screenshot capture
      on('after:screenshot', (details) => {
        console.log('Screenshot captured:', details.path);
      });

      return config;
    },
  },
})
