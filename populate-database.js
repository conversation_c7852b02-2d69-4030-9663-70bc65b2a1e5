const fs = require('fs');
const path = require('path');

// Read the SQL files
const countriesSql = fs.readFileSync(path.join(__dirname, 'countries-cities-expanded-complete.sql'), 'utf8');
const categoriesSql = fs.readFileSync(path.join(__dirname, 'categories-subcategories-dump.sql'), 'utf8');

// Parse countries data
const countries = [];
const countryInserts = countriesSql.match(/INSERT INTO public\.countries .*?;/gs);
if (countryInserts && countryInserts.length > 0) {
  const values = countryInserts[0].match(/VALUES \((.*?)\);/s);
  if (values) {
    const rows = values[1].split('), (');
    rows.forEach(row => {
      const cleanRow = row.replace(/[()]/g, '');
      const parts = cleanRow.split(', ');
      if (parts.length >= 3) {
        countries.push({
          name: parts[1].replace(/'/g, ''),
          code: parts[2].replace(/'/g, '')
        });
      }
    });
  }
}

// Parse cities data
const cities = [];
const cityInserts = countriesSql.match(/INSERT INTO public\.cities .*?;/gs);
if (cityInserts && cityInserts.length > 0) {
  const values = cityInserts[0].match(/VALUES \((.*?)\);/s);
  if (values) {
    const rows = values[1].split('), (');
    rows.forEach(row => {
      const cleanRow = row.replace(/[()]/g, '');
      const parts = cleanRow.split(', ');
      if (parts.length >= 3) {
        cities.push({
          name: parts[1].replace(/'/g, ''),
          countryId: parseInt(parts[2])
        });
      }
    });
  }
}

// Parse categories data
const categories = [];
const categoryInserts = categoriesSql.match(/INSERT INTO public\.categories .*?;/gs);
if (categoryInserts && categoryInserts.length > 0) {
  const values = categoryInserts[0].match(/VALUES \((.*?)\);/s);
  if (values) {
    const rows = values[1].split('), (');
    rows.forEach(row => {
      const cleanRow = row.replace(/[()]/g, '');
      const parts = cleanRow.split(', ');
      if (parts.length >= 3) {
        categories.push({
          name: parts[1].replace(/'/g, ''),
          description: parts[2] === 'NULL' ? '' : parts[2].replace(/'/g, '')
        });
      }
    });
  }
}

// Parse subcategories data
const subcategories = [];
const subcategoryInserts = categoriesSql.match(/INSERT INTO public\.subcategories .*?;/gs);
if (subcategoryInserts && subcategoryInserts.length > 0) {
  const values = subcategoryInserts[0].match(/VALUES \((.*?)\);/s);
  if (values) {
    const rows = values[1].split('), (');
    rows.forEach(row => {
      const cleanRow = row.replace(/[()]/g, '');
      const parts = cleanRow.split(', ');
      if (parts.length >= 4) {
        subcategories.push({
          name: parts[1].replace(/'/g, ''),
          description: parts[2] === 'NULL' ? '' : parts[2].replace(/'/g, ''),
          categoryId: parseInt(parts[3])
        });
      }
    });
  }
}

console.log(`Parsed ${countries.length} countries`);
console.log(`Parsed ${cities.length} cities`);
console.log(`Parsed ${categories.length} categories`);
console.log(`Parsed ${subcategories.length} subcategories`);

// Function to make API requests
async function makeRequest(endpoint, method = 'POST', data = null) {
  const url = `http://localhost:3001${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const result = await response.json();

    if (!response.ok) {
      console.error(`Error ${response.status}:`, result);
      return null;
    }

    return result;
  } catch (error) {
    console.error('Network error:', error);
    return null;
  }
}

// Populate database
async function populateDatabase() {
  console.log('Starting database population...');

  // Create countries
  console.log('Creating countries...');
  const createdCountries = [];
  for (let i = 0; i < countries.length; i++) {
    const country = countries[i];
    console.log(`Creating country ${i + 1}/${countries.length}: ${country.name}`);

    const result = await makeRequest('/api/countries', 'POST', country);
    if (result) {
      createdCountries.push({ ...country, id: result.id });
    }

    // Small delay to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 50));
  }

  // Create categories
  console.log('Creating categories...');
  const createdCategories = [];
  for (let i = 0; i < categories.length; i++) {
    const category = categories[i];
    console.log(`Creating category ${i + 1}/${categories.length}: ${category.name}`);

    const result = await makeRequest('/api/categories', 'POST', category);
    if (result) {
      createdCategories.push({ ...category, id: result.id });
    }

    await new Promise(resolve => setTimeout(resolve, 50));
  }

  // Create cities (need to map country IDs)
  console.log('Creating cities...');
  for (let i = 0; i < cities.length; i++) {
    const city = cities[i];
    // Find the corresponding country by original ID
    const countryIndex = city.countryId - 31; // Original IDs start from 31
    if (countryIndex >= 0 && countryIndex < createdCountries.length) {
      const countryId = createdCountries[countryIndex].id;
      const cityData = {
        name: city.name,
        countryId: countryId
      };

      console.log(`Creating city ${i + 1}/${cities.length}: ${city.name}`);

      await makeRequest('/api/cities', 'POST', cityData);
    }

    await new Promise(resolve => setTimeout(resolve, 50));
  }

  // Create subcategories
  console.log('Creating subcategories...');
  for (let i = 0; i < subcategories.length; i++) {
    const subcategory = subcategories[i];
    // Find the corresponding category by original ID
    const categoryIndex = subcategory.categoryId - 6; // Original IDs start from 6
    if (categoryIndex >= 0 && categoryIndex < createdCategories.length) {
      const categoryId = createdCategories[categoryIndex].id;
      const subcategoryData = {
        name: subcategory.name,
        description: subcategory.description,
        categoryId: categoryId
      };

      console.log(`Creating subcategory ${i + 1}/${subcategories.length}: ${subcategory.name}`);

      await makeRequest('/api/subcategories', 'POST', subcategoryData);
    }

    await new Promise(resolve => setTimeout(resolve, 50));
  }

  console.log('Database population completed!');
  console.log(`Created ${createdCountries.length} countries`);
  console.log(`Created ${createdCategories.length} categories`);
  console.log(`Created ${cities.length} cities`);
  console.log(`Created ${subcategories.length} subcategories`);
}

// Run the population script
populateDatabase().catch(console.error);