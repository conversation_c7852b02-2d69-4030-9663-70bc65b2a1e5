const fs = require('fs');
const path = require('path');

// Read the SQL files
const countriesSql = fs.readFileSync(path.join(__dirname, 'countries-cities-expanded-complete.sql'), 'utf8');
const categoriesSql = fs.readFileSync(path.join(__dirname, 'categories-subcategories-dump.sql'), 'utf8');

// Extract INSERT statements for countries
const countryInserts = countriesSql.match(/INSERT INTO public\.countries .*?;/gs);
const cityInserts = countriesSql.match(/INSERT INTO public\.cities .*?;/gs);

// Extract INSERT statements for categories and subcategories
const categoryInserts = categoriesSql.match(/INSERT INTO public\.categories .*?;/gs);
const subcategoryInserts = categoriesSql.match(/INSERT INTO public\.subcategories .*?;/gs);

// Parse countries data
const countries = [];
if (countryInserts && countryInserts.length > 0) {
  const values = countryInserts[0].match(/VALUES \((.*?)\);/s);
  if (values) {
    const rows = values[1].split('), (');
    rows.forEach(row => {
      const cleanRow = row.replace(/[()]/g, '');
      const parts = cleanRow.split(', ');
      if (parts.length >= 3) {
        countries.push({
          id: parseInt(parts[0]),
          name: parts[1].replace(/'/g, ''),
          code: parts[2].replace(/'/g, '')
        });
      }
    });
  }
}

// Parse cities data
const cities = [];
if (cityInserts && cityInserts.length > 0) {
  const values = cityInserts[0].match(/VALUES \((.*?)\);/s);
  if (values) {
    const rows = values[1].split('), (');
    rows.forEach(row => {
      const cleanRow = row.replace(/[()]/g, '');
      const parts = cleanRow.split(', ');
      if (parts.length >= 3) {
        cities.push({
          id: parseInt(parts[0]),
          name: parts[1].replace(/'/g, ''),
          country_id: parseInt(parts[2])
        });
      }
    });
  }
}

// Parse categories data
const categories = [];
if (categoryInserts && categoryInserts.length > 0) {
  const values = categoryInserts[0].match(/VALUES \((.*?)\);/s);
  if (values) {
    const rows = values[1].split('), (');
    rows.forEach(row => {
      const cleanRow = row.replace(/[()]/g, '');
      const parts = cleanRow.split(', ');
      if (parts.length >= 3) {
        categories.push({
          id: parseInt(parts[0]),
          name: parts[1].replace(/'/g, ''),
          description: parts[2] === 'NULL' ? null : parts[2].replace(/'/g, '')
        });
      }
    });
  }
}

// Parse subcategories data
const subcategories = [];
if (subcategoryInserts && subcategoryInserts.length > 0) {
  const values = subcategoryInserts[0].match(/VALUES \((.*?)\);/s);
  if (values) {
    const rows = values[1].split('), (');
    rows.forEach(row => {
      const cleanRow = row.replace(/[()]/g, '');
      const parts = cleanRow.split(', ');
      if (parts.length >= 4) {
        subcategories.push({
          id: parseInt(parts[0]),
          name: parts[1].replace(/'/g, ''),
          description: parts[2] === 'NULL' ? null : parts[2].replace(/'/g, ''),
          category_id: parseInt(parts[3])
        });
      }
    });
  }
}

console.log(`Parsed ${countries.length} countries`);
console.log(`Parsed ${cities.length} cities`);
console.log(`Parsed ${categories.length} categories`);
console.log(`Parsed ${subcategories.length} subcategories`);

// Export the data for use in seeding
module.exports = {
  countries,
  cities,
  categories,
  subcategories
};