version: '3.8'

services:
  # PostgreSQL Database with Security Hardening
  postgres:
    image: postgres:15-alpine
    container_name: nwa-prod-postgres
    environment:
      POSTGRES_DB: nwa_portal_prod
      POSTGRES_USER: nwa_prod_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./postgres/prod-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - nwa-prod-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U nwa_prod_user -d nwa_portal_prod"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/run/postgresql
    cap_drop:
      - ALL
    cap_add:
      - DAC_OVERRIDE
      - SETUID
      - SETGID

  # Redis with Authentication and Security
  redis:
    image: redis:7-alpine
    container_name: nwa-prod-redis
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --protected-mode yes
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --appendonly yes
      --appendfsync everysec
      --tcp-keepalive 300
      --timeout 300
      --loglevel notice
    volumes:
      - redis_prod_data:/data
    networks:
      - nwa-prod-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
    cap_drop:
      - ALL
    cap_add:
      - DAC_OVERRIDE
      - SETUID
      - SETGID

  # MinIO Object Storage with Security
  minio:
    image: minio/minio:latest
    container_name: nwa-prod-minio
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY}
    ports:
      - "9002:9000"
      - "9003:9001"
    volumes:
      - minio_prod_data:/data
    networks:
      - nwa-prod-network
    command: server /data --console-address ":9001"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - DAC_OVERRIDE
      - SETUID
      - SETGID

  # Next.js Application with Security Enhancements
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: nwa-prod-app
    environment:
      # Application Configuration
      NODE_ENV: production
      NEXTAUTH_URL: ${NEXTAUTH_URL}
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}

      # Database Configuration
      DATABASE_URL: postgresql://nwa_prod_user:${DB_PASSWORD}@postgres:5432/nwa_portal_prod

      # Redis Configuration
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0

      # File Storage Configuration
      MINIO_ENDPOINT: minio
      MINIO_PORT: 9000
      MINIO_ACCESS_KEY: ${MINIO_ACCESS_KEY}
      MINIO_SECRET_KEY: ${MINIO_SECRET_KEY}
      MINIO_BUCKET_NAME: nwa-uploads-prod
      MINIO_USE_SSL: false

      # Security Configuration
      RATE_LIMIT_WINDOW_MS: 60000
      RATE_LIMIT_MAX_REQUESTS: 100
      AUDIT_LOG_RETENTION_DAYS: 90
      SESSION_MAX_AGE: 86400
      ALLOWED_ORIGINS: ${ALLOWED_ORIGINS}

      # Email Configuration (for security alerts)
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASS: ${SMTP_PASS}
      SMTP_FROM: ${SMTP_FROM}

      # Monitoring and Alerting
      SLACK_WEBHOOK_URL: ${SLACK_WEBHOOK_URL}
      PAGERDUTY_INTEGRATION_KEY: ${PAGERDUTY_INTEGRATION_KEY}
      SECURITY_ALERT_EMAIL: ${SECURITY_ALERT_EMAIL}

      # Compliance Configuration
      GDPR_COMPLIANCE_ENABLED: true
      DATA_RETENTION_DAYS: 2555
      ENCRYPTION_KEY_VERSION: 1

      # Performance Configuration
      ENABLE_COMPRESSION: true
      ENABLE_CACHING: true
      CACHE_TTL: 3600

      # Feature Flags
      ENABLE_2FA: true
      ENABLE_AUDIT_LOGGING: true
      ENABLE_RATE_LIMITING: true
      ENABLE_SECURITY_HEADERS: true
    command: >
      sh -c "
        # Wait for database to be ready
        until nc -z postgres 5432; do
          echo 'Waiting for database...';
          sleep 2;
        done;

        # Run database migrations
        echo 'Running database migrations...';
        npx prisma migrate deploy;

        # Start the application
        echo 'Starting application...';
        exec node server.js
      "
    ports:
      - "3001:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
      minio:
        condition: service_started
    networks:
      - nwa-prod-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/run
      - /var/cache/nginx
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
      - SYS_CHROOT
      - SETUID
      - SETGID
    volumes:
      - ./prisma:/app/prisma:ro

  # Security Monitoring Service
  security-monitor:
    image: nwa-security-monitor:latest
    container_name: nwa-prod-security-monitor
    environment:
      DATABASE_URL: postgresql://nwa_prod_user:${DB_PASSWORD}@postgres:5432/nwa_portal_prod
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      LOG_LEVEL: info
      MONITORING_INTERVAL: 30
      ALERT_THRESHOLD: high
    networks:
      - nwa-prod-network
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
      - app
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - DAC_OVERRIDE
      - SETUID
      - SETGID

  # Log Aggregation Service
  log-aggregator:
    image: fluent/fluentd:latest
    container_name: nwa-prod-log-aggregator
    volumes:
      - ./fluentd/conf:/fluentd/etc
      - postgres_prod_data:/var/log/postgres
      - ./logs:/var/log/nwa
    networks:
      - nwa-prod-network
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
      - app
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - DAC_OVERRIDE
      - SETUID
      - SETGID

volumes:
  postgres_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/postgresql/data

  redis_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/redis/data

  minio_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/minio/data

networks:
  nwa-prod-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: nwa-prod-br
    ipam:
      config:
        - subnet: **********/16
          gateway: **********