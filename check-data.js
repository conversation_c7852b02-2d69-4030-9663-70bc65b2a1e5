import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkData() {
  try {
    console.log('Checking countries count...');
    const countriesCount = await prisma.country.count();
    console.log(`Countries: ${countriesCount}`);

    console.log('Checking cities count...');
    const citiesCount = await prisma.city.count();
    console.log(`Cities: ${citiesCount}`);

    console.log('Checking sample countries...');
    const countries = await prisma.country.findMany({
      take: 5,
      include: {
        cities: {
          take: 3
        }
      }
    });

    countries.forEach(country => {
      console.log(`Country: ${country.name} (${country.code}) - Cities: ${country.cities.length}`);
      country.cities.forEach(city => {
        console.log(`  - ${city.name}`);
      });
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkData();