version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.test
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=test
      - PORT=3001
      - DATABASE_URL=**************************************/nwa_test
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    volumes:
      - ./cypress:/app/cypress
      - ./cypress.config.ts:/app/cypress.config.ts
    networks:
      - test-network

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=nwa_test
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts:/docker-entrypoint-initdb.d
    networks:
      - test-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - test-network

  cypress:
    image: cypress/included:13.15.0
    environment:
      - CYPRESS_baseUrl=http://app:3001
      - CYPRESS_video=true
      - CYPRESS_screenshotOnRunFailure=true
    depends_on:
      - app
    volumes:
      - ./cypress:/e2e/cypress
      - ./cypress.config.ts:/e2e/cypress.config.ts
    working_dir: /e2e
    command: npx cypress run --record --parallel
    networks:
      - test-network

volumes:
  postgres_data:

networks:
  test-network:
    driver: bridge