--
-- PostgreSQL database dump
--

-- Dumped from database version 15.14
-- Dumped by pg_dump version 17.5 (Debian 17.5-1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: nwa_user
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO nwa_user;

--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: nwa_user
--

COMMENT ON SCHEMA public IS '';


--
-- Name: BulkUploadStatus; Type: TYPE; Schema: public; Owner: nwa_user
--

CREATE TYPE public."BulkUploadStatus" AS ENUM (
    'PENDING',
    'APPROVED',
    'REJECTED'
);


ALTER TYPE public."BulkUploadStatus" OWNER TO nwa_user;

--
-- Name: OrdinanceStatus; Type: TYPE; Schema: public; Owner: nwa_user
--

CREATE TYPE public."OrdinanceStatus" AS ENUM (
    'PENDING',
    'IN_PROGRESS',
    'COMPLETED',
    'EXPIRED',
    'CANCELLED'
);


ALTER TYPE public."OrdinanceStatus" OWNER TO nwa_user;

--
-- Name: TreatyStatus; Type: TYPE; Schema: public; Owner: nwa_user
--

CREATE TYPE public."TreatyStatus" AS ENUM (
    'ACTIVE',
    'EXPIRED',
    'TERMINATED',
    'PENDING_RENEWAL'
);


ALTER TYPE public."TreatyStatus" OWNER TO nwa_user;

--
-- Name: UserStatus; Type: TYPE; Schema: public; Owner: nwa_user
--

CREATE TYPE public."UserStatus" AS ENUM (
    'ACTIVE',
    'INACTIVE',
    'SUSPENDED',
    'PENDING_VERIFICATION'
);


ALTER TYPE public."UserStatus" OWNER TO nwa_user;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO nwa_user;

--
-- Name: audit_logs; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.audit_logs (
    id text NOT NULL,
    user_id text,
    action text NOT NULL,
    resource text NOT NULL,
    resource_id text,
    old_values jsonb,
    new_values jsonb,
    ip_address text,
    user_agent text,
    "timestamp" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    success boolean NOT NULL,
    status_code integer,
    error_message text,
    request_id text,
    duration integer,
    request_size integer,
    response_size integer,
    metadata jsonb,
    project_id text,
    api_endpoint text,
    request_method text,
    country_code text,
    city text,
    remote_server_id text
);


ALTER TABLE public.audit_logs OWNER TO nwa_user;

--
-- Name: authorization_codes; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.authorization_codes (
    id text NOT NULL,
    remote_server_id text NOT NULL,
    user_id text NOT NULL,
    code text NOT NULL,
    redirect_uri text,
    scope text,
    expires_at timestamp(3) without time zone NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    used_at timestamp(3) without time zone
);


ALTER TABLE public.authorization_codes OWNER TO nwa_user;

--
-- Name: categories; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.categories (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.categories OWNER TO nwa_user;

--
-- Name: categories_id_seq; Type: SEQUENCE; Schema: public; Owner: nwa_user
--

CREATE SEQUENCE public.categories_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.categories_id_seq OWNER TO nwa_user;

--
-- Name: categories_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: nwa_user
--

ALTER SEQUENCE public.categories_id_seq OWNED BY public.categories.id;


--
-- Name: cities; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.cities (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    country_id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.cities OWNER TO nwa_user;

--
-- Name: cities_id_seq; Type: SEQUENCE; Schema: public; Owner: nwa_user
--

CREATE SEQUENCE public.cities_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cities_id_seq OWNER TO nwa_user;

--
-- Name: cities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: nwa_user
--

ALTER SEQUENCE public.cities_id_seq OWNED BY public.cities.id;


--
-- Name: countries; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.countries (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    code character varying(3) NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.countries OWNER TO nwa_user;

--
-- Name: countries_id_seq; Type: SEQUENCE; Schema: public; Owner: nwa_user
--

CREATE SEQUENCE public.countries_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.countries_id_seq OWNER TO nwa_user;

--
-- Name: countries_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: nwa_user
--

ALTER SEQUENCE public.countries_id_seq OWNED BY public.countries.id;


--
-- Name: country_address_formats; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.country_address_formats (
    id integer NOT NULL,
    country_id integer NOT NULL,
    postal_code_label character varying(50) NOT NULL,
    postal_code_format character varying(50),
    postal_code_required boolean DEFAULT true NOT NULL,
    region_label character varying(50),
    region_required boolean DEFAULT false NOT NULL,
    town_label character varying(50),
    town_required boolean DEFAULT false NOT NULL,
    address_template text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.country_address_formats OWNER TO nwa_user;

--
-- Name: country_address_formats_id_seq; Type: SEQUENCE; Schema: public; Owner: nwa_user
--

CREATE SEQUENCE public.country_address_formats_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.country_address_formats_id_seq OWNER TO nwa_user;

--
-- Name: country_address_formats_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: nwa_user
--

ALTER SEQUENCE public.country_address_formats_id_seq OWNED BY public.country_address_formats.id;


--
-- Name: file_attachments; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.file_attachments (
    id text NOT NULL,
    treaty_id text NOT NULL,
    file_name text NOT NULL,
    file_path text NOT NULL,
    mime_type text NOT NULL,
    file_size integer NOT NULL,
    uploaded_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.file_attachments OWNER TO nwa_user;

--
-- Name: identification_types; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.identification_types (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    category text DEFAULT 'government'::text NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.identification_types OWNER TO nwa_user;

--
-- Name: notifications; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.notifications (
    id text NOT NULL,
    type text NOT NULL,
    message text NOT NULL,
    data jsonb,
    recipients text[],
    "readBy" text[],
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.notifications OWNER TO nwa_user;

--
-- Name: oauth_tokens; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.oauth_tokens (
    id text NOT NULL,
    remote_server_id text NOT NULL,
    user_id text,
    access_token text NOT NULL,
    refresh_token text,
    token_type text DEFAULT 'Bearer'::text NOT NULL,
    scope text,
    expires_at timestamp(3) without time zone NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.oauth_tokens OWNER TO nwa_user;

--
-- Name: ordinance_documents; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.ordinance_documents (
    id text NOT NULL,
    ordinance_id text NOT NULL,
    file_path text NOT NULL,
    file_name text NOT NULL,
    file_size integer NOT NULL,
    file_type text NOT NULL,
    uploaded_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.ordinance_documents OWNER TO nwa_user;

--
-- Name: ordinance_types; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.ordinance_types (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    category text NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.ordinance_types OWNER TO nwa_user;

--
-- Name: ordinances; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.ordinances (
    id text NOT NULL,
    user_id text NOT NULL,
    ordinance_type_id text NOT NULL,
    status text DEFAULT 'PENDING'::text NOT NULL,
    completed_date timestamp(3) without time zone,
    expiration_date timestamp(3) without time zone,
    notes text,
    document_path text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.ordinances OWNER TO nwa_user;

--
-- Name: pending_bulk_uploads; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.pending_bulk_uploads (
    id text NOT NULL,
    uploader_id text NOT NULL,
    file_name text NOT NULL,
    record_count integer NOT NULL,
    data text NOT NULL,
    status text DEFAULT 'PENDING'::text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.pending_bulk_uploads OWNER TO nwa_user;

--
-- Name: permissions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.permissions (
    id text NOT NULL,
    name text NOT NULL,
    resource text NOT NULL,
    action text NOT NULL,
    description text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.permissions OWNER TO nwa_user;

--
-- Name: positions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.positions (
    id text NOT NULL,
    title text NOT NULL,
    description text,
    level integer DEFAULT 0 NOT NULL,
    parent_id text,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.positions OWNER TO nwa_user;

--
-- Name: projects; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.projects (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    api_key_hash text NOT NULL,
    allowed_origins text[],
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.projects OWNER TO nwa_user;

--
-- Name: regions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.regions (
    id integer NOT NULL,
    country_id integer NOT NULL,
    code character varying(10),
    name character varying(255) NOT NULL,
    type character varying(50) DEFAULT 'state'::character varying NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.regions OWNER TO nwa_user;

--
-- Name: regions_id_seq; Type: SEQUENCE; Schema: public; Owner: nwa_user
--

CREATE SEQUENCE public.regions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.regions_id_seq OWNER TO nwa_user;

--
-- Name: regions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: nwa_user
--

ALTER SEQUENCE public.regions_id_seq OWNED BY public.regions.id;


--
-- Name: remote_server_permissions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.remote_server_permissions (
    id text DEFAULT (gen_random_uuid())::text NOT NULL,
    remote_server_id text NOT NULL,
    permission_name text NOT NULL,
    permission_description text,
    last_synced_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_active boolean DEFAULT true NOT NULL
);


ALTER TABLE public.remote_server_permissions OWNER TO nwa_user;

--
-- Name: remote_servers; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.remote_servers (
    id text NOT NULL,
    name text NOT NULL,
    url text NOT NULL,
    api_endpoint text,
    oauth_redirect_uris text[],
    development_redirect_uris text[],
    "apiKey" text NOT NULL,
    description text,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    client_id text,
    client_secret text,
    redirect_uris text[],
    grant_types text[] DEFAULT ARRAY['authorization_code'::text, 'refresh_token'::text],
    default_scopes text[] DEFAULT ARRAY['read:profile'::text],
    callback_url text,
    allowed_origins text[],
    token_endpoint_auth_method text DEFAULT 'client_secret_basic'::text NOT NULL
);


ALTER TABLE public.remote_servers OWNER TO nwa_user;

--
-- Name: role_permissions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.role_permissions (
    id text NOT NULL,
    role_id text NOT NULL,
    permission_id text NOT NULL
);


ALTER TABLE public.role_permissions OWNER TO nwa_user;

--
-- Name: role_remote_server_access; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.role_remote_server_access (
    id text DEFAULT (gen_random_uuid())::text NOT NULL,
    role_id text NOT NULL,
    remote_server_id text NOT NULL,
    auto_grant_permissions text[],
    created_by text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_active boolean DEFAULT true NOT NULL
);


ALTER TABLE public.role_remote_server_access OWNER TO nwa_user;

--
-- Name: roles; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.roles (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    is_system boolean DEFAULT false NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.roles OWNER TO nwa_user;

--
-- Name: scopes; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.scopes (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    category text DEFAULT 'general'::text NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.scopes OWNER TO nwa_user;

--
-- Name: sessions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.sessions (
    id text NOT NULL,
    session_token text NOT NULL,
    user_id text NOT NULL,
    expires timestamp(3) without time zone NOT NULL,
    ip_address text,
    user_agent text,
    last_active timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.sessions OWNER TO nwa_user;

--
-- Name: subcategories; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.subcategories (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    category_id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.subcategories OWNER TO nwa_user;

--
-- Name: subcategories_id_seq; Type: SEQUENCE; Schema: public; Owner: nwa_user
--

CREATE SEQUENCE public.subcategories_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.subcategories_id_seq OWNER TO nwa_user;

--
-- Name: subcategories_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: nwa_user
--

ALTER SEQUENCE public.subcategories_id_seq OWNED BY public.subcategories.id;


--
-- Name: system_settings; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.system_settings (
    id integer NOT NULL,
    security_level text DEFAULT 'MODERATE'::text NOT NULL,
    maintenance_mode boolean DEFAULT false NOT NULL,
    max_login_attempts integer DEFAULT 5 NOT NULL,
    session_timeout integer DEFAULT 3600 NOT NULL,
    password_min_length integer DEFAULT 8 NOT NULL,
    password_require_upper boolean DEFAULT true NOT NULL,
    password_require_lower boolean DEFAULT true NOT NULL,
    password_require_number boolean DEFAULT true NOT NULL,
    password_require_special boolean DEFAULT true NOT NULL,
    two_factor_auth_enabled boolean DEFAULT false NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.system_settings OWNER TO nwa_user;

--
-- Name: system_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: nwa_user
--

CREATE SEQUENCE public.system_settings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.system_settings_id_seq OWNER TO nwa_user;

--
-- Name: system_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: nwa_user
--

ALTER SEQUENCE public.system_settings_id_seq OWNED BY public.system_settings.id;


--
-- Name: title_positions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.title_positions (
    id text NOT NULL,
    title_id text NOT NULL,
    position_id text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.title_positions OWNER TO nwa_user;

--
-- Name: titles; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.titles (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.titles OWNER TO nwa_user;

--
-- Name: treaties; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.treaties (
    id text NOT NULL,
    user_id text NOT NULL,
    status text DEFAULT 'ACTIVE'::text NOT NULL,
    signed_date timestamp(3) without time zone,
    expiration_date timestamp(3) without time zone,
    renewal_date timestamp(3) without time zone,
    notes text,
    document_path text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    name text,
    description text
);


ALTER TABLE public.treaties OWNER TO nwa_user;

--
-- Name: treaty_treaty_types; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.treaty_treaty_types (
    id text DEFAULT gen_random_uuid() NOT NULL,
    treaty_id text NOT NULL,
    treaty_type_id text NOT NULL,
    assigned_at timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP,
    assigned_by text,
    status text DEFAULT 'ACTIVE'::text,
    notes text
);


ALTER TABLE public.treaty_treaty_types OWNER TO nwa_user;

--
-- Name: treaty_type_details; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.treaty_type_details (
    id text NOT NULL,
    user_id text NOT NULL,
    treaty_id text NOT NULL,
    treaty_type_id text NOT NULL,
    status text DEFAULT 'DRAFT'::text NOT NULL,
    full_legal_name text,
    date_of_birth timestamp(3) without time zone,
    gender_identity text,
    nationality text[] DEFAULT ARRAY[]::text[],
    phone_numbers jsonb,
    email text,
    current_country_id integer,
    current_city_id integer,
    identification_number text,
    photograph_path text,
    residential_address text,
    residence_status text,
    residence_status_other text,
    residence_use_designation text,
    residence_country_id integer,
    residence_city_id integer,
    peace_protected_premises boolean DEFAULT false,
    business_details jsonb,
    protection_items jsonb,
    declaration_accepted boolean DEFAULT false,
    signature_data text,
    signature_date timestamp(3) without time zone,
    attachment_paths text[] DEFAULT ARRAY[]::text[],
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.treaty_type_details OWNER TO nwa_user;

--
-- Name: treaty_types; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.treaty_types (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    category text NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    requested_by text,
    reviewed_at timestamp(3) without time zone,
    reviewed_by text,
    status text DEFAULT 'APPROVED'::text NOT NULL
);


ALTER TABLE public.treaty_types OWNER TO nwa_user;

--
-- Name: user_identifications; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_identifications (
    id text NOT NULL,
    user_id text NOT NULL,
    identification_type_id text NOT NULL,
    id_number text NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.user_identifications OWNER TO nwa_user;

--
-- Name: user_positions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_positions (
    id text NOT NULL,
    user_id text NOT NULL,
    position_id text NOT NULL,
    start_date timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    end_date timestamp(3) without time zone,
    is_active boolean DEFAULT true NOT NULL,
    notes text
);


ALTER TABLE public.user_positions OWNER TO nwa_user;

--
-- Name: user_profiles; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_profiles (
    id text NOT NULL,
    user_id text NOT NULL,
    nwa_email text,
    country_id integer,
    city_id integer,
    mobile text,
    bio text,
    date_of_birth timestamp(3) without time zone,
    title_id text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    first_name text,
    last_name text,
    peace_ambassador_number text,
    personal_email text,
    phone text,
    postal_code text,
    region_id integer,
    region_text text,
    street_address_1 text,
    street_address_2 text,
    town text
);


ALTER TABLE public.user_profiles OWNER TO nwa_user;

--
-- Name: user_project_scopes; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_project_scopes (
    id text NOT NULL,
    user_id text NOT NULL,
    project_id text NOT NULL,
    scope_id text NOT NULL,
    granted_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    granted_by text
);


ALTER TABLE public.user_project_scopes OWNER TO nwa_user;

--
-- Name: user_remote_server_access; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_remote_server_access (
    id text DEFAULT (gen_random_uuid())::text NOT NULL,
    user_id text NOT NULL,
    remote_server_id text NOT NULL,
    granted_by text,
    granted_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    notes text
);


ALTER TABLE public.user_remote_server_access OWNER TO nwa_user;

--
-- Name: user_remote_server_permissions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_remote_server_permissions (
    id text DEFAULT (gen_random_uuid())::text NOT NULL,
    user_id text NOT NULL,
    remote_server_id text NOT NULL,
    permission_name text NOT NULL,
    granted_by text,
    granted_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    expires_at timestamp(3) without time zone,
    is_active boolean DEFAULT true NOT NULL
);


ALTER TABLE public.user_remote_server_permissions OWNER TO nwa_user;

--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_roles (
    id text NOT NULL,
    user_id text NOT NULL,
    role_id text NOT NULL,
    assigned_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    assigned_by text
);


ALTER TABLE public.user_roles OWNER TO nwa_user;

--
-- Name: user_treaties; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_treaties (
    id text DEFAULT (gen_random_uuid())::text NOT NULL,
    user_id text NOT NULL,
    treaty_id text NOT NULL,
    assigned_at timestamp with time zone DEFAULT now(),
    status text DEFAULT 'ACTIVE'::text,
    notes text
);


ALTER TABLE public.user_treaties OWNER TO nwa_user;

--
-- Name: user_treaty_numbers; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_treaty_numbers (
    id text NOT NULL,
    user_id text NOT NULL,
    treaty_type_id text NOT NULL,
    treaty_number text NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.user_treaty_numbers OWNER TO nwa_user;

--
-- Name: user_treaty_types; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_treaty_types (
    id text DEFAULT (gen_random_uuid())::text NOT NULL,
    user_id text NOT NULL,
    treaty_type_id text NOT NULL,
    selected_at timestamp with time zone DEFAULT now(),
    status text DEFAULT 'ACTIVE'::text,
    notes text
);


ALTER TABLE public.user_treaty_types OWNER TO nwa_user;

--
-- Name: users; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.users (
    id text NOT NULL,
    name text,
    email text,
    email_verified timestamp(3) without time zone,
    image text,
    password_hash text,
    two_factor_secret text,
    two_factor_enabled boolean DEFAULT false NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    two_factor_enforced boolean DEFAULT false,
    backup_codes text[] DEFAULT ARRAY[]::text[]
);


ALTER TABLE public.users OWNER TO nwa_user;

--
-- Name: verification_tokens; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.verification_tokens (
    identifier text NOT NULL,
    token text NOT NULL,
    expires timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.verification_tokens OWNER TO nwa_user;

--
-- Name: categories id; Type: DEFAULT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.categories ALTER COLUMN id SET DEFAULT nextval('public.categories_id_seq'::regclass);


--
-- Name: cities id; Type: DEFAULT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.cities ALTER COLUMN id SET DEFAULT nextval('public.cities_id_seq'::regclass);


--
-- Name: countries id; Type: DEFAULT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.countries ALTER COLUMN id SET DEFAULT nextval('public.countries_id_seq'::regclass);


--
-- Name: country_address_formats id; Type: DEFAULT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.country_address_formats ALTER COLUMN id SET DEFAULT nextval('public.country_address_formats_id_seq'::regclass);


--
-- Name: regions id; Type: DEFAULT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.regions ALTER COLUMN id SET DEFAULT nextval('public.regions_id_seq'::regclass);


--
-- Name: subcategories id; Type: DEFAULT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.subcategories ALTER COLUMN id SET DEFAULT nextval('public.subcategories_id_seq'::regclass);


--
-- Name: system_settings id; Type: DEFAULT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.system_settings ALTER COLUMN id SET DEFAULT nextval('public.system_settings_id_seq'::regclass);


--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
855c5b35-6efd-4cc0-8339-4978be7003f2	12b0604705670692bbefb8fe99105afe567cc840a5e588454cc69c0239f9bf88	2025-09-08 10:52:47.151122+00	20250901004559_init	\N	\N	2025-09-08 10:52:46.89978+00	1
ba4e63d6-c583-43fc-a00e-9f3ef012e9fa	7c31e39ae556555a69a52ab4871c4bf228264519a315281ac7fbe6c9bc9d2563	2025-09-08 10:52:47.155788+00	20250901081649_add_personal_email_and_phone_fields	\N	\N	2025-09-08 10:52:47.151842+00	1
450114b4-6134-4a8e-b7ec-060dd72aa876	fd13f75f21fb326b102fe1c8c96b67b9aea816da10118a286616f0005c3cb735	2025-09-08 10:52:47.168512+00	20250901214653_add_data	\N	\N	2025-09-08 10:52:47.156838+00	1
48fdb56c-63f4-4054-af38-151d86b4f06e	22af9b35f52295d5946d30d318651a87142fc9949205383649ff3a76b0fb181c	2025-09-08 10:52:47.173078+00	20250902110309_add_treaty_types	\N	\N	2025-09-08 10:52:47.169398+00	1
737600bc-b465-4660-91f7-b77c9a44f512	9fc18265c6885b5081922b80cc6315dade0ea8163439978ca755bf77d44b0539	2025-09-08 10:52:47.197193+00	20250902150000_update_treaty_schema	\N	\N	2025-09-08 10:52:47.173844+00	1
cc7e1d64-a0c3-442d-a2df-4ab41e58a312	174fd240ca955ee9331d489b813939824dac4b144f1c6aa707a6364841a84e35	2025-09-08 10:52:47.203061+00	20250903112937_add_session_tracking_2fa_fields	\N	\N	2025-09-08 10:52:47.197969+00	1
59bdfda2-4f83-42b0-a3f4-ffc94ea53ed4	40a50e995e1d7a1add4c4646abbcaa950fc5983bd9de7206c759502e3a41ed5c	\N	20250904000000_add_treaty_type_status_notification_system	A migration failed to apply. New migrations cannot be applied before the error is recovered from. Read more about how to resolve migration issues in a production database: https://pris.ly/d/migrate-resolve\n\nMigration name: 20250904000000_add_treaty_type_status_notification_system\n\nDatabase error code: 42703\n\nDatabase error:\nERROR: column "status" of relation "treaty_types" does not exist\n\nDbError { severity: "ERROR", parsed_severity: Some(Error), code: SqlState(E42703), message: "column \\"status\\" of relation \\"treaty_types\\" does not exist", detail: None, hint: None, position: None, where_: None, schema: None, table: None, column: None, datatype: None, constraint: None, file: Some("tablecmds.c"), line: Some(7724), routine: Some("ATExecColumnDefault") }\n\n   0: sql_schema_connector::apply_migration::apply_script\n           with migration_name="20250904000000_add_treaty_type_status_notification_system"\n             at schema-engine/connectors/sql-schema-connector/src/apply_migration.rs:113\n   1: schema_commands::commands::apply_migrations::Applying migration\n           with migration_name="20250904000000_add_treaty_type_status_notification_system"\n             at schema-engine/commands/src/commands/apply_migrations.rs:95\n   2: schema_core::state::ApplyMigrations\n             at schema-engine/core/src/state.rs:236	\N	2025-09-08 10:52:47.203866+00	0
\.


--
-- Data for Name: audit_logs; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.audit_logs (id, user_id, action, resource, resource_id, old_values, new_values, ip_address, user_agent, "timestamp", success, status_code, error_message, request_id, duration, request_size, response_size, metadata, project_id, api_endpoint, request_method, country_code, city, remote_server_id) FROM stdin;
\.


--
-- Data for Name: authorization_codes; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.authorization_codes (id, remote_server_id, user_id, code, redirect_uri, scope, expires_at, created_at, used_at) FROM stdin;
\.


--
-- Data for Name: categories; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.categories (id, name, description, created_at, updated_at) FROM stdin;
6	Agriculture	Farming, forestry, fishing and related activities	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
7	Food & Beverage	Restaurants, cafes, food production and distribution	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
8	Manufacturing	Production of goods from raw materials	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
9	Construction	Building and infrastructure development	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
10	Transport & Logistics	Movement of goods and people	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
11	Technology	Software, IT services and digital solutions	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
12	Professional Services	Consulting, legal, accounting and other specialized services	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
13	Tourism & Hospitality	Hotels, travel agencies, tour operators and related services	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
14	Health & Wellness	Medical services, fitness and wellness providers	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
15	Education & Training	Schools, universities, training institutions and educational services	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
16	Retail & Wholesale	Sale of goods to consumers or other businesses	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
17	Energy & Utilities	Electricity, gas, water and waste management services	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
18	Creative Industries	Media, entertainment, design and arts services	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
19	Real Estate	Property sales, rentals and management services	2025-08-31 04:32:34.937	2025-08-31 04:32:34.937
\.


--
-- Data for Name: cities; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.cities (id, name, country_id, created_at, updated_at) FROM stdin;
250	Auckland	31	2025-09-01 11:38:31.479	2025-09-01 11:38:31.479
251	Wellington	31	2025-09-01 11:38:31.481	2025-09-01 11:38:31.481
252	Christchurch	31	2025-09-01 11:38:31.482	2025-09-01 11:38:31.482
253	Hamilton	31	2025-09-01 11:38:31.483	2025-09-01 11:38:31.483
254	Tauranga	31	2025-09-01 11:38:31.485	2025-09-01 11:38:31.485
255	Dunedin	31	2025-09-01 11:38:31.486	2025-09-01 11:38:31.486
402	New York	33	2025-09-08 11:25:01.579	2025-09-08 11:25:01.579
403	Los Angeles	33	2025-09-08 11:25:01.582	2025-09-08 11:25:01.582
404	Toronto	34	2025-09-08 11:25:01.585	2025-09-08 11:25:01.585
405	Vancouver	34	2025-09-08 11:25:01.588	2025-09-08 11:25:01.588
406	London	35	2025-09-08 11:25:01.59	2025-09-08 11:25:01.59
407	Manchester	35	2025-09-08 11:25:01.593	2025-09-08 11:25:01.593
408	Sydney	32	2025-09-08 11:25:01.596	2025-09-08 11:25:01.596
409	Melbourne	32	2025-09-08 11:25:01.598	2025-09-08 11:25:01.598
410	Houston	33	2025-09-08 11:25:01.601	2025-09-08 11:25:01.601
411	Phoenix	33	2025-09-08 11:25:01.604	2025-09-08 11:25:01.604
412	Dallas	33	2025-09-08 11:25:01.607	2025-09-08 11:25:01.607
\.


--
-- Data for Name: countries; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.countries (id, name, code, created_at, updated_at) FROM stdin;
31	New Zealand	NZ	2025-09-01 11:38:31.473	2025-09-01 11:38:31.473
32	Australia	AU	2025-09-01 11:38:31.487	2025-09-01 11:38:31.487
33	United States	US	2025-09-01 11:38:31.495	2025-09-01 11:38:31.495
34	Canada	CA	2025-09-01 11:38:31.507	2025-09-01 11:38:31.507
35	United Kingdom	GB	2025-09-01 11:38:31.515	2025-09-01 11:38:31.515
36	Germany	DE	2025-09-01 11:38:31.523	2025-09-01 11:38:31.523
37	France	FR	2025-09-01 11:38:31.531	2025-09-01 11:38:31.531
38	Japan	JP	2025-09-01 11:38:31.538	2025-09-01 11:38:31.538
39	China	CN	2025-09-01 11:38:31.546	2025-09-01 11:38:31.546
40	India	IN	2025-09-01 11:38:31.554	2025-09-01 11:38:31.554
\.


--
-- Data for Name: country_address_formats; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.country_address_formats (id, country_id, postal_code_label, postal_code_format, postal_code_required, region_label, region_required, town_label, town_required, address_template, created_at, updated_at) FROM stdin;
1	33	ZIP Code	^\\d{5}(-\\d{4})?$	t	State	t	City	t	{street}\n{town}, {region} {postalCode}\n{country}	2025-09-08 11:25:01.609	2025-09-08 11:25:01.609
2	34	Postal Code	^[A-Z]\\d[A-Z] ?\\d[A-Z]\\d$	t	Province	t	City	t	{street}\n{town}, {region} {postalCode}\n{country}	2025-09-08 11:25:01.613	2025-09-08 11:25:01.613
3	35	Postcode	^[A-Z]{1,2}\\d[A-Z\\d]? ?\\d[A-Z]{2}$	t	County	f	Town/City	t	{street}\n{town}\n{region}\n{postalCode}\n{country}	2025-09-08 11:25:01.615	2025-09-08 11:25:01.615
4	32	Postcode	^\\d{4}$	t	State	t	Suburb	t	{street}\n{town} {region} {postalCode}\n{country}	2025-09-08 11:25:01.617	2025-09-08 11:25:01.617
\.


--
-- Data for Name: file_attachments; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.file_attachments (id, treaty_id, file_name, file_path, mime_type, file_size, uploaded_at) FROM stdin;
\.


--
-- Data for Name: identification_types; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.identification_types (id, name, description, category, is_active, created_at, updated_at) FROM stdin;
cmfb1brhz001qpp9ne4bvc39s	Passport	International travel document issued by national government	government	t	2025-09-08 11:25:01.559	2025-09-08 20:57:58.546
cmfb1bri1001rpp9ngngb825i	Driver License	Government-issued license permitting operation of motor vehicles	government	t	2025-09-08 11:25:01.561	2025-09-08 20:57:58.548
cmfb1bri2001spp9n0ocefjla	National ID Card	Primary identification document for citizens	government	t	2025-09-08 11:25:01.563	2025-09-08 20:57:58.549
cmfb1bri3001tpp9nqtix9n7o	Military ID	Armed forces identification document	military	t	2025-09-08 11:25:01.564	2025-09-08 20:57:58.55
cmfb1bri4001upp9ny31scjnr	State ID Card	Non-driver state-issued identification	government	t	2025-09-08 11:25:01.565	2025-09-08 20:57:58.551
cmfb1bri5001vpp9neaw5bphm	Under 18 ID	Identification for minors under 18 years old	government	t	2025-09-08 11:25:01.566	2025-09-08 20:57:58.552
cmfb1bri6001wpp9nykwtqkyj	Veteran ID Card	Identification for military veterans	military	t	2025-09-08 11:25:01.567	2025-09-08 20:57:58.553
cmfb1bri7001xpp9nwpf10jyh	Federal Employee ID	Identification for government employees	government	t	2025-09-08 11:25:01.568	2025-09-08 20:57:58.554
cmfb1bri9001ypp9ndafffe2v	Tribal ID Card	Identification for tribal nation members	government	t	2025-09-08 11:25:01.569	2025-09-08 20:57:58.555
cmfb1bria001zpp9n007gb4lp	Immigration Document	Visa, green card, or other immigration status proof	government	t	2025-09-08 11:25:01.57	2025-09-08 20:57:58.555
cmfb1brib0020pp9neyr5lpll	Professional License	Professional certification or license document	professional	t	2025-09-08 11:25:01.571	2025-09-08 20:57:58.556
cmfb1bric0021pp9n9yx7xugt	Student ID	Educational institution identification	education	t	2025-09-08 11:25:01.572	2025-09-08 20:57:58.557
cmfb1brid0022pp9nfw5c9jr7	Healthcare Worker ID	Medical professional identification	professional	t	2025-09-08 11:25:01.573	2025-09-08 20:57:58.56
cmfb1brie0023pp9njsfes201	Diplomatic ID	Diplomatic passport or identification	diplomatic	t	2025-09-08 11:25:01.574	2025-09-08 20:57:58.561
cmfb1brif0024pp9ngp05390z	Police ID	Law enforcement identification	government	t	2025-09-08 11:25:01.575	2025-09-08 20:57:58.562
\.


--
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.notifications (id, type, message, data, recipients, "readBy", created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: oauth_tokens; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.oauth_tokens (id, remote_server_id, user_id, access_token, refresh_token, token_type, scope, expires_at, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: ordinance_documents; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.ordinance_documents (id, ordinance_id, file_path, file_name, file_size, file_type, uploaded_at) FROM stdin;
\.


--
-- Data for Name: ordinance_types; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.ordinance_types (id, name, description, category, is_active, created_at, updated_at) FROM stdin;
cmfb1brga0013pp9nq73ygbwg	Baptism	Water baptism ceremony	sacrament	t	2025-09-08 11:25:01.499	2025-09-08 11:25:01.499
cmfb1brge0014pp9nw53wgwug	Confirmation	Confirmation of faith	sacrament	t	2025-09-08 11:25:01.503	2025-09-08 11:25:01.503
cmfb1brgg0015pp9n4e5wwpxa	Blessing	Spiritual blessing ceremony	blessing	t	2025-09-08 11:25:01.505	2025-09-08 11:25:01.505
cmfb1brgi0016pp9nhbweqemu	Dedication	Child dedication ceremony	dedication	t	2025-09-08 11:25:01.506	2025-09-08 11:25:01.506
\.


--
-- Data for Name: ordinances; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.ordinances (id, user_id, ordinance_type_id, status, completed_date, expiration_date, notes, document_path, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: pending_bulk_uploads; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.pending_bulk_uploads (id, uploader_id, file_name, record_count, data, status, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: permissions; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.permissions (id, name, resource, action, description, created_at) FROM stdin;
cmfb1brex0003pp9nnbye2ehv	user:read	user	read	View user profiles	2025-09-08 11:25:01.449
cmfb1brf00004pp9nemmpgknm	user:write	user	write	Edit user profiles	2025-09-08 11:25:01.452
cmfb1brf20005pp9npgqanwwr	user:delete	user	delete	Delete user accounts	2025-09-08 11:25:01.454
cmfb1brf40006pp9n6ai789w6	ordinance:read	ordinance	read	View ordinances	2025-09-08 11:25:01.456
cmfb1brf60007pp9npeoqigxq	ordinance:write	ordinance	write	Create/edit ordinances	2025-09-08 11:25:01.458
cmfb1brf80008pp9nomrvusj2	ordinance:delete	ordinance	delete	Delete ordinances	2025-09-08 11:25:01.46
cmfb1brfa0009pp9n4b08lioo	treaty:read	treaty	read	View treaties	2025-09-08 11:25:01.462
cmfb1brfc000app9n14cizoli	treaty:write	treaty	write	Create/edit treaties	2025-09-08 11:25:01.464
cmfb1brfd000bpp9n7bip6kfz	treaty:delete	treaty	delete	Delete treaties	2025-09-08 11:25:01.466
cmfb1brff000cpp9nuxg0vxyn	admin:access	admin	access	Access admin panel	2025-09-08 11:25:01.468
\.


--
-- Data for Name: positions; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.positions (id, title, description, level, parent_id, is_active, created_at, updated_at) FROM stdin;
cmfb1brgk0017pp9nprcpt9x1	Elder	Church elder position	1	\N	t	2025-09-08 11:25:01.508	2025-09-08 11:25:01.508
cmfb1brgn0018pp9n1dcw4sss	Deacon	Church deacon position	2	\N	t	2025-09-08 11:25:01.512	2025-09-08 11:25:01.512
cmfb1brgp0019pp9n2vuxemuc	Minister	Licensed minister	1	\N	t	2025-09-08 11:25:01.513	2025-09-08 11:25:01.513
cmfb1brgr001app9n604406w7	Youth Leader	Youth ministry leader	3	\N	t	2025-09-08 11:25:01.515	2025-09-08 11:25:01.515
\.


--
-- Data for Name: projects; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.projects (id, name, description, api_key_hash, allowed_origins, is_active, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: regions; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.regions (id, country_id, code, name, type, is_active, created_at, updated_at) FROM stdin;
1	33	CA	California	state	t	2025-09-08 11:25:01.62	2025-09-08 11:25:01.62
2	33	TX	Texas	state	t	2025-09-08 11:25:01.624	2025-09-08 11:25:01.624
3	33	NY	New York	state	t	2025-09-08 11:25:01.626	2025-09-08 11:25:01.626
4	33	FL	Florida	state	t	2025-09-08 11:25:01.628	2025-09-08 11:25:01.628
5	33	IL	Illinois	state	t	2025-09-08 11:25:01.63	2025-09-08 11:25:01.63
6	33	PA	Pennsylvania	state	t	2025-09-08 11:25:01.631	2025-09-08 11:25:01.631
7	33	OH	Ohio	state	t	2025-09-08 11:25:01.633	2025-09-08 11:25:01.633
8	33	GA	Georgia	state	t	2025-09-08 11:25:01.635	2025-09-08 11:25:01.635
9	33	NC	North Carolina	state	t	2025-09-08 11:25:01.637	2025-09-08 11:25:01.637
10	33	MI	Michigan	state	t	2025-09-08 11:25:01.639	2025-09-08 11:25:01.639
11	34	ON	Ontario	province	t	2025-09-08 11:25:01.641	2025-09-08 11:25:01.641
12	34	QC	Quebec	province	t	2025-09-08 11:25:01.643	2025-09-08 11:25:01.643
13	34	BC	British Columbia	province	t	2025-09-08 11:25:01.645	2025-09-08 11:25:01.645
14	34	AB	Alberta	province	t	2025-09-08 11:25:01.647	2025-09-08 11:25:01.647
15	34	MB	Manitoba	province	t	2025-09-08 11:25:01.649	2025-09-08 11:25:01.649
16	34	SK	Saskatchewan	province	t	2025-09-08 11:25:01.652	2025-09-08 11:25:01.652
17	34	NS	Nova Scotia	province	t	2025-09-08 11:25:01.653	2025-09-08 11:25:01.653
18	34	NB	New Brunswick	province	t	2025-09-08 11:25:01.655	2025-09-08 11:25:01.655
19	34	NL	Newfoundland and Labrador	province	t	2025-09-08 11:25:01.657	2025-09-08 11:25:01.657
20	34	PE	Prince Edward Island	province	t	2025-09-08 11:25:01.659	2025-09-08 11:25:01.659
21	35	LND	London	region	t	2025-09-08 11:25:01.661	2025-09-08 11:25:01.661
22	35	GTM	Greater Manchester	county	t	2025-09-08 11:25:01.663	2025-09-08 11:25:01.663
23	35	WYK	West Yorkshire	county	t	2025-09-08 11:25:01.666	2025-09-08 11:25:01.666
24	35	SYK	South Yorkshire	county	t	2025-09-08 11:25:01.668	2025-09-08 11:25:01.668
25	35	ESX	Essex	county	t	2025-09-08 11:25:01.67	2025-09-08 11:25:01.67
26	35	KEN	Kent	county	t	2025-09-08 11:25:01.672	2025-09-08 11:25:01.672
27	32	NSW	New South Wales	state	t	2025-09-08 11:25:01.673	2025-09-08 11:25:01.673
28	32	VIC	Victoria	state	t	2025-09-08 11:25:01.675	2025-09-08 11:25:01.675
29	32	QLD	Queensland	state	t	2025-09-08 11:25:01.677	2025-09-08 11:25:01.677
30	32	WA	Western Australia	state	t	2025-09-08 11:25:01.679	2025-09-08 11:25:01.679
31	32	SA	South Australia	state	t	2025-09-08 11:25:01.681	2025-09-08 11:25:01.681
32	32	TAS	Tasmania	state	t	2025-09-08 11:25:01.683	2025-09-08 11:25:01.683
33	32	ACT	Australian Capital Territory	territory	t	2025-09-08 11:25:01.684	2025-09-08 11:25:01.684
34	32	NT	Northern Territory	territory	t	2025-09-08 11:25:01.686	2025-09-08 11:25:01.686
\.


--
-- Data for Name: remote_server_permissions; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.remote_server_permissions (id, remote_server_id, permission_name, permission_description, last_synced_at, is_active) FROM stdin;
\.


--
-- Data for Name: remote_servers; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.remote_servers (id, name, url, api_endpoint, oauth_redirect_uris, development_redirect_uris, "apiKey", description, "isActive", "createdAt", "updatedAt", client_id, client_secret, redirect_uris, grant_types, default_scopes, callback_url, allowed_origins, token_endpoint_auth_method) FROM stdin;
cmfb1bs0w002dpp9napz90l6u	NWA Promote Local	http://localhost:3002	\N	\N	\N	nwa_promote_api_key_dev_only	Local development client for NWA Promote	t	2025-09-08 11:25:02.241	2025-09-08 11:25:02.241	nwapromote-client-local	da69150be2ae984ea232f144387add211844500d9d94de131ba78f9e2936fbff	{http://localhost:3002/api/auth/callback/member-portal-custom}	{authorization_code,refresh_token}	{read:profile}	http://localhost:3002/api/auth/callback/member-portal-custom	{http://localhost:3002}	client_secret_basic
\.


--
-- Data for Name: role_permissions; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.role_permissions (id, role_id, permission_id) FROM stdin;
cmfb1brfk000epp9n8xusjh7t	cmfb1bres0001pp9n2t0gs0ze	cmfb1brex0003pp9nnbye2ehv
cmfb1brfo000gpp9n6y2r1164	cmfb1bres0001pp9n2t0gs0ze	cmfb1brf00004pp9nemmpgknm
cmfb1brfp000ipp9n2stpx0qr	cmfb1bres0001pp9n2t0gs0ze	cmfb1brf20005pp9npgqanwwr
cmfb1brfr000kpp9n6r7fa7jc	cmfb1bres0001pp9n2t0gs0ze	cmfb1brf40006pp9n6ai789w6
cmfb1brfu000mpp9nkm25k5h5	cmfb1bres0001pp9n2t0gs0ze	cmfb1brf60007pp9npeoqigxq
cmfb1brfv000opp9nbcxal30a	cmfb1bres0001pp9n2t0gs0ze	cmfb1brf80008pp9nomrvusj2
cmfb1brfx000qpp9n8ehgenue	cmfb1bres0001pp9n2t0gs0ze	cmfb1brfa0009pp9n4b08lioo
cmfb1brg0000spp9nxuhkfu97	cmfb1bres0001pp9n2t0gs0ze	cmfb1brfc000app9n14cizoli
cmfb1brg1000upp9nlw80f657	cmfb1bres0001pp9n2t0gs0ze	cmfb1brfd000bpp9n7bip6kfz
cmfb1brg3000wpp9n4fau8a5o	cmfb1bres0001pp9n2t0gs0ze	cmfb1brff000cpp9nuxg0vxyn
cmfb1brg4000ypp9ndzw7i7c0	cmfb1brem0000pp9nnv3scqmq	cmfb1brex0003pp9nnbye2ehv
cmfb1brg70010pp9nr22ijoy1	cmfb1brem0000pp9nnv3scqmq	cmfb1brf40006pp9n6ai789w6
cmfb1brg90012pp9nmuwsbriz	cmfb1brem0000pp9nnv3scqmq	cmfb1brfa0009pp9n4b08lioo
\.


--
-- Data for Name: role_remote_server_access; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.role_remote_server_access (id, role_id, remote_server_id, auto_grant_permissions, created_by, created_at, is_active) FROM stdin;
\.


--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.roles (id, name, description, is_system, created_at, updated_at) FROM stdin;
cmfb1brem0000pp9nnv3scqmq	member	Standard NWA member	t	2025-09-08 11:25:01.439	2025-09-08 11:25:01.439
cmfb1bres0001pp9n2t0gs0ze	admin	System administrator	t	2025-09-08 11:25:01.445	2025-09-08 11:25:01.445
cmfb1brev0002pp9nxjnrn74r	moderator	Community moderator	t	2025-09-08 11:25:01.447	2025-09-08 11:25:01.447
\.


--
-- Data for Name: scopes; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.scopes (id, name, description, category, is_active, created_at) FROM stdin;
cmfb1brgt001bpp9n82ib8b9r	read:users	Read user profile information	users	t	2025-09-08 11:25:01.517
cmfb1brgw001cpp9nsr9bldie	write:users	Create and update user profiles	users	t	2025-09-08 11:25:01.52
cmfb1brgy001dpp9nahqjtder	read:profiles	Read detailed user profile data	profiles	t	2025-09-08 11:25:01.522
cmfb1brgz001epp9nzhnfiggl	write:profiles	Update user profile information	profiles	t	2025-09-08 11:25:01.524
cmfb1brh5001fpp9nj2vri5t9	read:roles	Read user roles and permissions	roles	t	2025-09-08 11:25:01.53
cmfb1brh8001gpp9n5m788axk	write:roles	Assign and modify user roles	roles	t	2025-09-08 11:25:01.532
cmfb1brha001hpp9n9x1rph49	read:positions	Read user positions and hierarchy	positions	t	2025-09-08 11:25:01.534
cmfb1brhc001ipp9nselna3db	write:positions	Assign and modify user positions	positions	t	2025-09-08 11:25:01.536
cmfb1brhd001jpp9nezycvvvj	read:ordinances	Read user ordinance information	ordinances	t	2025-09-08 11:25:01.538
cmfb1brhg001kpp9necxl1yaj	write:ordinances	Create and update ordinances	ordinances	t	2025-09-08 11:25:01.54
cmfb1brhh001lpp9nvln8ovzc	read:treaties	Read user treaty information	treaties	t	2025-09-08 11:25:01.542
cmfb1brhj001mpp9ngr5zdg05	write:treaties	Create and update treaties	treaties	t	2025-09-08 11:25:01.544
cmfb1brhl001npp9nhr8lyi5i	read:audit	Read audit log information	audit	t	2025-09-08 11:25:01.545
cmfb1brhm001opp9n0qebee9y	admin:projects	Manage external projects	admin	t	2025-09-08 11:25:01.547
cmfb1brho001ppp9nn9aa3023	admin:scopes	Manage permission scopes	admin	t	2025-09-08 11:25:01.548
\.


--
-- Data for Name: sessions; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.sessions (id, session_token, user_id, expires, ip_address, user_agent, last_active, created_at) FROM stdin;
\.


--
-- Data for Name: subcategories; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.subcategories (id, name, description, category_id, created_at, updated_at) FROM stdin;
7	Crop Farming	\N	6	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
8	Livestock Farming	\N	6	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
9	Aquaculture	\N	6	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
10	Forestry	\N	6	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
11	Agricultural Services	\N	6	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
12	Restaurants	\N	7	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
13	Cafes & Coffee Shops	\N	7	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
14	Fast Food	\N	7	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
15	Food Production	\N	7	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
16	Bakeries	\N	7	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
17	Bars & Pubs	\N	7	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
18	Textiles	\N	8	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
19	Electronics	\N	8	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
20	Automotive Parts	\N	8	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
21	Furniture	\N	8	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
22	Chemicals	\N	8	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
23	Machinery	\N	8	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
24	Residential Building	\N	9	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
25	Commercial Construction	\N	9	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
26	Infrastructure	\N	9	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
27	Renovation	\N	9	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
28	Specialized Contractors	\N	9	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
29	Freight Services	\N	10	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
30	Courier & Delivery	\N	10	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
31	Passenger Transport	\N	10	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
32	Warehousing	\N	10	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
33	Logistics Solutions	\N	10	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
34	Software Development	\N	11	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
35	IT Consulting	\N	11	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
36	Web Services	\N	11	2025-08-01 04:32:34.952	2025-08-31 04:32:34.952
37	Mobile Apps	\N	11	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
38	Cybersecurity	\N	11	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
39	Data Services	\N	11	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
40	Legal Services	\N	12	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
41	Accounting & Tax	\N	12	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
42	Consulting	\N	12	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
43	Marketing & Advertising	\N	12	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
44	HR Services	\N	12	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
45	Architecture & Engineering	\N	12	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
46	Hotels & Accommodation	\N	13	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
47	Travel Agencies	\N	13	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
48	Tour Operators	\N	13	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
49	Event Planning	\N	13	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
50	Cruise Services	\N	13	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
51	Medical Clinics	\N	14	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
52	Dental Services	\N	14	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
53	Fitness Centers	\N	14	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
54	Spas & Wellness	\N	14	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
55	Mental Health Services	\N	14	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
56	Schools	\N	15	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
57	Universities	\N	15	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
58	Vocational Training	\N	15	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
59	Online Learning	\N	15	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
60	Tutoring Services	\N	15	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
61	Clothing Stores	\N	16	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
62	Electronics Retail	\N	16	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
63	Supermarkets	\N	16	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
64	Specialty Stores	\N	16	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
65	E-commerce	\N	16	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
66	Wholesale Distribution	\N	16	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
67	Electricity Providers	\N	17	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
68	Gas Suppliers	\N	17	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
69	Water Services	\N	17	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
70	Renewable Energy	\N	17	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
71	Waste Management	\N	17	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
72	Advertising Agencies	\N	18	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
73	Media Production	\N	18	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
74	Design Studios	\N	18	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
75	Entertainment	\N	18	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
76	Publishing	\N	18	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
77	Property Sales	\N	19	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
78	Rental Services	\N	19	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
79	Property Management	\N	19	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
80	Real Estate Agents	\N	19	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
81	Property Development	\N	19	2025-08-31 04:32:34.952	2025-08-31 04:32:34.952
\.


--
-- Data for Name: system_settings; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.system_settings (id, security_level, maintenance_mode, max_login_attempts, session_timeout, password_min_length, password_require_upper, password_require_lower, password_require_number, password_require_special, two_factor_auth_enabled, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: title_positions; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.title_positions (id, title_id, position_id, created_at) FROM stdin;
\.


--
-- Data for Name: titles; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.titles (id, name, description, is_active, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: treaties; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.treaties (id, user_id, status, signed_date, expiration_date, renewal_date, notes, document_path, created_at, updated_at, name, description) FROM stdin;
\.


--
-- Data for Name: treaty_treaty_types; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.treaty_treaty_types (id, treaty_id, treaty_type_id, assigned_at, assigned_by, status, notes) FROM stdin;
\.


--
-- Data for Name: treaty_type_details; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.treaty_type_details (id, user_id, treaty_id, treaty_type_id, status, full_legal_name, date_of_birth, gender_identity, nationality, phone_numbers, email, current_country_id, current_city_id, identification_number, photograph_path, residential_address, residence_status, residence_status_other, residence_use_designation, residence_country_id, residence_city_id, peace_protected_premises, business_details, protection_items, declaration_accepted, signature_data, signature_date, attachment_paths, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: treaty_types; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.treaty_types (id, name, description, category, is_active, created_at, updated_at, requested_by, reviewed_at, reviewed_by, status) FROM stdin;
523e48df-9503-41b6-9b6b-a3ab16b73adf	Micro Start-up Business	Small start-up businesses with minimal capital and staff	Micro Business	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
7e2b8bd6-e304-4a95-bf7e-e25a780144be	Micro Business	Small businesses with minimal capital and staff	Micro Business	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
6a32542f-d2fd-46a6-bad9-23f625bebf8c	Micro Family-owned enterprises / Hapu	Family-owned micro enterprises or traditional Hapu business structures	Micro Business	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
6435867f-64a8-499c-bd72-594befa8af97	Micro Sole Proprietors	Individual proprietorship businesses	Micro Business	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
aaab6011-2d01-4c4d-9638-ed8d5cda3e5a	Micro Home-based Operations	Businesses operated from home premises	Micro Business	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
9016e4b9-fd7d-41ae-85a7-0ae9866c4e63	Small Social Enterprises	Socially-focused enterprises with community benefit goals	Small Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
a1b10291-4225-445f-aaab-5d4b0c1149e7	Small Ethical Start-ups	Start-up businesses with ethical business practices	Small Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
234e2ec0-6d1a-4029-9a20-b9d34b62d30f	Small Registered NGOs	Registered non-governmental organizations	Small Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
95e9590e-cb35-48c2-b4b6-9f934fa14948	Small Not-for-Profit	Not-for-profit organizations	Small Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
020d3a27-901e-49ad-896b-0a6bd72a9198	Small Charitable Trusts	Charitable trust organizations	Small Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
900f33af-d0d4-4729-8b13-82ac0f5a9666	Small Trusts	Trust-based organizations	Small Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
abc6ae5c-6445-4bee-985e-d2c02c5a0c81	Small Cultural Preservation Societies	Organizations focused on cultural preservation	Small Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
bcfe9a17-6868-436e-9420-0ea22ea3b23a	Small Educational Groups	Small educational organizations and groups	Small Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
0fd15973-a0b9-45d8-8ecf-3dd6067468d5	Small Environmental Protection Groups	Organizations focused on environmental protection	Small Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
60846038-90fc-49a7-91c5-5450a0bb2517	Small Sustainability initiatives	Initiatives focused on sustainable practices	Small Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
695146b2-ee4a-4a3c-899d-82b3a15e3889	Medium Business & Enterprise	Medium-sized business enterprises	Medium Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
7b339b6b-046c-4d2b-bc0d-b39b73365ea2	Medium Registered NGOs	Medium-sized registered non-governmental organizations	Medium Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
ff75adde-a13b-4ef7-abca-d3a492518175	Medium Not-for-Profit	Medium-sized not-for-profit organizations	Medium Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
c7766e37-f094-48c2-a286-6371a5cb008d	Medium Charitable Trusts	Medium-sized charitable trust organizations	Medium Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
d9d8fb42-694a-4f79-8ef0-72ac0633cc23	Medium Trusts	Medium-sized trust-based organizations	Medium Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
61967a5a-46db-44a5-8610-2f36a3d98092	Medium Cultural Preservation Societies	Medium-sized organizations focused on cultural preservation	Medium Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
7a6fda64-2578-4005-aae1-b30fc5b75c4e	Medium Educational Groups	Medium-sized educational organizations and groups	Medium Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
020863cd-42c0-4c1a-8f55-16a54c400820	Medium Schools	Medium-sized educational institutions	Medium Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
3110e569-e114-4c25-b232-500a73439161	Medium Healing Centers	Medium-sized health and wellness centers	Medium Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
a0bfaf91-ca5b-435e-87f5-f358b4f80e6c	Medium Co-ops	Medium-sized cooperative organizations	Medium Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
44ae5011-b356-4411-b109-53f78e144c5d	Medium Environmental Protection Groups	Medium-sized organizations focused on environmental protection	Medium Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
5babfb5b-408a-4bc2-a17a-4e606af0f898	Medium Sustainability initiatives	Medium-sized initiatives focused on sustainable practices	Medium Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
2586881c-6ad7-46b4-9de7-3d3d541e31a7	Medium International and local companies	Medium-sized international and local companies	Medium Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
ae6cf724-04e5-48a9-920e-fd3d98a447dc	Large Registered NGOs	Large registered non-governmental organizations	Large Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
e543ff33-cbdf-465f-9d48-b3cb26724d94	Large Not-for-Profit Organisations	Large not-for-profit organizations	Large Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
971e091b-17d9-4c95-a4d1-379468b166f3	Large Churches	Large religious institutions - Churches	Large Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
7512b63a-6bf3-4aae-899c-a3ef8c2c18ca	Large Mosques	Large religious institutions - Mosques	Large Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
fc7b4d15-7e87-4bfc-9bcb-d9955463ec03	Large Temples	Large religious institutions - Temples	Large Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
c2347f37-0b12-4d23-8244-be88a9c2da50	Large Spiritual Institutions	Large spiritual and religious institutions	Large Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
ab72e06d-c46f-414c-8aca-09f4791cbd3a	Large Cultural Preservation Societies	Large organizations focused on cultural preservation	Large Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
ff92e90e-8dd7-4aa4-a9f9-7c72c051a597	Large Education Groups	Large educational organizations and groups	Large Organizations	t	2025-09-08 10:52:47.17	2025-09-08 10:52:47.17	\N	\N	\N	APPROVED
\.


--
-- Data for Name: user_identifications; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.user_identifications (id, user_id, identification_type_id, id_number, is_active, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: user_positions; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.user_positions (id, user_id, position_id, start_date, end_date, is_active, notes) FROM stdin;
\.


--
-- Data for Name: user_profiles; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.user_profiles (id, user_id, nwa_email, country_id, city_id, mobile, bio, date_of_birth, title_id, created_at, updated_at, first_name, last_name, peace_ambassador_number, personal_email, phone, postal_code, region_id, region_text, street_address_1, street_address_2, town) FROM stdin;
\.


--
-- Data for Name: user_project_scopes; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.user_project_scopes (id, user_id, project_id, scope_id, granted_at, granted_by) FROM stdin;
\.


--
-- Data for Name: user_remote_server_access; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.user_remote_server_access (id, user_id, remote_server_id, granted_by, granted_at, is_active, notes) FROM stdin;
\.


--
-- Data for Name: user_remote_server_permissions; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.user_remote_server_permissions (id, user_id, remote_server_id, permission_name, granted_by, granted_at, expires_at, is_active) FROM stdin;
\.


--
-- Data for Name: user_roles; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.user_roles (id, user_id, role_id, assigned_at, assigned_by) FROM stdin;
cmfb1brta0027pp9n0dzfyyfw	cmfb1brt60025pp9n2vrxhx2w	cmfb1brem0000pp9nnv3scqmq	2025-09-08 11:25:01.966	\N
cmfb1brtd0029pp9nos7aud7b	cmfb1brt60025pp9n2vrxhx2w	cmfb1bres0001pp9n2t0gs0ze	2025-09-08 11:25:01.97	\N
cmfb1bs0u002cpp9ndoy658ux	cmfb1bs0s002app9n9t5wuou7	cmfb1bres0001pp9n2t0gs0ze	2025-09-08 11:25:02.239	\N
\.


--
-- Data for Name: user_treaties; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.user_treaties (id, user_id, treaty_id, assigned_at, status, notes) FROM stdin;
\.


--
-- Data for Name: user_treaty_numbers; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.user_treaty_numbers (id, user_id, treaty_type_id, treaty_number, is_active, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: user_treaty_types; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.user_treaty_types (id, user_id, treaty_type_id, selected_at, status, notes) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.users (id, name, email, email_verified, image, password_hash, two_factor_secret, two_factor_enabled, created_at, updated_at, two_factor_enforced, backup_codes) FROM stdin;
cmfb1brt60025pp9n2vrxhx2w	Test User	<EMAIL>	\N	\N	$2a$12$H/Ulrw65LQrgZomxOeY2cuYoecN.KkbbFs0sIzXoiq6ffX4I9BNVy	\N	f	2025-09-08 11:25:01.962	2025-09-08 11:25:01.962	f	{}
cmfb1bs0s002app9n9t5wuou7	darren edward	<EMAIL>	\N	\N	$2a$12$Natz5V6LnUGqt8Lxh/6vuO91rG743bXylOvithGGa0I9IZmDumhfG	\N	f	2025-09-08 11:25:02.236	2025-09-08 11:25:02.236	f	{}
\.


--
-- Data for Name: verification_tokens; Type: TABLE DATA; Schema: public; Owner: nwa_user
--

COPY public.verification_tokens (identifier, token, expires) FROM stdin;
\.


--
-- Name: categories_id_seq; Type: SEQUENCE SET; Schema: public; Owner: nwa_user
--

SELECT pg_catalog.setval('public.categories_id_seq', 19, true);


--
-- Name: cities_id_seq; Type: SEQUENCE SET; Schema: public; Owner: nwa_user
--

SELECT pg_catalog.setval('public.cities_id_seq', 412, true);


--
-- Name: countries_id_seq; Type: SEQUENCE SET; Schema: public; Owner: nwa_user
--

SELECT pg_catalog.setval('public.countries_id_seq', 40, true);


--
-- Name: country_address_formats_id_seq; Type: SEQUENCE SET; Schema: public; Owner: nwa_user
--

SELECT pg_catalog.setval('public.country_address_formats_id_seq', 4, true);


--
-- Name: regions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: nwa_user
--

SELECT pg_catalog.setval('public.regions_id_seq', 34, true);


--
-- Name: subcategories_id_seq; Type: SEQUENCE SET; Schema: public; Owner: nwa_user
--

SELECT pg_catalog.setval('public.subcategories_id_seq', 81, true);


--
-- Name: system_settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: nwa_user
--

SELECT pg_catalog.setval('public.system_settings_id_seq', 1, false);


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: regions administrative_divisions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.regions
    ADD CONSTRAINT administrative_divisions_pkey PRIMARY KEY (id);


--
-- Name: audit_logs audit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_pkey PRIMARY KEY (id);


--
-- Name: authorization_codes authorization_codes_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.authorization_codes
    ADD CONSTRAINT authorization_codes_pkey PRIMARY KEY (id);


--
-- Name: categories categories_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_pkey PRIMARY KEY (id);


--
-- Name: cities cities_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.cities
    ADD CONSTRAINT cities_pkey PRIMARY KEY (id);


--
-- Name: countries countries_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.countries
    ADD CONSTRAINT countries_pkey PRIMARY KEY (id);


--
-- Name: country_address_formats country_address_formats_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.country_address_formats
    ADD CONSTRAINT country_address_formats_pkey PRIMARY KEY (id);


--
-- Name: file_attachments file_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.file_attachments
    ADD CONSTRAINT file_attachments_pkey PRIMARY KEY (id);


--
-- Name: identification_types identification_types_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.identification_types
    ADD CONSTRAINT identification_types_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: oauth_tokens oauth_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.oauth_tokens
    ADD CONSTRAINT oauth_tokens_pkey PRIMARY KEY (id);


--
-- Name: ordinance_documents ordinance_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.ordinance_documents
    ADD CONSTRAINT ordinance_documents_pkey PRIMARY KEY (id);


--
-- Name: ordinance_types ordinance_types_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.ordinance_types
    ADD CONSTRAINT ordinance_types_pkey PRIMARY KEY (id);


--
-- Name: ordinances ordinances_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.ordinances
    ADD CONSTRAINT ordinances_pkey PRIMARY KEY (id);


--
-- Name: pending_bulk_uploads pending_bulk_uploads_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.pending_bulk_uploads
    ADD CONSTRAINT pending_bulk_uploads_pkey PRIMARY KEY (id);


--
-- Name: permissions permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT permissions_pkey PRIMARY KEY (id);


--
-- Name: positions positions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.positions
    ADD CONSTRAINT positions_pkey PRIMARY KEY (id);


--
-- Name: projects projects_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_pkey PRIMARY KEY (id);


--
-- Name: remote_server_permissions remote_server_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.remote_server_permissions
    ADD CONSTRAINT remote_server_permissions_pkey PRIMARY KEY (id);


--
-- Name: remote_servers remote_servers_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.remote_servers
    ADD CONSTRAINT remote_servers_pkey PRIMARY KEY (id);


--
-- Name: role_permissions role_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_pkey PRIMARY KEY (id);


--
-- Name: role_remote_server_access role_remote_server_access_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.role_remote_server_access
    ADD CONSTRAINT role_remote_server_access_pkey PRIMARY KEY (id);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (id);


--
-- Name: scopes scopes_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.scopes
    ADD CONSTRAINT scopes_pkey PRIMARY KEY (id);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: subcategories subcategories_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.subcategories
    ADD CONSTRAINT subcategories_pkey PRIMARY KEY (id);


--
-- Name: system_settings system_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.system_settings
    ADD CONSTRAINT system_settings_pkey PRIMARY KEY (id);


--
-- Name: title_positions title_positions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.title_positions
    ADD CONSTRAINT title_positions_pkey PRIMARY KEY (id);


--
-- Name: titles titles_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.titles
    ADD CONSTRAINT titles_pkey PRIMARY KEY (id);


--
-- Name: treaties treaties_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaties
    ADD CONSTRAINT treaties_pkey PRIMARY KEY (id);


--
-- Name: treaty_treaty_types treaty_treaty_types_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_treaty_types
    ADD CONSTRAINT treaty_treaty_types_pkey PRIMARY KEY (id);


--
-- Name: treaty_type_details treaty_type_details_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_type_details
    ADD CONSTRAINT treaty_type_details_pkey PRIMARY KEY (id);


--
-- Name: treaty_types treaty_types_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_types
    ADD CONSTRAINT treaty_types_pkey PRIMARY KEY (id);


--
-- Name: user_identifications user_identifications_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_identifications
    ADD CONSTRAINT user_identifications_pkey PRIMARY KEY (id);


--
-- Name: user_positions user_positions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_positions
    ADD CONSTRAINT user_positions_pkey PRIMARY KEY (id);


--
-- Name: user_profiles user_profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT user_profiles_pkey PRIMARY KEY (id);


--
-- Name: user_project_scopes user_project_scopes_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_project_scopes
    ADD CONSTRAINT user_project_scopes_pkey PRIMARY KEY (id);


--
-- Name: user_remote_server_access user_remote_server_access_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_remote_server_access
    ADD CONSTRAINT user_remote_server_access_pkey PRIMARY KEY (id);


--
-- Name: user_remote_server_permissions user_remote_server_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_remote_server_permissions
    ADD CONSTRAINT user_remote_server_permissions_pkey PRIMARY KEY (id);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (id);


--
-- Name: user_treaties user_treaties_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_treaties
    ADD CONSTRAINT user_treaties_pkey PRIMARY KEY (id);


--
-- Name: user_treaties user_treaties_user_id_treaty_id_key; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_treaties
    ADD CONSTRAINT user_treaties_user_id_treaty_id_key UNIQUE (user_id, treaty_id);


--
-- Name: user_treaty_numbers user_treaty_numbers_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_treaty_numbers
    ADD CONSTRAINT user_treaty_numbers_pkey PRIMARY KEY (id);


--
-- Name: user_treaty_types user_treaty_types_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_treaty_types
    ADD CONSTRAINT user_treaty_types_pkey PRIMARY KEY (id);


--
-- Name: user_treaty_types user_treaty_types_user_id_treaty_type_id_key; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_treaty_types
    ADD CONSTRAINT user_treaty_types_user_id_treaty_type_id_key UNIQUE (user_id, treaty_type_id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: audit_logs_project_id_api_endpoint_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX audit_logs_project_id_api_endpoint_idx ON public.audit_logs USING btree (project_id, api_endpoint);


--
-- Name: audit_logs_resource_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX audit_logs_resource_idx ON public.audit_logs USING btree (resource);


--
-- Name: audit_logs_timestamp_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX audit_logs_timestamp_idx ON public.audit_logs USING btree ("timestamp");


--
-- Name: audit_logs_user_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX audit_logs_user_id_idx ON public.audit_logs USING btree (user_id);


--
-- Name: authorization_codes_code_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX authorization_codes_code_key ON public.authorization_codes USING btree (code);


--
-- Name: cities_country_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX cities_country_id_idx ON public.cities USING btree (country_id);


--
-- Name: cities_name_country_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX cities_name_country_id_key ON public.cities USING btree (name, country_id);


--
-- Name: countries_code_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX countries_code_key ON public.countries USING btree (code);


--
-- Name: country_address_formats_country_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX country_address_formats_country_id_key ON public.country_address_formats USING btree (country_id);


--
-- Name: file_attachments_treaty_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX file_attachments_treaty_id_idx ON public.file_attachments USING btree (treaty_id);


--
-- Name: identification_types_category_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX identification_types_category_idx ON public.identification_types USING btree (category);


--
-- Name: identification_types_is_active_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX identification_types_is_active_idx ON public.identification_types USING btree (is_active);


--
-- Name: identification_types_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX identification_types_name_key ON public.identification_types USING btree (name);


--
-- Name: idx_audit_logs_city; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_audit_logs_city ON public.audit_logs USING btree (city);


--
-- Name: idx_audit_logs_country_code; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_audit_logs_country_code ON public.audit_logs USING btree (country_code);


--
-- Name: idx_audit_logs_geo_composite; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_audit_logs_geo_composite ON public.audit_logs USING btree (country_code, city, "timestamp");


--
-- Name: idx_audit_logs_remote_server_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_audit_logs_remote_server_id ON public.audit_logs USING btree (remote_server_id);


--
-- Name: idx_audit_logs_server_activity; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_audit_logs_server_activity ON public.audit_logs USING btree (remote_server_id, "timestamp", action);


--
-- Name: idx_remote_server_permissions_active; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_remote_server_permissions_active ON public.remote_server_permissions USING btree (is_active);


--
-- Name: idx_remote_server_permissions_last_synced; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_remote_server_permissions_last_synced ON public.remote_server_permissions USING btree (last_synced_at);


--
-- Name: idx_remote_server_permissions_name; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_remote_server_permissions_name ON public.remote_server_permissions USING btree (permission_name);


--
-- Name: idx_remote_server_permissions_server_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_remote_server_permissions_server_id ON public.remote_server_permissions USING btree (remote_server_id);


--
-- Name: idx_remote_servers_allowed_origins; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_remote_servers_allowed_origins ON public.remote_servers USING gin (allowed_origins);


--
-- Name: idx_remote_servers_callback_url; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_remote_servers_callback_url ON public.remote_servers USING btree (callback_url);


--
-- Name: idx_remote_servers_token_endpoint_auth_method; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_remote_servers_token_endpoint_auth_method ON public.remote_servers USING btree (token_endpoint_auth_method);


--
-- Name: idx_role_remote_server_access_active; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_role_remote_server_access_active ON public.role_remote_server_access USING btree (is_active);


--
-- Name: idx_role_remote_server_access_created_at; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_role_remote_server_access_created_at ON public.role_remote_server_access USING btree (created_at);


--
-- Name: idx_role_remote_server_access_role_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_role_remote_server_access_role_id ON public.role_remote_server_access USING btree (role_id);


--
-- Name: idx_role_remote_server_access_server_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_role_remote_server_access_server_id ON public.role_remote_server_access USING btree (remote_server_id);


--
-- Name: idx_treaty_treaty_types_assigned_at; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_treaty_treaty_types_assigned_at ON public.treaty_treaty_types USING btree (assigned_at);


--
-- Name: idx_treaty_treaty_types_treaty_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_treaty_treaty_types_treaty_id ON public.treaty_treaty_types USING btree (treaty_id);


--
-- Name: idx_treaty_treaty_types_treaty_type_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_treaty_treaty_types_treaty_type_id ON public.treaty_treaty_types USING btree (treaty_type_id);


--
-- Name: idx_user_remote_server_access_active; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_remote_server_access_active ON public.user_remote_server_access USING btree (is_active);


--
-- Name: idx_user_remote_server_access_granted_at; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_remote_server_access_granted_at ON public.user_remote_server_access USING btree (granted_at);


--
-- Name: idx_user_remote_server_access_server_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_remote_server_access_server_id ON public.user_remote_server_access USING btree (remote_server_id);


--
-- Name: idx_user_remote_server_access_user_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_remote_server_access_user_id ON public.user_remote_server_access USING btree (user_id);


--
-- Name: idx_user_remote_server_permissions_active; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_remote_server_permissions_active ON public.user_remote_server_permissions USING btree (is_active);


--
-- Name: idx_user_remote_server_permissions_expires_at; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_remote_server_permissions_expires_at ON public.user_remote_server_permissions USING btree (expires_at);


--
-- Name: idx_user_remote_server_permissions_granted_at; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_remote_server_permissions_granted_at ON public.user_remote_server_permissions USING btree (granted_at);


--
-- Name: idx_user_remote_server_permissions_name; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_remote_server_permissions_name ON public.user_remote_server_permissions USING btree (permission_name);


--
-- Name: idx_user_remote_server_permissions_server_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_remote_server_permissions_server_id ON public.user_remote_server_permissions USING btree (remote_server_id);


--
-- Name: idx_user_remote_server_permissions_user_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_remote_server_permissions_user_id ON public.user_remote_server_permissions USING btree (user_id);


--
-- Name: idx_user_treaties_treaty_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_treaties_treaty_id ON public.user_treaties USING btree (treaty_id);


--
-- Name: idx_user_treaties_user_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_treaties_user_id ON public.user_treaties USING btree (user_id);


--
-- Name: idx_user_treaty_types_treaty_type_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_treaty_types_treaty_type_id ON public.user_treaty_types USING btree (treaty_type_id);


--
-- Name: idx_user_treaty_types_user_id; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX idx_user_treaty_types_user_id ON public.user_treaty_types USING btree (user_id);


--
-- Name: ordinance_types_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX ordinance_types_name_key ON public.ordinance_types USING btree (name);


--
-- Name: permissions_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX permissions_name_key ON public.permissions USING btree (name);


--
-- Name: permissions_resource_action_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX permissions_resource_action_key ON public.permissions USING btree (resource, action);


--
-- Name: positions_title_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX positions_title_key ON public.positions USING btree (title);


--
-- Name: projects_api_key_hash_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX projects_api_key_hash_idx ON public.projects USING btree (api_key_hash);


--
-- Name: projects_api_key_hash_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX projects_api_key_hash_key ON public.projects USING btree (api_key_hash);


--
-- Name: projects_is_active_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX projects_is_active_idx ON public.projects USING btree (is_active);


--
-- Name: regions_country_id_code_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX regions_country_id_code_key ON public.regions USING btree (country_id, code);


--
-- Name: regions_country_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX regions_country_id_idx ON public.regions USING btree (country_id);


--
-- Name: regions_country_id_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX regions_country_id_name_key ON public.regions USING btree (country_id, name);


--
-- Name: regions_is_active_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX regions_is_active_idx ON public.regions USING btree (is_active);


--
-- Name: remote_server_permissions_remote_server_id_permission_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX remote_server_permissions_remote_server_id_permission_name_key ON public.remote_server_permissions USING btree (remote_server_id, permission_name);


--
-- Name: remote_servers_client_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX remote_servers_client_id_key ON public.remote_servers USING btree (client_id);


--
-- Name: role_permissions_role_id_permission_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX role_permissions_role_id_permission_id_key ON public.role_permissions USING btree (role_id, permission_id);


--
-- Name: role_remote_server_access_role_id_remote_server_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX role_remote_server_access_role_id_remote_server_id_key ON public.role_remote_server_access USING btree (role_id, remote_server_id);


--
-- Name: roles_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX roles_name_key ON public.roles USING btree (name);


--
-- Name: scopes_is_active_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX scopes_is_active_idx ON public.scopes USING btree (is_active);


--
-- Name: scopes_name_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX scopes_name_idx ON public.scopes USING btree (name);


--
-- Name: scopes_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX scopes_name_key ON public.scopes USING btree (name);


--
-- Name: sessions_session_token_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX sessions_session_token_key ON public.sessions USING btree (session_token);


--
-- Name: title_positions_title_id_position_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX title_positions_title_id_position_id_key ON public.title_positions USING btree (title_id, position_id);


--
-- Name: titles_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX titles_name_key ON public.titles USING btree (name);


--
-- Name: treaty_treaty_types_treaty_id_treaty_type_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX treaty_treaty_types_treaty_id_treaty_type_id_key ON public.treaty_treaty_types USING btree (treaty_id, treaty_type_id);


--
-- Name: treaty_type_details_status_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX treaty_type_details_status_idx ON public.treaty_type_details USING btree (status);


--
-- Name: treaty_type_details_treaty_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX treaty_type_details_treaty_id_idx ON public.treaty_type_details USING btree (treaty_id);


--
-- Name: treaty_type_details_treaty_type_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX treaty_type_details_treaty_type_id_idx ON public.treaty_type_details USING btree (treaty_type_id);


--
-- Name: treaty_type_details_user_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX treaty_type_details_user_id_idx ON public.treaty_type_details USING btree (user_id);


--
-- Name: treaty_type_details_user_id_treaty_id_treaty_type_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX treaty_type_details_user_id_treaty_id_treaty_type_id_key ON public.treaty_type_details USING btree (user_id, treaty_id, treaty_type_id);


--
-- Name: treaty_types_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX treaty_types_name_key ON public.treaty_types USING btree (name);


--
-- Name: user_identifications_identification_type_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX user_identifications_identification_type_id_idx ON public.user_identifications USING btree (identification_type_id);


--
-- Name: user_identifications_user_id_identification_type_id_id_numb_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX user_identifications_user_id_identification_type_id_id_numb_key ON public.user_identifications USING btree (user_id, identification_type_id, id_number);


--
-- Name: user_identifications_user_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX user_identifications_user_id_idx ON public.user_identifications USING btree (user_id);


--
-- Name: user_profiles_country_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX user_profiles_country_id_idx ON public.user_profiles USING btree (country_id);


--
-- Name: user_profiles_nwa_email_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX user_profiles_nwa_email_key ON public.user_profiles USING btree (nwa_email);


--
-- Name: user_profiles_peace_ambassador_number_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX user_profiles_peace_ambassador_number_idx ON public.user_profiles USING btree (peace_ambassador_number);


--
-- Name: user_profiles_region_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX user_profiles_region_id_idx ON public.user_profiles USING btree (region_id);


--
-- Name: user_profiles_user_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX user_profiles_user_id_key ON public.user_profiles USING btree (user_id);


--
-- Name: user_project_scopes_user_id_project_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX user_project_scopes_user_id_project_id_idx ON public.user_project_scopes USING btree (user_id, project_id);


--
-- Name: user_project_scopes_user_id_project_id_scope_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX user_project_scopes_user_id_project_id_scope_id_key ON public.user_project_scopes USING btree (user_id, project_id, scope_id);


--
-- Name: user_remote_server_access_user_id_remote_server_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX user_remote_server_access_user_id_remote_server_id_key ON public.user_remote_server_access USING btree (user_id, remote_server_id);


--
-- Name: user_remote_server_permission_user_id_remote_server_id_perm_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX user_remote_server_permission_user_id_remote_server_id_perm_key ON public.user_remote_server_permissions USING btree (user_id, remote_server_id, permission_name);


--
-- Name: user_roles_user_id_role_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX user_roles_user_id_role_id_key ON public.user_roles USING btree (user_id, role_id);


--
-- Name: user_treaty_numbers_treaty_type_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX user_treaty_numbers_treaty_type_id_idx ON public.user_treaty_numbers USING btree (treaty_type_id);


--
-- Name: user_treaty_numbers_user_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX user_treaty_numbers_user_id_idx ON public.user_treaty_numbers USING btree (user_id);


--
-- Name: user_treaty_numbers_user_id_treaty_type_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX user_treaty_numbers_user_id_treaty_type_id_key ON public.user_treaty_numbers USING btree (user_id, treaty_type_id);


--
-- Name: users_email_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email);


--
-- Name: verification_tokens_identifier_token_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX verification_tokens_identifier_token_key ON public.verification_tokens USING btree (identifier, token);


--
-- Name: verification_tokens_token_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX verification_tokens_token_key ON public.verification_tokens USING btree (token);


--
-- Name: audit_logs audit_logs_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: audit_logs audit_logs_remote_server_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_remote_server_id_fkey FOREIGN KEY (remote_server_id) REFERENCES public.remote_servers(id);


--
-- Name: audit_logs audit_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: authorization_codes authorization_codes_remote_server_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.authorization_codes
    ADD CONSTRAINT authorization_codes_remote_server_id_fkey FOREIGN KEY (remote_server_id) REFERENCES public.remote_servers(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: authorization_codes authorization_codes_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.authorization_codes
    ADD CONSTRAINT authorization_codes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: cities cities_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.cities
    ADD CONSTRAINT cities_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.countries(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: country_address_formats country_address_formats_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.country_address_formats
    ADD CONSTRAINT country_address_formats_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.countries(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: file_attachments file_attachments_treaty_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.file_attachments
    ADD CONSTRAINT file_attachments_treaty_id_fkey FOREIGN KEY (treaty_id) REFERENCES public.treaties(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: oauth_tokens oauth_tokens_remote_server_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.oauth_tokens
    ADD CONSTRAINT oauth_tokens_remote_server_id_fkey FOREIGN KEY (remote_server_id) REFERENCES public.remote_servers(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: oauth_tokens oauth_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.oauth_tokens
    ADD CONSTRAINT oauth_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: ordinance_documents ordinance_documents_ordinance_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.ordinance_documents
    ADD CONSTRAINT ordinance_documents_ordinance_id_fkey FOREIGN KEY (ordinance_id) REFERENCES public.ordinances(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ordinances ordinances_ordinance_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.ordinances
    ADD CONSTRAINT ordinances_ordinance_type_id_fkey FOREIGN KEY (ordinance_type_id) REFERENCES public.ordinance_types(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ordinances ordinances_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.ordinances
    ADD CONSTRAINT ordinances_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: pending_bulk_uploads pending_bulk_uploads_uploader_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.pending_bulk_uploads
    ADD CONSTRAINT pending_bulk_uploads_uploader_id_fkey FOREIGN KEY (uploader_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: positions positions_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.positions
    ADD CONSTRAINT positions_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.positions(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: regions regions_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.regions
    ADD CONSTRAINT regions_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.countries(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: remote_server_permissions remote_server_permissions_remote_server_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.remote_server_permissions
    ADD CONSTRAINT remote_server_permissions_remote_server_id_fkey FOREIGN KEY (remote_server_id) REFERENCES public.remote_servers(id) ON DELETE CASCADE;


--
-- Name: role_permissions role_permissions_permission_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES public.permissions(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: role_permissions role_permissions_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: role_remote_server_access role_remote_server_access_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.role_remote_server_access
    ADD CONSTRAINT role_remote_server_access_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: role_remote_server_access role_remote_server_access_remote_server_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.role_remote_server_access
    ADD CONSTRAINT role_remote_server_access_remote_server_id_fkey FOREIGN KEY (remote_server_id) REFERENCES public.remote_servers(id) ON DELETE CASCADE;


--
-- Name: role_remote_server_access role_remote_server_access_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.role_remote_server_access
    ADD CONSTRAINT role_remote_server_access_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id) ON DELETE CASCADE;


--
-- Name: sessions sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: subcategories subcategories_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.subcategories
    ADD CONSTRAINT subcategories_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: title_positions title_positions_position_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.title_positions
    ADD CONSTRAINT title_positions_position_id_fkey FOREIGN KEY (position_id) REFERENCES public.positions(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: title_positions title_positions_title_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.title_positions
    ADD CONSTRAINT title_positions_title_id_fkey FOREIGN KEY (title_id) REFERENCES public.titles(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: treaties treaties_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaties
    ADD CONSTRAINT treaties_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: treaty_treaty_types treaty_treaty_types_assigned_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_treaty_types
    ADD CONSTRAINT treaty_treaty_types_assigned_by_fkey FOREIGN KEY (assigned_by) REFERENCES public.users(id);


--
-- Name: treaty_treaty_types treaty_treaty_types_treaty_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_treaty_types
    ADD CONSTRAINT treaty_treaty_types_treaty_id_fkey FOREIGN KEY (treaty_id) REFERENCES public.treaties(id) ON DELETE CASCADE;


--
-- Name: treaty_treaty_types treaty_treaty_types_treaty_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_treaty_types
    ADD CONSTRAINT treaty_treaty_types_treaty_type_id_fkey FOREIGN KEY (treaty_type_id) REFERENCES public.treaty_types(id) ON DELETE CASCADE;


--
-- Name: treaty_type_details treaty_type_details_current_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_type_details
    ADD CONSTRAINT treaty_type_details_current_city_id_fkey FOREIGN KEY (current_city_id) REFERENCES public.cities(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: treaty_type_details treaty_type_details_current_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_type_details
    ADD CONSTRAINT treaty_type_details_current_country_id_fkey FOREIGN KEY (current_country_id) REFERENCES public.countries(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: treaty_type_details treaty_type_details_residence_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_type_details
    ADD CONSTRAINT treaty_type_details_residence_city_id_fkey FOREIGN KEY (residence_city_id) REFERENCES public.cities(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: treaty_type_details treaty_type_details_residence_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_type_details
    ADD CONSTRAINT treaty_type_details_residence_country_id_fkey FOREIGN KEY (residence_country_id) REFERENCES public.countries(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: treaty_type_details treaty_type_details_treaty_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_type_details
    ADD CONSTRAINT treaty_type_details_treaty_id_fkey FOREIGN KEY (treaty_id) REFERENCES public.treaties(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: treaty_type_details treaty_type_details_treaty_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_type_details
    ADD CONSTRAINT treaty_type_details_treaty_type_id_fkey FOREIGN KEY (treaty_type_id) REFERENCES public.treaty_types(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: treaty_type_details treaty_type_details_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_type_details
    ADD CONSTRAINT treaty_type_details_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_identifications user_identifications_identification_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_identifications
    ADD CONSTRAINT user_identifications_identification_type_id_fkey FOREIGN KEY (identification_type_id) REFERENCES public.identification_types(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: user_identifications user_identifications_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_identifications
    ADD CONSTRAINT user_identifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_positions user_positions_position_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_positions
    ADD CONSTRAINT user_positions_position_id_fkey FOREIGN KEY (position_id) REFERENCES public.positions(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_positions user_positions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_positions
    ADD CONSTRAINT user_positions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_profiles user_profiles_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT user_profiles_city_id_fkey FOREIGN KEY (city_id) REFERENCES public.cities(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: user_profiles user_profiles_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT user_profiles_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.countries(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: user_profiles user_profiles_region_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT user_profiles_region_id_fkey FOREIGN KEY (region_id) REFERENCES public.regions(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: user_profiles user_profiles_title_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT user_profiles_title_id_fkey FOREIGN KEY (title_id) REFERENCES public.titles(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: user_profiles user_profiles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT user_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_project_scopes user_project_scopes_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_project_scopes
    ADD CONSTRAINT user_project_scopes_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_project_scopes user_project_scopes_scope_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_project_scopes
    ADD CONSTRAINT user_project_scopes_scope_id_fkey FOREIGN KEY (scope_id) REFERENCES public.scopes(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_project_scopes user_project_scopes_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_project_scopes
    ADD CONSTRAINT user_project_scopes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_remote_server_access user_remote_server_access_granted_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_remote_server_access
    ADD CONSTRAINT user_remote_server_access_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES public.users(id);


--
-- Name: user_remote_server_access user_remote_server_access_remote_server_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_remote_server_access
    ADD CONSTRAINT user_remote_server_access_remote_server_id_fkey FOREIGN KEY (remote_server_id) REFERENCES public.remote_servers(id) ON DELETE CASCADE;


--
-- Name: user_remote_server_access user_remote_server_access_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_remote_server_access
    ADD CONSTRAINT user_remote_server_access_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_remote_server_permissions user_remote_server_permissions_granted_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_remote_server_permissions
    ADD CONSTRAINT user_remote_server_permissions_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES public.users(id);


--
-- Name: user_remote_server_permissions user_remote_server_permissions_remote_server_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_remote_server_permissions
    ADD CONSTRAINT user_remote_server_permissions_remote_server_id_fkey FOREIGN KEY (remote_server_id) REFERENCES public.remote_servers(id) ON DELETE CASCADE;


--
-- Name: user_remote_server_permissions user_remote_server_permissions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_remote_server_permissions
    ADD CONSTRAINT user_remote_server_permissions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_roles user_roles_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_roles user_roles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_treaties user_treaties_treaty_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_treaties
    ADD CONSTRAINT user_treaties_treaty_id_fkey FOREIGN KEY (treaty_id) REFERENCES public.treaties(id) ON DELETE CASCADE;


--
-- Name: user_treaties user_treaties_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_treaties
    ADD CONSTRAINT user_treaties_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_treaty_numbers user_treaty_numbers_treaty_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_treaty_numbers
    ADD CONSTRAINT user_treaty_numbers_treaty_type_id_fkey FOREIGN KEY (treaty_type_id) REFERENCES public.treaty_types(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: user_treaty_numbers user_treaty_numbers_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_treaty_numbers
    ADD CONSTRAINT user_treaty_numbers_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_treaty_types user_treaty_types_treaty_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_treaty_types
    ADD CONSTRAINT user_treaty_types_treaty_type_id_fkey FOREIGN KEY (treaty_type_id) REFERENCES public.treaty_types(id) ON DELETE CASCADE;


--
-- Name: user_treaty_types user_treaty_types_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_treaty_types
    ADD CONSTRAINT user_treaty_types_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: nwa_user
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;


--
-- PostgreSQL database dump complete
--

