import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const seedIdentificationTypes = async () => {
  console.log('Seeding identification types...');

  const identificationTypes = [
    {
      name: 'Passport',
      description: 'International travel document issued by national government',
      category: 'government'
    },
    {
      name: 'Driver License',
      description: 'Government-issued license permitting operation of motor vehicles',
      category: 'government'
    },
    {
      name: 'National ID Card',
      description: 'Primary identification document for citizens',
      category: 'government'
    },
    {
      name: 'Military ID',
      description: 'Armed forces identification document',
      category: 'military'
    },
    {
      name: 'State ID Card',
      description: 'Non-driver state-issued identification',
      category: 'government'
    },
    {
      name: 'Under 18 ID',
      description: 'Identification for minors under 18 years old',
      category: 'government'
    },
    {
      name: 'Veteran ID Card',
      description: 'Identification for military veterans',
      category: 'military'
    },
    {
      name: 'Federal Employee ID',
      description: 'Identification for government employees',
      category: 'government'
    },
    {
      name: 'Tribal ID Card',
      description: 'Identification for tribal nation members',
      category: 'government'
    },
    {
      name: 'Immigration Document',
      description: 'Visa, green card, or other immigration status proof',
      category: 'government'
    },
    {
      name: 'Professional License',
      description: 'Professional certification or license document',
      category: 'professional'
    },
    {
      name: 'Student ID',
      description: 'Educational institution identification',
      category: 'education'
    },
    {
      name: 'Healthcare Worker ID',
      description: 'Medical professional identification',
      category: 'professional'
    },
    {
      name: 'Diplomatic ID',
      description: 'Diplomatic passport or identification',
      category: 'diplomatic'
    },
    {
      name: 'Police ID',
      description: 'Law enforcement identification',
      category: 'government'
    }
  ];

  // Use upsert to avoid duplicates
  for (const idType of identificationTypes) {
    await prisma.identificationType.upsert({
      where: { name: idType.name },
      update: idType,
      create: idType,
    });
  }

  const count = await prisma.identificationType.count();
  console.log(`Seeded ${count} identification types.`);
};

if (require.main === module) {
  seedIdentificationTypes()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
