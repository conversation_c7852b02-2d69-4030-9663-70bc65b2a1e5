-- Migration: Add address format support for different countries
-- This handles postal code terminology, administrative divisions, and address field requirements

-- Country address format configuration
CREATE TABLE country_address_formats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    country_id UUID NOT NULL REFERENCES countries(id) ON DELETE CASCADE,
    postal_code_label VARCHAR(50) NOT NULL DEFAULT 'Postal Code', -- 'ZIP Code', 'Postcode', etc.
    postal_code_format VARCHAR(50), -- Regex or format like 'NNNNN' for US, 'NNNN' for NZ
    postal_code_required BOOLEAN DEFAULT false,
    
    -- Administrative division (State/Province/Region/County)
    admin_division_label VARCHAR(50), -- 'State', 'Province', 'Region', 'County', etc.
    admin_division_required BOOLEAN DEFAULT false,
    
    -- Address field configuration
    street_address_required BOOLEAN DEFAULT true,
    city_required BOOLEAN DEFAULT true,
    
    -- Address format template for display
    address_format_template TEXT, -- Template like "{street}\n{city}, {admin_division} {postal_code}\n{country}"
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Administrative divisions (States, Provinces, Regions, etc.)
CREATE TABLE administrative_divisions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    country_id UUID NOT NULL REFERENCES countries(id) ON DELETE CASCADE,
    code VARCHAR(10) NOT NULL, -- 'CA', 'TX', 'ON', 'QC', etc.
    name VARCHAR(255) NOT NULL, -- 'California', 'Texas', 'Ontario', 'Quebec'
    type VARCHAR(50) NOT NULL, -- 'state', 'province', 'region', 'territory', 'county'
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for performance
CREATE INDEX idx_country_address_formats_country_id ON country_address_formats(country_id);
CREATE INDEX idx_administrative_divisions_country_id ON administrative_divisions(country_id);
CREATE INDEX idx_administrative_divisions_code ON administrative_divisions(code);
CREATE UNIQUE INDEX idx_administrative_divisions_country_code ON administrative_divisions(country_id, code);

-- Add state/province field to user profiles
ALTER TABLE profiles ADD COLUMN administrative_division_id UUID REFERENCES administrative_divisions(id);
ALTER TABLE profiles ADD COLUMN administrative_division_text VARCHAR(255); -- For free text when not in our DB

-- Sample data for common countries
INSERT INTO country_address_formats (country_id, postal_code_label, postal_code_format, postal_code_required, admin_division_label, admin_division_required, address_format_template)
SELECT 
    c.id,
    CASE c.code
        WHEN 'US' THEN 'ZIP Code'
        WHEN 'GB' THEN 'Postcode'
        WHEN 'NZ' THEN 'Postcode'
        WHEN 'CA' THEN 'Postal Code'
        WHEN 'AU' THEN 'Postcode'
        ELSE 'Postal Code'
    END,
    CASE c.code
        WHEN 'US' THEN '^\\d{5}(-\\d{4})?$' -- 12345 or 12345-6789
        WHEN 'GB' THEN '^[A-Z]{1,2}\\d[A-Z\\d]? ?\\d[A-Z]{2}$' -- SW1A 1AA
        WHEN 'NZ' THEN '^\\d{4}$' -- 1234
        WHEN 'CA' THEN '^[A-Z]\\d[A-Z] ?\\d[A-Z]\\d$' -- K1A 0A6
        WHEN 'AU' THEN '^\\d{4}$' -- 2000
        ELSE '^.+$'
    END,
    CASE c.code
        WHEN 'US' THEN true
        WHEN 'GB' THEN true
        WHEN 'NZ' THEN true
        WHEN 'CA' THEN true
        WHEN 'AU' THEN true
        ELSE false
    END,
    CASE c.code
        WHEN 'US' THEN 'State'
        WHEN 'GB' THEN 'County'
        WHEN 'NZ' THEN 'Region'
        WHEN 'CA' THEN 'Province'
        WHEN 'AU' THEN 'State'
        WHEN 'DE' THEN 'State'
        WHEN 'FR' THEN 'Region'
        ELSE null
    END,
    CASE c.code
        WHEN 'US' THEN true
        WHEN 'CA' THEN true
        WHEN 'AU' THEN true
        ELSE false
    END,
    CASE c.code
        WHEN 'US' THEN '{street}\n{city}, {admin_division} {postal_code}\n{country}'
        WHEN 'GB' THEN '{street}\n{city}\n{admin_division}\n{postal_code}\n{country}'
        WHEN 'NZ' THEN '{street}\n{city} {postal_code}\n{admin_division}\n{country}'
        WHEN 'CA' THEN '{street}\n{city}, {admin_division} {postal_code}\n{country}'
        WHEN 'AU' THEN '{street}\n{city} {admin_division} {postal_code}\n{country}'
        ELSE '{street}\n{city}\n{country}'
    END
FROM countries c 
WHERE c.code IN ('US', 'GB', 'NZ', 'CA', 'AU', 'DE', 'FR');

-- Sample administrative divisions
-- US States (abbreviated sample)
INSERT INTO administrative_divisions (country_id, code, name, type)
SELECT c.id, 'CA', 'California', 'state' FROM countries c WHERE c.code = 'US'
UNION ALL
SELECT c.id, 'TX', 'Texas', 'state' FROM countries c WHERE c.code = 'US'
UNION ALL
SELECT c.id, 'NY', 'New York', 'state' FROM countries c WHERE c.code = 'US'
UNION ALL
SELECT c.id, 'FL', 'Florida', 'state' FROM countries c WHERE c.code = 'US'
UNION ALL
-- Canadian Provinces
SELECT c.id, 'ON', 'Ontario', 'province' FROM countries c WHERE c.code = 'CA'
UNION ALL
SELECT c.id, 'QC', 'Quebec', 'province' FROM countries c WHERE c.code = 'CA'
UNION ALL
SELECT c.id, 'BC', 'British Columbia', 'province' FROM countries c WHERE c.code = 'CA'
UNION ALL
-- NZ Regions
SELECT c.id, 'AUK', 'Auckland', 'region' FROM countries c WHERE c.code = 'NZ'
UNION ALL
SELECT c.id, 'CAN', 'Canterbury', 'region' FROM countries c WHERE c.code = 'NZ'
UNION ALL
SELECT c.id, 'WGN', 'Wellington', 'region' FROM countries c WHERE c.code = 'NZ'
UNION ALL
-- Australian States
SELECT c.id, 'NSW', 'New South Wales', 'state' FROM countries c WHERE c.code = 'AU'
UNION ALL
SELECT c.id, 'VIC', 'Victoria', 'state' FROM countries c WHERE c.code = 'AU'
UNION ALL
SELECT c.id, 'QLD', 'Queensland', 'state' FROM countries c WHERE c.code = 'AU';
