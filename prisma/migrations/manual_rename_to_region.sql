-- Manual migration to rename administrative division terminology to region
-- This will be applied before generating the Prisma migration

-- Rename table
ALTER TABLE "administrative_divisions" RENAME TO "regions";

-- Rename columns in user_profiles
ALTER TABLE "user_profiles" RENAME COLUMN "administrative_division_id" TO "region_id";
ALTER TABLE "user_profiles" RENAME COLUMN "administrative_division_text" TO "region_text";

-- Rename columns in country_address_formats
ALTER TABLE "country_address_formats" RENAME COLUMN "administrative_division_label" TO "region_label";
ALTER TABLE "country_address_formats" RENAME COLUMN "administrative_division_required" TO "region_required";

-- Rename indexes (they should be automatically renamed with the column changes, but let's be explicit)
DROP INDEX IF EXISTS "user_profiles_administrative_division_id_idx";
CREATE INDEX "user_profiles_region_id_idx" ON "user_profiles"("region_id");

DROP INDEX IF EXISTS "administrative_divisions_country_id_idx";
CREATE INDEX "regions_country_id_idx" ON "regions"("country_id");

DROP INDEX IF EXISTS "administrative_divisions_is_active_idx";
CREATE INDEX "regions_is_active_idx" ON "regions"("is_active");

-- Rename unique constraints
DROP INDEX IF EXISTS "administrative_divisions_country_id_code_key";
DROP INDEX IF EXISTS "administrative_divisions_country_id_name_key";
CREATE UNIQUE INDEX "regions_country_id_code_key" ON "regions"("country_id", "code");
CREATE UNIQUE INDEX "regions_country_id_name_key" ON "regions"("country_id", "name");

-- Update foreign key constraint names (drop old, create new)
ALTER TABLE "user_profiles" DROP CONSTRAINT IF EXISTS "user_profiles_administrative_division_id_fkey";
ALTER TABLE "user_profiles" ADD CONSTRAINT "user_profiles_region_id_fkey" FOREIGN KEY ("region_id") REFERENCES "regions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "regions" DROP CONSTRAINT IF EXISTS "administrative_divisions_country_id_fkey";
ALTER TABLE "regions" ADD CONSTRAINT "regions_country_id_fkey" FOREIGN KEY ("country_id") REFERENCES "countries"("id") ON DELETE CASCADE ON UPDATE CASCADE;
