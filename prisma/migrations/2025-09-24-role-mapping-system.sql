-- Migration: Role Mapping System Implementation
-- Created: 2025-09-24
-- Description: Creates dedicated role mapping tables and migrates existing data

-- =============================================
-- PHASE 1: CREATE NEW TABLES
-- =============================================

-- Create remote_server_role_mappings table
CREATE TABLE IF NOT EXISTS remote_server_role_mappings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  local_role_id UUID NOT NULL,
  remote_server_id UUID NOT NULL,
  remote_role_name VARCHAR(255) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by <PERSON><PERSON><PERSON>,

  CONSTRAINT fk_remote_server_role_mappings_local_role
    FOREIGN KEY (local_role_id) REFERENCES roles(id) ON DELETE CASCADE,
  CONSTRAINT fk_remote_server_role_mappings_remote_server
    FOREIGN KEY (remote_server_id) REFERENCES remote_servers(id) ON DELETE CASCADE,
  CONSTRAINT fk_remote_server_role_mappings_created_by
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  CONSTRAINT unique_local_role_server_mapping
    UNIQUE(local_role_id, remote_server_id, remote_role_name)
);

-- Create remote_server_permission_mappings table
CREATE TABLE IF NOT EXISTS remote_server_permission_mappings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  remote_server_role_mapping_id UUID NOT NULL,
  local_permission_name VARCHAR(255) NOT NULL,
  remote_permission_name VARCHAR(255) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  CONSTRAINT fk_remote_server_permission_mappings_role_mapping
    FOREIGN KEY (remote_server_role_mapping_id) REFERENCES remote_server_role_mappings(id) ON DELETE CASCADE,
  CONSTRAINT unique_role_mapping_permission
    UNIQUE(remote_server_role_mapping_id, local_permission_name)
);

-- =============================================
-- PHASE 2: CREATE INDEXES
-- =============================================

-- Indexes for remote_server_role_mappings
CREATE INDEX IF NOT EXISTS idx_remote_server_role_mappings_server
  ON remote_server_role_mappings(remote_server_id);
CREATE INDEX IF NOT EXISTS idx_remote_server_role_mappings_local_role
  ON remote_server_role_mappings(local_role_id);
CREATE INDEX IF NOT EXISTS idx_remote_server_role_mappings_active
  ON remote_server_role_mappings(is_active);
CREATE INDEX IF NOT EXISTS idx_remote_server_role_mappings_created_by
  ON remote_server_role_mappings(created_by);

-- Indexes for remote_server_permission_mappings
CREATE INDEX IF NOT EXISTS idx_remote_server_permission_mappings_mapping
  ON remote_server_permission_mappings(remote_server_role_mapping_id);
CREATE INDEX IF NOT EXISTS idx_remote_server_permission_mappings_local
  ON remote_server_permission_mappings(local_permission_name);
CREATE INDEX IF NOT EXISTS idx_remote_server_permission_mappings_active
  ON remote_server_permission_mappings(is_active);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_role_mappings_server_active
  ON remote_server_role_mappings(remote_server_id, is_active);
CREATE INDEX IF NOT EXISTS idx_permission_mappings_mapping_active
  ON remote_server_permission_mappings(remote_server_role_mapping_id, is_active);

-- =============================================
-- PHASE 3: DATA MIGRATION
-- =============================================

-- Migrate existing role_remote_server_access data
INSERT INTO remote_server_role_mappings (
  local_role_id,
  remote_server_id,
  remote_role_name,
  is_active,
  created_at,
  created_by
)
SELECT
  role_id,
  remote_server_id,
  roles.name as remote_role_name, -- Use local role name as default
  is_active,
  created_at,
  created_by
FROM role_remote_server_access
JOIN roles ON role_remote_server_access.role_id = roles.id
WHERE role_remote_server_access.is_active = true
ON CONFLICT (local_role_id, remote_server_id, remote_role_name)
DO NOTHING;

-- Migrate auto-grant permissions to permission mappings
INSERT INTO remote_server_permission_mappings (
  remote_server_role_mapping_id,
  local_permission_name,
  remote_permission_name,
  is_active
)
SELECT
  rsm.id as remote_server_role_mapping_id,
  unnest(rra.auto_grant_permissions) as local_permission_name,
  unnest(rra.auto_grant_permissions) as remote_permission_name, -- Same as local for now
  true as is_active
FROM role_remote_server_access rra
JOIN remote_server_role_mappings rsm ON rra.role_id = rsm.local_role_id
  AND rra.remote_server_id = rsm.remote_server_id
WHERE rra.is_active = true
  AND array_length(rra.auto_grant_permissions, 1) > 0
ON CONFLICT (remote_server_role_mapping_id, local_permission_name)
DO NOTHING;

-- =============================================
-- PHASE 4: VALIDATION QUERIES
-- =============================================

-- Verify migration completed successfully
DO $$
DECLARE
  old_count INTEGER;
  new_count INTEGER;
  permission_count INTEGER;
BEGIN
  -- Count old records
  SELECT COUNT(*) INTO old_count FROM role_remote_server_access WHERE is_active = true;

  -- Count new records
  SELECT COUNT(*) INTO new_count FROM remote_server_role_mappings WHERE is_active = true;

  -- Count permission mappings
  SELECT COUNT(*) INTO permission_count FROM remote_server_permission_mappings WHERE is_active = true;

  RAISE NOTICE 'Migration Summary:';
  RAISE NOTICE '  Old role_remote_server_access records: %', old_count;
  RAISE NOTICE '  New remote_server_role_mappings records: %', new_count;
  RAISE NOTICE '  Permission mappings created: %', permission_count;

  IF old_count = new_count THEN
    RAISE NOTICE '  ✅ Migration successful: Record counts match';
  ELSE
    RAISE NOTICE '  ⚠️  Migration warning: Record counts differ (old: %, new: %)', old_count, new_count;
  END IF;
END $$;

-- =============================================
-- PHASE 5: CREATE VIEWS FOR BACKWARD COMPATIBILITY
-- =============================================

-- Create view to maintain compatibility with existing queries
CREATE OR REPLACE VIEW role_remote_server_access_view AS
SELECT
  rsm.id,
  rsm.local_role_id as role_id,
  rsm.remote_server_id,
  rsm.is_active,
  rsm.created_at,
  rsm.created_by,
  rsm.updated_at,
  ARRAY_AGG(DISTINCT rspm.remote_permission_name) FILTER (WHERE rspm.is_active = true) as auto_grant_permissions
FROM remote_server_role_mappings rsm
LEFT JOIN remote_server_permission_mappings rspm ON rsm.id = rspm.remote_server_role_mapping_id
WHERE rsm.is_active = true
GROUP BY rsm.id, rsm.local_role_id, rsm.remote_server_id, rsm.is_active, rsm.created_at, rsm.created_by, rsm.updated_at;

-- Grant access to the view
GRANT SELECT ON role_remote_server_access_view TO authenticated;
GRANT SELECT ON role_remote_server_access_view TO service_role;

-- =============================================
-- PHASE 6: ROLLBACK SCRIPT (IF NEEDED)
-- =============================================

/*
-- Rollback: Remove new tables and restore old data
-- WARNING: This will delete all new mapping data!

-- 1. Drop new tables
DROP TABLE IF EXISTS remote_server_permission_mappings CASCADE;
DROP TABLE IF EXISTS remote_server_role_mappings CASCADE;

-- 2. Drop indexes
DROP INDEX IF EXISTS idx_remote_server_role_mappings_server;
DROP INDEX IF EXISTS idx_remote_server_role_mappings_local_role;
DROP INDEX IF EXISTS idx_remote_server_role_mappings_active;
DROP INDEX IF EXISTS idx_remote_server_role_mappings_created_by;
DROP INDEX IF EXISTS idx_remote_server_permission_mappings_mapping;
DROP INDEX IF EXISTS idx_remote_server_permission_mappings_local;
DROP INDEX IF EXISTS idx_remote_server_permission_mappings_active;
DROP INDEX IF EXISTS idx_role_mappings_server_active;
DROP INDEX IF EXISTS idx_permission_mappings_mapping_active;

-- 3. Drop view
DROP VIEW IF EXISTS role_remote_server_access_view;

-- 4. Restore old table (if backup exists)
-- This would need to be done from a backup
*/