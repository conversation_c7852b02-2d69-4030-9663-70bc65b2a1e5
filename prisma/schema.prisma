generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Session {
  id           String    @id @default(cuid())
  sessionToken String    @unique @map("session_token")
  userId       String    @map("user_id")
  expires      DateTime
  ipAddress    String?   @map("ip_address")
  userAgent    String?   @map("user_agent")
  lastActive   DateTime? @default(now()) @map("last_active")
  createdAt    DateTime? @default(now()) @map("created_at")
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model PasswordResetToken {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  token     String   @unique
  expiresAt DateTime @map("expires_at")
  usedAt    DateTime? @map("used_at")
  createdAt DateTime @default(now()) @map("created_at")
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([token])
  @@index([expiresAt])
  @@map("password_reset_tokens")
}

model User {
  id                                                                              String                           @id @default(cuid())
  name                                                                            String?
  email                                                                           String?                          @unique
  emailVerified                                                                   DateTime?                        @map("email_verified")
  image                                                                           String?
  passwordHash                                                                    String?                          @map("password_hash")
  twoFactorSecret                                                                 String?                          @map("two_factor_secret")
  twoFactorEnabled                                                                Boolean                          @default(false) @map("two_factor_enabled")
  createdAt                                                                       DateTime                         @default(now()) @map("created_at")
  updatedAt                                                                       DateTime                         @updatedAt @map("updated_at")
  twoFactorEnforced                                                               Boolean?                         @default(false) @map("two_factor_enforced")
  backupCodes                                                                     String[]                         @default([]) @map("backup_codes")
  auditLogs                                                                       AuditLog[]
  authorization_codes                                                             AuthorizationCode[]
  oauth_tokens                                                                    OAuthToken[]
  ordinances                                                                      Ordinance[]
  pendingBulkUploads                                                              PendingBulkUpload[]
  role_remote_server_access                                                       role_remote_server_access[]
  sessions                                                                        Session[]
  treaties                                                                        Treaty[]
  treaty_treaty_types                                                             TreatyTreatyType[]
  treatyTypeDetails                                                               TreatyTypeDetails[]
  userPositions                                                                   UserPosition[]
  profile                                                                         UserProfile?
  userProjectScopes                                                               UserProjectScope[]
  user_remote_server_access_user_remote_server_access_granted_byTousers           user_remote_server_access[]      @relation("user_remote_server_access_granted_byTousers")
  user_remote_server_access_user_remote_server_access_user_idTousers              user_remote_server_access[]      @relation("user_remote_server_access_user_idTousers")
  user_remote_server_permissions_user_remote_server_permissions_granted_byTousers user_remote_server_permissions[] @relation("user_remote_server_permissions_granted_byTousers")
  user_remote_server_permissions_user_remote_server_permissions_user_idTousers    user_remote_server_permissions[] @relation("user_remote_server_permissions_user_idTousers")
  roleMappingsCreatedBy                                                           remote_server_role_mappings[]    @relation("role_mapping_created_by")
  roleMappingsUpdatedBy                                                           remote_server_role_mappings[]    @relation("role_mapping_updated_by")
  permissionMappingsCreatedBy                                                     remote_server_permission_mappings[] @relation("permission_mapping_created_by")
  permissionMappingsUpdatedBy                                                     remote_server_permission_mappings[] @relation("permission_mapping_updated_by")
  userRoles                                                                       UserRole[]
  userTreaties                                                                    UserTreaty[]
  userTreatyTypes                                                                 UserTreatyType[]
  userIdentifications                                                             UserIdentification[]
  userTreatyNumbers                                                               UserTreatyNumber[]
  nationTreatyMemberships                                                          NationTreatyMember[]
  nationTreatyEnvoys                                                              NationTreatyEnvoy[]
  emergencyContactForEnvoys                                                       NationTreatyEnvoy[]                @relation("EmergencyContact")
  nationId                                                                        String?                           @map("nation_id")
  tribeId                                                                         String?                           @map("tribe_id")
  nationTreaty                                                                    NationTreaty?                     @relation("UserNation", fields: [nationId], references: [id])
  tribeTreaty                                                                     NationTreaty?                     @relation("UserTribe", fields: [tribeId], references: [id])
  processedPayments                                                               Payment[]                         @relation("PaymentProcessor")
  securityEvents                                                                  SecurityEvent[]
  passwordResetTokens                                                             PasswordResetToken[]

  @@map("users")
}

model UserProfile {
  id                    String    @id @default(cuid())
  userId                String    @unique @map("user_id")
  nwaEmail              String?   @unique @map("nwa_email")
  countryId             Int?      @map("country_id")
  cityId                Int?      @map("city_id")
  mobile                String?
  bio                   String?
  dateOfBirth           DateTime? @map("date_of_birth")
  titleId               String?   @map("title_id")
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")
  firstName             String?   @map("first_name")
  lastName              String?   @map("last_name")
  peaceAmbassadorNumber String?   @map("peace_ambassador_number")
  personalEmail         String?   @map("personal_email")
  phone                 String?
  regionId              Int?      @map("region_id")
  regionText            String?   @map("region_text")
  postalCode            String?   @map("postal_code")
  streetAddress1        String?   @map("street_address_1")
  streetAddress2        String?   @map("street_address_2")
  town                  String?   @map("town")
  city                  City?     @relation(fields: [cityId], references: [id])
  country               Country?  @relation(fields: [countryId], references: [id])
  region                Region?   @relation(fields: [regionId], references: [id])
  title                 Title?    @relation(fields: [titleId], references: [id])
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([peaceAmbassadorNumber])
  @@index([countryId])
  @@index([regionId])
  @@map("user_profiles")
}

model Title {
  id             String          @id @default(cuid())
  name           String          @unique
  description    String?
  isActive       Boolean         @default(true) @map("is_active")
  isAmbassadorial Boolean        @default(false) @map("is_ambassadorial")
  createdAt      DateTime        @default(now()) @map("created_at")
  updatedAt      DateTime        @updatedAt @map("updated_at")
  titlePositions TitlePosition[]
  userProfiles   UserProfile[]

  @@map("titles")
}

model TitlePosition {
  id         String   @id @default(cuid())
  titleId    String   @map("title_id")
  positionId String   @map("position_id")
  createdAt  DateTime @default(now()) @map("created_at")
  position   Position @relation(fields: [positionId], references: [id], onDelete: Cascade)
  title      Title    @relation(fields: [titleId], references: [id], onDelete: Cascade)

  @@unique([titleId, positionId])
  @@map("title_positions")
}

model Role {
  id                        String                      @id @default(cuid())
  name                      String                      @unique
  description               String?
  isSystem                  Boolean                     @default(false) @map("is_system")
  createdAt                 DateTime                    @default(now()) @map("created_at")
  updatedAt                 DateTime                    @updatedAt @map("updated_at")
  rolePermissions           RolePermission[]
  role_remote_server_access role_remote_server_access[]
  userRoles                 UserRole[]

  @@map("roles")
}

model Permission {
  id              String           @id @default(cuid())
  name            String           @unique
  resource        String
  action          String
  description     String?
  createdAt       DateTime         @default(now()) @map("created_at")
  rolePermissions RolePermission[]

  @@unique([resource, action])
  @@map("permissions")
}

model RolePermission {
  id           String     @id @default(cuid())
  roleId       String     @map("role_id")
  permissionId String     @map("permission_id")
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

model UserRole {
  id         String   @id @default(cuid())
  userId     String   @map("user_id")
  roleId     String   @map("role_id")
  assignedAt DateTime @default(now()) @map("assigned_at")
  assignedBy String?  @map("assigned_by")
  role       Role     @relation(fields: [roleId], references: [id], onDelete: Cascade)
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_roles")
}

model Position {
  id             String          @id @default(cuid())
  title          String          @unique
  description    String?
  level          Int             @default(0)
  parentId       String?         @map("parent_id")
  isActive       Boolean         @default(true) @map("is_active")
  createdAt      DateTime        @default(now()) @map("created_at")
  updatedAt      DateTime        @updatedAt @map("updated_at")
  parent         Position?       @relation("PositionHierarchy", fields: [parentId], references: [id])
  children       Position[]      @relation("PositionHierarchy")
  titlePositions TitlePosition[]
  userPositions  UserPosition[]

  @@map("positions")
}

model UserPosition {
  id         String    @id @default(cuid())
  userId     String    @map("user_id")
  positionId String    @map("position_id")
  startDate  DateTime  @default(now()) @map("start_date")
  endDate    DateTime? @map("end_date")
  isActive   Boolean   @default(true) @map("is_active")
  notes      String?
  position   Position  @relation(fields: [positionId], references: [id], onDelete: Cascade)
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_positions")
}

model OrdinanceType {
  id          String      @id @default(cuid())
  name        String      @unique
  description String?
  category    String
  isActive    Boolean     @default(true) @map("is_active")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")
  ordinances  Ordinance[]

  @@map("ordinance_types")
}

model Ordinance {
  id              String              @id @default(cuid())
  userId          String              @map("user_id")
  ordinanceTypeId String              @map("ordinance_type_id")
  status          String              @default("PENDING")
  completedDate   DateTime?           @map("completed_date")
  expirationDate  DateTime?           @map("expiration_date")
  notes           String?
  documentPath    String?             @map("document_path")
  createdAt       DateTime            @default(now()) @map("created_at")
  updatedAt       DateTime            @updatedAt @map("updated_at")
  documents       OrdinanceDocument[]
  ordinanceType   OrdinanceType       @relation(fields: [ordinanceTypeId], references: [id], onDelete: Cascade)
  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ordinances")
}

model OrdinanceDocument {
  id          String    @id @default(cuid())
  ordinanceId String    @map("ordinance_id")
  filePath    String    @map("file_path")
  fileName    String    @map("file_name")
  fileSize    Int       @map("file_size")
  fileType    String    @map("file_type")
  uploadedAt  DateTime  @default(now()) @map("uploaded_at")
  ordinance   Ordinance @relation(fields: [ordinanceId], references: [id], onDelete: Cascade)

  @@map("ordinance_documents")
}

model Treaty {
  id                String              @id @default(cuid())
  userId            String              @map("user_id")
  status            String              @default("ACTIVE")
  signedDate        DateTime?           @map("signed_date")
  expirationDate    DateTime?           @map("expiration_date")
  renewalDate       DateTime?           @map("renewal_date")
  notes             String?
  documentPath      String?             @map("document_path")
  createdAt         DateTime            @default(now()) @map("created_at")
  updatedAt         DateTime            @updatedAt @map("updated_at")
  name              String?
  description       String?
  treatyCategory    String              @default("USER") @map("treaty_category")
  nationTreatyId    String?             @map("nation_treaty_id")
  fileAttachments   FileAttachment[]
  user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  nationTreaty      NationTreaty?       @relation(fields: [nationTreatyId], references: [id])
  treatyTreatyTypes TreatyTreatyType[]
  treatyTypeDetails TreatyTypeDetails[]
  userTreaties      UserTreaty[]

  @@map("treaties")
}

model TreatyType {
  id                    String              @id @default(cuid())
  name                  String              @unique
  description           String?
  category              String
  price                 Decimal             @default(0.00) @db.Decimal(10, 2)
  currency              String              @default("USD")
  requiresPayment       Boolean             @default(true) @map("requires_payment")
  paymentDeadlineDays   Int                 @default(30) @map("payment_deadline_days")
  isActive              Boolean             @default(true) @map("is_active")
  createdAt             DateTime            @default(now()) @map("created_at")
  updatedAt             DateTime            @updatedAt @map("updated_at")
  requestedBy           String?             @map("requested_by")
  reviewedAt            DateTime?           @map("reviewed_at")
  reviewedBy            String?             @map("reviewed_by")
  status                String              @default("APPROVED")
  treatyTreatyTypes     TreatyTreatyType[]
  treatyTypeDetails     TreatyTypeDetails[]
  userTreatyTypes       UserTreatyType[]
  userTreatyNumbers     UserTreatyNumber[]
  
  @@map("treaty_types")
}

model UserTreaty {
  id         String    @id @default(dbgenerated("(gen_random_uuid())::text"))
  userId     String    @map("user_id")
  treatyId   String    @map("treaty_id")
  assignedAt DateTime? @default(now()) @map("assigned_at") @db.Timestamptz(6)
  status     String?   @default("ACTIVE")
  notes      String?
  treaty     Treaty    @relation(fields: [treatyId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([userId, treatyId])
  @@index([treatyId], map: "idx_user_treaties_treaty_id")
  @@index([userId], map: "idx_user_treaties_user_id")
  @@map("user_treaties")
}

model UserTreatyType {
  id                String            @id @default(dbgenerated("(gen_random_uuid())::text"))
  userId            String            @map("user_id")
  treatyTypeId      String            @map("treaty_type_id")
  selectedAt        DateTime?         @default(now()) @map("selected_at") @db.Timestamptz(6)
  status            ApplicationStatus @default(ACTIVE) @map("application_status")
  paymentStatus     PaymentStatus     @default(NOT_REQUIRED) @map("payment_status")
  appliedAt         DateTime?         @map("applied_at")
  approvedAt        DateTime?         @map("approved_at")
  rejectedAt        DateTime?         @map("rejected_at")
  approvedBy        String?           @map("approved_by")
  rejectedBy        String?           @map("rejected_by")
  rejectionReason   String?           @map("rejection_reason")
  notes             String?
  treatyType        TreatyType        @relation(fields: [treatyTypeId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user              User              @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  payments          Payment[]

  @@unique([userId, treatyTypeId])
  @@index([treatyTypeId], map: "idx_user_treaty_types_treaty_type_id")
  @@index([userId], map: "idx_user_treaty_types_user_id")
  @@index([status], map: "idx_user_treaty_types_application_status")
  @@index([paymentStatus], map: "idx_user_treaty_types_payment_status")
  @@index([appliedAt], map: "idx_user_treaty_types_applied_at")
  @@index([approvedAt], map: "idx_user_treaty_types_approved_at")
  @@map("user_treaty_types")
}

model Payment {
  id               String         @id @default(dbgenerated("(gen_random_uuid())::text"))
  userTreatyTypeId String         @map("user_treaty_type_id")
  amount           Decimal        @db.Decimal(10, 2)
  currency         String         @default("USD")
  status           PaymentStatus  @default(PENDING)
  paymentMethod    PaymentMethod? @map("payment_method")
  transactionId    String?        @map("transaction_id")
  paymentDate      DateTime?      @map("payment_date")
  notes            String?
  processedBy      String?        @map("processed_by")
  createdAt        DateTime       @default(now()) @map("created_at")
  updatedAt        DateTime       @updatedAt @map("updated_at")
  userTreatyType   UserTreatyType @relation(fields: [userTreatyTypeId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  processor        User?          @relation("PaymentProcessor", fields: [processedBy], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([status], map: "idx_payments_status")
  @@index([userTreatyTypeId], map: "idx_payments_user_treaty_type_id")
  @@index([createdAt], map: "idx_payments_created_at")
  @@map("payments")
}

model TreatyTreatyType {
  id           String     @id @default(dbgenerated("gen_random_uuid()"))
  treatyId     String     @map("treaty_id")
  treatyTypeId String     @map("treaty_type_id")
  assignedAt   DateTime?  @default(now()) @map("assigned_at") @db.Timestamptz(6)
  assignedBy   String?    @map("assigned_by")
  status       String?    @default("ACTIVE")
  notes        String?
  users        User?      @relation(fields: [assignedBy], references: [id], onDelete: NoAction, onUpdate: NoAction)
  treaty       Treaty     @relation(fields: [treatyId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  treatyType   TreatyType @relation(fields: [treatyTypeId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([treatyId, treatyTypeId])
  @@index([assignedAt], map: "idx_treaty_treaty_types_assigned_at")
  @@index([treatyId], map: "idx_treaty_treaty_types_treaty_id")
  @@index([treatyTypeId], map: "idx_treaty_treaty_types_treaty_type_id")
  @@map("treaty_treaty_types")
}

model Notification {
  id            String   @id @default(cuid())
  type          String
  message       String
  data          Json?
  recipients    String[]
  readBy        String[]
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Enhanced notification tracking fields
  trigger       String?  // What triggered this notification (e.g., "user_creation", "treaty_approval")
  triggeredBy   String?  // User ID who created/triggered this notification
  priority      String   @default("NORMAL") // LOW, NORMAL, HIGH, URGENT
  category      String   @default("SYSTEM") // USER_MANAGEMENT, TREATY, REMOTE_SERVER, SYSTEM, SECURITY
  recipientTypes Json?   // JSON object tracking user vs role recipients

  @@map("notifications")
}

model FileAttachment {
  id         String   @id @default(cuid())
  treatyId   String   @map("treaty_id")
  fileName   String   @map("file_name")
  filePath   String   @map("file_path")
  mimeType   String   @map("mime_type")
  fileSize   Int      @map("file_size")
  uploadedAt DateTime @default(now()) @map("uploaded_at")
  treaty     Treaty   @relation(fields: [treatyId], references: [id], onDelete: Cascade)

  @@index([treatyId])
  @@map("file_attachments")
}

model AuditLog {
  id               String        @id @default(cuid())
  userId           String?       @map("user_id")
  action           String
  resource         String
  resourceId       String?       @map("resource_id")
  oldValues        Json?         @map("old_values")
  newValues        Json?         @map("new_values")
  ipAddress        String?       @map("ip_address")
  userAgent        String?       @map("user_agent")
  timestamp        DateTime      @default(now())
  success          Boolean       @map("success")
  statusCode       Int?          @map("status_code")
  errorMessage     String?       @map("error_message")
  requestId        String?       @map("request_id")
  duration         Int?          @map("duration")
  requestSize      Int?          @map("request_size")
  responseSize     Int?          @map("response_size")
  metadata         Json?         @map("metadata")
  projectId        String?       @map("project_id")
  apiEndpoint      String?       @map("api_endpoint")
  requestMethod    String?       @map("request_method")
  country_code     String?
  city             String?
  remote_server_id String?
  project          Project?      @relation(fields: [projectId], references: [id])
  remote_servers   RemoteServer? @relation(fields: [remote_server_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user             User?         @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([resource])
  @@index([timestamp])
  @@index([projectId, apiEndpoint])
  @@index([city], map: "idx_audit_logs_city")
  @@index([country_code], map: "idx_audit_logs_country_code")
  @@index([country_code, city, timestamp], map: "idx_audit_logs_geo_composite")
  @@index([remote_server_id], map: "idx_audit_logs_remote_server_id")
  @@index([remote_server_id, timestamp, action], map: "idx_audit_logs_server_activity")
  @@map("audit_logs")
}

model Project {
  id                String             @id @default(cuid())
  name              String
  description       String?
  apiKeyHash        String             @unique @map("api_key_hash")
  allowedOrigins    String[]           @map("allowed_origins")
  isActive          Boolean            @default(true) @map("is_active")
  createdAt         DateTime           @default(now()) @map("created_at")
  updatedAt         DateTime           @updatedAt @map("updated_at")
  auditLogs         AuditLog[]
  userProjectScopes UserProjectScope[]

  @@index([apiKeyHash])
  @@index([isActive])
  @@map("projects")
}

model Scope {
  id                String             @id @default(cuid())
  name              String             @unique
  description       String?
  category          String             @default("general")
  isActive          Boolean            @default(true) @map("is_active")
  createdAt         DateTime           @default(now()) @map("created_at")
  userProjectScopes UserProjectScope[]

  @@index([name])
  @@index([isActive])
  @@map("scopes")
}

model UserProjectScope {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  projectId String   @map("project_id")
  scopeId   String   @map("scope_id")
  grantedAt DateTime @default(now()) @map("granted_at")
  grantedBy String?  @map("granted_by")
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  scope     Scope    @relation(fields: [scopeId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, projectId, scopeId])
  @@index([userId, projectId])
  @@map("user_project_scopes")
}

model PendingBulkUpload {
  id          String   @id @default(cuid())
  uploaderId  String   @map("uploader_id")
  fileName    String   @map("file_name")
  recordCount Int      @map("record_count")
  data        String
  status      String   @default("PENDING") @map("status")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  uploader    User     @relation(fields: [uploaderId], references: [id], onDelete: Cascade)

  @@map("pending_bulk_uploads")
}

model RemoteServer {
  id                             String                           @id @default(cuid())
  name                           String
  url                            String
  apiEndpoint                    String?                          @map("api_endpoint")
  oauthRedirectUris              String[]                         @map("oauth_redirect_uris")
  developmentRedirectUris        String[]                         @map("development_redirect_uris")
  apiKey                         String                           @map("apiKey")
  description                    String?
  isActive                       Boolean                          @default(true)
  createdAt                      DateTime                         @default(now())
  updatedAt                      DateTime                         @updatedAt
  clientId                       String?                          @unique @map("client_id")
  clientSecret                   String?                          @map("client_secret")
  redirectUris                   String[]                         @map("redirect_uris")
  grantTypes                     String[]                         @default(["authorization_code", "refresh_token"]) @map("grant_types")
  defaultScopes                  String[]                         @default(["read:profile"]) @map("default_scopes")
  callbackUrl                    String?                          @map("callback_url")
  allowedOrigins                 String[]                         @map("allowed_origins")
  tokenEndpointAuthMethod        String                           @default("client_secret_basic") @map("token_endpoint_auth_method")
  audit_logs                     AuditLog[]
  AuthorizationCode              AuthorizationCode[]
  OAuthToken                     OAuthToken[]
  remote_server_permissions      remote_server_permissions[]
  role_remote_server_access      role_remote_server_access[]
  user_remote_server_access      user_remote_server_access[]
  user_remote_server_permissions user_remote_server_permissions[]
  roleMappings                   remote_server_role_mappings[]
  permissionMappings             remote_server_permission_mappings[]

  @@index([allowedOrigins], map: "idx_remote_servers_allowed_origins", type: Gin)
  @@index([callbackUrl], map: "idx_remote_servers_callback_url")
  @@index([tokenEndpointAuthMethod], map: "idx_remote_servers_token_endpoint_auth_method")
  @@map("remote_servers")
}

model AuthorizationCode {
  id             String       @id @default(cuid())
  remoteServerId String       @map("remote_server_id")
  userId         String       @map("user_id")
  code           String       @unique
  redirectUri    String?      @map("redirect_uri")
  scope          String?
  expiresAt      DateTime     @map("expires_at")
  createdAt      DateTime     @default(now()) @map("created_at")
  usedAt         DateTime?    @map("used_at")
  remoteServer   RemoteServer @relation(fields: [remoteServerId], references: [id])
  users          User         @relation(fields: [userId], references: [id])

  @@map("authorization_codes")
}

model OAuthToken {
  id             String       @id @default(cuid())
  remoteServerId String       @map("remote_server_id")
  userId         String?      @map("user_id")
  accessToken    String       @map("access_token")
  refreshToken   String?      @map("refresh_token")
  tokenType      String       @default("Bearer") @map("token_type")
  scope          String?
  expiresAt      DateTime     @map("expires_at")
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @updatedAt @map("updated_at")
  remoteServer   RemoteServer @relation(fields: [remoteServerId], references: [id])
  users          User?        @relation(fields: [userId], references: [id])

  @@map("oauth_tokens")
}

model remote_server_permissions {
  id                     String       @id @default(dbgenerated("(gen_random_uuid())::text"))
  remote_server_id       String
  permission_name        String
  permission_description String?
  last_synced_at         DateTime     @default(now())
  is_active              Boolean      @default(true)
  remote_servers         RemoteServer @relation(fields: [remote_server_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([remote_server_id, permission_name])
  @@index([is_active], map: "idx_remote_server_permissions_active")
  @@index([last_synced_at], map: "idx_remote_server_permissions_last_synced")
  @@index([permission_name], map: "idx_remote_server_permissions_name")
  @@index([remote_server_id], map: "idx_remote_server_permissions_server_id")
}

model role_remote_server_access {
  id                     String       @id @default(dbgenerated("(gen_random_uuid())::text"))
  role_id                String
  remote_server_id       String
  auto_grant_permissions String[]
  created_by             String?
  created_at             DateTime     @default(now())
  is_active              Boolean      @default(true)
  users                  User?        @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction)
  remote_servers         RemoteServer @relation(fields: [remote_server_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  roles                  Role         @relation(fields: [role_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([role_id, remote_server_id])
  @@index([is_active], map: "idx_role_remote_server_access_active")
  @@index([created_at], map: "idx_role_remote_server_access_created_at")
  @@index([role_id], map: "idx_role_remote_server_access_role_id")
  @@index([remote_server_id], map: "idx_role_remote_server_access_server_id")
}

model user_remote_server_access {
  id                                                String       @id @default(dbgenerated("(gen_random_uuid())::text"))
  user_id                                           String
  remote_server_id                                  String
  granted_by                                        String?
  granted_at                                        DateTime     @default(now())
  is_active                                         Boolean      @default(true)
  notes                                             String?
  users_user_remote_server_access_granted_byTousers User?        @relation("user_remote_server_access_granted_byTousers", fields: [granted_by], references: [id], onDelete: NoAction, onUpdate: NoAction)
  remote_servers                                    RemoteServer @relation(fields: [remote_server_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users_user_remote_server_access_user_idTousers    User         @relation("user_remote_server_access_user_idTousers", fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, remote_server_id])
  @@index([is_active], map: "idx_user_remote_server_access_active")
  @@index([granted_at], map: "idx_user_remote_server_access_granted_at")
  @@index([remote_server_id], map: "idx_user_remote_server_access_server_id")
  @@index([user_id], map: "idx_user_remote_server_access_user_id")
}

model user_remote_server_permissions {
  id                                                     String       @id @default(dbgenerated("(gen_random_uuid())::text"))
  user_id                                                String
  remote_server_id                                       String
  permission_name                                        String
  granted_by                                             String?
  granted_at                                             DateTime     @default(now())
  expires_at                                             DateTime?
  is_active                                              Boolean      @default(true)
  users_user_remote_server_permissions_granted_byTousers User?        @relation("user_remote_server_permissions_granted_byTousers", fields: [granted_by], references: [id], onDelete: NoAction, onUpdate: NoAction)
  remote_servers                                         RemoteServer @relation(fields: [remote_server_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users_user_remote_server_permissions_user_idTousers    User         @relation("user_remote_server_permissions_user_idTousers", fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, remote_server_id, permission_name], map: "user_remote_server_permission_user_id_remote_server_id_perm_key")
  @@index([is_active], map: "idx_user_remote_server_permissions_active")
  @@index([expires_at], map: "idx_user_remote_server_permissions_expires_at")
  @@index([granted_at], map: "idx_user_remote_server_permissions_granted_at")
  @@index([permission_name], map: "idx_user_remote_server_permissions_name")
  @@index([remote_server_id], map: "idx_user_remote_server_permissions_server_id")
  @@index([user_id], map: "idx_user_remote_server_permissions_user_id")
}

model remote_server_role_mappings {
  id                     String       @id @default(dbgenerated("(gen_random_uuid())::text"))
  remote_server_id       String
  local_role_name        String
  remote_role_name       String
  is_active              Boolean      @default(true)
  created_at             DateTime     @default(now())
  updated_at             DateTime     @updatedAt
  created_by             String?
  updated_by             String?
  remote_servers         RemoteServer @relation(fields: [remote_server_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  created_by_user        User?        @relation("role_mapping_created_by", fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction)
  updated_by_user        User?        @relation("role_mapping_updated_by", fields: [updated_by], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([remote_server_id, local_role_name, remote_role_name])
  @@index([is_active], map: "idx_remote_server_role_mappings_active")
  @@index([created_at], map: "idx_remote_server_role_mappings_created_at")
  @@index([local_role_name], map: "idx_remote_server_role_mappings_local_role")
  @@index([remote_role_name], map: "idx_remote_server_role_mappings_remote_role")
  @@index([remote_server_id], map: "idx_remote_server_role_mappings_server_id")
  @@map("remote_server_role_mappings")
}

model remote_server_permission_mappings {
  id                     String       @id @default(dbgenerated("(gen_random_uuid())::text"))
  remote_server_id       String
  local_permission_name  String
  remote_permission_name String
  is_active              Boolean      @default(true)
  created_at             DateTime     @default(now())
  updated_at             DateTime     @updatedAt
  created_by             String?
  updated_by             String?
  remote_servers         RemoteServer @relation(fields: [remote_server_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  created_by_user        User?        @relation("permission_mapping_created_by", fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction)
  updated_by_user        User?        @relation("permission_mapping_updated_by", fields: [updated_by], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([remote_server_id, local_permission_name, remote_permission_name])
  @@index([is_active], map: "idx_remote_server_permission_mappings_active")
  @@index([created_at], map: "idx_remote_server_permission_mappings_created_at")
  @@index([local_permission_name], map: "idx_remote_server_permission_mappings_local_permission")
  @@index([remote_permission_name], map: "idx_remote_server_permission_mappings_remote_permission")
  @@index([remote_server_id], map: "idx_remote_server_permission_mappings_server_id")
  @@map("remote_server_permission_mappings")
}

model Country {
  id                                Int                   @id @default(autoincrement())
  name                              String                @db.VarChar(255)
  code                              String                @unique @db.VarChar(3)
  createdAt                         DateTime              @default(now()) @map("created_at")
  updatedAt                         DateTime              @updatedAt @map("updated_at")
  cities                            City[]
  addressFormat                     CountryAddressFormat?
  regions                           Region[]
  treatyTypeDetailsCurrentCountry   TreatyTypeDetails[]   @relation("TreatyTypeDetailsCurrentCountry")
  treatyTypeDetailsResidenceCountry TreatyTypeDetails[]   @relation("TreatyTypeDetailsResidenceCountry")
  userProfiles                      UserProfile[]

  @@map("countries")
}

model City {
  id                             Int                 @id @default(autoincrement())
  name                           String              @db.VarChar(255)
  countryId                      Int                 @map("country_id")
  createdAt                      DateTime            @default(now()) @map("created_at")
  updatedAt                      DateTime            @updatedAt @map("updated_at")
  country                        Country             @relation(fields: [countryId], references: [id], onDelete: Cascade)
  treatyTypeDetailsCurrentCity   TreatyTypeDetails[] @relation("TreatyTypeDetailsCurrentCity")
  treatyTypeDetailsResidenceCity TreatyTypeDetails[] @relation("TreatyTypeDetailsResidenceCity")
  userProfiles                   UserProfile[]

  @@unique([name, countryId])
  @@index([countryId])
  @@map("cities")
}

model CountryAddressFormat {
  id                 Int      @id @default(autoincrement())
  countryId          Int      @unique @map("country_id")
  postalCodeLabel    String   @map("postal_code_label") @db.VarChar(50)
  postalCodeFormat   String?  @map("postal_code_format") @db.VarChar(50)
  postalCodeRequired Boolean  @default(true) @map("postal_code_required")
  regionLabel        String?  @map("region_label") @db.VarChar(50)
  regionRequired     Boolean  @default(false) @map("region_required")
  townLabel          String?  @map("town_label") @db.VarChar(50)
  townRequired       Boolean  @default(false) @map("town_required")
  addressTemplate    String?  @map("address_template")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  country            Country  @relation(fields: [countryId], references: [id], onDelete: Cascade)

  @@map("country_address_formats")
}

model Region {
  id           Int           @id(map: "administrative_divisions_pkey") @default(autoincrement())
  countryId    Int           @map("country_id")
  code         String?       @db.VarChar(10)
  name         String        @db.VarChar(255)
  type         String        @default("state") @db.VarChar(50)
  isActive     Boolean       @default(true) @map("is_active")
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @updatedAt @map("updated_at")
  country      Country       @relation(fields: [countryId], references: [id], onDelete: Cascade)
  userProfiles UserProfile[]

  @@unique([countryId, code])
  @@unique([countryId, name])
  @@index([countryId])
  @@index([isActive])
  @@map("regions")
}

model Category {
  id            Int           @id @default(autoincrement())
  name          String        @db.VarChar(255)
  description   String?
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @updatedAt @map("updated_at")
  subcategories Subcategory[]

  @@map("categories")
}

model Subcategory {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(255)
  description String?
  categoryId  Int      @map("category_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  category    Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@map("subcategories")
}

model SystemSetting {
  id                     Int      @id @default(autoincrement())
  securityLevel          String   @default("MODERATE") @map("security_level")
  maintenanceMode        Boolean  @default(false) @map("maintenance_mode")
  maxLoginAttempts       Int      @default(5) @map("max_login_attempts")
  sessionTimeout         Int      @default(3600) @map("session_timeout")
  passwordMinLength      Int      @default(8) @map("password_min_length")
  passwordRequireUpper   Boolean  @default(true) @map("password_require_upper")
  passwordRequireLower   Boolean  @default(true) @map("password_require_lower")
  passwordRequireNumber  Boolean  @default(true) @map("password_require_number")
  passwordRequireSpecial Boolean  @default(true) @map("password_require_special")
  twoFactorAuthEnabled   Boolean  @default(false) @map("two_factor_auth_enabled")
  createdAt              DateTime @default(now()) @map("created_at")
  updatedAt              DateTime @updatedAt @map("updated_at")

  @@map("system_settings")
}

model TreatyTypeDetails {
  id                      String     @id @default(cuid())
  userId                  String     @map("user_id")
  treatyId                String     @map("treaty_id")
  treatyTypeId            String     @map("treaty_type_id")
  status                  String     @default("DRAFT")
  fullLegalName           String?    @map("full_legal_name")
  dateOfBirth             DateTime?  @map("date_of_birth")
  genderIdentity          String?    @map("gender_identity")
  nationality             String[]   @default([])
  phoneNumbers            Json?      @map("phone_numbers")
  email                   String?
  currentCountryId        Int?       @map("current_country_id")
  currentCityId           Int?       @map("current_city_id")
  identificationNumber    String?    @map("identification_number")
  photographPath          String?    @map("photograph_path")
  residentialAddress      String?    @map("residential_address")
  residenceStatus         String?    @map("residence_status")
  residenceStatusOther    String?    @map("residence_status_other")
  residenceUseDesignation String?    @map("residence_use_designation")
  residenceCountryId      Int?       @map("residence_country_id")
  residenceCityId         Int?       @map("residence_city_id")
  peaceProtectedPremises  Boolean?   @default(false) @map("peace_protected_premises")
  businessDetails         Json?      @map("business_details")
  protectionItems         Json?      @map("protection_items")
  declarationAccepted     Boolean?   @default(false) @map("declaration_accepted")
  signatureData           String?    @map("signature_data")
  signatureDate           DateTime?  @map("signature_date")
  attachmentPaths         String[]   @default([]) @map("attachment_paths")
  createdAt               DateTime   @default(now()) @map("created_at")
  updatedAt               DateTime   @updatedAt @map("updated_at")
  currentCity             City?      @relation("TreatyTypeDetailsCurrentCity", fields: [currentCityId], references: [id])
  currentCountry          Country?   @relation("TreatyTypeDetailsCurrentCountry", fields: [currentCountryId], references: [id])
  residenceCity           City?      @relation("TreatyTypeDetailsResidenceCity", fields: [residenceCityId], references: [id])
  residenceCountry        Country?   @relation("TreatyTypeDetailsResidenceCountry", fields: [residenceCountryId], references: [id])
  treaty                  Treaty     @relation(fields: [treatyId], references: [id], onDelete: Cascade)
  treatyType              TreatyType @relation(fields: [treatyTypeId], references: [id], onDelete: Cascade)
  user                    User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, treatyId, treatyTypeId])
  @@index([userId])
  @@index([treatyId])
  @@index([treatyTypeId])
  @@index([status])
  @@map("treaty_type_details")
}

model IdentificationType {
  id                  String               @id @default(cuid())
  name                String               @unique
  description         String?
  category            String               @default("government")
  isActive            Boolean              @default(true) @map("is_active")
  createdAt           DateTime             @default(now()) @map("created_at")
  updatedAt           DateTime             @updatedAt @map("updated_at")
  userIdentifications UserIdentification[]

  @@index([isActive])
  @@index([category])
  @@map("identification_types")
}

model UserIdentification {
  id                   String             @id @default(cuid())
  userId               String             @map("user_id")
  identificationTypeId String             @map("identification_type_id")
  idNumber             String             @map("id_number")
  isActive             Boolean            @default(true) @map("is_active")
  createdAt            DateTime           @default(now()) @map("created_at")
  updatedAt            DateTime           @updatedAt @map("updated_at")
  user                 User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  identificationType   IdentificationType @relation(fields: [identificationTypeId], references: [id], onUpdate: Cascade)

  @@unique([userId, identificationTypeId, idNumber])
  @@index([userId])
  @@index([identificationTypeId])
  @@map("user_identifications")
}

model UserTreatyNumber {
  id           String     @id @default(cuid())
  userId       String     @map("user_id")
  treatyTypeId String     @map("treaty_type_id")
  treatyNumber String     @map("treaty_number")
  isActive     Boolean    @default(true) @map("is_active")
  createdAt    DateTime   @default(now()) @map("created_at")
  updatedAt    DateTime   @updatedAt @map("updated_at")
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  treatyType   TreatyType @relation(fields: [treatyTypeId], references: [id], onUpdate: Cascade)

  @@unique([userId, treatyTypeId])
  @@index([userId])
  @@index([treatyTypeId])
  @@map("user_treaty_numbers")
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_VERIFICATION
}

enum OrdinanceStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  EXPIRED
  CANCELLED
}

enum TreatyStatus {
  ACTIVE
  EXPIRED
  TERMINATED
  PENDING_RENEWAL
}

enum BulkUploadStatus {
  PENDING
  APPROVED
  REJECTED
}

enum ApplicationStatus {
  ACTIVE
  APPLIED
  UNDER_REVIEW
  APPROVED
  REJECTED
}

enum PaymentStatus {
  NOT_REQUIRED
  AWAITING_PAYMENT
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum PaymentMethod {
  BANK_TRANSFER
  CASH
  CHECK
  MONEY_ORDER
  OTHER
}

// Nation Treaty System Models

model NationTreaty {
  id                    String   @id @default(cuid())
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  deletedAt             DateTime? @map("deleted_at")
  
  // Core identification fields
  name                  String   @unique
  officialName          String   @map("official_name")
  
  // Status and description
  status                NationTreatyStatus @default(ACTIVE)
  description           String?
  
  // Contact information
  contactEmail          String?  @map("contact_email")
  contactPhone          String?  @map("contact_phone")
  contactAddress        String?  @map("contact_address")
  website               String?
  
  // Emergency contact information
  emergencyContactName  String?  @map("emergency_contact_name")
  emergencyContactPhone String?  @map("emergency_contact_phone")
  
  // Additional information
  notes                 String?
  documentPath          String?  @map("document_path")
  
  // Soft delete flag
  isDeleted             Boolean  @default(false) @map("is_deleted")
  
  // JSONB field for additional metadata
  metadata              Json?
  
  // Relationships
  members               NationTreatyMember[]
  envoys                NationTreatyEnvoy[]
  offices               NationTreatyOffice[]
  usersAsNation         User[]                     @relation("UserNation")
  usersAsTribe          User[]                     @relation("UserTribe")
  treaties              Treaty[]
  
  @@map("nation_treaties")
}

model NationTreatyMember {
  id           String              @id @default(cuid())
  createdAt    DateTime            @default(now()) @map("created_at")
  updatedAt    DateTime            @updatedAt @map("updated_at")
  
  // Foreign keys
  userId       String              @map("user_id")
  nationTreatyId String             @map("nation_treaty_id")
  
  // Member information
  role         NationTreatyMemberRole @default(MEMBER)
  joinDate     DateTime            @default(now()) @map("join_date")
  notes        String?
  
  // Status information
  status       NationTreatyMemberStatus @default(ACTIVE)
  
  // JSONB field for additional metadata
  metadata     Json?
  
  // Relationships
  user         User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  nationTreaty NationTreaty        @relation(fields: [nationTreatyId], references: [id], onDelete: Cascade)
  
  @@unique([userId, nationTreatyId])
  @@map("nation_treaty_members")
}

model NationTreatyEnvoy {
  id                    String                  @id @default(cuid())
  createdAt             DateTime                @default(now()) @map("created_at")
  updatedAt             DateTime                @updatedAt @map("updated_at")
  
  // Foreign keys
  userId                String                  @map("user_id")
  nationTreatyId        String                  @map("nation_treaty_id")
  emergencyContactUserId String?                 @map("emergency_contact_user_id")
  
  // Envoy information
  envoyType             NationTreatyEnvoyType
  title                 String?
  
  // Contact details
  address               String?
  city                  String?
  region                String?
  country               String?
  phone                 String?
  mobile                String?
  email                 String?
  
  // Additional information
  notes                 String?
  status                NationTreatyEnvoyStatus @default(ACTIVE)
  
  // JSONB field for additional metadata
  metadata              Json?
  
  // Relationships
  user                  User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  nationTreaty          NationTreaty            @relation(fields: [nationTreatyId], references: [id], onDelete: Cascade)
  emergencyContactUser  User?                   @relation("EmergencyContact", fields: [emergencyContactUserId], references: [id])
  
  @@unique([userId, nationTreatyId, envoyType])
  @@map("nation_treaty_envoys")
}

model NationTreatyOffice {
  id                    String                    @id @default(cuid())
  createdAt             DateTime                  @default(now()) @map("created_at")
  updatedAt             DateTime                  @updatedAt @map("updated_at")
  
  // Foreign keys
  nationTreatyId        String                    @map("nation_treaty_id")
  
  // Office information
  officeType            NationTreatyOfficeType    @default(ENVOY_OFFICE)
  streetAddress         String?                   @map("street_address")
  city                  String?
  region                String?
  country               String?
  postalCode            String?                   @map("postal_code")
  phone                 String?
  email                 String?
  website               String?
  isPrimary             Boolean                   @default(false) @map("is_primary")
  status                NationTreatyOfficeStatus @default(ACTIVE)
  notes                 String?
  
  // JSONB field for additional metadata
  metadata              Json?
  
  // Relationships
  nationTreaty          NationTreaty              @relation(fields: [nationTreatyId], references: [id], onDelete: Cascade)
  
  @@map("nation_treaty_offices")
}

// Enums for Nation Treaty System

enum NationTreatyStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING
}

enum NationTreatyMemberRole {
  MEMBER
  ENVOY
  ADMIN
}

enum NationTreatyMemberStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum NationTreatyEnvoyType {
  PRIMARY
  EMERGENCY
  LEGAL
}

enum NationTreatyEnvoyStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum NationTreatyOfficeType {
  ENVOY_OFFICE
  CONSULATE
  EMBASSY
}

enum NationTreatyOfficeStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum SecurityEventType {
  AUTHENTICATION_FAILURE
  AUTHORIZATION_FAILURE
  SUSPICIOUS_ACTIVITY
  RATE_LIMIT_EXCEEDED
  BRUTE_FORCE_ATTACK
  UNUSUAL_ACCESS_PATTERN
  DATA_EXFILTRATION
  PRIVILEGE_ESCALATION
  SESSION_HIJACKING
  MALWARE_DETECTION
}

enum SecurityEventSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum SecurityEventStatus {
  OPEN
  INVESTIGATING
  RESOLVED
  FALSE_POSITIVE
}

model SecurityEvent {
  id          String               @id @default(cuid())
  type        SecurityEventType
  severity    SecurityEventSeverity
  description String
  source      String
  userId      String?              @map("user_id")
  serverId    String?              @map("server_id")
  ipAddress   String               @map("ip_address")
  userAgent   String?              @map("user_agent")
  metadata    Json?
  detectedAt  DateTime             @default(now()) @map("detected_at")
  resolvedAt  DateTime?            @map("resolved_at")
  status      SecurityEventStatus  @default(OPEN)
  assignedTo  String?              @map("assigned_to")
  resolution  String?
  createdAt   DateTime             @default(now()) @map("created_at")
  updatedAt   DateTime             @updatedAt @map("updated_at")

  user        User?                @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([serverId])
  @@index([type])
  @@index([severity])
  @@index([status])
  @@index([detectedAt])
  @@map("security_events")
}

model SecurityAlert {
  id        String   @id @default(cuid())
  eventId   String   @map("event_id")
  ruleId    String   @map("rule_id")
  channels  Json     @map("channels")
  sentAt    DateTime @default(now()) @map("sent_at")
  status    String   @default("PENDING")
  errorMessage String? @map("error_message")
  metadata  Json?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([eventId])
  @@index([ruleId])
  @@index([status])
  @@index([sentAt])
  @@map("security_alerts")
}

model IncidentResponse {
  id        String   @id @default(cuid())
  eventId   String   @map("event_id")
  ruleId    String   @map("rule_id")
  actions   Json     @map("actions")
  executedAt DateTime @default(now()) @map("executed_at")
  status    String   @default("PENDING")
  results   Json     @map("results")
  errorMessage String? @map("error_message")
  metadata  Json?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([eventId])
  @@index([ruleId])
  @@index([status])
  @@index([executedAt])
  @@map("incident_responses")
}
