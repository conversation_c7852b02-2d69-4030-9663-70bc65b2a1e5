import 'dotenv/config'
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create initial roles
  const memberRole = await prisma.role.upsert({
    where: { name: 'member' },
    update: {},
    create: {
      name: 'member',
      description: 'Standard NWA member',
      isSystem: true,
    },
  })

  const adminRole = await prisma.role.upsert({
    where: { name: 'admin' },
    update: {},
    create: {
      name: 'admin',
      description: 'System administrator',
      isSystem: true,
    },
  })

  const moderatorRole = await prisma.role.upsert({
    where: { name: 'moderator' },
    update: {},
    create: {
      name: 'moderator',
      description: 'Community moderator',
      isSystem: true,
    },
  })

  console.log('✅ Created initial roles')

  // Create initial permissions
  const permissions = [
    { name: 'user:read', resource: 'user', action: 'read', description: 'View user profiles' },
    { name: 'user:write', resource: 'user', action: 'write', description: 'Edit user profiles' },
    { name: 'user:delete', resource: 'user', action: 'delete', description: 'Delete user accounts' },
    { name: 'ordinance:read', resource: 'ordinance', action: 'read', description: 'View ordinances' },
    { name: 'ordinance:write', resource: 'ordinance', action: 'write', description: 'Create/edit ordinances' },
    { name: 'ordinance:delete', resource: 'ordinance', action: 'delete', description: 'Delete ordinances' },
    { name: 'treaty:read', resource: 'treaty', action: 'read', description: 'View treaties' },
    { name: 'treaty:write', resource: 'treaty', action: 'write', description: 'Create/edit treaties' },
    { name: 'treaty:delete', resource: 'treaty', action: 'delete', description: 'Delete treaties' },
    { name: 'admin:access', resource: 'admin', action: 'access', description: 'Access admin panel' },
    // Remote server permissions
    { name: 'remote_servers:read', resource: 'remote_servers', action: 'read', description: 'Read remote server configurations' },
    { name: 'remote_servers:write', resource: 'remote_servers', action: 'write', description: 'Create and modify remote server configurations' },
    { name: 'remote_servers:delete', resource: 'remote_servers', action: 'delete', description: 'Delete remote server configurations' },
    { name: 'remote_servers:sync', resource: 'remote_servers', action: 'sync', description: 'Synchronize with remote servers' },
    // Role permissions
    { name: 'roles:read', resource: 'roles', action: 'read', description: 'Read role definitions' },
    { name: 'roles:write', resource: 'roles', action: 'write', description: 'Create and modify roles' },
    { name: 'roles:delete', resource: 'roles', action: 'delete', description: 'Delete roles' },
    // Permission management
    { name: 'permissions:read', resource: 'permissions', action: 'read', description: 'Read system permissions and roles' },
    { name: 'permissions:write', resource: 'permissions', action: 'write', description: 'Modify system permissions and roles' },
    // Audit permissions
    { name: 'audit:read', resource: 'audit', action: 'read', description: 'Read audit logs' },
    { name: 'audit:write', resource: 'audit', action: 'write', description: 'Create audit log entries' },
    // File permissions
    { name: 'files:read', resource: 'files', action: 'read', description: 'Read files and documents' },
    { name: 'files:write', resource: 'files', action: 'write', description: 'Upload and modify files' },
    { name: 'files:delete', resource: 'files', action: 'delete', description: 'Delete files' },
  ]

  for (const perm of permissions) {
    await prisma.permission.upsert({
      where: { name: perm.name },
      update: {},
      create: perm,
    })
  }

  console.log('✅ Created initial permissions')

  // Assign permissions to roles
  const allPermissions = await prisma.permission.findMany()

  // Admin gets all permissions
  for (const permission of allPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: adminRole.id,
          permissionId: permission.id,
        }
      },
      update: {},
      create: {
        roleId: adminRole.id,
        permissionId: permission.id,
      },
    })
  }

  // Members get basic read permissions
  const memberPermissions = await prisma.permission.findMany({
    where: {
      OR: [
        { resource: 'user', action: 'read' },
        { resource: 'ordinance', action: 'read' },
        { resource: 'treaty', action: 'read' },
        { resource: 'files', action: 'read' },
        { resource: 'remote_servers', action: 'read' },
      ]
    }
  })

  for (const permission of memberPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: memberRole.id,
          permissionId: permission.id,
        }
      },
      update: {},
      create: {
        roleId: memberRole.id,
        permissionId: permission.id,
      },
    })
  }

  // Moderators get additional permissions
  const moderatorPermissions = await prisma.permission.findMany({
    where: {
      OR: [
        { resource: 'user', action: 'read' },
        { resource: 'user', action: 'write' },
        { resource: 'ordinance', action: 'read' },
        { resource: 'ordinance', action: 'write' },
        { resource: 'treaty', action: 'read' },
        { resource: 'treaty', action: 'write' },
        { resource: 'files', action: 'read' },
        { resource: 'files', action: 'write' },
        { resource: 'remote_servers', action: 'read' },
        { resource: 'remote_servers', action: 'write' },
        { resource: 'remote_servers', action: 'sync' },
      ]
    }
  })

  for (const permission of moderatorPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: moderatorRole.id,
          permissionId: permission.id,
        }
      },
      update: {},
      create: {
        roleId: moderatorRole.id,
        permissionId: permission.id,
      },
    })
  }

  console.log('✅ Assigned permissions to roles')

  // Create ordinance types
  const ordinanceTypes = [
    {
      name: 'Trade Agreement',
      description: 'Formal agreement between two or more parties concerning trade relations',
      category: 'trade',
    },
    {
      name: 'Peace Treaty',
      description: 'Official agreement to end a conflict and establish peace',
      category: 'diplomacy',
    },
    {
      name: 'Cultural Exchange Program',
      description: 'Program to foster cultural understanding and cooperation',
      category: 'culture',
    },
    {
      name: 'Environmental Accord',
      description: 'Agreement to address environmental issues of mutual concern',
      category: 'environment',
    },
  ]

  for (const type of ordinanceTypes) {
    await prisma.ordinanceType.upsert({
      where: { name: type.name },
      update: {},
      create: type,
    })
  }

  console.log('✅ Created ordinance types')

  // Create sample positions
  const positions = [
    { title: 'Elder', description: 'Church elder position', level: 1 },
    { title: 'Deacon', description: 'Church deacon position', level: 2 },
    { title: 'Minister', description: 'Licensed minister', level: 1 },
    { title: 'Youth Leader', description: 'Youth ministry leader', level: 3, parentId: null },
  ]

  for (const position of positions) {
    await prisma.position.upsert({
      where: { title: position.title },
      update: {},
      create: {
        title: position.title,
        description: position.description,
        level: position.level,
      },
    })
  }

  console.log('✅ Created sample positions')

  // Create ambassadorial titles
  const titles = [
    {
      name: 'Peace Ambassador',
      description: 'Ambassador for peace initiatives and diplomatic relations',
      isAmbassadorial: true,
    },
    {
      name: 'Global Ambassador Diaspora',
      description: 'Ambassador representing diaspora communities globally',
      isAmbassadorial: true,
    },
    {
      name: 'Cultural Ambassador',
      description: 'Ambassador for cultural exchange and heritage preservation',
      isAmbassadorial: true,
    },
    {
      name: 'Economic Ambassador',
      description: 'Ambassador for economic development and trade relations',
      isAmbassadorial: true,
    },
    {
      name: 'Environmental Ambassador',
      description: 'Ambassador for environmental protection and sustainability',
      isAmbassadorial: true,
    },
    {
      name: 'Plenipotentiary Ambassador',
      description: 'Ambassador with full power to represent and make binding decisions',
      isAmbassadorial: true,
    },
  ]

  const createdTitles = []
  for (const titleData of titles) {
    const title = await prisma.title.upsert({
      where: { name: titleData.name },
      update: {},
      create: titleData,
    })
    createdTitles.push(title)
  }

  console.log('✅ Created ambassadorial titles')

  // Create additional positions for Global Ambassador Diaspora
  const ambassadorPositions = [
    {
      title: 'Head of Maritime for Aotearoa & Pacifica',
      description: 'Maritime affairs leadership for Aotearoa and Pacific region',
      level: 1,
    },
    {
      title: 'Head of Ecology Aotearoa',
      description: 'Environmental and ecological leadership for Aotearoa',
      level: 1,
    },
    {
      title: 'Head of Arts & Culture for Aotearoa',
      description: 'Arts and cultural affairs leadership for Aotearoa',
      level: 1,
    },
  ]

  const createdAmbassadorPositions = []
  for (const positionData of ambassadorPositions) {
    const position = await prisma.position.upsert({
      where: { title: positionData.title },
      update: {},
      create: positionData,
    })
    createdAmbassadorPositions.push(position)
  }

  console.log('✅ Created ambassador positions')

  // Link Global Ambassador Diaspora positions
  const globalAmbassadorTitle = createdTitles.find(t => t.name === 'Global Ambassador Diaspora')
  if (globalAmbassadorTitle) {
    for (const position of createdAmbassadorPositions) {
      await prisma.titlePosition.upsert({
        where: {
          titleId_positionId: {
            titleId: globalAmbassadorTitle.id,
            positionId: position.id,
          }
        },
        update: {},
        create: {
          titleId: globalAmbassadorTitle.id,
          positionId: position.id,
        },
      })
    }
    console.log('✅ Linked Global Ambassador Diaspora positions')
  }

  // Create initial scopes for external API authentication
  const scopes = [
    { name: 'read:users', description: 'Read user profile information', category: 'users' },
    { name: 'write:users', description: 'Create and update user profiles', category: 'users' },
    { name: 'read:profiles', description: 'Read detailed user profile data', category: 'profiles' },
    { name: 'write:profiles', description: 'Update user profile information', category: 'profiles' },
    { name: 'read:roles', description: 'Read user roles and permissions', category: 'roles' },
    { name: 'write:roles', description: 'Assign and modify user roles', category: 'roles' },
    { name: 'read:positions', description: 'Read user positions and hierarchy', category: 'positions' },
    { name: 'write:positions', description: 'Assign and modify user positions', category: 'positions' },
    { name: 'read:ordinances', description: 'Read user ordinance information', category: 'ordinances' },
    { name: 'write:ordinances', description: 'Create and update ordinances', category: 'ordinances' },
    { name: 'read:treaties', description: 'Read user treaty information', category: 'treaties' },
    { name: 'write:treaties', description: 'Create and update treaties', category: 'treaties' },
    { name: 'read:audit', description: 'Read audit log information', category: 'audit' },
    { name: 'admin:projects', description: 'Manage external projects', category: 'admin' },
    { name: 'admin:scopes', description: 'Manage permission scopes', category: 'admin' },
  ]

  for (const scope of scopes) {
    await prisma.scope.upsert({
      where: { name: scope.name },
      update: {},
      create: scope,
    })
  }

  console.log('✅ Created initial API scopes')

  // Create sample countries
  const countries = [
    { name: 'USA', code: 'US' },
    { name: 'Canada', code: 'CA' },
    { name: 'UK', code: 'GB' },
    { name: 'Australia', code: 'AU' },
  ]

  const createdCountries = []
  for (const countryData of countries) {
    const country = await prisma.country.upsert({
      where: { code: countryData.code }, // Use code as the unique identifier for upsert
      update: {},
      create: countryData,
    })
    createdCountries.push(country)
  }
  console.log('✅ Created sample countries')

  // Create identification types
  const identificationTypes = [
    {
      name: 'Passport',
      description: 'International travel document issued by national government',
      category: 'government'
    },
    {
      name: 'Driver License',
      description: 'Government-issued license permitting operation of motor vehicles',
      category: 'government'
    },
    {
      name: 'National ID Card',
      description: 'Primary identification document for citizens',
      category: 'government'
    },
    {
      name: 'Military ID',
      description: 'Armed forces identification document',
      category: 'military'
    },
    {
      name: 'State ID Card',
      description: 'Non-driver state-issued identification',
      category: 'government'
    },
    {
      name: 'Under 18 ID',
      description: 'Identification for minors under 18 years old',
      category: 'government'
    },
    {
      name: 'Veteran ID Card',
      description: 'Identification for military veterans',
      category: 'military'
    },
    {
      name: 'Federal Employee ID',
      description: 'Identification for government employees',
      category: 'government'
    },
    {
      name: 'Tribal ID Card',
      description: 'Identification for tribal nation members',
      category: 'government'
    },
    {
      name: 'Immigration Document',
      description: 'Visa, green card, or other immigration status proof',
      category: 'government'
    },
    {
      name: 'Professional License',
      description: 'Professional certification or license document',
      category: 'professional'
    },
    {
      name: 'Student ID',
      description: 'Educational institution identification',
      category: 'education'
    },
    {
      name: 'Healthcare Worker ID',
      description: 'Medical professional identification',
      category: 'professional'
    },
    {
      name: 'Diplomatic ID',
      description: 'Diplomatic passport or identification',
      category: 'diplomatic'
    },
    {
      name: 'Police ID',
      description: 'Law enforcement identification',
      category: 'government'
    }
  ]

  // Use upsert to avoid duplicates
  for (const idType of identificationTypes) {
    await prisma.identificationType.upsert({
      where: { name: idType.name },
      update: idType,
      create: idType,
    })
  }

  console.log('✅ Created identification types')

  // Create sample cities
  const cities = [
    { name: 'New York', countryCode: 'US' },
    { name: 'Los Angeles', countryCode: 'US' },
    { name: 'Toronto', countryCode: 'CA' },
    { name: 'Vancouver', countryCode: 'CA' },
    { name: 'London', countryCode: 'GB' },
    { name: 'Manchester', countryCode: 'GB' },
    { name: 'Sydney', countryCode: 'AU' },
    { name: 'Melbourne', countryCode: 'AU' },
    { name: 'Houston', countryCode: 'US' }, // Adding Houston for the test user
    { name: 'Phoenix', countryCode: 'US' }, // Adding Phoenix for the test user
    { name: 'Dallas', countryCode: 'US' }, // Adding Dallas for the test user
  ]

  const createdCities = []
  for (const cityData of cities) {
    const country = createdCountries.find(c => c.code === cityData.countryCode)
    if (!country) {
      console.warn(`Country with code ${cityData.countryCode} not found for city ${cityData.name}`)
      continue
    }

    // Find the city by name and countryId
    // Since there's no explicit unique constraint on name+countryId, we use findFirst
    let city = await prisma.city.findFirst({
      where: {
        name: cityData.name,
        countryId: country.id,
      },
    })

    // If the city doesn't exist, create it
    if (!city) {
      city = await prisma.city.create({
        data: {
          name: cityData.name,
          country: {
            connect: {
              id: country.id,
            },
          },
        },
      })
      console.log(`Created city: ${city.name}`)
    } else {
      console.log(`Found existing city: ${city.name}`)
    }

    createdCities.push(city)
  }
  console.log('✅ Created sample cities')

  // Create address formats for countries
  const addressFormats = [
    {
      countryCode: 'US',
      postalCodeLabel: 'ZIP Code',
      postalCodeFormat: '^\\d{5}(-\\d{4})?$',
      postalCodeRequired: true,
      regionLabel: 'State',
      regionRequired: true,
      townLabel: 'City',
      townRequired: true,
      addressTemplate: '{street}\n{town}, {region} {postalCode}\n{country}'
    },
    {
      countryCode: 'CA',
      postalCodeLabel: 'Postal Code',
      postalCodeFormat: '^[A-Z]\\d[A-Z] ?\\d[A-Z]\\d$',
      postalCodeRequired: true,
      regionLabel: 'Province',
      regionRequired: true,
      townLabel: 'City',
      townRequired: true,
      addressTemplate: '{street}\n{town}, {region} {postalCode}\n{country}'
    },
    {
      countryCode: 'GB',
      postalCodeLabel: 'Postcode',
      postalCodeFormat: '^[A-Z]{1,2}\\d[A-Z\\d]? ?\\d[A-Z]{2}$',
      postalCodeRequired: true,
      regionLabel: 'County',
      regionRequired: false,
      townLabel: 'Town/City',
      townRequired: true,
      addressTemplate: '{street}\n{town}\n{region}\n{postalCode}\n{country}'
    },
    {
      countryCode: 'AU',
      postalCodeLabel: 'Postcode',
      postalCodeFormat: '^\\d{4}$',
      postalCodeRequired: true,
      regionLabel: 'State',
      regionRequired: true,
      townLabel: 'Suburb',
      townRequired: true,
      addressTemplate: '{street}\n{town} {region} {postalCode}\n{country}'
    }
  ]

  for (const formatData of addressFormats) {
    const country = createdCountries.find(c => c.code === formatData.countryCode)
    if (country) {
      await prisma.countryAddressFormat.upsert({
        where: { countryId: country.id },
        update: {},
        create: {
          countryId: country.id,
          postalCodeLabel: formatData.postalCodeLabel,
          postalCodeFormat: formatData.postalCodeFormat,
          postalCodeRequired: formatData.postalCodeRequired,
          regionLabel: formatData.regionLabel,
          regionRequired: formatData.regionRequired,
          townLabel: formatData.townLabel,
          townRequired: formatData.townRequired,
          addressTemplate: formatData.addressTemplate
        }
      })
    }
  }

  console.log('✅ Created address formats')

  // Create administrative divisions
  const adminDivisions = [
    // US States
    { countryCode: 'US', code: 'CA', name: 'California', type: 'state' },
    { countryCode: 'US', code: 'TX', name: 'Texas', type: 'state' },
    { countryCode: 'US', code: 'NY', name: 'New York', type: 'state' },
    { countryCode: 'US', code: 'FL', name: 'Florida', type: 'state' },
    { countryCode: 'US', code: 'IL', name: 'Illinois', type: 'state' },
    { countryCode: 'US', code: 'PA', name: 'Pennsylvania', type: 'state' },
    { countryCode: 'US', code: 'OH', name: 'Ohio', type: 'state' },
    { countryCode: 'US', code: 'GA', name: 'Georgia', type: 'state' },
    { countryCode: 'US', code: 'NC', name: 'North Carolina', type: 'state' },
    { countryCode: 'US', code: 'MI', name: 'Michigan', type: 'state' },
    
    // Canadian Provinces
    { countryCode: 'CA', code: 'ON', name: 'Ontario', type: 'province' },
    { countryCode: 'CA', code: 'QC', name: 'Quebec', type: 'province' },
    { countryCode: 'CA', code: 'BC', name: 'British Columbia', type: 'province' },
    { countryCode: 'CA', code: 'AB', name: 'Alberta', type: 'province' },
    { countryCode: 'CA', code: 'MB', name: 'Manitoba', type: 'province' },
    { countryCode: 'CA', code: 'SK', name: 'Saskatchewan', type: 'province' },
    { countryCode: 'CA', code: 'NS', name: 'Nova Scotia', type: 'province' },
    { countryCode: 'CA', code: 'NB', name: 'New Brunswick', type: 'province' },
    { countryCode: 'CA', code: 'NL', name: 'Newfoundland and Labrador', type: 'province' },
    { countryCode: 'CA', code: 'PE', name: 'Prince Edward Island', type: 'province' },
    
    // UK Counties (sample)
    { countryCode: 'GB', code: 'LND', name: 'London', type: 'region' },
    { countryCode: 'GB', code: 'GTM', name: 'Greater Manchester', type: 'county' },
    { countryCode: 'GB', code: 'WYK', name: 'West Yorkshire', type: 'county' },
    { countryCode: 'GB', code: 'SYK', name: 'South Yorkshire', type: 'county' },
    { countryCode: 'GB', code: 'ESX', name: 'Essex', type: 'county' },
    { countryCode: 'GB', code: 'KEN', name: 'Kent', type: 'county' },
    
    // Australian States
    { countryCode: 'AU', code: 'NSW', name: 'New South Wales', type: 'state' },
    { countryCode: 'AU', code: 'VIC', name: 'Victoria', type: 'state' },
    { countryCode: 'AU', code: 'QLD', name: 'Queensland', type: 'state' },
    { countryCode: 'AU', code: 'WA', name: 'Western Australia', type: 'state' },
    { countryCode: 'AU', code: 'SA', name: 'South Australia', type: 'state' },
    { countryCode: 'AU', code: 'TAS', name: 'Tasmania', type: 'state' },
    { countryCode: 'AU', code: 'ACT', name: 'Australian Capital Territory', type: 'territory' },
    { countryCode: 'AU', code: 'NT', name: 'Northern Territory', type: 'territory' }
  ]

  for (const divisionData of adminDivisions) {
    const country = createdCountries.find(c => c.code === divisionData.countryCode)
    if (country) {
      await prisma.region.upsert({
        where: {
          countryId_code: {
            countryId: country.id,
            code: divisionData.code
          }
        },
        update: {},
        create: {
          countryId: country.id,
          code: divisionData.code,
          name: divisionData.name,
          type: divisionData.type
        }
      })
    }
  }

  console.log('✅ Created regions')

  // Create sample nations and tribes for treaty management system
  const sampleNations = [
    {
      name: 'United Tribe of Aotearoa',
      officialName: 'United Tribe of Aotearoa',
      description: 'Sovereign tribal nation representing the indigenous peoples of New Zealand',
      contactEmail: '<EMAIL>',
      contactPhone: '+64-9-123-4567',
      website: 'https://aotearoa-tribe.org',
      status: 'ACTIVE'
    },
    {
      name: 'Terra Australis',
      officialName: 'Terra Australis',
      description: 'Sovereign nation representing the peoples of Australia',
      contactEmail: '<EMAIL>',
      contactPhone: '+61-2-9876-5432',
      website: 'https://terra-australis.gov.au',
      status: 'ACTIVE'
    },
    {
      name: 'Pacific Islands Alliance',
      officialName: 'Pacific Islands Alliance',
      description: 'Alliance of Pacific Island nations working together for peace and prosperity',
      contactEmail: '<EMAIL>',
      contactPhone: '+************',
      website: 'https://pacific-alliance.org',
      status: 'ACTIVE'
    },
    {
      name: 'First Nations Confederacy',
      officialName: 'First Nations Confederacy of North America',
      description: 'Confederation of First Nations peoples across North America',
      contactEmail: '<EMAIL>',
      contactPhone: '******-555-0123',
      website: 'https://firstnations-confederacy.org',
      status: 'ACTIVE'
    },
    {
      name: 'Māori Sovereign Nation',
      officialName: 'Māori Sovereign Nation',
      description: 'Representing the Māori people of New Zealand in international affairs',
      contactEmail: '<EMAIL>',
      contactPhone: '+64-7-888-9999',
      website: 'https://maori-nation.nz',
      status: 'ACTIVE'
    }
  ]

  const createdNations = []
  for (const nationData of sampleNations) {
    const nation = await prisma.nationTreaty.upsert({
      where: { name: nationData.name },
      update: {},
      create: {
        name: nationData.name,
        officialName: nationData.officialName,
        description: nationData.description,
        contactEmail: nationData.contactEmail,
        contactPhone: nationData.contactPhone,
        website: nationData.website,
        status: nationData.status as any, // Type cast to handle enum
      }
    })
    createdNations.push(nation)
  }

  console.log('✅ Created sample nations and tribes')

  const defaultPassword = await bcrypt.hash('password123', 12)

  // Create enhanced test user with complete profile
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Test User',
      passwordHash: defaultPassword,
    },
  })

  // Create comprehensive user profile for test user
  const testUserCountry = createdCountries.find(c => c.name === 'USA');
  const testUserCity = createdCities.find(c => c.name === 'Houston' && c.countryId === testUserCountry?.id);
  const testUserRegion = testUserCountry ? await prisma.region.findFirst({
    where: { countryId: testUserCountry.id, code: 'TX' }
  }) : null;

  if (testUserCountry && testUserCity) {
    await prisma.userProfile.upsert({
      where: { userId: testUser.id },
      update: {},
      create: {
        userId: testUser.id,
        firstName: 'Test',
        lastName: 'User',
        personalEmail: '<EMAIL>',
        nwaEmail: '<EMAIL>',
        phone: '******-0123',
        mobile: '******-0124',
        dateOfBirth: new Date('1990-01-15'),
        streetAddress1: '123 Main Street',
        streetAddress2: 'Apt 4B',
        town: 'Houston',
        postalCode: '77001',
        countryId: testUserCountry.id,
        cityId: testUserCity.id,
        regionId: testUserRegion?.id,
        bio: 'Test user for development purposes with complete profile information',
        peaceAmbassadorNumber: 'NWA-TEST-001',
      },
    });
  }

  // Assign member role to test user
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: testUser.id,
        roleId: memberRole.id,
      }
    },
    update: {},
    create: {
      userId: testUser.id,
      roleId: memberRole.id,
    },
  })

  // Assign admin role to test user for development
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: testUser.id,
        roleId: adminRole.id,
      }
    },
    update: {},
    create: {
      userId: testUser.id,
      roleId: adminRole.id,
    },
  })

  console.log('✅ Created enhanced test user with complete profile and admin privileges')

  // Create dedicated admin user aligned with Cypress E2E credentials
  const cypressAdminPassword = await bcrypt.hash('AdminPass123!', 12)
  const cypressAdminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      name: 'Cypress Admin',
      passwordHash: cypressAdminPassword,
      twoFactorEnabled: false,
    },
    create: {
      email: '<EMAIL>',
      name: 'Cypress Admin',
      passwordHash: cypressAdminPassword,
      twoFactorEnabled: false,
    },
  })

  await prisma.userProfile.upsert({
    where: { userId: cypressAdminUser.id },
    update: {
      firstName: 'Cypress',
      lastName: 'Admin',
      personalEmail: '<EMAIL>',
      nwaEmail: '<EMAIL>',
      phone: '******-0101',
      mobile: '******-0199',
      streetAddress1: '450 Cypress Integration Way',
      streetAddress2: 'Suite 600',
      town: testUserCity?.name ?? 'Houston',
      postalCode: '73301',
      bio: 'System administrator account dedicated to automated Cypress coverage.',
      peaceAmbassadorNumber: 'NWA-ADMIN-001',
      countryId: testUserCountry?.id ?? null,
      cityId: testUserCity?.id ?? null,
      regionId: testUserRegion?.id ?? null,
    },
    create: {
      userId: cypressAdminUser.id,
      firstName: 'Cypress',
      lastName: 'Admin',
      personalEmail: '<EMAIL>',
      nwaEmail: '<EMAIL>',
      phone: '******-0101',
      mobile: '******-0199',
      streetAddress1: '450 Cypress Integration Way',
      streetAddress2: 'Suite 600',
      town: testUserCity?.name ?? 'Houston',
      postalCode: '73301',
      bio: 'System administrator account dedicated to automated Cypress coverage.',
      peaceAmbassadorNumber: 'NWA-ADMIN-001',
      countryId: testUserCountry?.id ?? null,
      cityId: testUserCity?.id ?? null,
      regionId: testUserRegion?.id ?? null,
    },
  })

  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: cypressAdminUser.id,
        roleId: adminRole.id,
      }
    },
    update: {},
    create: {
      userId: cypressAdminUser.id,
      roleId: adminRole.id,
    },
  })

  console.log('✅ Created Cypress administration user for end-to-end coverage')

  // Create additional test users with complete profiles
  const additionalTestUsers = [
    {
      email: '<EMAIL>',
      name: 'Jane Doe',
      firstName: 'Jane',
      lastName: 'Doe',
      personalEmail: '<EMAIL>',
      nwaEmail: '<EMAIL>',
      phone: '******-0125',
      mobile: '******-0126',
      dateOfBirth: new Date('1985-05-20'),
      streetAddress1: '456 Oak Avenue',
      streetAddress2: 'Suite 200',
      town: 'Los Angeles',
      postalCode: '90210',
      countryCode: 'US',
      cityName: 'Los Angeles',
      regionCode: 'CA',
      bio: 'Senior project manager with expertise in international relations and treaty negotiations.',
      peaceAmbassadorNumber: 'NWA-JANE-002'
    },
    {
      email: '<EMAIL>', 
      name: 'John Smith',
      firstName: 'John',
      lastName: 'Smith',
      personalEmail: '<EMAIL>',
      nwaEmail: '<EMAIL>',
      phone: '******-0127',
      mobile: '******-0128',
      dateOfBirth: new Date('1992-11-30'),
      streetAddress1: '789 Pine Street',
      streetAddress2: '',
      town: 'Phoenix',
      postalCode: '85001',
      countryCode: 'US',
      cityName: 'Phoenix',
      regionCode: 'AZ',
      bio: 'Environmental lawyer and human rights advocate specializing in international environmental law.',
      peaceAmbassadorNumber: 'NWA-JOHN-003'
    },
    {
      email: '<EMAIL>',
      name: 'Mary Williams',
      firstName: 'Mary',
      lastName: 'Williams',
      personalEmail: '<EMAIL>',
      nwaEmail: '<EMAIL>',
      phone: '******-0129',
      mobile: '******-0130',
      dateOfBirth: new Date('1988-08-12'),
      streetAddress1: '321 Elm Street',
      streetAddress2: 'Apt 5C',
      town: 'Dallas',
      postalCode: '75201',
      countryCode: 'US',
      cityName: 'Dallas',
      regionCode: 'TX',
      bio: 'Cultural ambassador and educator focused on preserving indigenous cultures and traditions.',
      peaceAmbassadorNumber: 'NWA-MARY-004'
    },
    {
      email: '<EMAIL>',
      name: 'David Brown', 
      firstName: 'David',
      lastName: 'Brown',
      personalEmail: '<EMAIL>',
      nwaEmail: '<EMAIL>',
      phone: '******-0131',
      mobile: '******-0132',
      dateOfBirth: new Date('1975-03-08'),
      streetAddress1: '654 Maple Drive',
      streetAddress2: '',
      town: 'Toronto',
      postalCode: 'M5H 2N2',
      countryCode: 'CA',
      cityName: 'Toronto',
      regionCode: 'ON',
      bio: 'Economic development specialist with experience in international trade and sustainable development projects.',
      peaceAmbassadorNumber: 'NWA-DAVID-005'
    },
    {
      email: '<EMAIL>',
      name: 'Sarah Jones',
      firstName: 'Sarah', 
      lastName: 'Jones',
      personalEmail: '<EMAIL>',
      nwaEmail: '<EMAIL>',
      phone: '+44-20-7946-0123',
      mobile: '+44-20-7946-0124',
      dateOfBirth: new Date('1995-12-03'),
      streetAddress1: '987 Thames Street',
      streetAddress2: 'Flat 3B',
      town: 'London',
      postalCode: 'EC4V 3BJ',
      countryCode: 'GB',
      cityName: 'London',
      regionCode: 'LND',
      bio: 'Youth activist and social entrepreneur working on peace education programs for young people.',
      peaceAmbassadorNumber: 'NWA-SARAH-PLN-001'
    }
  ];

  // Create additional test users with profiles
  for (const userData of additionalTestUsers) {
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: {
        email: userData.email,
        name: userData.name,
        passwordHash: defaultPassword,
      },
    });

    const country = createdCountries.find(c => c.code === userData.countryCode);
    const city = createdCities.find(c => c.name === userData.cityName && c.countryId === country?.id);
    const region = country ? await prisma.region.findFirst({
      where: { countryId: country.id, code: userData.regionCode }
    }) : null;

    if (country && city) {
      await prisma.userProfile.upsert({
        where: { userId: user.id },
        update: {},
        create: {
          userId: user.id,
          firstName: userData.firstName,
          lastName: userData.lastName,
          personalEmail: userData.personalEmail,
          nwaEmail: userData.nwaEmail,
          phone: userData.phone,
          mobile: userData.mobile,
          dateOfBirth: userData.dateOfBirth,
          streetAddress1: userData.streetAddress1,
          streetAddress2: userData.streetAddress2,
          town: userData.town,
          postalCode: userData.postalCode,
          countryId: country.id,
          cityId: city.id,
          regionId: region?.id,
          bio: userData.bio,
          peaceAmbassadorNumber: userData.peaceAmbassadorNumber,
        },
      });
    }

    // Assign member role to additional users
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: user.id,
          roleId: memberRole.id,
        }
      },
      update: {},
      create: {
        userId: user.id,
        roleId: memberRole.id,
      },
    });
  }

  console.log('✅ Created additional test users with complete profiles')

  // Assign specific titles to users for testing
  const sarahUser = await prisma.user.findUnique({ where: { email: '<EMAIL>' } });
  const plenipotentiaryTitle = createdTitles.find(t => t.name === 'Plenipotentiary Ambassador');
  
  if (sarahUser && plenipotentiaryTitle) {
    await prisma.userProfile.update({
      where: { userId: sarahUser.id },
      data: { titleId: plenipotentiaryTitle.id }
    });
    console.log('✅ Assigned Plenipotentiary Ambassador title to Sarah Jones');
  }

  // Create admin user with specified credentials
  const adminPassword = await bcrypt.hash('fcA*5-c0nwmFF!!', 12)
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'darren edward',
      passwordHash: adminPassword,
    },
  })

  // Assign admin role to the specified user
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: adminUser.id,
        roleId: adminRole.id,
      }
    },
    update: {},
    create: {
      userId: adminUser.id,
      roleId: adminRole.id,
    },
  })

  console.log('✅ Created admin user with specified credentials')

  // Treaty data migration is handled separately by migrate-treaty-data.cjs
  console.log('✅ Treaty data migration handled separately')

  // Create OAuth client configuration for NWAPromote
  const nwaPromoteClient = await prisma.remoteServer.upsert({
    where: { clientId: 'nwapromote-client-local' },
    update: {},
    create: {
      name: 'NWA Promote Local',
      url: 'http://localhost:3002',
      apiKey: 'nwa_promote_api_key_dev_only',
      description: 'Local development client for NWA Promote',
      isActive: true,
      clientId: 'nwapromote-client-local',
      clientSecret: 'da69150be2ae984ea232f144387add211844500d9d94de131ba78f9e2936fbff',
      redirectUris: ['http://localhost:3002/api/auth/callback/member-portal-custom'],
      grantTypes: ['authorization_code', 'refresh_token'],
      defaultScopes: ['read:profile'],
      callbackUrl: 'http://localhost:3002/api/auth/callback/member-portal-custom',
      allowedOrigins: ['http://localhost:3002'],
      tokenEndpointAuthMethod: 'client_secret_basic',
    },
  })

  console.log('✅ Created OAuth client configuration for NWAPromote')

  console.log('🎉 Database seeding completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

// Utility function to create a user with a profile
async function createUserWithProfile(userData: any, profileData: any, roleName: string) {
  const user = await prisma.user.upsert({
    where: { email: userData.email },
    update: {},
    create: {
      ...userData,
      passwordHash: await bcrypt.hash(userData.password, 12),
    },
  })

  const role = await prisma.role.findUnique({ where: { name: roleName } })
  if (role) {
    await prisma.userRole.upsert({
      where: { userId_roleId: { userId: user.id, roleId: role.id } },
      update: {},
      create: { userId: user.id, roleId: role.id },
    })
  }

  const country = await prisma.country.findUnique({ where: { code: profileData.countryCode } })
  if (!country) {
    console.warn(`Country with code ${profileData.countryCode} not found for user ${userData.email}`)
    return
  }

  const city = await prisma.city.findFirst({
    where: { name: profileData.cityName, countryId: country.id },
  })
  if (!city) {
    console.warn(`City ${profileData.cityName} in country ${profileData.countryCode} not found for user ${userData.email}`)
    return
  }

  await prisma.userProfile.upsert({
    where: { userId: user.id },
    update: {},
    create: {
      userId: user.id,
      firstName: profileData.firstName,
      lastName: profileData.lastName,
      nwaEmail: profileData.nwaEmail,
      countryId: country.id,
      cityId: city.id,
      mobile: profileData.mobile,
      bio: profileData.bio,
    },
  })

  console.log(`✅ Created user: ${userData.email}`)
}
