import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

describe('Remote Server Permission Database Schema Tests', () => {
  beforeAll(async () => {
    await prisma.$connect()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  beforeEach(async () => {
    // Clean up test data before each test to avoid unique constraint violations
    // Clean up in reverse order of dependencies
    await prisma.user_remote_server_permissions.deleteMany({})
    await prisma.user_remote_server_access.deleteMany({})
    await prisma.role_remote_server_access.deleteMany({})
    await prisma.remote_server_permissions.deleteMany({})
    await prisma.authorizationCode.deleteMany({})
    await prisma.oAuthToken.deleteMany({})
    await prisma.auditLog.deleteMany({})
    await prisma.userRole.deleteMany({})
    await prisma.remoteServer.deleteMany({})
    await prisma.role.deleteMany({})
    await prisma.user.deleteMany({})
  })

  describe('User Remote Server Access Model', () => {
    test('should create user remote server access relationship', async () => {
      // Create test user
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Remote Access User',
        },
      })

      // Create test remote server
      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-remote-server-access',
          name: 'Test Remote Server',
          url: 'http://test.example.com',
          apiKey: 'test-api-key-123',
          description: 'Test server for access validation',
        },
      })

      // Create user remote server access
      const userRemoteAccess = await prisma.user_remote_server_access.create({
        data: {
          user_id: user.id,
          remote_server_id: remoteServer.id,
          granted_by: user.id,
          is_active: true,
          notes: 'Test access grant',
        },
      })

      expect(userRemoteAccess.user_id).toBe(user.id)
      expect(userRemoteAccess.remote_server_id).toBe(remoteServer.id)
      expect(userRemoteAccess.is_active).toBe(true)
      expect(userRemoteAccess.notes).toBe('Test access grant')
      expect(userRemoteAccess.granted_at).toBeDefined()

      // Clean up
      await prisma.user_remote_server_access.delete({ where: { id: userRemoteAccess.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })

    test('should enforce unique constraint on user-server relationship', async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Unique Test User',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-unique-server',
          name: 'Unique Test Server',
          url: 'http://unique.example.com',
          apiKey: 'unique-api-key-123',
        },
      })

      // Create first access record
      const firstAccess = await prisma.user_remote_server_access.create({
        data: {
          user_id: user.id,
          remote_server_id: remoteServer.id,
          granted_by: user.id,
        },
      })

      // Attempt to create duplicate should fail
      await expect(
        prisma.user_remote_server_access.create({
          data: {
            user_id: user.id,
            remote_server_id: remoteServer.id,
            granted_by: user.id,
          },
        })
      ).rejects.toThrow()

      // Clean up
      await prisma.user_remote_server_access.delete({ where: { id: firstAccess.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })
  })

  describe('Remote Server Permissions Model', () => {
    test('should store permissions fetched from remote server', async () => {
      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-permissions-server',
          name: 'Permissions Test Server',
          url: 'http://permissions.example.com',
          apiKey: 'permissions-api-key-123',
        },
      })

      const serverPermission = await prisma.remote_server_permissions.create({
        data: {
          remote_server_id: remoteServer.id,
          permission_name: 'view_documents',
          permission_description: 'Permission to view documents',
          is_active: true,
        },
      })

      expect(serverPermission.remote_server_id).toBe(remoteServer.id)
      expect(serverPermission.permission_name).toBe('view_documents')
      expect(serverPermission.permission_description).toBe('Permission to view documents')
      expect(serverPermission.is_active).toBe(true)
      expect(serverPermission.last_synced_at).toBeDefined()

      // Clean up
      await prisma.remote_server_permissions.delete({ where: { id: serverPermission.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
    })

    test('should enforce unique constraint on server-permission combination', async () => {
      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-permission-unique-server',
          name: 'Permission Unique Test Server',
          url: 'http://permunique.example.com',
          apiKey: 'perm-unique-api-key-123',
        },
      })

      // Create first permission
      const firstPermission = await prisma.remote_server_permissions.create({
        data: {
          remote_server_id: remoteServer.id,
          permission_name: 'duplicate_permission',
          permission_description: 'Test permission for uniqueness',
        },
      })

      // Attempt to create duplicate should fail
      await expect(
        prisma.remote_server_permissions.create({
          data: {
            remote_server_id: remoteServer.id,
            permission_name: 'duplicate_permission',
            permission_description: 'Another description',
          },
        })
      ).rejects.toThrow()

      // Clean up
      await prisma.remote_server_permissions.delete({ where: { id: firstPermission.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
    })
  })

  describe('User Remote Server Permissions Model', () => {
    test('should assign specific permissions to user for remote server', async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'User Permissions Test',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-user-permissions-server',
          name: 'User Permissions Server',
          url: 'http://userperms.example.com',
          apiKey: 'user-perms-api-key-123',
        },
      })

      const userServerPermission = await prisma.user_remote_server_permissions.create({
        data: {
          user_id: user.id,
          remote_server_id: remoteServer.id,
          permission_name: 'send_documents',
          granted_by: user.id,
          is_active: true,
        },
      })

      expect(userServerPermission.user_id).toBe(user.id)
      expect(userServerPermission.remote_server_id).toBe(remoteServer.id)
      expect(userServerPermission.permission_name).toBe('send_documents')
      expect(userServerPermission.is_active).toBe(true)
      expect(userServerPermission.granted_at).toBeDefined()

      // Clean up
      await prisma.user_remote_server_permissions.delete({ where: { id: userServerPermission.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })

    test('should support permission expiration', async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Expiration Test User',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-expiration-server',
          name: 'Expiration Test Server',
          url: 'http://expiration.example.com',
          apiKey: 'expiration-api-key-123',
        },
      })

      const futureDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now

      const userServerPermission = await prisma.user_remote_server_permissions.create({
        data: {
          user_id: user.id,
          remote_server_id: remoteServer.id,
          permission_name: 'temporary_access',
          granted_by: user.id,
          expires_at: futureDate,
        },
      })

      expect(userServerPermission.expires_at).toEqual(futureDate)

      // Clean up
      await prisma.user_remote_server_permissions.delete({ where: { id: userServerPermission.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })

    test('should enforce unique constraint on user-server-permission combination', async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'User Permission Unique Test',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-user-perm-unique-server',
          name: 'User Permission Unique Server',
          url: 'http://userpermunique.example.com',
          apiKey: 'user-perm-unique-api-key-123',
        },
      })

      // Create first user permission
      const firstPermission = await prisma.user_remote_server_permissions.create({
        data: {
          user_id: user.id,
          remote_server_id: remoteServer.id,
          permission_name: 'unique_user_permission',
          granted_by: user.id,
        },
      })

      // Attempt to create duplicate should fail
      await expect(
        prisma.user_remote_server_permissions.create({
          data: {
            user_id: user.id,
            remote_server_id: remoteServer.id,
            permission_name: 'unique_user_permission',
            granted_by: user.id,
          },
        })
      ).rejects.toThrow()

      // Clean up
      await prisma.user_remote_server_permissions.delete({ where: { id: firstPermission.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })
  })

  describe('Role Remote Server Access Model', () => {
    test('should configure automatic server access for roles', async () => {
      const role = await prisma.role.create({
        data: {
          name: 'test-envoy-role',
          description: 'Test Envoy role for server access',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-role-server',
          name: 'Role Test Server',
          url: 'http://roletest.example.com',
          apiKey: 'role-test-api-key-123',
        },
      })

      const testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Role Test User',
        },
      })

      const roleServerAccess = await prisma.role_remote_server_access.create({
        data: {
          role_id: role.id,
          remote_server_id: remoteServer.id,
          auto_grant_permissions: ['view_documents', 'send_documents'],
          created_by: testUser.id,
          is_active: true,
        },
      })

      expect(roleServerAccess.role_id).toBe(role.id)
      expect(roleServerAccess.remote_server_id).toBe(remoteServer.id)
      expect(roleServerAccess.auto_grant_permissions).toEqual(['view_documents', 'send_documents'])
      expect(roleServerAccess.is_active).toBe(true)
      expect(roleServerAccess.created_at).toBeDefined()

      // Clean up
      await prisma.role_remote_server_access.delete({ where: { id: roleServerAccess.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.role.delete({ where: { id: role.id } })
      await prisma.user.delete({ where: { id: testUser.id } })
    })

    test('should enforce unique constraint on role-server combination', async () => {
      const role = await prisma.role.create({
        data: {
          name: 'test-unique-role',
          description: 'Test role for uniqueness validation',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-role-unique-server',
          name: 'Role Unique Test Server',
          url: 'http://roleunique.example.com',
          apiKey: 'role-unique-api-key-123',
        },
      })

      const testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Role Unique Test User',
        },
      })

      // Create first role-server access
      const firstAccess = await prisma.role_remote_server_access.create({
        data: {
          role_id: role.id,
          remote_server_id: remoteServer.id,
          auto_grant_permissions: ['test_permission'],
          created_by: testUser.id,
        },
      })

      // Attempt to create duplicate should fail
      await expect(
        prisma.role_remote_server_access.create({
          data: {
            role_id: role.id,
            remote_server_id: remoteServer.id,
            auto_grant_permissions: ['different_permission'],
            created_by: testUser.id,
          },
        })
      ).rejects.toThrow()

      // Clean up
      await prisma.role_remote_server_access.delete({ where: { id: firstAccess.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.role.delete({ where: { id: role.id } })
      await prisma.user.delete({ where: { id: testUser.id } })
    })
  })

  describe('Enhanced Audit Logs Model', () => {
    test('should store geographic information in audit logs', async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Geographic Audit User',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-geo-audit-server',
          name: 'Geographic Audit Server',
          url: 'http://geoaudit.example.com',
          apiKey: 'geo-audit-api-key-123',
        },
      })

      const auditLog = await prisma.auditLog.create({
        data: {
          user: { connect: { id: user.id } },
          action: 'GRANT_REMOTE_ACCESS',
          resource: 'remote_server_access',
          resourceId: remoteServer.id,
          oldValues: {},
          newValues: { access: 'granted' },
          ipAddress: '*************',
          country_code: 'US',
          city: 'New York',
          userAgent: 'test-browser',
          remote_servers: { connect: { id: remoteServer.id } },
          success: true,
          apiEndpoint: '/api/admin/remote-servers/assign',
          requestMethod: 'POST',
        },
      })

      expect(auditLog.userId).toBe(user.id)
      expect(auditLog.action).toBe('GRANT_REMOTE_ACCESS')
      expect(auditLog.ipAddress).toBe('*************')
      expect(auditLog.country_code).toBe('US')
      expect(auditLog.city).toBe('New York')
      expect(auditLog.remote_server_id).toBe(remoteServer.id)

      // Clean up
      await prisma.auditLog.delete({ where: { id: auditLog.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })
  })

  describe('Complete Workflow Integration Tests', () => {
    test('should handle complete user remote server permission workflow', async () => {
      // Create test entities
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Workflow Test User',
        },
      })

      const role = await prisma.role.create({
        data: {
          name: 'test-workflow-role',
          description: 'Test role for workflow validation',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-workflow-server',
          name: 'Workflow Test Server',
          url: 'http://workflow.example.com',
          apiKey: 'workflow-api-key-123',
        },
      })

      // 1. Assign user to role
      const userRole = await prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: role.id,
          assignedBy: user.id,
        },
      })

      // 2. Configure role for automatic server access
      const roleServerAccess = await prisma.role_remote_server_access.create({
        data: {
          role_id: role.id,
          remote_server_id: remoteServer.id,
          auto_grant_permissions: ['view_documents', 'send_documents'],
          created_by: user.id,
        },
      })

      // 3. Grant user access to server
      const userServerAccess = await prisma.user_remote_server_access.create({
        data: {
          user_id: user.id,
          remote_server_id: remoteServer.id,
          granted_by: user.id,
        },
      })

      // 4. Add specific permissions
      const userPermission1 = await prisma.user_remote_server_permissions.create({
        data: {
          user_id: user.id,
          remote_server_id: remoteServer.id,
          permission_name: 'view_documents',
          granted_by: user.id,
        },
      })

      const userPermission2 = await prisma.user_remote_server_permissions.create({
        data: {
          user_id: user.id,
          remote_server_id: remoteServer.id,
          permission_name: 'send_documents',
          granted_by: user.id,
        },
      })

      // 5. Store remote server permissions
      const serverPermission1 = await prisma.remote_server_permissions.create({
        data: {
          remote_server_id: remoteServer.id,
          permission_name: 'view_documents',
          permission_description: 'View documents permission',
        },
      })

      const serverPermission2 = await prisma.remote_server_permissions.create({
        data: {
          remote_server_id: remoteServer.id,
          permission_name: 'send_documents',
          permission_description: 'Send documents permission',
        },
      })

      // Verify all relationships work
      // We need to query each relationship separately since they don't have simple include names
      const userRoles = await prisma.userRole.findMany({
        where: { userId: user.id },
        include: {
          role: {
            include: {
              role_remote_server_access: true,
            },
          },
        },
      })

      const userServerAccessRecords = await prisma.user_remote_server_access.findMany({
        where: { user_id: user.id }
      })

      const userServerPermissionsRecords = await prisma.user_remote_server_permissions.findMany({
        where: { user_id: user.id }
      })

      expect(userRoles).toHaveLength(1)
      expect(userServerAccessRecords).toHaveLength(1)
      expect(userServerPermissionsRecords).toHaveLength(2)
      expect(userRoles[0].role.role_remote_server_access).toHaveLength(1)

      // Clean up in reverse order
      await prisma.user_remote_server_permissions.delete({ where: { id: userPermission1.id } })
      await prisma.user_remote_server_permissions.delete({ where: { id: userPermission2.id } })
      await prisma.remote_server_permissions.delete({ where: { id: serverPermission1.id } })
      await prisma.remote_server_permissions.delete({ where: { id: serverPermission2.id } })
      await prisma.user_remote_server_access.delete({ where: { id: userServerAccess.id } })
      await prisma.role_remote_server_access.delete({ where: { id: roleServerAccess.id } })
      await prisma.userRole.delete({ where: { id: userRole.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.role.delete({ where: { id: role.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })
  })
})