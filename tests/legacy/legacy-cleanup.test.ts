import { glob } from 'glob';
import fs from 'fs';

describe('Legacy fields cleanup', () => {
  test('src/ has no legacy identification field references', async () => {
    const files = await glob('src/**/*.{ts,tsx}');

    const forbiddenPatterns = [
      /driversLicense\b/,
      /passportNumber\b/,
      /tradeTreatyNumber\b/,
      /name\s*=\s*"license"/,
      /name\s*=\s*"passport"/
    ];

    const offenders: { file: string; line: number; text: string }[] = [];

    for (const file of files) {
      // Skip tests directory to avoid false positives in test fixtures
      if (file.includes('__tests__')) continue;

      const content = fs.readFileSync(file, 'utf8');
      const lines = content.split(/\r?\n/);
      lines.forEach((line, idx) => {
        forbiddenPatterns.forEach((re) => {
          if (re.test(line)) {
            offenders.push({ file, line: idx + 1, text: line.trim() });
          }
        });
      });
    }

    if (offenders.length > 0) {
      console.error('Legacy references found:', offenders);
      throw new Error(`Found ${offenders.length} legacy references in src/`);
    }
  });
});

