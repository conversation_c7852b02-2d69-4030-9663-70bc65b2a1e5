// Mock global Request first
global.Request = class MockRequest {
  constructor(public url: string, public init?: any) {}
} as any;

import { GET } from '../../src/app/api/countries/[id]/address-format/route';
import { prisma } from '../../src/lib/prisma';

jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    countryAddressFormat: {
      findUnique: jest.fn(),
    },
    country: {
      findUnique: jest.fn(),
    },
  },
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

function makeRequest(params: { id: string }) {
  return { params };
}

describe('GET /api/countries/[id]/address-format', () => {
  beforeEach(() => {
    jest.resetAllMocks();
    // Note: Cache clearing is handled internally by the route
  });

  describe('Success scenarios', () => {
    it('returns address format for existing country', async () => {
      const mockAddressFormat = {
        id: 1,
        countryId: 33,
        postalCodeLabel: 'ZIP Code',
        postalCodeFormat: '^\\d{5}(-\\d{4})?$',
        postalCodeRequired: true,
        regionLabel: 'State',
        regionRequired: true,
        townLabel: 'City',
        townRequired: true,
        addressTemplate: '{street}\\n{town}, {region} {postalCode}\\n{country}',
        createdAt: new Date('2025-01-01T00:00:00Z'),
        updatedAt: new Date('2025-01-01T00:00:00Z')
      };

      mockPrisma.countryAddressFormat.findUnique.mockResolvedValue(mockAddressFormat);

      const request = makeRequest({ id: '33' });
      const response = await GET(request as any, { params: { id: '33' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.addressFormat).toMatchObject({
        id: 1,
        countryId: 33,
        postalCodeLabel: 'ZIP Code',
        postalCodeFormat: '^\\d{5}(-\\d{4})?$',
        postalCodeRequired: true,
        regionLabel: 'State',
        regionRequired: true,
        townLabel: 'City',
        townRequired: true,
        addressTemplate: '{street}\\n{town}, {region} {postalCode}\\n{country}'
      });
      expect(mockPrisma.countryAddressFormat.findUnique).toHaveBeenCalledWith({
        where: {
          countryId: 33,
        },
      });
    });

    it('returns default address format when no format exists for country', async () => {
      const mockCountry = {
        id: 999,
        name: 'Test Country',
        code: 'TC',
        createdAt: new Date('2025-01-01T00:00:00Z'),
        updatedAt: new Date('2025-01-01T00:00:00Z')
      };

      mockPrisma.countryAddressFormat.findUnique.mockResolvedValue(null);
      mockPrisma.country.findUnique.mockResolvedValue(mockCountry);

      const request = makeRequest({ id: '999' });
      const response = await GET(request as any, { params: { id: '999' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.addressFormat).toEqual({
        id: null,
        countryId: 999,
        postalCodeLabel: 'Postal Code',
        postalCodeFormat: '^.+$',
        postalCodeRequired: false,
        regionLabel: 'Region',
        regionRequired: false,
        townLabel: 'City',
        townRequired: true,
        addressTemplate: '{street}\\n{town}\\n{country}',
        createdAt: null,
        updatedAt: null
      });
    });

    it('handles different country address format patterns', async () => {
      const ukAddressFormat = {
        id: 2,
        countryId: 35,
        postalCodeLabel: 'Postcode',
        postalCodeFormat: '^[A-Z]{1,2}\\d[A-Z\\d]? ?\\d[A-Z]{2}$',
        postalCodeRequired: true,
        regionLabel: 'County',
        regionRequired: false,
        townLabel: 'Town/City',
        townRequired: true,
        addressTemplate: '{street}\\n{town}\\n{region}\\n{postalCode}\\n{country}',
        createdAt: new Date('2025-01-01T00:00:00Z'),
        updatedAt: new Date('2025-01-01T00:00:00Z')
      };

      mockPrisma.countryAddressFormat.findUnique.mockResolvedValue(ukAddressFormat);

      const request = makeRequest({ id: '35' });
      const response = await GET(request as any, { params: { id: '35' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.addressFormat.postalCodeLabel).toBe('Postcode');
      expect(data.addressFormat.regionLabel).toBe('County');
      expect(data.addressFormat.regionRequired).toBe(false);
    });
  });

  describe('Error scenarios', () => {
    it('returns 400 when id parameter is missing', async () => {
      const request = makeRequest({ id: '' });
      const response = await GET(request as any, { params: { id: '' } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toEqual({
        error: 'Bad Request',
        message: 'Country ID is required'
      });
      expect(mockPrisma.countryAddressFormat.findUnique).not.toHaveBeenCalled();
    });

    it('returns 400 when id parameter is invalid (non-numeric)', async () => {
      const request = makeRequest({ id: 'invalid' });
      const response = await GET(request as any, { params: { id: 'invalid' } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toEqual({
        error: 'Bad Request',
        message: 'Country ID must be a valid integer'
      });
      expect(mockPrisma.countryAddressFormat.findUnique).not.toHaveBeenCalled();
    });

    it('returns 400 when id parameter is zero', async () => {
      const request = makeRequest({ id: '0' });
      const response = await GET(request as any, { params: { id: '0' } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toEqual({
        error: 'Bad Request',
        message: 'Country ID must be a valid integer'
      });
    });

    it('returns 400 when id parameter is negative', async () => {
      const request = makeRequest({ id: '-1' });
      const response = await GET(request as any, { params: { id: '-1' } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toEqual({
        error: 'Bad Request',
        message: 'Country ID must be a valid integer'
      });
    });

    it('returns 404 when country does not exist', async () => {
      // Clear any cached data and ensure mocks return proper sequence
      mockPrisma.countryAddressFormat.findUnique.mockResolvedValue(null);
      mockPrisma.country.findUnique.mockResolvedValue(null);

      const request = makeRequest({ id: '999' });
      const response = await GET(request as any, { params: { id: '999' } });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data).toEqual({
        error: 'Not Found',
        message: 'Country not found'
      });
      expect(mockPrisma.countryAddressFormat.findUnique).toHaveBeenCalledWith({
        where: { countryId: 999 }
      });
      expect(mockPrisma.country.findUnique).toHaveBeenCalledWith({
        where: { id: 999 }
      });
    });

    it('returns 500 when database error occurs', async () => {
      const dbError = new Error('Database connection failed');
      mockPrisma.countryAddressFormat.findUnique.mockRejectedValue(dbError);

      const request = makeRequest({ id: '123' }); // Use different ID to avoid cache
      const response = await GET(request as any, { params: { id: '123' } });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data).toEqual({
        error: 'Internal Server Error',
        message: 'An error occurred while fetching address format'
      });
    });
  });

  describe('Response structure validation', () => {
    it('returns address format with correct structure', async () => {
      const mockAddressFormat = {
        id: 1,
        countryId: 33,
        postalCodeLabel: 'ZIP Code',
        postalCodeFormat: '^\\d{5}(-\\d{4})?$',
        postalCodeRequired: true,
        regionLabel: 'State',
        regionRequired: true,
        townLabel: 'City',
        townRequired: true,
        addressTemplate: '{street}\\n{town}, {region} {postalCode}\\n{country}',
        createdAt: new Date('2025-01-01T00:00:00Z'),
        updatedAt: new Date('2025-01-01T00:00:00Z')
      };

      mockPrisma.countryAddressFormat.findUnique.mockResolvedValue(mockAddressFormat);

      const request = makeRequest({ id: '33' });
      const response = await GET(request as any, { params: { id: '33' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.addressFormat).toHaveProperty('id', 1);
      expect(data.addressFormat).toHaveProperty('countryId', 33);
      expect(data.addressFormat).toHaveProperty('postalCodeLabel', 'ZIP Code');
      expect(data.addressFormat).toHaveProperty('postalCodeFormat', '^\\d{5}(-\\d{4})?$');
      expect(data.addressFormat).toHaveProperty('postalCodeRequired', true);
      expect(data.addressFormat).toHaveProperty('regionLabel', 'State');
      expect(data.addressFormat).toHaveProperty('regionRequired', true);
      expect(data.addressFormat).toHaveProperty('townLabel', 'City');
      expect(data.addressFormat).toHaveProperty('townRequired', true);
      expect(data.addressFormat).toHaveProperty('addressTemplate');
      expect(data.addressFormat).toHaveProperty('createdAt');
      expect(data.addressFormat).toHaveProperty('updatedAt');
    });

    it('returns default format with null values for non-existent country format', async () => {
      const mockCountry = {
        id: 999,
        name: 'Test Country',
        code: 'TC',
        createdAt: new Date('2025-01-01T00:00:00Z'),
        updatedAt: new Date('2025-01-01T00:00:00Z')
      };

      mockPrisma.countryAddressFormat.findUnique.mockResolvedValue(null);
      mockPrisma.country.findUnique.mockResolvedValue(mockCountry);

      const request = makeRequest({ id: '999' });
      const response = await GET(request as any, { params: { id: '999' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.addressFormat).toHaveProperty('id', null);
      expect(data.addressFormat).toHaveProperty('countryId', 999);
      expect(data.addressFormat).toHaveProperty('createdAt', null);
      expect(data.addressFormat).toHaveProperty('updatedAt', null);
      expect(data.addressFormat.postalCodeLabel).toBe('Postal Code');
      expect(data.addressFormat.postalCodeRequired).toBe(false);
      expect(data.addressFormat.regionLabel).toBe('Region');
      expect(data.addressFormat.regionRequired).toBe(false);
    });
  });

  describe('Default address format behavior', () => {
    it('provides sensible defaults for countries without specific rules', async () => {
      const mockCountry = {
        id: 123,
        name: 'Generic Country',
        code: 'GC',
        createdAt: new Date('2025-01-01T00:00:00Z'),
        updatedAt: new Date('2025-01-01T00:00:00Z')
      };

      mockPrisma.countryAddressFormat.findUnique.mockResolvedValue(null);
      mockPrisma.country.findUnique.mockResolvedValue(mockCountry);

      const request = makeRequest({ id: '123' });
      const response = await GET(request as any, { params: { id: '123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      
      const defaultFormat = data.addressFormat;
      expect(defaultFormat.postalCodeLabel).toBe('Postal Code');
      expect(defaultFormat.postalCodeFormat).toBe('^.+$');
      expect(defaultFormat.postalCodeRequired).toBe(false);
      expect(defaultFormat.regionLabel).toBe('Region');
      expect(defaultFormat.regionRequired).toBe(false);
      expect(defaultFormat.townLabel).toBe('City');
      expect(defaultFormat.townRequired).toBe(true);
      expect(defaultFormat.addressTemplate).toBe('{street}\\n{town}\\n{country}');
    });
  });

  describe('Caching behavior', () => {
    it('makes single database call for address format lookup', async () => {
      const mockAddressFormat = {
        id: 1,
        countryId: 444,
        postalCodeLabel: 'ZIP Code',
        postalCodeFormat: '^\\d{5}(-\\d{4})?$',
        postalCodeRequired: true,
        regionLabel: 'State',
        regionRequired: true,
        townLabel: 'City',
        townRequired: true,
        addressTemplate: '{street}\\n{town}, {region} {postalCode}\\n{country}',
        createdAt: new Date('2025-01-01T00:00:00Z'),
        updatedAt: new Date('2025-01-01T00:00:00Z')
      };

      mockPrisma.countryAddressFormat.findUnique.mockResolvedValue(mockAddressFormat);

      const request = makeRequest({ id: '444' }); // Use unique ID to avoid cache conflicts
      await GET(request as any, { params: { id: '444' } });

      expect(mockPrisma.countryAddressFormat.findUnique).toHaveBeenCalledTimes(1);
      expect(mockPrisma.country.findUnique).not.toHaveBeenCalled();
    });
  });
});
