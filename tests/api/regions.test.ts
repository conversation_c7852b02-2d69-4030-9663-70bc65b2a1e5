import { NextRequest } from 'next/server';

// Mock Prisma client
jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    region: {
      findMany: jest.fn(),
    },
    country: {
      findUnique: jest.fn(),
    },
  },
}));

// Mock NextResponse - use jest.fn() directly in mock
jest.mock('next/server', () => ({
  NextResponse: {
    json: jest.fn().mockImplementation((data, options) => ({
      json: () => Promise.resolve(data),
      status: options?.status || 200
    })),
    next: jest.fn(),
  },
}));

import { GET } from '../../src/app/api/regions/route';
import { prisma } from '../../src/lib/prisma';
import { NextResponse } from 'next/server';

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockNextResponse = NextResponse as jest.Mocked<typeof NextResponse>;

function makeRequest(url: string) {
  return {
    url,
    headers: {
      get: jest.fn(),
    },
  } as unknown as NextRequest;
}

describe('GET /api/regions', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  describe('Success scenarios', () => {
    it('returns regions filtered by countryId', async () => {
      const mockCountry = {
        id: 33,
        name: 'United States',
        code: 'US',
        createdAt: new Date('2025-01-01T00:00:00Z'),
        updatedAt: new Date('2025-01-01T00:00:00Z')
      };

      const mockRegions = [
        {
          id: 1,
          countryId: 33,
          code: 'CA',
          name: 'California',
          type: 'state',
          isActive: true,
          createdAt: new Date('2025-01-01T00:00:00Z'),
          updatedAt: new Date('2025-01-01T00:00:00Z')
        },
        {
          id: 2,
          countryId: 33,
          code: 'TX',
          name: 'Texas',
          type: 'state',
          isActive: true,
          createdAt: new Date('2025-01-01T00:00:00Z'),
          updatedAt: new Date('2025-01-01T00:00:00Z')
        }
      ];

      mockPrisma.country.findUnique.mockResolvedValue(mockCountry);
      mockPrisma.region.findMany.mockResolvedValue(mockRegions);

      const request = makeRequest('http://localhost:3000/api/regions?countryId=33');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({ regions: mockRegions });
      expect(mockPrisma.region.findMany).toHaveBeenCalledWith({
        where: {
          countryId: 33,
          isActive: true,
        },
        orderBy: {
          name: 'asc',
        },
      });
    });

    it('returns empty array when no regions found for country', async () => {
      const mockCountry = {
        id: 999,
        name: 'Test Country',
        code: 'TC',
        createdAt: new Date('2025-01-01T00:00:00Z'),
        updatedAt: new Date('2025-01-01T00:00:00Z')
      };

      mockPrisma.country.findUnique.mockResolvedValue(mockCountry);
      mockPrisma.region.findMany.mockResolvedValue([]);

      const request = makeRequest('http://localhost:3000/api/regions?countryId=999');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({ regions: [] });
    });
  });

  describe('Error scenarios', () => {
    it('returns 400 when countryId is missing', async () => {
      const request = makeRequest('http://localhost:3000/api/regions');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toEqual({
        error: 'Bad Request',
        message: 'countryId parameter is required'
      });
      expect(mockPrisma.region.findMany).not.toHaveBeenCalled();
    });

    it('returns 400 when countryId is invalid (non-numeric)', async () => {
      const request = makeRequest('http://localhost:3000/api/regions?countryId=invalid');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toEqual({
        error: 'Bad Request',
        message: 'countryId must be a valid integer'
      });
      expect(mockPrisma.region.findMany).not.toHaveBeenCalled();
    });

    it('returns 404 when country does not exist', async () => {
      mockPrisma.country.findUnique.mockResolvedValue(null);

      const request = makeRequest('http://localhost:3000/api/regions?countryId=999');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data).toEqual({
        error: 'Not Found',
        message: 'Country not found'
      });
      expect(mockPrisma.country.findUnique).toHaveBeenCalledWith({
        where: { id: 999 }
      });
    });

    it('returns 500 when database error occurs', async () => {
      const dbError = new Error('Database connection failed');
      mockPrisma.country.findUnique.mockRejectedValue(dbError);

      const request = makeRequest('http://localhost:3000/api/regions?countryId=33');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data).toEqual({
        error: 'Internal Server Error',
        message: 'An error occurred while fetching regions'
      });
    });
  });
});
