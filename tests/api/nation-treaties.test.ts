/** @jest-environment node */

// Nation Treaty API CRUD Operations Test Suite
import { NextRequest } from 'next/server'
import { GET, POST, PUT, DELETE } from '../../src/app/api/nation-treaties/route'
import { GET as GET_SINGLE, PUT as PUT_SINGLE, DELETE as DELETE_SINGLE } from '../../src/app/api/nation-treaties/[id]/route'
import { GET as GET_MEMBERS, POST as POST_MEMBER, DELETE as DELETE_MEMBER } from '../../src/app/api/nation-treaties/[id]/members/route'
import { GET as GET_ENVOYS, POST as POST_ENVOY, DELETE as DELETE_ENVOY } from '../../src/app/api/nation-treaties/[id]/envoys/route'

// Mock dependencies
jest.mock('next-auth/next')
jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    nationTreaty: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn()
    },
    nationTreatyMember: {
      findMany: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      count: jest.fn()
    },
    nationTreatyEnvoy: {
      findMany: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      count: jest.fn()
    },
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn()
    },
    treaty: {
      findMany: jest.fn(),
      count: jest.fn()
    },
    treatyType: {
      findMany: jest.fn()
    },
    $transaction: jest.fn()
  },
}))

jest.mock('../../src/lib/audit', () => ({
  logAudit: jest.fn()
}))

jest.mock('../../src/lib/validation', () => ({
  validateNationTreatyCreate: jest.fn(),
  validateNationTreatyQuery: jest.fn(),
  validateNationTreatyUpdate: jest.fn(),
  NationTreatyError: class MockNationTreatyError extends Error {
    constructor(message: string, public code?: string) {
      super(message);
      this.name = 'NationTreatyError';
    }
  },
  ValidationError: class MockValidationError extends Error {
    constructor(message: string, public field?: string) {
      super(message);
      this.name = 'ValidationError';
    }
  }
}))

// Import mocked modules
import { getServerSession } from 'next-auth/next'
import { prisma } from '../../src/lib/prisma'
import { logAudit } from '../../src/lib/audit'
import {
  validateNationTreatyCreate,
  validateNationTreatyQuery,
  validateNationTreatyUpdate,
  NationTreatyError,
  ValidationError
} from '../../src/lib/validation'

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>
const mockPrisma = prisma as jest.Mocked<typeof prisma>

// Test data
const mockUser = {
  id: 'cmfbrh33h002appsj5bf3uh1j',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'ADMIN'
}

const mockSession = {
  user: mockUser
}

const mockNationTreaty = {
  id: 'test-nation-treaty-id',
  name: 'United Tribe of Aotearoa',
  officialName: 'United Tribe of Aotearoa - New World Alliance Treaty',
  status: 'ACTIVE',
  description: 'Treaty between the United Tribe of Aotearoa and New World Alliance',
  contactEmail: '<EMAIL>',
  contactPhone: '+64-9-123-4567',
  contactAddress: '123 Treaty Street, Auckland, New Zealand',
  website: 'https://aotearoa.nz',
  emergencyContactName: 'Emergency Coordinator',
  emergencyContactPhone: '+64-21-123-4567',
  notes: 'Primary diplomatic treaty',
  documentPath: '/documents/aotearoa-treaty.pdf',
  isDeleted: false,
  metadata: { region: 'Oceania', treatyDate: '2024-01-01' },
  createdAt: new Date(),
  updatedAt: new Date()
}

const mockNationTreatyMember = {
  id: 'test-member-id',
  nationTreatyId: 'test-nation-treaty-id',
  userId: 'cmfbrh33h002appsj5bf3uh1j',
  status: 'ACTIVE',
  joinedAt: new Date(),
  user: mockUser
}

const mockNationTreatyEnvoy = {
  id: 'test-envoy-id',
  nationTreatyId: 'test-nation-treaty-id',
  userId: 'cmfbrh33h002appsj5bf3uh1j',
  title: 'Ambassador',
  status: 'ACTIVE',
  appointedAt: new Date(),
  user: mockUser
}

describe('Nation Treaty API - CRUD Operations', () => {
  beforeEach(() => {
    jest.clearAllMocks()

    // Setup default mock implementations
    mockGetServerSession.mockResolvedValue(mockSession)
    ;(logAudit as jest.MockedFunction<typeof logAudit>).mockResolvedValue()
    ;(validateNationTreatyCreate as jest.MockedFunction<typeof validateNationTreatyCreate>).mockReturnValue({
      ...mockNationTreaty,
      status: 'ACTIVE' as const
    })
    ;(validateNationTreatyQuery as jest.MockedFunction<typeof validateNationTreatyQuery>).mockReturnValue({
      page: 1,
      limit: 10,
      search: undefined,
      status: undefined,
      sortBy: 'createdAt',
      sortOrder: 'desc',
      contactEmail: undefined,
      country: undefined
    })

    // Setup default Prisma transaction mock
    mockPrisma.$transaction.mockImplementation((callback) => {
      return callback(mockPrisma as any)
    })
  })

  describe('GET /api/nation-treaties', () => {
    it('should return paginated list of nation treaties', async () => {
      const mockTreaties = [mockNationTreaty]
      
      mockPrisma.nationTreaty.findMany.mockResolvedValue(mockTreaties)
      mockPrisma.nationTreaty.count.mockResolvedValue(1)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties?page=1&limit=10')
      const response = await GET(request)
      const data = await response.json()
      
      expect(response.status).toBe(200)
      expect(data).toEqual({
        success: true,
        data: mockTreaties,
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1
        }
      })
      expect(logAudit).toHaveBeenCalledWith(
        mockUser.id,
        'nation_treaty_read',
        expect.objectContaining({ page: 1, limit: 10 })
      )
    })

    it('should filter nation treaties by status', async () => {
      const mockTreaties = [mockNationTreaty]
      
      mockPrisma.nationTreaty.findMany.mockResolvedValue(mockTreaties)
      mockPrisma.nationTreaty.count.mockResolvedValue(1)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties?status=ACTIVE')
      const response = await GET(request)
      const data = await response.json()
      
      expect(response.status).toBe(200)
      expect(mockPrisma.nationTreaty.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: 'ACTIVE'
          })
        })
      )
    })

    it('should search nation treaties by name', async () => {
      const mockTreaties = [mockNationTreaty]
      
      mockPrisma.nationTreaty.findMany.mockResolvedValue(mockTreaties)
      mockPrisma.nationTreaty.count.mockResolvedValue(1)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties?search=Aotearoa')
      const response = await GET(request)
      const data = await response.json()
      
      expect(response.status).toBe(200)
      expect(mockPrisma.nationTreaty.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: [
              { name: { contains: 'Aotearoa' } },
              { officialName: { contains: 'Aotearoa' } },
              { description: { contains: 'Aotearoa' } }
            ]
          })
        })
      )
    })

    it('should handle authentication errors', async () => {
      mockGetServerSession.mockResolvedValue(null)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties')
      const response = await GET(request)
      const data = await response.json()
      
      expect(response.status).toBe(401)
      expect(data).toEqual({
        success: false,
        error: 'Unauthorized'
      })
    })

    it('should handle database errors', async () => {
      mockPrisma.nationTreaty.findMany.mockRejectedValue(new Error('Database error'))
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties')
      const response = await GET(request)
      const data = await response.json()
      
      expect(response.status).toBe(500)
      expect(data).toEqual({
        success: false,
        error: 'Failed to fetch nation treaties'
      })
    })
  })

  describe('POST /api/nation-treaties', () => {
    it('should create a new nation treaty', async () => {
      const requestBody = {
        name: 'United Tribe of Aotearoa',
        officialName: 'United Tribe of Aotearoa - New World Alliance Treaty',
        status: 'ACTIVE',
        description: 'Treaty between the United Tribe of Aotearoa and New World Alliance',
        contactEmail: '<EMAIL>',
        contactPhone: '+64-9-123-4567',
        contactAddress: '123 Treaty Street, Auckland, New Zealand'
      }
      
      mockPrisma.nationTreaty.create.mockResolvedValue(mockNationTreaty)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      })
      const response = await POST(request)
      const data = await response.json()
      
      expect(response.status).toBe(201)
      expect(data).toEqual({
        success: true,
        data: mockNationTreaty
      })
      expect(logAudit).toHaveBeenCalledWith(
        mockUser.id,
        'nation_treaty_create',
        expect.objectContaining({ name: 'United Tribe of Aotearoa' })
      )
    })

    it('should validate required fields', async () => {
      const requestBody = {
        name: '',
        officialName: 'Test Treaty'
      }
      
      ;(validateNationTreatyCreate as jest.MockedFunction<typeof validateNationTreatyCreate>).mockImplementation(() => {
        throw new ValidationError('Name is required', 'name')
      })
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      })
      const response = await POST(request)
      const data = await response.json()
      
      expect(response.status).toBe(400)
      expect(data).toEqual({
        success: false,
        error: expect.stringContaining('validation')
      })
    })

    it('should handle duplicate treaty names', async () => {
      const requestBody = {
        name: 'United Tribe of Aotearoa',
        officialName: 'United Tribe of Aotearoa - New World Alliance Treaty'
      }
      
      mockPrisma.nationTreaty.create.mockRejectedValue({
        code: 'P2002',
        meta: { target: ['name'] }
      })
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      })
      const response = await POST(request)
      const data = await response.json()
      
      expect(response.status).toBe(409)
      expect(data).toEqual({
        success: false,
        error: 'A nation treaty with this name already exists'
      })
    })
  })

  describe('GET /api/nation-treaties/[id]', () => {
    it('should return a single nation treaty by ID', async () => {
      mockPrisma.nationTreaty.findUnique.mockResolvedValue(mockNationTreaty)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties/test-nation-treaty-id')
      const response = await GET_SINGLE(request, { params: { id: 'test-nation-treaty-id' } })
      const data = await response.json()
      
      expect(response.status).toBe(200)
      expect(data).toEqual({
        success: true,
        data: mockNationTreaty
      })
    })

    it('should return 404 for non-existent treaty', async () => {
      mockPrisma.nationTreaty.findUnique.mockResolvedValue(null)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties/non-existent-id')
      const response = await GET_SINGLE(request, { params: { id: 'non-existent-id' } })
      const data = await response.json()
      
      expect(response.status).toBe(404)
      expect(data).toEqual({
        success: false,
        error: 'Nation treaty not found'
      })
    })
  })

  describe('PUT /api/nation-treaties/[id]', () => {
    it('should update a nation treaty', async () => {
      const updatedTreaty = { ...mockNationTreaty, status: 'INACTIVE' }
      const requestBody = { status: 'INACTIVE' }
      
      mockPrisma.nationTreaty.findUnique.mockResolvedValue(mockNationTreaty)
      mockPrisma.nationTreaty.update.mockResolvedValue(updatedTreaty)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties/test-nation-treaty-id', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      })
      const response = await PUT_SINGLE(request, { params: { id: 'test-nation-treaty-id' } })
      const data = await response.json()
      
      expect(response.status).toBe(200)
      expect(data).toEqual({
        success: true,
        data: updatedTreaty
      })
      expect(logAudit).toHaveBeenCalledWith(
        mockUser.id,
        'nation_treaty_update',
        expect.objectContaining({ id: 'test-nation-treaty-id', status: 'INACTIVE' })
      )
    })

    it('should prevent updating treaties with invalid status transitions', async () => {
      const requestBody = { status: 'ACTIVE' }
      
      mockPrisma.nationTreaty.findUnique.mockResolvedValue({
        ...mockNationTreaty,
        status: 'TERMINATED'
      })
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties/test-nation-treaty-id', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      })
      const response = await PUT_SINGLE(request, { params: { id: 'test-nation-treaty-id' } })
      const data = await response.json()
      
      expect(response.status).toBe(400)
      expect(data).toEqual({
        success: false,
        error: 'Cannot reactivate a terminated treaty'
      })
    })
  })

  describe('DELETE /api/nation-treaties/[id]', () => {
    it('should soft delete a nation treaty', async () => {
      const deletedTreaty = { ...mockNationTreaty, isDeleted: true, deletedAt: new Date() }
      
      mockPrisma.nationTreaty.findUnique.mockResolvedValue(mockNationTreaty)
      mockPrisma.nationTreaty.update.mockResolvedValue(deletedTreaty)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties/test-nation-treaty-id', {
        method: 'DELETE'
      })
      const response = await DELETE_SINGLE(request, { params: { id: 'test-nation-treaty-id' } })
      const data = await response.json()
      
      expect(response.status).toBe(200)
      expect(data).toEqual({
        success: true,
        data: deletedTreaty
      })
      expect(logAudit).toHaveBeenCalledWith(
        mockUser.id,
        'nation_treaty_delete',
        expect.objectContaining({ id: 'test-nation-treaty-id' })
      )
    })

    it('should prevent deleting treaties with active members or envoys', async () => {
      mockPrisma.nationTreaty.findUnique.mockResolvedValue(mockNationTreaty)
      mockPrisma.nationTreatyMember.count.mockResolvedValue(5)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties/test-nation-treaty-id', {
        method: 'DELETE'
      })
      const response = await DELETE_SINGLE(request, { params: { id: 'test-nation-treaty-id' } })
      const data = await response.json()
      
      expect(response.status).toBe(400)
      expect(data).toEqual({
        success: false,
        error: 'Cannot delete nation treaty with active members or envoys'
      })
    })
  })

  describe('Member Management', () => {
    describe('GET /api/nation-treaties/[id]/members', () => {
      it('should return list of treaty members', async () => {
        const mockMembers = [mockNationTreatyMember]
        
        mockPrisma.nationTreatyMember.findMany.mockResolvedValue(mockMembers)
        mockPrisma.nationTreatyMember.count.mockResolvedValue(1)
        
        const request = new NextRequest('http://localhost:3000/api/nation-treaties/test-nation-treaty-id/members')
        const response = await GET_MEMBERS(request, { params: { id: 'test-nation-treaty-id' } })
        const data = await response.json()
        
        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.data).toEqual(mockMembers)
      })
    })

    describe('POST /api/nation-treaties/[id]/members', () => {
      it('should add a member to nation treaty', async () => {
        const requestBody = { userId: 'cmfbrh33h002appsj5bf3uh1j' }
        
        mockPrisma.nationTreatyMember.create.mockResolvedValue(mockNationTreatyMember)
        
        const request = new NextRequest('http://localhost:3000/api/nation-treaties/test-nation-treaty-id/members', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestBody)
        })
        const response = await POST_MEMBER(request, { params: { id: 'test-nation-treaty-id' } })
        const data = await response.json()
        
        expect(response.status).toBe(201)
        expect(data).toEqual({
          success: true,
          data: mockNationTreatyMember
        })
      })

      it('should prevent duplicate membership', async () => {
        const requestBody = { userId: 'cmfbrh33h002appsj5bf3uh1j' }
        
        mockPrisma.nationTreatyMember.create.mockRejectedValue({
          code: 'P2002',
          meta: { target: ['nationTreatyId', 'userId'] }
        })
        
        const request = new NextRequest('http://localhost:3000/api/nation-treaties/test-nation-treaty-id/members', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestBody)
        })
        const response = await POST_MEMBER(request, { params: { id: 'test-nation-treaty-id' } })
        const data = await response.json()
        
        expect(response.status).toBe(409)
        expect(data).toEqual({
          success: false,
          error: 'User is already a member of this nation treaty'
        })
      })
    })

    describe('DELETE /api/nation-treaties/[id]/members/[memberId]', () => {
      it('should remove a member from nation treaty', async () => {
        mockPrisma.nationTreatyMember.delete.mockResolvedValue(mockNationTreatyMember)
        
        const request = new NextRequest('http://localhost:3000/api/nation-treaties/test-nation-treaty-id/members/test-member-id', {
          method: 'DELETE'
        })
        const response = await DELETE_MEMBER(request, { 
          params: { id: 'test-nation-treaty-id', memberId: 'test-member-id' } 
        })
        const data = await response.json()
        
        expect(response.status).toBe(200)
        expect(data).toEqual({
          success: true,
          data: mockNationTreatyMember
        })
      })
    })
  })

  describe('Envoy Management', () => {
    describe('GET /api/nation-treaties/[id]/envoys', () => {
      it('should return list of treaty envoys', async () => {
        const mockEnvoys = [mockNationTreatyEnvoy]
        
        mockPrisma.nationTreatyEnvoy.findMany.mockResolvedValue(mockEnvoys)
        mockPrisma.nationTreatyEnvoy.count.mockResolvedValue(1)
        
        const request = new NextRequest('http://localhost:3000/api/nation-treaties/test-nation-treaty-id/envoys')
        const response = await GET_ENVOYS(request, { params: { id: 'test-nation-treaty-id' } })
        const data = await response.json()
        
        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.data).toEqual(mockEnvoys)
      })
    })

    describe('POST /api/nation-treaties/[id]/envoys', () => {
      it('should add an envoy to nation treaty', async () => {
        const requestBody = { 
          userId: 'cmfbrh33h002appsj5bf3uh1j',
          title: 'Ambassador'
        }
        
        mockPrisma.nationTreatyEnvoy.create.mockResolvedValue(mockNationTreatyEnvoy)
        
        const request = new NextRequest('http://localhost:3000/api/nation-treaties/test-nation-treaty-id/envoys', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestBody)
        })
        const response = await POST_ENVOY(request, { params: { id: 'test-nation-treaty-id' } })
        const data = await response.json()
        
        expect(response.status).toBe(201)
        expect(data).toEqual({
          success: true,
          data: mockNationTreatyEnvoy
        })
      })
    })

    describe('DELETE /api/nation-treaties/[id]/envoys/[envoyId]', () => {
      it('should remove an envoy from nation treaty', async () => {
        mockPrisma.nationTreatyEnvoy.delete.mockResolvedValue(mockNationTreatyEnvoy)
        
        const request = new NextRequest('http://localhost:3000/api/nation-treaties/test-nation-treaty-id/envoys/test-envoy-id', {
          method: 'DELETE'
        })
        const response = await DELETE_ENVOY(request, { 
          params: { id: 'test-nation-treaty-id', envoyId: 'test-envoy-id' } 
        })
        const data = await response.json()
        
        expect(response.status).toBe(200)
        expect(data).toEqual({
          success: true,
          data: mockNationTreatyEnvoy
        })
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle authentication errors consistently', async () => {
      mockGetServerSession.mockResolvedValue(null)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties')
      const response = await GET(request)
      const data = await response.json()
      
      expect(response.status).toBe(401)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Unauthorized')
    })

    it('should handle authorization errors for non-admin users', async () => {
      const nonAdminSession = {
        user: { ...mockUser, role: 'USER' }
      }
      mockGetServerSession.mockResolvedValue(nonAdminSession)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: 'Test Treaty' })
      })
      const response = await POST(request)
      const data = await response.json()
      
      expect(response.status).toBe(403)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Forbidden')
    })

    it('should handle validation errors consistently', async () => {
      ;(validateNationTreatyCreate as jest.MockedFunction<typeof validateNationTreatyCreate>).mockImplementation(() => {
        throw new ValidationError('Validation failed', 'name')
      })
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: '' })
      })
      const response = await POST(request)
      const data = await response.json()
      
      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('validation')
    })

    it('should handle database transaction errors', async () => {
      mockPrisma.$transaction.mockRejectedValue(new Error('Transaction failed'))
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: 'Test Treaty' })
      })
      const response = await POST(request)
      const data = await response.json()
      
      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Failed to create nation treaty')
    })
  })

  describe('Performance and Edge Cases', () => {
    it('should handle large pagination requests', async () => {
      mockPrisma.nationTreaty.findMany.mockResolvedValue([])
      mockPrisma.nationTreaty.count.mockResolvedValue(1000)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties?page=50&limit=100')
      const response = await GET(request)
      const data = await response.json()
      
      expect(response.status).toBe(200)
      expect(data.pagination.page).toBe(50)
      expect(data.pagination.limit).toBe(100)
    })

    it('should handle complex search queries', async () => {
      mockPrisma.nationTreaty.findMany.mockResolvedValue([mockNationTreaty])
      mockPrisma.nationTreaty.count.mockResolvedValue(1)
      
      const request = new NextRequest('http://localhost:3000/api/nation-treaties?search=Aotearoa&status=ACTIVE&contactEmail=aotearoa.nz')
      const response = await GET(request)
      const data = await response.json()
      
      expect(response.status).toBe(200)
      expect(mockPrisma.nationTreaty.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            AND: [
              {
                OR: [
                  { name: { contains: 'Aotearoa' } },
                  { officialName: { contains: 'Aotearoa' } },
                  { description: { contains: 'Aotearoa' } }
                ]
              },
              { status: 'ACTIVE' },
              { contactEmail: { contains: 'aotearoa.nz' } }
            ]
          })
        })
      )
    })

    it('should handle concurrent operations', async () => {
      const createPromise1 = new Promise(resolve => {
        setTimeout(resolve, 100)
      })
      const createPromise2 = new Promise(resolve => {
        setTimeout(resolve, 50)
      })
      
      mockPrisma.nationTreaty.create.mockResolvedValueOnce(mockNationTreaty)
      mockPrisma.nationTreaty.create.mockResolvedValueOnce(mockNationTreaty)
      
      const request1 = new NextRequest('http://localhost:3000/api/nation-treaties', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: 'Treaty 1' })
      })
      const request2 = new NextRequest('http://localhost:3000/api/nation-treaties', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: 'Treaty 2' })
      })
      
      const [response1, response2] = await Promise.all([
        POST(request1),
        POST(request2)
      ])
      
      expect(response1.status).toBe(201)
      expect(response2.status).toBe(201)
    })
  })
})
