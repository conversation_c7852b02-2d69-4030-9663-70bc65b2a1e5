// Mock next/server to avoid Request dependency errors in test environment
jest.mock('next/server', () => ({
  NextResponse: {
    json: (body: any, init?: any) => ({ status: init?.status ?? 200, json: async () => body }),
  },
}));

import { NextRequest } from 'next/server';
import { GET, POST } from '../../src/app/api/users/[id]/treaty-numbers/route';
import { PUT as UPDATE_TN } from '../../src/app/api/users/[id]/treaty-numbers/[treatyNumberId]/route';
import { prisma } from '../../src/lib/prisma';

jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    user: { findUnique: jest.fn() },
    treatyType: { findUnique: jest.fn() },
    userTreatyNumber: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
  },
}));

jest.mock('../../src/lib/middleware/require-auth', () => ({
  requireAuth: jest.fn().mockResolvedValue({ 
    userId: 'user-1', 
    email: '<EMAIL>', 
    name: 'Test User', 
    roles: ['admin'],
    permissions: []
  }),
}));

jest.mock('../../src/lib/middleware/audit-logging', () => ({
  auditLogger: { logApiAccess: jest.fn() },
}));

function makeRequest(url: string, options: any = {}): NextRequest {
  return {
    url,
    method: options.method || 'GET',
    headers: { get: jest.fn().mockReturnValue('application/json') },
    json: jest.fn().mockResolvedValue(options.body || {}),
  } as unknown as NextRequest;
}

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('User Treaty Numbers API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('POST validates format and ensures treatyTypeId uniqueness per user', async () => {
    (mockPrisma.user.findUnique as jest.Mock).mockResolvedValue({ id: 'user-1' });

    // Invalid format first
    let req = makeRequest('http://localhost/api/users/user-1/treaty-numbers', {
      method: 'POST',
      body: { treatyNumber: 'B@D123456', treatyTypeId: 'tt-1' },
    });
    let res = await POST(req, { params: { id: 'user-1' } as any });
    expect(res.status).toBe(400);

    // Valid format but treaty type inactive
    (mockPrisma.treatyType.findUnique as jest.Mock).mockResolvedValue({ id: 'tt-1', isActive: false });
    req = makeRequest('http://localhost/api/users/user-1/treaty-numbers', {
      method: 'POST',
      body: { treatyNumber: 'ABC123456', treatyTypeId: 'tt-1' },
    });
    res = await POST(req, { params: { id: 'user-1' } as any });
    expect(res.status).toBe(404);

    // Active but duplicate for this type
    (mockPrisma.treatyType.findUnique as jest.Mock).mockResolvedValue({ id: 'tt-1', isActive: true });
    (mockPrisma.userTreatyNumber.findFirst as jest.Mock).mockResolvedValue({ id: 'dup' });
    req = makeRequest('http://localhost/api/users/user-1/treaty-numbers', {
      method: 'POST',
      body: { treatyNumber: 'ABC123456', treatyTypeId: 'tt-1' },
    });
    res = await POST(req, { params: { id: 'user-1' } as any });
    expect(res.status).toBe(409);
  });

  test('PUT validates format and uniqueness per treaty type', async () => {
    // Existing record
    (mockPrisma.userTreatyNumber.findFirst as jest.Mock).mockResolvedValue({ id: 'tn-1', treatyNumber: 'ABC123456', treatyType: { id: 'tt-1', name: 'Peace' }, isActive: true });

    // Invalid format
    let req = makeRequest('http://localhost/api/users/user-1/treaty-numbers/tn-1', {
      method: 'PUT',
      body: { treatyNumber: 'bad', treatyTypeId: 'tt-2' },
    });
    let res = await UPDATE_TN(req, { params: { id: 'user-1', treatyNumberId: 'tn-1' } as any });
    expect(res.status).toBe(400);

    // Valid format but treaty type inactive
    (mockPrisma.treatyType.findUnique as jest.Mock).mockResolvedValue({ id: 'tt-2', isActive: false });
    req = makeRequest('http://localhost/api/users/user-1/treaty-numbers/tn-1', {
      method: 'PUT',
      body: { treatyNumber: 'DEF123456', treatyTypeId: 'tt-2' },
    });
    res = await UPDATE_TN(req, { params: { id: 'user-1', treatyNumberId: 'tn-1' } as any });
    expect(res.status).toBe(404);

    // Active but duplicate for the type
    (mockPrisma.treatyType.findUnique as jest.Mock).mockResolvedValue({ id: 'tt-2', isActive: true });
    (mockPrisma.userTreatyNumber.findFirst as jest.Mock).mockResolvedValueOnce({ id: 'other' });
    req = makeRequest('http://localhost/api/users/user-1/treaty-numbers/tn-1', {
      method: 'PUT',
      body: { treatyNumber: 'DEF123456', treatyTypeId: 'tt-2' },
    });
    res = await UPDATE_TN(req, { params: { id: 'user-1', treatyNumberId: 'tn-1' } as any });
    expect(res.status).toBe(409);
  });
});

