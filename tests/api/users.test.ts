import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { GET, POST } from '../../src/app/api/users/route';
import { GET as getUserById, PUT as updateUser } from '../../src/app/api/users/[id]/route';
import { prisma } from '../../src/lib/prisma';

// Mock dependencies
jest.mock('next-auth/next');
jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    user: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    userProfile: {
      findFirst: jest.fn(),
    },
    country: {
      findFirst: jest.fn(),
    },
    city: {
      findFirst: jest.fn(),
    },
    region: {
      findFirst: jest.fn(),
    },
  },
}));

jest.mock('../../src/lib/user-audit-logger', () => ({
  userAuditLogger: {
    logUserCreation: jest.fn(),
    logUserUpdate: jest.fn(),
    getClientIP: jest.fn().mockReturnValue('127.0.0.1'),
    getUserAgent: jest.fn().mockReturnValue('test-agent'),
  },
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

// Mock session data
const mockSession = {
  user: {
    id: 'admin-123',
    email: '<EMAIL>',
    name: 'Admin User',
  },
};

// Mock data
const mockCountry = {
  id: 1,
  name: 'United States',
  code: 'US',
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockRegion = {
  id: 1,
  name: 'California',
  code: 'CA',
  countryId: 1,
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockCity = {
  id: 1,
  name: 'Los Angeles',
  countryId: 1,
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockUser = {
  id: 'user-123',
  name: 'John Doe',
  email: '<EMAIL>',
  createdAt: new Date(),
  updatedAt: new Date(),
  profile: {
    firstName: 'John',
    lastName: 'Doe',
    personalEmail: '<EMAIL>',
    nwaEmail: '<EMAIL>',
    dateOfBirth: new Date('1980-01-01'),
    phone: '******-0123',
    mobile: '******-0456',
    streetAddress1: '123 Main Street',
    streetAddress2: 'Apt 4B',
    town: 'Springfield',
    postalCode: '90210',
    regionId: 1,
    regionText: 'California',
    countryId: 1,
    cityId: 1,
    peaceAmbassadorNumber: 'PA-001',
    bio: 'Test bio',
    titleId: 'title-1',
    country: mockCountry,
    city: mockCity,
    region: mockRegion,
  },
  userPositions: [{
    positionId: 'pos-1',
    position: { title: 'Ambassador' }
  }],
};

const mockLegacyUser = {
  id: 'user-124',
  name: 'Jane Smith',
  email: '<EMAIL>',
  createdAt: new Date(),
  updatedAt: new Date(),
  profile: {
    firstName: 'Jane',
    lastName: 'Smith',
    streetAddress1: null,
    streetAddress2: null,
    // Legacy data would come from a migration or manual mapping
    town: 'Springfield',
    postalCode: '54321',
    regionText: 'Texas',
    countryId: 1,
    country: mockCountry,
  },
  userPositions: [],
};

function makeRequest(url: string, options: any = {}): NextRequest {
  return {
    url,
    method: options.method || 'GET',
    headers: {
      get: jest.fn().mockReturnValue('application/json'),
    },
    json: jest.fn().mockResolvedValue(options.body || {}),
  } as unknown as NextRequest;
}

describe('User API Endpoints', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetServerSession.mockResolvedValue(mockSession);
  });

  describe('POST /api/users - User Creation with New Address Structure', () => {
    beforeEach(() => {
      mockPrisma.user.findUnique.mockResolvedValue(null); // No existing user
      mockPrisma.user.findFirst.mockResolvedValue(null); // No duplicate name
      mockPrisma.userProfile.findFirst.mockResolvedValue(null); // No duplicate phone/email
    });

    test('creates user with new address field structure', async () => {
      const userData = {
        firstName: 'John',
        surname: 'Doe',
        email: '<EMAIL>',
        streetAddress1: '123 Main Street',
        streetAddress2: 'Apt 4B',
        town: 'Springfield',
        postalCode: '90210',
        regionId: '1',
        regionText: 'California',
        country: 'United States',
        city: 'Los Angeles',
        phone: '******-0123',
        mobile: '******-0456',
      };

      mockPrisma.country.findFirst.mockResolvedValue(mockCountry);
      mockPrisma.city.findFirst.mockResolvedValue(mockCity);
      mockPrisma.region.findFirst.mockResolvedValue(mockRegion);
      mockPrisma.user.create.mockResolvedValue({
        id: 'new-user-id',
        ...userData,
        createdAt: new Date(),
      } as any);

      const request = makeRequest('/api/users', {
        method: 'POST',
        body: userData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('User created successfully');
      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: {
          name: 'John Doe',
          email: '<EMAIL>',
          profile: {
            create: expect.objectContaining({
              firstName: 'John',
              lastName: 'Doe',
              streetAddress1: '123 Main Street',
              streetAddress2: 'Apt 4B',
              town: 'Springfield',
              postalCode: '90210',
              regionId: 1,
              regionText: 'California',
              countryId: 1,
              cityId: 1,
            }),
          },
        },
      });
    });

    test('handles backward compatibility with legacy streetAddress field', async () => {
      const userData = {
        firstName: 'Jane',
        surname: 'Smith',
        email: '<EMAIL>',
        streetAddress: '456 Legacy Street', // Old field format
        town: 'Springfield',
        country: 'United States',
      };

      mockPrisma.country.findFirst.mockResolvedValue(mockCountry);
      mockPrisma.user.create.mockResolvedValue({
        id: 'new-user-id',
        ...userData,
        createdAt: new Date(),
      } as any);

      const request = makeRequest('/api/users', {
        method: 'POST',
        body: userData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: {
          name: 'Jane Smith',
          email: '<EMAIL>',
          profile: {
            create: expect.objectContaining({
              streetAddress1: '456 Legacy Street', // Mapped from legacy field
              streetAddress2: undefined,
            }),
          },
        },
      });
    });

    test('validates regionId against country', async () => {
      const userData = {
        firstName: 'John',
        surname: 'Doe',
        email: '<EMAIL>',
        regionId: '999', // Invalid region
        country: 'United States',
      };

      mockPrisma.country.findFirst.mockResolvedValue(mockCountry);
      mockPrisma.region.findFirst.mockResolvedValue(null); // Invalid region

      const request = makeRequest('/api/users', {
        method: 'POST',
        body: userData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid region for the selected country');
    });

    test('validates postal code format (integration point)', async () => {
      // Note: This test ensures the API accepts postal codes for validation
      // The actual validation would be done client-side or with additional middleware
      const userData = {
        firstName: 'John',
        surname: 'Doe',
        email: '<EMAIL>',
        postalCode: '12345-6789', // US format
        country: 'United States',
      };

      mockPrisma.country.findFirst.mockResolvedValue(mockCountry);
      mockPrisma.user.create.mockResolvedValue({
        id: 'new-user-id',
        ...userData,
        createdAt: new Date(),
      } as any);

      const request = makeRequest('/api/users', {
        method: 'POST',
        body: userData,
      });

      const response = await POST(request);

      expect(response.status).toBe(200);
      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          profile: {
            create: expect.objectContaining({
              postalCode: '12345-6789',
            }),
          },
        }),
      });
    });

    test('handles mutual exclusion of regionId and regionText', async () => {
      const userData = {
        firstName: 'John',
        surname: 'Doe',
        email: '<EMAIL>',
        regionId: '1',
        regionText: 'Custom Region', // Both provided - should be fine in creation
        country: 'United States',
      };

      mockPrisma.country.findFirst.mockResolvedValue(mockCountry);
      mockPrisma.region.findFirst.mockResolvedValue(mockRegion);
      mockPrisma.user.create.mockResolvedValue({
        id: 'new-user-id',
        ...userData,
        createdAt: new Date(),
      } as any);

      const request = makeRequest('/api/users', {
        method: 'POST',
        body: userData,
      });

      const response = await POST(request);

      expect(response.status).toBe(200);
      // In creation, both can be provided (regionId takes precedence in database)
    });
  });

  describe('GET /api/users/[id] - Individual User Retrieval', () => {
    test('returns user with new address field structure', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any);

      const response = await getUserById(
        makeRequest('/api/users/user-123'),
        { params: Promise.resolve({ id: 'user-123' }) }
      );
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.user).toMatchObject({
        id: 'user-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        personalEmail: '<EMAIL>',
        nwaEmail: '<EMAIL>',
        dob: '1980-01-01',
        phone: '******-0123',
        mobile: '******-0456',
        streetAddress1: '123 Main Street',
        streetAddress2: 'Apt 4B',
        town: 'Springfield',
        postalCode: '90210',
        regionId: '1',
        regionText: 'California',
        country: 'United States',
        city: 'Los Angeles',
        streetAddress: '123 Main Street', // Backward compatibility mapping
        peaceAmbassadorNumber: 'PA-001',
        bio: 'Test bio',
        titleId: 'title-1',
        positionId: 'pos-1',
        position: 'Ambassador',
        createdAt: mockUser.createdAt,
        updatedAt: mockUser.updatedAt,
      });
    });

    test('handles backward compatibility with legacy address data', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(mockLegacyUser as any);

      const response = await getUserById(
        makeRequest('/api/users/user-124'),
        { params: Promise.resolve({ id: 'user-124' }) }
      );
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.user).toMatchObject({
        firstName: 'Jane',
        lastName: 'Smith',
        streetAddress1: null,
        streetAddress2: null,
        town: 'Springfield',
        postalCode: '54321',
        regionText: 'Texas',
        streetAddress: '', // Empty since streetAddress1 is null
      });
    });

    test('returns 404 for non-existent user', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null);

      const response = await getUserById(
        makeRequest('/api/users/non-existent'),
        { params: Promise.resolve({ id: 'non-existent' }) }
      );
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('User not found');
    });
  });

  describe('PUT /api/users/[id] - User Updates with New Address Structure', () => {
    beforeEach(() => {
      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any);
    });

    test('updates user with new address field structure', async () => {
      const updateData = {
        streetAddress1: '456 New Street',
        streetAddress2: 'Suite 200',
        town: 'New Springfield',
        postalCode: '54321',
        regionText: 'Updated Region',
        country: 'United States',
      };

      mockPrisma.country.findFirst.mockResolvedValue(mockCountry);
      mockPrisma.user.update.mockResolvedValue({
        ...mockUser,
        profile: { ...mockUser.profile, ...updateData },
      } as any);

      const request = makeRequest('/api/users/user-123', {
        method: 'PUT',
        body: updateData,
      });

      const response = await updateUser(
        request,
        { params: Promise.resolve({ id: 'user-123' }) }
      );
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('User updated successfully');
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        data: {
          profile: {
            update: expect.objectContaining({
              streetAddress1: '456 New Street',
              streetAddress2: 'Suite 200',
              town: 'New Springfield',
              postalCode: '54321',
              regionText: 'Updated Region',
              countryId: 1,
            }),
          },
        },
        include: expect.any(Object),
      });
    });

    test('handles backward compatibility when updating with legacy streetAddress', async () => {
      const updateData = {
        streetAddress: '789 Legacy Updated Street',
      };

      mockPrisma.user.update.mockResolvedValue({
        ...mockUser,
        profile: { ...mockUser.profile, streetAddress1: '789 Legacy Updated Street' },
      } as any);

      const request = makeRequest('/api/users/user-123', {
        method: 'PUT',
        body: updateData,
      });

      const response = await updateUser(
        request,
        { params: Promise.resolve({ id: 'user-123' }) }
      );

      expect(response.status).toBe(200);
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        data: {
          profile: {
            update: expect.objectContaining({
              streetAddress1: '789 Legacy Updated Street', // Mapped from legacy field
            }),
          },
        },
        include: expect.any(Object),
      });
    });

    test('validates regionId/regionText mutual exclusion', async () => {
      const updateData = {
        regionId: '1',
        regionText: 'Custom Region Text',
        country: 'United States',
      };

      mockPrisma.country.findFirst.mockResolvedValue(mockCountry);

      const request = makeRequest('/api/users/user-123', {
        method: 'PUT',
        body: updateData,
      });

      const response = await updateUser(
        request,
        { params: Promise.resolve({ id: 'user-123' }) }
      );
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Cannot specify both regionId and regionText');
    });

    test('clears region selection when country changes', async () => {
      const updateData = {
        country: 'Canada', // Different country
      };

      const canadaCountry = { id: 2, name: 'Canada', code: 'CA' };
      mockPrisma.country.findFirst.mockResolvedValue(canadaCountry);
      mockPrisma.user.update.mockResolvedValue({
        ...mockUser,
        profile: { ...mockUser.profile, countryId: 2, regionId: null, regionText: null },
      } as any);

      const request = makeRequest('/api/users/user-123', {
        method: 'PUT',
        body: updateData,
      });

      const response = await updateUser(
        request,
        { params: Promise.resolve({ id: 'user-123' }) }
      );

      expect(response.status).toBe(200);
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        data: {
          profile: {
            update: expect.objectContaining({
              countryId: 2,
            }),
          },
        },
        include: expect.any(Object),
      });
    });

    test('validates address field requirements based on country rules', async () => {
      // This test ensures the API structure supports country-specific validation
      // Actual validation rules would be implemented based on CountryAddressFormat
      const updateData = {
        streetAddress1: '', // Empty required field
        country: 'United States',
      };

      mockPrisma.country.findFirst.mockResolvedValue(mockCountry);
      mockPrisma.user.update.mockResolvedValue({
        ...mockUser,
        profile: { ...mockUser.profile, streetAddress1: '' },
      } as any);

      const request = makeRequest('/api/users/user-123', {
        method: 'PUT',
        body: updateData,
      });

      const response = await updateUser(
        request,
        { params: Promise.resolve({ id: 'user-123' }) }
      );

      // API accepts the update - validation would be done at form level
      expect(response.status).toBe(200);
    });

    test('returns 404 for non-existent user', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null);

      const request = makeRequest('/api/users/non-existent', {
        method: 'PUT',
        body: { firstName: 'Test' },
      });

      const response = await updateUser(
        request,
        { params: Promise.resolve({ id: 'non-existent' }) }
      );
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('User not found');
    });
  });

  describe('Data Migration Edge Cases', () => {
    test('preserves address data integrity during updates', async () => {
      const userWithPartialAddress = {
        ...mockUser,
        profile: {
          ...mockUser.profile,
          streetAddress1: '123 Main St',
          streetAddress2: null,
          town: 'Springfield',
          regionText: null,
          regionId: 1,
        },
      };

      mockPrisma.user.findUnique.mockResolvedValue(userWithPartialAddress as any);
      mockPrisma.user.update.mockResolvedValue(userWithPartialAddress as any);

      const updateData = {
        streetAddress2: 'Apt 5C', // Adding second address line
      };

      const request = makeRequest('/api/users/user-123', {
        method: 'PUT',
        body: updateData,
      });

      const response = await updateUser(
        request,
        { params: Promise.resolve({ id: 'user-123' }) }
      );

      expect(response.status).toBe(200);
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        data: {
          profile: {
            update: expect.objectContaining({
              streetAddress2: 'Apt 5C',
            }),
          },
        },
        include: expect.any(Object),
      });
    });

    test('handles users with completely empty address data', async () => {
      const userWithNoAddress = {
        ...mockUser,
        profile: {
          ...mockUser.profile,
          streetAddress1: null,
          streetAddress2: null,
          town: null,
          postalCode: null,
          regionId: null,
          regionText: null,
          countryId: null,
          cityId: null,
        },
      };

      mockPrisma.user.findUnique.mockResolvedValue(userWithNoAddress as any);

      const response = await getUserById(
        makeRequest('/api/users/user-123'),
        { params: Promise.resolve({ id: 'user-123' }) }
      );
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.user).toMatchObject({
        streetAddress1: null,
        streetAddress2: null,
        town: null,
        postalCode: null,
        regionId: null,
        regionText: null,
        country: null,
        city: null,
        streetAddress: '', // Empty mapping for backward compatibility
      });
    });
  });

  describe('Authentication and Authorization', () => {
    test('returns 401 for unauthenticated requests', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = makeRequest('/api/users', {
        method: 'POST',
        body: { firstName: 'Test', surname: 'User', email: '<EMAIL>' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });
  });

  describe('Error Handling', () => {
    test('handles database errors gracefully', async () => {
      mockPrisma.user.create.mockRejectedValue(new Error('Database error'));

      const request = makeRequest('/api/users', {
        method: 'POST',
        body: {
          firstName: 'Test',
          surname: 'User',
          email: '<EMAIL>',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Internal server error');
    });

    test('validates required fields', async () => {
      const request = makeRequest('/api/users', {
        method: 'POST',
        body: {
          // Missing required fields
          email: '<EMAIL>',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Validation error');
    });
  });
});
