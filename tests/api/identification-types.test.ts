// Mock next/server to avoid Request dependency errors in test environment
jest.mock('next/server', () => ({
  NextResponse: {
    json: (body: any, init?: any) => ({ status: init?.status ?? 200, json: async () => body }),
  },
}));

import { NextRequest } from 'next/server';
import { GET, POST } from '../../src/app/api/identification-types/route';
import { DELETE as DELETE_TYPE, PUT as UPDATE_TYPE } from '../../src/app/api/identification-types/[id]/route';
import { prisma } from '../../src/lib/prisma';

// Mocks
jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    identificationType: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    userIdentification: {
      count: jest.fn(),
    },
  },
}));

jest.mock('../../src/lib/middleware/require-auth', () => ({
  requireAuth: jest.fn().mockResolvedValue({ user: { id: 'admin-1' }, response: null }),
}));

jest.mock('../../src/lib/middleware/audit-logging', () => ({
  auditLogger: {
    logApiAccess: jest.fn(),
  },
}));

function makeRequest(url: string, options: any = {}): NextRequest {
  return {
    url,
    method: options.method || 'GET',
    headers: {
      get: jest.fn().mockReturnValue('application/json'),
    },
    json: jest.fn().mockResolvedValue(options.body || {}),
  } as unknown as NextRequest;
}

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('Identification Types API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('GET /api/identification-types returns filtered list', async () => {
    (mockPrisma.identificationType.findMany as jest.Mock).mockResolvedValue([
      { id: 'id-1', name: 'Passport', category: 'government', isActive: true },
    ]);

    const req = makeRequest('http://localhost/api/identification-types?active=true&category=government');
    const res = await GET(req);
    const data = await res.json();

    expect(res.status).toBe(200);
    expect(data.identificationTypes).toHaveLength(1);
    expect(mockPrisma.identificationType.findMany).toHaveBeenCalled();
  });

  test('POST /api/identification-types validates category and duplicate name', async () => {
    // Invalid category
    let req = makeRequest('http://localhost/api/identification-types', {
      method: 'POST',
      body: { name: 'Foo', category: 'invalid' },
    });
    let res = await POST(req);
    expect(res.status).toBe(400);

    // Duplicate name
    (mockPrisma.identificationType.findUnique as jest.Mock).mockResolvedValue({ id: 'dup-1' });
    req = makeRequest('http://localhost/api/identification-types', {
      method: 'POST',
      body: { name: 'Passport', category: 'government' },
    });
    res = await POST(req);
    expect(res.status).toBe(409);
  });

  test('DELETE /api/identification-types/[id] prevents delete when in use', async () => {
    (mockPrisma.identificationType.findUnique as jest.Mock).mockResolvedValue({ id: 'id-1', name: 'Passport' });
    (mockPrisma.userIdentification.count as jest.Mock).mockResolvedValue(3);

    const req = makeRequest('http://localhost/api/identification-types/id-1', { method: 'DELETE' });
    const res = await DELETE_TYPE(req, { params: { id: 'id-1' } as any });
    const data = await res.json();

    expect(res.status).toBe(409);
    expect(data.code).toBe('ID_TYPE_IN_USE');
  });

  test('PUT /api/identification-types/[id] updates fields with validations', async () => {
    (mockPrisma.identificationType.findUnique as jest.Mock).mockResolvedValueOnce({ id: 'id-1', name: 'Passport' });
    (mockPrisma.identificationType.findUnique as jest.Mock).mockResolvedValueOnce(null); // no duplicate on new name
    (mockPrisma.identificationType.update as jest.Mock).mockResolvedValue({ id: 'id-1', name: 'Passport - New' });

    const req = makeRequest('http://localhost/api/identification-types/id-1', {
      method: 'PUT',
      body: { name: 'Passport - New', category: 'government' },
    });
    const res = await UPDATE_TYPE(req, { params: { id: 'id-1' } as any });
    expect(res.status).toBe(200);
  });
});

