import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { GET } from '../../src/app/api/users/search/route';
import { prisma } from '../../src/lib/prisma';

// Mock dependencies
jest.mock('next-auth/next');
jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    user: {
      findMany: jest.fn(),
      count: jest.fn(),
      findUnique: jest.fn(),
    },
    userRoles: {
      findMany: jest.fn(),
    },
  },
}));

jest.mock('../../src/lib/middleware/rate-limiting', () => ({
  predefinedRateLimiters: {
    api: jest.fn().mockResolvedValue({ 
      allowed: true, 
      headers: {} 
    }),
  },
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

// Mock session data
const mockAdminSession = {
  user: {
    id: 'admin-123',
    email: '<EMAIL>',
    name: 'Admin User',
  },
};

const mockAdminUser = {
  id: 'admin-123',
  userRoles: [
    {
      role: {
        name: 'admin',
      },
    },
  ],
};

const mockUsers = [
  {
    id: 'user-1',
    name: 'John Doe',
    email: '<EMAIL>',
    createdAt: new Date('2024-01-01'),
    profile: {
      firstName: 'John',
      lastName: 'Doe',
      nwaEmail: '<EMAIL>',
    },
  },
  {
    id: 'user-2', 
    name: 'Jane Smith',
    email: '<EMAIL>',
    createdAt: new Date('2024-01-02'),
    profile: {
      firstName: 'Jane',
      lastName: 'Smith',
      nwaEmail: '<EMAIL>',
    },
  },
  {
    id: 'user-3',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    createdAt: new Date('2024-01-03'),
    profile: {
      firstName: 'Bob',
      lastName: 'Johnson',
      nwaEmail: '<EMAIL>',
    },
  },
];

function makeRequest(url: string): NextRequest {
  return {
    url,
    method: 'GET',
    headers: {
      get: jest.fn().mockReturnValue('application/json'),
    },
  } as unknown as NextRequest;
}

describe('GET /api/users/search', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetServerSession.mockResolvedValue(mockAdminSession);
    mockPrisma.user.findUnique.mockResolvedValue(mockAdminUser as any);
  });

  describe('Authentication and Authorization', () => {
    test('returns 401 for unauthenticated requests', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = makeRequest('http://localhost/api/users/search?q=john');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    test('returns 403 for non-admin users', async () => {
      const nonAdminUser = {
        id: 'user-123',
        userRoles: [{ role: { name: 'member' } }],
      };
      mockPrisma.user.findUnique.mockResolvedValue(nonAdminUser as any);

      const request = makeRequest('http://localhost/api/users/search?q=john');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Forbidden');
    });

    test('returns 404 when admin user not found in database', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null);

      const request = makeRequest('http://localhost/api/users/search?q=john');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('User not found');
    });
  });

  describe('Search Functionality', () => {
    test('searches users by name', async () => {
      mockPrisma.user.findMany.mockResolvedValue([mockUsers[0]] as any);
      mockPrisma.user.count.mockResolvedValue(1);

      const request = makeRequest('http://localhost/api/users/search?q=john');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.users).toHaveLength(1);
      expect(data.users[0].name).toBe('John Doe');
      expect(data.users[0].email).toBe('<EMAIL>');
      
      expect(mockPrisma.user.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { name: { contains: 'john', mode: 'insensitive' } },
            { email: { contains: 'john', mode: 'insensitive' } },
            { profile: { nwaEmail: { contains: 'john', mode: 'insensitive' } } },
          ],
        },
        include: {
          profile: {
            select: {
              firstName: true,
              lastName: true,
              nwaEmail: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: 0,
        take: 10,
      });
    });

    test('searches users by email', async () => {
      mockPrisma.user.findMany.mockResolvedValue([mockUsers[1]] as any);
      mockPrisma.user.count.mockResolvedValue(1);

      const request = makeRequest('http://localhost/api/users/search?q=<EMAIL>');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.users).toHaveLength(1);
      expect(data.users[0].email).toBe('<EMAIL>');
    });

    test('searches users by NWA email', async () => {
      mockPrisma.user.findMany.mockResolvedValue([mockUsers[2]] as any);
      mockPrisma.user.count.mockResolvedValue(1);

      const request = makeRequest('http://localhost/api/users/search?q=<EMAIL>');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.users).toHaveLength(1);
      expect(data.users[0].profile.nwaEmail).toBe('<EMAIL>');
    });

    test('returns multiple search results', async () => {
      mockPrisma.user.findMany.mockResolvedValue(mockUsers as any);
      mockPrisma.user.count.mockResolvedValue(3);

      const request = makeRequest('http://localhost/api/users/search?q=@example.com');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.users).toHaveLength(3);
      expect(data.pagination.total).toBe(3);
    });

    test('returns empty results when no matches found', async () => {
      mockPrisma.user.findMany.mockResolvedValue([]);
      mockPrisma.user.count.mockResolvedValue(0);

      const request = makeRequest('http://localhost/api/users/search?q=nonexistent');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.users).toHaveLength(0);
      expect(data.pagination.total).toBe(0);
    });
  });

  describe('Pagination', () => {
    test('applies default pagination parameters', async () => {
      mockPrisma.user.findMany.mockResolvedValue(mockUsers as any);
      mockPrisma.user.count.mockResolvedValue(3);

      const request = makeRequest('http://localhost/api/users/search?q=test');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 3,
        pages: 1,
      });

      expect(mockPrisma.user.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 0,
          take: 10,
        })
      );
    });

    test('applies custom pagination parameters', async () => {
      mockPrisma.user.findMany.mockResolvedValue([mockUsers[0]] as any);
      mockPrisma.user.count.mockResolvedValue(25);

      const request = makeRequest('http://localhost/api/users/search?q=test&page=2&limit=5');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.pagination).toEqual({
        page: 2,
        limit: 5,
        total: 25,
        pages: 5,
      });

      expect(mockPrisma.user.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 5,
          take: 5,
        })
      );
    });

    test('validates pagination parameters', async () => {
      const request = makeRequest('http://localhost/api/users/search?q=test&page=0&limit=101');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Bad Request');
      expect(data.message).toBe('Invalid pagination parameters');
    });
  });

  describe('Query Validation', () => {
    test('requires minimum query length', async () => {
      const request = makeRequest('http://localhost/api/users/search?q=a');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Bad Request');
      expect(data.message).toBe('Search query must be at least 2 characters long');
    });

    test('requires query parameter', async () => {
      const request = makeRequest('http://localhost/api/users/search');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Bad Request');
      expect(data.message).toBe('Search query is required');
    });

    test('handles empty query parameter', async () => {
      const request = makeRequest('http://localhost/api/users/search?q=');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Bad Request');
      expect(data.message).toBe('Search query is required');
    });
  });

  describe('Response Format', () => {
    test('returns correctly formatted user data', async () => {
      const mockUser = {
        id: 'user-1',
        name: 'John Doe',
        email: '<EMAIL>',
        createdAt: new Date('2024-01-01T10:00:00Z'),
        profile: {
          firstName: 'John',
          lastName: 'Doe',
          nwaEmail: '<EMAIL>',
        },
      };

      mockPrisma.user.findMany.mockResolvedValue([mockUser] as any);
      mockPrisma.user.count.mockResolvedValue(1);

      const request = makeRequest('http://localhost/api/users/search?q=john');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.users[0]).toEqual({
        id: 'user-1',
        name: 'John Doe',
        email: '<EMAIL>',
        nwaEmail: '<EMAIL>',
        createdAt: '2024-01-01T10:00:00.000Z',
        profile: {
          firstName: 'John',
          lastName: 'Doe',
        },
      });
    });
  });

  describe('Error Handling', () => {
    test('handles database errors gracefully', async () => {
      mockPrisma.user.findMany.mockRejectedValue(new Error('Database connection failed'));

      const request = makeRequest('http://localhost/api/users/search?q=john');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Internal Server Error');
    });

    test('handles rate limiting', async () => {
      const { predefinedRateLimiters } = require('../../src/lib/middleware/rate-limiting');
      predefinedRateLimiters.api.mockResolvedValue({
        allowed: false,
        headers: { 'X-RateLimit-Remaining': '0' },
      });

      const request = makeRequest('http://localhost/api/users/search?q=john');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(429);
      expect(data.error).toBe('Too Many Requests');
    });
  });
});
