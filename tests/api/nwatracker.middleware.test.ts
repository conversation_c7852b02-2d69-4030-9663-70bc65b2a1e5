import { GET } from '../../src/app/api/nwatracker/route';
import { prisma } from '../../src/lib/prisma';

jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    remoteServer: {
      findFirst: jest.fn(),
    },
    user: {
      findMany: jest.fn(),
      count: jest.fn(),
      findUnique: jest.fn(),
    },
  },
}));

function makeReq({ url = 'http://localhost/api/nwatracker', headers = {} } = {}) {
  return {
    url,
    headers: new Headers(headers),
  } as any;
}

describe('NWATracker API key & host whitelist', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  test('returns 401 without api key', async () => {
    (prisma.remoteServer.findFirst as jest.Mock).mockResolvedValue(null);

    const res: any = await GET(makeReq({ url: 'http://localhost/api/nwatracker' }));
    const data = await res.json();

    expect(res.status).toBe(401);
    expect(data.error).toBe('Unauthorized');
  });

  test('allows request when api key matches and host is allowed', async () => {
    const remoteServer = {
      id: 'rs-1',
      name: 'NWATracker',
      url: 'http://nwatracker.example',
      apiEndpoint: '/api/nwatracker',
      allowedOrigins: ['localhost:3003'],
      isActive: true,
    };

    (prisma.remoteServer.findFirst as jest.Mock).mockResolvedValue(remoteServer);

    // Mock user queries to return empty list
    (prisma.user.findMany as jest.Mock).mockResolvedValue([]);
    (prisma.user.count as jest.Mock).mockResolvedValue(0);

    const req = makeReq({ url: 'http://localhost:3003/api/nwatracker', headers: { 'x-api-key': 'test-key', host: 'localhost:3003' } });

    const res: any = await GET(req);
    const data = await res.json();

    expect(res.status).toBe(200);
    expect(data.users).toBeDefined();
    expect(Array.isArray(data.users)).toBe(true);
  });
});
