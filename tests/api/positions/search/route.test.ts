import { NextRequest } from 'next/server';

// Mock NextResponse before importing the modules that use it
jest.mock('next/server', () => ({
  NextResponse: {
    json: jest.fn(),
  },
}));

// Now import the modules
import { GET as searchGET } from '@/app/api/positions/search/route';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// Mock Prisma client
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
    },
    position: {
      count: jest.fn(),
      findMany: jest.fn(),
    },
  },
}));

// Mock NextAuth
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));

jest.mock('@/lib/auth', () => ({
  authOptions: {},
}));

// Get the mock function
const { json: mockJson } = require('next/server').NextResponse;

describe('Position Search API', () => {
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Admin User',
  };

  const mockAdminUser = {
    ...mockUser,
    userRoles: [
      {
        role: {
          name: 'admin',
        },
      },
    ],
  };

  const mockPositions = [
    {
      id: 'pos-1',
      title: 'Ambassador to France',
      description: 'Diplomatic representative in France',
      level: 1,
      parentId: null,
      isActive: true,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    },
    {
      id: 'pos-2',
      title: 'Consul General',
      description: 'Senior consular officer',
      level: 2,
      parentId: null,
      isActive: true,
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful authentication
    (getServerSession as jest.Mock).mockResolvedValue({
      user: mockUser,
    });

    // Mock admin user lookup
    (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockAdminUser);
  });

  describe('GET /api/positions/search', () => {
    it('should return 401 if user is not authenticated', async () => {
      (getServerSession as jest.Mock).mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/positions/search?titleId=title-1&query=ambassador');
      await searchGET(request);

      expect(mockJson).toHaveBeenCalledWith({ error: 'Unauthorized' }, { status: 401 });
    });

    it('should return 403 if user is not admin', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        ...mockUser,
        userRoles: [
          {
            role: {
              name: 'user',
            },
          },
        ],
      });

      const request = new NextRequest('http://localhost:3000/api/positions/search?titleId=title-1&query=ambassador');
      await searchGET(request);

      expect(mockJson).toHaveBeenCalledWith({ error: 'Forbidden' }, { status: 403 });
    });

    it('should return 400 if titleId is missing', async () => {
      const request = new NextRequest('http://localhost:3000/api/positions/search?query=ambassador');
      await searchGET(request);

      expect(mockJson).toHaveBeenCalledWith({ 
        error: 'Bad Request', 
        message: 'Title ID is required' 
      }, { status: 400 });
    });

    it('should return 400 if query is too short', async () => {
      const request = new NextRequest('http://localhost:3000/api/positions/search?titleId=title-1&query=a');
      await searchGET(request);

      expect(mockJson).toHaveBeenCalledWith({ 
        error: 'Bad Request', 
        message: 'Search query must be at least 2 characters long' 
      }, { status: 400 });
    });

    it('should return 400 if limit is invalid', async () => {
      const request = new NextRequest('http://localhost:3000/api/positions/search?titleId=title-1&query=ambassador&limit=101');
      await searchGET(request);

      expect(mockJson).toHaveBeenCalledWith({ 
        error: 'Bad Request', 
        message: 'Limit must be between 1 and 100' 
      }, { status: 400 });
    });

    it('should search positions by title and description', async () => {
      (prisma.position.count as jest.Mock).mockResolvedValue(2);
      (prisma.position.findMany as jest.Mock).mockResolvedValue(mockPositions);

      const request = new NextRequest('http://localhost:3000/api/positions/search?titleId=title-1&query=ambassador');
      await searchGET(request);

      expect(prisma.position.count).toHaveBeenCalledWith({
        where: expect.objectContaining({
          titlePositions: {
            some: {
              titleId: 'title-1'
            }
          },
          OR: [
            { title: { contains: 'ambassador', mode: 'insensitive' } },
            { description: { contains: 'ambassador', mode: 'insensitive' } }
          ]
        })
      });

      expect(prisma.position.findMany).toHaveBeenCalledWith(expect.objectContaining({
        where: expect.objectContaining({
          titlePositions: {
            some: {
              titleId: 'title-1'
            }
          }
        }),
        take: 20,
        skip: 0
      }));

      expect(mockJson).toHaveBeenCalledWith(expect.objectContaining({
        success: true,
        data: mockPositions,
        total: 2
      }));
    });

    it('should handle pagination correctly', async () => {
      (prisma.position.count as jest.Mock).mockResolvedValue(50);
      (prisma.position.findMany as jest.Mock).mockResolvedValue(mockPositions);

      const request = new NextRequest('http://localhost:3000/api/positions/search?titleId=title-1&query=ambassador&page=2&limit=10');
      await searchGET(request);

      expect(prisma.position.findMany).toHaveBeenCalledWith(expect.objectContaining({
        take: 10,
        skip: 10
      }));
    });

    it('should handle empty search results', async () => {
      (prisma.position.count as jest.Mock).mockResolvedValue(0);
      (prisma.position.findMany as jest.Mock).mockResolvedValue([]);

      const request = new NextRequest('http://localhost:3000/api/positions/search?titleId=title-1&query=nonexistent');
      await searchGET(request);

      expect(mockJson).toHaveBeenCalledWith(expect.objectContaining({
        success: true,
        data: [],
        total: 0
      }));
    });

    it('should handle database errors gracefully', async () => {
      (prisma.position.count as jest.Mock).mockRejectedValue(new Error('Database error'));

      const request = new NextRequest('http://localhost:3000/api/positions/search?titleId=title-1&query=ambassador');
      await searchGET(request);

      expect(mockJson).toHaveBeenCalledWith({ error: 'Failed to search positions' }, { status: 500 });
    });

    it('should work with exact title matches', async () => {
      (prisma.position.count as jest.Mock).mockResolvedValue(1);
      (prisma.position.findMany as jest.Mock).mockResolvedValue([mockPositions[0]]);

      const request = new NextRequest('http://localhost:3000/api/positions/search?titleId=title-1&query=Ambassador to France');
      await searchGET(request);

      expect(prisma.position.findMany).toHaveBeenCalledWith(expect.objectContaining({
        where: expect.objectContaining({
          OR: [
            { title: { contains: 'Ambassador to France', mode: 'insensitive' } },
            { description: { contains: 'Ambassador to France', mode: 'insensitive' } }
          ]
        })
      }));
    });
  });
});