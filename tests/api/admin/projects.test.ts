/** @jest-environment node */

import { NextRequest } from 'next/server';

// Mock NextResponse before importing the modules that use it
jest.mock('next/server', () => ({
  NextResponse: {
    json: jest.fn(),
  },
}));


// Now import the modules
import * as projectRoutes from '@/app/api/admin/projects/route';
import * as projectDetailRoutes from '@/app/api/admin/projects/[id]/route';
import * as userProjectRoutes from '@/app/api/admin/users/[id]/projects/[projectId]/route';
import { prisma } from '@/lib/prisma';
import { createAuth<PERSON>hain } from '@/lib/middleware';
import { checkAdmin } from '@/lib/check-admin';

// Get the mock function
const { json: mockJson } = require('next/server').NextResponse;

// Get the mocked getServerSession function
const { getServerSession } = require('next-auth/next');

// Mock Prisma client
jest.mock('@/lib/prisma', () => ({
  prisma: {
    project: {
      findMany: jest.fn(),
      count: jest.fn(),
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
    scope: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    userProjectScope: {
      findMany: jest.fn(),
      upsert: jest.fn(),
      findUnique: jest.fn(),
      delete: jest.fn(),
    },
    auditLog: {
      create: jest.fn(),
    },
  },
}));

// Mock NextAuth
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));

jest.mock('next-auth', () => ({
  default: jest.fn(),
}));

jest.mock('@/lib/auth', () => ({
  authOptions: {},
}));

jest.mock('@/lib/middleware', () => ({
  createAuthChain: jest.fn(() => jest.fn().mockResolvedValue(undefined)),
}));

jest.mock('@/lib/check-admin', () => ({
  checkAdmin: jest.fn().mockResolvedValue(true),
}));

// Mock jose library to avoid ES module issues
jest.mock('jose', () => ({
  compactDecrypt: jest.fn(),
  jwtVerify: jest.fn(),
  createSecretKey: jest.fn(),
  importSPKI: jest.fn(),
  importX509: jest.fn(),
}));

// Mock openid-client library to avoid ES module issues
jest.mock('openid-client', () => ({
  Issuer: {
    discover: jest.fn(),
  },
  Client: jest.fn(),
  generators: {
    state: jest.fn(),
    codeVerifier: jest.fn(),
    codeChallenge: jest.fn(),
  },
}));

const mockedCreateAuthChain = createAuthChain as jest.Mock;
const mockedCheckAdmin = checkAdmin as jest.Mock;

describe('Project Management API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockJson.mockReset();

    // Mock a valid admin session
    (getServerSession as jest.Mock).mockResolvedValue({
      user: {
        id: 'admin-user-id',
        email: '<EMAIL>',
        name: 'Admin User',
      },
    });

    // Mock user with admin roles
    (prisma.user.findUnique as jest.Mock).mockResolvedValue({
      id: 'admin-user-id',
      email: '<EMAIL>',
      name: 'Admin User',
    });

    (prisma.auditLog.create as jest.Mock).mockResolvedValue({ id: 'audit-log-id' });

    mockedCreateAuthChain.mockImplementation(() => jest.fn().mockResolvedValue(undefined));
    mockedCheckAdmin.mockResolvedValue(true);
  });

  describe('GET /api/admin/projects', () => {
    it('should return a list of projects with pagination', async () => {
      // Mock request
      const request = {
        url: 'http://localhost:3000/api/admin/projects?page=1&limit=25',
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      // Mock Prisma responses
      (prisma.project.findMany as jest.Mock).mockResolvedValue([
        {
          id: 'project_123',
          name: 'Test Project',
          description: 'A test project',
          allowedOrigins: ['https://example.com'],
          isActive: true,
          createdAt: new Date(),
        },
      ]);

      (prisma.project.count as jest.Mock).mockResolvedValue(1);

      // Call the endpoint
      await projectRoutes.GET(request);

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        projects: [
          {
            id: 'project_123',
            name: 'Test Project',
            description: 'A test project',
            allowedOrigins: ['https://example.com'],
            isActive: true,
            createdAt: expect.any(Date),
          },
        ],
        pagination: {
          page: 1,
          limit: 25,
          total: 1,
        },
      });
    });

    it('should return 400 for invalid pagination parameters', async () => {
      // Mock request with invalid pagination
      const request = {
        url: 'http://localhost:3000/api/admin/projects?page=0&limit=0',
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      // Call the endpoint
      await projectRoutes.GET(request);

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Bad Request',
          message: 'Invalid pagination parameters',
        }),
        { status: 400 }
      );
    });
  });

  describe('POST /api/admin/projects', () => {
    it('should create a new project with an API key', async () => {
      // Mock request
      const request = {
        json: jest.fn().mockResolvedValue({
          name: 'New Project',
          description: 'A new test project',
          allowedOrigins: ['https://newproject.com'],
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      // Mock Prisma response
      (prisma.project.create as jest.Mock).mockResolvedValue({
        id: 'project_456',
        name: 'New Project',
        description: 'A new test project',
        allowedOrigins: ['https://newproject.com'],
        isActive: true,
        createdAt: new Date(),
      });

      // Call the endpoint
      await projectRoutes.POST(request);

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(expect.objectContaining({
        project: expect.objectContaining({
          id: 'project_456',
          name: 'New Project',
          description: 'A new test project',
          allowedOrigins: ['https://newproject.com'],
          isActive: true,
          createdAt: expect.any(Date),
          apiKey: expect.stringMatching(/^prj_/),
        }),
      }));
    });

    it('should return 400 for invalid request body', async () => {
      // Mock request with invalid body
      const request = {
        json: jest.fn().mockResolvedValue({
          name: '', // Should not be empty
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      // Call the endpoint
      await projectRoutes.POST(request);

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Bad Request',
        }),
        { status: 400 }
      );
    });
  });

  describe('GET /api/admin/projects/[id]', () => {
    it('should return project details for valid project ID', async () => {
      // Mock request
      const request = {
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'project_123' });

      // Mock Prisma response
      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_123',
        name: 'Test Project',
        description: 'A test project',
        allowedOrigins: ['https://example.com'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Call the endpoint
      await projectDetailRoutes.GET(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        project: {
          id: 'project_123',
          name: 'Test Project',
          description: 'A test project',
          allowedOrigins: ['https://example.com'],
          isActive: true,
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
        },
      });
    });

    it('should return 404 for non-existent project', async () => {
      // Mock request
      const request = {
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'nonexistent' });

      // Mock Prisma response
      (prisma.project.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await projectDetailRoutes.GET(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'Project not found',
        }),
        { status: 404 }
      );
    });
  });

  describe('PUT /api/admin/projects/[id]', () => {
    it('should update project details', async () => {
      // Mock request
      const request = {
        json: jest.fn().mockResolvedValue({
          name: 'Updated Project',
          description: 'An updated test project',
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'project_123' });

      // Mock Prisma responses
      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_123',
      });

      (prisma.project.update as jest.Mock).mockResolvedValue({
        id: 'project_123',
        name: 'Updated Project',
        description: 'An updated test project',
        allowedOrigins: ['https://example.com'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Call the endpoint
      await projectDetailRoutes.PUT(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        project: {
          id: 'project_123',
          name: 'Updated Project',
          description: 'An updated test project',
          allowedOrigins: ['https://example.com'],
          isActive: true,
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
        },
      });
    });

    it('should return 404 for updating non-existent project', async () => {
      // Mock request
      const request = {
        json: jest.fn().mockResolvedValue({
          name: 'Updated Project',
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'nonexistent' });

      // Mock Prisma response
      (prisma.project.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await projectDetailRoutes.PUT(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'Project not found',
        }),
        { status: 404 }
      );
    });

    it('should return 400 for invalid request body', async () => {
      // Mock request with invalid body
      const request = {
        json: jest.fn().mockResolvedValue({
          name: '', // Should not be empty
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'project_123' });

      // Mock Prisma response
      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_123',
      });

      // Call the endpoint
      await projectDetailRoutes.PUT(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Bad Request',
        }),
        { status: 400 }
      );
    });
  });

  describe('DELETE /api/admin/projects/[id]', () => {
    it('should deactivate project by default', async () => {
      // Mock request
      const request = {
        url: 'http://localhost:3000/api/admin/projects/project_123',
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'project_123' });

      // Mock Prisma responses
      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_123',
      });

      (prisma.project.update as jest.Mock).mockResolvedValue({
        id: 'project_123',
        name: 'Test Project',
        isActive: false,
      });

      // Call the endpoint
      await projectDetailRoutes.DELETE(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        message: 'Project deactivated successfully',
        project: {
          id: 'project_123',
          name: 'Test Project',
          isActive: false,
        },
      });
    });

    it('should permanently delete project when permanent=true', async () => {
      // Mock request
      const request = {
        url: 'http://localhost:3000/api/admin/projects/project_123?permanent=true',
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'project_123' });

      // Mock Prisma responses
      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_123',
      });

      (prisma.project.delete as jest.Mock).mockResolvedValue({});

      // Call the endpoint
      await projectDetailRoutes.DELETE(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        message: 'Project deleted permanently',
      });
    });

    it('should return 404 for deleting non-existent project', async () => {
      // Mock request
      const request = {
        url: 'http://localhost:3000/api/admin/projects/nonexistent',
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'nonexistent' });

      // Mock Prisma response
      (prisma.project.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await projectDetailRoutes.DELETE(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'Project not found',
        }),
        { status: 404 }
      );
    });
  });
});

describe('User Project Scopes API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockJson.mockReset();
    mockedCreateAuthChain.mockImplementation(() => jest.fn().mockResolvedValue(undefined));
    mockedCheckAdmin.mockResolvedValue(true);
  });

  it('returns placeholder response for GET handler', async () => {
    const request = {
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = Promise.resolve({ id: 'user_123', projectId: 'project_456' });

    await userProjectRoutes.GET(request, { params });

    expect(mockJson).toHaveBeenCalledWith({ message: 'Mock GET response' });
  });

  it('returns placeholder response for POST handler', async () => {
    const request = {
      json: jest.fn().mockResolvedValue({ scopes: ['scope_789'] }),
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = Promise.resolve({ id: 'user_123', projectId: 'project_456' });

    await userProjectRoutes.POST(request, { params });

    expect(mockJson).toHaveBeenCalledWith({ message: 'Mock POST response' });
  });
});
