// Mock next/server to avoid Request dependency errors in test environment
jest.mock('next/server', () => ({
  NextResponse: {
    json: (body: any, init?: any) => ({ status: init?.status ?? 200, json: async () => body }),
  },
}));

import { NextRequest } from 'next/server';
import { GET, POST } from '../../src/app/api/users/[id]/identifications/route';
import { DELETE as DELETE_ID, PUT as UPDATE_ID } from '../../src/app/api/users/[id]/identifications/[identificationId]/route';
import { prisma } from '../../src/lib/prisma';

jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    user: { findUnique: jest.fn() },
    identificationType: { findUnique: jest.fn() },
    userIdentification: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
  },
}));

jest.mock('../../src/lib/middleware/require-auth', () => ({
  requireAuth: jest.fn().mockResolvedValue({ user: { id: 'user-1', roles: [{ name: 'admin' }] }, response: null }),
}));

jest.mock('../../src/lib/middleware/audit-logging', () => ({
  auditLogger: { logApiAccess: jest.fn() },
}));

function makeRequest(url: string, options: any = {}): NextRequest {
  return {
    url,
    method: options.method || 'GET',
    headers: { get: jest.fn().mockReturnValue('application/json') },
    json: jest.fn().mockResolvedValue(options.body || {}),
  } as unknown as NextRequest;
}

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('User Identifications API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('POST enforces ID number format', async () => {
    (mockPrisma.user.findUnique as jest.Mock).mockResolvedValue({ id: 'user-1' });
    (mockPrisma.identificationType.findUnique as jest.Mock).mockResolvedValue({ id: 'type-1' });

    const req = makeRequest('http://localhost/api/users/user-1/identifications', {
      method: 'POST',
      body: { identificationTypeId: 'type-1', idNumber: 'invalid space' },
    });
    const res = await POST(req, { params: { id: 'user-1' } as any });
    expect(res.status).toBe(400);
  });

  test('POST prevents duplicate identification', async () => {
    (mockPrisma.user.findUnique as jest.Mock).mockResolvedValue({ id: 'user-1' });
    (mockPrisma.identificationType.findUnique as jest.Mock).mockResolvedValue({ id: 'type-1' });
    (mockPrisma.userIdentification.findFirst as jest.Mock).mockResolvedValue({ id: 'existing' });

    const req = makeRequest('http://localhost/api/users/user-1/identifications', {
      method: 'POST',
      body: { identificationTypeId: 'type-1', idNumber: 'ABC123' },
    });
    const res = await POST(req, { params: { id: 'user-1' } as any });
    expect(res.status).toBe(409);
  });

  test('DELETE enforces minimum 2 identifications', async () => {
    (mockPrisma.userIdentification.findFirst as jest.Mock).mockResolvedValue({ id: 'uid-1', identificationType: { name: 'Passport' } });
    (mockPrisma.userIdentification.count as jest.Mock).mockResolvedValue(2);

    const req = makeRequest('http://localhost/api/users/user-1/identifications/uid-1', { method: 'DELETE' });
    const res = await DELETE_ID(req, { params: { id: 'user-1', identificationId: 'uid-1' } as any });
    const data = await res.json();
    expect(res.status).toBe(400);
    expect(data.code).toBe('MINIMUM_IDS_REQUIRED');
  });

  test('PUT validates ID number format', async () => {
    (mockPrisma.userIdentification.findFirst as jest.Mock).mockResolvedValue({ id: 'uid-1', identificationType: { name: 'Passport' } });
    (mockPrisma.identificationType.findUnique as jest.Mock).mockResolvedValue({ id: 'type-1' });

    const req = makeRequest('http://localhost/api/users/user-1/identifications/uid-1', {
      method: 'PUT',
      body: { identificationTypeId: 'type-1', idNumber: 'bad number' },
    });
    const res = await UPDATE_ID(req, { params: { id: 'user-1', identificationId: 'uid-1' } as any });
    expect(res.status).toBe(400);
  });
});

