import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { GET } from '../../src/app/api/users/[id]/full/route';
import { prisma } from '../../src/lib/prisma';

// Mock dependencies
jest.mock('next-auth/next');
jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
    },
  },
}));

jest.mock('../../src/lib/middleware/rate-limiting', () => ({
  predefinedRateLimiters: {
    api: jest.fn().mockResolvedValue({ 
      allowed: true, 
      headers: {} 
    }),
  },
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

// Mock session data
const mockAdminSession = {
  user: {
    id: 'admin-123',
    email: '<EMAIL>',
    name: 'Admin User',
  },
};

const mockAdminUser = {
  id: 'admin-123',
  userRoles: [
    {
      role: {
        name: 'admin',
      },
    },
  ],
};

const mockCompleteUser = {
  id: 'user-123',
  name: 'John Doe',
  email: '<EMAIL>',
  personalEmail: '<EMAIL>',
  nwaEmail: '<EMAIL>',
  createdAt: new Date('2024-01-01T10:00:00Z'),
  updatedAt: new Date('2024-01-02T10:00:00Z'),
  profile: {
    firstName: 'John',
    lastName: 'Doe',
    dateOfBirth: new Date('1980-01-01'),
    phone: '******-0123',
    mobile: '******-0456',
    bio: 'Test biography',
    streetAddress1: '123 Main Street',
    streetAddress2: 'Apt 4B',
    town: 'Springfield',
    city: 'Los Angeles',
    country: 'United States',
    postalCode: '90210',
    regionId: 1,
    regionText: 'California',
    peaceAmbassadorNumber: 'PA-001',
  },
  identifications: [
    {
      id: 'id-1',
      type: 'drivers_license',
      number: 'DL123456789',
      issuedDate: new Date('2020-01-01'),
      expiryDate: new Date('2025-01-01'),
    },
    {
      id: 'id-2',
      type: 'passport',
      number: 'PP987654321',
      issuedDate: new Date('2019-06-01'),
      expiryDate: new Date('2029-06-01'),
    },
  ],
  positions: [
    {
      id: 'pos-1',
      positionId: 'position-1',
      titleId: 'title-1',
      startDate: new Date('2023-01-01'),
      endDate: null,
      isActive: true,
    },
  ],
  treaties: [
    {
      id: 'treaty-1',
      treatyTypeId: 'treaty-type-1',
      treatyNumber: 'TR-001',
      status: 'active',
      createdAt: new Date('2023-01-01'),
    },
    {
      id: 'treaty-2',
      treatyTypeId: 'treaty-type-2',
      treatyNumber: 'TR-002',
      status: 'draft',
      createdAt: new Date('2023-06-01'),
    },
  ],
  ordinances: [
    {
      id: 'ord-1',
      ordinanceId: 'ordinance-1',
      assignedAt: new Date('2023-02-01'),
      status: 'active',
    },
  ],
};

const mockMinimalUser = {
  id: 'user-456',
  name: 'Jane Smith',
  email: '<EMAIL>',
  personalEmail: null,
  nwaEmail: null,
  createdAt: new Date('2024-01-01T10:00:00Z'),
  updatedAt: new Date('2024-01-01T10:00:00Z'),
  profile: null,
  identifications: [],
  positions: [],
  treaties: [],
  ordinances: [],
};

function makeRequest(url: string): NextRequest {
  return {
    url,
    method: 'GET',
    headers: {
      get: jest.fn().mockReturnValue('application/json'),
    },
  } as unknown as NextRequest;
}

describe('GET /api/users/[id]/full', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetServerSession.mockResolvedValue(mockAdminSession);
    // Mock admin check for session user
    mockPrisma.user.findUnique
      .mockResolvedValueOnce(mockAdminUser as any) // First call for admin check
      .mockResolvedValueOnce(mockCompleteUser as any); // Second call for user data
  });

  describe('Authentication and Authorization', () => {
    test('returns 401 for unauthenticated requests', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = makeRequest('http://localhost/api/users/user-123/full');
      const response = await GET(request, { params: Promise.resolve({ id: 'user-123' }) });
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    test('returns 403 for non-admin users', async () => {
      const nonAdminUser = {
        id: 'user-123',
        userRoles: [{ role: { name: 'member' } }],
      };
      mockPrisma.user.findUnique
        .mockResolvedValueOnce(nonAdminUser as any)
        .mockResolvedValueOnce(mockCompleteUser as any);

      const request = makeRequest('http://localhost/api/users/user-123/full');
      const response = await GET(request, { params: Promise.resolve({ id: 'user-123' }) });
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Forbidden');
    });

    test('returns 404 when admin user not found in database', async () => {
      mockPrisma.user.findUnique.mockResolvedValueOnce(null);

      const request = makeRequest('http://localhost/api/users/user-123/full');
      const response = await GET(request, { params: Promise.resolve({ id: 'user-123' }) });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('User not found');
    });
  });

  describe('User Data Retrieval', () => {
    test('returns complete user data with all relations', async () => {
      const request = makeRequest('http://localhost/api/users/user-123/full');
      const response = await GET(request, { params: Promise.resolve({ id: 'user-123' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.user).toEqual({
        id: 'user-123',
        name: 'John Doe',
        email: '<EMAIL>',
        personalEmail: '<EMAIL>',
        nwaEmail: '<EMAIL>',
        createdAt: '2024-01-01T10:00:00.000Z',
        updatedAt: '2024-01-02T10:00:00.000Z',
        profile: {
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: '1980-01-01T00:00:00.000Z',
          phone: '******-0123',
          mobile: '******-0456',
          bio: 'Test biography',
          streetAddress1: '123 Main Street',
          streetAddress2: 'Apt 4B',
          town: 'Springfield',
          city: 'Los Angeles',
          country: 'United States',
          postalCode: '90210',
          regionId: 1,
          regionText: 'California',
          peaceAmbassadorNumber: 'PA-001',
        },
        identifications: [
          {
            id: 'id-1',
            type: 'drivers_license',
            number: 'DL123456789',
            issuedDate: '2020-01-01T00:00:00.000Z',
            expiryDate: '2025-01-01T00:00:00.000Z',
          },
          {
            id: 'id-2',
            type: 'passport',
            number: 'PP987654321',
            issuedDate: '2019-06-01T00:00:00.000Z',
            expiryDate: '2029-06-01T00:00:00.000Z',
          },
        ],
        positions: [
          {
            id: 'pos-1',
            positionId: 'position-1',
            titleId: 'title-1',
            startDate: '2023-01-01T00:00:00.000Z',
            endDate: null,
            isActive: true,
          },
        ],
        treaties: [
          {
            id: 'treaty-1',
            treatyTypeId: 'treaty-type-1',
            treatyNumber: 'TR-001',
            status: 'active',
            createdAt: '2023-01-01T00:00:00.000Z',
          },
          {
            id: 'treaty-2',
            treatyTypeId: 'treaty-type-2',
            treatyNumber: 'TR-002',
            status: 'draft',
            createdAt: '2023-06-01T00:00:00.000Z',
          },
        ],
        ordinances: [
          {
            id: 'ord-1',
            ordinanceId: 'ordinance-1',
            assignedAt: '2023-02-01T00:00:00.000Z',
            status: 'active',
          },
        ],
      });

      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        include: {
          profile: true,
          identifications: true,
          positions: true,
          treaties: true,
          ordinances: true,
        },
      });
    });

    test('returns user with minimal data when profile and relations are empty', async () => {
      mockPrisma.user.findUnique
        .mockResolvedValueOnce(mockAdminUser as any) // Admin check
        .mockResolvedValueOnce(mockMinimalUser as any); // User data

      const request = makeRequest('http://localhost/api/users/user-456/full');
      const response = await GET(request, { params: Promise.resolve({ id: 'user-456' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.user).toEqual({
        id: 'user-456',
        name: 'Jane Smith',
        email: '<EMAIL>',
        personalEmail: null,
        nwaEmail: null,
        createdAt: '2024-01-01T10:00:00.000Z',
        updatedAt: '2024-01-01T10:00:00.000Z',
        profile: null,
        identifications: [],
        positions: [],
        treaties: [],
        ordinances: [],
      });
    });

    test('returns 404 when requested user does not exist', async () => {
      mockPrisma.user.findUnique
        .mockResolvedValueOnce(mockAdminUser as any) // Admin check passes
        .mockResolvedValueOnce(null); // User not found

      const request = makeRequest('http://localhost/api/users/nonexistent/full');
      const response = await GET(request, { params: Promise.resolve({ id: 'nonexistent' }) });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('User not found');
    });
  });

  describe('Database Query Structure', () => {
    test('includes all necessary relations in query', async () => {
      const request = makeRequest('http://localhost/api/users/user-123/full');
      await GET(request, { params: Promise.resolve({ id: 'user-123' }) });

      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        include: {
          profile: true,
          identifications: true,
          positions: true,
          treaties: true,
          ordinances: true,
        },
      });
    });
  });

  describe('Response Format', () => {
    test('properly formats dates as ISO strings', async () => {
      const request = makeRequest('http://localhost/api/users/user-123/full');
      const response = await GET(request, { params: Promise.resolve({ id: 'user-123' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      
      // Check main user dates
      expect(data.user.createdAt).toBe('2024-01-01T10:00:00.000Z');
      expect(data.user.updatedAt).toBe('2024-01-02T10:00:00.000Z');
      
      // Check profile dates
      expect(data.user.profile.dateOfBirth).toBe('1980-01-01T00:00:00.000Z');
      
      // Check identification dates
      expect(data.user.identifications[0].issuedDate).toBe('2020-01-01T00:00:00.000Z');
      expect(data.user.identifications[0].expiryDate).toBe('2025-01-01T00:00:00.000Z');
      
      // Check position dates
      expect(data.user.positions[0].startDate).toBe('2023-01-01T00:00:00.000Z');
      expect(data.user.positions[0].endDate).toBe(null);
      
      // Check treaty dates
      expect(data.user.treaties[0].createdAt).toBe('2023-01-01T00:00:00.000Z');
      
      // Check ordinance dates
      expect(data.user.ordinances[0].assignedAt).toBe('2023-02-01T00:00:00.000Z');
    });

    test('handles null and undefined values correctly', async () => {
      mockPrisma.user.findUnique
        .mockResolvedValueOnce(mockAdminUser as any)
        .mockResolvedValueOnce(mockMinimalUser as any);

      const request = makeRequest('http://localhost/api/users/user-456/full');
      const response = await GET(request, { params: Promise.resolve({ id: 'user-456' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.user.personalEmail).toBe(null);
      expect(data.user.nwaEmail).toBe(null);
      expect(data.user.profile).toBe(null);
      expect(data.user.identifications).toEqual([]);
      expect(data.user.positions).toEqual([]);
      expect(data.user.treaties).toEqual([]);
      expect(data.user.ordinances).toEqual([]);
    });
  });

  describe('Error Handling', () => {
    test('handles database errors gracefully', async () => {
      mockPrisma.user.findUnique
        .mockResolvedValueOnce(mockAdminUser as any) // Admin check passes
        .mockRejectedValueOnce(new Error('Database connection failed')); // User query fails

      const request = makeRequest('http://localhost/api/users/user-123/full');
      const response = await GET(request, { params: Promise.resolve({ id: 'user-123' }) });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Internal Server Error');
    });

    test('handles rate limiting', async () => {
      const { predefinedRateLimiters } = require('../../src/lib/middleware/rate-limiting');
      predefinedRateLimiters.api.mockResolvedValue({
        allowed: false,
        headers: { 'X-RateLimit-Remaining': '0' },
      });

      const request = makeRequest('http://localhost/api/users/user-123/full');
      const response = await GET(request, { params: Promise.resolve({ id: 'user-123' }) });
      const data = await response.json();

      expect(response.status).toBe(429);
      expect(data.error).toBe('Too Many Requests');
    });
  });

  describe('Security Considerations', () => {
    test('admin check happens before user data retrieval', async () => {
      const nonAdminUser = {
        id: 'user-123',
        userRoles: [{ role: { name: 'member' } }],
      };
      
      mockPrisma.user.findUnique
        .mockResolvedValueOnce(nonAdminUser as any);

      const request = makeRequest('http://localhost/api/users/user-123/full');
      const response = await GET(request, { params: Promise.resolve({ id: 'user-123' }) });

      expect(response.status).toBe(403);
      
      // Verify that user data query was never made
      expect(mockPrisma.user.findUnique).toHaveBeenCalledTimes(1);
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'admin-123' },
        include: {
          userRoles: {
            include: {
              role: true,
            },
          },
        },
      });
    });
  });
});
