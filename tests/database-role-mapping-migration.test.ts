/**
 * @jest-environment node
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

describe('Role Mapping Migration Tests', () => {
  beforeAll(async () => {
    await prisma.$connect()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  beforeEach(async () => {
    // Clean up test data in correct order to avoid foreign key constraints
    await prisma.auditLog.deleteMany({})
    await prisma.user_remote_server_permissions.deleteMany({})
    await prisma.user_remote_server_access.deleteMany({})
    await prisma.role_remote_server_access.deleteMany({})
    await prisma.remote_server_permissions.deleteMany({})
    await prisma.remoteServer.deleteMany({})
    await prisma.role.deleteMany({})
    await prisma.user.deleteMany({})
  })

  describe('Migration Data Integrity', () => {
    test('should migrate role_remote_server_access data correctly', async () => {
      // Create test data
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Migration Test User',
        },
      })

      const role = await prisma.role.create({
        data: {
          name: 'test-envoy-role',
          description: 'Test role for migration',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-migration-server',
          name: 'Migration Test Server',
          url: 'http://migration.example.com',
          apiKey: 'migration-api-key-123',
        },
      })

      // Create old-style role server access
      const oldAccess = await prisma.role_remote_server_access.create({
        data: {
          role_id: role.id,
          remote_server_id: remoteServer.id,
          auto_grant_permissions: ['read_documents', 'write_documents'],
          created_by: user.id,
          is_active: true,
        },
      })

      // Run migration (simulate)
      const migrationResult = await prisma.$queryRaw`
        INSERT INTO remote_server_role_mappings (
          remote_server_id,
          local_role_name,
          remote_role_name,
          is_active,
          created_at,
          created_by
        )
        SELECT
          remote_server_id,
          roles.name as local_role_name,
          roles.name as remote_role_name,
          is_active,
          created_at,
          created_by
        FROM role_remote_server_access
        JOIN roles ON role_remote_server_access.role_id = roles.id
        WHERE role_remote_server_access.is_active = true
        ON CONFLICT (remote_server_id, local_role_name, remote_role_name)
        DO NOTHING;
      `

      // Verify migration
      const newMappings = await prisma.remote_server_role_mappings.findMany({
        where: {
          local_role_name: role.name,
          remote_server_id: remoteServer.id,
        },
      })

      expect(newMappings).toHaveLength(1)
      expect(newMappings[0].remote_role_name).toBe('test-envoy-role')
      expect(newMappings[0].is_active).toBe(true)

      // Clean up
      await prisma.role_remote_server_access.delete({ where: { id: oldAccess.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.role.delete({ where: { id: role.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })

    test('should migrate auto-grant permissions to permission mappings', async () => {
      // Create test data
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Permission Test User',
        },
      })

      const role = await prisma.role.create({
        data: {
          name: 'test-role-with-permissions',
          description: 'Test role with permissions',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-permission-server',
          name: 'Permission Test Server',
          url: 'http://permission.example.com',
          apiKey: 'permission-api-key-123',
        },
      })

      // Create old-style role server access with permissions
      const oldAccess = await prisma.role_remote_server_access.create({
        data: {
          role_id: role.id,
          remote_server_id: remoteServer.id,
          auto_grant_permissions: ['read_documents', 'write_documents', 'delete_documents'],
          created_by: user.id,
          is_active: true,
        },
      })

      // Run migration
      await prisma.$queryRaw`
        INSERT INTO remote_server_role_mappings (
          remote_server_id,
          local_role_name,
          remote_role_name,
          is_active,
          created_at,
          created_by
        )
        SELECT
          remote_server_id,
          roles.name as local_role_name,
          roles.name as remote_role_name,
          is_active,
          created_at,
          created_by
        FROM role_remote_server_access
        JOIN roles ON role_remote_server_access.role_id = roles.id
        WHERE role_remote_server_access.is_active = true
        ON CONFLICT (remote_server_id, local_role_name, remote_role_name)
        DO NOTHING;
      `

      // Migrate permissions
      await prisma.$queryRaw`
        INSERT INTO remote_server_permission_mappings (
          remote_server_role_mapping_id,
          local_permission_name,
          remote_permission_name,
          is_active
        )
        SELECT
          rsm.id as remote_server_role_mapping_id,
          unnest(rra.auto_grant_permissions) as local_permission_name,
          unnest(rra.auto_grant_permissions) as remote_permission_name,
          true as is_active
        FROM role_remote_server_access rra
        JOIN remote_server_role_mappings rsm ON rra.role_id = rsm.local_role_id
          AND rra.remote_server_id = rsm.remote_server_id
        WHERE rra.is_active = true
          AND array_length(rra.auto_grant_permissions, 1) > 0
        ON CONFLICT (remote_server_role_mapping_id, local_permission_name)
        DO NOTHING;
      `

      // Verify permission mappings
      const roleMapping = await prisma.remote_server_role_mappings.findFirst({
        where: {
          local_role_name: role.name,
          remote_server_id: remoteServer.id,
        },
      })

      const permissionMappings = await prisma.remote_server_permission_mappings.findMany({
        where: {
          remote_server_role_mapping_id: roleMapping?.id,
        },
      })

      expect(permissionMappings).toHaveLength(3)
      expect(permissionMappings.map(p => p.local_permission_name)).toEqual(
        expect.arrayContaining(['read_documents', 'write_documents', 'delete_documents'])
      )

      // Clean up
      await prisma.role_remote_server_access.delete({ where: { id: oldAccess.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.role.delete({ where: { id: role.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })
  })

  describe('New Table Constraints', () => {
    test('should enforce unique constraint on role mappings', async () => {
      const role = await prisma.role.create({
        data: {
          name: 'unique-test-role',
          description: 'Test role for uniqueness',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-unique-server',
          name: 'Unique Test Server',
          url: 'http://unique.example.com',
          apiKey: 'unique-api-key-123',
        },
      })

      // Create first mapping
      const firstMapping = await prisma.remote_server_role_mappings.create({
        data: {
          local_role_name: role.name,
          remote_server_id: remoteServer.id,
          remote_role_name: 'test-remote-role',
        },
      })

      // Attempt to create duplicate should fail
      await expect(
        prisma.remote_server_role_mappings.create({
          data: {
            local_role_name: role.name,
            remote_server_id: remoteServer.id,
            remote_role_name: 'test-remote-role',
          },
        })
      ).rejects.toThrow()

      // Clean up
      await prisma.remote_server_role_mappings.delete({ where: { id: firstMapping.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.role.delete({ where: { id: role.id } })
    })

    test('should enforce unique constraint on permission mappings', async () => {
      const role = await prisma.role.create({
        data: {
          name: 'permission-unique-role',
          description: 'Test role for permission uniqueness',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-permission-unique-server',
          name: 'Permission Unique Server',
          url: 'http://permissionunique.example.com',
          apiKey: 'permission-unique-api-key-123',
        },
      })

      const roleMapping = await prisma.remote_server_role_mappings.create({
        data: {
          local_role_name: role.name,
          remote_server_id: remoteServer.id,
          remote_role_name: 'test-remote-role',
        },
      })

      // Create first permission mapping
      const firstPermission = await prisma.remote_server_permission_mappings.create({
        data: {
          remote_server_role_mapping_id: roleMapping.id,
          local_permission_name: 'test_permission',
          remote_permission_name: 'test_remote_permission',
        },
      })

      // Attempt to create duplicate should fail
      await expect(
        prisma.remote_server_permission_mappings.create({
          data: {
            remote_server_role_mapping_id: roleMapping.id,
            local_permission_name: 'test_permission',
            remote_permission_name: 'different_remote_permission',
          },
        })
      ).rejects.toThrow()

      // Clean up
      await prisma.remote_server_permission_mappings.delete({ where: { id: firstPermission.id } })
      await prisma.remote_server_role_mappings.delete({ where: { id: roleMapping.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.role.delete({ where: { id: role.id } })
    })
  })

  describe('Backward Compatibility View', () => {
    test('should provide backward compatibility through view', async () => {
      const role = await prisma.role.create({
        data: {
          name: 'view-test-role',
          description: 'Test role for view compatibility',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-view-server',
          name: 'View Test Server',
          url: 'http://view.example.com',
          apiKey: 'view-api-key-123',
        },
      })

      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'View Test User',
        },
      })

      // Create new-style mapping
      const roleMapping = await prisma.remote_server_role_mappings.create({
        data: {
          local_role_name: role.name,
          remote_server_id: remoteServer.id,
          remote_role_name: 'test-remote-role',
          created_by: user.id,
        },
      })

      // Create permission mappings
      await prisma.remote_server_permission_mappings.create({
        data: {
          remote_server_role_mapping_id: roleMapping.id,
          local_permission_name: 'read_documents',
          remote_permission_name: 'read',
        },
      })

      await prisma.remote_server_permission_mappings.create({
        data: {
          remote_server_role_mapping_id: roleMapping.id,
          local_permission_name: 'write_documents',
          remote_permission_name: 'write',
        },
      })

      // Test backward compatibility view
      const viewResult = await prisma.$queryRaw`
        SELECT * FROM role_remote_server_access_view
        WHERE role_id = ${role.id} AND remote_server_id = ${remoteServer.id}
      `

      expect(viewResult).toHaveLength(1)
      expect(viewResult[0].auto_grant_permissions).toEqual(['read', 'write'])

      // Clean up
      await prisma.remote_server_permission_mappings.deleteMany({
        where: { remote_server_role_mapping_id: roleMapping.id }
      })
      await prisma.remote_server_role_mappings.delete({ where: { id: roleMapping.id } })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.role.delete({ where: { id: role.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })
  })

  describe('Migration Validation', () => {
    test('should validate migration completed successfully', async () => {
      // Create test data
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Validation Test User',
        },
      })

      const role1 = await prisma.role.create({
        data: {
          name: 'test-role-1',
          description: 'Test role 1',
        },
      })

      const role2 = await prisma.role.create({
        data: {
          name: 'test-role-2',
          description: 'Test role 2',
        },
      })

      const remoteServer = await prisma.remoteServer.create({
        data: {
          id: 'test-validation-server',
          name: 'Validation Test Server',
          url: 'http://validation.example.com',
          apiKey: 'validation-api-key-123',
        },
      })

      // Create old-style access records
      await prisma.role_remote_server_access.create({
        data: {
          role_id: role1.id,
          remote_server_id: remoteServer.id,
          auto_grant_permissions: ['read', 'write'],
          created_by: user.id,
          is_active: true,
        },
      })

      await prisma.role_remote_server_access.create({
        data: {
          role_id: role2.id,
          remote_server_id: remoteServer.id,
          auto_grant_permissions: ['read', 'admin'],
          created_by: user.id,
          is_active: true,
        },
      })

      // Run migration
      await prisma.$queryRaw`
        INSERT INTO remote_server_role_mappings (
          local_role_id,
          remote_server_id,
          remote_role_name,
          is_active,
          created_at,
          created_by
        )
        SELECT
          role_id,
          remote_server_id,
          roles.name as remote_role_name,
          is_active,
          created_at,
          created_by
        FROM role_remote_server_access
        JOIN roles ON role_remote_server_access.role_id = roles.id
        WHERE role_remote_server_access.is_active = true
        ON CONFLICT (local_role_id, remote_server_id, remote_role_name)
        DO NOTHING;
      `

      // Validate migration results
      const validationResult = await prisma.$queryRaw`
        SELECT
          old_count.old_records,
          new_count.new_records,
          perm_count.permission_mappings
        FROM
          (SELECT COUNT(*) as old_records FROM role_remote_server_access WHERE is_active = true) old_count,
          (SELECT COUNT(*) as new_records FROM remote_server_role_mappings WHERE is_active = true) new_count,
          (SELECT COUNT(*) as permission_mappings FROM remote_server_permission_mappings WHERE is_active = true) perm_count
      `

      expect(validationResult[0].old_records).toBe(2)
      expect(validationResult[0].new_records).toBe(2)
      expect(validationResult[0].permission_mappings).toBe(3) // read, write, admin

      // Clean up
      await prisma.role_remote_server_access.deleteMany({
        where: { remote_server_id: remoteServer.id }
      })
      await prisma.remoteServer.delete({ where: { id: remoteServer.id } })
      await prisma.role.deleteMany({ where: { id: { in: [role1.id, role2.id] } } })
      await prisma.user.delete({ where: { id: user.id } })
    })
  })
})