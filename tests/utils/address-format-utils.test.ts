import {
  parseAddressTemplate,
  replacePlaceholders,
  validatePostalCode,
  formatAddressForDisplay,
  getCountryFieldLabels,
  sanitizeAddressComponent,
  buildFormattedAddress
} from '../../src/lib/utils/address-format-utils';

// Mock address format data for testing
const mockAddressFormats = {
  US: {
    postalCodeLabel: 'ZIP Code',
    postalCodeFormat: '^\\d{5}(-\\d{4})?$',
    postalCodeRequired: true,
    regionLabel: 'State',
    regionRequired: true,
    townLabel: 'City',
    townRequired: true,
    addressTemplate: '{street}\n{town}, {region} {postalCode}\n{country}'
  },
  GB: {
    postalCodeLabel: 'Postcode',
    postalCodeFormat: '^[A-Z]{1,2}\\d[A-Z\\d]? ?\\d[A-Z]{2}$',
    postalCodeRequired: true,
    regionLabel: 'County',
    regionRequired: false,
    townLabel: 'Town/City',
    townRequired: true,
    addressTemplate: '{street}\n{town}\n{region}\n{postalCode}\n{country}'
  },
  CA: {
    postalCodeLabel: 'Postal Code',
    postalCodeFormat: '^[A-Z]\\d[A-Z] ?\\d[A-Z]\\d$',
    postalCodeRequired: true,
    regionLabel: 'Province',
    regionRequired: true,
    townLabel: 'City',
    townRequired: true,
    addressTemplate: '{street}\n{town}, {region} {postalCode}\n{country}'
  }
};

const mockAddressData = {
  complete: {
    streetAddress1: '123 Main Street',
    streetAddress2: 'Apt 2B',
    town: 'Springfield',
    region: 'California',
    postalCode: '90210',
    country: 'United States'
  },
  partial: {
    streetAddress1: '456 Oak Avenue',
    streetAddress2: null,
    town: 'Manchester',
    region: null,
    postalCode: 'M1 1AA',
    country: 'United Kingdom'
  },
  minimal: {
    streetAddress1: '789 Pine Road',
    streetAddress2: null,
    town: null,
    region: null,
    postalCode: null,
    country: 'Generic Country'
  }
};

describe('AddressFormatUtils', () => {
  describe('parseAddressTemplate', () => {
    it('extracts placeholders from address template', () => {
      const template = '{street}\n{town}, {region} {postalCode}\n{country}';
      const placeholders = parseAddressTemplate(template);
      
      expect(placeholders).toEqual(['street', 'town', 'region', 'postalCode', 'country']);
    });

    it('handles templates with duplicate placeholders', () => {
      const template = '{street}\n{town} - {town}\n{country}';
      const placeholders = parseAddressTemplate(template);
      
      expect(placeholders).toEqual(['street', 'town', 'country']);
    });

    it('returns empty array for template without placeholders', () => {
      const template = 'Static address format';
      const placeholders = parseAddressTemplate(template);
      
      expect(placeholders).toEqual([]);
    });

    it('handles empty or null template', () => {
      expect(parseAddressTemplate('')).toEqual([]);
      expect(parseAddressTemplate(null as any)).toEqual([]);
      expect(parseAddressTemplate(undefined as any)).toEqual([]);
    });

    it('handles complex templates with various placeholders', () => {
      const template = 'Line 1: {street}\nLine 2: {town} {region}\nLine 3: {postalCode} {country}';
      const placeholders = parseAddressTemplate(template);
      
      expect(placeholders).toEqual(['street', 'town', 'region', 'postalCode', 'country']);
    });
  });

  describe('replacePlaceholders', () => {
    it('replaces all placeholders with address data', () => {
      const template = '{street}\n{town}, {region} {postalCode}';
      const addressData = {
        street: '123 Main Street',
        town: 'Springfield',
        region: 'California',
        postalCode: '90210'
      };

      const result = replacePlaceholders(template, addressData);
      expect(result).toBe('123 Main Street\nSpringfield, California 90210');
    });

    it('handles missing address data gracefully', () => {
      const template = '{street}\n{town}, {region} {postalCode}';
      const addressData = {
        street: '123 Main Street',
        town: 'Springfield',
        region: null,
        postalCode: undefined
      };

      const result = replacePlaceholders(template, addressData);
      expect(result).toBe('123 Main Street\nSpringfield');
    });

    it('removes empty lines when entire line has no data', () => {
      const template = '{street}\n{town}\n{region}\n{postalCode}';
      const addressData = {
        street: '123 Main Street',
        town: null,
        region: null,
        postalCode: '90210'
      };

      const result = replacePlaceholders(template, addressData);
      expect(result).toBe('123 Main Street\n90210');
    });

    it('preserves template structure with partial data', () => {
      const template = '{street}\n{town}, {region}';
      const addressData = {
        street: '123 Main Street',
        town: 'Springfield',
        region: null
      };

      const result = replacePlaceholders(template, addressData);
      expect(result).toBe('123 Main Street\nSpringfield');
    });

    it('handles templates with no placeholders', () => {
      const template = 'Static text only';
      const addressData = { street: '123 Main Street' };

      const result = replacePlaceholders(template, addressData);
      expect(result).toBe('Static text only');
    });
  });

  describe('validatePostalCode', () => {
    it('validates US ZIP codes correctly', () => {
      expect(validatePostalCode('90210', mockAddressFormats.US.postalCodeFormat)).toBe(true);
      expect(validatePostalCode('90210-1234', mockAddressFormats.US.postalCodeFormat)).toBe(true);
      expect(validatePostalCode('9021', mockAddressFormats.US.postalCodeFormat)).toBe(false);
      expect(validatePostalCode('ABCDE', mockAddressFormats.US.postalCodeFormat)).toBe(false);
    });

    it('validates UK postcodes correctly', () => {
      expect(validatePostalCode('M1 1AA', mockAddressFormats.GB.postalCodeFormat)).toBe(true);
      expect(validatePostalCode('SW1A 1AA', mockAddressFormats.GB.postalCodeFormat)).toBe(true);
      expect(validatePostalCode('M11AA', mockAddressFormats.GB.postalCodeFormat)).toBe(true);
      expect(validatePostalCode('12345', mockAddressFormats.GB.postalCodeFormat)).toBe(false);
      expect(validatePostalCode('ABC', mockAddressFormats.GB.postalCodeFormat)).toBe(false);
    });

    it('validates Canadian postal codes correctly', () => {
      expect(validatePostalCode('K1A 0A6', mockAddressFormats.CA.postalCodeFormat)).toBe(true);
      expect(validatePostalCode('K1A0A6', mockAddressFormats.CA.postalCodeFormat)).toBe(true);
      expect(validatePostalCode('12345', mockAddressFormats.CA.postalCodeFormat)).toBe(false);
      expect(validatePostalCode('ABCDEF', mockAddressFormats.CA.postalCodeFormat)).toBe(false);
    });

    it('handles empty or null postal codes', () => {
      expect(validatePostalCode('', mockAddressFormats.US.postalCodeFormat)).toBe(false);
      expect(validatePostalCode(null as any, mockAddressFormats.US.postalCodeFormat)).toBe(false);
      expect(validatePostalCode(undefined as any, mockAddressFormats.US.postalCodeFormat)).toBe(false);
    });

    it('handles invalid regex patterns gracefully', () => {
      expect(validatePostalCode('90210', '[invalid-regex')).toBe(false);
      expect(validatePostalCode('90210', null as any)).toBe(false);
      expect(validatePostalCode('90210', '')).toBe(false);
    });

    it('returns true for any format when no pattern provided', () => {
      expect(validatePostalCode('anything', '^.+$')).toBe(true);
      expect(validatePostalCode('123', '^.+$')).toBe(true);
      expect(validatePostalCode('ABC-XYZ', '^.+$')).toBe(true);
    });
  });

  describe('formatAddressForDisplay', () => {
    it('formats complete address using US template', () => {
      const result = formatAddressForDisplay(
        mockAddressData.complete,
        mockAddressFormats.US.addressTemplate
      );

      expect(result).toBe(
        '123 Main Street\nApt 2B\nSpringfield, California 90210\nUnited States'
      );
    });

    it('formats partial address using UK template', () => {
      const addressData = {
        streetAddress1: '456 Oak Avenue',
        streetAddress2: null,
        town: 'Manchester',
        region: null,
        postalCode: 'M1 1AA',
        country: 'United Kingdom'
      };

      const result = formatAddressForDisplay(
        addressData,
        mockAddressFormats.GB.addressTemplate
      );

      expect(result).toBe('456 Oak Avenue\nManchester\nM1 1AA\nUnited Kingdom');
    });

    it('handles minimal address data', () => {
      const result = formatAddressForDisplay(
        mockAddressData.minimal,
        '{street}\n{town}\n{country}'
      );

      expect(result).toBe('789 Pine Road\nGeneric Country');
    });

    it('returns empty string for completely empty address', () => {
      const emptyAddress = {
        streetAddress1: null,
        streetAddress2: null,
        town: null,
        region: null,
        postalCode: null,
        country: null
      };

      const result = formatAddressForDisplay(emptyAddress, '{street}\n{town}');
      expect(result).toBe('');
    });

    it('handles null or undefined template', () => {
      const result = formatAddressForDisplay(mockAddressData.complete, null as any);
      expect(result).toBe('');

      const result2 = formatAddressForDisplay(mockAddressData.complete, undefined as any);
      expect(result2).toBe('');
    });
  });

  describe('getCountryFieldLabels', () => {
    it('returns US-specific field labels', () => {
      const labels = getCountryFieldLabels(mockAddressFormats.US);

      expect(labels).toEqual({
        postalCodeLabel: 'ZIP Code',
        regionLabel: 'State',
        townLabel: 'City'
      });
    });

    it('returns UK-specific field labels', () => {
      const labels = getCountryFieldLabels(mockAddressFormats.GB);

      expect(labels).toEqual({
        postalCodeLabel: 'Postcode',
        regionLabel: 'County',
        townLabel: 'Town/City'
      });
    });

    it('returns Canadian field labels', () => {
      const labels = getCountryFieldLabels(mockAddressFormats.CA);

      expect(labels).toEqual({
        postalCodeLabel: 'Postal Code',
        regionLabel: 'Province',
        townLabel: 'City'
      });
    });

    it('returns default labels when format is null or missing labels', () => {
      const labels = getCountryFieldLabels(null as any);

      expect(labels).toEqual({
        postalCodeLabel: 'Postal Code',
        regionLabel: 'Region',
        townLabel: 'City'
      });
    });

    it('handles partial address format objects', () => {
      const partialFormat = {
        postalCodeLabel: 'Custom Code',
        regionLabel: undefined,
        townLabel: null
      };

      const labels = getCountryFieldLabels(partialFormat as any);

      expect(labels).toEqual({
        postalCodeLabel: 'Custom Code',
        regionLabel: 'Region',
        townLabel: 'City'
      });
    });
  });

  describe('sanitizeAddressComponent', () => {
    it('trims whitespace from components', () => {
      expect(sanitizeAddressComponent('  123 Main St  ')).toBe('123 Main St');
      expect(sanitizeAddressComponent('\n\tTest\n\t')).toBe('Test');
    });

    it('returns null for empty or whitespace-only strings', () => {
      expect(sanitizeAddressComponent('')).toBeNull();
      expect(sanitizeAddressComponent('   ')).toBeNull();
      expect(sanitizeAddressComponent('\n\t')).toBeNull();
    });

    it('returns null for null or undefined input', () => {
      expect(sanitizeAddressComponent(null)).toBeNull();
      expect(sanitizeAddressComponent(undefined)).toBeNull();
    });

    it('preserves valid non-empty strings', () => {
      expect(sanitizeAddressComponent('123 Main Street')).toBe('123 Main Street');
      expect(sanitizeAddressComponent('A')).toBe('A');
    });

    it('handles mixed content correctly', () => {
      expect(sanitizeAddressComponent(' Mixed  content  ')).toBe('Mixed  content');
      expect(sanitizeAddressComponent('Line1\nLine2')).toBe('Line1\nLine2');
    });
  });

  describe('buildFormattedAddress', () => {
    it('builds complete formatted address with all components', () => {
      const addressData = {
        streetAddress1: '123 Main Street',
        streetAddress2: 'Suite 100',
        town: 'Springfield',
        region: 'California',
        postalCode: '90210',
        country: 'United States'
      };

      const result = buildFormattedAddress(addressData);

      expect(result).toEqual({
        street: '123 Main Street\nSuite 100',
        town: 'Springfield',
        region: 'California',
        postalCode: '90210',
        country: 'United States'
      });
    });

    it('builds address with only street address 1', () => {
      const addressData = {
        streetAddress1: '123 Main Street',
        streetAddress2: null,
        town: 'Springfield',
        region: 'California',
        postalCode: '90210',
        country: 'United States'
      };

      const result = buildFormattedAddress(addressData);

      expect(result).toEqual({
        street: '123 Main Street',
        town: 'Springfield',
        region: 'California',
        postalCode: '90210',
        country: 'United States'
      });
    });

    it('handles missing components gracefully', () => {
      const addressData = {
        streetAddress1: '123 Main Street',
        streetAddress2: null,
        town: null,
        region: 'California',
        postalCode: null,
        country: 'United States'
      };

      const result = buildFormattedAddress(addressData);

      expect(result).toEqual({
        street: '123 Main Street',
        town: null,
        region: 'California',
        postalCode: null,
        country: 'United States'
      });
    });

    it('returns object with null values for completely empty address', () => {
      const addressData = {
        streetAddress1: null,
        streetAddress2: null,
        town: null,
        region: null,
        postalCode: null,
        country: null
      };

      const result = buildFormattedAddress(addressData);

      expect(result).toEqual({
        street: null,
        town: null,
        region: null,
        postalCode: null,
        country: null
      });
    });

    it('sanitizes address components', () => {
      const addressData = {
        streetAddress1: '  123 Main Street  ',
        streetAddress2: '   ',
        town: '\n\tSpringfield\t\n',
        region: '',
        postalCode: '90210',
        country: '  United States  '
      };

      const result = buildFormattedAddress(addressData);

      expect(result).toEqual({
        street: '123 Main Street',
        town: 'Springfield',
        region: null,
        postalCode: '90210',
        country: 'United States'
      });
    });
  });

  describe('Integration scenarios', () => {
    it('completes full address formatting workflow', () => {
      // 1. Parse template
      const template = mockAddressFormats.US.addressTemplate;
      const placeholders = parseAddressTemplate(template);
      expect(placeholders).toContain('street');
      expect(placeholders).toContain('postalCode');

      // 2. Build formatted address
      const formattedAddress = buildFormattedAddress(mockAddressData.complete);

      // 3. Replace placeholders
      const displayAddress = replacePlaceholders(template, formattedAddress);

      // 4. Validate postal code
      const isValidPostal = validatePostalCode(
        mockAddressData.complete.postalCode!,
        mockAddressFormats.US.postalCodeFormat
      );

      expect(displayAddress).toContain('123 Main Street');
      expect(displayAddress).toContain('Springfield, California 90210');
      expect(isValidPostal).toBe(true);
    });

    it('handles edge case with minimal data', () => {
      const template = '{street}\n{town}\n{country}';
      const addressData = {
        streetAddress1: 'Test Street',
        streetAddress2: null,
        town: null,
        region: null,
        postalCode: null,
        country: 'Test Country'
      };

      const formattedAddress = buildFormattedAddress(addressData);
      const displayAddress = replacePlaceholders(template, formattedAddress);
      const labels = getCountryFieldLabels(null);

      expect(displayAddress).toBe('Test Street\nTest Country');
      expect(labels.postalCodeLabel).toBe('Postal Code');
    });
  });
});
