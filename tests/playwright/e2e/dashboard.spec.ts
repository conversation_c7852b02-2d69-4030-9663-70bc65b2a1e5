import { test, expect } from '../fixtures/test-setup'

test.describe('Dashboard Tests', () => {
  test.beforeEach(async ({ page, testHelpers }) => {
    // Login before each test
    await testHelpers.login('<EMAIL>', 'password')
    await page.goto('/dashboard')
  })

  test('should display dashboard overview', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Dashboard')
    await expect(page.locator('[data-testid="dashboard-overview"]')).toBeVisible()
  })

  test('should show user statistics', async ({ page }) => {
    await expect(page.locator('[data-testid="user-stats"]')).toBeVisible()
    await expect(page.locator('[data-testid="total-users"]')).toBeVisible()
    await expect(page.locator('[data-testid="active-users"]')).toBeVisible()
  })

  test('should display recent activity', async ({ page }) => {
    await expect(page.locator('[data-testid="recent-activity"]')).toBeVisible()
    await expect(page.locator('[data-testid="activity-list"]')).toBeVisible()
  })

  test('should have working navigation links', async ({ page }) => {
    // Test navigation to different sections
    await page.click('[data-testid="nav-users"]')
    await expect(page.url()).toContain('/users')

    await page.click('[data-testid="nav-settings"]')
    await expect(page.url()).toContain('/settings')
  })

  test('should handle logout', async ({ page, testHelpers }) => {
    await testHelpers.logout()
    await expect(page.url()).toContain('/login')
  })
})

test.describe('Dashboard - Mobile View', () => {
  test.beforeEach(async ({ page, testHelpers }) => {
    await testHelpers.login('<EMAIL>', 'password')
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/dashboard')
  })

  test('should display mobile-friendly dashboard', async ({ page }) => {
    await expect(page.locator('[data-testid="mobile-dashboard"]')).toBeVisible()
    await expect(page.locator('[data-testid="mobile-nav"]')).toBeVisible()
  })

  test('should have working mobile navigation', async ({ page }) => {
    await page.click('[data-testid="mobile-menu-button"]')
    await expect(page.locator('[data-testid="mobile-nav-menu"]')).toBeVisible()
  })
})