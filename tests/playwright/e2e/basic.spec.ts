import { test, expect } from '@playwright/test'

test.describe('NWA Member Portal - Basic Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should load the homepage', async ({ page }) => {
    await expect(page.locator('body')).toContainText('NWA Member Portal')
  })

  test('should have working navigation', async ({ page }) => {
    // Check if main navigation elements exist
    await expect(page.locator('nav')).toBeVisible()
    await expect(page.locator('[href="/dashboard"]')).toBeVisible()
    await expect(page.locator('[href="/settings"]')).toBeVisible()
  })

  test('should display login page when accessing /login', async ({ page }) => {
    await page.goto('/login')
    await expect(page.locator('body')).toContainText('Sign in to your account')
  })

  test('should display dashboard when logged in', async ({ page }) => {
    // This test assumes you have a test user or can bypass authentication
    // You may need to modify this based on your authentication setup
    await page.goto('/dashboard')
    // Check if dashboard loads (may redirect to login if not authenticated)
    await expect(page.url()).toContain('/dashboard')
  })
})

test.describe('Settings Page Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/settings')
  })

  test('should load the settings page', async ({ page }) => {
    await expect(page.locator('body')).toContainText('System Settings')
  })

  test('should have tabs for different settings sections', async ({ page }) => {
    await expect(page.locator('text=Remote Servers')).toBeVisible()
    await expect(page.locator('text=Local Permissions')).toBeVisible()
    await expect(page.locator('text=Security Audit')).toBeVisible()
  })

  test('should have working tab navigation', async ({ page }) => {
    // Click on Local Permissions tab
    await page.click('text=Local Permissions')

    // Check if the tab content loads
    await expect(page.locator('text=User Permission Management')).toBeVisible()
    await expect(page.locator('text=Select a user to manage their roles and permissions')).toBeVisible()
  })
})

test.describe('API Tests', () => {
  test('should return user data from /api/users', async ({ request }) => {
    const response = await request.get('/api/users')
    expect(response.ok()).toBeTruthy()
    expect(response.status()).toBe(200)

    const data = await response.json()
    expect(data).toHaveProperty('users')
  })

  test('should return countries data from /api/countries', async ({ request }) => {
    const response = await request.get('/api/countries')
    expect(response.ok()).toBeTruthy()
    expect(response.status()).toBe(200)

    const data = await response.json()
    expect(data).toHaveProperty('countries')
  })

  test('should return cities data from /api/cities', async ({ request }) => {
    const response = await request.get('/api/cities')
    expect(response.ok()).toBeTruthy()
    expect(response.status()).toBe(200)

    const data = await response.json()
    expect(data).toHaveProperty('cities')
  })
})