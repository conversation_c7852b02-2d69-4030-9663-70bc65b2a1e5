import { Page, BrowserContext } from '@playwright/test'

export class TestHelpers {
  constructor(private page: Page, private context: BrowserContext) {}

  async login(email: string, password: string) {
    await this.page.goto('/login')
    await this.page.fill('input[type="email"]', email)
    await this.page.fill('input[type="password"]', password)
    await this.page.click('button[type="submit"]')
  }

  async isAuthenticated(): Promise<boolean> {
    return await this.context.cookies().then(cookies => {
      return cookies.some(cookie => cookie.name.includes('next-auth'))
    })
  }

  async logout() {
    await this.page.click('[data-testid="user-menu"]')
    await this.page.click('[data-testid="logout-button"]')
  }

  async waitForApiResponse(url: string, method = 'GET') {
    await this.page.waitForResponse(response =>
      response.url().includes(url) && response.request().method() === method
    )
  }

  async checkPageTitle(title: string) {
    await this.page.waitForFunction(
      (expectedTitle: string) => document.title.includes(expectedTitle),
      title
    )
  }

  async fillFormField(selector: string, value: string) {
    await this.page.fill(selector, value)
  }

  async selectDropdownOption(selector: string, option: string) {
    await this.page.selectOption(selector, option)
  }

  async checkElementExists(selector: string) {
    return await this.page.locator(selector).isVisible()
  }

  async checkElementText(selector: string, text: string) {
    await this.page.locator(selector).filter({ hasText: text }).isVisible()
  }

  async waitForElement(selector: string, timeout = 10000) {
    await this.page.waitForSelector(selector, { timeout })
  }

  async waitForElementToDisappear(selector: string, timeout = 10000) {
    await this.page.waitForSelector(selector, { state: 'detached', timeout })
  }

  async takeScreenshot(name: string) {
    await this.page.screenshot({ path: `test-results/screenshots/${name}.png` })
  }

  async checkTableRowCount(selector: string, expectedCount: number) {
    const rows = await this.page.locator(selector).count()
    return rows === expectedCount
  }

  async checkAlertText(expectedText: string) {
    this.page.on('dialog', async dialog => {
      expect(dialog.message()).toContain(expectedText)
      await dialog.accept()
    })
  }
}