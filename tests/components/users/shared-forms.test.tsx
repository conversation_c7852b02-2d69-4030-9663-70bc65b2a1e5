import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { UserBasicInfoForm } from '../../../src/components/users/manage/shared/UserBasicInfoForm';
import { UserContactForm } from '../../../src/components/users/manage/shared/UserContactForm';
import { UserIdentificationForm } from '../../../src/components/users/manage/shared/UserIdentificationForm';
import { UserPositionsForm } from '../../../src/components/users/manage/shared/UserPositionsForm';
import { UserTreatiesForm } from '../../../src/components/users/manage/shared/UserTreatiesForm';
import { UserOrdinancesForm } from '../../../src/components/users/manage/shared/UserOrdinancesForm';

// Mock toast notifications
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

// Mock API calls
global.fetch = jest.fn();

const mockBasicUserData = {
  firstName: 'John',
  surname: 'Doe',
  dob: '1980-01-01',
  email: '<EMAIL>',
  personalEmail: '<EMAIL>',
  nwaEmail: '<EMAIL>',
  phone: '******-0123',
  mobile: '******-0456',
  bio: 'Test biography',
};

const mockContactData = {
  streetAddress1: '123 Main Street',
  streetAddress2: 'Apt 4B',
  town: 'Springfield',
  city: 'Los Angeles',
  country: 'United States',
  postalCode: '90210',
  regionId: '1',
  regionText: 'California',
};

const mockIdentificationData = {
  peaceAmbassadorNumber: 'PA-001',
  identifications: [
    {
      id: 'id-1',
      type: 'drivers_license',
      number: 'DL123456789',
      issuedDate: '2020-01-01',
      expiryDate: '2025-01-01',
    },
  ],
  treatyNumbers: [
    {
      id: 'treaty-1',
      treatyTypeId: 'treaty-type-1',
      treatyNumber: 'TR-001',
    },
  ],
};

const mockPositionData = {
  titleId: 'title-1',
  positionId: 'position-1',
};

describe('Shared User Form Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({}),
    });
  });

  describe('UserBasicInfoForm', () => {
    test('renders in create mode', () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserBasicInfoForm
          mode="create"
          data={mockBasicUserData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      expect(screen.getByDisplayValue('John')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /create user/i })).toBeInTheDocument();
    });

    test('renders in update mode', () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserBasicInfoForm
          mode="update"
          data={mockBasicUserData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      expect(screen.getByDisplayValue('John')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /update user/i })).toBeInTheDocument();
    });

    test('calls onChange when input values change', () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserBasicInfoForm
          mode="create"
          data={mockBasicUserData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      const firstNameInput = screen.getByDisplayValue('John');
      fireEvent.change(firstNameInput, { target: { value: 'Jane' } });

      expect(mockOnChange).toHaveBeenCalledWith({
        ...mockBasicUserData,
        firstName: 'Jane',
      });
    });

    test('validates required fields', async () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserBasicInfoForm
          mode="create"
          data={{ ...mockBasicUserData, firstName: '', surname: '', email: '' }}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      const submitButton = screen.getByRole('button', { name: /create user/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockOnSave).not.toHaveBeenCalled();
      });
    });

    test('calls onSave when form is submitted with valid data', async () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserBasicInfoForm
          mode="create"
          data={mockBasicUserData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      const submitButton = screen.getByRole('button', { name: /create user/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockOnSave).toHaveBeenCalledWith(mockBasicUserData);
      });
    });
  });

  describe('UserContactForm', () => {
    test('renders contact form fields', () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserContactForm
          mode="create"
          data={mockContactData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      expect(screen.getByDisplayValue('123 Main Street')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Apt 4B')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Springfield')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Los Angeles')).toBeInTheDocument();
      expect(screen.getByDisplayValue('United States')).toBeInTheDocument();
      expect(screen.getByDisplayValue('90210')).toBeInTheDocument();
    });

    test('handles country selection with autocomplete', async () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve([
          { id: '1', name: 'United States' },
          { id: '2', name: 'United Kingdom' },
        ]),
      });

      render(
        <UserContactForm
          mode="create"
          data={mockContactData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      const countryInput = screen.getByDisplayValue('United States');
      fireEvent.change(countryInput, { target: { value: 'United K' } });

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/countries?q=United%20K')
        );
      });
    });

    test('updates data when address fields change', () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserContactForm
          mode="update"
          data={mockContactData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      const streetInput = screen.getByDisplayValue('123 Main Street');
      fireEvent.change(streetInput, { target: { value: '456 Oak Avenue' } });

      expect(mockOnChange).toHaveBeenCalledWith({
        ...mockContactData,
        streetAddress1: '456 Oak Avenue',
      });
    });
  });

  describe('UserIdentificationForm', () => {
    test('renders identification fields', () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserIdentificationForm
          mode="create"
          data={mockIdentificationData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      expect(screen.getByDisplayValue('PA-001')).toBeInTheDocument();
      expect(screen.getByDisplayValue('DL123456789')).toBeInTheDocument();
      expect(screen.getByDisplayValue('TR-001')).toBeInTheDocument();
    });

    test('allows adding new identification documents', async () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserIdentificationForm
          mode="create"
          data={mockIdentificationData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      const addButton = screen.getByRole('button', { name: /add identification/i });
      fireEvent.click(addButton);

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith({
          ...mockIdentificationData,
          identifications: [
            ...mockIdentificationData.identifications,
            expect.objectContaining({
              type: '',
              number: '',
              issuedDate: '',
              expiryDate: '',
            }),
          ],
        });
      });
    });

    test('allows removing identification documents', async () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserIdentificationForm
          mode="update"
          data={mockIdentificationData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      const removeButton = screen.getByRole('button', { name: /remove/i });
      fireEvent.click(removeButton);

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith({
          ...mockIdentificationData,
          identifications: [],
        });
      });
    });
  });

  describe('UserPositionsForm', () => {
    const mockTitles = [
      { id: 'title-1', name: 'Ambassador', description: 'Ambassador Title', isActive: true },
      { id: 'title-2', name: 'Trade Representative', description: 'Trade Rep', isActive: true },
    ];

    const mockPositions = [
      { id: 'position-1', title: 'Senior Ambassador', description: 'Senior Position', level: 1, parentId: null, isActive: true },
      { id: 'position-2', title: 'Junior Ambassador', description: 'Junior Position', level: 2, parentId: 'position-1', isActive: true },
    ];

    beforeEach(() => {
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockTitles),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ positions: mockPositions }),
        });
    });

    test('renders position selection fields', async () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserPositionsForm
          mode="create"
          data={mockPositionData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Ambassadorial Title')).toBeInTheDocument();
        expect(screen.getByText('Position')).toBeInTheDocument();
      });
    });

    test('fetches positions when title is selected', async () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserPositionsForm
          mode="create"
          data={{ titleId: '', positionId: '' }}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      await waitFor(() => {
        const titleSelect = screen.getByRole('combobox', { name: /ambassadorial title/i });
        fireEvent.change(titleSelect, { target: { value: 'title-1' } });
      });

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/positions/positions?titleId=title-1')
      );
    });
  });

  describe('UserTreatiesForm', () => {
    test('renders treaty management interface', () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserTreatiesForm
          mode="create"
          data={{}}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      expect(screen.getByText(/treaty management/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /create treaty/i })).toBeInTheDocument();
    });

    test('allows creating new treaties', async () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserTreatiesForm
          mode="create"
          data={{}}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      const createButton = screen.getByRole('button', { name: /create treaty/i });
      fireEvent.click(createButton);

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalled();
      });
    });
  });

  describe('UserOrdinancesForm', () => {
    const mockOrdinances = [
      { id: 'ord-1', name: 'Trade Ordinance', description: 'Trade related ordinance' },
      { id: 'ord-2', name: 'Peace Ordinance', description: 'Peace related ordinance' },
    ];

    beforeEach(() => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ ordinanceTypes: mockOrdinances }),
      });
    });

    test('renders ordinance selection interface', async () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserOrdinancesForm
          mode="create"
          data={{ selectedOrdinances: [] }}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      await waitFor(() => {
        expect(screen.getByText(/select ordinance/i)).toBeInTheDocument();
      });
    });

    test('allows selecting ordinances', async () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserOrdinancesForm
          mode="create"
          data={{ selectedOrdinances: [] }}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      await waitFor(() => {
        const select = screen.getByRole('combobox');
        fireEvent.change(select, { target: { value: 'ord-1' } });
      });

      const addButton = screen.getByRole('button', { name: /add ordinance/i });
      fireEvent.click(addButton);

      expect(mockOnChange).toHaveBeenCalledWith({
        selectedOrdinances: ['ord-1'],
      });
    });

    test('allows removing selected ordinances', async () => {
      const mockOnChange = jest.fn();
      const mockOnSave = jest.fn();

      render(
        <UserOrdinancesForm
          mode="update"
          data={{ selectedOrdinances: ['ord-1'] }}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      await waitFor(() => {
        const removeButton = screen.getByRole('button', { name: /remove/i });
        fireEvent.click(removeButton);
      });

      expect(mockOnChange).toHaveBeenCalledWith({
        selectedOrdinances: [],
      });
    });
  });

  describe('Mode Prop Functionality', () => {
    test('all components accept mode prop correctly', () => {
      const commonProps = {
        data: {},
        onChange: jest.fn(),
        onSave: jest.fn(),
      };

      // Test that both modes render without errors
      expect(() => render(<UserBasicInfoForm mode="create" {...commonProps} />)).not.toThrow();
      expect(() => render(<UserBasicInfoForm mode="update" {...commonProps} />)).not.toThrow();
      
      expect(() => render(<UserContactForm mode="create" {...commonProps} />)).not.toThrow();
      expect(() => render(<UserContactForm mode="update" {...commonProps} />)).not.toThrow();
      
      expect(() => render(<UserIdentificationForm mode="create" {...commonProps} />)).not.toThrow();
      expect(() => render(<UserIdentificationForm mode="update" {...commonProps} />)).not.toThrow();
      
      expect(() => render(<UserPositionsForm mode="create" {...commonProps} />)).not.toThrow();
      expect(() => render(<UserPositionsForm mode="update" {...commonProps} />)).not.toThrow();
      
      expect(() => render(<UserTreatiesForm mode="create" {...commonProps} />)).not.toThrow();
      expect(() => render(<UserTreatiesForm mode="update" {...commonProps} />)).not.toThrow();
      
      expect(() => render(<UserOrdinancesForm mode="create" {...commonProps} />)).not.toThrow();
      expect(() => render(<UserOrdinancesForm mode="update" {...commonProps} />)).not.toThrow();
    });
  });
});
