import { PermissionSyncService } from '@/lib/services/permission-sync-service'

// Mock the remote server permissions fetch
jest.mock('@/app/actions/remote-servers', () => ({
  fetchRemoteServerPermissions: jest.fn()
}))

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    remoteServer: {
      findUnique: jest.fn()
    },
    remote_server_permissions: {
      upsert: jest.fn(),
      updateMany: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      count: jest.fn(),
      deleteMany: jest.fn()
    }
  }
}))

// Import the mocked function and prisma
import { fetchRemoteServerPermissions } from '@/app/actions/remote-servers'
import { prisma } from '@/lib/prisma'

describe('PermissionSyncService Integration Tests', () => {
  let syncService: PermissionSyncService

  beforeEach(() => {
    syncService = new PermissionSyncService()
    jest.clearAllMocks()
  })

  afterEach(() => {
    // Clear all mocks
    jest.clearAllMocks()
  })

  describe('syncServerPermissions', () => {
    test('should successfully sync permissions to database', async () => {
      const mockServerId = 'test-server-123'
      const mockServer = {
        id: mockServerId,
        name: 'Test Server',
        url: 'https://test.example.com',
        apiKey: 'test-api-key',
        isActive: true
      }

      // Mock the server fetch
      ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(mockServer)

      // Mock successful permissions fetch
      const mockPermissionsData = {
        success: true,
        data: {
          permissions: [
            { name: 'view_videos', description: 'View video content' },
            { name: 'upload_ebooks', description: 'Upload ebooks' },
            { name: 'create_podcast_links', description: 'Create podcast links' }
          ],
          roles: [
            { name: 'editor', description: 'Editor role', permissions: ['view_videos', 'upload_ebooks'] }
          ]
        }
      }

      ;(fetchRemoteServerPermissions as jest.Mock).mockResolvedValue(mockPermissionsData)

      // Mock successful database operations
      ;(prisma.remote_server_permissions.upsert as jest.Mock).mockImplementation((args: any) => 
        Promise.resolve({
          id: 'test-id',
          remote_server_id: mockServerId,
          permission_name: args.create.permission_name,
          permission_description: args.create.permission_description,
          is_active: true,
          last_synced_at: new Date()
        })
      )

      // Execute sync
      const result = await syncService.syncServerPermissions(mockServerId)

      // Verify result
      expect(result.success).toBe(true)
      expect(result.permissionsSynced).toBe(3)
      expect(result.rolesSynced).toBe(1)
      expect(result.errors).toHaveLength(0)

      // Verify database operations were called
      expect(prisma.remote_server_permissions.upsert).toHaveBeenCalledTimes(3)
      expect(prisma.remote_server_permissions.updateMany).toHaveBeenCalled()
    })

    test('should handle permission updates and deactivations', async () => {
      const mockServerId = 'test-server-456'
      const mockServer = {
        id: mockServerId,
        name: 'Test Server',
        url: 'https://test.example.com',
        apiKey: 'test-api-key',
        isActive: true
      }

      // Mock the server fetch
      ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(mockServer)

      // First sync with some permissions
      const firstSyncData = {
        success: true,
        data: {
          permissions: [
            { name: 'view_videos', description: 'View video content' },
            { name: 'upload_ebooks', description: 'Upload ebooks' }
          ],
          roles: []
        }
      }

      ;(fetchRemoteServerPermissions as jest.Mock).mockResolvedValueOnce(firstSyncData)

      await syncService.syncServerPermissions(mockServerId)

      // Second sync with different permissions (one removed, one added, one updated)
      const secondSyncData = {
        success: true,
        data: {
          permissions: [
            { name: 'view_videos', description: 'View video content (updated)' },
            { name: 'create_podcast_links', description: 'Create podcast links' }
          ],
          roles: []
        }
      }

      ;(fetchRemoteServerPermissions as jest.Mock).mockResolvedValueOnce(secondSyncData)

      const result = await syncService.syncServerPermissions(mockServerId)

      // Verify result
      expect(result.success).toBe(true)
      expect(result.permissionsSynced).toBe(2)

      // Verify database state
      const allPermissions = await prisma.remote_server_permissions.findMany({
        where: { remote_server_id: mockServerId },
        orderBy: { permission_name: 'asc' }
      })

      expect(allPermissions).toHaveLength(3) // 2 active + 1 inactive

      const activePermissions = allPermissions.filter(p => p.is_active)
      const inactivePermissions = allPermissions.filter(p => !p.is_active)

      expect(activePermissions).toHaveLength(2)
      expect(inactivePermissions).toHaveLength(1)

      // Verify active permissions
      expect(activePermissions.map(p => p.permission_name)).toEqual([
        'create_podcast_links',
        'view_videos'
      ])

      // Verify updated description
      const viewVideosPermission = activePermissions.find(p => p.permission_name === 'view_videos')
      expect(viewVideosPermission?.permission_description).toBe('View video content (updated)')

      // Verify inactive permission
      expect(inactivePermissions[0].permission_name).toBe('upload_ebooks')
    })

    test('should handle server not found', async () => {
      ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(null)

      const result = await syncService.syncServerPermissions('non-existent-server')

      expect(result.success).toBe(false)
      expect(result.errors).toContain('Remote server not found')
      expect(fetchRemoteServerPermissions).not.toHaveBeenCalled()
    })

    test('should handle fetch errors', async () => {
      const mockServer = {
        id: 'test-server-789',
        name: 'Test Server',
        url: 'https://test.example.com',
        apiKey: 'test-api-key',
        isActive: true
      }

      ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(mockServer)
      ;(fetchRemoteServerPermissions as jest.Mock).mockResolvedValue({
        success: false,
        error: 'Connection failed'
      })

      const result = await syncService.syncServerPermissions('test-server-789')

      expect(result.success).toBe(false)
      expect(result.errors).toContain('Connection failed')
    })
  })

  describe('getSyncStats', () => {
    test('should return correct sync statistics', async () => {
      const mockServerId = 'stats-test-server'

      // Create test permissions
      await prisma.remote_server_permissions.createMany({
        data: [
          {
            remote_server_id: mockServerId,
            permission_name: 'active_perm_1',
            permission_description: 'Active permission 1',
            is_active: true,
            last_synced_at: new Date()
          },
          {
            remote_server_id: mockServerId,
            permission_name: 'active_perm_2',
            permission_description: 'Active permission 2',
            is_active: true,
            last_synced_at: new Date()
          },
          {
            remote_server_id: mockServerId,
            permission_name: 'inactive_perm',
            permission_description: 'Inactive permission',
            is_active: false,
            last_synced_at: new Date()
          }
        ]
      })

      const stats = await syncService.getSyncStats(mockServerId)

      expect(stats).toEqual({
        totalPermissions: 3,
        activePermissions: 2,
        inactivePermissions: 1,
        lastSync: expect.any(Date)
      })
    })

    test('should return null for non-existent server', async () => {
      const stats = await syncService.getSyncStats('non-existent-server')
      expect(stats).toBeNull()
    })
  })

  describe('getLastSyncTime', () => {
    test('should return last sync time', async () => {
      const mockServerId = 'sync-time-test-server'
      const testDate = new Date('2024-01-01T00:00:00Z')

      await prisma.remote_server_permissions.createMany({
        data: [
          {
            remote_server_id: mockServerId,
            permission_name: 'perm_1',
            permission_description: 'Permission 1',
            is_active: true,
            last_synced_at: testDate
          },
          {
            remote_server_id: mockServerId,
            permission_name: 'perm_2',
            permission_description: 'Permission 2',
            is_active: true,
            last_synced_at: new Date('2024-01-02T00:00:00Z')
          }
        ]
      })

      const lastSync = await syncService.getLastSyncTime(mockServerId)

      // Should return the most recent sync time
      expect(lastSync).toEqual(new Date('2024-01-02T00:00:00Z'))
    })

    test('should return null for server with no permissions', async () => {
      const lastSync = await syncService.getLastSyncTime('server-with-no-perms')
      expect(lastSync).toBeNull()
    })
  })
})