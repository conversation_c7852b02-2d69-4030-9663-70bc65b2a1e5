import { fetchRemoteServerPermissions } from '@/app/actions/remote-servers'

// Mock the prisma module
jest.mock('@/lib/prisma', () => ({
  prisma: {
    remoteServer: {
      findUnique: jest.fn(),
    },
  },
}))

// Import the mocked prisma to access the mock functions
import { prisma } from '@/lib/prisma'

// Mock fetch globally
const mockFetch = jest.fn()
global.fetch = mockFetch

describe('Permission Synchronization Service Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFetch.mockReset()
  })

  describe('fetchRemoteServerPermissions', () => {
    test('should successfully fetch and parse permissions from remote server', async () => {
      // Mock the remote server data
      const mockRemoteServer = {
        id: 'test-sync-server',
        name: 'Test Sync Server',
        url: 'https://test.example.com',
        apiKey: 'test-api-key-123',
      }
      
      ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(mockRemoteServer)

      // Mock successful response from remote server
      const mockPermissionsData = {
        permissions: [
          { name: 'view_documents', description: 'View documents permission' },
          { name: 'edit_documents', description: 'Edit documents permission' },
          { name: 'delete_documents', description: 'Delete documents permission' },
        ],
        roles: [
          {
            name: 'editor',
            description: 'Editor role',
            permissions: ['view_documents', 'edit_documents']
          },
          {
            name: 'admin',
            description: 'Administrator role',
            permissions: ['view_documents', 'edit_documents', 'delete_documents']
          }
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockPermissionsData,
      })

      const result = await fetchRemoteServerPermissions(mockRemoteServer.id)

      expect(result.success).toBe(true)
      expect(result.data).toEqual({
        permissions: [
          { name: 'view_documents', description: 'View documents permission' },
          { name: 'edit_documents', description: 'Edit documents permission' },
          { name: 'delete_documents', description: 'Delete documents permission' },
        ],
        roles: [
          {
            name: 'editor',
            description: 'Editor role',
            permissions: ['view_documents', 'edit_documents']
          },
          {
            name: 'admin',
            description: 'Administrator role',
            permissions: ['view_documents', 'edit_documents', 'delete_documents']
          }
        ]
      })

      // Verify fetch was called with correct parameters
      expect(mockFetch).toHaveBeenCalledWith(
        'https://test.example.com/api/permissions',
        {
          headers: {
            'Authorization': 'Bearer test-api-key-123',
            'Content-Type': 'application/json'
          }
        }
      )
    })

    test('should handle string-based permission format from remote server', async () => {
      const mockRemoteServer = {
        id: 'test-string-perms-server',
        name: 'Test String Perms Server',
        url: 'https://string.example.com',
        apiKey: 'string-api-key-123',
      }
      
      ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(mockRemoteServer)

      // Mock response with string permissions
      const mockStringPermissionsData = {
        permissions: ['view_documents', 'edit_documents', 'delete_documents'],
        roles: ['editor', 'admin']
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockStringPermissionsData,
      })

      const result = await fetchRemoteServerPermissions(mockRemoteServer.id)

      expect(result.success).toBe(true)
      expect(result.data).toEqual({
        permissions: [
          { name: 'view_documents', description: '' },
          { name: 'edit_documents', description: '' },
          { name: 'delete_documents', description: '' },
        ],
        roles: [
          { name: 'editor', description: '', permissions: [] },
          { name: 'admin', description: '', permissions: [] }
        ]
      })
    })

    test('should return error when remote server not found', async () => {
      ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(null)
      
      const result = await fetchRemoteServerPermissions('non-existent-server-id')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Remote server not found')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    test('should handle remote server connection errors', async () => {
      const mockRemoteServer = {
        id: 'test-error-server',
        name: 'Test Error Server',
        url: 'https://error.example.com',
        apiKey: 'error-api-key-123',
      }
      
      ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(mockRemoteServer)

      // Mock failed response
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 502,
        statusText: 'Bad Gateway',
      })

      const result = await fetchRemoteServerPermissions(mockRemoteServer.id)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Error connecting to remote server')
    })

    test('should handle network errors', async () => {
      const mockRemoteServer = {
        id: 'test-network-error-server',
        name: 'Test Network Error Server',
        url: 'https://network-error.example.com',
        apiKey: 'network-error-api-key-123',
      }
      
      ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(mockRemoteServer)

      // Mock network error
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const result = await fetchRemoteServerPermissions(mockRemoteServer.id)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to fetch permissions')
    })

    test('should handle malformed JSON responses', async () => {
      const mockRemoteServer = {
        id: 'test-malformed-server',
        name: 'Test Malformed Server',
        url: 'https://malformed.example.com',
        apiKey: 'malformed-api-key-123',
      }
      
      ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(mockRemoteServer)

      // Mock successful response with invalid JSON
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => { throw new Error('Invalid JSON') },
      })

      const result = await fetchRemoteServerPermissions(mockRemoteServer.id)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to fetch permissions')
    })
  })

  describe('Permission Validation', () => {
    test('should validate permission names are non-empty strings', async () => {
      const mockRemoteServer = {
        id: 'test-validation-server',
        name: 'Test Validation Server',
        url: 'https://validation.example.com',
        apiKey: 'validation-api-key-123',
      }
      
      ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(mockRemoteServer)

      // Mock response with invalid permission data
      const mockInvalidData = {
        permissions: [
          { name: '', description: 'Empty name' },
          { name: 'valid_permission', description: 'Valid permission' },
          { name: null, description: 'Null name' },
        ],
        roles: []
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockInvalidData,
      })

      const result = await fetchRemoteServerPermissions(mockRemoteServer.id)

      expect(result.success).toBe(true)
      // Should filter out invalid permissions
      expect(result.data.permissions).toEqual([
        { name: 'valid_permission', description: 'Valid permission' }
      ])
    })

    test('should handle mixed permission formats in same response', async () => {
      const mockRemoteServer = {
        id: 'test-mixed-format-server',
        name: 'Test Mixed Format Server',
        url: 'https://mixed.example.com',
        apiKey: 'mixed-api-key-123',
      }
      
      ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(mockRemoteServer)

      // Mock response with mixed permission formats
      const mockMixedData = {
        permissions: [
          'view_documents', // string format
          { name: 'edit_documents', description: 'Edit permission' }, // object format
          'delete_documents', // string format
        ],
        roles: [
          'editor', // string format
          { name: 'admin', description: 'Admin role', permissions: ['view_documents'] } // object format
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockMixedData,
      })

      const result = await fetchRemoteServerPermissions(mockRemoteServer.id)

      expect(result.success).toBe(true)
      expect(result.data).toEqual({
        permissions: [
          { name: 'view_documents', description: '' },
          { name: 'edit_documents', description: 'Edit permission' },
          { name: 'delete_documents', description: '' },
        ],
        roles: [
          { name: 'editor', description: '', permissions: [] },
          { name: 'admin', description: 'Admin role', permissions: ['view_documents'] }
        ]
      })
    })
  })
})