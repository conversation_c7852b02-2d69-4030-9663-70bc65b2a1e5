describe('Nation Treaty Database Schema Validation', () => {
  let prisma: any

  beforeAll(async () => {
    // Use global prisma instance
    prisma = global.prisma
  })

  describe('NationTreaty Model', () => {
    test('should create and retrieve nation treaty with valid data', async () => {
      const nationTreaty = await prisma.nationTreaty.create({
        data: {
          name: 'United Tribe of Aotearoa',
          officialName: 'The United Tribe of Aotearoa',
          description: 'Official diplomatic treaty between the United Tribe of Aotearoa and NWA Alliance',
          status: 'ACTIVE',
          contactEmail: '<EMAIL>',
          contactPhone: '+64-9-123-4567',
          contactAddress: '123 Tribal Way, Auckland, New Zealand',
          website: 'https://aotearoa.example.com',
          emergencyContactName: 'Emergency Services',
          emergencyContactPhone: '+64-9-999-9999',
          notes: 'Primary diplomatic contact for all Aotearoa tribal members',
          documentPath: '/documents/treaties/aotearoa-treaty.pdf',
        },
      })

      expect(nationTreaty.id).toBeDefined()
      expect(nationTreaty.name).toBe('United Tribe of Aotearoa')
      expect(nationTreaty.officialName).toBe('The United Tribe of Aotearoa')
      expect(nationTreaty.status).toBe('ACTIVE')
      expect(nationTreaty.createdAt).toBeInstanceOf(Date)
      expect(nationTreaty.updatedAt).toBeInstanceOf(Date)

      // Clean up
      await prisma.nationTreaty.delete({ where: { id: nationTreaty.id } })
    })

    test('should validate required fields for nation treaty', async () => {
      await expect(
        prisma.nationTreaty.create({
          data: {
            // Missing required 'name' field
            description: 'Test treaty without name',
          },
        })
      ).rejects.toThrow()
    })

    test('should enforce unique nation treaty name constraint', async () => {
      const treatyName = 'Test Nation Treaty'
      
      // Create first treaty
      await prisma.nationTreaty.create({
        data: {
          name: treatyName,
          officialName: 'Test Nation Treaty Official',
          description: 'First treaty with this name',
        },
      })

      // Try to create second treaty with same name
      await expect(
        prisma.nationTreaty.create({
          data: {
            name: treatyName,
            officialName: 'Test Nation Treaty Official 2',
            description: 'Second treaty with same name',
          },
        })
      ).rejects.toThrow()

      // Clean up
      await prisma.nationTreaty.deleteMany({ where: { name: treatyName } })
    })

    test('should validate status enum values', async () => {
      const validStatuses = ['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING']
      
      for (const status of validStatuses) {
        const nationTreaty = await prisma.nationTreaty.create({
          data: {
            name: `Test Treaty ${status}`,
            officialName: `Test Treaty ${status} Official`,
            description: `Testing ${status} status`,
            status: status as any,
          },
        })

        expect(nationTreaty.status).toBe(status)
        await prisma.nationTreaty.delete({ where: { id: nationTreaty.id } })
      }

      // Test invalid status
      await expect(
        prisma.nationTreaty.create({
          data: {
            name: 'Test Invalid Status',
            officialName: 'Test Invalid Status Official',
            description: 'Testing invalid status',
            status: 'INVALID' as any,
          },
        })
      ).rejects.toThrow()
    })

    test('should handle soft delete functionality', async () => {
      const nationTreaty = await prisma.nationTreaty.create({
        data: {
          name: 'Test Soft Delete',
          officialName: 'Test Soft Delete Official',
          description: 'Testing soft delete',
          status: 'ACTIVE',
        },
      })

      // Soft delete
      const deletedTreaty = await prisma.nationTreaty.update({
        where: { id: nationTreaty.id },
        data: { status: 'INACTIVE' },
      })

      expect(deletedTreaty.status).toBe('INACTIVE')

      // Clean up
      await prisma.nationTreaty.delete({ where: { id: nationTreaty.id } })
    })
  })

  describe('NationTreatyMember Model', () => {
    let testUser: any
    let testNationTreaty: any

    beforeEach(async () => {
      // Create test user
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Nation Test User',
        },
      })

      // Create test nation treaty
      testNationTreaty = await prisma.nationTreaty.create({
        data: {
          name: 'Test Nation for Members',
          officialName: 'Test Nation for Members Official',
          description: 'Test nation treaty for member validation',
        },
      })
    })

    afterEach(async () => {
      // Clean up
      await prisma.nationTreatyMember.deleteMany({
        where: { 
          OR: [
            { userId: testUser.id },
            { nationTreatyId: testNationTreaty.id }
          ]
        },
      })
      await prisma.nationTreaty.delete({ where: { id: testNationTreaty.id } })
      await prisma.user.delete({ where: { id: testUser.id } })
    })

    test('should create and retrieve nation treaty member with relationship', async () => {
      const member = await prisma.nationTreatyMember.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          role: 'MEMBER',
          joinDate: new Date(),
          notes: 'Test member for validation',
        },
      })

      expect(member.id).toBeDefined()
      expect(member.userId).toBe(testUser.id)
      expect(member.nationTreatyId).toBe(testNationTreaty.id)
      expect(member.role).toBe('MEMBER')
      expect(member.joinDate).toBeInstanceOf(Date)

      // Verify relationship
      const retrievedMember = await prisma.nationTreatyMember.findUnique({
        where: { id: member.id },
        include: {
          user: true,
          nationTreaty: true,
        },
      })

      expect(retrievedMember?.user.email).toBe('<EMAIL>')
      expect(retrievedMember?.nationTreaty.name).toBe('Test Nation for Members')

      // Clean up
      await prisma.nationTreatyMember.delete({ where: { id: member.id } })
    })

    test('should validate member role enum values', async () => {
      const validRoles = ['MEMBER', 'ENVOY', 'ADMIN']
      
      for (const role of validRoles) {
        const member = await prisma.nationTreatyMember.create({
          data: {
            userId: testUser.id,
            nationTreatyId: testNationTreaty.id,
            role: role as any,
            joinDate: new Date(),
          },
        })

        expect(member.role).toBe(role)
        await prisma.nationTreatyMember.delete({ where: { id: member.id } })
      }

      // Test invalid role
      await expect(
        prisma.nationTreatyMember.create({
          data: {
            userId: testUser.id,
            nationTreatyId: testNationTreaty.id,
            role: 'INVALID' as any,
            joinDate: new Date(),
          },
        })
      ).rejects.toThrow()
    })

    test('should enforce unique user-nation combination', async () => {
      // Create first membership
      await prisma.nationTreatyMember.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          role: 'MEMBER',
          joinDate: new Date(),
        },
      })

      // Try to create second membership for same user and nation
      await expect(
        prisma.nationTreatyMember.create({
          data: {
            userId: testUser.id,
            nationTreatyId: testNationTreaty.id,
            role: 'ADMIN',
            joinDate: new Date(),
          },
        })
      ).rejects.toThrow()
    })

    test('should auto-generate joinDate when not provided', async () => {
      const member = await prisma.nationTreatyMember.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          role: 'MEMBER',
        },
      })

      expect(member.joinDate).toBeInstanceOf(Date)
      expect(member.joinDate.getTime()).toBeLessThanOrEqual(Date.now())

      // Clean up
      await prisma.nationTreatyMember.delete({ where: { id: member.id } })
    })
  })

  describe('NationTreatyEnvoy Model', () => {
    let testUser: any
    let testNationTreaty: any
    let emergencyUser: any

    beforeEach(async () => {
      // Create test users
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Envoy Test User',
        },
      })

      emergencyUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Emergency Test User',
          profile: {
            create: {
              phone: '+64-9-111-2222',
              mobile: '+64-21-111-2222',
            },
          },
        },
      })

      // Create test nation treaty
      testNationTreaty = await prisma.nationTreaty.create({
        data: {
          name: 'Test Nation for Envoys',
          officialName: 'Test Nation for Envoys Official',
          description: 'Test nation treaty for envoy validation',
        },
      })
    })

    afterEach(async () => {
      // Clean up
      await prisma.nationTreatyEnvoy.deleteMany({
        where: { 
          OR: [
            { userId: testUser.id },
            { nationTreatyId: testNationTreaty.id },
            { emergencyContactUserId: emergencyUser.id }
          ]
        },
      })
      await prisma.nationTreaty.delete({ where: { id: testNationTreaty.id } })
      await prisma.user.delete({ where: { id: testUser.id } })
      await prisma.user.delete({ where: { id: emergencyUser.id } })
    })

    test('should create and retrieve nation treaty envoy with contact details', async () => {
      const envoy = await prisma.nationTreatyEnvoy.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          envoyType: 'PRIMARY',
          title: 'His Excellency',
          address: '123 Embassy Row, Wellington, New Zealand',
          city: 'Wellington',
          region: 'Wellington Region',
          country: 'New Zealand',
          phone: '+64-4-123-4567',
          mobile: '+64-21-123-4567',
          emergencyContactUserId: emergencyUser.id,
          notes: 'Primary diplomatic contact',
        },
      })

      expect(envoy.id).toBeDefined()
      expect(envoy.userId).toBe(testUser.id)
      expect(envoy.nationTreatyId).toBe(testNationTreaty.id)
      expect(envoy.envoyType).toBe('PRIMARY')
      expect(envoy.address).toBe('123 Embassy Row, Wellington, New Zealand')
      expect(envoy.emergencyContactUserId).toBe(emergencyUser.id)

      // Verify relationship
      const retrievedEnvoy = await prisma.nationTreatyEnvoy.findUnique({
        where: { id: envoy.id },
        include: {
          user: true,
          nationTreaty: true,
          emergencyContactUser: true,
        },
      })

      expect(retrievedEnvoy?.user.email).toBe('<EMAIL>')
      expect(retrievedEnvoy?.nationTreaty.name).toBe('Test Nation for Envoys')
      expect(retrievedEnvoy?.emergencyContactUser.email).toBe('<EMAIL>')

      // Clean up
      await prisma.nationTreatyEnvoy.delete({ where: { id: envoy.id } })
    })

    test('should validate envoy type enum values', async () => {
      const validEnvoyTypes = ['PRIMARY', 'EMERGENCY', 'LEGAL']
      
      for (const envoyType of validEnvoyTypes) {
        const envoy = await prisma.nationTreatyEnvoy.create({
          data: {
            userId: testUser.id,
            nationTreatyId: testNationTreaty.id,
            envoyType: envoyType as any,
            emergencyContactUserId: emergencyUser.id,
          },
        })

        expect(envoy.envoyType).toBe(envoyType)
        await prisma.nationTreatyEnvoy.delete({ where: { id: envoy.id } })
      }

      // Test invalid envoy type
      await expect(
        prisma.nationTreatyEnvoy.create({
          data: {
            userId: testUser.id,
            nationTreatyId: testNationTreaty.id,
            envoyType: 'INVALID' as any,
            emergencyContactUserId: emergencyUser.id,
          },
        })
      ).rejects.toThrow()
    })

    test('should enforce unique user-nation-envoyType combination', async () => {
      // Create first envoy
      await prisma.nationTreatyEnvoy.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          envoyType: 'PRIMARY',
          emergencyContactUserId: emergencyUser.id,
        },
      })

      // Try to create second envoy with same user, nation, and type
      await expect(
        prisma.nationTreatyEnvoy.create({
          data: {
            userId: testUser.id,
            nationTreatyId: testNationTreaty.id,
            envoyType: 'PRIMARY',
            emergencyContactUserId: emergencyUser.id,
          },
        })
      ).rejects.toThrow()
    })

    test('should allow multiple envoy types for same user in different nations', async () => {
      // Create second nation treaty
      const secondNationTreaty = await prisma.nationTreaty.create({
        data: {
          name: 'Second Test Nation',
          officialName: 'Second Test Nation Official',
          description: 'Second nation treaty for testing',
        },
      })

      // Create envoy in first nation
      const firstEnvoy = await prisma.nationTreatyEnvoy.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          envoyType: 'PRIMARY',
          emergencyContactUserId: emergencyUser.id,
        },
      })

      // Create envoy in second nation with same type (should be allowed)
      const secondEnvoy = await prisma.nationTreatyEnvoy.create({
        data: {
          userId: testUser.id,
          nationTreatyId: secondNationTreaty.id,
          envoyType: 'PRIMARY',
          emergencyContactUserId: emergencyUser.id,
        },
      })

      expect(firstEnvoy.envoyType).toBe('PRIMARY')
      expect(secondEnvoy.envoyType).toBe('PRIMARY')

      // Clean up
      await prisma.nationTreatyEnvoy.delete({ where: { id: firstEnvoy.id } })
      await prisma.nationTreatyEnvoy.delete({ where: { id: secondEnvoy.id } })
      await prisma.nationTreaty.delete({ where: { id: secondNationTreaty.id } })
    })

    test('should require emergency contact user for EMERGENCY envoy type', async () => {
      // Test that EMERGENCY envoy type requires emergencyContactUserId
      await expect(
        prisma.nationTreatyEnvoy.create({
          data: {
            userId: testUser.id,
            nationTreatyId: testNationTreaty.id,
            envoyType: 'EMERGENCY',
            // Missing emergencyContactUserId
          },
        })
      ).rejects.toThrow()

      // Test that PRIMARY envoy type also requires emergencyContactUserId
      await expect(
        prisma.nationTreatyEnvoy.create({
          data: {
            userId: testUser.id,
            nationTreatyId: testNationTreaty.id,
            envoyType: 'PRIMARY',
            // Missing emergencyContactUserId
          },
        })
      ).rejects.toThrow()
    })
  })

  describe('Cascade Deletion Behavior', () => {
    let testUser: any
    let testNationTreaty: any
    let emergencyUser: any

    beforeEach(async () => {
      // Create test users
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Cascade Test User',
        },
      })

      emergencyUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Cascade Emergency User',
          profile: {
            create: {
              phone: '+64-9-333-4444',
              mobile: '+64-21-333-4444',
            },
          },
        },
      })

      // Create test nation treaty
      testNationTreaty = await prisma.nationTreaty.create({
        data: {
          name: 'Test Nation for Cascade',
          officialName: 'Test Nation for Cascade Official',
          description: 'Test nation treaty for cascade deletion testing',
        },
      })
    })

    afterEach(async () => {
      // Clean up any remaining data
      await prisma.nationTreatyMember.deleteMany({
        where: { 
          OR: [
            { userId: testUser.id },
            { nationTreatyId: testNationTreaty.id }
          ]
        },
      })
      await prisma.nationTreatyEnvoy.deleteMany({
        where: { 
          OR: [
            { userId: testUser.id },
            { nationTreatyId: testNationTreaty.id },
            { emergencyContactUserId: emergencyUser.id }
          ]
        },
      })
      await prisma.nationTreaty.deleteMany({ where: { id: testNationTreaty.id } })
      await prisma.user.deleteMany({
        where: { 
          OR: [
            { id: testUser.id },
            { id: emergencyUser.id }
          ]
        },
      })
    })

    test('should cascade delete members when nation treaty is deleted', async () => {
      // Create member
      const member = await prisma.nationTreatyMember.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          role: 'MEMBER',
        },
      })

      // Delete nation treaty
      await prisma.nationTreaty.delete({ where: { id: testNationTreaty.id } })

      // Verify member is also deleted
      const deletedMember = await prisma.nationTreatyMember.findUnique({
        where: { id: member.id },
      })

      expect(deletedMember).toBeNull()
    })

    test('should cascade delete envoys when nation treaty is deleted', async () => {
      // Create envoy
      const envoy = await prisma.nationTreatyEnvoy.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          envoyType: 'PRIMARY',
          emergencyContactUserId: emergencyUser.id,
        },
      })

      // Delete nation treaty
      await prisma.nationTreaty.delete({ where: { id: testNationTreaty.id } })

      // Verify envoy is also deleted
      const deletedEnvoy = await prisma.nationTreatyEnvoy.findUnique({
        where: { id: envoy.id },
      })

      expect(deletedEnvoy).toBeNull()
    })

    test('should cascade delete members when user is deleted', async () => {
      // Create member
      const member = await prisma.nationTreatyMember.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          role: 'MEMBER',
        },
      })

      // Delete user
      await prisma.user.delete({ where: { id: testUser.id } })

      // Verify member is also deleted
      const deletedMember = await prisma.nationTreatyMember.findUnique({
        where: { id: member.id },
      })

      expect(deletedMember).toBeNull()
    })

    test('should cascade delete envoys when user is deleted', async () => {
      // Create envoy
      const envoy = await prisma.nationTreatyEnvoy.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          envoyType: 'PRIMARY',
          emergencyContactUserId: emergencyUser.id,
        },
      })

      // Delete user
      await prisma.user.delete({ where: { id: testUser.id } })

      // Verify envoy is also deleted
      const deletedEnvoy = await prisma.nationTreatyEnvoy.findUnique({
        where: { id: envoy.id },
      })

      expect(deletedEnvoy).toBeNull()
    })

    test('should handle foreign key constraint for emergency contact user deletion', async () => {
      // Create envoy
      const envoy = await prisma.nationTreatyEnvoy.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          envoyType: 'PRIMARY',
          emergencyContactUserId: emergencyUser.id,
        },
      })

      // Try to delete emergency contact user (should fail due to foreign key constraint)
      await expect(
        prisma.user.delete({ where: { id: emergencyUser.id } })
      ).rejects.toThrow()

      // Clean up
      await prisma.nationTreatyEnvoy.delete({ where: { id: envoy.id } })
    })
  })

  describe('Relationship Queries', () => {
    let testUser: any
    let testNationTreaty: any
    let emergencyUser: any
    let secondUser: any

    beforeEach(async () => {
      // Create test users
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Relationship Test User',
        },
      })

      secondUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Second User Test',
        },
      })

      emergencyUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Emergency Relationship User',
          profile: {
            create: {
              phone: '+64-9-555-6666',
              mobile: '+64-21-555-6666',
            },
          },
        },
      })

      // Create test nation treaty
      testNationTreaty = await prisma.nationTreaty.create({
        data: {
          name: 'Test Nation for Relationships',
          officialName: 'Test Nation for Relationships Official',
          description: 'Test nation treaty for relationship validation',
        },
      })
    })

    afterEach(async () => {
      // Clean up
      await prisma.nationTreatyMember.deleteMany({
        where: { 
          OR: [
            { userId: testUser.id },
            { userId: secondUser.id },
            { nationTreatyId: testNationTreaty.id }
          ]
        },
      })
      await prisma.nationTreatyEnvoy.deleteMany({
        where: { 
          OR: [
            { userId: testUser.id },
            { userId: secondUser.id },
            { nationTreatyId: testNationTreaty.id },
            { emergencyContactUserId: emergencyUser.id }
          ]
        },
      })
      await prisma.nationTreaty.delete({ where: { id: testNationTreaty.id } })
      await prisma.user.deleteMany({
        where: { 
          OR: [
            { id: testUser.id },
            { id: secondUser.id },
            { id: emergencyUser.id }
          ]
        },
      })
    })

    test('should retrieve all members of a nation treaty', async () => {
      // Create multiple members
      await prisma.nationTreatyMember.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          role: 'ADMIN',
        },
      })

      await prisma.nationTreatyMember.create({
        data: {
          userId: secondUser.id,
          nationTreatyId: testNationTreaty.id,
          role: 'MEMBER',
        },
      })

      // Retrieve nation treaty with members
      const nationWithMembers = await prisma.nationTreaty.findUnique({
        where: { id: testNationTreaty.id },
        include: {
          members: {
            include: {
              user: true,
            },
          },
        },
      })

      expect(nationWithMembers?.members).toHaveLength(2)
      expect(nationWithMembers?.members.some((m) => m.user.email === '<EMAIL>')).toBe(true)
      expect(nationWithMembers?.members.some((m) => m.user.email === '<EMAIL>')).toBe(true)
    })

    test('should retrieve all envoys of a nation treaty', async () => {
      // Create multiple envoys
      await prisma.nationTreatyEnvoy.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          envoyType: 'PRIMARY',
          emergencyContactUserId: emergencyUser.id,
        },
      })

      await prisma.nationTreatyEnvoy.create({
        data: {
          userId: secondUser.id,
          nationTreatyId: testNationTreaty.id,
          envoyType: 'EMERGENCY',
          emergencyContactUserId: emergencyUser.id,
        },
      })

      // Retrieve nation treaty with envoys
      const nationWithEnvoys = await prisma.nationTreaty.findUnique({
        where: { id: testNationTreaty.id },
        include: {
          envoys: {
            include: {
              user: true,
              emergencyContactUser: true,
            },
          },
        },
      })

      expect(nationWithEnvoys?.envoys).toHaveLength(2)
      expect(nationWithEnvoys?.envoys.some((e) => e.user.email === '<EMAIL>')).toBe(true)
      expect(nationWithEnvoys?.envoys.some((e) => e.user.email === '<EMAIL>')).toBe(true)
    })

    test('should retrieve all nation treaties for a user', async () => {
      // Create second nation treaty
      const secondNationTreaty = await prisma.nationTreaty.create({
        data: {
          name: 'Second Test Nation for User',
          officialName: 'Second Test Nation for User Official',
          description: 'Second nation treaty for user relationship testing',
        },
      })

      // Create memberships for user in multiple nations
      await prisma.nationTreatyMember.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          role: 'MEMBER',
        },
      })

      await prisma.nationTreatyMember.create({
        data: {
          userId: testUser.id,
          nationTreatyId: secondNationTreaty.id,
          role: 'ADMIN',
        },
      })

      // Retrieve user with nation treaties
      const userWithNations = await prisma.user.findUnique({
        where: { id: testUser.id },
        include: {
          nationTreatyMemberships: {
            include: {
              nationTreaty: true,
            },
          },
        },
      })

      expect(userWithNations?.nationTreatyMemberships).toHaveLength(2)
      expect(userWithNations?.nationTreatyMemberships.some((m) => m.nationTreaty.name === 'Test Nation for Relationships')).toBe(true)
      expect(userWithNations?.nationTreatyMemberships.some((m) => m.nationTreaty.name === 'Second Test Nation for User')).toBe(true)

      // Clean up
      await prisma.nationTreaty.delete({ where: { id: secondNationTreaty.id } })
    })

    test('should filter nation treaties by status', async () => {
      // Create multiple nation treaties with different statuses
      const activeTreaty = await prisma.nationTreaty.create({
        data: {
          name: 'Active Nation Treaty',
          officialName: 'Active Nation Treaty Official',
          description: 'Active nation treaty',
          status: 'ACTIVE',
        },
      })

      const inactiveTreaty = await prisma.nationTreaty.create({
        data: {
          name: 'Inactive Nation Treaty',
          officialName: 'Inactive Nation Treaty Official',
          description: 'Inactive nation treaty',
          status: 'INACTIVE',
        },
      })

      // Query active treaties
      const activeTreaties = await prisma.nationTreaty.findMany({
        where: { status: 'ACTIVE' },
      })

      // Query inactive treaties
      const inactiveTreaties = await prisma.nationTreaty.findMany({
        where: { status: 'INACTIVE' },
      })

      expect(activeTreaties.some((t) => t.name === 'Active Nation Treaty')).toBe(true)
      expect(inactiveTreaties.some((t) => t.name === 'Inactive Nation Treaty')).toBe(true)
      expect(activeTreaties.some((t) => t.name === 'Inactive Nation Treaty')).toBe(false)
      expect(inactiveTreaties.some((t) => t.name === 'Active Nation Treaty')).toBe(false)

      // Clean up
      await prisma.nationTreaty.delete({ where: { id: activeTreaty.id } })
      await prisma.nationTreaty.delete({ where: { id: inactiveTreaty.id } })
    })

    test('should filter members by role within a nation treaty', async () => {
      // Create members with different roles
      await prisma.nationTreatyMember.create({
        data: {
          userId: testUser.id,
          nationTreatyId: testNationTreaty.id,
          role: 'ADMIN',
        },
      })

      await prisma.nationTreatyMember.create({
        data: {
          userId: secondUser.id,
          nationTreatyId: testNationTreaty.id,
          role: 'MEMBER',
        },
      })

      // Filter admins
      const adminMembers = await prisma.nationTreatyMember.findMany({
        where: {
          nationTreatyId: testNationTreaty.id,
          role: 'ADMIN',
        },
        include: {
          user: true,
        },
      })

      // Filter regular members
      const regularMembers = await prisma.nationTreatyMember.findMany({
        where: {
          nationTreatyId: testNationTreaty.id,
          role: 'MEMBER',
        },
        include: {
          user: true,
        },
      })

      expect(adminMembers).toHaveLength(1)
      expect(adminMembers[0].user.email).toBe('<EMAIL>')
      expect(regularMembers).toHaveLength(1)
      expect(regularMembers[0].user.email).toBe('<EMAIL>')
    })
  })

  describe('Performance and Data Integrity', () => {
    test('should handle bulk nation treaty creation efficiently', async () => {
      const treatyNames = Array.from({ length: 10 }, (_, i) => `Bulk Test Nation ${i + 1}`)
      const createdTreaties = []

      // Create multiple treaties
      for (const name of treatyNames) {
        const treaty = await prisma.nationTreaty.create({
          data: {
            name: name,
            officialName: `${name} Official`,
            description: `Bulk test nation treaty ${name}`,
          },
        })
        createdTreaties.push(treaty)
      }

      // Verify all treaties were created
      expect(createdTreaties).toHaveLength(10)

      // Query all treaties to test performance
      const allTreaties = await prisma.nationTreaty.findMany({
        where: {
          name: {
            contains: 'Bulk Test Nation',
          },
        },
        orderBy: {
          name: 'asc',
        },
      })

      expect(allTreaties).toHaveLength(10)
      expect(allTreaties[0].name).toBe('Bulk Test Nation 1')
      expect(allTreaties[9].name).toBe('Bulk Test Nation 10')

      // Clean up
      await prisma.nationTreaty.deleteMany({
        where: {
          name: {
            contains: 'Bulk Test Nation',
          },
        },
      })
    })

    test('should handle bulk member assignment efficiently', async () => {
      // Create nation treaty
      const nationTreaty = await prisma.nationTreaty.create({
        data: {
          name: 'Bulk Member Test Nation',
          officialName: 'Bulk Member Test Nation Official',
          description: 'Test nation for bulk member assignment',
        },
      })

      // Create multiple users
      const users = []
      for (let i = 0; i < 5; i++) {
        const user = await prisma.user.create({
          data: {
            email: `bulk-user-${i + 1}@example.com`,
            name: `Bulk User ${i + 1}`,
          },
        })
        users.push(user)
      }

      // Assign all users as members
      const memberPromises = users.map((user) =>
        prisma.nationTreatyMember.create({
          data: {
            userId: user.id,
            nationTreatyId: nationTreaty.id,
            role: 'MEMBER',
          },
        })
      )

      const members = await Promise.all(memberPromises)
      expect(members).toHaveLength(5)

      // Verify all members are assigned
      const nationWithMembers = await prisma.nationTreaty.findUnique({
        where: { id: nationTreaty.id },
        include: {
          members: {
            include: {
              user: true,
            },
          },
        },
      })

      expect(nationWithMembers?.members).toHaveLength(5)

      // Clean up
      await prisma.nationTreaty.delete({ where: { id: nationTreaty.id } })
      await prisma.user.deleteMany({
        where: {
          email: {
            contains: 'bulk-user-',
          },
        },
      })
    })

    test('should maintain data consistency with concurrent operations', async () => {
      // Create nation treaty
      const nationTreaty = await prisma.nationTreaty.create({
        data: {
          name: 'Concurrent Test Nation',
          officialName: 'Concurrent Test Nation Official',
          description: 'Test nation for concurrent operations',
        },
      })

      // Create user
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Concurrent Test User',
        },
      })

      // Perform concurrent operations
      const operations = [
        prisma.nationTreatyMember.create({
          data: {
            userId: user.id,
            nationTreatyId: nationTreaty.id,
            role: 'MEMBER',
          },
        }),
        prisma.nationTreaty.update({
          where: { id: nationTreaty.id },
          data: { notes: 'Updated during concurrent operation' },
        }),
      ]

      await Promise.all(operations)

      // Verify consistency
      const finalState = await prisma.nationTreaty.findUnique({
        where: { id: nationTreaty.id },
        include: {
          members: true,
        },
      })

      expect(finalState?.notes).toBe('Updated during concurrent operation')
      expect(finalState?.members).toHaveLength(1)

      // Clean up
      await prisma.nationTreaty.delete({ where: { id: nationTreaty.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })
  })
})