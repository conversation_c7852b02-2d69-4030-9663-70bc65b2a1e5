// Test setup for database tests requiring actual database connection
import { PrismaClient } from '@prisma/client'

// Extend global interface
declare global {
  var prisma: PrismaClient
}

// Global test setup
global.beforeAll(async () => {
  // Initialize Prisma client for tests
  const prisma = new PrismaClient()
  global.prisma = prisma
  
  try {
    await prisma.$connect()
    console.log('Connected to test database')
  } catch (error) {
    console.error('Failed to connect to test database:', error)
    throw error
  }
})

global.afterAll(async () => {
  if (global.prisma) {
    try {
      await global.prisma.$disconnect()
      console.log('Disconnected from test database')
    } catch (error) {
      console.error('Failed to disconnect from test database:', error)
    }
  }
})