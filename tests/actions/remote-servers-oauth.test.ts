// Mock Prisma client methods
jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    $queryRawUnsafe: jest.fn(),
    $executeRawUnsafe: jest.fn()
  }
}))

import { PrismaClient } from '@prisma/client'
const prisma = new PrismaClient()

// Get the mock functions
const { $queryRawUnsafe: mockQueryRawUnsafe, $executeRawUnsafe: mockExecuteRawUnsafe } = require('../../src/lib/prisma').prisma

import { 
  getAllRemoteServers, 
  getRemoteServerById, 
  updateRemoteServerConfig,
  deleteRemoteServerById
} from '../../src/app/actions/remote-servers-oauth'

describe('Remote Servers OAuth Actions', () => {
  const mockRemoteServers = [
    {
      id: 'server_1',
      name: 'Test Server',
      url: 'https://test.example.com',
      apiKey: 'test-api-key',
      description: 'Test server description',
      isActive: true,
      createdAt: new Date('2023-01-01T00:00:00Z'),
      updatedAt: new Date('2023-01-01T00:00:00Z'),
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUris: ['https://test.example.com/callback']
    }
  ]

  beforeEach(() => {
    jest.clearAllMocks()
  })

  afterAll(async () => {
    await prisma.$disconnect()
  })

  describe('getAllRemoteServers', () => {
    it('should fetch all remote servers successfully', async () => {
      mockQueryRawUnsafe.mockResolvedValue(mockRemoteServers)

      const result = await getAllRemoteServers()

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockRemoteServers)
      expect(mockQueryRawUnsafe).toHaveBeenCalled()
    })

    it('should handle errors when fetching remote servers', async () => {
      mockQueryRawUnsafe.mockRejectedValue(new Error('Database error'))

      const result = await getAllRemoteServers()

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to fetch remote servers')
    })
  })

  describe('getRemoteServerById', () => {
    it('should fetch a specific remote server successfully', async () => {
      mockQueryRawUnsafe.mockResolvedValue(mockRemoteServers)

      const result = await getRemoteServerById('server_1')

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockRemoteServers[0])
      expect(mockQueryRawUnsafe).toHaveBeenCalledWith(expect.stringContaining('SELECT'), 'server_1')
    })

    it('should return error when remote server is not found', async () => {
      mockQueryRawUnsafe.mockResolvedValue([])

      const result = await getRemoteServerById('nonexistent')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Remote server not found')
    })

    it('should handle errors when fetching remote server', async () => {
      mockQueryRawUnsafe.mockRejectedValue(new Error('Database error'))

      const result = await getRemoteServerById('server_1')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to fetch remote server')
    })
  })

  describe('updateRemoteServerConfig', () => {
    it('should update remote server configuration successfully', async () => {
      // Mock the existence check
      mockQueryRawUnsafe
        .mockResolvedValueOnce([{ id: 'server_1' }]) // Existence check
        .mockResolvedValueOnce(mockRemoteServers) // Update result

      const updateData = {
        name: 'Updated Server Name',
        isActive: false
      }

      const result = await updateRemoteServerConfig('server_1', updateData)

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockRemoteServers[0])
      expect(mockQueryRawUnsafe).toHaveBeenCalledTimes(2)
    })

    it('should return error when remote server is not found', async () => {
      mockQueryRawUnsafe.mockResolvedValueOnce([]) // Existence check

      const result = await updateRemoteServerConfig('nonexistent', { name: 'Test' })

      expect(result.success).toBe(false)
      expect(result.error).toBe('Remote server not found')
    })

    it('should handle errors when updating remote server', async () => {
      mockQueryRawUnsafe
        .mockResolvedValueOnce([{ id: 'server_1' }]) // Existence check
        .mockRejectedValueOnce(new Error('Database error')) // Update error

      const result = await updateRemoteServerConfig('server_1', { name: 'Test' })

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to update remote server')
    })
  })

  describe('deleteRemoteServerById', () => {
    it('should delete remote server successfully', async () => {
      mockQueryRawUnsafe.mockResolvedValueOnce([{ id: 'server_1' }]) // Existence check
      mockExecuteRawUnsafe.mockResolvedValueOnce(undefined) // Delete result

      const result = await deleteRemoteServerById('server_1')

      expect(result.success).toBe(true)
      expect(mockExecuteRawUnsafe).toHaveBeenCalledWith(expect.stringContaining('DELETE'), 'server_1')
    })

    it('should return error when remote server is not found', async () => {
      mockQueryRawUnsafe.mockResolvedValueOnce([]) // Existence check

      const result = await deleteRemoteServerById('nonexistent')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Remote server not found')
    })

    it('should handle errors when deleting remote server', async () => {
      mockQueryRawUnsafe.mockResolvedValueOnce([{ id: 'server_1' }]) // Existence check
      mockExecuteRawUnsafe.mockRejectedValueOnce(new Error('Database error')) // Delete error

      const result = await deleteRemoteServerById('server_1')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to delete remote server')
    })
  })
})