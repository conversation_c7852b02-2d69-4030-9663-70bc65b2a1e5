# Testing Documentation

This project uses both <PERSON><PERSON> and <PERSON>wright for comprehensive testing coverage.

## Overview

- **Cypress**: End-to-end testing with a focus on user interactions and component testing
- **Playwright**: Cross-browser testing with powerful automation capabilities
- **Jest**: Unit and integration testing (already configured)

## Cypress Setup

Cypress is already configured and ready to use.

### Running Cypress Tests

```bash
# Open Cypress Test Runner (interactive mode)
npm run cypress:open

# Run Cypress tests headlessly
npm run cypress:run

# Run E2E tests (alias for cypress:run)
npm run test:e2e

# Open Cypress with specific test file
npm run test:e2e:open
```

### Cypress Configuration

- **Config file**: `cypress.config.ts`
- **Test files**: Located in `cypress/e2e/`
- **Support files**: Located in `cypress/support/`
- **Base URL**: `http://localhost:3001` (configurable via environment)

### Custom Commands

Cypress includes several custom commands for common operations:

- `cy.login(email, password)` - Login with credentials
- `cy.isAuthenticated()` - Check if user is authenticated
- `cy.logout()` - Logout current user
- `cy.waitForApi(url, method)` - Wait for API response
- `cy.checkPageTitle(title)` - Verify page title
- `cy.fillField(selector, value)` - Fill form field
- `cy.selectOption(selector, option)` - Select dropdown option

## Playwright Setup

Playwright is configured for cross-browser testing.

### Installation

First, install Playwright browsers:

```bash
npm run playwright:install
```

### Running Playwright Tests

```bash
# Run all Playwright tests
npm run playwright:test

# Run tests in headed mode (visible browser)
npm run playwright:test:headed

# Run tests with UI mode
npm run playwright:test:ui

# Debug tests
npm run playwright:test:debug

# Show test report
npm run playwright:report
```

### Playwright Configuration

- **Config file**: `playwright.config.ts`
- **Test files**: Located in `tests/playwright/e2e/`
- **Fixtures**: Located in `tests/playwright/fixtures/`
- **Utils**: Located in `tests/playwright/utils/`
- **Base URL**: `http://localhost:3001` (configurable via environment)

### Supported Browsers

- Chromium (Desktop)
- Firefox (Desktop)
- WebKit (Safari Desktop)
- Mobile Chrome (Pixel 5)
- Mobile Safari (iPhone 12)

### Test Structure

```
tests/playwright/
├── e2e/                    # End-to-end tests
│   ├── basic.spec.ts      # Basic functionality tests
│   └── dashboard.spec.ts  # Dashboard-specific tests
├── fixtures/              # Test fixtures and setup
│   └── test-setup.ts      # Base test configuration
└── utils/                 # Helper utilities
    └── test-helpers.ts    # Common test functions
```

### Test Helpers

Playwright includes a `TestHelpers` class with useful methods:

- `login(email, password)` - Login with credentials
- `isAuthenticated()` - Check authentication status
- `logout()` - Logout current user
- `waitForApiResponse(url, method)` - Wait for API calls
- `checkPageTitle(title)` - Verify page title
- `fillFormField(selector, value)` - Fill form fields
- `selectDropdownOption(selector, option)` - Select options
- `takeScreenshot(name)` - Capture screenshots

## Test Organization

### Cypress Tests
- **Location**: `cypress/e2e/`
- **Purpose**: User interaction testing, component testing
- **Best for**: Testing user workflows, form interactions, UI components

### Playwright Tests
- **Location**: `tests/playwright/e2e/`
- **Purpose**: Cross-browser testing, API testing, complex scenarios
- **Best for**: Multi-browser testing, API integration, complex user flows

### Jest Tests
- **Location**: `tests/` and `__tests__/`
- **Purpose**: Unit testing, API testing, integration testing
- **Best for**: Testing individual functions, API endpoints, utilities

## Environment Setup

### Development Environment
1. Start the development server: `npm run dev`
2. Run tests in separate terminals

### Test Environment Variables
- `BASE_URL` - Base URL for tests (default: http://localhost:3001)
- `CI` - Set to true for CI environment (affects test configuration)

## Best Practices

### Writing Tests
1. Use descriptive test names
2. Group related tests with `describe` blocks
3. Use `beforeEach` for common setup
4. Keep tests independent and isolated
5. Use data-testid attributes for reliable element selection

### Test Data
- Use fixtures for test data
- Avoid hard-coded test data in test files
- Use environment-specific data when needed

### Debugging
- Use `npm run playwright:test:ui` for interactive debugging
- Use `npm run cypress:open` for Cypress debugging
- Add `console.log` statements for debugging
- Use browser dev tools in headed mode

## CI/CD Integration

Both testing frameworks are configured for CI/CD environments:

- Tests run in headless mode
- Screenshots and videos are captured on failure
- Test reports are generated in multiple formats
- Parallel execution is supported

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure the dev server is running on port 3001
2. **Browser not found**: Run `npm run playwright:install` to install browsers
3. **Authentication issues**: Check test credentials and authentication flow
4. **Element not found**: Use data-testid attributes and wait for elements

### Getting Help

- Check the official Cypress documentation: https://docs.cypress.io/
- Check the official Playwright documentation: https://playwright.dev/
- Review existing tests for examples
- Use the test helpers and fixtures provided