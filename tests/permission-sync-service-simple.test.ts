import { PermissionSyncService } from '@/lib/services/permission-sync-service'

// Mock the remote server permissions fetch
jest.mock('@/app/actions/remote-servers', () => ({
  fetchRemoteServerPermissions: jest.fn()
}))

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    remoteServer: {
      findUnique: jest.fn()
    },
    remote_server_permissions: {
      upsert: jest.fn(),
      updateMany: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      count: jest.fn(),
      deleteMany: jest.fn()
    }
  }
}))

// Import the mocked function and prisma
import { fetchRemoteServerPermissions } from '@/app/actions/remote-servers'
import { prisma } from '@/lib/prisma'

describe('PermissionSyncService Simple Tests', () => {
  let syncService: PermissionSyncService

  beforeEach(() => {
    syncService = new PermissionSyncService()
    jest.clearAllMocks()
  })

  test('should handle server not found', async () => {
    ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(null)

    const result = await syncService.syncServerPermissions('non-existent-server')

    expect(result.success).toBe(false)
    expect(result.errors).toContain('Remote server not found')
    expect(fetchRemoteServerPermissions).not.toHaveBeenCalled()
  })

  test('should handle fetch errors', async () => {
    const mockServer = {
      id: 'test-server-789',
      name: 'Test Server',
      url: 'https://test.example.com',
      apiKey: 'test-api-key',
      isActive: true
    }

    ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(mockServer)
    ;(fetchRemoteServerPermissions as jest.Mock).mockResolvedValue({
      success: false,
      error: 'Connection failed'
    })

    const result = await syncService.syncServerPermissions('test-server-789')

    expect(result.success).toBe(false)
    expect(result.errors).toContain('Connection failed')
  })

  test('should successfully sync permissions', async () => {
    const mockServerId = 'test-server-123'
    const mockServer = {
      id: mockServerId,
      name: 'Test Server',
      url: 'https://test.example.com',
      apiKey: 'test-api-key',
      isActive: true
    }

    // Mock the server fetch
    ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(mockServer)

    // Mock successful permissions fetch
    const mockPermissionsData = {
      success: true,
      data: {
        permissions: [
          { name: 'view_videos', description: 'View video content' },
          { name: 'upload_ebooks', description: 'Upload ebooks' }
        ],
        roles: []
      }
    }

    ;(fetchRemoteServerPermissions as jest.Mock).mockResolvedValue(mockPermissionsData)

    // Mock successful database operations
    ;(prisma.remote_server_permissions.upsert as jest.Mock).mockImplementation((args: any) => 
      Promise.resolve({
        id: 'test-id',
        remote_server_id: mockServerId,
        permission_name: args.create.permission_name,
        permission_description: args.create.permission_description,
        is_active: true,
        last_synced_at: new Date()
      })
    )

    // Execute sync
    const result = await syncService.syncServerPermissions(mockServerId)

    // Verify result
    expect(result.success).toBe(true)
    expect(result.permissionsSynced).toBe(2)
    expect(result.errors).toHaveLength(0)

    // Verify database operations were called
    expect(prisma.remote_server_permissions.upsert).toHaveBeenCalledTimes(2)
    expect(prisma.remote_server_permissions.updateMany).toHaveBeenCalled()
  })

  test('should handle database errors during sync', async () => {
    const mockServerId = 'test-server-456'
    const mockServer = {
      id: mockServerId,
      name: 'Test Server',
      url: 'https://test.example.com',
      apiKey: 'test-api-key',
      isActive: true
    }

    ;(prisma.remoteServer.findUnique as jest.Mock).mockResolvedValue(mockServer)

    // Mock successful permissions fetch
    const mockPermissionsData = {
      success: true,
      data: {
        permissions: [
          { name: 'view_videos', description: 'View video content' }
        ],
        roles: []
      }
    }

    ;(fetchRemoteServerPermissions as jest.Mock).mockResolvedValue(mockPermissionsData)

    // Mock database error
    ;(prisma.remote_server_permissions.upsert as jest.Mock).mockRejectedValue(
      new Error('Database connection failed')
    )

    const result = await syncService.syncServerPermissions(mockServerId)

    expect(result.success).toBe(true) // Individual permission errors don't fail the whole sync
    expect(result.errors.length).toBeGreaterThan(0)
    expect(result.errors[0]).toContain('Database connection failed')
  })
})