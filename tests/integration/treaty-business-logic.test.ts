import { ApplicationStatus, PaymentStatus } from '@prisma/client';
import { ApplicationStateMachine, PaymentStateMachine, WorkflowCoordinator, TransitionContext } from '../../src/lib/workflow/StateMachine';

describe('Treaty Application Business Logic Tests', () => {
  describe('Application Lifecycle Management', () => {
    test('should handle complete application workflow for paid treaties', async () => {
      // Step 1: Application submitted
      let applicationStatus = ApplicationStatus.APPLIED;
      let paymentStatus = PaymentStatus.NOT_REQUIRED;
      
      // Step 2: Admin reviews application
      const reviewContext: TransitionContext = {
        userId: 'admin-123',
        notes: 'Initial review complete',
      };

      const reviewResult = await ApplicationStateMachine.executeTransition(
        'app-123',
        applicationStatus,
        ApplicationStatus.UNDER_REVIEW,
        reviewContext,
        true // requiresPayment
      );

      expect(reviewResult.success).toBe(true);
      applicationStatus = reviewResult.newStatus!;

      // Step 3: Admin approves application
      const approvalContext: TransitionContext = {
        userId: 'admin-123',
        notes: 'Application approved pending payment',
      };

      const approvalResult = await ApplicationStateMachine.executeTransition(
        'app-123',
        applicationStatus,
        ApplicationStatus.APPROVED,
        approvalContext,
        true
      );

      expect(approvalResult.success).toBe(true);
      expect(approvalResult.requiresPayment).toBe(true);
      applicationStatus = approvalResult.newStatus!;

      // Step 4: Handle payment workflow (simplified - focus on the payment transition)
      // In a real system, this would be triggered by a separate payment submission
      paymentStatus = PaymentStatus.AWAITING_PAYMENT; // Simulate payment submission

      // Step 5: Admin processes payment (from AWAITING_PAYMENT to PENDING to PAID)
      // First, payment moves to PENDING when submitted
      const pendingResult = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        paymentStatus,
        PaymentStatus.PENDING,
        {
          adminId: 'admin-123',
          notes: 'Payment submitted',
        }
      );

      expect(pendingResult.success).toBe(true);
      paymentStatus = pendingResult.newStatus!;

      // Then, admin verifies payment
      const verificationResult = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        paymentStatus,
        PaymentStatus.PAID,
        {
          adminId: 'admin-123',
          notes: 'Payment verified',
        }
      );

      expect(verificationResult.success).toBe(true);
      paymentStatus = verificationResult.newStatus!;

      // Step 6: Activate treaty
      const activationResult = await ApplicationStateMachine.executeTransition(
        'app-123',
        applicationStatus,
        ApplicationStatus.ACTIVE,
        {
          userId: 'admin-123',
          notes: 'Treaty activated',
        },
        false
      );

      expect(activationResult.success).toBe(true);
      applicationStatus = activationResult.newStatus!;

      // Final state validation
      expect(applicationStatus).toBe(ApplicationStatus.ACTIVE);
      expect(paymentStatus).toBe(PaymentStatus.PAID);
    });

    test('should handle complete application workflow for free treaties', async () => {
      // Step 1: Application submitted
      let applicationStatus = ApplicationStatus.APPLIED;
      
      // Step 2: Admin reviews and approves
      const context: TransitionContext = {
        userId: 'admin-123',
        notes: 'Approved - free treaty',
      };

      const approvalResult = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.APPROVED,
        context,
        false // requiresPayment
      );

      expect(approvalResult.success).toBe(true);
      expect(approvalResult.autoActivate).toBe(true);
      
      // For free treaties, approval should lead to activation
      const activationResult = await ApplicationStateMachine.executeTransition(
        'app-123',
        approvalResult.newStatus!,
        ApplicationStatus.ACTIVE,
        context,
        false
      );

      expect(activationResult.success).toBe(true);
      expect(activationResult.newStatus).toBe(ApplicationStatus.ACTIVE);
    });

    test('should handle application rejection workflow', async () => {
      const context: TransitionContext = {
        userId: 'admin-123',
        notes: 'Insufficient documentation provided',
      };

      const result = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.REJECTED,
        context,
        false
      );

      expect(result.success).toBe(true);
      expect(result.newStatus).toBe(ApplicationStatus.REJECTED);
    });

    test('should handle application resubmission after rejection', async () => {
      // First, reject the application
      const rejectContext: TransitionContext = {
        userId: 'admin-123',
        notes: 'Please provide additional documentation',
      };

      const rejectResult = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.REJECTED,
        rejectContext,
        false
      );

      expect(rejectResult.success).toBe(true);

      // Then resubmit
      const resubmitContext: TransitionContext = {
        userId: 'user-123',
        notes: 'Additional documentation provided',
      };

      const resubmitResult = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.REJECTED,
        ApplicationStatus.APPLIED,
        resubmitContext,
        false
      );

      expect(resubmitResult.success).toBe(true);
      expect(resubmitResult.newStatus).toBe(ApplicationStatus.APPLIED);
    });
  });

  describe('Payment Processing Scenarios', () => {
    test('should handle successful payment verification', async () => {
      const result = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        PaymentStatus.PENDING,
        PaymentStatus.PAID,
        {
          adminId: 'admin-123',
          notes: 'Payment received and verified',
        }
      );

      expect(result.success).toBe(true);
      expect(result.newStatus).toBe(PaymentStatus.PAID);
    });

    test('should handle payment rejection with retry', async () => {
      // First, reject payment
      const rejectResult = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        PaymentStatus.PENDING,
        PaymentStatus.FAILED,
        {
          adminId: 'admin-123',
          notes: 'Invalid payment method',
        }
      );

      expect(rejectResult.success).toBe(true);
      expect(rejectResult.newStatus).toBe(PaymentStatus.FAILED);

      // Then retry payment
      const retryResult = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        PaymentStatus.FAILED,
        PaymentStatus.PENDING,
        {
          adminId: 'admin-123',
          notes: 'Payment resubmitted',
        }
      );

      expect(retryResult.success).toBe(true);
      expect(retryResult.newStatus).toBe(PaymentStatus.PENDING);
    });

    test('should handle payment refund', async () => {
      const result = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        PaymentStatus.PAID,
        PaymentStatus.REFUNDED,
        {
          adminId: 'admin-123',
          notes: 'Refund requested by user',
        }
      );

      expect(result.success).toBe(true);
      expect(result.newStatus).toBe(PaymentStatus.REFUNDED);
    });
  });

  describe('Error Handling and Validation', () => {
    test('should prevent invalid state transitions', () => {
      const invalidTransitions = [
        { from: ApplicationStatus.APPLIED, to: ApplicationStatus.ACTIVE },
        { from: ApplicationStatus.UNDER_REVIEW, to: ApplicationStatus.APPLIED },
        { from: ApplicationStatus.ACTIVE, to: ApplicationStatus.UNDER_REVIEW },
        { from: ApplicationStatus.REJECTED, to: ApplicationStatus.APPROVED },
      ];

      invalidTransitions.forEach(({ from, to }) => {
        const result = ApplicationStateMachine.validateApplicationTransition(from, to);
        expect(result.valid).toBe(false);
        expect(result.reason).toContain(`Cannot transition from ${from} to ${to}`);
      });
    });

    test('should enforce business rules for rejection', async () => {
      // Test rejection without reason
      const result1 = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.REJECTED,
        {} as TransitionContext, // No notes
        false
      );

      expect(result1.success).toBe(false);
      expect(result1.error).toBe('Rejection requires a reason to be provided');

      // Test rejection with reason
      const result2 = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.REJECTED,
        {
          userId: 'admin-123',
          notes: 'Valid rejection reason',
        },
        false
      );

      expect(result2.success).toBe(true);
    });

    test('should validate admin authorization for payment verification', async () => {
      // Test without admin ID
      const result1 = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        PaymentStatus.PENDING,
        PaymentStatus.PAID,
        {
          adminId: '', // Empty admin ID
          notes: 'Payment verified',
        }
      );

      expect(result1.success).toBe(false);
      expect(result1.error).toBe('Payment verification requires admin authorization');

      // Test with admin ID
      const result2 = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        PaymentStatus.PENDING,
        PaymentStatus.PAID,
        {
          adminId: 'admin-123',
          notes: 'Payment verified',
        }
      );

      expect(result2.success).toBe(true);
    });

    test('should enforce documentation for payment failures', async () => {
      // Test without failure notes
      const result1 = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        PaymentStatus.PENDING,
        PaymentStatus.FAILED,
        {
          adminId: 'admin-123',
          // No notes
        }
      );

      expect(result1.success).toBe(false);
      expect(result1.error).toBe('Payment failure requires explanation');

      // Test with failure notes
      const result2 = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        PaymentStatus.PENDING,
        PaymentStatus.FAILED,
        {
          adminId: 'admin-123',
          notes: 'Invalid payment details',
        }
      );

      expect(result2.success).toBe(true);
    });
  });

  describe('Workflow Integration Scenarios', () => {
    test('should handle complex treaty activation scenarios', async () => {
      // Scenario 1: Paid treaty with payment verification
      const paidTreatyResult = await WorkflowCoordinator.handleApplicationWorkflow(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        PaymentStatus.NOT_REQUIRED,
        ApplicationStatus.APPROVED,
        {
          userId: 'admin-123',
          notes: 'Approved for paid treaty',
        },
        true
      );

      expect(paidTreatyResult.applicationResult.success).toBe(true);
      expect(paidTreatyResult.applicationResult.requiresPayment).toBe(true);
      expect(paidTreatyResult.paymentResult?.newStatus).toBe(PaymentStatus.AWAITING_PAYMENT);

      // Scenario 2: Free treaty with auto-activation
      const freeTreatyResult = await WorkflowCoordinator.handleApplicationWorkflow(
        'app-456',
        ApplicationStatus.UNDER_REVIEW,
        PaymentStatus.NOT_REQUIRED,
        ApplicationStatus.APPROVED,
        {
          userId: 'admin-123',
          notes: 'Approved for free treaty',
        },
        false
      );

      expect(freeTreatyResult.applicationResult.success).toBe(true);
      expect(freeTreatyResult.applicationResult.autoActivate).toBe(true);
      expect(freeTreatyResult.activationRequired).toBe(true);
    });

    test('should handle concurrent application workflows', async () => {
      // Simulate multiple applications being processed simultaneously
      const applications = [
        { id: 'app-1', requiresPayment: true },
        { id: 'app-2', requiresPayment: false },
        { id: 'app-3', requiresPayment: true },
      ];

      const results = await Promise.all(applications.map(app => 
        WorkflowCoordinator.handleApplicationWorkflow(
          app.id,
          ApplicationStatus.UNDER_REVIEW,
          PaymentStatus.NOT_REQUIRED,
          ApplicationStatus.APPROVED,
          {
            userId: 'admin-123',
            notes: `Processing application ${app.id}`,
          },
          app.requiresPayment
        )
      ));

      // All workflows should succeed
      results.forEach((result, index) => {
        expect(result.applicationResult.success).toBe(true);
        
        if (applications[index].requiresPayment) {
          expect(result.applicationResult.requiresPayment).toBe(true);
          expect(result.paymentResult?.newStatus).toBe(PaymentStatus.AWAITING_PAYMENT);
        } else {
          expect(result.applicationResult.autoActivate).toBe(true);
          expect(result.activationRequired).toBe(true);
        }
      });
    });

    test('should handle error recovery scenarios', async () => {
      // Test failed transition recovery
      const failedResult = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.REJECTED,
        {} as TransitionContext, // Missing notes
        false
      );

      expect(failedResult.success).toBe(false);

      // Recovery with proper notes
      const recoveryResult = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.REJECTED,
        {
          userId: 'admin-123',
          notes: 'Proper rejection reason provided',
        },
        false
      );

      expect(recoveryResult.success).toBe(true);
    });
  });

  describe('Status Reporting and User Experience', () => {
    test('should provide accurate status descriptions for users', () => {
      const statusDescriptions = [
        { status: ApplicationStatus.APPLIED, expectedDescription: 'Application submitted and awaiting review' },
        { status: ApplicationStatus.UNDER_REVIEW, expectedDescription: 'Application is currently under review' },
        { status: ApplicationStatus.APPROVED, expectedDescription: 'Application approved - payment may be required' },
        { status: ApplicationStatus.REJECTED, expectedDescription: 'Application was rejected' },
        { status: ApplicationStatus.ACTIVE, expectedDescription: 'Treaty is active and in good standing' },
      ];

      statusDescriptions.forEach(({ status, expectedDescription }) => {
        const description = ApplicationStateMachine.getStatusDescription(status);
        expect(description).toBe(expectedDescription);
      });
    });

    test('should provide accurate payment status descriptions', () => {
      const paymentStatusDescriptions = [
        { status: PaymentStatus.NOT_REQUIRED, expectedDescription: 'No payment required' },
        { status: PaymentStatus.AWAITING_PAYMENT, expectedDescription: 'Payment is required to activate treaty' },
        { status: PaymentStatus.PENDING, expectedDescription: 'Payment submitted and awaiting verification' },
        { status: PaymentStatus.PAID, expectedDescription: 'Payment verified and complete' },
        { status: PaymentStatus.FAILED, expectedDescription: 'Payment verification failed' },
        { status: PaymentStatus.REFUNDED, expectedDescription: 'Payment has been refunded' },
      ];

      paymentStatusDescriptions.forEach(({ status, expectedDescription }) => {
        const description = PaymentStateMachine.getPaymentStatusDescription(status);
        expect(description).toBe(expectedDescription);
      });
    });

    test('should guide users through next steps', () => {
      const nextSteps = [
        { currentStatus: ApplicationStatus.APPLIED, expectedNext: ['UNDER_REVIEW', 'REJECTED'] },
        { currentStatus: ApplicationStatus.UNDER_REVIEW, expectedNext: ['APPROVED', 'REJECTED'] },
        { currentStatus: ApplicationStatus.APPROVED, expectedNext: ['ACTIVE'] },
        { currentStatus: ApplicationStatus.REJECTED, expectedNext: ['APPLIED'] },
        { currentStatus: ApplicationStatus.ACTIVE, expectedNext: [] },
      ];

      nextSteps.forEach(({ currentStatus, expectedNext }) => {
        const nextStatuses = ApplicationStateMachine.getNextExpectedStatus(currentStatus);
        expect(nextStatuses).toEqual(expect.arrayContaining(expectedNext));
      });
    });
  });
});