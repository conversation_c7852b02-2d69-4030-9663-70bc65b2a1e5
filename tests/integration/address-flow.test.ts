import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CreateUserTab } from '../../src/components/users/manage/CreateUserTab';
import { UpdateUserTab } from '../../src/components/users/manage/UpdateUserTab';
import UserProfile from '../../src/components/UserProfile';
import { useSession } from 'next-auth/react';

// Mock dependencies
jest.mock('next-auth/react');
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  }
}));

const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock data for complete integration testing
const mockCountries = [
  { id: '1', name: 'United States' },
  { id: '2', name: 'United Kingdom' },
  { id: '3', name: 'Canada' }
];

const mockUSRegions = [
  { id: '1', name: 'California', code: 'CA' },
  { id: '2', name: 'Texas', code: 'TX' },
  { id: '3', name: 'New York', code: 'NY' }
];

const mockUKRegions = [
  { id: '4', name: 'England', code: 'ENG' },
  { id: '5', name: 'Scotland', code: 'SCT' },
  { id: '6', name: 'Wales', code: 'WLS' }
];

const mockUSAddressFormat = {
  addressFormat: {
    id: 1,
    countryId: 1,
    postalCodeLabel: 'ZIP Code',
    postalCodeFormat: '^\\d{5}(-\\d{4})?$',
    postalCodeRequired: true,
    regionLabel: 'State',
    regionRequired: true,
    townLabel: 'City',
    townRequired: true,
    addressTemplate: '{street}\\n{town}, {region} {postalCode}\\n{country}',
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  }
};

const mockUKAddressFormat = {
  addressFormat: {
    id: 2,
    countryId: 2,
    postalCodeLabel: 'Postcode',
    postalCodeFormat: '^[A-Z]{1,2}\\d[A-Z\\d]? ?\\d[A-Z]{2}$',
    postalCodeRequired: true,
    regionLabel: 'County',
    regionRequired: false,
    townLabel: 'Town/City',
    townRequired: true,
    addressTemplate: '{street}\\n{town}\\n{region}\\n{postalCode}\\n{country}',
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  }
};

const mockUser = {
  id: '123',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '******-0123',
  mobile: '******-0456',
  streetAddress1: '123 Main Street',
  streetAddress2: 'Apt 4B',
  town: 'Springfield',
  city: 'Springfield',
  country: 'United States',
  postalCode: '90210',
  regionId: '1',
  regionText: 'California',
  dob: '1980-01-01',
  license: 'DL123456',
  passport: 'PA987654',
  peaceAmbassadorNumber: 'PA-001',
  tradeTreatyNumber: 'TT-001',
  bio: 'Test bio',
  titleId: '1',
  positionId: '1',
  position: 'Ambassador'
};

const mockSession = {
  user: {
    id: '123',
    name: 'John Doe',
    email: '<EMAIL>'
  }
};

describe('Address Form Integration Tests', () => {
  const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default fetch implementation
    mockFetch.mockImplementation((url: string, options?: any) => {
      if (url.includes('/api/positions/titles')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve([])
        });
      }
      if (url.includes('/api/positions/positions')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ positions: [] })
        });
      }
      if (url.includes('/api/ordinance-types')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ ordinanceTypes: [] })
        });
      }
      if (url.includes('/api/countries') && url.includes('q=')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockCountries)
        });
      }
      if (url.includes('/api/regions?countryId=1')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockUSRegions)
        });
      }
      if (url.includes('/api/regions?countryId=2')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockUKRegions)
        });
      }
      if (url.includes('/api/countries/1/address-format')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockUSAddressFormat)
        });
      }
      if (url.includes('/api/countries/2/address-format')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockUKAddressFormat)
        });
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({})
      });
    });
  });

  describe('Complete Address Form Flow - Create User', () => {
    test('country selection triggers region loading and field label updates', async () => {
      render(<CreateUserTab />);
      
      // Navigate to contact details tab
      fireEvent.click(screen.getByText('Contact Details'));
      
      // Fill in required fields first
      await userEvent.type(screen.getByLabelText(/first name/i), 'John');
      await userEvent.type(screen.getByLabelText(/surname/i), 'Doe');
      await userEvent.type(screen.getByLabelText(/email/i), '<EMAIL>');
      
      // Select country
      const countryInput = screen.getByLabelText(/country/i);
      await userEvent.type(countryInput, 'United States');
      
      // Wait for country dropdown and select
      await waitFor(() => {
        if (screen.queryByText('United States')) {
          fireEvent.mouseDown(screen.getByText('United States'));
        }
      });
      
      // Verify API calls were made
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/regions?countryId=1');
        expect(mockFetch).toHaveBeenCalledWith('/api/countries/1/address-format');
      });
      
      // Verify field labels are updated based on address format
      await waitFor(() => {
        // US-specific labels should be applied
        expect(screen.queryByLabelText(/zip code/i) || screen.queryByLabelText(/postal code/i)).toBeInTheDocument();
        expect(screen.queryByLabelText(/state/i) || screen.queryByLabelText(/region/i)).toBeInTheDocument();
      });
    });

    test('region selection stores correct regionId and clears regionText', async () => {
      render(<CreateUserTab />);
      
      fireEvent.click(screen.getByText('Contact Details'));
      
      // Fill in required fields
      await userEvent.type(screen.getByLabelText(/first name/i), 'John');
      await userEvent.type(screen.getByLabelText(/surname/i), 'Doe');
      await userEvent.type(screen.getByLabelText(/email/i), '<EMAIL>');
      
      // Select country first
      const countryInput = screen.getByLabelText(/country/i);
      await userEvent.type(countryInput, 'United States');
      
      await waitFor(() => {
        if (screen.queryByText('United States')) {
          fireEvent.mouseDown(screen.getByText('United States'));
        }
      });
      
      // Wait for regions to load and select a region
      await waitFor(() => {
        const regionSelect = screen.queryByLabelText(/state/i) || screen.queryByLabelText(/region/i);
        if (regionSelect && regionSelect.tagName === 'SELECT') {
          fireEvent.change(regionSelect, { target: { value: '1' } });
        }
      });
      
      // Verify that selecting a region clears any existing regionText
      // (This would be verified in the component state, here we test the UI behavior)
      await waitFor(() => {
        const regionSelect = screen.queryByLabelText(/state/i) || screen.queryByLabelText(/region/i);
        if (regionSelect) {
          expect(regionSelect).toBeInTheDocument();
        }
      });
    });

    test('form validation enforces country-specific requirements', async () => {
      render(<CreateUserTab />);
      
      fireEvent.click(screen.getByText('Contact Details'));
      
      // Fill basic required fields
      await userEvent.type(screen.getByLabelText(/first name/i), 'John');
      await userEvent.type(screen.getByLabelText(/surname/i), 'Doe');
      await userEvent.type(screen.getByLabelText(/email/i), '<EMAIL>');
      
      // Select US (which requires ZIP code and state)
      const countryInput = screen.getByLabelText(/country/i);
      await userEvent.type(countryInput, 'United States');
      
      await waitFor(() => {
        if (screen.queryByText('United States')) {
          fireEvent.mouseDown(screen.getByText('United States'));
        }
      });
      
      // Fill address fields but leave required ones empty
      const streetInput = screen.getByLabelText(/street address/i);
      await userEvent.type(streetInput, '123 Main St');
      
      // Try to submit without required postal code
      const submitButton = screen.getByText('Create User');
      fireEvent.click(submitButton);
      
      // Should show validation error (behavior depends on implementation)
      await waitFor(() => {
        // Validation would be triggered - exact behavior depends on form validation implementation
        expect(mockFetch).toHaveBeenCalledWith('/api/countries/1/address-format');
      });
    });

    test('complete form submission includes all address structure fields', async () => {
      let submittedData: any;
      
      mockFetch.mockImplementation((url: string, options?: any) => {
        if (url.includes('/api/users') && options?.method === 'POST') {
          submittedData = JSON.parse(options.body);
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ id: '123', message: 'User created' })
          });
        }
        return mockFetch.getMockImplementation()!(url, options);
      });
      
      render(<CreateUserTab />);
      
      fireEvent.click(screen.getByText('Contact Details'));
      
      // Fill all required fields
      await userEvent.type(screen.getByLabelText(/first name/i), 'John');
      await userEvent.type(screen.getByLabelText(/surname/i), 'Doe');
      await userEvent.type(screen.getByLabelText(/email/i), '<EMAIL>');
      
      const streetAddress1Input = screen.getByLabelText(/street address/i);
      await userEvent.type(streetAddress1Input, '123 Main Street');
      
      const streetAddress2Input = screen.getByLabelText(/apt.*suite.*unit/i) || screen.getByLabelText(/address line 2/i);
      await userEvent.type(streetAddress2Input, 'Apt 4B');
      
      const townInput = screen.getByLabelText(/town/i) || screen.getByLabelText(/city/i);
      await userEvent.type(townInput, 'Springfield');
      
      const postalInput = screen.getByLabelText(/postal code/i);
      await userEvent.type(postalInput, '90210');
      
      // Submit form
      const submitButton = screen.getByText('Create User');
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(submittedData).toBeDefined();
        expect(submittedData).toHaveProperty('streetAddress1', '123 Main Street');
        expect(submittedData).toHaveProperty('streetAddress2', 'Apt 4B');
        expect(submittedData).toHaveProperty('town', 'Springfield');
        expect(submittedData).toHaveProperty('postalCode', '90210');
        expect(submittedData).not.toHaveProperty('streetAddress'); // No legacy field
      });
    });
  });

  describe('Address Data Flow - Update User', () => {
    beforeEach(() => {
      // Mock user search and retrieval for UpdateUserTab
      mockFetch.mockImplementation((url: string, options?: any) => {
        if (url.includes('/api/users') && url.includes('search=')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              users: [{
                id: '123',
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
                position: 'Ambassador'
              }]
            })
          });
        }
        if (url.includes('/api/users/123') && !options?.method) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ user: mockUser })
          });
        }
        if (url.includes('/api/users/123') && options?.method === 'PUT') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ success: true, message: 'User updated successfully' })
          });
        }
        return mockFetch.getMockImplementation()!(url, options);
      });
    });

    test('user update flow preserves address data integrity', async () => {
      render(<UpdateUserTab />);
      
      // Search for user
      const searchInput = screen.getByPlaceholderText('Search by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });
      
      fireEvent.mouseDown(screen.getByText('John Doe'));
      
      // Wait for user data to load and verify existing data is preserved
      await waitFor(() => {
        expect(screen.getByDisplayValue('123 Main Street')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Apt 4B')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Springfield')).toBeInTheDocument();
        expect(screen.getByDisplayValue('90210')).toBeInTheDocument();
        expect(screen.getByDisplayValue('United States')).toBeInTheDocument();
      });
      
      // Update address fields
      const streetAddress2Input = screen.getByDisplayValue('Apt 4B');
      await userEvent.clear(streetAddress2Input);
      await userEvent.type(streetAddress2Input, 'Suite 200');
      
      // Submit update
      const updateButton = screen.getByText('Update User');
      fireEvent.click(updateButton);
      
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/users/123', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: expect.stringContaining('Suite 200')
        });
      });
    });

    test('backward compatibility with existing user address data', async () => {
      const legacyUser = {
        ...mockUser,
        streetAddress: '456 Legacy Street', // Old format
        streetAddress1: '',
        streetAddress2: ''
      };
      
      mockFetch.mockImplementation((url: string, options?: any) => {
        if (url.includes('/api/users/123') && !options?.method) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ user: legacyUser })
          });
        }
        return mockFetch.getMockImplementation()!(url, options);
      });
      
      render(<UpdateUserTab />);
      
      const searchInput = screen.getByPlaceholderText('Search by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });
      
      fireEvent.mouseDown(screen.getByText('John Doe'));
      
      // Verify legacy address is mapped to new field
      await waitFor(() => {
        expect(screen.getByDisplayValue('456 Legacy Street')).toBeInTheDocument();
      });
    });
  });

  describe('Cross-Component Address Data Flow', () => {
    test('address data consistency between create, update, and profile display', async () => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated'
      });
      
      // Mock user profile API
      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/users/123')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({ user: mockUser })
          });
        }
        if (url.includes('/api/countries?q=United%20States')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve([{ id: '1', name: 'United States' }])
          });
        }
        if (url.includes('/api/countries/1/address-format')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockUSAddressFormat)
          });
        }
        return mockFetch.getMockImplementation()!(url);
      });
      
      // Render UserProfile and verify address formatting
      render(<UserProfile />);
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });
      
      // Verify address is formatted using country template
      await waitFor(() => {
        expect(screen.getByText(/123 Main Street/)).toBeInTheDocument();
        expect(screen.getByText(/Springfield/)).toBeInTheDocument();
        expect(screen.getByText(/90210/)).toBeInTheDocument();
      });
      
      // Verify address format API was called for formatting
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/countries?q=United%20States');
        expect(mockFetch).toHaveBeenCalledWith('/api/countries/1/address-format');
      });
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('handles invalid region/country combinations gracefully', async () => {
      render(<CreateUserTab />);
      
      fireEvent.click(screen.getByText('Contact Details'));
      
      // Fill basic fields
      await userEvent.type(screen.getByLabelText(/first name/i), 'John');
      await userEvent.type(screen.getByLabelText(/surname/i), 'Doe');
      await userEvent.type(screen.getByLabelText(/email/i), '<EMAIL>');
      
      // Mock API to return error for invalid region
      mockFetch.mockImplementation((url: string, options?: any) => {
        if (url.includes('/api/users') && options?.method === 'POST') {
          return Promise.resolve({
            ok: false,
            status: 400,
            json: () => Promise.resolve({ error: 'Invalid region for the selected country' })
          });
        }
        return mockFetch.getMockImplementation()!(url, options);
      });
      
      // Try to submit with invalid data
      const submitButton = screen.getByText('Create User');
      fireEvent.click(submitButton);
      
      // Should handle error gracefully
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/users', expect.objectContaining({
          method: 'POST'
        }));
      });
    });

    test('handles countries without regions appropriately', async () => {
      mockFetch.mockImplementation((url: string) => {
        if (url.includes('/api/regions?countryId=3')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve([]) // No regions for this country
          });
        }
        return mockFetch.getMockImplementation()!(url);
      });
      
      render(<CreateUserTab />);
      
      fireEvent.click(screen.getByText('Contact Details'));
      
      // Fill basic fields and select a country without regions
      await userEvent.type(screen.getByLabelText(/first name/i), 'John');
      await userEvent.type(screen.getByLabelText(/surname/i), 'Doe');
      await userEvent.type(screen.getByLabelText(/email/i), '<EMAIL>');
      
      const countryInput = screen.getByLabelText(/country/i);
      await userEvent.type(countryInput, 'Canada');
      
      await waitFor(() => {
        if (screen.queryByText('Canada')) {
          fireEvent.mouseDown(screen.getByText('Canada'));
        }
      });
      
      // Should show free-form region input instead of dropdown
      await waitFor(() => {
        const regionInput = screen.queryByLabelText(/region/i);
        if (regionInput && regionInput.tagName === 'INPUT') {
          expect(regionInput).toBeInTheDocument();
        }
      });
    });
  });

  describe('Performance and User Experience', () => {
    test('debounced region search performs efficiently', async () => {
      render(<UpdateUserTab />);
      
      const searchInput = screen.getByPlaceholderText('Search by name or email...');
      await userEvent.type(searchInput, 'John Doe');
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });
      
      fireEvent.mouseDown(screen.getByText('John Doe'));
      
      await waitFor(() => {
        expect(screen.getByDisplayValue('John')).toBeInTheDocument();
      });
      
      // Country change should trigger regions loading efficiently
      const countryInput = screen.getByDisplayValue('United States');
      await userEvent.clear(countryInput);
      await userEvent.type(countryInput, 'United Kingdom');
      
      await waitFor(() => {
        if (screen.queryByText('United Kingdom')) {
          fireEvent.mouseDown(screen.getByText('United Kingdom'));
        }
      });
      
      // Verify efficient API usage (debounced, not excessive calls)
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/regions?countryId=2');
      });
      
      // Should clear previous region selection when country changes
      await waitFor(() => {
        // Verify region field is cleared/reset
        const regionSelect = screen.queryByLabelText(/county/i) || screen.queryByLabelText(/region/i);
        if (regionSelect) {
          expect(regionSelect).toBeInTheDocument();
        }
      });
    });
  });
});
