// Mock next/server to avoid Request dependency errors in test environment
jest.mock('next/server', () => ({
  NextResponse: {
    json: (body: any, init?: any) => ({ status: init?.status ?? 200, json: async () => body }),
  },
}));

import { NextRequest } from 'next/server';
import { GET, POST } from '../../src/app/api/treaty-applications/route';
import { PUT } from '../../src/app/api/admin/treaty-applications/[id]/status/route';
import { POST as SUBMIT_PAYMENT } from '../../src/app/api/treaty-payments/route';
import { PUT as VERIFY_PAYMENT } from '../../src/app/api/admin/treaty-payments/[id]/verify/route';
import { PUT as UPDATE_PRICING } from '../../src/app/api/admin/treaty-types/[id]/pricing/route';
import { GET as GET_TREATY_TYPES } from '../../src/app/api/treaty-types/route';
import { prisma } from '../../src/lib/prisma';
import { ApplicationStatus, PaymentStatus } from '@prisma/client';
import { ApplicationStateMachine, WorkflowCoordinator, TransitionContext } from '../../src/lib/workflow/StateMachine';

// Mock dependencies
jest.mock('next-auth/next');
jest.mock('../../src/lib/prisma');
jest.mock('../../src/lib/auth', () => ({
  authOptions: {
    adapter: {},
    providers: [],
  },
}));

const { getServerSession } = require('next-auth/next');
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;


const mockPrisma = prisma as jest.Mocked<typeof prisma>;

// Test data
const mockUserSession = {
  user: {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
  },
};

const mockAdminSession = {
  user: {
    id: 'admin-123',
    email: '<EMAIL>',
    name: 'Admin User',
  },
};

const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  name: 'Test User',
  userRoles: [],
  userProfile: {
    peaceAmbassadorNumber: 'PA123456',
  },
};

const mockAdminUser = {
  id: 'admin-123',
  email: '<EMAIL>',
  name: 'Admin User',
  userRoles: [
    {
      role: {
        name: 'admin',
      },
    },
  ],
};

const mockTreatyType = {
  id: 'treaty-type-1',
  name: 'Test Treaty',
  description: 'Test treaty description',
  price: 100,
  currency: 'USD',
  requiresPayment: true,
  paymentDeadlineDays: 30,
  isActive: true,
};

const mockApplication = {
  id: 'app-123',
  userId: 'user-123',
  treatyTypeId: 'treaty-type-1',
  status: ApplicationStatus.APPLIED,
  paymentStatus: PaymentStatus.NOT_REQUIRED,
  appliedAt: new Date(),
  treatyType: mockTreatyType,
  payments: [],
};

const mockPayment = {
  id: 'payment-123',
  userTreatyTypeId: 'app-123',
  amount: 100,
  currency: 'USD',
  paymentMethod: 'CASH',
  status: PaymentStatus.AWAITING_PAYMENT,
  paymentDate: new Date(),
  notes: 'Test payment',
  userTreatyType: mockApplication,
};

// Helper function to create mock request
function createMockRequest(
  url: string,
  options: {
    method?: string;
    body?: any;
    headers?: Record<string, string>;
  } = {}
): NextRequest {
  return {
    url,
    method: options.method || 'GET',
    headers: {
      get: jest.fn((name: string) => options.headers?.[name] || ''),
      'x-forwarded-for': '127.0.0.1',
      'user-agent': 'test-agent',
    },
    json: jest.fn().mockResolvedValue(options.body || {}),
  } as unknown as NextRequest;
}

// Helper function to create mock params
function createMockParams(params: Record<string, string>) {
  return Promise.resolve(params);
}

describe('Treaty Application Workflow Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default session mock
    mockGetServerSession.mockResolvedValue(mockUserSession);
    
    // Default Prisma mocks
    mockPrisma.user.findUnique.mockResolvedValue(mockUser);
    mockPrisma.userProfile.findUnique.mockResolvedValue(mockUser.userProfile);
    mockPrisma.treatyType.findUnique.mockResolvedValue(mockTreatyType);
    mockPrisma.userTreatyType.findFirst.mockResolvedValue(null);
    mockPrisma.userTreatyType.findMany.mockResolvedValue([]);
    mockPrisma.userTreatyType.count.mockResolvedValue(0);
    mockPrisma.payment.findFirst.mockResolvedValue(null);
    mockPrisma.payment.findMany.mockResolvedValue([]);
    mockPrisma.payment.count.mockResolvedValue(0);
    mockPrisma.auditLog.create.mockResolvedValue({} as any);
  });

  describe('State Machine Tests', () => {
    test('should validate valid status transitions', () => {
      const result = ApplicationStateMachine.validateApplicationTransition(
        ApplicationStatus.APPLIED,
        ApplicationStatus.UNDER_REVIEW
      );
      
      expect(result.valid).toBe(true);
    });

    test('should reject invalid status transitions', () => {
      const result = ApplicationStateMachine.validateApplicationTransition(
        ApplicationStatus.APPLIED,
        ApplicationStatus.ACTIVE
      );
      
      expect(result.valid).toBe(false);
      expect(result.reason).toContain('Cannot transition from APPLIED to ACTIVE');
    });

    test('should get valid transitions for current status', () => {
      const transitions = ApplicationStateMachine.getValidTransitions(ApplicationStatus.APPLIED);
      
      expect(transitions).toContain(ApplicationStatus.UNDER_REVIEW);
      expect(transitions).toContain(ApplicationStatus.REJECTED);
    });

    test('should handle business rule validation for rejection without reason', () => {
      const result = ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.APPLIED,
        ApplicationStatus.REJECTED,
        {} as TransitionContext
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('Rejection requires a reason to be provided');
    });

    test('should handle payment requirement for approved treaties', async () => {
      const result = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.APPROVED,
        {} as TransitionContext,
        true // requiresPayment
      );

      expect(result.success).toBe(true);
      expect(result.requiresPayment).toBe(true);
    });

    test('should auto-activate free treaties', async () => {
      const result = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.APPROVED,
        {} as TransitionContext,
        false // requiresPayment
      );

      expect(result.success).toBe(true);
      expect(result.autoActivate).toBe(true);
    });
  });

  describe('Treaty Application Creation', () => {
    test('should create treaty application successfully', async () => {
      const request = createMockRequest('/api/treaty-applications', {
        method: 'POST',
        body: {
          treatyTypeId: 'treaty-type-1',
          submissionReason: 'Test submission',
          notes: 'Test notes',
        },
      });

      mockPrisma.userTreatyType.create.mockResolvedValue(mockApplication);

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.application).toEqual({
        id: 'app-123',
        userId: 'user-123',
        treatyTypeId: 'treaty-type-1',
        status: ApplicationStatus.APPLIED,
        paymentStatus: PaymentStatus.NOT_REQUIRED,
        appliedAt: mockApplication.appliedAt,
        notes: 'Test notes',
        treatyType: mockTreatyType,
      });

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'CREATE_TREATY_APPLICATION',
          resource: 'UserTreatyType',
          resourceId: 'app-123',
          success: true,
        })
      );
    });

    test('should reject application without peace ambassador status', async () => {
      mockPrisma.userProfile.findUnique.mockResolvedValue(null);

      const request = createMockRequest('/api/treaty-applications', {
        method: 'POST',
        body: {
          treatyTypeId: 'treaty-type-1',
          submissionReason: 'Test submission',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Peace Ambassador status is required to apply for treaties');
    });

    test('should reject duplicate application', async () => {
      mockPrisma.userTreatyType.findFirst.mockResolvedValue(mockApplication);

      const request = createMockRequest('/api/treaty-applications', {
        method: 'POST',
        body: {
          treatyTypeId: 'treaty-type-1',
          submissionReason: 'Test submission',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(409);
      expect(data.error).toBe('You already have an active or pending application for this treaty type');
    });
  });

  describe('Admin Status Updates', () => {
    beforeEach(() => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockAdminUser);
    });

    test('should update application status to UNDER_REVIEW', async () => {
      const request = createMockRequest('/api/admin/treaty-applications/app-123/status', {
        method: 'PUT',
        body: {
          status: ApplicationStatus.UNDER_REVIEW,
          reviewComments: 'Initial review',
        },
      });

      mockPrisma.userTreatyType.findUnique.mockResolvedValue(mockApplication);
      mockPrisma.userTreatyType.update.mockResolvedValue({
        ...mockApplication,
        status: ApplicationStatus.UNDER_REVIEW,
        reviewedAt: new Date(),
      });

      const response = await PUT(request, { params: createMockParams({ id: 'app-123' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.application.status).toBe(ApplicationStatus.UNDER_REVIEW);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'ADMIN_UPDATE_APPLICATION_STATUS',
          resource: 'UserTreatyType',
          resourceId: 'app-123',
          success: true,
        })
      );
    });

    test('should approve application and require payment', async () => {
      const request = createMockRequest('/api/admin/treaty-applications/app-123/status', {
        method: 'PUT',
        body: {
          status: ApplicationStatus.APPROVED,
          reviewComments: 'Application approved',
        },
      });

      const updatedApplication = {
        ...mockApplication,
        status: ApplicationStatus.APPROVED,
        paymentStatus: PaymentStatus.AWAITING_PAYMENT,
        approvedAt: new Date(),
        approvedBy: 'admin-123',
      };

      mockPrisma.userTreatyType.findUnique.mockResolvedValue(mockApplication);
      mockPrisma.userTreatyType.update.mockResolvedValue(updatedApplication);

      const response = await PUT(request, { params: createMockParams({ id: 'app-123' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.application.status).toBe(ApplicationStatus.APPROVED);
      expect(data.application.paymentStatus).toBe(PaymentStatus.AWAITING_PAYMENT);
    });

    test('should reject application with reason', async () => {
      const request = createMockRequest('/api/admin/treaty-applications/app-123/status', {
        method: 'PUT',
        body: {
          status: ApplicationStatus.REJECTED,
          rejectionReason: 'Insufficient documentation',
        },
      });

      const updatedApplication = {
        ...mockApplication,
        status: ApplicationStatus.REJECTED,
        rejectedAt: new Date(),
        rejectedBy: 'admin-123',
        rejectionReason: 'Insufficient documentation',
      };

      mockPrisma.userTreatyType.findUnique.mockResolvedValue(mockApplication);
      mockPrisma.userTreatyType.update.mockResolvedValue(updatedApplication);

      const response = await PUT(request, { params: createMockParams({ id: 'app-123' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.application.status).toBe(ApplicationStatus.REJECTED);
      expect(data.application.rejectionReason).toBe('Insufficient documentation');
    });

    test('should prevent invalid status transitions', async () => {
      const request = createMockRequest('/api/admin/treaty-applications/app-123/status', {
        method: 'PUT',
        body: {
          status: ApplicationStatus.ACTIVE, // Invalid transition from APPLIED
        },
      });

      mockPrisma.userTreatyType.findUnique.mockResolvedValue(mockApplication);

      const response = await PUT(request, { params: createMockParams({ id: 'app-123' }) });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Cannot transition from APPLIED to ACTIVE');
    });
  });

  describe('Payment Processing Workflow', () => {
    test('should submit payment successfully', async () => {
      const approvedApplication = {
        ...mockApplication,
        status: ApplicationStatus.APPROVED,
        paymentStatus: PaymentStatus.AWAITING_PAYMENT,
      };

      const request = createMockRequest('/api/treaty-payments', {
        method: 'POST',
        body: {
          applicationId: 'app-123',
          amount: 100,
          paymentMethod: 'CASH',
          payerName: 'John Doe',
          payerContact: '<EMAIL>',
          notes: 'Test payment',
        },
      });

      mockPrisma.userTreatyType.findUnique.mockResolvedValue(approvedApplication);
      mockPrisma.payment.create.mockResolvedValue(mockPayment);

      const response = await SUBMIT_PAYMENT(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.payment.status).toBe(PaymentStatus.AWAITING_PAYMENT);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'SUBMIT_PAYMENT',
          resource: 'Payment',
          resourceId: 'payment-123',
          success: true,
        })
      );
    });

    test('should reject payment for non-approved applications', async () => {
      const request = createMockRequest('/api/treaty-payments', {
        method: 'POST',
        body: {
          applicationId: 'app-123',
          amount: 100,
          paymentMethod: 'CASH',
          payerName: 'John Doe',
          payerContact: '<EMAIL>',
        },
      });

      mockPrisma.userTreatyType.findUnique.mockResolvedValue(mockApplication); // Status: APPLIED

      const response = await SUBMIT_PAYMENT(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Payment can only be submitted for approved applications');
    });

    test('should reject payment with incorrect amount', async () => {
      const approvedApplication = {
        ...mockApplication,
        status: ApplicationStatus.APPROVED,
        paymentStatus: PaymentStatus.AWAITING_PAYMENT,
      };

      const request = createMockRequest('/api/treaty-payments', {
        method: 'POST',
        body: {
          applicationId: 'app-123',
          amount: 50, // Incorrect amount
          paymentMethod: 'CASH',
          payerName: 'John Doe',
          payerContact: '<EMAIL>',
        },
      });

      mockPrisma.userTreatyType.findUnique.mockResolvedValue(approvedApplication);

      const response = await SUBMIT_PAYMENT(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Payment amount must be exactly 100 USD');
    });

    test('should prevent duplicate payment submissions', async () => {
      const approvedApplication = {
        ...mockApplication,
        status: ApplicationStatus.APPROVED,
        paymentStatus: PaymentStatus.AWAITING_PAYMENT,
      };

      const request = createMockRequest('/api/treaty-payments', {
        method: 'POST',
        body: {
          applicationId: 'app-123',
          amount: 100,
          paymentMethod: 'CASH',
          payerName: 'John Doe',
          payerContact: '<EMAIL>',
        },
      });

      mockPrisma.userTreatyType.findUnique.mockResolvedValue(approvedApplication);
      mockPrisma.payment.findFirst.mockResolvedValue(mockPayment);

      const response = await SUBMIT_PAYMENT(request);
      const data = await response.json();

      expect(response.status).toBe(409);
      expect(data.error).toBe('A payment is already pending for this application');
    });
  });

  describe('Payment Verification', () => {
    beforeEach(() => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockAdminUser);
    });

    test('should verify payment and activate treaty', async () => {
      const request = createMockRequest('/api/admin/treaty-payments/payment-123/verify', {
        method: 'PUT',
        body: {
          action: 'verify',
          verificationNotes: 'Payment verified',
          receiptNumber: 'RC123456',
        },
      });

      const verifiedPayment = {
        ...mockPayment,
        status: PaymentStatus.PAID,
        processedBy: 'admin-123',
      };

      mockPrisma.payment.findUnique.mockResolvedValue(mockPayment);
      mockPrisma.payment.update.mockResolvedValue(verifiedPayment);

      const response = await VERIFY_PAYMENT(request, { params: createMockParams({ id: 'payment-123' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.payment.status).toBe(PaymentStatus.PAID);

      expect(mockPrisma.userTreatyType.update).toHaveBeenCalledWith({
        where: { id: 'app-123' },
        data: {
          paymentStatus: PaymentStatus.PAID,
          status: ApplicationStatus.ACTIVE,
        },
      });

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'ACTIVATE_TREATY',
          resource: 'UserTreatyType',
          resourceId: 'app-123',
          success: true,
        })
      );
    });

    test('should reject payment and reset status', async () => {
      const request = createMockRequest('/api/admin/treaty-payments/payment-123/verify', {
        method: 'PUT',
        body: {
          action: 'reject',
          verificationNotes: 'Invalid payment',
        },
      });

      const rejectedPayment = {
        ...mockPayment,
        status: PaymentStatus.FAILED,
        processedBy: 'admin-123',
      };

      mockPrisma.payment.findUnique.mockResolvedValue(mockPayment);
      mockPrisma.payment.update.mockResolvedValue(rejectedPayment);

      const response = await VERIFY_PAYMENT(request, { params: createMockParams({ id: 'payment-123' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.payment.status).toBe(PaymentStatus.FAILED);

      expect(mockPrisma.userTreatyType.update).toHaveBeenCalledWith({
        where: { id: 'app-123' },
        data: {
          paymentStatus: PaymentStatus.AWAITING_PAYMENT,
        },
      });
    });

    test('should prevent verification of non-verifiable payments', async () => {
      const paidPayment = {
        ...mockPayment,
        status: PaymentStatus.PAID,
      };

      const request = createMockRequest('/api/admin/treaty-payments/payment-123/verify', {
        method: 'PUT',
        body: {
          action: 'verify',
          verificationNotes: 'Already verified',
        },
      });

      mockPrisma.payment.findUnique.mockResolvedValue(paidPayment);

      const response = await VERIFY_PAYMENT(request, { params: createMockParams({ id: 'payment-123' }) });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Cannot verify payment with status PAID');
    });
  });

  describe('Treaty Types Management', () => {
    beforeEach(() => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockAdminUser);
    });

    test('should update treaty type pricing', async () => {
      const request = createMockRequest('/api/admin/treaty-types/treaty-type-1/pricing', {
        method: 'PUT',
        body: {
          price: 150,
          currency: 'USD',
          requiresPayment: true,
          paymentDeadlineDays: 45,
          autoApprove: false,
        },
      });

      const updatedTreatyType = {
        ...mockTreatyType,
        price: 150,
        paymentDeadlineDays: 45,
      };

      mockPrisma.treatyType.findUnique.mockResolvedValue(mockTreatyType);
      mockPrisma.treatyType.update.mockResolvedValue(updatedTreatyType);

      const response = await UPDATE_PRICING(request, { params: createMockParams({ id: 'treaty-type-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.treatyType.price).toBe(150);
      expect(data.treatyType.paymentDeadlineDays).toBe(45);

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'UPDATE_TREATY_TYPE_PRICING',
          resource: 'TreatyType',
          resourceId: 'treaty-type-1',
          success: true,
        })
      );
    });

    test('should set price to 0 when payment is not required', async () => {
      const request = createMockRequest('/api/admin/treaty-types/treaty-type-1/pricing', {
        method: 'PUT',
        body: {
          price: 100,
          requiresPayment: false,
        },
      });

      const updatedTreatyType = {
        ...mockTreatyType,
        price: 0,
        requiresPayment: false,
      };

      mockPrisma.treatyType.findUnique.mockResolvedValue(mockTreatyType);
      mockPrisma.treatyType.update.mockResolvedValue(updatedTreatyType);

      const response = await UPDATE_PRICING(request, { params: createMockParams({ id: 'treaty-type-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.treatyType.price).toBe(0);
      expect(data.treatyType.requiresPayment).toBe(false);
    });

    test('should validate currency codes', async () => {
      const request = createMockRequest('/api/admin/treaty-types/treaty-type-1/pricing', {
        method: 'PUT',
        body: {
          price: 100,
          currency: 'INVALID', // Invalid currency
        },
      });

      mockPrisma.treatyType.findUnique.mockResolvedValue(mockTreatyType);

      const response = await UPDATE_PRICING(request, { params: createMockParams({ id: 'treaty-type-1' }) });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid currency code');
    });

    test('should prevent negative prices', async () => {
      const request = createMockRequest('/api/admin/treaty-types/treaty-type-1/pricing', {
        method: 'PUT',
        body: {
          price: -100, // Negative price
        },
      });

      mockPrisma.treatyType.findUnique.mockResolvedValue(mockTreatyType);

      const response = await UPDATE_PRICING(request, { params: createMockParams({ id: 'treaty-type-1' }) });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Price must be a non-negative number');
    });
  });

  describe('Complete Workflow Integration', () => {
    test('should handle complete application lifecycle with payment', async () => {
      // This test simulates the complete workflow:
      // 1. Create application
      // 2. Admin reviews and approves
      // 3. User submits payment
      // 4. Admin verifies payment
      // 5. Treaty becomes active

      // Step 1: Create application
      mockGetServerSession.mockResolvedValue(mockUserSession);
      mockPrisma.userTreatyType.create.mockResolvedValue(mockApplication);

      const createRequest = createMockRequest('/api/treaty-applications', {
        method: 'POST',
        body: {
          treatyTypeId: 'treaty-type-1',
          submissionReason: 'Complete workflow test',
        },
      });

      const createResponse = await POST(createRequest);
      expect(createResponse.status).toBe(200);

      // Step 2: Admin approves
      mockGetServerSession.mockResolvedValue(mockAdminSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockAdminUser);
      mockPrisma.userTreatyType.findUnique.mockResolvedValue(mockApplication);
      mockPrisma.userTreatyType.update.mockResolvedValue({
        ...mockApplication,
        status: ApplicationStatus.APPROVED,
        paymentStatus: PaymentStatus.AWAITING_PAYMENT,
        approvedAt: new Date(),
        approvedBy: 'admin-123',
      });

      const approveRequest = createMockRequest('/api/admin/treaty-applications/app-123/status', {
        method: 'PUT',
        body: {
          status: ApplicationStatus.APPROVED,
          reviewComments: 'Approved for payment',
        },
      });

      const approveResponse = await PUT(approveRequest, { params: createMockParams({ id: 'app-123' }) });
      expect(approveResponse.status).toBe(200);

      // Step 3: User submits payment
      mockGetServerSession.mockResolvedValue(mockUserSession);
      mockPrisma.userTreatyType.findUnique.mockResolvedValue({
        ...mockApplication,
        status: ApplicationStatus.APPROVED,
        paymentStatus: PaymentStatus.AWAITING_PAYMENT,
      });
      mockPrisma.payment.create.mockResolvedValue(mockPayment);

      const paymentRequest = createMockRequest('/api/treaty-payments', {
        method: 'POST',
        body: {
          applicationId: 'app-123',
          amount: 100,
          paymentMethod: 'CASH',
          payerName: 'John Doe',
          payerContact: '<EMAIL>',
        },
      });

      const paymentResponse = await SUBMIT_PAYMENT(paymentRequest);
      expect(paymentResponse.status).toBe(200);

      // Step 4: Admin verifies payment
      mockGetServerSession.mockResolvedValue(mockAdminSession);
      mockPrisma.payment.findUnique.mockResolvedValue(mockPayment);
      mockPrisma.payment.update.mockResolvedValue({
        ...mockPayment,
        status: PaymentStatus.PAID,
        processedBy: 'admin-123',
      });

      const verifyRequest = createMockRequest('/api/admin/treaty-payments/payment-123/verify', {
        method: 'PUT',
        body: {
          action: 'verify',
          verificationNotes: 'Payment verified',
        },
      });

      const verifyResponse = await VERIFY_PAYMENT(verifyRequest, { params: createMockParams({ id: 'payment-123' }) });
      expect(verifyResponse.status).toBe(200);

      // Verify all audit logs were created
      expect(mockPrisma.auditLog.create).toHaveBeenCalledTimes(5); // Create + Approve + Payment + Verify + Activate
    });

    test('should handle free treaty workflow (no payment required)', async () => {
      // Create a free treaty type
      const freeTreatyType = {
        ...mockTreatyType,
        price: 0,
        requiresPayment: false,
      };

      // Step 1: Create application
      mockGetServerSession.mockResolvedValue(mockUserSession);
      mockPrisma.treatyType.findUnique.mockResolvedValue(freeTreatyType);
      mockPrisma.userTreatyType.create.mockResolvedValue(mockApplication);

      const createRequest = createMockRequest('/api/treaty-applications', {
        method: 'POST',
        body: {
          treatyTypeId: 'treaty-type-1',
          submissionReason: 'Free treaty test',
        },
      });

      const createResponse = await POST(createRequest);
      expect(createResponse.status).toBe(200);

      // Step 2: Admin approves (should auto-activate)
      mockGetServerSession.mockResolvedValue(mockAdminSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockAdminUser);
      mockPrisma.userTreatyType.findUnique.mockResolvedValue(mockApplication);
      mockPrisma.userTreatyType.update.mockResolvedValue({
        ...mockApplication,
        status: ApplicationStatus.ACTIVE, // Auto-activated
        paymentStatus: PaymentStatus.NOT_REQUIRED,
        approvedAt: new Date(),
        approvedBy: 'admin-123',
      });

      const approveRequest = createMockRequest('/api/admin/treaty-applications/app-123/status', {
        method: 'PUT',
        body: {
          status: ApplicationStatus.APPROVED,
          reviewComments: 'Approved - free treaty',
        },
      });

      const approveResponse = await PUT(approveRequest, { params: createMockParams({ id: 'app-123' }) });
      expect(approveResponse.status).toBe(200);

      // Verify treaty was auto-activated
      const data = await approveResponse.json();
      expect(data.application.status).toBe(ApplicationStatus.ACTIVE);
      expect(data.application.paymentStatus).toBe(PaymentStatus.NOT_REQUIRED);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle non-existent application', async () => {
      const request = createMockRequest('/api/admin/treaty-applications/non-existent/status', {
        method: 'PUT',
        body: {
          status: ApplicationStatus.UNDER_REVIEW,
        },
      });

      mockGetServerSession.mockResolvedValue(mockAdminSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockAdminUser);
      mockPrisma.userTreatyType.findUnique.mockResolvedValue(null);

      const response = await PUT(request, { params: createMockParams({ id: 'non-existent' }) });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('Application not found');
    });

    test('should handle unauthorized access', async () => {
      const request = createMockRequest('/api/admin/treaty-applications/app-123/status', {
        method: 'PUT',
        body: {
          status: ApplicationStatus.UNDER_REVIEW,
        },
      });

      mockGetServerSession.mockResolvedValue(null);

      const response = await PUT(request, { params: createMockParams({ id: 'app-123' }) });
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    test('should handle forbidden access (non-admin)', async () => {
      const request = createMockRequest('/api/admin/treaty-applications/app-123/status', {
        method: 'PUT',
        body: {
          status: ApplicationStatus.UNDER_REVIEW,
        },
      });

      mockGetServerSession.mockResolvedValue(mockUserSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockUser); // Non-admin user

      const response = await PUT(request, { params: createMockParams({ id: 'app-123' }) });
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Forbidden');
    });

    test('should handle invalid status values', async () => {
      const request = createMockRequest('/api/admin/treaty-applications/app-123/status', {
        method: 'PUT',
        body: {
          status: 'INVALID_STATUS', // Invalid status
        },
      });

      mockGetServerSession.mockResolvedValue(mockAdminSession);
      mockPrisma.user.findUnique.mockResolvedValue(mockAdminUser);

      const response = await PUT(request, { params: createMockParams({ id: 'app-123' }) });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Valid status is required');
    });
  });
});