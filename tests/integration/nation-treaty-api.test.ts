import { NextRequest } from 'next/server';
import { createMocks } from 'node-mocks-http';
import { GET, POST, PUT, DELETE } from '@/app/api/nation-treaties/route';
import { GET as GETEnvoys, POST as POSTEnvoys, DELETE as DELETEEnvoys } from '@/app/api/nation-treaties/[id]/envoys/route';
import { GET as GETOffices, POST as POSTOffices, PUT as PUTOffices, DELETE as DELETEOffices } from '@/app/api/nation-treaties/[id]/offices/route';
import { prisma } from '@/lib/prisma';

// Mock NextResponse
jest.mock('next/server', () => ({
  NextResponse: {
    json: jest.fn().mockImplementation((data, options) => ({
      json: () => data,
      status: options?.status || 200,
      ok: (options?.status || 200) < 400,
    })),
  },
}));

// Mock getServerSession
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));

// Mock authOptions
jest.mock('@/lib/auth', () => ({
  authOptions: {},
}));

// Mock prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    nationTreaty: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      findUnique: jest.fn(),
      delete: jest.fn(),
    },
    nationTreatyEnvoy: {
      create: jest.fn(),
      findMany: jest.fn(),
      delete: jest.fn(),
    },
    nationTreatyOffice: {
      create: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

// Mock logAudit
jest.mock('@/lib/audit', () => ({
  logAudit: jest.fn(),
}));

describe('Nation Treaty API Integration Tests', () => {
  const mockSession = {
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'ADMIN',
    },
    expires: '2024-12-31',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    const { getServerSession } = require('next-auth/next');
    getServerSession.mockResolvedValue(mockSession);
  });

  describe('GET /api/nation-treaties', () => {
    it('should return nation treaties with pagination', async () => {
      const mockTreaties = [
        {
          id: '1',
          name: 'Test Nation Treaty',
          officialName: 'Official Test Nation Treaty',
          status: 'ACTIVE',
          description: 'Test description',
          contactEmail: '<EMAIL>',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      prisma.nationTreaty.findMany.mockResolvedValue(mockTreaties);
      prisma.nationTreaty.count.mockResolvedValue(1);

      const request = new NextRequest('http://localhost:3000/api/nation-treaties?page=1&limit=10');
      const response = await GET(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockTreaties);
    });
  });

  describe('POST /api/nation-treaties', () => {
    it('should create a new nation treaty', async () => {
      const newTreaty = {
        name: 'New Nation Treaty',
        officialName: 'Official New Nation Treaty',
        status: 'ACTIVE',
        description: 'New treaty description',
        contactEmail: '<EMAIL>',
        contactPhone: '+1234567890',
        emergencyContactName: 'Emergency Contact',
        emergencyContactPhone: '+0987654321',
      };

      const createdTreaty = {
        id: 'new-treaty-id',
        ...newTreaty,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prisma.nationTreaty.create.mockResolvedValue(createdTreaty);

      const request = new NextRequest('http://localhost:3000/api/nation-treaties', {
        method: 'POST',
        body: JSON.stringify(newTreaty),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);

      expect(response.status).toBe(201);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data).toEqual(createdTreaty);
    });

    it('should return validation error for invalid data', async () => {
      const invalidTreaty = {
        name: '', // Invalid: name is required
        officialName: 'Official Name',
      };

      const request = new NextRequest('http://localhost:3000/api/nation-treaties', {
        method: 'POST',
        body: JSON.stringify(invalidTreaty),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('Validation error');
    });
  });

  describe('Nation Treaty Envoys API', () => {
    it('should add envoy to nation treaty', async () => {
      const mockUser = {
        id: 'user-id',
        email: '<EMAIL>',
        name: 'Test User',
      };

      const mockEnvoy = {
        id: 'envoy-id',
        userId: 'user-id',
        nationTreatyId: 'treaty-id',
        envoyType: 'PRIMARY',
        title: 'Ambassador',
        status: 'ACTIVE',
        appointedAt: new Date(),
        user: mockUser,
      };

      prisma.user.findUnique.mockResolvedValue(mockUser);
      prisma.nationTreatyEnvoy.create.mockResolvedValue(mockEnvoy);

      const request = new NextRequest('http://localhost:3000/api/nation-treaties/treaty-id/envoys', {
        method: 'POST',
        body: JSON.stringify({
          userId: 'user-id',
          envoyType: 'PRIMARY',
          title: 'Ambassador',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POSTEnvoys(request, { params: Promise.resolve({ id: 'treaty-id' }) });

      expect(response.status).toBe(201);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockEnvoy);
    });

    it('should get nation treaty envoys', async () => {
      const mockEnvoys = [
        {
          id: 'envoy-id',
          userId: 'user-id',
          nationTreatyId: 'treaty-id',
          envoyType: 'PRIMARY',
          title: 'Ambassador',
          status: 'ACTIVE',
          appointedAt: new Date(),
          user: {
            id: 'user-id',
            name: 'Test User',
            email: '<EMAIL>',
          },
        },
      ];

      prisma.nationTreatyEnvoy.findMany.mockResolvedValue(mockEnvoys);

      const request = new NextRequest('http://localhost:3000/api/nation-treaties/treaty-id/envoys');
      const response = await GETEnvoys(request, { params: Promise.resolve({ id: 'treaty-id' }) });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockEnvoys);
    });
  });

  describe('Nation Treaty Offices API', () => {
    it('should create new office', async () => {
      const newOffice = {
        nationTreatyId: 'treaty-id',
        officeType: 'EMBASSY',
        status: 'PLANNED',
        streetAddress: '123 Main St',
        city: 'Test City',
        country: 'Test Country',
        phone: '+1234567890',
        email: '<EMAIL>',
        website: 'https://example.com',
      };

      const createdOffice = {
        id: 'office-id',
        ...newOffice,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prisma.nationTreatyOffice.create.mockResolvedValue(createdOffice);

      const request = new NextRequest('http://localhost:3000/api/nation-treaties/treaty-id/offices', {
        method: 'POST',
        body: JSON.stringify(newOffice),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POSTOffices(request, { params: Promise.resolve({ id: 'treaty-id' }) });

      expect(response.status).toBe(201);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data).toEqual(createdOffice);
    });

    it('should get nation treaty offices', async () => {
      const mockOffices = [
        {
          id: 'office-id',
          nationTreatyId: 'treaty-id',
          officeType: 'EMBASSY',
          status: 'ACTIVE',
          streetAddress: '123 Main St',
          city: 'Test City',
          country: 'Test Country',
          phone: '+1234567890',
          email: '<EMAIL>',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      prisma.nationTreatyOffice.findMany.mockResolvedValue(mockOffices);

      const request = new NextRequest('http://localhost:3000/api/nation-treaties/treaty-id/offices');
      const response = await GETOffices(request, { params: Promise.resolve({ id: 'treaty-id' }) });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockOffices);
    });

    it('should update office', async () => {
      const updatedOffice = {
        id: 'office-id',
        nationTreatyId: 'treaty-id',
        officeType: 'EMBASSY',
        status: 'ACTIVE',
        streetAddress: '123 Main St',
        city: 'Test City',
        country: 'Test Country',
        phone: '+1234567890',
        email: '<EMAIL>',
        website: 'https://example.com',
        updatedAt: new Date(),
      };

      prisma.nationTreatyOffice.update.mockResolvedValue(updatedOffice);

      const request = new NextRequest('http://localhost:3000/api/nation-treaties/treaty-id/offices', {
        method: 'PUT',
        body: JSON.stringify({
          id: 'office-id',
          status: 'ACTIVE',
          phone: '+1234567890',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await PUTOffices(request, { params: Promise.resolve({ id: 'treaty-id' }) });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data).toEqual(updatedOffice);
    });

    it('should delete office', async () => {
      const deletedOffice = {
        id: 'office-id',
        nationTreatyId: 'treaty-id',
        officeType: 'EMBASSY',
        status: 'DELETED',
        streetAddress: '123 Main St',
        city: 'Test City',
        country: 'Test Country',
        deletedAt: new Date(),
      };

      prisma.nationTreatyOffice.update.mockResolvedValue(deletedOffice);

      const request = new NextRequest('http://localhost:3000/api/nation-treaties/treaty-id/offices?id=office-id', {
        method: 'DELETE',
      });

      const response = await DELETEOffices(request, { params: Promise.resolve({ id: 'treaty-id' }) });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle unauthorized access', async () => {
      const { getServerSession } = require('next-auth/next');
      getServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/nation-treaties');
      const response = await GET(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Unauthorized');
    });

    it('should handle forbidden access for non-admin users', async () => {
      const { getServerSession } = require('next-auth/next');
      getServerSession.mockResolvedValue({
        user: {
          id: 'test-user-id',
          role: 'USER', // Non-admin role
        },
      });

      const request = new NextRequest('http://localhost:3000/api/nation-treaties', {
        method: 'POST',
        body: JSON.stringify({
          name: 'Test Treaty',
          officialName: 'Official Test Treaty',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);

      expect(response.status).toBe(403);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Forbidden');
    });

    it('should handle database errors gracefully', async () => {
      prisma.nationTreaty.findMany.mockRejectedValue(new Error('Database connection failed'));

      const request = new NextRequest('http://localhost:3000/api/nation-treaties');
      const response = await GET(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to fetch nation treaties');
    });
  });
});