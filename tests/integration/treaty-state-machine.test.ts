import { 
  ApplicationStateMachine, 
  PaymentStateMachine, 
  WorkflowCoordinator, 
  TransitionContext 
} from '../../src/lib/workflow/StateMachine';
import { ApplicationStatus, PaymentStatus } from '@prisma/client';

describe('Treaty Application State Machine Tests', () => {
  describe('Application State Machine', () => {
    test('should validate valid transitions from APPLIED', () => {
      const result = ApplicationStateMachine.validateApplicationTransition(
        ApplicationStatus.APPLIED,
        ApplicationStatus.UNDER_REVIEW
      );
      
      expect(result.valid).toBe(true);
    });

    test('should validate valid transitions from UNDER_REVIEW', () => {
      const result = ApplicationStateMachine.validateApplicationTransition(
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.APPROVED
      );
      
      expect(result.valid).toBe(true);
    });

    test('should validate valid transitions from APPROVED', () => {
      const result = ApplicationStateMachine.validateApplicationTransition(
        ApplicationStatus.APPROVED,
        ApplicationStatus.ACTIVE
      );
      
      expect(result.valid).toBe(true);
    });

    test('should validate valid transitions from REJECTED', () => {
      const result = ApplicationStateMachine.validateApplicationTransition(
        ApplicationStatus.REJECTED,
        ApplicationStatus.APPLIED
      );
      
      expect(result.valid).toBe(true);
    });

    test('should reject invalid transitions', () => {
      const result = ApplicationStateMachine.validateApplicationTransition(
        ApplicationStatus.APPLIED,
        ApplicationStatus.ACTIVE
      );
      
      expect(result.valid).toBe(false);
      expect(result.reason).toContain('Cannot transition from APPLIED to ACTIVE');
    });

    test('should reject transitions from ACTIVE state', () => {
      const result = ApplicationStateMachine.validateApplicationTransition(
        ApplicationStatus.ACTIVE,
        ApplicationStatus.UNDER_REVIEW
      );
      
      expect(result.valid).toBe(false);
      expect(result.reason).toBe('Cannot transition from ACTIVE to UNDER_REVIEW. Valid transitions: ');
    });

    test('should get valid transitions for each state', () => {
      const appliedTransitions = ApplicationStateMachine.getValidTransitions(ApplicationStatus.APPLIED);
      expect(appliedTransitions).toContain(ApplicationStatus.UNDER_REVIEW);
      expect(appliedTransitions).toContain(ApplicationStatus.REJECTED);

      const underReviewTransitions = ApplicationStateMachine.getValidTransitions(ApplicationStatus.UNDER_REVIEW);
      expect(underReviewTransitions).toContain(ApplicationStatus.APPROVED);
      expect(underReviewTransitions).toContain(ApplicationStatus.REJECTED);

      const approvedTransitions = ApplicationStateMachine.getValidTransitions(ApplicationStatus.APPROVED);
      expect(approvedTransitions).toContain(ApplicationStatus.ACTIVE);

      const activeTransitions = ApplicationStateMachine.getValidTransitions(ApplicationStatus.ACTIVE);
      expect(activeTransitions).toEqual([]);
    });

    test('should handle user-initiated transitions', () => {
      const cancelResult = ApplicationStateMachine.validateApplicationTransition(
        ApplicationStatus.APPLIED,
        ApplicationStatus.REJECTED,
        true,
        'CANCEL'
      );
      
      expect(cancelResult.valid).toBe(true);

      const invalidCancelResult = ApplicationStateMachine.validateApplicationTransition(
        ApplicationStatus.ACTIVE,
        ApplicationStatus.REJECTED,
        true,
        'CANCEL'
      );
      
      expect(invalidCancelResult.valid).toBe(false);
    });

    test('should execute transition with payment requirement', async () => {
      const result = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.APPROVED,
        {} as TransitionContext,
        true // requiresPayment
      );

      expect(result.success).toBe(true);
      expect(result.requiresPayment).toBe(true);
      expect(result.newStatus).toBe(ApplicationStatus.APPROVED);
    });

    test('should execute transition without payment requirement', async () => {
      const result = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.APPROVED,
        {} as TransitionContext,
        false // requiresPayment
      );

      expect(result.success).toBe(true);
      expect(result.autoActivate).toBe(true);
      expect(result.newStatus).toBe(ApplicationStatus.APPROVED);
    });

    test('should reject transition without rejection reason', async () => {
      const result = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.REJECTED,
        {} as TransitionContext, // No notes
        false
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('Rejection requires a reason to be provided');
    });

    test('should accept transition with rejection reason', async () => {
      const context: TransitionContext = {
        notes: 'Insufficient documentation',
        userId: 'user-123',
      };

      const result = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.REJECTED,
        context,
        false
      );

      expect(result.success).toBe(true);
      expect(result.newStatus).toBe(ApplicationStatus.REJECTED);
    });

    test('should get next expected status', () => {
      const nextFromApplied = ApplicationStateMachine.getNextExpectedStatus(ApplicationStatus.APPLIED);
      expect(nextFromApplied).toContain(ApplicationStatus.UNDER_REVIEW);
      expect(nextFromApplied).toContain(ApplicationStatus.REJECTED);

      const nextFromUnderReview = ApplicationStateMachine.getNextExpectedStatus(ApplicationStatus.UNDER_REVIEW);
      expect(nextFromUnderReview).toContain(ApplicationStatus.APPROVED);
      expect(nextFromUnderReview).toContain(ApplicationStatus.REJECTED);

      const nextFromApproved = ApplicationStateMachine.getNextExpectedStatus(ApplicationStatus.APPROVED);
      expect(nextFromApproved).toContain(ApplicationStatus.ACTIVE);

      const nextFromActive = ApplicationStateMachine.getNextExpectedStatus(ApplicationStatus.ACTIVE);
      expect(nextFromActive).toEqual([]);
    });

    test('should identify terminal states', () => {
      expect(ApplicationStateMachine.isTerminalState(ApplicationStatus.ACTIVE)).toBe(true);
      expect(ApplicationStateMachine.isTerminalState(ApplicationStatus.APPLIED)).toBe(false);
      expect(ApplicationStateMachine.isTerminalState(ApplicationStatus.UNDER_REVIEW)).toBe(false);
      expect(ApplicationStateMachine.isTerminalState(ApplicationStatus.APPROVED)).toBe(false);
      expect(ApplicationStateMachine.isTerminalState(ApplicationStatus.REJECTED)).toBe(false);
    });

    test('should provide status descriptions', () => {
      expect(ApplicationStateMachine.getStatusDescription(ApplicationStatus.APPLIED))
        .toBe('Application submitted and awaiting review');
      expect(ApplicationStateMachine.getStatusDescription(ApplicationStatus.UNDER_REVIEW))
        .toBe('Application is currently under review');
      expect(ApplicationStateMachine.getStatusDescription(ApplicationStatus.APPROVED))
        .toBe('Application approved - payment may be required');
      expect(ApplicationStateMachine.getStatusDescription(ApplicationStatus.REJECTED))
        .toBe('Application was rejected');
      expect(ApplicationStateMachine.getStatusDescription(ApplicationStatus.ACTIVE))
        .toBe('Treaty is active and in good standing');
    });
  });

  describe('Payment State Machine', () => {
    test('should validate valid payment transitions', () => {
      const result = PaymentStateMachine.validatePaymentTransition(
        PaymentStatus.AWAITING_PAYMENT,
        PaymentStatus.PENDING
      );
      
      expect(result.valid).toBe(true);
    });

    test('should validate pending to paid transition', () => {
      const result = PaymentStateMachine.validatePaymentTransition(
        PaymentStatus.PENDING,
        PaymentStatus.PAID
      );
      
      expect(result.valid).toBe(true);
    });

    test('should validate pending to failed transition', () => {
      const result = PaymentStateMachine.validatePaymentTransition(
        PaymentStatus.PENDING,
        PaymentStatus.FAILED
      );
      
      expect(result.valid).toBe(true);
    });

    test('should validate failed to pending transition (retry)', () => {
      const result = PaymentStateMachine.validatePaymentTransition(
        PaymentStatus.FAILED,
        PaymentStatus.PENDING
      );
      
      expect(result.valid).toBe(true);
    });

    test('should validate paid to refunded transition', () => {
      const result = PaymentStateMachine.validatePaymentTransition(
        PaymentStatus.PAID,
        PaymentStatus.REFUNDED
      );
      
      expect(result.valid).toBe(true);
    });

    test('should reject invalid payment transitions', () => {
      const result = PaymentStateMachine.validatePaymentTransition(
        PaymentStatus.AWAITING_PAYMENT,
        PaymentStatus.PAID
      );
      
      expect(result.valid).toBe(false);
      expect(result.reason).toContain('Cannot transition from AWAITING_PAYMENT to PAID');
    });

    test('should reject transitions from terminal states', () => {
      const result1 = PaymentStateMachine.validatePaymentTransition(
        PaymentStatus.NOT_REQUIRED,
        PaymentStatus.AWAITING_PAYMENT
      );
      
      expect(result1.valid).toBe(false);

      const result2 = PaymentStateMachine.validatePaymentTransition(
        PaymentStatus.REFUNDED,
        PaymentStatus.PENDING
      );
      
      expect(result2.valid).toBe(false);
    });

    test('should execute payment verification', async () => {
      const result = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        PaymentStatus.PENDING,
        PaymentStatus.PAID,
        {
          adminId: 'admin-123',
          notes: 'Payment verified',
        }
      );

      expect(result.success).toBe(true);
      expect(result.newStatus).toBe(PaymentStatus.PAID);
    });

    test('should execute payment rejection', async () => {
      const result = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        PaymentStatus.PENDING,
        PaymentStatus.FAILED,
        {
          adminId: 'admin-123',
          notes: 'Invalid payment',
        }
      );

      expect(result.success).toBe(true);
      expect(result.newStatus).toBe(PaymentStatus.FAILED);
    });

    test('should reject payment verification without admin', async () => {
      const result = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        PaymentStatus.PENDING,
        PaymentStatus.PAID,
        {
          adminId: '', // Empty admin ID
        }
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('Payment verification requires admin authorization');
    });

    test('should reject payment failure without notes', async () => {
      const result = await PaymentStateMachine.executePaymentTransition(
        'payment-123',
        PaymentStatus.PENDING,
        PaymentStatus.FAILED,
        {
          adminId: 'admin-123',
          // No notes
        }
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('Payment failure requires explanation');
    });

    test('should provide payment status descriptions', () => {
      expect(PaymentStateMachine.getPaymentStatusDescription(PaymentStatus.NOT_REQUIRED))
        .toBe('No payment required');
      expect(PaymentStateMachine.getPaymentStatusDescription(PaymentStatus.AWAITING_PAYMENT))
        .toBe('Payment is required to activate treaty');
      expect(PaymentStateMachine.getPaymentStatusDescription(PaymentStatus.PENDING))
        .toBe('Payment submitted and awaiting verification');
      expect(PaymentStateMachine.getPaymentStatusDescription(PaymentStatus.PAID))
        .toBe('Payment verified and complete');
      expect(PaymentStateMachine.getPaymentStatusDescription(PaymentStatus.FAILED))
        .toBe('Payment verification failed');
      expect(PaymentStateMachine.getPaymentStatusDescription(PaymentStatus.REFUNDED))
        .toBe('Payment has been refunded');
    });
  });

  describe('Workflow Coordinator', () => {
    test('should handle paid treaty workflow', async () => {
      const result = await WorkflowCoordinator.handleApplicationWorkflow(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        PaymentStatus.NOT_REQUIRED,
        ApplicationStatus.APPROVED,
        {} as TransitionContext,
        true // requiresPayment
      );

      expect(result.applicationResult.success).toBe(true);
      expect(result.applicationResult.requiresPayment).toBe(true);
      expect(result.paymentResult).toBeDefined();
      expect(result.paymentResult?.newStatus).toBe(PaymentStatus.AWAITING_PAYMENT);
      expect(result.activationRequired).toBe(false);
    });

    test('should handle free treaty workflow', async () => {
      const result = await WorkflowCoordinator.handleApplicationWorkflow(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        PaymentStatus.NOT_REQUIRED,
        ApplicationStatus.APPROVED,
        {} as TransitionContext,
        false // requiresPayment
      );

      expect(result.applicationResult.success).toBe(true);
      expect(result.applicationResult.autoActivate).toBe(true);
      expect(result.paymentResult).toBeUndefined();
      expect(result.activationRequired).toBe(true);
    });

    test('should handle manual activation after payment', async () => {
      const result = await WorkflowCoordinator.handleApplicationWorkflow(
        'app-123',
        ApplicationStatus.APPROVED,
        PaymentStatus.PAID,
        ApplicationStatus.ACTIVE,
        {} as TransitionContext,
        true
      );

      expect(result.applicationResult.success).toBe(true);
      expect(result.paymentResult).toBeUndefined();
      expect(result.activationRequired).toBe(true);
    });

    test('should handle failed transitions', async () => {
      const result = await WorkflowCoordinator.handleApplicationWorkflow(
        'app-123',
        ApplicationStatus.APPLIED,
        PaymentStatus.NOT_REQUIRED,
        ApplicationStatus.ACTIVE, // Invalid transition
        {} as TransitionContext,
        false
      );

      expect(result.applicationResult.success).toBe(false);
      expect(result.activationRequired).toBe(false);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle empty transition context', async () => {
      const result = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.APPROVED,
        {} as TransitionContext,
        false
      );

      expect(result.success).toBe(true);
    });

    test('should handle transition context with partial data', async () => {
      const context: TransitionContext = {
        userId: 'user-123',
        // Missing other optional fields
      };

      const result = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.APPROVED,
        context,
        false
      );

      expect(result.success).toBe(true);
    });

    test('should handle payment data in transition context', async () => {
      const context: TransitionContext = {
        userId: 'user-123',
        paymentData: {
          amount: 100,
          method: 'CASH',
          transactionId: 'TX123',
        },
      };

      const result = await ApplicationStateMachine.executeTransition(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        ApplicationStatus.APPROVED,
        context,
        true
      );

      expect(result.success).toBe(true);
      expect(result.requiresPayment).toBe(true);
    });

    test('should handle complex workflow scenarios', async () => {
      // Test complete workflow from application to activation
      const context: TransitionContext = {
        userId: 'admin-123',
        notes: 'Approved for payment',
      };

      // Step 1: Application approval with payment requirement
      const approvalResult = await WorkflowCoordinator.handleApplicationWorkflow(
        'app-123',
        ApplicationStatus.UNDER_REVIEW,
        PaymentStatus.NOT_REQUIRED,
        ApplicationStatus.APPROVED,
        context,
        true
      );

      expect(approvalResult.applicationResult.success).toBe(true);
      expect(approvalResult.applicationResult.requiresPayment).toBe(true);

      // Step 2: Payment verification leading to activation
      const activationResult = await WorkflowCoordinator.handleApplicationWorkflow(
        'app-123',
        ApplicationStatus.APPROVED,
        PaymentStatus.PAID,
        ApplicationStatus.ACTIVE,
        {
          userId: 'admin-123',
          notes: 'Payment verified, activating treaty',
        },
        true
      );

      expect(activationResult.applicationResult.success).toBe(true);
      expect(activationResult.activationRequired).toBe(true);
    });
  });
});