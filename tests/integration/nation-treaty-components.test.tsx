import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { NationTreatyManagement } from '@/components/treaties/NationTreatyManagement';
import { NationTreatyOfficeModal } from '@/components/treaties/modals/NationTreatyOfficeModal';
import { UserNationTribeForm } from '@/components/users/manage/shared/UserNationTribeForm';

// Mock fetch globally
global.fetch = jest.fn();

// Mock react-hook-form
jest.mock('react-hook-form', () => ({
  useForm: () => ({
    register: jest.fn(),
    handleSubmit: jest.fn(),
    watch: jest.fn(),
    setValue: jest.fn(),
    formState: { errors: {} },
    reset: jest.fn(),
  }),
  Controller: ({ render }) => render({ field: { value: '', onChange: jest.fn() } }),
}));

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  Search: () => <div data-testid="search-icon">Search</div>,
  Plus: () => <div data-testid="plus-icon">Plus</div>,
  Edit: () => <div data-testid="edit-icon">Edit</div>,
  Trash2: () => <div data-testid="trash-icon">Trash</div>,
  X: () => <div data-testid="x-icon">X</div>,
  Building: () => <div data-testid="building-icon">Building</div>,
  Mail: () => <div data-testid="mail-icon">Mail</div>,
  Phone: () => <div data-testid="phone-icon">Phone</div>,
  Globe: () => <div data-testid="globe-icon">Globe</div>,
  MapPin: () => <div data-testid="map-pin-icon">MapPin</div>,
  Settings: () => <div data-testid="settings-icon">Settings</div>,
}));

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock @hookform/resolvers
jest.mock('@hookform/resolvers', () => ({
  zodResolver: () => (data) => data,
}));

describe('Nation Treaty Components Integration Tests', () => {
  const mockNationTreaties = [
    {
      id: '1',
      name: 'Test Nation Treaty',
      officialName: 'Official Test Nation Treaty',
      status: 'ACTIVE',
      description: 'Test description',
      contactEmail: '<EMAIL>',
      contactPhone: '+1234567890',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    {
      id: '2',
      name: 'Second Nation Treaty',
      officialName: 'Official Second Nation Treaty',
      status: 'PENDING',
      description: 'Second treaty description',
      contactEmail: '<EMAIL>',
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-02T00:00:00Z',
    },
  ];

  const mockEnvoys = [
    {
      id: 'envoy-1',
      userId: 'user-1',
      nationTreatyId: '1',
      envoyType: 'PRIMARY',
      title: 'Ambassador',
      status: 'ACTIVE',
      appointedAt: '2024-01-01T00:00:00Z',
      user: {
        id: 'user-1',
        name: 'John Doe',
        email: '<EMAIL>',
      },
    },
  ];

  const mockOffices = [
    {
      id: 'office-1',
      nationTreatyId: '1',
      officeType: 'EMBASSY',
      status: 'ACTIVE',
      streetAddress: '123 Main St',
      city: 'Test City',
      country: 'Test Country',
      phone: '+1234567890',
      email: '<EMAIL>',
      website: 'https://embassy.example.com',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ];

  const mockUsers = [
    {
      id: 'user-1',
      name: 'John Doe',
      email: '<EMAIL>',
      nwaEmail: '<EMAIL>',
    },
    {
      id: 'user-2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      nwaEmail: '<EMAIL>',
    },
  ];

  const mockNations = [
    {
      id: 'nation-1',
      name: 'Test Nation',
      officialName: 'Official Test Nation',
      status: 'ACTIVE',
    },
    {
      id: 'nation-2',
      name: 'Second Nation',
      officialName: 'Official Second Nation',
      status: 'ACTIVE',
    },
  ];

  const mockTribes = [
    {
      id: 'tribe-1',
      name: 'Test Tribe',
      officialName: 'Official Test Tribe',
      status: 'ACTIVE',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockClear();
  });

  describe('NationTreatyManagement', () => {
    it('should render nation treaty management interface', async () => {
      // Mock fetch responses
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: mockNationTreaties,
            pagination: { total: 2, page: 1, limit: 10 },
          }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockUsers }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockNations }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockTribes }),
        });

      render(<NationTreatyManagement />);

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('Nation Treaty Management')).toBeInTheDocument();
      });

      // Verify search functionality
      expect(screen.getByPlaceholderText('Search nation treaties...')).toBeInTheDocument();
      
      // Verify create button
      expect(screen.getByText('Create Nation Treaty')).toBeInTheDocument();
    });

    it('should display nation treaties in a table', async () => {
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: mockNationTreaties,
            pagination: { total: 2, page: 1, limit: 10 },
          }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockUsers }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockNations }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockTribes }),
        });

      render(<NationTreatyManagement />);

      await waitFor(() => {
        expect(screen.getByText('Test Nation Treaty')).toBeInTheDocument();
        expect(screen.getByText('Official Test Nation Treaty')).toBeInTheDocument();
        expect(screen.getByText('Second Nation Treaty')).toBeInTheDocument();
      });

      // Verify status badges
      expect(screen.getByText('ACTIVE')).toBeInTheDocument();
      expect(screen.getByText('PENDING')).toBeInTheDocument();
    });

    it('should handle search functionality', async () => {
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: mockNationTreaties,
            pagination: { total: 2, page: 1, limit: 10 },
          }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockUsers }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockNations }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockTribes }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: [mockNationTreaties[0]], // Filtered result
            pagination: { total: 1, page: 1, limit: 10 },
          }),
        });

      render(<NationTreatyManagement />);

      await waitFor(() => {
        const searchInput = screen.getByPlaceholderText('Search nation treaties...');
        fireEvent.change(searchInput, { target: { value: 'Test' } });
        fireEvent.keyDown(searchInput, { key: 'Enter' });
      });

      // Verify search was triggered
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('search=Test'),
        expect.any(Object)
      );
    });
  });

  describe('NationTreatyOfficeModal', () => {
    it('should render office creation modal', () => {
      const mockOnClose = jest.fn();
      const mockOnSubmit = jest.fn();

      render(
        <NationTreatyOfficeModal
          isOpen={true}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
          treatyId="treaty-1"
        />
      );

      expect(screen.getByText('Create New Office')).toBeInTheDocument();
      expect(screen.getByLabelText('Office Type')).toBeInTheDocument();
      expect(screen.getByLabelText('Status')).toBeInTheDocument();
      expect(screen.getByLabelText('Street Address')).toBeInTheDocument();
      expect(screen.getByLabelText('City')).toBeInTheDocument();
      expect(screen.getByLabelText('Country')).toBeInTheDocument();
      expect(screen.getByText('Create Office')).toBeInTheDocument();
    });

    it('should handle form submission', async () => {
      const mockOnClose = jest.fn();
      const mockOnSubmit = jest.fn();

      render(
        <NationTreatyOfficeModal
          isOpen={true}
          onClose={mockOnClose}
          onSubmit={mockOnSubmit}
          treatyId="treaty-1"
        />
      );

      // Fill form fields
      fireEvent.change(screen.getByLabelText('Street Address'), {
        target: { value: '123 Main St' },
      });
      fireEvent.change(screen.getByLabelText('City'), {
        target: { value: 'Test City' },
      });
      fireEvent.change(screen.getByLabelText('Country'), {
        target: { value: 'Test Country' },
      });

      // Submit form
      fireEvent.click(screen.getByText('Create Office'));

      // Verify form submission
      expect(mockOnSubmit).toHaveBeenCalledWith({
        nationTreatyId: 'treaty-1',
        officeType: 'ENVOY_OFFICE',
        status: 'PLANNED',
        streetAddress: '123 Main St',
        city: 'Test City',
        country: 'Test Country',
        phone: '',
        email: '',
        website: '',
        isPrimary: false,
        notes: '',
      });
    });
  });

  describe('UserNationTribeForm', () => {
    it('should render nation/tribe assignment form', () => {
      const mockOnChange = jest.fn();
      const mockData = {
        nationId: '',
        tribeId: '',
        nationRole: null,
        tribeRole: null,
      };

      render(
        <UserNationTribeForm
          data={mockData}
          onChange={mockOnChange}
          nations={mockNations}
          tribes={mockTribes}
        />
      );

      expect(screen.getByText('Nation/Tribe Assignment')).toBeInTheDocument();
      expect(screen.getByLabelText('Nation')).toBeInTheDocument();
      expect(screen.getByLabelText('Tribe')).toBeInTheDocument();
      expect(screen.getByText('Nation Role')).toBeInTheDocument();
      expect(screen.getByText('Tribe Role')).toBeInTheDocument();
    });

    it('should handle nation selection', () => {
      const mockOnChange = jest.fn();
      const mockData = {
        nationId: '',
        tribeId: '',
        nationRole: null,
        tribeRole: null,
      };

      render(
        <UserNationTribeForm
          data={mockData}
          onChange={mockOnChange}
          nations={mockNations}
          tribes={mockTribes}
        />
      );

      // Select nation
      fireEvent.change(screen.getByLabelText('Nation'), {
        target: { value: 'nation-1' },
      });

      expect(mockOnChange).toHaveBeenCalledWith({
        ...mockData,
        nationId: 'nation-1',
      });
    });

    it('should handle role selection', () => {
      const mockOnChange = jest.fn();
      const mockData = {
        nationId: 'nation-1',
        tribeId: '',
        nationRole: null,
        tribeRole: null,
      };

      render(
        <UserNationTribeForm
          data={mockData}
          onChange={mockOnChange}
          nations={mockNations}
          tribes={mockTribes}
        />
      );

      // Select nation role
      fireEvent.click(screen.getByText('Select nation role...'));
      fireEvent.click(screen.getByText('Member'));

      expect(mockOnChange).toHaveBeenCalledWith({
        ...mockData,
        nationRole: 'member',
      });
    });

    it('should validate form data', () => {
      const mockOnChange = jest.fn();
      const mockData = {
        nationId: 'nation-1',
        tribeId: 'tribe-1',
        nationRole: 'admin',
        tribeRole: 'member',
      };

      render(
        <UserNationTribeForm
          data={mockData}
          onChange={mockOnChange}
          nations={mockNations}
          tribes={mockTribes}
        />
      );

      // Form should be disabled when both nation and tribe are selected
      const formContainer = screen.getByText('Nation/Tribe Assignment').parentElement;
      expect(formContainer).toHaveClass('opacity-50');
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: false,
          json: () => Promise.resolve({ success: false, error: 'API Error' }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: [] }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: [] }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: [] }),
        });

      render(<NationTreatyManagement />);

      await waitFor(() => {
        expect(screen.getByText('Nation Treaty Management')).toBeInTheDocument();
      });

      // Should display error state
      expect(screen.getByText(/Failed to load nation treaties/)).toBeInTheDocument();
    });

    it('should handle network errors', async () => {
      (fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      render(<NationTreatyManagement />);

      await waitFor(() => {
        expect(screen.getByText('Nation Treaty Management')).toBeInTheDocument();
      });

      // Should display error state
      expect(screen.getByText(/Failed to load nation treaties/)).toBeInTheDocument();
    });
  });
});