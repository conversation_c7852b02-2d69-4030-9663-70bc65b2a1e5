# Database Migration Guide

This guide explains how to apply the new database migrations for the enhanced user identification and audit logging features.

## Overview

The following changes have been made to the database schema:

1. Added `peace_ambassador_number` and `trade_treaty_number` fields to the `user_profiles` table
2. Updated the `user_profiles` table structure to match the Prisma schema
3. Added foreign key constraints for country and city relationships
4. Created indexes for improved query performance

## Prerequisites

Before applying the migrations, ensure:

1. The database is running and accessible
2. You have the necessary permissions to modify the database schema
3. You have a backup of the current database (recommended)

## Applying Migrations

### Method 1: Using the Migration Script (Recommended)

Run the provided migration script:

```bash
./scripts/apply-migrations.sh
```

This script will:
1. Test the database connection
2. Apply all pending Prisma migrations
3. Apply any additional SQL migrations

### Method 2: Manual Migration

If you prefer to apply migrations manually:

1. **Apply Prisma migrations:**
   ```bash
   npx prisma migrate deploy
   ```

2. **Apply additional SQL migrations:**
   ```bash
   psql -h localhost -p 5436 -U nwa_user -d nwa_portal -f prisma/migrations/20250828130000_add_peace_ambassador_and_trade_treaty_numbers/migration.sql
   psql -h localhost -p 5436 -U nwa_user -d nwa_portal -f prisma/migrations/20250828130001_add_indexes_for_identification_fields/migration.sql
   ```

## Verifying the Migration

After applying the migrations, verify that the changes were successful:

1. **Check table structure:**
   ```sql
   \d user_profiles
   ```

2. **Verify new columns exist:**
   ```sql
   SELECT column_name, data_type 
   FROM information_schema.columns 
   WHERE table_name = 'user_profiles' 
   AND column_name IN ('peace_ambassador_number', 'trade_treaty_number');
   ```

3. **Check indexes:**
   ```sql
   SELECT indexname 
   FROM pg_indexes 
   WHERE tablename = 'user_profiles' 
   AND indexname LIKE '%peace_ambassador%' OR indexname LIKE '%trade_treaty%';
   ```

## Rollback Procedure

In case of issues, you can rollback the migrations:

1. **For Prisma migrations:**
   ```bash
   npx prisma migrate resolve --rolled-back "migration_name"
   ```

2. **For SQL migrations:**
   Create reverse migration scripts to drop the new columns and constraints.

## Testing

After applying the migrations:

1. **Restart the application server**
2. **Test user creation with new fields**
3. **Verify audit logs are being generated**
4. **Check that existing functionality still works**

## Common Issues

### Permission Denied

If you encounter permission errors:
- Ensure you're connecting with a user that has DDL permissions
- Check that the database user has the necessary privileges

### Constraint Violations

If foreign key constraints fail:
- Verify that the referenced tables (countries, cities) exist
- Ensure data integrity before applying the migration

### Migration Conflicts

If Prisma reports migration conflicts:
- Use `npx prisma migrate resolve` to resolve conflicts
- Consider resetting the migration history if in development

## Support

For issues with the migration, contact the development team or check the documentation for the specific migration files in:
- `prisma/migrations/20250828130000_add_peace_ambassador_and_trade_treaty_numbers/migration.sql`
- `prisma/migrations/20250828130001_add_indexes_for_identification_fields/migration.sql`