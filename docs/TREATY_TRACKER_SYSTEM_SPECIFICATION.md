# Treaty Tracker System - Implementation Specification

## Overview
This document specifies the comprehensive Treaty Tracker System for managing treaty applications, approvals, payments, and reporting. The system will provide both user profile management and system-wide tracking capabilities.

## System Architecture

### Core Components
1. **User Profile Management** - Treaty application/approval per user
2. **System Settings** - Global configuration and reporting
3. **Payment Processing** - Transaction tracking and management
4. **Approval Workflows** - Multi-stage approval processes
5. **Reporting Dashboard** - Analytics and insights

## Database Schema Enhancements

### New Tables Required

#### 1. `treaty_applications` - Track treaty type applications
```sql
CREATE TABLE treaty_applications (
    id VARCHAR PRIMARY KEY,
    user_id VARCHAR REFERENCES users(id),
    treaty_id VARCHAR REFERENCES treaties(id),
    treaty_type_id VARCHAR REFERENCES treaty_types(id),
    application_status VARCHAR DEFAULT 'APPLIED', -- APPLIED, UNDER_REVIEW, APPROVED, REJECTED
    payment_status VARCHAR DEFAULT 'PENDING', -- PENDING, AWAITING_PAYMENT, PAID, FAILED, REFUNDED
    applied_at TIMESTAMP DEFAULT NOW(),
    reviewed_at TIMESTAMP,
    approved_at TIMESTAMP,
    approved_by VARCHAR REFERENCES users(id),
    rejected_at TIMESTAMP,
    rejected_by VARCHAR REFERENCES users(id),
    rejection_reason TEXT,
    amount DECIMAL(10,2),
    currency VARCHAR DEFAULT 'USD',
    payment_deadline TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 2. `payments` - Track payment transactions
```sql
CREATE TABLE payments (
    id VARCHAR PRIMARY KEY,
    treaty_application_id VARCHAR REFERENCES treaty_applications(id),
    user_id VARCHAR REFERENCES users(id),
    amount DECIMAL(10,2),
    currency VARCHAR DEFAULT 'USD',
    status VARCHAR DEFAULT 'PENDING', -- PENDING, COMPLETED, FAILED, REFUNDED, CANCELLED
    payment_method VARCHAR, -- BANK_TRANSFER, CREDIT_CARD, CASH, OTHER
    transaction_id VARCHAR,
    payment_date TIMESTAMP,
    invoice_id VARCHAR,
    receipt_id VARCHAR,
    processed_by VARCHAR REFERENCES users(id),
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 3. `treaty_approvals` - Track approval history
```sql
CREATE TABLE treaty_approvals (
    id VARCHAR PRIMARY KEY,
    treaty_application_id VARCHAR REFERENCES treaty_applications(id),
    approver_id VARCHAR REFERENCES users(id),
    approval_stage VARCHAR, -- INITIAL, SECONDARY, FINAL
    status VARCHAR, -- APPROVED, REJECTED, PENDING
    comments TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 4. `treaty_settings` - Global system configuration
```sql
CREATE TABLE treaty_settings (
    id VARCHAR PRIMARY KEY,
    setting_key VARCHAR UNIQUE,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Enhanced Existing Tables

#### `treaty_types` enhancements
```sql
ALTER TABLE treaty_types ADD COLUMN requires_payment BOOLEAN DEFAULT TRUE;
ALTER TABLE treaty_types ADD COLUMN currency VARCHAR DEFAULT 'USD';
ALTER TABLE treaty_types ADD COLUMN payment_deadline_days INT DEFAULT 30;
ALTER TABLE treaty_types ADD COLUMN auto_approve BOOLEAN DEFAULT FALSE;
ALTER TABLE treaty_types ADD COLUMN approval_required BOOLEAN DEFAULT TRUE;
ALTER TABLE treaty_types ADD COLUMN max_applications INT DEFAULT NULL; -- NULL = unlimited
```

#### `user_treaty_types` enhancements
```sql
ALTER TABLE user_treaty_types ADD COLUMN application_status VARCHAR DEFAULT 'ACTIVE';
ALTER TABLE user_treaty_types ADD COLUMN payment_status VARCHAR DEFAULT 'PAID';
ALTER TABLE user_treaty_types ADD COLUMN applied_at TIMESTAMP;
ALTER TABLE user_treaty_types ADD COLUMN approved_at TIMESTAMP;
ALTER TABLE user_treaty_types ADD COLUMN approved_by VARCHAR REFERENCES users(id);
```

## User Profile Interface (UpdateUserTab Enhancement)

### Enhanced Treaty Tab Structure

```
┌─────────────────────────────────────────────────────────────────┐
│  TREATY MANAGEMENT - [User Name]                                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  📋 APPLICATIONS MANAGEMENT                                     │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ Treaty Type         | Status     | Applied    | Action      │ │
│  │ Maritime Basic      | PENDING    | 2024-01-15 │ [Review]    │ │
│  │ Church Charter      | APPROVED   | 2024-01-10 │ [View]      │ │
│  │ Business Premium    | REJECTED   | 2024-01-08 │ [Reason]    │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  💳 PAYMENT PROCESSING                                          │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ Treaty Type         | Amount     | Status     | Action      │ │
│  │ Maritime Basic      | $299.00    | PENDING    │ [Process]   │ │
│  │ Church Charter      | $150.00    | PAID       | [Receipt]   │ │
│  │ Business Premium    | $499.00    | FAILED     | [Retry]     │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  ✅ ACTIVE TREATY TYPES                                         │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ Treaty Type         | Start Date | Expiry     | Renew       │ │
│  │ Maritime Basic      | 2024-01-20 | 2025-01-20 │ [Renew]     │ │
│  │ Church Charter      | 2023-12-01 | 2024-12-01 │ [Extend]    │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  📊 APPLICATION HISTORY                                         │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ Date               | Action      | Treaty Type | Status      │ │
│  │ 2024-01-15 14:30   | Applied     | Maritime    | PENDING     │ │
│  │ 2024-01-10 09:15   | Approved    | Church      | ACTIVE      │ │
│  │ 2024-01-08 16:45   | Rejected    | Business    | REJECTED    │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### Admin Actions Available

#### Application Management
- **Review Application** - View application details, approve/reject
- **View Details** - See complete application information
- **Show Rejection Reason** - Display why application was rejected

#### Payment Processing
- **Process Payment** - Mark payment as received, add transaction details
- **View Receipt** - Show payment receipt and transaction details
- **Retry Payment** - Handle failed payment scenarios

#### Treaty Type Management
- **Renew** - Extend treaty type subscription
- **Extend** - Modify expiry dates
- **Deactivate** - Remove treaty type access

## System Settings Interface

### New Menu Item: `/admin/manage/treaty-tracker`

#### Configuration Sections

```
┌─────────────────────────────────────────────────────────────────┐
│  TREATY TRACKER SYSTEM SETTINGS                                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ⚙️ GENERAL SETTINGS                                            │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ Default Currency:       [USD ▼]                              │ │
│  │ Payment Deadline Days:  [30] days                           │ │
│  │ Auto-Approve:           [ ] Enable                          │ │
│  │ Require Approval:       [✓] Enable                          │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  💳 PAYMENT SETTINGS                                            │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ Accepted Methods:     [✓] Bank Transfer                      │ │
│  │                      [✓] Credit Card                         │ │
│  │                      [ ] Cash                                │ │
│  │                      [✓] Other                               │ │
│  │ Payment Gateway:      [Stripe ▼]                            │ │
│  │ Auto-reminder Days:   [3] days before deadline               │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  📧 NOTIFICATION SETTINGS                                       │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ Application Received: [✓] Email User                         │ │
│  │ Application Approved: [✓] Email User                         │ │
│  │ Application Rejected: [✓] Email User                         │
│  │ Payment Reminder:     [✓] Email User                        │ │
│  │ Payment Received:     [✓] Email User                         │ │
│  │ Admin Notifications:  [✓] Email Admins                      │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### Reporting Dashboard

```
┌─────────────────────────────────────────────────────────────────┐
│  TREATY TRACKER REPORTING DASHBOARD                               │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  📈 OVERVIEW STATISTICS                                          │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ Total Applications:    1,247                                 │ │
│  │ Pending Review:        45                                    │ │
│  │ Approved:             892                                   │ │
│  │ Rejected:             310                                   │ │
│  │ Total Revenue:        $456,780                              │ │
│  │ Pending Payments:     $23,450                               │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  📊 APPLICATIONS BY STATUS                                       │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ ████████████████████ Approved (892)                          │ │
│  │ ████ Pending Review (45)                                      │ │
│  │ █████ Rejected (310)                                          │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  💰 REVENUE BY TREATY TYPE                                       │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ Maritime Basic:      $125,000 (45%)                         │ │
│  │ Church Charter:      $89,500 (20%)                          │ │
│  │ Business Premium:    $142,280 (35%)                         │ │
│  │ Other Types:          $100,000 (10%)                         │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  📅 MONTHLY TRENDS                                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ January: ████████ 150 apps                                   │ │
│  │ February: ████████████ 210 apps                              │ │
│  │ March: ████████████████ 280 apps                             │ │
│  │ April: ████████████████ 265 apps                             │ │
│  │ May: ████████████████████ 342 apps                           │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  [Export Report] [Filter Date Range] [View Details]              │
└─────────────────────────────────────────────────────────────────┘
```

## API Endpoints Required

### Treaty Application Management
- `POST /api/treaty-applications` - Create new application
- `GET /api/treaty-applications` - List applications (with filters)
- `GET /api/treaty-applications/[id]` - Get application details
- `PUT /api/treaty-applications/[id]/status` - Update application status
- `POST /api/treaty-applications/[id]/approve` - Approve application
- `POST /api/treaty-applications/[id]/reject` - Reject application

### Payment Processing
- `POST /api/payments` - Process payment
- `GET /api/payments` - List payments
- `GET /api/payments/[id]` - Get payment details
- `PUT /api/payments/[id]/status` - Update payment status

### Reporting & Analytics
- `GET /api/treaty-tracker/statistics` - Get overview statistics
- `GET /api/treaty-tracker/revenue` - Get revenue reports
- `GET /api/treaty-tracker/applications` - Get application trends
- `POST /api/treaty-tracker/export` - Export reports

### System Settings
- `GET /api/treaty-settings` - Get system settings
- `PUT /api/treaty-settings` - Update system settings

## User Workflow

### Application Process
1. **User applies for treaty** → Admin approves treaty-level application
2. **User selects treaty types** → System generates application forms
3. **User completes forms** → Forms stored with status 'APPLIED'
4. **Admin reviews applications** → Approve/Reject each treaty type
5. **Approved applications** → Generate payment invoices
6. **User makes payment** → Admin confirms payment receipt
7. **Payment verified** → Treaty type activated for user

### Status Flow
```
APPLIED → UNDER_REVIEW → APPROVED → AWAITING_PAYMENT → PAID → ACTIVE
                                    ↓
                                REJECTED
```

## Implementation Phases

### Phase 1: Database Schema (Week 1-2)
- [ ] Create new tables (treaty_applications, payments, treaty_approvals, treaty_settings)
- [ ] Enhance existing tables (treaty_types, user_treaty_types)
- [ ] Create database migrations
- [ ] Update Prisma schema

### Phase 2: User Profile Interface (Week 2-3)
- [ ] Enhance UpdateUserTab treaty section
- [ ] Create application management components
- [ ] Implement payment processing interface
- [ ] Add application history tracking

### Phase 3: System Settings (Week 3-4)
- [ ] Create treaty tracker settings page
- [ ] Implement configuration management
- [ ] Add notification settings
- [ ] Create reporting dashboard

### Phase 4: API Development (Week 4-5)
- [ ] Implement application management APIs
- [ ] Create payment processing APIs
- [ ] Build reporting and analytics APIs
- [ ] Add system settings APIs

### Phase 5: Testing & Deployment (Week 5-6)
- [ ] Unit testing for all components
- [ ] Integration testing
- [ ] User acceptance testing
- [ ] Production deployment

## Success Metrics

- **Application Processing Time**: Reduce from manual to < 48 hours
- **Payment Processing**: Automate 90% of payment tracking
- **Reporting Accuracy**: 100% real-time data accuracy
- **User Experience**: Single interface for all treaty management
- **Admin Efficiency**: 70% reduction in manual administrative tasks

## Dependencies & Risks

### Dependencies
- Existing user authentication system
- Current treaty and treaty type database structure
- Payment gateway integration (if applicable)
- Email notification system

### Risks
- Data migration complexity from existing manual processes
- User adoption of new workflow
- Payment processing integration challenges
- Reporting performance with large datasets

## Conclusion

The Treaty Tracker System will provide a comprehensive solution for managing treaty applications, approvals, payments, and reporting. By implementing both user profile management and system-wide tracking, the system will streamline operations, improve visibility, and provide valuable insights into treaty management operations.

The phased implementation approach ensures minimal disruption to existing operations while gradually introducing new functionality and capabilities.