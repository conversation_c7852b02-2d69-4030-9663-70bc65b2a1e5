# Address APIs Documentation

This document provides comprehensive documentation for the address-related API endpoints in the NWA Alliance application.

## Overview

The address API system supports structured address data with country-specific formatting, regions, and backward compatibility. The following endpoints are available:

- **Country Address Formats** - Get address format configuration for countries
- **Regions** - Get regions/states for specific countries  
- **User APIs** - Create/update users with structured address fields

## Endpoints

### Country Address Formats

Get address format configuration for a specific country.

#### GET `/api/countries/{id}/address-format`

Returns the address format configuration for the specified country, including field labels, validation rules, and display templates.

**Parameters:**
- `id` (path) - Country ID (integer, required)

**Response:**
```json
{
  "addressFormat": {
    "id": 1,
    "countryId": 1,
    "postalCodeLabel": "ZIP Code",
    "postalCodeFormat": "^\\d{5}(-\\d{4})?$",
    "postalCodeRequired": true,
    "regionLabel": "State",
    "regionRequired": true,
    "townLabel": "City",
    "townRequired": true,
    "addressTemplate": "{street}\\n{town}, {region} {postalCode}\\n{country}",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

**Default Format (when country has no custom format):**
```json
{
  "addressFormat": {
    "id": null,
    "countryId": 1,
    "postalCodeLabel": "Postal Code",
    "postalCodeFormat": "^.+$",
    "postalCodeRequired": false,
    "regionLabel": "Region", 
    "regionRequired": false,
    "townLabel": "City",
    "townRequired": true,
    "addressTemplate": "{street}\\n{town}\\n{country}",
    "createdAt": null,
    "updatedAt": null
  }
}
```

**Error Responses:**
- `400` - Invalid country ID
- `404` - Country not found
- `500` - Internal server error

**Caching:**
- In-memory cache with 5-minute TTL
- Cache key: country ID
- Automatic cache invalidation

#### Example Usage

```javascript
// Fetch address format for United States (ID: 1)
const response = await fetch('/api/countries/1/address-format');
const { addressFormat } = await response.json();

// Use format for dynamic form labels
document.getElementById('postalCode').label = addressFormat.postalCodeLabel;
```

### Regions

Get regions/states for a specific country.

#### GET `/api/regions`

Returns a list of regions for the specified country.

**Query Parameters:**
- `countryId` (required) - Country ID (integer)
- `active` (optional) - Filter by active status (`true`, `false`, or `all`). Default: `true`

**Response:**
```json
{
  "regions": [
    {
      "id": 1,
      "name": "California",
      "code": "CA",
      "countryId": 1,
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "name": "Texas",
      "code": "TX", 
      "countryId": 1,
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

**Error Responses:**
- `400` - Missing or invalid countryId
- `404` - Country not found
- `500` - Internal server error

**Caching:**
- In-memory cache with 5-minute TTL
- Cache key: `{countryId}-{active}`
- Automatic cache invalidation

#### Example Usage

```javascript
// Fetch active regions for United States
const response = await fetch('/api/regions?countryId=1&active=true');
const { regions } = await response.json();

// Populate region dropdown
regions.forEach(region => {
  const option = document.createElement('option');
  option.value = region.id;
  option.textContent = region.name;
  regionSelect.appendChild(option);
});
```

### User APIs

Create and update users with structured address fields.

#### POST `/api/users`

Create a new user with structured address data.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "John",
  "lastName": "Doe",
  "streetAddress1": "123 Main Street",
  "streetAddress2": "Apt 4B",
  "town": "Springfield",
  "postalCode": "62701",
  "regionId": 12,
  "regionText": null,
  "country": "US",
  "city": 1,
  "phoneNumber": "******-123-4567"
}
```

**Address Field Validation:**
- `streetAddress1`: Required, 1-255 characters
- `streetAddress2`: Optional, max 255 characters
- `town`: Required, 1-100 characters
- `postalCode`: Optional, validated against country format
- `regionId`: Optional, must be valid region for country
- `regionText`: Optional when regionId provided, max 100 characters
- `country`: Required, valid country code
- `city`: Optional, must be valid city ID

**Backward Compatibility:**
The API also accepts legacy `address` field and will parse it into structured fields:

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123", 
  "firstName": "John",
  "lastName": "Doe",
  "address": "123 Main Street, Apt 4B, Springfield, IL 62701",
  "country": "US"
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "streetAddress1": "123 Main Street",
    "streetAddress2": "Apt 4B",
    "town": "Springfield",
    "postalCode": "62701",
    "regionId": 12,
    "regionText": null,
    "country": "US",
    "city": 1,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

#### PUT `/api/users/{id}`

Update an existing user's information, including address fields.

**Parameters:**
- `id` (path) - User ID (string, required)

**Request Body:**
Same structure as POST request. All fields are optional for updates.

**Partial Update Example:**
```json
{
  "streetAddress1": "456 Oak Avenue",
  "postalCode": "62702"
}
```

**Response:**
Same structure as POST response with updated user data.

**Error Responses:**
- `400` - Validation errors, invalid data
- `401` - Unauthorized access  
- `404` - User not found
- `409` - Duplicate email address
- `500` - Internal server error

#### GET `/api/users/{id}`

Get a specific user's information including formatted address.

**Parameters:**
- `id` (path) - User ID (string, required)

**Response:**
```json
{
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "streetAddress1": "123 Main Street",
    "streetAddress2": "Apt 4B", 
    "town": "Springfield",
    "postalCode": "62701",
    "regionId": 12,
    "regionText": null,
    "country": "US",
    "city": 1,
    "formattedAddress": "123 Main Street, Apt 4B\\nSpringfield, IL 62701\\nUnited States",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

## Address Field Migration

### Legacy to Structured Migration

When migrating from legacy `address` field to structured fields, the API applies the following logic:

1. **Parse Legacy Address**: Attempts to parse single address string into components
2. **Country Matching**: Uses country to determine parsing rules
3. **Component Extraction**: Extracts street, city, region, postal code
4. **Fallback Handling**: Preserves original address if parsing fails

### Migration Examples

**US Address:**
```
Input: "123 Main St, Springfield, IL 62701"
Output: {
  "streetAddress1": "123 Main St",
  "town": "Springfield", 
  "regionText": "IL",
  "postalCode": "62701"
}
```

**UK Address:**
```  
Input: "123 High St, Springfield, Gloucestershire, GL1 1AA"
Output: {
  "streetAddress1": "123 High St",
  "town": "Springfield",
  "regionText": "Gloucestershire", 
  "postalCode": "GL1 1AA"
}
```

## Validation Rules

### Country-Specific Postal Code Formats

- **US**: `^\d{5}(-\d{4})?$` (12345 or 12345-6789)
- **UK**: `^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$` (SW1A 1AA)
- **Canada**: `^[A-Z]\d[A-Z]\s?\d[A-Z]\d$` (A1A 1A1)
- **Australia**: `^\d{4}$` (1234)
- **Germany**: `^\d{5}$` (12345)

### Region Validation

- `regionId` must exist in the regions table
- `regionId` must belong to the specified country
- Either `regionId` or `regionText` can be provided, not both
- `regionText` is limited to 100 characters

### Address Template Variables

Address templates support the following variables:

- `{street}` - Combined street address lines
- `{town}` - Town/city name
- `{region}` - Region name or text
- `{postalCode}` - Postal/ZIP code
- `{country}` - Country name

## Performance Features

### Caching Strategy

- **Address Formats**: 5-minute in-memory cache per country
- **Regions**: 5-minute in-memory cache per country/active combination
- **Cache Keys**: Deterministic based on parameters
- **Cache Invalidation**: Automatic TTL-based expiration

### Performance Monitoring

All endpoints include performance monitoring:

```javascript
// Performance metrics are automatically collected
// View metrics via performance monitoring tools
const metrics = performanceMonitor.getSummary();
console.log(metrics['address_format_api']);
```

### Rate Limiting

Standard application rate limiting applies:
- 100 requests per minute per IP
- 1000 requests per hour per authenticated user

## Error Handling

### Standard Error Response Format

```json
{
  "error": "Error Type",
  "message": "Human-readable error message",
  "details": {
    "field": "specific error details"
  }
}
```

### Common Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `409` - Conflict (duplicate data)
- `422` - Unprocessable Entity (business logic errors)
- `429` - Too Many Requests (rate limiting)
- `500` - Internal Server Error

## Security Considerations

### Authentication

- All user modification endpoints require authentication
- JWT tokens or session-based authentication
- Role-based access control for administrative functions

### Data Validation

- All input data sanitized and validated
- SQL injection protection via Prisma ORM
- XSS prevention through output encoding
- CSRF protection for state-changing operations

### Audit Logging

All API operations are logged with:
- User ID and IP address
- Timestamp and operation type
- Before/after data for updates
- Geographic location data

## Testing

### API Testing

```bash
# Run API endpoint tests
npm run test:api

# Run address-specific API tests  
npm run test -- api/address

# Generate API test coverage
npm run test:coverage -- api/
```

### Example Test Cases

```javascript
describe('Address APIs', () => {
  test('should return address format for valid country', async () => {
    const response = await request(app)
      .get('/api/countries/1/address-format')
      .expect(200);
      
    expect(response.body.addressFormat).toBeDefined();
    expect(response.body.addressFormat.countryId).toBe(1);
  });

  test('should validate postal code format', async () => {
    const response = await request(app)
      .post('/api/users')
      .send({
        email: '<EMAIL>',
        country: 'US',
        postalCode: 'invalid'
      })
      .expect(400);
      
    expect(response.body.details.postalCode).toContain('Invalid format');
  });
});
```

## Migration Guide

For detailed migration instructions, see [Migration Guide](./migration-guide.md).

## Support

For API support and questions:
- Documentation: `/docs/api/`
- Issues: Create GitHub issue with 'api' label
- Testing: Use provided test utilities and examples
