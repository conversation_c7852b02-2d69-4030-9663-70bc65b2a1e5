# User Creation Interface Enhancement Specification

## Overview
This document outlines the improvements to be made to the user creation interface to include a tabbed layout with distinct sections for different types of user information. This restructuring will improve the user experience and data organization for creating new members.

## Current State
The current user creation interface is a single form that collects all user information in one view. Based on the database schema, we have the following relevant tables:
- `users` - Basic user information (name, email, etc.)
- `user_profiles` - Extended user information (license, passport, etc.)
- `treaties` - User treaty information
- `ordinances` - User ordinance information

## Proposed Enhancements

### 1. Tabbed Interface Structure
The user creation interface will be organized into the following tabs:
1. **Create User** - Basic user information
2. **Contact Details** - Contact information
3. **Identification** - Identity documents and numbers
4. **Member Treaty** - Treaty information
5. **Member Ordinance** - Ordinance information

### 2. Create User Tab
This tab will collect the most basic user information required for account creation.

Fields:
- First Name (text input)
- Surname (text input)
- Email (text input, with validation)
- Password (password input, with strength indicator)
- Confirm Password (password input)
- Status (dropdown: ACTIVE, INACTIVE, SUSPENDED, PENDING_VERIFICATION)

### 3. Contact Details Tab
This tab will collect contact information for the user.

Fields:
- Street Address (text input)
- Town (text input)
- City (text input)
- Country (text input)
- Postal Code (text input)

### 4. Identification Tab
This tab will collect all identity-related documents and numbers.

Fields:
- License (text input)
- Passport (text input)
- Peace Ambassador Number (text input)
- Trade Treaty Number (text input)

### 3. Identification Tab
This tab will collect all identity-related documents and numbers.

Fields:
- License (text input)
- Passport (text input)
- Peace Ambassador Number (text input)
- Trade Treaty Number (text input)

### 5. Member Treaty Tab
This tab will collect treaty-related information for the user.

Fields:
- Treaty Type (dropdown selection from treaty_types table)
- Status (dropdown: ACTIVE, EXPIRED, TERMINATED, PENDING_RENEWAL)
- Signed Date (date picker)
- Expiration Date (date picker)
- Renewal Date (date picker)
- Notes (textarea)
- Document Upload (file upload)

### 6. Member Ordinance Tab
This tab will collect ordinance-related information for the user.

Fields:
- Ordinance Type (dropdown selection from ordinance_types table)
- Status (dropdown: PENDING, IN_PROGRESS, COMPLETED, EXPIRED, CANCELLED)
- Completed Date (date picker)
- Expiration Date (date picker)
- Notes (textarea)
- Document Upload (file upload)

### 7. API Endpoints

#### a. Create User with All Details
`POST /api/users/create-with-details`

Request Body:
```json
{
  "user": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "securePassword123!",
    "status": "ACTIVE"
  },
  "profile": {
    "nwaEmail": "<EMAIL>",
    "streetAddress": "123 Main Street",
    "town": "Springfield",
    "city": "New York",
    "country": "United States",
    "postalCode": "10001",
    "mobile": "+**********",
    "bio": "A brief bio",
    "dateOfBirth": "1990-01-01",
    "titleId": "title-123"
  },
  "identification": {
    "license": "L123456789",
    "passport": "P987654321",
    "peaceAmbassadorNumber": "PA-12345",
    "tradeTreatyNumber": "TT-67890"
  },
  "treaty": {
    "treatyTypeId": "treaty-type-123",
    "status": "ACTIVE",
    "signedDate": "2025-01-01",
    "expirationDate": "2026-01-01",
    "renewalDate": "2025-12-01",
    "notes": "Treaty notes",
    "document": "base64-encoded-file"
  },
  "ordinance": {
    "ordinanceTypeId": "ordinance-type-123",
    "status": "PENDING",
    "completedDate": "2025-06-01",
    "expirationDate": "2026-06-01",
    "notes": "Ordinance notes",
    "document": "base64-encoded-file"
  }
}
```

#### b. Get Countries for Autocomplete
`GET /api/countries?q={query}`

Response:
```json
[
  {
    "id": "country-1",
    "name": "United States",
    "code": "US"
  },
  {
    "id": "country-2",
    "name": "United Kingdom",
    "code": "UK"
  }
]
```

#### c. Get Cities for Autocomplete
`GET /api/cities?q={query}&countryId={countryId}`

Response:
```json
[
  {
    "id": "city-1",
    "name": "New York"
  },
  {
    "id": "city-2",
    "name": "Los Angeles"
  }
]
```

#### d. Get User Creation Form Options
`GET /api/users/creation-options`

Response:
```json
{
  "titles": [
    {
      "id": "title-123",
      "name": "Ambassador"
    }
  ],
  "treatyTypes": [
    {
      "id": "treaty-type-123",
      "name": "Trade Agreement"
    }
  ],
  "ordinanceTypes": [
    {
      "id": "ordinance-type-123",
      "name": "Peace Ordinance"
    }
  ],
  "statuses": {
    "user": ["ACTIVE", "INACTIVE", "SUSPENDED", "PENDING_VERIFICATION"],
    "treaty": ["ACTIVE", "EXPIRED", "TERMINATED", "PENDING_RENEWAL"],
    "ordinance": ["PENDING", "IN_PROGRESS", "COMPLETED", "EXPIRED", "CANCELLED"]
  }
}
```

### 8. Database Schema Changes

#### a. Add Countries Table
```sql
CREATE TABLE "countries" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "countries_pkey" PRIMARY KEY ("id")
);
```

#### b. Add Cities Table
```sql
CREATE TABLE "cities" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "country_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "cities_pkey" PRIMARY KEY ("id")
);
```

#### c. Update User Profiles Table
```sql
ALTER TABLE "user_profiles" 
DROP COLUMN "country",
DROP COLUMN "city",
ADD COLUMN "country_id" TEXT,
ADD COLUMN "city_id" TEXT;

ALTER TABLE "user_profiles" 
ADD CONSTRAINT "user_profiles_country_id_fkey" 
FOREIGN KEY ("country_id") REFERENCES "countries"("id") 
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "user_profiles" 
ADD CONSTRAINT "user_profiles_city_id_fkey" 
FOREIGN KEY ("city_id") REFERENCES "cities"("id") 
ON DELETE SET NULL ON UPDATE CASCADE;
```

### 9. Validation Improvements

#### a. Frontend Validation
- Implement client-side validation for all fields
- Show clear error messages when validation fails
- Prevent form submission if any required fields are missing or invalid
- Validate that passwords match in the Create User tab

#### b. Backend Validation
- Implement server-side validation for all fields
- Ensure data integrity before saving to database
- Return appropriate error messages for invalid data
- Implement proper data sanitization

### 9. UI/UX Improvements

#### a. Tab Navigation
- Use a clear tab interface with visual indicators for the active tab
- Allow users to navigate between tabs
- Implement validation for each tab before allowing navigation to the next tab
- Show progress indicators to show which tabs have been completed

#### b. Form Layout
- Within each tab, organize fields in a clean, readable layout
- Use appropriate input types for each field (date pickers for dates, dropdowns for selections)
- Group related fields together with clear section headings
- Implement proper spacing and alignment

#### c. Responsive Design
- Ensure the interface works well on different screen sizes
- Optimize tab layout for mobile devices
- Ensure form fields are easily tappable on touch devices

### 10. Implementation Steps

1. **Frontend Development**
   - Create tabbed interface component
   - Implement form components for each tab
   - Add validation logic for each tab
   - Implement responsive design

2. **Backend API Development**
   - Create or update API endpoints to handle multi-step user creation
   - Implement proper data validation and sanitization
   - Ensure proper error handling and response formatting

3. **Database Integration**
   - Ensure proper relationships between users, user_profiles, treaties, and ordinances tables
   - Implement transaction handling for multi-table inserts
   - Add any necessary database constraints or indexes

4. **Testing**
   - Unit tests for form validation
   - Integration tests for API endpoints
   - End-to-end tests for the complete user creation flow
   - Accessibility testing

5. **Documentation**
   - Update user documentation
   - Create developer documentation for the API endpoints

### 11. Security Considerations

- Implement proper input validation and sanitization
- Use secure password hashing
- Implement proper authentication and authorization for user creation
- Ensure sensitive data is properly protected
- Implement rate limiting to prevent abuse
- Add audit logging for user creation activities

### 12. Backward Compatibility

- Ensure existing user creation methods continue to work
- Provide migration path for existing user data
- Maintain API compatibility where possible

## Conclusion
These enhancements will provide a more organized and user-friendly experience for creating new members. The tabbed layout will help users focus on one section at a time while ensuring all necessary information is collected.