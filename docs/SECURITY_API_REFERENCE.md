# NWA Alliance Security API Reference

## Overview

This document provides comprehensive API reference documentation for all security-related endpoints implemented as part of the 2025-09-24 Comprehensive Security Audit. All endpoints include authentication, authorization, rate limiting, input validation, and audit logging.

### Security Status: ✅ FULLY IMPLEMENTED

All security API endpoints are documented and validated through comprehensive testing.

---

## Authentication Endpoints

### NextAuth.js Authentication

**Base URL**: `/api/auth/[...nextauth]`

Handles all NextAuth.js authentication flows including:
- Email/password authentication
- Session management
- CSRF protection
- JWT token validation

#### Supported Methods
- `GET` - Session validation
- `POST` - Authentication requests
- `DELETE` - Session termination

#### Rate Limiting
- 100 requests per minute per IP address
- Automatic blocking after 5 failed attempts

#### Security Features
- CSRF token validation
- Secure session cookies
- IP address tracking
- Failed attempt logging

---

## User Management Endpoints

### Get Current User Permissions

**Endpoint**: `GET /api/user/permissions`

Returns the current user's permissions and roles.

#### Authentication Required
- ✅ Yes (session-based)

#### Response Format
```typescript
interface UserPermissionsResponse {
  permissions: string[];
  roles: string[];
  userId: string;
  email: string;
  name: string;
}
```

#### Example Request
```bash
curl -H "Authorization: Bearer <session-token>" \
     /api/user/permissions
```

#### Example Response
```json
{
  "permissions": [
    "remote_servers:read",
    "remote_servers:write",
    "users:read",
    "audit:read"
  ],
  "roles": ["admin"],
  "userId": "user123",
  "email": "<EMAIL>",
  "name": "System Administrator"
}
```

#### Rate Limiting
- 50 requests per minute per user

#### Audit Logging
- ✅ Enabled - All access attempts logged

---

### Update User Password

**Endpoint**: `POST /api/user/password`

Updates the current user's password.

#### Authentication Required
- ✅ Yes (session-based)

#### Request Body
```typescript
interface PasswordUpdateRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}
```

#### Validation Rules
- Current password must be correct
- New password must meet complexity requirements
- Password confirmation must match

#### Example Request
```bash
curl -X POST /api/user/password \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <session-token>" \
  -d '{
    "currentPassword": "oldpassword123",
    "newPassword": "NewSecurePass123!",
    "confirmPassword": "NewSecurePass123!"
  }'
```

#### Rate Limiting
- 10 requests per hour per user
- 3 failed attempts trigger account lockout

#### Security Features
- Password complexity validation
- Failed attempt tracking
- Account lockout after failures
- Audit logging of all attempts

---

### Two-Factor Authentication Setup

**Endpoint**: `POST /api/user/2fa/setup`

Initiates 2FA setup for the current user.

#### Authentication Required
- ✅ Yes (session-based)

#### Response Format
```typescript
interface TwoFactorSetupResponse {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}
```

#### Example Response
```json
{
  "secret": "JBSWY3DPEHPK3PXP",
  "qrCodeUrl": "data:image/png;base64,iVBORw0KGgoAAAANS...",
  "backupCodes": [
    "A1B2C3D4",
    "E5F6G7H8",
    "I9J0K1L2"
  ]
}
```

#### Rate Limiting
- 5 requests per hour per user

#### Security Features
- TOTP secret generation
- QR code generation
- Backup code generation
- Secure secret storage

---

### Verify Two-Factor Authentication

**Endpoint**: `POST /api/user/2fa/verify`

Verifies 2FA setup completion.

#### Authentication Required
- ✅ Yes (session-based)

#### Request Body
```typescript
interface TwoFactorVerifyRequest {
  token: string;
}
```

#### Example Request
```bash
curl -X POST /api/user/2fa/verify \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <session-token>" \
  -d '{"token": "123456"}'
```

#### Security Features
- TOTP token validation
- Automatic 2FA enablement on success
- Failed attempt tracking

---

## Permission Management Endpoints

### Get System Permissions and Roles

**Endpoint**: `GET /api/permissions`

Returns all system permissions and roles.

#### Authentication Required
- ✅ Yes (session-based)

#### Required Permissions
- `permissions:read`

#### Response Format
```typescript
interface PermissionsResponse {
  permissions: Array<{
    id: string;
    name: string;
    resource: string;
    action: string;
    description: string;
    createdAt: string;
  }>;
  roles: Array<{
    id: string;
    name: string;
    description: string;
    hierarchy: number;
    permissions: string[];
    createdAt: string;
  }>;
  permissionCategories: Array<{
    name: string;
    description: string;
    permissions: string[];
  }>;
}
```

#### Example Request
```bash
curl -H "Authorization: Bearer <admin-token>" \
     /api/permissions
```

#### Rate Limiting
- 30 requests per minute per user

#### Audit Logging
- ✅ Enabled - All access attempts logged

---

### Assign Permissions to Role

**Endpoint**: `POST /api/roles/[roleId]/permissions`

Assigns permissions to a specific role.

#### Authentication Required
- ✅ Yes (session-based)

#### Required Permissions
- `roles:write`

#### URL Parameters
- `roleId` - The ID of the role to modify

#### Request Body
```typescript
interface RolePermissionAssignment {
  permissions: string[];
  operation: 'add' | 'remove' | 'replace';
}
```

#### Example Request
```bash
curl -X POST /api/roles/admin123/permissions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin-token>" \
  -d '{
    "permissions": ["remote_servers:write", "users:read"],
    "operation": "add"
  }'
```

#### Validation Rules
- Role must exist
- Permissions must be valid
- User must have permission to modify roles

#### Rate Limiting
- 20 requests per minute per user

#### Security Features
- Permission validation
- Role hierarchy checking
- Audit logging of all changes

---

### Get Role Permissions

**Endpoint**: `GET /api/roles/[roleId]/permissions`

Returns all permissions assigned to a specific role.

#### Authentication Required
- ✅ Yes (session-based)

#### Required Permissions
- `roles:read`

#### URL Parameters
- `roleId` - The ID of the role to query

#### Example Request
```bash
curl -H "Authorization: Bearer <admin-token>" \
     /api/roles/admin123/permissions
```

#### Response Format
```typescript
interface RolePermissionsResponse {
  role: {
    id: string;
    name: string;
    description: string;
  };
  permissions: Array<{
    id: string;
    name: string;
    resource: string;
    action: string;
    description: string;
  }>;
  inheritedPermissions: string[];
}
```

---

## Audit and Security Endpoints

### Get Audit Logs

**Endpoint**: `GET /api/audit/logs`

Returns audit log entries with filtering and pagination.

#### Authentication Required
- ✅ Yes (session-based)

#### Required Permissions
- `audit:read`

#### Query Parameters
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 50, max: 100)
- `userId` - Filter by user ID
- `action` - Filter by action type
- `resource` - Filter by resource type
- `startDate` - Filter from date (ISO 8601)
- `endDate` - Filter to date (ISO 8601)
- `success` - Filter by success status

#### Example Request
```bash
curl -H "Authorization: Bearer <admin-token>" \
     "/api/audit/logs?page=1&limit=25&action=update&resource=user"
```

#### Response Format
```typescript
interface AuditLogsResponse {
  logs: Array<{
    id: string;
    userId: string;
    action: string;
    resource: string;
    resourceId: string;
    success: boolean;
    statusCode: number;
    ipAddress: string;
    userAgent: string;
    timestamp: string;
    duration: number;
    metadata: Record<string, any>;
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

#### Rate Limiting
- 30 requests per minute per user

#### Security Features
- Comprehensive filtering
- Pagination support
- IP address logging
- User agent tracking

---

### Get Security Events

**Endpoint**: `GET /api/security/events`

Returns security events and incidents.

#### Authentication Required
- ✅ Yes (session-based)

#### Required Permissions
- `audit:read`

#### Query Parameters
- `severity` - Filter by severity level
- `eventType` - Filter by event type
- `startDate` - Filter from date
- `endDate` - Filter to date
- `ipAddress` - Filter by IP address

#### Example Request
```bash
curl -H "Authorization: Bearer <admin-token>" \
     "/api/security/events?severity=high&startDate=2025-01-01"
```

#### Response Format
```typescript
interface SecurityEventsResponse {
  events: Array<{
    id: string;
    userId: string;
    eventType: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    ipAddress: string;
    userAgent: string;
    metadata: Record<string, any>;
    createdAt: string;
    resolvedAt?: string;
    resolution?: string;
  }>;
  summary: {
    totalEvents: number;
    criticalEvents: number;
    unresolvedEvents: number;
    eventsByType: Record<string, number>;
  };
}
```

#### Rate Limiting
- 20 requests per minute per user

---

### Report Security Event

**Endpoint**: `POST /api/security/events`

Reports a security event for analysis.

#### Authentication Required
- ✅ Yes (session-based)

#### Request Body
```typescript
interface SecurityEventReport {
  eventType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  metadata?: Record<string, any>;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
}
```

#### Example Request
```bash
curl -X POST /api/security/events \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin-token>" \
  -d '{
    "eventType": "suspicious_activity",
    "severity": "high",
    "description": "Multiple failed login attempts",
    "metadata": {
      "attemptCount": 15,
      "timeWindow": "5 minutes"
    }
  }'
```

#### Security Features
- Automatic event classification
- Alert triggering based on severity
- Integration with monitoring systems

---

## Remote Server Security Endpoints

### Get Remote Server Permissions

**Endpoint**: `GET /api/remote-servers/[id]/user-permissions`

Returns user permissions for a specific remote server.

#### Authentication Required
- ✅ Yes (session-based)

#### Required Permissions
- `remote_servers:read`

#### URL Parameters
- `id` - The remote server ID

#### Example Request
```bash
curl -H "Authorization: Bearer <token>" \
     /api/remote-servers/server123/user-permissions
```

#### Response Format
```typescript
interface RemoteServerPermissionsResponse {
  serverId: string;
  serverName: string;
  permissions: Array<{
    userId: string;
    userEmail: string;
    permissions: string[];
    roles: string[];
    assignedAt: string;
    assignedBy: string;
  }>;
}
```

#### Rate Limiting
- 30 requests per minute per user

---

### Sync Remote Server Permissions

**Endpoint**: `POST /api/remote-servers/[id]/sync`

Synchronizes permissions with the remote server.

#### Authentication Required
- ✅ Yes (session-based)

#### Required Permissions
- `remote_servers:sync`

#### URL Parameters
- `id` - The remote server ID

#### Request Body
```typescript
interface SyncRequest {
  force?: boolean;
  dryRun?: boolean;
}
```

#### Example Request
```bash
curl -X POST /api/remote-servers/server123/sync \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin-token>" \
  -d '{"dryRun": true}'
```

#### Security Features
- Dry run capability
- Comprehensive logging
- Rollback support
- Conflict detection

---

## Rate Limiting Endpoints

### Get Rate Limit Status

**Endpoint**: `GET /api/rate-limit/status`

Returns current rate limit status for the authenticated user.

#### Authentication Required
- ✅ Yes (session-based)

#### Response Format
```typescript
interface RateLimitStatusResponse {
  limits: Array<{
    endpoint: string;
    windowMs: number;
    maxRequests: number;
    remainingRequests: number;
    resetTime: string;
  }>;
  blocked: boolean;
  blockedUntil?: string;
}
```

#### Example Request
```bash
curl -H "Authorization: Bearer <token>" \
     /api/rate-limit/status
```

#### Rate Limiting
- 10 requests per minute per user

---

## Security Testing Endpoints

### Run Security Tests

**Endpoint**: `POST /api/security/test`

Runs security tests and returns results.

#### Authentication Required
- ✅ Yes (session-based)

#### Required Permissions
- `admin:access`

#### Request Body
```typescript
interface SecurityTestRequest {
  testType: 'penetration' | 'load' | 'compliance' | 'all';
  includePerformance?: boolean;
  generateReport?: boolean;
}
```

#### Example Request
```bash
curl -X POST /api/security/test \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin-token>" \
  -d '{
    "testType": "penetration",
    "generateReport": true
  }'
```

#### Response Format
```typescript
interface SecurityTestResponse {
  testId: string;
  testType: string;
  status: 'running' | 'completed' | 'failed';
  results: {
    passed: number;
    failed: number;
    warnings: number;
    errors: Array<{
      test: string;
      message: string;
      severity: string;
    }>;
  };
  reportUrl?: string;
  completedAt?: string;
}
```

#### Security Features
- Comprehensive test coverage
- Report generation
- Performance benchmarking
- Compliance validation

---

## Error Handling

All security endpoints follow consistent error handling patterns:

### Authentication Errors (401)
```json
{
  "error": "Unauthorized",
  "message": "Authentication required",
  "code": "AUTHENTICATION_REQUIRED"
}
```

### Authorization Errors (403)
```json
{
  "error": "Forbidden",
  "message": "Insufficient permissions",
  "requiredPermissions": ["admin:access"],
  "code": "INSUFFICIENT_PERMISSIONS"
}
```

### Validation Errors (400)
```json
{
  "error": "Validation Error",
  "message": "Invalid input data",
  "details": {
    "field": "password",
    "message": "Password must be at least 8 characters"
  },
  "code": "VALIDATION_ERROR"
}
```

### Rate Limit Errors (429)
```json
{
  "error": "Too Many Requests",
  "message": "Rate limit exceeded",
  "retryAfter": 60,
  "code": "RATE_LIMIT_EXCEEDED"
}
```

---

## Security Headers

All security endpoints include comprehensive security headers:

### Standard Security Headers
- `X-Frame-Options: DENY` - Prevents clickjacking
- `X-Content-Type-Options: nosniff` - Prevents MIME sniffing
- `X-XSS-Protection: 1; mode=block` - Enables XSS filtering
- `Referrer-Policy: strict-origin-when-cross-origin` - Controls referrer information
- `Content-Security-Policy` - Prevents XSS attacks

### CORS Headers
- `Access-Control-Allow-Origin` - Specifies allowed origins
- `Access-Control-Allow-Methods` - Specifies allowed HTTP methods
- `Access-Control-Allow-Headers` - Specifies allowed headers
- `Access-Control-Allow-Credentials: true` - Allows credentials

---

## Best Practices for API Usage

### 1. Authentication
- Always include valid authentication headers
- Use HTTPS for all requests
- Store tokens securely
- Implement token refresh logic

### 2. Error Handling
- Handle all HTTP status codes appropriately
- Implement retry logic for rate limits
- Log security events for monitoring
- Don't expose sensitive information in errors

### 3. Rate Limiting
- Implement exponential backoff for retries
- Monitor rate limit status
- Distribute requests across time windows
- Handle 429 responses gracefully

### 4. Security Monitoring
- Log all API requests and responses
- Monitor for suspicious activity
- Implement alerting for security events
- Regular security testing

---

## Support and Troubleshooting

### Common Issues

#### Authentication Failures
**Problem**: 401 Unauthorized responses
**Solution**:
1. Verify session token validity
2. Check token expiration
3. Ensure proper authentication headers
4. Verify user account status

#### Permission Denied
**Problem**: 403 Forbidden responses
**Solution**:
1. Check user permissions
2. Verify role assignments
3. Review permission requirements
4. Contact system administrator

#### Rate Limiting
**Problem**: 429 Too Many Requests
**Solution**:
1. Implement request throttling
2. Use exponential backoff
3. Monitor rate limit status
4. Distribute requests over time

### Debug Commands

```bash
# Test authentication
curl -H "Authorization: Bearer <token>" /api/user/permissions

# Check rate limit status
curl -H "Authorization: Bearer <token>" /api/rate-limit/status

# View audit logs
curl -H "Authorization: Bearer <admin-token>" /api/audit/logs

# Run security tests
curl -X POST /api/security/test \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin-token>" \
  -d '{"testType": "all"}'
```

### Support Channels
- **Security Issues**: <EMAIL>
- **API Support**: <EMAIL>
- **Documentation**: <EMAIL>

---

## Conclusion

The NWA Alliance Security API provides comprehensive security functionality with enterprise-grade protection, monitoring, and compliance features. All endpoints are thoroughly tested, documented, and follow security best practices.

**API Status**: ✅ FULLY IMPLEMENTED AND VALIDATED
**Last Updated**: 2025-09-24
**Version**: 1.0.0
