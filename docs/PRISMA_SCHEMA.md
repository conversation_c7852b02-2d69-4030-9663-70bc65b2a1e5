# NWA Member Portal Prisma Schema Documentation

This document provides comprehensive documentation for the Prisma schema used in the NWA Member Portal application, including all OAuth-related models and database structures.

## 📋 Table of Contents

1. [Overview](#overview)
2. [Database Models](#database-models)
3. [OAuth System](#oauth-system)
4. [Setup Instructions](#setup-instructions)
5. [Running Migrations](#running-migrations)
6. [Seeding Data](#seeding-data)
7. [Development Guidelines](#development-guidelines)

## 📖 Overview

The NWA Member Portal uses Prisma as its ORM (Object-Relational Mapping) tool to interact with a PostgreSQL database. This schema defines all database models, relationships, and constraints for the application.

Key features of our Prisma schema:
- Full TypeScript support with type safety
- Comprehensive user management system
- OAuth 2.0 implementation for external authentication
- Role-based access control (RBAC)
- File attachment support
- Audit logging capabilities

## 🗄 Database Models

### Core User Models

#### User
The primary user model containing basic user information and authentication data.

#### UserProfile
Extended user profile information including contact details, biographical information, and organizational data.

#### Session
NextAuth.js session management for web authentication.

#### VerificationToken
Tokens used for email verification and password reset functionality.

### Role & Permission System

#### Role
User roles that define access levels and permissions.

#### Permission
Individual permissions that can be assigned to roles.

#### RolePermission
Many-to-many relationship between roles and permissions.

#### UserRole
Many-to-many relationship between users and roles.

### Organizational Structure

#### Title
Ambassadorial and organizational titles.

#### Position
Diplomatic and organizational positions.

#### TitlePosition
Relationship between titles and positions.

#### UserPosition
Assignment of users to positions with time-based tracking.

### Content Management

#### OrdinanceType
Categories and types of ordinances.

#### Ordinance
Individual ordinance records with status tracking.

#### TreatyType
Categories and types of treaties.

#### Treaty
Individual treaty records with status tracking.

#### FileAttachment
File attachments for treaties and other documents.

### Administrative Features

#### Project
External API projects with their own authentication credentials.

#### Scope
API scopes that define access permissions for external projects.

#### UserProjectScope
User-specific scope assignments for projects.

#### AuditLog
Comprehensive audit trail for all system activities.

#### PendingBulkUpload
Temporary storage for bulk user upload data.

### OAuth System Models

#### RemoteServer
OAuth 2.0 clients that can authenticate with the system.

#### AuthorizationCode
Temporary authorization codes used in the OAuth flow.

#### OAuthToken
Access and refresh tokens issued to OAuth clients.

## 🔐 OAuth System

The NWA Member Portal implements a complete OAuth 2.0 authorization server that allows external applications to authenticate users and access their data with appropriate permissions.

### OAuth Flow

1. **Authorization Request**: Client redirects user to authorization endpoint
2. **User Authentication**: User authenticates and grants permissions
3. **Authorization Code**: System generates temporary authorization code
4. **Token Exchange**: Client exchanges code for access/refresh tokens
5. **API Access**: Client uses access token to access protected resources

### RemoteServer Model

The `RemoteServer` model represents OAuth 2.0 clients:

```prisma
model RemoteServer {
  id                      String    @id @default(cuid())
  name                    String
  url                     String
  apiKey                  String    @map("apiKey")
  description             String?
  isActive                Boolean   @default(true)
  createdAt               DateTime  @default(now())
  updatedAt               DateTime  @updatedAt
  clientId                String?   @unique @map("client_id")
  clientSecret            String?   @map("client_secret")
  redirectUris            String[]  @map("redirect_uris")
  grantTypes              String[]  @default(["authorization_code", "refresh_token"]) @map("grant_types")
  defaultScopes           String[]  @default(["read:profile"]) @map("default_scopes")
  callbackUrl             String?   @map("callback_url")
  allowedOrigins          String[]  @map("allowed_origins")
  tokenEndpointAuthMethod String    @default("client_secret_basic") @map("token_endpoint_auth_method")
  
  AuthorizationCode       AuthorizationCode[]
  OAuthToken              OAuthToken[]
}
```

### AuthorizationCode Model

Temporary codes used in the authorization flow:

```prisma
model AuthorizationCode {
  id             String    @id @default(cuid())
  remoteServerId String    @map("remote_server_id")
  userId         String    @map("user_id")
  code           String    @unique
  redirectUri    String?   @map("redirect_uri")
  scope          String?
  expiresAt      DateTime  @map("expires_at")
  createdAt      DateTime  @default(now()) @map("created_at")
  usedAt         DateTime? @map("used_at")

  remoteServer RemoteServer @relation(fields: [remoteServerId], references: [id])
  user         User         @relation(fields: [userId], references: [id])
}
```

### OAuthToken Model

Access and refresh tokens for API authentication:

```prisma
model OAuthToken {
  id              String    @id @default(cuid())
  remoteServerId  String    @map("remote_server_id")
  userId          String?   @map("user_id")
  accessToken     String    @map("access_token")
  refreshToken    String?   @map("refresh_token")
  tokenType       String    @default("Bearer") @map("token_type")
  scope           String?   
  expiresAt       DateTime  @map("expires_at")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  remoteServer    RemoteServer @relation(fields: [remoteServerId], references: [id])
  user            User?        @relation(fields: [userId], references: [id])
}
```

## ⚙ Setup Instructions

### Prerequisites

1. Node.js 18+ installed
2. PostgreSQL database running
3. Docker (optional, for containerized development)

### Environment Configuration

Create a `.env` file in the project root with the following variables:

```env
# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/database_name?schema=public"

# NextAuth.js Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your_nextauth_secret_here"

# OAuth Configuration
OAUTH_CLIENT_ID="your_oauth_client_id"
OAUTH_CLIENT_SECRET="your_oauth_client_secret"
```

### Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Generate Prisma client:
   ```bash
   npx prisma generate
   ```

3. Run database migrations:
   ```bash
   npx prisma migrate dev
   ```

4. Seed the database (optional):
   ```bash
   npm run prisma:seed
   ```

## 🚀 Running Migrations

### Development Migrations

For development environments, use:
```bash
npx prisma migrate dev
```

This command will:
1. Compare your schema with the database
2. Generate a new migration if needed
3. Apply the migration to your database
4. Generate the Prisma Client

### Production Migrations

For production deployments, use:
```bash
npx prisma migrate deploy
```

This command applies pending migrations without generating new ones.

### Migration Naming

When prompted for a migration name, use descriptive names like:
- `add_user_profile_fields`
- `create_oauth_tables`
- `add_unique_constraint_to_client_id`

## 🌱 Seeding Data

### Running the Seed Script

The seed script creates initial data for development and testing:

```bash
npm run prisma:seed
```

### What the Seed Script Creates

1. **Initial Roles**:
   - `member`: Standard user role
   - `admin`: System administrator role
   - `moderator`: Community moderator role

2. **Initial Permissions**:
   - User management permissions
   - Content management permissions
   - Administrative permissions

3. **Test User**:
   - Email: `<EMAIL>`
   - Name: `Test User`

4. **Test User Profile**:
   - NWA Email: `<EMAIL>`
   - Country: `USA`
   - City: `New York`

5. **OAuth Client Configuration**:
   - Client ID: `nwapromote-client-local`
   - Client Secret: `da69150be2ae984ea232f144387add211844500d9d94de131ba78f9e2936fbff`
   - Redirect URI: `http://localhost:3002/api/auth/callback/member-portal-custom`

### Customizing Seed Data

To customize the seed data for your environment:
1. Edit `prisma/seed.ts`
2. Modify the test user credentials
3. Adjust role assignments
4. Update OAuth client configuration

## 🛠 Development Guidelines

### Adding New Models

1. Add the model to `prisma/schema.prisma`
2. Run `npx prisma migrate dev` to create and apply migration
3. Run `npx prisma generate` to update the Prisma Client
4. Update the seed script if needed

### Model Best Practices

1. **Use descriptive names**: `UserProfile` instead of `UP`
2. **Follow naming conventions**: CamelCase for model names, snake_case for database columns
3. **Add appropriate indexes**: Use `@@index` for frequently queried fields
4. **Define relations explicitly**: Always specify relation fields and references
5. **Use appropriate data types**: Choose the right type for each field
6. **Add validation constraints**: Use `@unique`, `@default`, and other constraints

### Relation Guidelines

1. **Bidirectional relations**: Define relations on both sides when appropriate
2. **Foreign key constraints**: Use `@relation` with proper `fields` and `references`
3. **Cascade options**: Specify `onDelete` and `onUpdate` behaviors
4. **Many-to-many relations**: Use explicit join tables for better control

### Migration Best Practices

1. **Small, focused migrations**: Create separate migrations for unrelated changes
2. **Descriptive names**: Use clear, concise migration names
3. **Test in development**: Always test migrations in development first
4. **Backup production**: Always backup production data before migrating
5. **Rollback plans**: Consider how to rollback problematic migrations

## 🧪 Testing

### Database Testing

Run database tests with:
```bash
npm run test:database
```

### Integration Testing

Run integration tests with:
```bash
npm run test:integration
```

## 📊 Schema Visualization

To visualize the database schema:
```bash
npx prisma studio
```

This opens Prisma Studio, a visual database browser.

## 🆘 Troubleshooting

### Common Issues

1. **Migration conflicts**: Reset the database with `npx prisma migrate reset`
2. **Type generation errors**: Run `npx prisma generate --watch` for continuous generation
3. **Connection issues**: Verify `DATABASE_URL` in your environment variables
4. **Seed script errors**: Check that all required models exist and have proper relations

### Resetting Development Database

To reset your development database:
```bash
npx prisma migrate reset
```

This will:
1. Drop the database
2. Recreate it
3. Apply all migrations
4. Run the seed script

## 📚 Additional Resources

- [Prisma Documentation](https://www.prisma.io/docs/)
- [NextAuth.js Documentation](https://next-auth.js.org/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

---
**NWA Member Portal** - Peace, Love, Prosperity for Humanity