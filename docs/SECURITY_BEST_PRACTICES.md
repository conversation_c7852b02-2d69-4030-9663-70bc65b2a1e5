# NWA Alliance Security Best Practices Guide

## Overview

This guide provides developers with essential security best practices for building secure applications on the NWA Alliance platform. Following these practices ensures that new features integrate properly with the existing security infrastructure and maintain the highest security standards.

### Target Audience
- Frontend developers
- Backend developers
- Full-stack developers
- Security-conscious developers

### Security Status: ✅ FULLY IMPLEMENTED

All security best practices are documented and validated through comprehensive testing.

---

## 1. Authentication Best Practices

### 1.1 Session Management

#### ✅ DO: Use NextAuth.js Sessions
```typescript
// Correct: Use NextAuth.js for session management
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }

  // Process authenticated request...
}
```

#### ❌ DON'T: Implement Custom Session Management
```typescript
// Incorrect: Custom session handling
const sessionCookie = request.cookies.get('session');
if (!sessionCookie) {
  return NextResponse.json({ error: 'No session' }, { status: 401 });
}
// Custom session validation logic...
```

### 1.2 Client-Side Authentication

#### ✅ DO: Use NextAuth.js React Hooks
```typescript
'use client';

import { useSession } from 'next-auth/react';

export function UserProfile() {
  const { data: session, status } = useSession();

  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  if (!session) {
    return <div>Please sign in</div>;
  }

  return (
    <div>
      <h1>Welcome, {session.user.name}!</h1>
      <p>Email: {session.user.email}</p>
    </div>
  );
}
```

#### ✅ DO: Handle Authentication States
```typescript
'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export function ProtectedComponent() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  if (!session) {
    return null; // Will redirect
  }

  return <div>Protected content</div>;
}
```

---

## 2. Authorization Best Practices

### 2.1 API Route Protection

#### ✅ DO: Use requireAuth Middleware
```typescript
// src/app/api/protected-endpoint/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/middleware/require-auth';

export async function GET(request: NextRequest) {
  try {
    // Require specific permissions
    const authContext = await requireAuth(request, {
      requirePermissions: ['users:read'],
      auditLogging: true,
    });

    // Your protected logic here...
    return NextResponse.json({
      message: 'Success',
      userId: authContext.userId,
    });
  } catch (error) {
    // Error handling is automatic
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    );
  }
}
```

#### ✅ DO: Specify Exact Permissions
```typescript
// Good: Specific permissions
const authContext = await requireAuth(request, {
  requirePermissions: ['remote_servers:write'],
});

// Better: Multiple options (OR logic)
const authContext = await requireAuth(request, {
  requirePermissions: ['admin:access', 'remote_servers:write'],
});

// Best: Role-based access
const authContext = await requireAuth(request, {
  requireRoles: ['admin'],
});
```

#### ❌ DON'T: Skip Authorization
```typescript
// Incorrect: No authorization
export async function GET(request: NextRequest) {
  // Anyone can access this - SECURITY RISK!
  return NextResponse.json({ data: 'sensitive information' });
}
```

### 2.2 Frontend Permission Checks

#### ✅ DO: Use Permission Components
```typescript
import { PermissionWrapper } from '@/components/auth/PermissionWrapper';

export function AdminPanel() {
  return (
    <PermissionWrapper
      roles={['admin']}
      fallback={<div>Access denied</div>}
    >
      <div>
        <h1>Admin Panel</h1>
        <UserManagement />
        <SystemSettings />
      </div>
    </PermissionWrapper>
  );
}
```

#### ✅ DO: Check Permissions Before Actions
```typescript
import { usePermissions } from '@/hooks/usePermissions';

export function UserActions({ userId }: { userId: string }) {
  const { canEditUsers, canDeleteUsers } = usePermissions();

  return (
    <div>
      {canEditUsers && (
        <button onClick={() => editUser(userId)}>
          Edit User
        </button>
      )}

      {canDeleteUsers && (
        <button onClick={() => deleteUser(userId)}>
          Delete User
        </button>
      )}
    </div>
  );
}
```

#### ❌ DON'T: Assume Permissions
```typescript
// Incorrect: Assuming permissions without checking
export function UserActions() {
  return (
    <div>
      <button onClick={() => editUser()}>Edit User</button>
      <button onClick={() => deleteUser()}>Delete User</button>
      {/* These might fail if user lacks permissions */}
    </div>
  );
}
```

---

## 3. Input Validation Best Practices

### 3.1 Server-Side Validation

#### ✅ DO: Use Zod Schemas
```typescript
import { z } from 'zod';

const userUpdateSchema = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email(),
  role: z.enum(['member', 'admin', 'moderator']).optional(),
});

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const validation = userUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validation.error.flatten()
        },
        { status: 400 }
      );
    }

    const { name, email, role } = validation.data;
    // Safe to use validated data...
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid JSON' },
      { status: 400 }
    );
  }
}
```

#### ✅ DO: Validate All Inputs
```typescript
// Validate query parameters
const searchParamsSchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  search: z.string().max(100).optional(),
});

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const params = Object.fromEntries(searchParams.entries());

  const validation = searchParamsSchema.safeParse(params);
  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid parameters' },
      { status: 400 }
    );
  }

  const { page = 1, limit = 10, search } = validation.data;
  // Safe to use validated parameters...
}
```

### 3.2 Client-Side Validation

#### ✅ DO: Validate Before Submission
```typescript
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const userSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100),
  email: z.string().email('Invalid email address'),
});

type UserFormData = z.infer<typeof userSchema>;

export function UserForm() {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
  });

  const onSubmit = async (data: UserFormData) => {
    // Data is already validated by Zod
    const response = await fetch('/api/users', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      // Handle server-side validation errors
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div>
        <label>Name</label>
        <input {...register('name')} />
        {errors.name && <span>{errors.name.message}</span>}
      </div>

      <div>
        <label>Email</label>
        <input {...register('email')} />
        {errors.email && <span>{errors.email.message}</span>}
      </div>

      <button type="submit">Submit</button>
    </form>
  );
}
```

---

## 4. Database Security Best Practices

### 4.1 Safe Query Practices

#### ✅ DO: Use Parameterized Queries
```typescript
// Correct: Parameterized queries
const users = await prisma.user.findMany({
  where: {
    email: {
      contains: searchTerm, // Safe - Prisma handles parameterization
    },
    role: {
      in: roleFilter, // Safe - Prisma handles parameterization
    },
  },
  take: 10,
});
```

#### ✅ DO: Use Prisma Input Validation
```typescript
// Correct: Let Prisma handle validation
const user = await prisma.user.create({
  data: {
    email: '<EMAIL>', // Prisma validates email format
    name: 'John Doe',
    role: 'member', // Prisma validates against enum
  },
});
```

#### ❌ DON'T: Use Raw SQL for User Input
```typescript
// Incorrect: Raw SQL with user input
const users = await prisma.$queryRaw`
  SELECT * FROM users WHERE email LIKE '%${searchTerm}%'
`; // SQL Injection risk!
```

### 4.2 Transaction Management

#### ✅ DO: Use Database Transactions
```typescript
// Correct: Use transactions for related operations
const result = await prisma.$transaction(async (tx) => {
  // Create user
  const user = await tx.user.create({
    data: { email: '<EMAIL>', name: 'John Doe' },
  });

  // Assign role
  await tx.userRole.create({
    data: {
      userId: user.id,
      roleId: memberRole.id,
    },
  });

  // Log audit event
  await tx.auditLog.create({
    data: {
      userId: user.id,
      action: 'create',
      resource: 'user',
    },
  });

  return user;
});
```

#### ✅ DO: Handle Transaction Rollbacks
```typescript
// Correct: Proper error handling
try {
  const result = await prisma.$transaction(async (tx) => {
    // Multiple operations...
    return await performDatabaseOperations(tx);
  });

  // Success
  return NextResponse.json({ success: true, data: result });
} catch (error) {
  // Transaction automatically rolled back
  console.error('Transaction failed:', error);
  return NextResponse.json(
    { error: 'Operation failed' },
    { status: 500 }
  );
}
```

---

## 5. API Security Best Practices

### 5.1 Rate Limiting

#### ✅ DO: Implement Appropriate Rate Limits
```typescript
// src/app/api/sensitive-endpoint/route.ts
export async function POST(request: NextRequest) {
  // Strict rate limiting for sensitive operations
  const rateLimitResult = await checkRateLimit(request, {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5,      // 5 requests per minute
  });

  if (!rateLimitResult.allowed) {
    return NextResponse.json(
      {
        error: 'Too Many Requests',
        retryAfter: rateLimitResult.retryAfter,
      },
      {
        status: 429,
        headers: {
          'Retry-After': String(rateLimitResult.retryAfter),
        },
      }
    );
  }

  // Process request...
}
```

#### ✅ DO: Use Different Limits for Different Operations
```typescript
// Read operations - higher limits
const readRateLimit = {
  windowMs: 60 * 1000,
  maxRequests: 100,
};

// Write operations - lower limits
const writeRateLimit = {
  windowMs: 60 * 1000,
  maxRequests: 30,
};

// Delete operations - strictest limits
const deleteRateLimit = {
  windowMs: 60 * 1000,
  maxRequests: 10,
};
```

### 5.2 Error Handling

#### ✅ DO: Provide Consistent Error Responses
```typescript
// Consistent error format
export async function GET(request: NextRequest) {
  try {
    const data = await getData();

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error) {
    console.error('API Error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred',
      },
      { status: 500 }
    );
  }
}
```

#### ✅ DO: Log Security Events
```typescript
// Log security-relevant events
export async function DELETE(request: NextRequest) {
  try {
    const authContext = await requireAuth(request, {
      requirePermissions: ['users:delete'],
      auditLogging: true, // Enable audit logging
    });

    const user = await deleteUser(userId);

    // Log successful deletion
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'delete',
      'user',
      true,
      request,
      { metadata: { deletedUserId: userId } }
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    // Log failed attempt
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'delete',
      'user',
      false,
      request,
      {
        statusCode: 403,
        errorMessage: 'Permission denied',
        metadata: { attemptedUserId: userId }
      }
    );

    return NextResponse.json(
      { error: 'Permission denied' },
      { status: 403 }
    );
  }
}
```

### 5.3 CORS Configuration

#### ✅ DO: Configure CORS Properly
```typescript
// src/lib/middleware/cors.ts
import Cors from 'cors';

const cors = Cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3001'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400, // 24 hours
});

// Apply CORS middleware
export async function applyCors(request: NextRequest, response: NextResponse) {
  return new Promise((resolve, reject) => {
    cors(request as any, response as any, (result: any) => {
      if (result instanceof Error) {
        reject(result);
      } else {
        resolve(result);
      }
    });
  });
}
```

---

## 6. Frontend Security Best Practices

### 6.1 Component Security

#### ✅ DO: Sanitize User Input
```typescript
'use client';

import DOMPurify from 'isomorphic-dompurify';

interface UserContentProps {
  content: string;
  allowHtml?: boolean;
}

export function UserContent({ content, allowHtml = false }: UserContentProps) {
  if (allowHtml) {
    // Sanitize HTML content
    const sanitizedContent = DOMPurify.sanitize(content);
    return <div dangerouslySetInnerHTML={{ __html: sanitizedContent }} />;
  }

  // Plain text - safe
  return <div>{content}</div>;
}
```

#### ✅ DO: Validate Props
```typescript
// src/components/UserProfile.tsx
interface UserProfileProps {
  userId: string;
  showEmail?: boolean;
  showPhone?: boolean;
}

export function UserProfile({ userId, showEmail = false, showPhone = false }: UserProfileProps) {
  // Validate required props
  if (!userId) {
    console.error('UserProfile: userId is required');
    return <div>Error: Invalid user ID</div>;
  }

  // Use props safely
  return (
    <div>
      <h2>User Profile</h2>
      {showEmail && <div>Email: {user.email}</div>}
      {showPhone && <div>Phone: {user.phone}</div>}
    </div>
  );
}
```

### 6.2 State Management Security

#### ✅ DO: Use Secure State Management
```typescript
// src/hooks/useSecureData.ts
import { useState, useEffect, useCallback } from 'react';
import { usePermissions } from '@/hooks/usePermissions';

export function useSecureData(userId: string) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { hasPermission } = usePermissions();

  const fetchData = useCallback(async () => {
    if (!userId) {
      setError('Invalid user ID');
      setLoading(false);
      return;
    }

    // Check permissions before fetching
    if (!hasPermission('users:read')) {
      setError('Permission denied');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`/api/users/${userId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch user data');
      }

      const userData = await response.json();
      setData(userData);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [userId, hasPermission]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}
```

### 6.3 Form Security

#### ✅ DO: Implement Secure Forms
```typescript
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const secureFormSchema = z.object({
  email: z.string().email('Invalid email'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export function SecureForm() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
  } = useForm({
    resolver: zodResolver(secureFormSchema),
  });

  const onSubmit = async (data: FormData) => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError('root', { message: errorData.message });
        return;
      }

      // Success
    } catch (error) {
      setError('root', { message: 'Network error' });
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div>
        <label>Email</label>
        <input
          type="email"
          {...register('email')}
          autoComplete="email"
        />
        {errors.email && <span>{errors.email.message}</span>}
      </div>

      <div>
        <label>Password</label>
        <input
          type="password"
          {...register('password')}
          autoComplete="new-password"
        />
        {errors.password && <span>{errors.password.message}</span>}
      </div>

      <div>
        <label>Confirm Password</label>
        <input
          type="password"
          {...register('confirmPassword')}
          autoComplete="new-password"
        />
        {errors.confirmPassword && <span>{errors.confirmPassword.message}</span>}
      </div>

      {errors.root && <div className="error">{errors.root.message}</div>}

      <button type="submit" disabled={isSubmitting}>
        {isSubmitting ? 'Creating Account...' : 'Create Account'}
      </button>
    </form>
  );
}
```

---

## 7. Security Headers and CORS

### 7.1 Security Headers

#### ✅ DO: Implement Security Headers
```typescript
// src/lib/middleware/security-headers.ts
import { NextResponse } from 'next/server';

export function addSecurityHeaders(response: NextResponse) {
  const headers = response.headers;

  // Prevent clickjacking
  headers.set('X-Frame-Options', 'DENY');

  // Prevent MIME type sniffing
  headers.set('X-Content-Type-Options', 'nosniff');

  // Enable XSS protection
  headers.set('X-XSS-Protection', '1; mode=block');

  // Referrer policy
  headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Content Security Policy
  headers.set('Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
    "style-src 'self' 'unsafe-inline'; " +
    "img-src 'self' data: https:; " +
    "font-src 'self'; " +
    "connect-src 'self' https:;"
  );

  // Permissions policy
  headers.set('Permissions-Policy',
    'camera=(), microphone=(), geolocation=(), payment=()'
  );

  return response;
}
```

### 7.2 CORS Configuration

#### ✅ DO: Configure CORS Securely
```typescript
// src/lib/middleware/cors.ts
const corsOptions = {
  // Specify exact origins
  origin: [
    'http://localhost:3001',
    'https://yourdomain.com',
    'https://www.yourdomain.com',
  ],

  // Limit methods
  methods: ['GET', 'POST', 'PUT', 'DELETE'],

  // Specify allowed headers
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
  ],

  // Enable credentials for authenticated requests
  credentials: true,

  // Cache preflight requests
  maxAge: 86400, // 24 hours
};

// Apply CORS middleware
export async function withCors(handler: Function) {
  return async (request: NextRequest, ...args: any[]) => {
    const response = await handler(request, ...args);

    // Add CORS headers
    response.headers.set('Access-Control-Allow-Origin', request.headers.get('origin') || '');
    response.headers.set('Access-Control-Allow-Methods', corsOptions.methods.join(', '));
    response.headers.set('Access-Control-Allow-Headers', corsOptions.allowedHeaders.join(', '));
    response.headers.set('Access-Control-Allow-Credentials', 'true');
    response.headers.set('Access-Control-Max-Age', String(corsOptions.maxAge));

    return response;
  };
}
```

---

## 8. Logging and Monitoring

### 8.1 Structured Logging

#### ✅ DO: Use Structured Logging
```typescript
// src/lib/logger.ts
interface LogContext {
  userId?: string;
  requestId?: string;
  ip?: string;
  userAgent?: string;
  [key: string]: any;
}

export function logSecurityEvent(
  level: 'info' | 'warn' | 'error',
  event: string,
  context: LogContext
) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level,
    event,
    context,
  };

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`[${level.toUpperCase()}] ${event}`, context);
  }

  // Log to external service in production
  if (process.env.NODE_ENV === 'production') {
    // Send to logging service
    sendToLoggingService(logEntry);
  }
}

// Usage
logSecurityEvent('warn', 'suspicious_activity', {
  userId: 'user123',
  ip: '***********',
  action: 'multiple_failed_logins',
  attemptCount: 5,
});
```

### 8.2 Error Tracking

#### ✅ DO: Track and Report Errors
```typescript
// src/lib/error-tracker.ts
export async function trackError(
  error: Error,
  context: {
    userId?: string;
    component?: string;
    action?: string;
    metadata?: Record<string, any>;
  }
) {
  const errorReport = {
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    context,
  };

  // Log error
  console.error('Application Error:', errorReport);

  // Report to error tracking service
  if (process.env.ERROR_TRACKING_DSN) {
    await reportError(errorReport);
  }

  // Store in database for analysis
  await prisma.errorLog.create({
    data: {
      message: error.message,
      stack: error.stack,
      context: JSON.stringify(context),
      createdAt: new Date(),
    },
  });
}
```

---

## 9. Testing Security Features

### 9.1 Unit Testing Security

#### ✅ DO: Test Permission Logic
```typescript
// src/__tests__/components/PermissionWrapper.test.tsx
import { render, screen } from '@testing-library/react';
import { PermissionProvider } from '@/components/providers/PermissionProvider';
import { PermissionWrapper } from '@/components/auth/PermissionWrapper';

describe('PermissionWrapper', () => {
  it('renders children when user has required permission', () => {
    render(
      <PermissionProvider permissions={['users:read']}>
        <PermissionWrapper permissions={['users:read']}>
          <div>Secret Content</div>
        </PermissionWrapper>
      </PermissionProvider>
    );

    expect(screen.getByText('Secret Content')).toBeInTheDocument();
  });

  it('renders fallback when user lacks permission', () => {
    render(
      <PermissionProvider permissions={['users:read']}>
        <PermissionWrapper permissions={['users:write']}>
          <div>Secret Content</div>
        </PermissionWrapper>
      </PermissionProvider>
    );

    expect(screen.queryByText('Secret Content')).not.toBeInTheDocument();
    expect(screen.getByText('Access Denied')).toBeInTheDocument();
  });
});
```

### 9.2 Integration Testing Security

#### ✅ DO: Test Security Endpoints
```typescript
// src/__tests__/api/security-endpoints.test.ts
import { NextRequest } from 'next/server';
import { createMocks } from 'node-mocks-http';

describe('/api/users/[id]', () => {
  it('requires authentication', async () => {
    const { req, res } = createMocks({
      method: 'GET',
      url: '/api/users/123',
    });

    const response = await GET(req as NextRequest);

    expect(response.status).toBe(401);
    expect(await response.json()).toMatchObject({
      error: 'Unauthorized',
    });
  });

  it('requires proper permissions', async () => {
    const { req, res } = createMocks({
      method: 'PUT',
      url: '/api/users/123',
      headers: {
        authorization: 'Bearer valid-token',
      },
      body: { name: 'New Name' },
    });

    // Mock insufficient permissions
    mockUserPermissions(['users:read']); // Missing users:write

    const response = await PUT(req as NextRequest);

    expect(response.status).toBe(403);
    expect(await response.json()).toMatchObject({
      error: 'Forbidden',
    });
  });
});
```

### 9.3 Security Testing Tools

#### ✅ DO: Use Security Testing Libraries
```typescript
// package.json
{
  "devDependencies": {
    "@testing-library/jest-dom": "^5.16.5",
    "@testing-library/react": "^13.4.0",
    "@testing-library/user-event": "^14.4.3",
    "jest": "^29.0.0",
    "supertest": "^6.3.0",
    "node-mocks-http": "^1.11.0"
  }
}
```

---

## 10. Performance Security Considerations

### 10.1 Efficient Permission Checking

#### ✅ DO: Cache Permission Results
```typescript
// src/hooks/usePermissions.ts
import { useMemo } from 'react';

export function usePermissions() {
  const { permissions } = usePermissionContext();

  const permissionCache = useMemo(() => {
    const cache = new Map();
    permissions.forEach(permission => {
      cache.set(permission, true);
    });
    return cache;
  }, [permissions]);

  const hasPermission = useCallback((permission: string) => {
    return permissionCache.get(permission) || false;
  }, [permissionCache]);

  return { hasPermission };
}
```

#### ✅ DO: Optimize Database Queries
```typescript
// Optimized permission query
const userPermissions = await prisma.user.findUnique({
  where: { id: userId },
  select: {
    id: true,
    userRoles: {
      select: {
        role: {
          select: {
            rolePermissions: {
              select: {
                permission: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    },
  },
});
```

### 10.2 Rate Limiting Performance

#### ✅ DO: Use Efficient Rate Limiting
```typescript
// Use in-memory store for development
const rateLimitStore = process.env.NODE_ENV === 'development'
  ? new Map()
  : new RedisStore();

// Efficient rate limiting
const rateLimitResult = await checkRateLimit(request, {
  windowMs: 60 * 1000,
  maxRequests: 100,
  store: rateLimitStore,
});
```

---

## 11. Deployment Security

### 11.1 Environment Configuration

#### ✅ DO: Use Environment Variables
```typescript
// .env.local
NEXTAUTH_SECRET=your-super-secret-key-here
NEXTAUTH_URL=http://localhost:3001
DATABASE_URL=postgresql://user:password@localhost:5432/nwa
REDIS_URL=redis://localhost:6379
ALLOWED_ORIGINS=http://localhost:3001,https://yourdomain.com
```

#### ✅ DO: Validate Environment Variables
```typescript
// src/lib/config.ts
export const config = {
  nextauth: {
    secret: process.env.NEXTAUTH_SECRET,
    url: process.env.NEXTAUTH_URL,
  },
  database: {
    url: process.env.DATABASE_URL!,
  },
  redis: {
    url: process.env.REDIS_URL,
  },
  security: {
    allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || [],
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000'),
    maxRequestsPerWindow: parseInt(process.env.MAX_REQUESTS_PER_WINDOW || '100'),
  },
};

// Validate required environment variables
if (!config.nextauth.secret) {
  throw new Error('NEXTAUTH_SECRET is required');
}
```

### 11.2 Production Deployment

#### ✅ DO: Secure Production Environment
```bash
# Production environment variables
NEXTAUTH_SECRET=$(openssl rand -base64 32)
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
NODE_ENV=production
```

#### ✅ DO: Enable Security Features in Production
```typescript
// src/lib/middleware/security.ts
export function getSecurityConfig() {
  const isProduction = process.env.NODE_ENV === 'production';

  return {
    // Strict in production, lenient in development
    cors: {
      origin: isProduction
        ? process.env.ALLOWED_ORIGINS?.split(',')
        : ['http://localhost:3001'],
    },
    rateLimiting: {
      enabled: true,
      strict: isProduction,
    },
    logging: {
      level: isProduction ? 'warn' : 'info',
      includeStackTrace: !isProduction,
    },
  };
}
```

---

## 12. Common Security Anti-Patterns

### 12.1 Anti-Patterns to Avoid

#### ❌ DON'T: Store Secrets in Code
```typescript
// Incorrect: Hardcoded secrets
const API_KEY = 'sk-1234567890abcdef';
const DATABASE_PASSWORD = 'secret123';

// Correct: Use environment variables
const API_KEY = process.env.OPENAI_API_KEY;
const DATABASE_PASSWORD = process.env.DATABASE_PASSWORD;
```

#### ❌ DON'T: Trust Client-Side Validation Only
```typescript
// Incorrect: Client-side only validation
export async function createUser(data: any) {
  // No server-side validation - SECURITY RISK!
  return await prisma.user.create({ data });
}

// Correct: Server-side validation
export async function createUser(data: any) {
  const validation = userSchema.safeParse(data);
  if (!validation.success) {
    throw new Error('Invalid user data');
  }
  return await prisma.user.create({ data: validation.data });
}
```

#### ❌ DON'T: Expose Sensitive Data
```typescript
// Incorrect: Exposing sensitive data
export async function getUser(userId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    // Exposing password hash - SECURITY RISK!
    select: { id: true, email: true, passwordHash: true }
  });
  return user;
}

// Correct: Select only needed fields
export async function getUser(userId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { id: true, email: true, name: true, createdAt: true }
  });
  return user;
}
```

#### ❌ DON'T: Skip Error Handling
```typescript
// Incorrect: No error handling
export async function deleteUser(userId: string) {
  await prisma.user.delete({ where: { id: userId } });
  // What if user doesn't exist? What if no permission?
}

// Correct: Proper error handling
export async function deleteUser(userId: string) {
  try {
    const user = await prisma.user.delete({
      where: { id: userId }
    });
    return { success: true, user };
  } catch (error) {
    if (error.code === 'P2025') {
      throw new Error('User not found');
    }
    throw new Error('Failed to delete user');
  }
}
```

---

## 13. Security Checklist

### 13.1 Pre-Deployment Checklist

#### Authentication & Authorization
- [ ] All API endpoints require authentication
- [ ] Authorization checks are implemented
- [ ] Permission inheritance works correctly
- [ ] Role assignments are validated

#### Input Validation
- [ ] All user inputs are validated
- [ ] SQL injection prevention is in place
- [ ] XSS protection is implemented
- [ ] File upload validation is configured

#### Data Protection
- [ ] Sensitive data is encrypted at rest
- [ ] Data is encrypted in transit (HTTPS)
- [ ] Database queries use parameterization
- [ ] Error messages don't expose sensitive data

#### Monitoring & Logging
- [ ] Security events are logged
- [ ] Audit trails are maintained
- [ ] Error tracking is configured
- [ ] Performance monitoring is in place

#### Testing
- [ ] Unit tests cover security logic
- [ ] Integration tests validate security
- [ ] Penetration testing is completed
- [ ] Load testing is performed

### 13.2 Code Review Checklist

#### Security Headers
- [ ] Security headers are configured
- [ ] CORS is properly configured
- [ ] Content Security Policy is implemented

#### Session Management
- [ ] Sessions have appropriate timeouts
- [ ] Session fixation is prevented
- [ ] Secure session handling is implemented

#### Error Handling
- [ ] Errors don't expose sensitive information
- [ ] Proper HTTP status codes are used
- [ ] Error logging is configured

#### Dependencies
- [ ] Dependencies are up to date
- [ ] Security vulnerabilities are patched
- [ ] Unused dependencies are removed

---

## 14. Resources and References

### Official Documentation
- [NextAuth.js Documentation](https://next-auth.js.org/)
- [Prisma Documentation](https://www.prisma.io/docs/)
- [Next.js Security](https://nextjs.org/docs/advanced-features/security-headers)

### Security Libraries
- [Zod Validation](https://zod.dev/)
- [Helmet.js Security Headers](https://helmetjs.github.io/)
- [DOMPurify XSS Protection](https://github.com/cure53/DOMPurify)

### Security Standards
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [OWASP Cheat Sheet Series](https://cheatsheetseries.owasp.org/)
- [Web Security Academy](https://portswigger.net/web-security)

### Testing Tools
- [Jest Testing Framework](https://jestjs.io/)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Supertest API Testing](https://github.com/visionmedia/supertest)

---

## Conclusion

Following these security best practices ensures that your NWA Alliance application maintains the highest security standards. Remember that security is an ongoing process that requires regular updates, monitoring, and testing.

### Key Principles
1. **Defense in Depth**: Implement multiple layers of security
2. **Least Privilege**: Grant minimum necessary permissions
3. **Fail Secure**: Default to secure behavior
4. **Audit Everything**: Log security-relevant events
5. **Keep Updated**: Regularly update dependencies and security measures

### Support
For security-related questions or concerns:
- **Security Team**: <EMAIL>
- **Development Team**: <EMAIL>
- **Documentation**: <EMAIL>

**Last Updated**: 2025-09-24
**Version**: 1.0.0