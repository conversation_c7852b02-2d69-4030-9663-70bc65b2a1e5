# Database Setup Guide

This document provides comprehensive instructions for setting up and managing the NWA Member Portal database using Prisma ORM.

## Prerequisites

- Node.js 18+ installed
- PostgreSQL 12+ running locally or accessible remotely
- npm or yarn package manager

## Quick Start

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

3. **Set up the database:**
   ```bash
   npm run db:setup
   ```

## Environment Configuration

Create a `.env` file with the following variables:

```env
# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# NextAuth.js Configuration
NEXTAUTH_URL="http://localhost:3005"
NEXTAUTH_SECRET="your-nextauth-secret-key"

# Other configurations...
```

## Database Commands

### Development Workflow

```bash
# Generate Prisma client
npm run db:generate

# Create and apply migration
npm run db:migrate

# Push schema changes without migration (development only)
npm run db:push

# Seed database with initial data
npm run db:seed

# Reset database (WARNING: destroys all data)
npm run db:reset

# Open Prisma Studio
npm run db:studio
```

### Utility Commands

```bash
# Test database connection
npm run db:util test

# Complete database setup (migrate + seed)
npm run db:util setup

# Create new migration
npm run db:util migrate <migration_name>

# Reset and reseed database
npm run db:util reset
```

## Database Schema Overview

### NextAuth.js Integration

The schema includes all required models for NextAuth.js:

- **User**: Core user information
- **Account**: OAuth provider accounts
- **Session**: User sessions
- **VerificationToken**: Email verification tokens

### NWA-Specific Models

- **UserProfile**: Extended user information (personal/NWA emails, biographical data)
- **Role/Permission**: Role-based access control system
- **Position**: Hierarchical position management
- **Ordinance/Treaty**: Religious ordinances and treaties tracking
- **AuditLog**: Comprehensive audit logging

### Key Features

1. **NextAuth.js Compatibility**: Full integration with NextAuth.js Prisma adapter
2. **Role-Based Access Control**: Flexible permission system
3. **Audit Logging**: Complete change tracking
4. **Hierarchical Positions**: Support for organizational structure
5. **Document Management**: File attachment support for ordinances/treaties

## Testing

Run database tests to verify schema integrity:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## Migration Management

### Creating Migrations

```bash
# Create a new migration
npx prisma migrate dev --name descriptive_migration_name
```

### Migration Best Practices

1. Always review generated SQL before applying
2. Test migrations on development database first
3. Backup production data before applying migrations
4. Use descriptive migration names

### Common Migration Scenarios

```bash
# Add new field
npx prisma migrate dev --name add_user_preferences

# Create new table
npx prisma migrate dev --name add_notifications_table

# Modify relationships
npx prisma migrate dev --name update_user_role_relations
```

## Seeding Data

The seed script creates:

- Initial roles (member, admin, moderator)
- Basic permissions
- Role-permission assignments
- Sample ordinance types
- Sample treaty types
- Sample positions

Customize `prisma/seed.ts` for your specific needs.

## Production Deployment

### Environment Setup

1. Set production DATABASE_URL
2. Generate secure NEXTAUTH_SECRET
3. Configure proper connection pooling

### Deployment Steps

```bash
# Generate Prisma client
npx prisma generate

# Apply migrations
npx prisma migrate deploy

# Seed production data (if needed)
npm run db:seed
```

### Connection Pooling

For production environments, consider:

- Connection pool size: 10-20 connections
- Connection timeout: 30 seconds
- Query timeout: 60 seconds

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check DATABASE_URL and PostgreSQL service
2. **Migration Errors**: Review schema changes and constraints
3. **Seed Failures**: Ensure database is empty or use upsert operations

### Debug Commands

```bash
# Check database connection
npm run db:util test

# View current schema
npx prisma db pull

# Introspect database
npx prisma introspect

# View migration status
npx prisma migrate status
```

## Security Considerations

1. **Environment Variables**: Never commit .env files
2. **Database Credentials**: Use strong passwords and rotate regularly
3. **Audit Logging**: Monitor for suspicious activities
4. **Access Control**: Implement proper role-based permissions

## Performance Optimization

1. **Indexes**: Key fields are indexed for optimal query performance
2. **Connection Pooling**: Configured for serverless environments
3. **Query Optimization**: Use Prisma's query optimization features
4. **Monitoring**: Set up query performance monitoring

## Support

For issues related to:
- Database schema: Check the design document
- Prisma ORM: Refer to [Prisma documentation](https://www.prisma.io/docs)
- NextAuth.js: Check [NextAuth.js documentation](https://next-auth.js.org)

## Contributing

When modifying the database schema:

1. Update the Prisma schema file
2. Create appropriate migrations
3. Update seed data if necessary
4. Add/update tests
5. Update documentation