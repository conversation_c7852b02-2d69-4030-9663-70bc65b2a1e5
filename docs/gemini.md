# Gemini Development Guidelines

## General Principles

*   **Linting:** All code must be "lint safe," meaning it must pass all linting checks without any errors or warnings.

### Specific Linting Rules

*   **Avoid `any`:** The use of the `any` type in TypeScript is strictly prohibited. It defeats the purpose of static typing and should be replaced with more specific types or `unknown`.
*   **Explicit Return Types:** All functions must have explicit return types. This improves code readability and helps catch bugs.
*   **No Unused Variables:** Variables that are declared but never used must be removed from the code.
*   **No `console.log`:** Do not commit code that contains `console.log` statements. Use a proper logger for debugging and monitoring.
*   **No Magic Numbers:** Avoid the use of "magic numbers" (i.e., numbers that appear in the code without explanation). Instead, declare them as constants with meaningful names.
*   **Prefer `const`:** Use `const` to declare variables that are not reassigned. Use `let` only for variables that need to be reassigned.

*   **Import Management:** Before removing any imports, thoroughly investigate the codebase to ensure the import is not used anywhere. This prevents breaking changes.
*   **Testing with Live Data:** Avoid using mock data in tests. Instead, insert necessary data into a test database and run tests against live data to ensure real-world behavior.

## Security Best Practices

*   **Input Sanitization:** Sanitize all user-provided input to prevent injection attacks, such as Cross-Site Scripting (XSS) and SQL Injection (SQLi).
*   **Database Interaction:** Use parameterized queries or an Object-Relational Mapper (ORM) like Prisma to interact with the database. Avoid raw SQL queries when possible.
*   **Authorization:** Implement robust authorization checks to ensure that users can only access the data and perform the actions that they are explicitly permitted to.
*   **Data Logging:** Do not log sensitive information, such as passwords, API keys, or personal user data.
*   **Secure Headers:** Use security-focused HTTP headers in the application's responses, including Content Security Policy (CSP), HTTP Strict Transport Security (HSTS), and X-Content-Type-Options.

## Code Quality and Best Practices

*   **Clarity and Conciseness:** Write code that is easy to read, understand, and maintain.
*   **Consistency:** Follow the existing coding style, naming conventions, and architectural patterns in the codebase.
*   **Testing:** Write unit and integration tests for all new features and bug fixes to ensure code quality and prevent regressions.
*   **Dependency Management:** Keep all project dependencies up to date to incorporate the latest features and security patches.
*   **File Length:** To maintain readability and ease of maintenance, individual code files should not exceed 500 lines.
*   **Test File Organization:** Cypress E2E tests should be broken down into separate files for each distinct area of testing. This ensures that test files remain focused and manageable.
