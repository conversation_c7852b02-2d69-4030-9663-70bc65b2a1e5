# NWA Alliance Deployment

## Deployment Scripts

This project includes a deployment script that can be used to deploy the application in two modes:

### Local Deployment
For local development and testing:
```bash
./deploy.sh --local
```

This will:
1. Clean all build artifacts
2. Rebuild the Next.js application
3. Build Docker images
4. Stop any running containers
5. Start all services locally using docker-compose

### Production Deployment
For deploying to production environment:
```bash
./deploy.sh
```

This will:
1. Clean all build artifacts
2. Rebuild the Next.js application
3. Build Docker images
4. Push images to GitHub Container Registry (GHCR)
5. Trigger redeployment on Dokploy server

## Prerequisites

- Docker and Docker Compose installed
- Node.js and npm installed
- GitHub account with appropriate permissions for GHCR
- Environment variables set:
  - `GITHUB_USERNAME` - Your GitHub username
  - `GITHUB_TOKEN` - GitHub personal access token with container registry permissions

## Environment Variables

The deployment script uses the following environment variables:

- `GITHUB_USERNAME` - GitHub username for authentication
- `GITHUB_TOKEN` - GitHub personal access token for pushing to GHCR
- `CLIENT_DEPLOY_WEBHOOK` - Dokploy webhook URL for client deployment
- `SERVER_DEPLOY_WEBHOOK` - Dokploy webhook URL for server deployment

## Local Development URLs

When running with `--local` flag:
- Application: http://localhost:3001
- PostgreSQL database: localhost:5434
- MinIO dashboard: http://localhost:9003
- Redis: localhost:6380 (used internally)

## Production URLs

- Application: http://nwa.cloudns.ch