don't use pfkill, use netstat and lsof -ti :port,port | xargs kill -9 args if this fails use netstat port to find the pid

# Icon Integration & ESLint Security Best Practices

## Icon Wiring Requirements

**CRITICAL**: All Lucide React icons imported into components MUST be wired into the UI using Tailwind CSS to prevent ESLint "unused import" warnings.

### Icon Integration Pattern

When adding icons to components, follow this exact pattern:

```tsx
import { Search, User, Plus, CheckCircle } from 'lucide-react';

// Header with icon
<h1 className="text-2xl font-bold flex items-center">
  <Search className="h-6 w-6 mr-2 text-blue-600" />
  Page Title
</h1>

// Button with icon
<button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
  <Plus className="h-4 w-4 mr-2" />
  Add New Item
</button>

// Status indicator with icon
<div className="flex items-center">
  <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
  <span>Status: Active</span>
</div>
```

### Required Tailwind Classes for Icons

- **Sizing**: `h-4 w-4`, `h-5 w-5`, `h-6 w-6` (be consistent with context)
- **Spacing**: `mr-2`, `ml-2`, `mr-3`, `ml-3` for proper spacing
- **Colors**: Use semantic color classes (`text-blue-600`, `text-green-600`, `text-red-600`)
- **Flex alignment**: Always use with `flex items-center` parent container

### Icon Placement Rules

1. **Headers**: Icons go before text with `mr-2` or `mr-3` spacing
2. **Buttons**: Icons go before button text with `mr-2` spacing
3. **Status indicators**: Icons go before status text with `mr-2` spacing
4. **Cards/Sections**: Icons in headers use `h-5 w-5` or `h-6 w-6` sizing

## ESLint Security & Type Safety Requirements

### TypeScript Security Best Practices

**MANDATORY**: Avoid `any` type usage. Use proper TypeScript patterns:

```tsx
// ❌ BAD - Using any
const data: any = await response.json();
const user = data.user;

// ✅ GOOD - Proper typing
interface UserResponse {
  user: {
    id: string;
    name: string;
    email: string;
  };
}

const data: UserResponse = await response.json();
const user = data.user;
```

### Secure API Response Handling

```tsx
// ✅ Proper response validation with Zod
import { z } from 'zod';

const UserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
});

const result = UserSchema.safeParse(data);
if (!result.success) {
  throw new Error('Invalid user data');
}
const user = result.data;
```

### ESLint Rules Compliance

**Critical rules that must be followed**:

1. **@typescript-eslint/no-unused-vars**: All variables must be used
2. **@typescript-eslint/no-explicit-any**: Avoid `any` type
3. **@typescript-eslint/consistent-type-imports**: Use `import type` for type-only imports
4. **no-case-declarations**: Use block scopes in switch cases
5. **no-constant-condition**: Avoid `while(true)` without breaks

### Import Type Best Practices

```tsx
// ❌ BAD - Regular import for types only
import { User, UserProfile } from './types';

// ✅ GOOD - Type-only imports
import type { User, UserProfile } from './types';
```

### Variable Naming & Unused Variables

- Use `_` prefix for intentionally unused variables: `_unusedVar`
- Use `console.log()` for debugging variables that will be used
- Destructure with ignore pattern: `const { active, ...rest } = data;`

## Security Considerations

### Input Validation

Always validate user inputs and API responses:

```tsx
import { z } from 'zod';

const Create_userSchema = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email(),
  role: z.enum(['user', 'admin']),
});

// Validate before processing
const validatedData = CreateUserSchema.parse(inputData);
```

### Error Handling

Never expose sensitive information in error messages:

```tsx
// ❌ BAD - Exposing internal details
catch (error) {
  console.error('Database error:', error.details);
  return res.status(500).json({ error: 'Database connection failed' });
}

// ✅ GOOD - Generic error messages
catch (error) {
  console.error('Processing error:', error.message);
  return res.status(500).json({ error: 'Internal server error' });
}
```

## Code Quality Standards

### Before Committing

1. **Run linting**: `npm run lint`
2. **Type checking**: `npm run typecheck`
3. **Build test**: `npm run build`
4. **Icon verification**: Ensure all imported icons are visible in UI

### File Organization

- Keep components under 500 lines
- Use proper TypeScript interfaces
- Follow existing naming conventions
- Maintain consistent file structure

These practices ensure security, maintainability, and ESLint compliance across the project.
