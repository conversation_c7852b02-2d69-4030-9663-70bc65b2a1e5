# Nation Treaty System User Guide

## Overview

The Nation Treaty System provides comprehensive management of diplomatic relationships, treaty agreements, and associated entities within the NWA Alliance platform. This guide will help you understand how to use the system effectively.

## Getting Started

### Accessing the System

1. **Navigate to Nation Treaties**: 
   - Click on "Nation Treaties" in the main sidebar navigation
   - Alternatively, navigate directly to `/treaties` in your browser

2. **Login Requirements**:
   - You must be logged into the NWA Alliance platform
   - Different features require different permission levels:
     - **Viewing**: Basic user access
     - **Creating/Editing**: Admin or Super Admin privileges
     - **Managing Members/Envoys**: Admin privileges
     - **Managing Offices**: Admin privileges

### System Layout

The main interface consists of:

1. **Navigation Menu** (Left Side):
   - Nation Treaties (current page)
   - Treaty Audit
   - System Settings

2. **Main Content Area** (Right Side):
   - Search and filter controls
   - Treaty list/table
   - Action buttons for management

## Managing Nation Treaties

### Viewing Nation Treaties

The main page displays all nation treaties in a structured table format:

- **Search**: Use the search box to find treaties by name, official name, or description
- **Filter**: Filter by status (Active, Inactive, Suspended, Pending)
- **Sort**: Click column headers to sort by different fields
- **Pagination**: Navigate through multiple pages of results

### Creating a New Nation Treaty

1. Click the **"Create Nation Treaty"** button in the top-right corner
2. Fill in the required information:
   - **Name**: Display name of the treaty (required)
   - **Official Name**: Full official diplomatic name (required)
   - **Status**: Choose from Active, Inactive, Suspended, or Pending
   - **Description**: Detailed description of the treaty
   - **Contact Information**: Primary contact details
   - **Emergency Contact**: Emergency contact information
   - **Additional Information**: Website, notes, and metadata
3. Click **"Create"** to save the treaty

### Editing an Existing Treaty

1. Locate the treaty you want to edit in the list
2. Click the **Edit** icon (pencil) in the Actions column
3. Modify the required fields
4. Click **"Update"** to save changes

### Deleting a Treaty

1. Locate the treaty you want to delete
2. Click the **Delete** icon (trash) in the Actions column
3. Confirm the deletion in the popup dialog
4. Note: This is a soft delete - the treaty is archived but not permanently removed

## Managing Treaty Members

### Accessing Members

1. Click on any treaty in the list to view details
2. Navigate to the **"Members"** tab
3. View all current members with their roles and status

### Adding Members

1. Click **"Add Member"** button
2. Search for the user by name or email
3. Select the appropriate role:
   - **Member**: Basic treaty participant
   - **Envoy**: Diplomatic representative
   - **Admin**: Treaty administrator
4. Set the member status (Active, Inactive, or Suspended)
5. Add any additional notes
6. Click **"Add Member"** to save

### Managing Member Roles

1. Locate the member in the list
2. Click **Edit** to modify their role or status
3. Update the information as needed
4. Click **"Update"** to save changes

### Removing Members

1. Locate the member you want to remove
2. Click **Delete** in the Actions column
3. Confirm the removal

## Managing Treaty Envoys

### Understanding Envoys

Envoys are diplomatic representatives appointed to treaties. They can have different types:

- **PRIMARY**: Main diplomatic representative
- **EMERGENCY**: Emergency contact envoy
- **LEGAL**: Legal affairs envoy

### Managing Envoys

1. Go to the **"Envoys"** tab for a specific treaty
2. View current envoys with their titles and appointment details
3. Use **"Add Envoy"** to appoint new envoys
4. Search for users and assign envoy titles
5. Set envoy types and status

### Common Envoy Titles

- Ambassador
- Consul General
- Consul
- Diplomat
- Special Envoy
- Legal Attaché

## Managing Treaty Offices

### Office Types

The system supports various office types:

- **Envoy Office**: Basic diplomatic office
- **Consulate General**: Major consular office
- **Embassy**: Primary diplomatic mission
- **Trade Office**: Commercial representation
- **Cultural Center**: Cultural exchange facility

### Office Status Options

- **Active**: Fully operational
- **Inactive**: Not currently operational
- **Under Construction**: Being built or renovated
- **Planned**: Approved but not yet constructed

### Creating Offices

1. Navigate to the **"Offices"** tab for a treaty
2. Click **"Create Office"**
3. Fill in office details:
   - **Office Type**: Select from available types
   - **Status**: Choose current operational status
   - **Address**: Complete physical address
   - **Contact Information**: Office-specific contacts
   - **Primary Office**: Mark as main office if applicable
4. Click **"Create Office"** to save

### Managing Multiple Offices

- **Primary Office**: Only one office can be marked as primary
- **Contact Information**: Each office can have unique contact details
- **Location Tracking**: Full address with country, region, and city
- **Status Management**: Track construction and operational phases

## User Integration

### Assigning Nations/Tribes to Users

When creating or managing users, you can assign nation and tribe memberships:

1. Navigate to **User Management** → **Create User**
2. Fill in user details
3. Navigate to the **"Nation/Tribe Assignment"** tab
4. Select nation and tribe from dropdown menus
5. Assign appropriate roles for each
6. **Important**: Users can be assigned to either a nation OR a tribe, but not both simultaneously

### Role-Based Access

Different roles provide different access levels:

- **Member**: Can view treaty information
- **Envoy**: Can represent the treaty diplomatically
- **Admin**: Full management capabilities

## Best Practices

### Data Entry

1. **Consistent Naming**: Use consistent naming conventions for treaties and offices
2. **Complete Information**: Fill in as much detail as possible for better tracking
3. **Contact Accuracy**: Ensure contact information is current and accurate
4. **Status Management**: Keep status information up to date

### Security Considerations

1. **Access Control**: Only assign admin roles to trusted users
2. **Data Privacy**: Handle personal contact information responsibly
3. **Audit Trail**: All actions are logged for accountability
4. **Regular Reviews**: Periodically review access permissions

### Performance Tips

1. **Search Effectively**: Use specific search terms to find treaties quickly
2. **Filter Appropriately**: Use status filters to focus on relevant treaties
3. **Batch Operations**: Perform multiple updates efficiently when possible
4. **Data Cleanup**: Regularly archive or update outdated information

## Troubleshooting

### Common Issues

**Cannot Create/Edit Treaties**
- Verify you have admin privileges
- Check that all required fields are completed
- Ensure you're not using duplicate names

**Cannot Find Users for Member Assignment**
- Verify the user exists in the system
- Check that the user has the correct permissions
- Search by email if name search doesn't work

**Office Address Not Saving**
- Verify all address fields are correctly formatted
- Check that country and region names are valid
- Ensure postal code format is correct

**Status Changes Not Applying**
- Verify you have permission to modify status
- Check for conflicting active/inactive states
- Ensure all related entities are properly configured

### Getting Help

1. **System Administration**: Contact your system administrator for technical issues
2. **User Support**: Use the built-in support system for user guidance
3. **Documentation**: Refer to this guide and API documentation
4. **Training**: Request formal training for complex operations

## Mobile Usage

The system is fully responsive and works on mobile devices:

- **Touch-Friendly**: All buttons and controls are touch-optimized
- **Responsive Tables**: Data tables adapt to small screens
- **Simplified Navigation**: Mobile-optimized menu system
- **Form Entry**: Mobile-friendly form layouts

## Integration with Other Systems

### User Management System
- Seamless integration with user creation and management
- Role-based access control across the platform
- Consistent user experience

### Document Management
- Treaty documents can be linked to treaty records
- Version control for important documents
- Access permissions for sensitive materials

### Audit System
- Complete audit trail of all system operations
- Compliance tracking and reporting
- Security incident monitoring

## Advanced Features

### Bulk Operations
- Create multiple treaties from templates
- Batch update treaty status
- Export data for external analysis

### Reporting
- Treaty status reports
- Member activity summaries
- Office utilization statistics

### Automation
- Automatic status updates based on dates
- Notification systems for important changes
- Scheduled data exports

## Glossary

- **Nation Treaty**: Formal agreement between nations
- **Member**: User associated with a treaty
- **Envoy**: Diplomatic representative
- **Office**: Physical diplomatic location
- **Status**: Current operational state
- **Primary Office**: Main diplomatic location

## Future Updates

The system is continuously being improved with:
- Enhanced search capabilities
- Advanced reporting features
- Integration with external systems
- Mobile app improvements
- Additional automation features

For the latest updates and feature announcements, refer to the system changelog or contact your system administrator.

---

*This user guide is maintained by the NWA Alliance development team. For questions or suggestions, please contact the support team.*