# Treaty Types Pricing Requirements

## Overview
Treaty types now support pricing functionality. Each treaty type can have an associated cost that is displayed when users assign treaty types to their treaties.

## Current Implementation

### Database Schema
- **Table**: `treaty_types`
- **Field**: `price` (DECIMAL(10,2))
- **Default Value**: 0.00
- **Migration**: `20250914080147_add_treaty_type_price`

### Frontend Display
- Treaty types are displayed with pricing in the format: `{name} - ${price.toFixed(2)} ({category})`
- Example: "Large Church - $0.00 (Religious)"

### User Workflow
1. User must first have a treaty number assigned in the Identification tab
2. User can then access the Treaties tab to add treaty types to their assigned treaty
3. Form shows only treaties that the user has from identification tab
4. User selects treaty types with associated costs
5. System calculates and displays total cost

## Current Pricing Status
- All treaty types are currently set to $0.00
- Pricing display is functional but costs are not yet configurable through the UI
- UX for setting prices on treaty types needs to be implemented

## Future Requirements
- [ ] Add treaty type pricing management interface for administrators
- [ ] Implement cost calculation and display in treaty assignment workflow
- [ ] Add payment processing integration for treaty type costs
- [ ] Create pricing tiers and discounts for different organization types
- [ ] Add bulk pricing updates for treaty types

## Technical Notes
- Price field is stored as DECIMAL(10,2) in the database
- Frontend uses TypeScript interface with `price: number` type
- Price formatting uses `.toFixed(2)` for consistent display
- Prices are displayed in USD format ($X.XX)

## Related Files
- `prisma/schema.prisma` - Database model with price field
- `prisma/migrations/20250914080147_add_treaty_type_price/migration.sql` - Migration file
- `src/components/users/manage/UserTreatyManagement.tsx` - Updated form with pricing display
- `src/app/api/treaty-types/route.ts` - Treaty types API (may need updates for pricing management)

## Integration Points
- **Identification Tab**: Must have treaty number before accessing treaty types
- **Treaties Tab**: Displays pricing and allows assignment of treaty types
- **Admin Interface**: Future development needed for price management
- **Payment System**: Future integration needed for paid treaty types