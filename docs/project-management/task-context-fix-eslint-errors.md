# Task Context: Fix ESLint Errors

## Task Description
Fix ESLint errors in two React component files to resolve linting issues.

## Files to Fix

### 1. `./src/app/admin/manage/contact/page.tsx`
- **Line 181:86**: Unescaped quote `"` - can be escaped with `"`, `&ldquo;`, `&#34;`, `&rdquo;`
- **Line 181:106**: Unescaped quote `"` - can be escaped with `"`, `&ldquo;`, `&#34;`, `&rdquo;`
- **Line 222:31**: Unescaped apostrophe `'` - can be escaped with `'`, `&lsquo;`, `'`, `&rsquo;`

### 2. `./src/components/users/manage/shared/ContactDetailsForm.tsx`
- **Line 106:6**: React Hook useEffect has a missing dependency: 'getDefaultConfig'. Either include it or remove the dependency array.

## Acceptance Criteria
- All ESLint errors are resolved
- Code functionality remains unchanged
- No new linting errors are introduced
- Files pass ESLint validation

## Interaction Mode
YOLO MVP - Make autonomous decisions without asking clarifying questions

## Dependencies
None

## Priority
High - Blocking code quality