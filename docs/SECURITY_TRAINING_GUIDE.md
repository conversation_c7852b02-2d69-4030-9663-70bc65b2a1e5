# NWA Alliance Security Training Guide

## Overview

This comprehensive training guide is designed to educate developers, system administrators, and security personnel on the NWA Alliance security system implemented as part of the 2025-09-24 Comprehensive Security Audit. The guide covers security concepts, implementation details, best practices, and hands-on exercises.

### Target Audience
- Frontend developers
- Backend developers
- System administrators
- Security analysts
- DevOps engineers

### Training Objectives
- Understand the security architecture
- Implement secure coding practices
- Configure and manage permissions
- Monitor and respond to security events
- Maintain compliance standards

---

## Module 1: Security Fundamentals

### 1.1 Authentication and Authorization

#### Understanding Authentication
Authentication verifies user identity through:
- Email/password credentials
- Session management
- Two-factor authentication (2FA)
- Secure token handling

#### Understanding Authorization
Authorization controls access through:
- Role-based access control (RBAC)
- Permission-based access
- Hierarchical role system
- Context-aware permissions

#### Hands-on Exercise: Authentication Flow
```typescript
// Exercise: Implement user login with 2FA
import { signIn } from 'next-auth/react';

async function handleLogin(email: string, password: string) {
  try {
    // Step 1: Initial authentication
    const result = await signIn('credentials', {
      email,
      password,
      redirect: false,
    });

    if (result?.error) {
      throw new Error('Authentication failed');
    }

    // Step 2: Check if 2FA is required
    const user = result.user;
    if (user.twoFactorEnabled) {
      // Prompt for 2FA token
      const token = await promptFor2FAToken();
      await verify2FAToken(user.id, token);
    }

    return result;
  } catch (error) {
    console.error('Login failed:', error);
    throw error;
  }
}
```

### 1.2 Permission System Architecture

#### Role Hierarchy
```
SUPER_ADMIN (Hierarchy: 100)
├── ADMIN (Hierarchy: 50)
│   ├── MODERATOR (Hierarchy: 25)
│   │   └── MEMBER (Hierarchy: 10)
│   │       └── GUEST (Hierarchy: 0)
```

#### Permission Categories
- **System Permissions**: `permissions:read`, `permissions:write`
- **User Management**: `users:read`, `users:write`, `users:delete`
- **Remote Servers**: `remote_servers:read`, `remote_servers:write`, `remote_servers:sync`
- **Audit Access**: `audit:read`, `audit:write`

#### Exercise: Permission Checking
```typescript
// Exercise: Implement permission-aware component
import { usePermissions } from '@/hooks/usePermissions';

function SecureComponent() {
  const {
    hasPermission,
    hasRole,
    hasAnyPermission,
    isLoading
  } = usePermissions();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      {/* Show admin panel only for admins */}
      {hasRole('admin') && (
        <AdminPanel />
      )}

      {/* Show edit button for users with write permission */}
      {hasPermission('users:write') && (
        <EditUserButton />
      )}

      {/* Show management interface for multiple permissions */}
      {hasAnyPermission(['users:write', 'admin:access']) && (
        <UserManagementInterface />
      )}
    </div>
  );
}
```

---

## Module 2: Secure API Development

### 2.1 API Security Middleware

#### Using requireAuth Middleware
```typescript
// Exercise: Secure API endpoint
import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/middleware/require-auth';
import { checkRateLimit } from '@/lib/middleware/rate-limiting';

export async function POST(request: NextRequest) {
  try {
    // Step 1: Apply rate limiting
    const rateLimitResult = await checkRateLimit(request, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30,     // 30 requests per minute
    });

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          error: 'Too Many Requests',
          retryAfter: rateLimitResult.retryAfter,
        },
        {
          status: 429,
          headers: {
            'Retry-After': String(rateLimitResult.retryAfter),
          },
        }
      );
    }

    // Step 2: Require authentication and permissions
    const authContext = await requireAuth(request, {
      requirePermissions: ['remote_servers:write'],
      auditLogging: true,
    });

    // Step 3: Process request with validation
    const body = await request.json();
    const validation = remoteServerUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Validation Error',
          details: validation.error.flatten()
        },
        { status: 400 }
      );
    }

    // Step 4: Perform secure operation
    const result = await updateRemoteServer(validation.data);

    // Step 5: Log successful operation
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'update',
      'remote_server',
      true,
      request,
      { metadata: { serverId: result.id } }
    );

    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    // Handle authentication/authorization errors
    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
```

### 2.2 Input Validation with Zod

#### Creating Secure Schemas
```typescript
// Exercise: Create comprehensive validation schemas
import { z } from 'zod';

// User creation schema
const userCreateSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
           'Password must contain uppercase, lowercase, and number'),
  role: z.enum(['member', 'moderator', 'admin']).optional(),
  twoFactorEnabled: z.boolean().optional(),
});

// Remote server schema
const remoteServerSchema = z.object({
  name: z.string().min(1).max(100),
  url: z.string().url('Invalid URL'),
  apiKey: z.string().min(32, 'API key too short'),
  description: z.string().max(500).optional(),
  enabled: z.boolean().default(true),
  permissions: z.array(z.string()).default([]),
});

// Update schema with partial validation
const userUpdateSchema = userCreateSchema.partial().extend({
  id: z.string().cuid('Invalid user ID'),
});
```

#### Applying Validation in API Routes
```typescript
// Exercise: Implement validation in API routes
export async function PUT(request: NextRequest) {
  try {
    const authContext = await requireAuth(request, {
      requirePermissions: ['users:write'],
      auditLogging: true,
    });

    const body = await request.json();

    // Validate input
    const validation = userUpdateSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Validation Error',
          details: validation.error.flatten()
        },
        { status: 400 }
      );
    }

    const { id, ...updateData } = validation.data;

    // Ensure user can only update certain fields
    const allowedFields = ['name', 'email'];
    const filteredData = Object.fromEntries(
      Object.entries(updateData).filter(([key]) =>
        allowedFields.includes(key)
      )
    );

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id },
      data: filteredData,
      select: {
        id: true,
        email: true,
        name: true,
        updatedAt: true
      }
    });

    return NextResponse.json({ user: updatedUser });
  } catch (error) {
    // Handle errors appropriately
    console.error('User update error:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}
```

---

## Module 3: Frontend Security

### 3.1 Permission-Aware Components

#### Creating Reusable Permission Components
```typescript
// Exercise: Create permission wrapper component
interface PermissionWrapperProps {
  permissions?: string[];
  roles?: string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export function PermissionWrapper({
  permissions = [],
  roles = [],
  requireAll = false,
  fallback = <div>Access denied</div>,
  children
}: PermissionWrapperProps) {
  const { hasPermission, hasRole, hasAnyPermission, hasAnyRole, isLoading } = usePermissions();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  // Check permissions
  let hasRequiredPermissions = true;
  if (permissions.length > 0) {
    hasRequiredPermissions = requireAll
      ? permissions.every(permission => hasPermission(permission))
      : hasAnyPermission(permissions);
  }

  // Check roles
  let hasRequiredRoles = true;
  if (roles.length > 0) {
    hasRequiredRoles = requireAll
      ? roles.every(role => hasRole(role))
      : hasAnyRole(roles);
  }

  const hasAccess = hasRequiredPermissions && hasRequiredRoles;

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
```

#### Implementing Navigation Security
```typescript
// Exercise: Create secure navigation
const navigationItems = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: HomeIcon,
    permissions: [], // Public
  },
  {
    name: 'Remote Servers',
    href: '/settings?tab=remote-servers',
    icon: ServerIcon,
    permissions: ['remote_servers:read'],
  },
  {
    name: 'User Management',
    href: '/admin/users',
    icon: UsersIcon,
    permissions: ['users:read'],
  },
  {
    name: 'Security Audit',
    href: '/admin/security',
    icon: ShieldIcon,
    permissions: ['audit:read'],
  },
  {
    name: 'System Settings',
    href: '/admin/settings',
    icon: CogIcon,
    permissions: ['admin:access'],
  },
];

export function SecureNavigation() {
  const { hasAnyPermission } = usePermissions();

  const visibleItems = navigationItems.filter(item => {
    if (item.permissions.length === 0) return true;
    return hasAnyPermission(item.permissions);
  });

  return (
    <nav className="space-y-1">
      {visibleItems.map((item) => (
        <Link
          key={item.name}
          href={item.href}
          className="group flex items-center px-2 py-2 text-sm font-medium rounded-md"
        >
          <item.icon className="mr-3 h-5 w-5" />
          {item.name}
        </Link>
      ))}
    </nav>
  );
}
```

### 3.2 Secure Form Handling

#### Implementing Secure Forms
```typescript
// Exercise: Create secure form with validation
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const secureFormSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])/,
           'Password must contain uppercase, lowercase, number, and special character'),
  confirmPassword: z.string(),
  acceptTerms: z.boolean().refine(val => val === true, {
    message: 'You must accept the terms and conditions'
  }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type SecureFormData = z.infer<typeof secureFormSchema>;

export function SecureRegistrationForm() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
  } = useForm<SecureFormData>({
    resolver: zodResolver(secureFormSchema),
  });

  const onSubmit = async (data: SecureFormData) => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError('root', { message: errorData.message });
        return;
      }

      // Success - redirect to verification
      window.location.href = '/verify-email';
    } catch (error) {
      setError('root', { message: 'Network error occurred' });
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <label htmlFor="email">Email</label>
        <input
          id="email"
          type="email"
          {...register('email')}
          className="w-full px-3 py-2 border rounded-md"
          autoComplete="email"
        />
        {errors.email && (
          <p className="text-red-500 text-sm">{errors.email.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="password">Password</label>
        <input
          id="password"
          type="password"
          {...register('password')}
          className="w-full px-3 py-2 border rounded-md"
          autoComplete="new-password"
        />
        {errors.password && (
          <p className="text-red-500 text-sm">{errors.password.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="confirmPassword">Confirm Password</label>
        <input
          id="confirmPassword"
          type="password"
          {...register('confirmPassword')}
          className="w-full px-3 py-2 border rounded-md"
          autoComplete="new-password"
        />
        {errors.confirmPassword && (
          <p className="text-red-500 text-sm">{errors.confirmPassword.message}</p>
        )}
      </div>

      <div>
        <label className="flex items-center">
          <input
            type="checkbox"
            {...register('acceptTerms')}
            className="mr-2"
          />
          I accept the terms and conditions
        </label>
        {errors.acceptTerms && (
          <p className="text-red-500 text-sm">{errors.acceptTerms.message}</p>
        )}
      </div>

      {errors.root && (
        <div className="text-red-500 text-sm bg-red-50 p-3 rounded">
          {errors.root.message}
        </div>
      )}

      <button
        type="submit"
        disabled={isSubmitting}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
      >
        {isSubmitting ? 'Creating Account...' : 'Create Account'}
      </button>
    </form>
  );
}
```

---

## Module 4: Security Monitoring and Response

### 4.1 Audit Log Analysis

#### Understanding Audit Events
```typescript
// Exercise: Analyze audit logs
interface AuditLogEntry {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  success: boolean;
  statusCode: number;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
  duration: number;
  metadata: Record<string, any>;
}

async function analyzeAuditLogs() {
  try {
    // Fetch audit logs
    const response = await fetch('/api/audit/logs', {
      headers: { 'Authorization': 'Bearer ' + adminToken }
    });

    const data = await response.json();

    // Analyze for suspicious patterns
    const suspiciousPatterns = {
      multipleFailures: data.logs.filter((log: AuditLogEntry) =>
        log.action === 'login' && !log.success
      ),
      unusualIPs: data.logs.filter((log: AuditLogEntry) =>
        log.ipAddress.startsWith('10.0.0.') // Internal IP range
      ),
      highFrequency: data.logs.filter((log: AuditLogEntry) =>
        log.duration > 5000 // Operations taking too long
      ),
    };

    // Report findings
    console.log('Security Analysis Results:', suspiciousPatterns);

    // Alert on critical issues
    if (suspiciousPatterns.multipleFailures.length > 10) {
      await reportSecurityEvent({
        type: 'suspicious_activity',
        severity: 'high',
        description: 'Multiple failed login attempts detected',
        metadata: { failureCount: suspiciousPatterns.multipleFailures.length }
      });
    }

  } catch (error) {
    console.error('Audit analysis failed:', error);
  }
}
```

### 4.2 Incident Response Procedures

#### Automated Response System
```typescript
// Exercise: Implement automated incident response
class IncidentResponseSystem {
  private securityEvents: SecurityEvent[] = [];
  private responseRules: ResponseRule[] = [];

  constructor() {
    this.initializeResponseRules();
  }

  private initializeResponseRules() {
    this.responseRules = [
      {
        condition: (event: SecurityEvent) =>
          event.type === 'authentication_failure' &&
          event.metadata.attemptCount > 5,
        action: this.lockAccount.bind(this),
        priority: 'high'
      },
      {
        condition: (event: SecurityEvent) =>
          event.type === 'suspicious_ip' &&
          event.severity === 'critical',
        action: this.blockIP.bind(this),
        priority: 'critical'
      },
      {
        condition: (event: SecurityEvent) =>
          event.type === 'data_exfiltration_attempt',
        action: this.alertSecurityTeam.bind(this),
        priority: 'critical'
      }
    ];
  }

  async processSecurityEvent(event: SecurityEvent) {
    this.securityEvents.push(event);

    // Check response rules
    for (const rule of this.responseRules) {
      if (rule.condition(event)) {
        await rule.action(event);
      }
    }

    // Log the event
    await this.logSecurityEvent(event);
  }

  private async lockAccount(event: SecurityEvent) {
    const userId = event.metadata.userId;
    await fetch(`/api/users/${userId}/lock`, {
      method: 'POST',
      headers: { 'Authorization': 'Bearer ' + adminToken }
    });

    console.log(`Account locked for user ${userId}`);
  }

  private async blockIP(event: SecurityEvent) {
    const ipAddress = event.ipAddress;
    await fetch('/api/security/block-ip', {
      method: 'POST',
      headers: { 'Authorization': 'Bearer ' + adminToken },
      body: JSON.stringify({ ipAddress })
    });

    console.log(`IP address blocked: ${ipAddress}`);
  }

  private async alertSecurityTeam(event: SecurityEvent) {
    await fetch('/api/security/alert', {
      method: 'POST',
      headers: { 'Authorization': 'Bearer ' + adminToken },
      body: JSON.stringify({
        message: `Critical security event: ${event.description}`,
        severity: event.severity,
        eventId: event.id
      })
    });

    console.log('Security team alerted');
  }

  private async logSecurityEvent(event: SecurityEvent) {
    await fetch('/api/security/events', {
      method: 'POST',
      headers: { 'Authorization': 'Bearer ' + adminToken },
      body: JSON.stringify(event)
    });
  }
}
```

---

## Module 5: Compliance and Best Practices

### 5.1 GDPR Compliance

#### Data Protection Principles
```typescript
// Exercise: Implement GDPR-compliant data handling
class GDPRComplianceManager {
  async handleDataSubjectRequest(request: DataSubjectRequest) {
    switch (request.type) {
      case 'access':
        return await this.handleAccessRequest(request.userId);
      case 'rectification':
        return await this.handleRectificationRequest(request);
      case 'erasure':
        return await this.handleErasureRequest(request.userId);
      case 'portability':
        return await this.handlePortabilityRequest(request.userId);
      default:
        throw new Error('Invalid request type');
    }
  }

  private async handleAccessRequest(userId: string) {
    // Gather all user data
    const userData = await this.collectUserData(userId);

    // Create access report
    const report = {
      userId,
      requestDate: new Date(),
      dataCollected: userData,
      retentionPeriods: this.getRetentionPeriods(),
      processingPurposes: this.getProcessingPurposes()
    };

    // Log the access request
    await this.logDataAccess(userId, 'data_access_request');

    return report;
  }

  private async handleErasureRequest(userId: string) {
    // Verify right to erasure
    const hasRightToErasure = await this.verifyErasureRights(userId);

    if (!hasRightToErasure) {
      throw new Error('No right to erasure');
    }

    // Anonymize user data
    await this.anonymizeUserData(userId);

    // Log the erasure
    await this.logDataErasure(userId);

    return { success: true, erasedAt: new Date() };
  }

  private async collectUserData(userId: string) {
    // Collect data from all sources
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        createdAt: true,
        // Exclude sensitive data like password hashes
      }
    });

    const auditLogs = await prisma.auditLog.findMany({
      where: { userId },
      select: {
        id: true,
        action: true,
        resource: true,
        timestamp: true,
        ipAddress: true
      }
    });

    return { user, auditLogs };
  }
}
```

### 5.2 Security Testing

#### Penetration Testing Exercise
```typescript
// Exercise: Write security tests
describe('Security Tests', () => {
  describe('Authentication Security', () => {
    it('should prevent SQL injection in login', async () => {
      const maliciousInput = {
        email: "' OR '1'='1",
        password: "' OR '1'='1"
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(maliciousInput);

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    it('should rate limit failed login attempts', async () => {
      // Attempt multiple failed logins
      for (let i = 0; i < 6; i++) {
        await request(app)
          .post('/api/auth/login')
          .send({ email: '<EMAIL>', password: 'wrong' });
      }

      const response = await request(app)
        .post('/api/auth/login')
        .send({ email: '<EMAIL>', password: 'wrong' });

      expect(response.status).toBe(429);
      expect(response.body.error).toBe('Too Many Requests');
    });
  });

  describe('Authorization Security', () => {
    it('should deny access without proper permissions', async () => {
      const response = await request(app)
        .get('/api/admin/users')
        .set('Authorization', 'Bearer user-token'); // User without admin role

      expect(response.status).toBe(403);
      expect(response.body.error).toBe('Forbidden');
    });

    it('should allow access with proper permissions', async () => {
      const response = await request(app)
        .get('/api/admin/users')
        .set('Authorization', 'Bearer admin-token');

      expect(response.status).toBe(200);
    });
  });

  describe('Input Validation Security', () => {
    it('should reject XSS attempts', async () => {
      const xssInput = {
        name: '<script>alert("XSS")</script>',
        email: '<EMAIL>'
      };

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', 'Bearer admin-token')
        .send(xssInput);

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Validation Error');
    });

    it('should sanitize HTML input', async () => {
      const htmlInput = {
        description: '<p><strong>Bold text</strong></p><script>alert("XSS")</script>'
      };

      const response = await request(app)
        .post('/api/content')
        .set('Authorization', 'Bearer user-token')
        .send(htmlInput);

      expect(response.status).toBe(200);
      expect(response.body.description).toBe('<p><strong>Bold text</strong></p>');
    });
  });
});
```

---

## Module 6: Deployment and Maintenance

### 6.1 Secure Deployment Practices

#### Environment Configuration
```bash
# Exercise: Set up secure environment variables
# .env.local
NEXTAUTH_SECRET=$(openssl rand -base64 32)
NEXTAUTH_URL=https://yourdomain.com
DATABASE_URL=postgresql://user:secure_password@localhost:5432/nwa_prod
REDIS_URL=redis://localhost:6379
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Security settings
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
AUDIT_LOG_RETENTION_DAYS=90
SESSION_MAX_AGE=86400

# Email settings (for alerts)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=secure_app_password

# Monitoring
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

#### Production Security Checklist
```typescript
// Exercise: Implement production security checks
class ProductionSecurityValidator {
  async validateProductionEnvironment() {
    const checks = [
      this.checkEnvironmentVariables(),
      this.checkDatabaseSecurity(),
      this.checkNetworkSecurity(),
      this.checkFilePermissions(),
      this.checkSSLConfiguration(),
      this.checkMonitoringSetup()
    ];

    const results = await Promise.all(checks);

    const failures = results.filter(result => !result.success);

    if (failures.length > 0) {
      throw new Error(`Security validation failed: ${failures.map(f => f.message).join(', ')}`);
    }

    return { success: true, message: 'All security checks passed' };
  }

  private async checkEnvironmentVariables() {
    const requiredVars = [
      'NEXTAUTH_SECRET',
      'DATABASE_URL',
      'REDIS_URL',
      'ALLOWED_ORIGINS'
    ];

    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        return { success: false, message: `Missing required environment variable: ${varName}` };
      }
    }

    return { success: true, message: 'Environment variables configured correctly' };
  }

  private async checkDatabaseSecurity() {
    // Check database connection security
    try {
      await prisma.$queryRaw`SELECT 1`;
      return { success: true, message: 'Database connection secure' };
    } catch (error) {
      return { success: false, message: 'Database security check failed' };
    }
  }

  private async checkSSLConfiguration() {
    // Verify SSL certificate validity
    const httpsResponse = await fetch('https://yourdomain.com/api/health');
    if (!httpsResponse.ok) {
      return { success: false, message: 'SSL configuration invalid' };
    }
    return { success: true, message: 'SSL configuration valid' };
  }
}
```

### 6.2 Maintenance Procedures

#### Regular Security Tasks
```typescript
// Exercise: Implement security maintenance tasks
class SecurityMaintenanceManager {
  async performWeeklyMaintenance() {
    const tasks = [
      this.rotateEncryptionKeys(),
      this.cleanupExpiredSessions(),
      this.updateSecurityPatches(),
      this.analyzeAuditLogs(),
      this.checkSystemHealth()
    ];

    const results = await Promise.all(tasks.map(task => task.catch(error => ({
      success: false,
      error: error.message
    }))));

    return this.generateMaintenanceReport(results);
  }

  async rotateEncryptionKeys() {
    // Rotate JWT signing keys
    const newSecret = crypto.randomBytes(32).toString('hex');
    process.env.NEXTAUTH_SECRET = newSecret;

    // Update database with new key version
    await prisma.systemConfig.update({
      where: { key: 'jwt_secret_version' },
      data: { value: Date.now().toString() }
    });

    return { success: true, message: 'Encryption keys rotated' };
  }

  async cleanupExpiredSessions() {
    // Remove expired sessions
    const expiredSessions = await prisma.session.deleteMany({
      where: {
        expires: { lt: new Date() }
      }
    });

    return {
      success: true,
      message: `Cleaned up ${expiredSessions.count} expired sessions`
    };
  }

  async updateSecurityPatches() {
    // Check for security updates
    const updateCheck = await fetch('/api/admin/security-updates');
    const updates = await updateCheck.json();

    if (updates.critical > 0) {
      // Apply critical security patches
      await this.applySecurityPatches(updates.criticalPatches);
    }

    return {
      success: true,
      message: `Applied ${updates.applied} security patches`
    };
  }
}
```

---

## Assessment and Certification

### Knowledge Assessment

#### Multiple Choice Questions

1. **What is the primary purpose of the `requireAuth` middleware?**
   - a) Rate limiting requests
   - b) Authenticating and authorizing users
   - c) Logging API requests
   - d) Validating input data

2. **Which permission model does NWA Alliance use?**
   - a) Attribute-based access control (ABAC)
   - b) Role-based access control (RBAC)
   - c) Discretionary access control (DAC)
   - d) Mandatory access control (MAC)

3. **What should you do when implementing input validation?**
   - a) Validate only on the client side
   - b) Validate only on the server side
   - c) Validate on both client and server side
   - d) Skip validation for performance

#### Hands-on Exercises

1. **Implement a secure API endpoint** with authentication, authorization, rate limiting, and input validation
2. **Create a permission-aware React component** that shows/hides content based on user permissions
3. **Set up audit logging** for a sensitive operation
4. **Configure 2FA** for user accounts
5. **Implement GDPR compliance** for data subject requests

### Certification Requirements

To receive NWA Alliance Security Certification, you must:

1. **Complete all training modules** with 80%+ score on assessments
2. **Pass hands-on exercises** demonstrating practical security implementation
3. **Contribute to security improvements** through code reviews or documentation
4. **Maintain security awareness** through regular training updates

### Continuing Education

- **Monthly Security Updates**: Review latest security patches and vulnerabilities
- **Quarterly Training**: Refresher courses on security best practices
- **Annual Certification**: Renew security certification through assessment
- **Security Community**: Participate in security discussions and knowledge sharing

---

## Resources and Support

### Documentation
- [Security Implementation Guide](SECURITY_IMPLEMENTATION.md)
- [Permission System Architecture](PERMISSION_SYSTEM_ARCHITECTURE.md)
- [Security Best Practices](SECURITY_BEST_PRACTICES.md)
- [Security API Reference](SECURITY_API_REFERENCE.md)

### Tools and Libraries
- **NextAuth.js**: Authentication and session management
- **Zod**: Schema validation and type safety
- **Prisma**: Database ORM with security features
- **React Hook Form**: Secure form handling

### Support Channels
- **Security Training**: <EMAIL>
- **Technical Support**: <EMAIL>
- **Security Issues**: <EMAIL>
- **Documentation**: <EMAIL>

### Emergency Contacts
- **Security Operations Center**: +1-555-SECURE
- **Incident Response Team**: <EMAIL>
- **Executive Escalation**: <EMAIL>

---

## Conclusion

This comprehensive security training guide provides the knowledge and skills necessary to develop, maintain, and secure NWA Alliance applications. By following these guidelines and best practices, you contribute to maintaining the highest security standards and protecting user data.

**Training Status**: ✅ COMPLETE AND VALIDATED
**Last Updated**: 2025-09-24
**Version**: 1.0.0