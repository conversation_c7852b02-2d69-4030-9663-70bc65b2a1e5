# Address Field Migration Guide

This guide provides step-by-step instructions for migrating from legacy single address fields to structured address fields in the NWA Alliance application.

## Overview

The NWA Alliance application has been updated to support structured address fields with country-specific formatting, regions, and improved data validation. This migration transforms single `address` fields into structured components for better data quality and user experience.

## Changes Summary

### Before (Legacy System)
- Single `address` text field
- Basic validation
- No country-specific formatting
- Limited regional support

### After (Structured System)
- Multiple address components:
  - `streetAddress1` (required)
  - `streetAddress2` (optional)
  - `town` (required)
  - `postalCode` (validated per country)
  - `regionId` (selected from database)
  - `regionText` (manual entry when regions unavailable)
  - `country` (required)
  - `city` (optional association)
- Country-specific field labels and validation
- Dynamic region selection
- Formatted address display
- Full backward compatibility

## Migration Checklist

### Phase 1: Database Updates ✅
- [x] Add new address fields to UserProfile table
- [x] Create CountryAddressFormat table
- [x] Create Region table with country associations
- [x] Add database indexes for performance
- [x] Keep legacy `address` field for backward compatibility

### Phase 2: API Updates ✅
- [x] Update user creation endpoints
- [x] Update user update endpoints
- [x] Create country address format API
- [x] Create regions API
- [x] Add input validation and sanitization
- [x] Implement backward compatibility parsing
- [x] Add comprehensive audit logging

### Phase 3: Component Updates ✅
- [x] Update CreateUserTab component
- [x] Update UpdateUserTab component
- [x] Update UserProfile component
- [x] Create custom hooks for region loading
- [x] Add performance optimizations
- [x] Implement comprehensive testing

### Phase 4: Infrastructure ✅
- [x] Add caching for address formats and regions
- [x] Implement performance monitoring
- [x] Create custom hooks for debouncing
- [x] Add lazy loading for region data
- [x] Update route parameter consistency

### Phase 5: Documentation ✅
- [x] Component documentation
- [x] API documentation
- [x] Migration guide
- [x] Performance optimization guide

## Component Migration

### CreateUserTab Component

#### Before
```tsx
<FormField
  control={form.control}
  name="address"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Address</FormLabel>
      <FormControl>
        <Textarea 
          placeholder="Enter full address"
          {...field}
        />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>
```

#### After
```tsx
{/* Street Address 1 - Primary address line */}
<FormField
  control={form.control}
  name="streetAddress1"
  render={({ field }) => (
    <FormItem>
      <FormLabel>{addressFormat?.streetAddress1Label || 'Street Address'}</FormLabel>
      <FormControl>
        <Input 
          placeholder={`Enter ${addressFormat?.streetAddress1Label?.toLowerCase() || 'street address'}`}
          {...field}
        />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>

{/* Street Address 2 - Secondary address line */}
<FormField
  control={form.control}
  name="streetAddress2"
  render={({ field }) => (
    <FormItem>
      <FormLabel>{addressFormat?.streetAddress2Label || 'Street Address 2 (Optional)'}</FormLabel>
      <FormControl>
        <Input 
          placeholder={`Enter ${addressFormat?.streetAddress2Label?.toLowerCase() || 'apartment, suite, etc.'}`}
          {...field}
        />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>

{/* Town/City */}
<FormField
  control={form.control}
  name="town"
  render={({ field }) => (
    <FormItem>
      <FormLabel>
        {addressFormat?.townLabel || 'City'}
        {addressFormat?.townRequired && <span className="text-destructive ml-1">*</span>}
      </FormLabel>
      <FormControl>
        <Input 
          placeholder={`Enter ${addressFormat?.townLabel?.toLowerCase() || 'city'}`}
          {...field}
        />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>

{/* Region Selection */}
{regions.length > 0 ? (
  <FormField
    control={form.control}
    name="regionId"
    render={({ field }) => (
      <FormItem>
        <FormLabel>
          {addressFormat?.regionLabel || 'State/Province'}
          {addressFormat?.regionRequired && <span className="text-destructive ml-1">*</span>}
        </FormLabel>
        <Select onValueChange={field.onChange} value={field.value}>
          <FormControl>
            <SelectTrigger>
              <SelectValue placeholder={`Select ${addressFormat?.regionLabel?.toLowerCase() || 'state/province'}`} />
            </SelectTrigger>
          </FormControl>
          <SelectContent>
            {regions.map((region) => (
              <SelectItem key={region.id} value={region.id.toString()}>
                {region.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <FormMessage />
      </FormItem>
    )}
  />
) : (
  <FormField
    control={form.control}
    name="regionText"
    render={({ field }) => (
      <FormItem>
        <FormLabel>
          {addressFormat?.regionLabel || 'State/Province'}
          {addressFormat?.regionRequired && <span className="text-destructive ml-1">*</span>}
        </FormLabel>
        <FormControl>
          <Input 
            placeholder={`Enter ${addressFormat?.regionLabel?.toLowerCase() || 'state/province'}`}
            {...field}
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
)}

{/* Postal Code */}
<FormField
  control={form.control}
  name="postalCode"
  render={({ field }) => (
    <FormItem>
      <FormLabel>
        {addressFormat?.postalCodeLabel || 'Postal Code'}
        {addressFormat?.postalCodeRequired && <span className="text-destructive ml-1">*</span>}
      </FormLabel>
      <FormControl>
        <Input 
          placeholder={`Enter ${addressFormat?.postalCodeLabel?.toLowerCase() || 'postal code'}`}
          {...field}
        />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>
```

### UserProfile Component

#### Before
```tsx
<div className="space-y-2">
  <h4 className="font-medium">Address</h4>
  <p className="text-sm text-muted-foreground">
    {user.address || 'No address provided'}
  </p>
</div>
```

#### After
```tsx
<div className="space-y-2">
  <h4 className="font-medium">Address</h4>
  {formattedAddress ? (
    <div className="text-sm text-muted-foreground whitespace-pre-line">
      {formattedAddress}
    </div>
  ) : (
    <p className="text-sm text-muted-foreground">No address provided</p>
  )}
</div>
```

## API Migration

### User Creation Endpoint

#### Before
```javascript
// POST /api/users
{
  "firstName": "John",
  "lastName": "Doe", 
  "email": "<EMAIL>",
  "address": "123 Main St, Springfield, IL 62701"
}
```

#### After
```javascript
// POST /api/users - Structured approach
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>", 
  "streetAddress1": "123 Main Street",
  "streetAddress2": "Apt 4B",
  "town": "Springfield",
  "postalCode": "62701",
  "regionId": 12,
  "country": "US"
}

// POST /api/users - Legacy compatibility (still supported)
{
  "firstName": "John", 
  "lastName": "Doe",
  "email": "<EMAIL>",
  "address": "123 Main St, Springfield, IL 62701",
  "country": "US"
}
```

### New API Endpoints

#### Country Address Formats
```javascript
// GET /api/countries/1/address-format
{
  "addressFormat": {
    "id": 1,
    "countryId": 1,
    "postalCodeLabel": "ZIP Code",
    "postalCodeFormat": "^\\d{5}(-\\d{4})?$",
    "postalCodeRequired": true,
    "regionLabel": "State",
    "regionRequired": true,
    "townLabel": "City", 
    "townRequired": true,
    "addressTemplate": "{street}\\n{town}, {region} {postalCode}\\n{country}"
  }
}
```

#### Regions
```javascript
// GET /api/regions?countryId=1
{
  "regions": [
    {
      "id": 1,
      "name": "California",
      "code": "CA",
      "countryId": 1,
      "isActive": true
    },
    {
      "id": 2,
      "name": "Texas",
      "code": "TX",
      "countryId": 1,
      "isActive": true
    }
  ]
}
```

## Database Migration

### Schema Changes

#### New Tables
```sql
-- Country address formats
CREATE TABLE CountryAddressFormat (
    id SERIAL PRIMARY KEY,
    countryId INTEGER NOT NULL REFERENCES Country(id),
    postalCodeLabel VARCHAR(50) DEFAULT 'Postal Code',
    postalCodeFormat TEXT DEFAULT '^.+$',
    postalCodeRequired BOOLEAN DEFAULT false,
    regionLabel VARCHAR(50) DEFAULT 'Region',
    regionRequired BOOLEAN DEFAULT false,
    townLabel VARCHAR(50) DEFAULT 'City',
    townRequired BOOLEAN DEFAULT true,
    addressTemplate TEXT DEFAULT '{street}\n{town}\n{country}',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(countryId)
);

-- Regions/States
CREATE TABLE Region (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10),
    countryId INTEGER NOT NULL REFERENCES Country(id),
    isActive BOOLEAN DEFAULT true,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(countryId, code),
    INDEX idx_region_country (countryId),
    INDEX idx_region_active (isActive)
);
```

#### Updated UserProfile Table
```sql
ALTER TABLE UserProfile ADD COLUMN streetAddress1 VARCHAR(255);
ALTER TABLE UserProfile ADD COLUMN streetAddress2 VARCHAR(255);
ALTER TABLE UserProfile ADD COLUMN town VARCHAR(100);
ALTER TABLE UserProfile ADD COLUMN postalCode VARCHAR(20);
ALTER TABLE UserProfile ADD COLUMN regionId INTEGER REFERENCES Region(id);
ALTER TABLE UserProfile ADD COLUMN regionText VARCHAR(100);

-- Keep existing streetAddress for backward compatibility
-- ALTER TABLE UserProfile ADD COLUMN streetAddress TEXT; -- Already exists

-- Add indexes
CREATE INDEX idx_userprofile_region ON UserProfile(regionId);
CREATE INDEX idx_userprofile_country ON UserProfile(countryId);
```

### Data Migration Script

```sql
-- Example data migration (run after adding new columns)
-- This would be customized based on existing address data patterns

-- Sample US states
INSERT INTO Region (name, code, countryId, isActive) VALUES 
    ('California', 'CA', 1, true),
    ('Texas', 'TX', 1, true),
    ('New York', 'NY', 1, true),
    ('Illinois', 'IL', 1, true)
ON CONFLICT (countryId, code) DO NOTHING;

-- Sample address formats
INSERT INTO CountryAddressFormat (
    countryId, 
    postalCodeLabel, 
    postalCodeFormat, 
    postalCodeRequired,
    regionLabel,
    regionRequired,
    townLabel,
    townRequired,
    addressTemplate
) VALUES (
    1, -- US
    'ZIP Code',
    '^\\d{5}(-\\d{4})?$',
    true,
    'State',
    true, 
    'City',
    true,
    '{street}\n{town}, {region} {postalCode}\n{country}'
) ON CONFLICT (countryId) DO NOTHING;
```

## Testing Migration

### Unit Tests
```javascript
describe('Address Field Migration', () => {
  test('should handle legacy address parsing', async () => {
    const response = await request(app)
      .post('/api/users')
      .send({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        address: '123 Main St, Springfield, IL 62701',
        country: 'US'
      });
      
    expect(response.status).toBe(201);
    expect(response.body.user.streetAddress1).toBe('123 Main St');
    expect(response.body.user.town).toBe('Springfield');
  });

  test('should handle structured address input', async () => {
    const response = await request(app)
      .post('/api/users')
      .send({
        firstName: 'John',
        lastName: 'Doe', 
        email: '<EMAIL>',
        streetAddress1: '123 Main Street',
        town: 'Springfield',
        postalCode: '62701',
        regionId: '12',
        country: 'US'
      });
      
    expect(response.status).toBe(201);
    expect(response.body.user.streetAddress1).toBe('123 Main Street');
  });
});
```

### Integration Tests
```javascript
describe('Address Components Integration', () => {
  test('should load country address format and regions', async () => {
    render(<CreateUserTab />);
    
    // Select a country
    const countrySelect = screen.getByLabelText(/country/i);
    fireEvent.change(countrySelect, { target: { value: 'US' } });
    
    // Wait for address format to load
    await waitFor(() => {
      expect(screen.getByLabelText(/zip code/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/state/i)).toBeInTheDocument();
    });
  });
});
```

## Performance Considerations

### Caching Implementation
The migration includes caching strategies to improve performance:

```javascript
// Address formats cached for 5 minutes
const addressFormatCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000;

// Regions cached per country
const regionsCache = new Map(); 
```

### Lazy Loading
Regions are only loaded when a country is selected:

```javascript
const { regions, loading } = useRegions({ 
  countryId, 
  enabled: !!countryId 
});
```

### Debouncing
Search inputs are debounced to prevent excessive API calls:

```javascript
const debouncedSearch = useDebouncedRegionSearch(searchTerm, 300);
```

## Rollback Strategy

If issues are encountered, the system supports graceful rollback:

1. **API Level**: Legacy address field parsing continues to work
2. **Component Level**: Forms can be temporarily reverted to single address field
3. **Database Level**: Original address column is preserved

### Emergency Rollback
```javascript
// Temporary component fallback
const USE_LEGACY_ADDRESS = process.env.USE_LEGACY_ADDRESS === 'true';

if (USE_LEGACY_ADDRESS) {
  return <LegacyAddressField />;
}
return <StructuredAddressFields />;
```

## Validation Updates

### Client-Side Validation
```javascript
const addressSchema = z.object({
  streetAddress1: z.string().min(1, 'Street address is required').max(255),
  streetAddress2: z.string().max(255).optional(),
  town: z.string().min(1, 'City is required').max(100),
  postalCode: z.string().regex(postalCodePattern, 'Invalid postal code format').optional(),
  regionId: z.string().optional(),
  regionText: z.string().max(100).optional(),
  country: z.string().min(2, 'Country is required'),
});
```

### Server-Side Validation
```javascript
// Country-specific postal code validation
const validatePostalCode = (code, countryId) => {
  const format = getCountryAddressFormat(countryId);
  if (format.postalCodeRequired && !code) {
    throw new Error('Postal code is required');
  }
  if (code && !new RegExp(format.postalCodeFormat).test(code)) {
    throw new Error('Invalid postal code format');
  }
};
```

## Monitoring and Alerts

### Performance Metrics
Monitor the following metrics post-migration:

- API response times for address endpoints
- Cache hit rates for address formats and regions
- Form completion rates
- Validation error rates

### Health Checks
```javascript
// Monitor address format cache health
const cacheHealthCheck = () => {
  const cacheSize = addressFormatCache.size;
  const hitRate = cacheHits / (cacheHits + cacheMisses);
  
  if (hitRate < 0.8) {
    console.warn('Low cache hit rate:', hitRate);
  }
};
```

## Support and Troubleshooting

### Common Issues

#### 1. Regions Not Loading
**Symptoms**: Region dropdown shows no options
**Solution**: Check country selection and API connectivity
```javascript
// Debug regions loading
console.log('Country ID:', countryId);
console.log('Regions loading:', loading);
console.log('Regions data:', regions);
```

#### 2. Postal Code Validation Errors
**Symptoms**: Valid postal codes rejected
**Solution**: Verify country address format configuration
```javascript
// Check address format
const format = await fetch(`/api/countries/${countryId}/address-format`);
console.log('Address format:', await format.json());
```

#### 3. Legacy Data Not Parsing
**Symptoms**: Old address data not displaying correctly
**Solution**: Review address parsing logic and add custom patterns

### Contact Support
For migration issues:
- Create GitHub issue with 'migration' label
- Include error logs and browser console output
- Provide sample data that's not parsing correctly

## Conclusion

This migration enhances the address handling system while maintaining full backward compatibility. The structured approach provides:

- Better data quality and validation
- Country-specific user experiences  
- Improved performance through caching
- Comprehensive testing and monitoring
- Flexible rollback options

The migration is designed to be safe, incremental, and reversible to ensure minimal disruption to the application.
