# Nation Treaty API Documentation

## Base URL
```
http://localhost:3001/api
```

## Authentication
All API endpoints require authentication via NextAuth.js session. Admin-level operations require `ADMIN` or `SUPER_ADMIN` role.

## Response Format
```json
{
  "success": boolean,
  "data": object|array,
  "message": string (optional),
  "error": string (error responses only),
  "pagination": object (paginated responses)
}
```

---

## Nation Treaty Endpoints

### GET /nation-treaties
Retrieve a paginated list of nation treaties with optional filtering.

**Query Parameters:**
- `page` (number, default: 1) - Page number for pagination
- `limit` (number, default: 10, max: 100) - Items per page
- `search` (string) - Search in name, officialName, and description
- `status` (string) - Filter by status: ACTIVE, INACTIVE, SUSPENDED, PENDING
- `contactEmail` (string) - Filter by contact email
- `country` (string) - Filter by office country
- `sortBy` (string) - Sort field: name, officialName, status, createdAt, updatedAt
- `sortOrder` (string) - Sort direction: asc, desc

**Example Request:**
```bash
GET /nation-treaties?page=1&limit=10&status=ACTIVE&sortBy=name&sortOrder=asc
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "clx1y2z3a4b5c",
      "name": "Test Nation Treaty",
      "officialName": "Official Test Nation Treaty",
      "status": "ACTIVE",
      "description": "Test treaty description",
      "contactEmail": "<EMAIL>",
      "contactPhone": "+1234567890",
      "contactAddress": "123 Diplomat St",
      "website": "https://example.com",
      "emergencyContactName": "Jane Doe",
      "emergencyContactPhone": "+0987654321",
      "emergencyContactEmail": "<EMAIL>",
      "notes": "Important notes about the treaty",
      "metadata": {},
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 10,
    "totalPages": 1
  }
}
```

### POST /nation-treaties
Create a new nation treaty. Requires admin privileges.

**Request Body:**
```json
{
  "name": "string (required, 2-100 chars)",
  "officialName": "string (required, 2-200 chars)",
  "status": "ACTIVE|INACTIVE|SUSPENDED|PENDING (default: ACTIVE)",
  "description": "string (optional, max 1000 chars)",
  "contactEmail": "string (email format, optional)",
  "contactPhone": "string (phone format, optional)",
  "contactAddress": "string (optional, max 500 chars)",
  "website": "string (URL format, optional)",
  "emergencyContactName": "string (required if emergencyContactPhone provided)",
  "emergencyContactPhone": "string (phone format, required if emergencyContactName provided)",
  "emergencyContactEmail": "string (email format, optional)",
  "notes": "string (optional, max 2000 chars)",
  "metadata": "object (optional)"
}
```

**Example Request:**
```bash
POST /nation-treaties
Content-Type: application/json

{
  "name": "United Nations Treaty",
  "officialName": "Treaty Between United Nations and Member States",
  "status": "ACTIVE",
  "description": "Comprehensive cooperation agreement",
  "contactEmail": "<EMAIL>",
  "contactPhone": "+12125551234",
  "contactAddress": "United Nations Headquarters, NY 10017",
  "website": "https://un.org/treaty",
  "emergencyContactName": "John Smith",
  "emergencyContactPhone": "+12125555678"
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "id": "clx1y2z3a4b5c",
    "name": "United Nations Treaty",
    "officialName": "Treaty Between United Nations and Member States",
    "status": "ACTIVE",
    "description": "Comprehensive cooperation agreement",
    "contactEmail": "<EMAIL>",
    "contactPhone": "+12125551234",
    "contactAddress": "United Nations Headquarters, NY 10017",
    "website": "https://un.org/treaty",
    "emergencyContactName": "John Smith",
    "emergencyContactPhone": "+12125555678",
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z"
  },
  "message": "Nation treaty created successfully"
}
```

### PUT /nation-treaties
Update an existing nation treaty. Requires admin privileges.

**Request Body:**
```json
{
  "id": "string (required, CUID format)",
  "name": "string (2-100 chars, optional)",
  "officialName": "string (2-200 chars, optional)",
  "status": "ACTIVE|INACTIVE|SUSPENDED|PENDING (optional)",
  "description": "string (max 1000 chars, optional)",
  "contactEmail": "string (email format, optional)",
  "contactPhone": "string (phone format, optional)",
  "contactAddress": "string (max 500 chars, optional)",
  "website": "string (URL format, optional)",
  "emergencyContactName": "string (optional)",
  "emergencyContactPhone": "string (phone format, optional)",
  "emergencyContactEmail": "string (email format, optional)",
  "notes": "string (max 2000 chars, optional)",
  "metadata": "object (optional)"
}
```

**Example Request:**
```bash
PUT /nation-treaties
Content-Type: application/json

{
  "id": "clx1y2z3a4b5c",
  "status": "SUSPENDED",
  "notes": "Treaty suspended pending review"
}
```

### DELETE /nation-treaties
Soft delete a nation treaty. Requires admin privileges.

**Query Parameters:**
- `id` (string, required) - Nation treaty ID to delete

**Example Request:**
```bash
DELETE /nation-treaties?id=clx1y2z3a4b5c
```

---

## Member Management Endpoints

### GET /nation-treaties/[id]/members
Retrieve members of a specific nation treaty.

**Path Parameters:**
- `id` (string) - Nation treaty ID

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 10, max: 100) - Items per page
- `search` (string) - Search in user name and email
- `status` (string) - Filter by status: ACTIVE, INACTIVE, SUSPENDED
- `sortBy` (string) - Sort field: joinedAt, status, createdAt
- `sortOrder` (string) - Sort direction: asc, desc

**Example Request:**
```bash
GET /nation-treaties/clx1y2z3a4b5c/members?status=ACTIVE
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "clx6y7z8a9b0c",
      "userId": "user123",
      "nationTreatyId": "clx1y2z3a4b5c",
      "role": "ADMIN",
      "status": "ACTIVE",
      "joinedAt": "2024-01-01T00:00:00Z",
      "notes": "Primary administrator",
      "user": {
        "id": "user123",
        "name": "John Doe",
        "email": "<EMAIL>",
        "nwaEmail": "<EMAIL>"
      }
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 10,
    "totalPages": 1
  }
}
```

### POST /nation-treaties/[id]/members
Add a user as a member of a nation treaty. Requires admin privileges.

**Path Parameters:**
- `id` (string) - Nation treaty ID

**Request Body:**
```json
{
  "userId": "string (required, CUID format)",
  "role": "MEMBER|ENVOY|ADMIN (default: MEMBER)",
  "status": "ACTIVE|INACTIVE|SUSPENDED (default: ACTIVE)",
  "notes": "string (optional, max 500 chars)"
}
```

**Example Request:**
```bash
POST /nation-treaties/clx1y2z3a4b5c/members
Content-Type: application/json

{
  "userId": "user456",
  "role": "ENVOY",
  "status": "ACTIVE",
  "notes": "Appointed as envoy representative"
}
```

---

## Envoy Management Endpoints

### GET /nation-treaties/[id]/envoys
Retrieve envoys appointed to a nation treaty.

**Path Parameters:**
- `id` (string) - Nation treaty ID

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 10, max: 100) - Items per page
- `search` (string) - Search in user name and envoy title
- `status` (string) - Filter by status: ACTIVE, INACTIVE, SUSPENDED
- `title` (string) - Filter by envoy title
- `sortBy` (string) - Sort field: appointedAt, title, status, createdAt
- `sortOrder` (string) - Sort direction: asc, desc

**Example Request:**
```bash
GET /nation-treaties/clx1y2z3a4b5c/envoys?status=ACTIVE
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "clx9y0z1a2b3c",
      "userId": "user789",
      "nationTreatyId": "clx1y2z3a4b5c",
      "envoyType": "PRIMARY",
      "title": "Ambassador",
      "status": "ACTIVE",
      "appointedAt": "2024-01-01T00:00:00Z",
      "notes": "Primary ambassador to the treaty",
      "user": {
        "id": "user789",
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "nwaEmail": "<EMAIL>"
      }
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 10,
    "totalPages": 1
  }
}
```

### POST /nation-treaties/[id]/envoys
Appoint a user as an envoy to a nation treaty. Requires admin privileges.

**Path Parameters:**
- `id` (string) - Nation treaty ID

**Request Body:**
```json
{
  "userId": "string (required, CUID format)",
  "envoyType": "PRIMARY|EMERGENCY|LEGAL (default: PRIMARY)",
  "title": "string (optional, max 50 chars)",
  "status": "ACTIVE|INACTIVE|SUSPENDED (default: ACTIVE)",
  "notes": "string (optional, max 500 chars)"
}
```

**Example Request:**
```bash
POST /nation-treaties/clx1y2z3a4b5c/envoys
Content-Type: application/json

{
  "userId": "user789",
  "envoyType": "PRIMARY",
  "title": "Ambassador",
  "status": "ACTIVE",
  "notes": "Appointed as primary ambassador"
}
```

---

## Office Management Endpoints

### GET /nation-treaties/[id]/offices
Retrieve offices associated with a nation treaty.

**Path Parameters:**
- `id` (string) - Nation treaty ID

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 10, max: 100) - Items per page
- `search` (string) - Search in address fields
- `status` (string) - Filter by status: ACTIVE, INACTIVE, UNDER_CONSTRUCTION, PLANNED
- `officeType` (string) - Filter by office type
- `country` (string) - Filter by country
- `city` (string) - Filter by city
- `sortBy` (string) - Sort field: city, officeType, status, country, createdAt, updatedAt
- `sortOrder` (string) - Sort direction: asc, desc

**Example Request:**
```bash
GET /nation-treaties/clx1y2z3a4b5c/offices?status=ACTIVE&country=USA
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "clx2y3z4a5b6c",
      "nationTreatyId": "clx1y2z3a4b5c",
      "officeType": "EMBASSY",
      "status": "ACTIVE",
      "streetAddress": "1600 Pennsylvania Avenue NW",
      "city": "Washington",
      "region": "DC",
      "country": "United States",
      "postalCode": "20500",
      "phone": "+12024561111",
      "email": "<EMAIL>",
      "website": "https://embassy.treaty.org",
      "isPrimary": true,
      "notes": "Primary embassy location",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 10,
    "totalPages": 1
  }
}
```

### POST /nation-treaties/[id]/offices
Create a new office for a nation treaty. Requires admin privileges.

**Path Parameters:**
- `id` (string) - Nation treaty ID

**Request Body:**
```json
{
  "officeType": "ENVOY_OFFICE|CONSULATE_GENERAL|EMBASSY|TRADE_OFFICE|CULTURAL_CENTER (default: ENVOY_OFFICE)",
  "status": "ACTIVE|INACTIVE|UNDER_CONSTRUCTION|PLANNED (default: PLANNED)",
  "streetAddress": "string (optional, max 500 chars)",
  "city": "string (optional, max 100 chars)",
  "region": "string (optional, max 100 chars)",
  "country": "string (optional, max 100 chars)",
  "postalCode": "string (optional, max 20 chars)",
  "phone": "string (phone format, optional)",
  "email": "string (email format, optional)",
  "website": "string (URL format, optional)",
  "isPrimary": "boolean (default: false)",
  "notes": "string (optional, max 2000 chars)",
  "metadata": "object (optional)"
}
```

**Example Request:**
```bash
POST /nation-treaties/clx1y2z3a4b5c/offices
Content-Type: application/json

{
  "officeType": "EMBASSY",
  "status": "ACTIVE",
  "streetAddress": "1600 Pennsylvania Avenue NW",
  "city": "Washington",
  "region": "DC",
  "country": "United States",
  "postalCode": "20500",
  "phone": "+12024561111",
  "email": "<EMAIL>",
  "website": "https://embassy.treaty.org",
  "isPrimary": true,
  "notes": "Primary embassy location"
}
```

### PUT /nation-treaties/[id]/offices
Update an existing office. Requires admin privileges.

**Path Parameters:**
- `id` (string) - Nation treaty ID

**Request Body:**
```json
{
  "id": "string (required, CUID format)",
  "officeType": "ENVOY_OFFICE|CONSULATE_GENERAL|EMBASSY|TRADE_OFFICE|CULTURAL_CENTER (optional)",
  "status": "ACTIVE|INACTIVE|UNDER_CONSTRUCTION|PLANNED (optional)",
  "streetAddress": "string (max 500 chars, optional)",
  "city": "string (max 100 chars, optional)",
  "region": "string (max 100 chars, optional)",
  "country": "string (max 100 chars, optional)",
  "postalCode": "string (max 20 chars, optional)",
  "phone": "string (phone format, optional)",
  "email": "string (email format, optional)",
  "website": "string (URL format, optional)",
  "isPrimary": "boolean (optional)",
  "notes": "string (max 2000 chars, optional)",
  "metadata": "object (optional)"
}
```

**Example Request:**
```bash
PUT /nation-treaties/clx1y2z3a4b5c/offices
Content-Type: application/json

{
  "id": "clx2y3z4a5b6c",
  "status": "ACTIVE",
  "phone": "+12024562222"
}
```

### DELETE /nation-treaties/[id]/offices
Soft delete an office. Requires admin privileges.

**Path Parameters:**
- `id` (string) - Nation treaty ID

**Query Parameters:**
- `id` (string, required) - Office ID to delete

**Example Request:**
```bash
DELETE /nation-treaties/clx1y2z3a4b5c/offices?id=clx2y3z4a5b6c
```

---

## Error Responses

### Validation Error (400)
```json
{
  "success": false,
  "error": "Validation error: Nation treaty name must be at least 2 characters"
}
```

### Unauthorized (401)
```json
{
  "success": false,
  "error": "Unauthorized"
}
```

### Forbidden (403)
```json
{
  "success": false,
  "error": "Forbidden"
}
```

### Not Found (404)
```json
{
  "success": false,
  "error": "Nation treaty not found"
}
```

### Conflict (409)
```json
{
  "success": false,
  "error": "Nation treaty with this name already exists"
}
```

### Server Error (500)
```json
{
  "success": false,
  "error": "Failed to create nation treaty"
}
```

---

## Data Validation

### Email Format
- Must match standard email regex: `^[^\s@]+@[^\s@]+\.[^\s@]+$`

### Phone Format
- International format with optional country code: `^[\+]?[1-9][\d]{0,15}$`

### Required Field Validation
- Treaty creation requires `name` and `officialName`
- Emergency contact requires both `emergencyContactName` and `emergencyContactPhone` if either is provided

### Status Validation
- All status fields use predefined enums
- Invalid status values are rejected

---

## Rate Limiting

All endpoints are subject to rate limiting to prevent abuse:
- 100 requests per minute per user
- 1000 requests per minute per IP address

Exceeded limits result in HTTP 429 Too Many Requests responses.

---

## Audit Logging

All operations are logged with:
- User ID performing the action
- Action type (create, update, delete)
- Resource type and ID
- Timestamp
- Changed data (for updates)

Audit logs are accessible through the administration interface.

---

## Pagination

All list endpoints support pagination with consistent response format:

```json
{
  "pagination": {
    "total": "total number of items",
    "page": "current page number",
    "limit": "items per page",
    "totalPages": "total number of pages"
  }
}
```

Use `page` and `limit` query parameters to control pagination.