# Nation Treaty System Developer Guide

## Overview

This guide provides technical information for developers working with the Nation Treaty System in the NWA Alliance platform. It covers architecture, code structure, development practices, and extension points.

## Technology Stack

- **Frontend**: Next.js 15 with React 18 and TypeScript 5
- **Backend**: Next.js API Routes with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **UI Components**: Radix UI with Tailwind CSS
- **Form Handling**: React Hook Form with Zod validation
- **Testing**: Jest with Testing Library
- **State Management**: React hooks and context

## Project Structure

### Core Files and Directories

```
src/
├── app/
│   ├── api/
│   │   ├── nation-treaties/
│   │   │   ├── route.ts                 # Main CRUD operations
│   │   │   ├── [id]/
│   │   │   │   ├── envoys/route.ts      # Envoy management
│   │   │   │   ├── members/route.ts     # Member management
│   │   │   │   └── offices/route.ts     # Office management
│   │   │   └── statistics/route.ts     # Treaty statistics
│   ├── treaties/
│   │   └── page.tsx                    # Main treaties page
│   └── trade-treaties/
│       └── page.tsx                    # Trade treaties page
├── components/
│   ├── treaties/
│   │   ├── NationTreatyManagement.tsx   # Main management component
│   │   ├── modals/
│   │   │   ├── NationTreatyModal.tsx   # Treaty creation/editing
│   │   │   └── NationTreatyOfficeModal.tsx # Office management
│   │   ├── VerticalMenu.tsx             # Navigation menu
│   │   ├── TreatiesManagementTab.tsx    # Trade treaty management
│   │   └── TreatyAuditTab.tsx          # Audit interface
│   └── users/
│       └── manage/
│           └── shared/
│               └── UserNationTribeForm.tsx # User assignment form
├── lib/
│   ├── services/
│   │   └── nation-treaty.ts            # Business logic service
│   ├── validation/
│   │   └── nation-treaty.ts            # Zod validation schemas
│   └── audit.ts                        # Audit logging
├── prisma/
│   └── schema.prisma                    # Database schema
└── types/                               # TypeScript type definitions
```

## Database Schema

### Core Models

```prisma
model NationTreaty {
  id                    String                    @id @default(cuid())
  name                  String                    @unique
  officialName          String
  status                NationTreatyStatus        @default(ACTIVE)
  description           String?
  contactEmail          String?
  contactPhone          String?
  contactAddress        String?
  website               String?
  emergencyContactName  String?
  emergencyContactPhone String?
  emergencyContactEmail String?
  notes                 String?
  metadata              Json?
  createdAt             DateTime                  @default(now())
  updatedAt             DateTime                  @updatedAt
  deletedAt             DateTime?
  
  // Relationships
  members               NationTreatyMember[]
  envoys                NationTreatyEnvoy[]
  offices               NationTreatyOffice[]
  users_nation          User[]                    @relation("UserNation")
  users_tribe           User[]                    @relation("UserTribe")
}

model NationTreatyMember {
  id             String                    @id @default(cuid())
  userId         String
  nationTreatyId String
  role           NationTreatyMemberRole   @default(MEMBER)
  status         NationTreatyMemberStatus @default(ACTIVE)
  joinedAt       DateTime                  @default(now())
  notes          String?
  
  // Relationships
  user           User                      @relation(fields: [userId], references: [id], onDelete: Cascade)
  nationTreaty   NationTreaty              @relation(fields: [nationTreatyId], references: [id], onDelete: Cascade)
  
  @@unique([userId, nationTreatyId])
}

model NationTreatyEnvoy {
  id             String                    @id @default(cuid())
  userId         String
  nationTreatyId String
  envoyType      NationTreatyEnvoyType    @default(PRIMARY)
  title          String?
  status         NationTreatyEnvoyStatus  @default(ACTIVE)
  appointedAt    DateTime                  @default(now())
  notes          String?
  
  // Relationships
  user           User                      @relation(fields: [userId], references: [id], onDelete: Cascade)
  nationTreaty   NationTreaty              @relation(fields: [nationTreatyId], references: [id], onDelete: Cascade)
  
  @@unique([userId, nationTreatyId])
}

model NationTreatyOffice {
  id                    String                    @id @default(cuid())
  nationTreatyId        String
  officeType            NationTreatyOfficeType   @default(ENVOY_OFFICE)
  status                NationTreatyOfficeStatus @default(PLANNED)
  streetAddress         String?
  city                  String?
  region                String?
  country               String?
  postalCode            String?
  phone                 String?
  email                 String?
  website               String?
  isPrimary             Boolean                   @default(false)
  notes                 String?
  metadata              Json?
  createdAt             DateTime                  @default(now())
  updatedAt             DateTime                  @updatedAt
  deletedAt             DateTime?
  
  // Relationships
  nationTreaty          NationTreaty              @relation(fields: [nationTreatyId], references: [id], onDelete: Cascade)
}
```

### Key Design Decisions

1. **Soft Deletes**: Use `deletedAt` timestamp instead of hard deletes
2. **Unique Constraints**: Prevent duplicate memberships and envoy appointments
3. **Cascading Deletes**: Automatically clean up related records
4. **JSON Fields**: Flexible metadata storage for additional attributes
5. **Status Enums**: Controlled vocabularies for consistent state management

## API Development

### Route Structure

All API routes follow RESTful conventions:

```
GET    /api/nation-treaties              # List treaties
POST   /api/nation-treaties              # Create treaty
PUT    /api/nation-treaties              # Update treaty
DELETE /api/nation-treaties              # Delete treaty

GET    /api/nation-treaties/[id]/members  # List members
POST   /api/nation-treaties/[id]/members  # Add member
PUT    /api/nation-treaties/[id]/members  # Update member
DELETE /api/nation-treaties/[id]/members  # Remove member

GET    /api/nation-treaties/[id]/envoys   # List envoys
POST   /api/nation-treaties/[id]/envoys   # Appoint envoy
DELETE /api/nation-treaties/[id]/envoys   # Remove envoy

GET    /api/nation-treaties/[id]/offices  # List offices
POST   /api/nation-treaties/[id]/offices  # Create office
PUT    /api/nation-treaties/[id]/offices  # Update office
DELETE /api/nation-treaties/[id]/offices  # Delete office
```

### Authentication Pattern

```typescript
// Standard authentication check
const session = await getServerSession(authOptions) as ExtendedSession | null;
if (!session?.user) {
  return NextResponse.json(
    { success: false, error: 'Unauthorized' },
    { status: 401 }
  );
}

// Role-based authorization
if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
  return NextResponse.json(
    { success: false, error: 'Forbidden' },
    { status: 403 }
  );
}
```

### Validation Pattern

```typescript
// Input validation using Zod schemas
const validatedData = validateNationTreatyCreate(body);

// Error handling for validation failures
try {
  const result = await someOperation(validatedData);
  return NextResponse.json({ success: true, data: result });
} catch (error) {
  if (error instanceof ValidationError) {
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 400 }
    );
  }
  // Handle other error types
}
```

### Service Layer Pattern

```typescript
// Business logic encapsulation in service classes
class NationTreatyService {
  constructor(private prisma: PrismaClient) {}

  async createNationTreaty(data: NationTreatyCreate, userId: string) {
    // Business logic, validation, and database operations
    return this.prisma.nationTreaty.create({
      data: {
        ...data,
        createdBy: userId,
      },
    });
  }

  async getNationTreaties(query: NationTreatyQuery) {
    // Complex query logic with filtering and pagination
    const { page, limit, search, status, sortBy, sortOrder } = query;
    
    const where: any = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { officialName: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    if (status) {
      where.status = status;
    }
    
    const [treaties, total] = await Promise.all([
      this.prisma.nationTreaty.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
      }),
      this.prisma.nationTreaty.count({ where }),
    ]);
    
    return { treaties, pagination: { total, page, limit, totalPages: Math.ceil(total / limit) } };
  }
}
```

## Frontend Development

### Component Architecture

#### Management Components

```typescript
// Main management component with tabbed interface
interface NationTreatyManagementProps {}

export const NationTreatyManagement: React.FC<NationTreatyManagementProps> = () => {
  const [activeTab, setActiveTab] = useState('treaties');
  const [selectedTreaty, setSelectedTreaty] = useState<NationTreaty | null>(null);
  
  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="treaties">Treaties</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="envoys">Envoys</TabsTrigger>
          <TabsTrigger value="offices">Offices</TabsTrigger>
        </TabsList>
        
        <TabsContent value="treaties">
          <TreatyList onSelect={setSelectedTreaty} />
        </TabsContent>
        
        <TabsContent value="members">
          {selectedTreaty && <MemberManagement treaty={selectedTreaty} />}
        </TabsContent>
        
        {/* Other tabs */}
      </Tabs>
    </div>
  );
};
```

#### Form Components

```typescript
// Modal form for treaty creation/editing
interface NationTreatyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: NationTreatyCreate) => void;
  treaty?: NationTreaty; // For editing
}

export const NationTreatyModal: React.FC<NationTreatyModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  treaty,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<NationTreatyCreate>({
    resolver: zodResolver(nationTreatyCreateSchema),
    defaultValues: treaty ? {
      name: treaty.name,
      officialName: treaty.officialName,
      status: treaty.status,
      description: treaty.description || '',
      // ... other fields
    } : {},
  });

  const onFormSubmit = (data: NationTreatyCreate) => {
    onSubmit(data);
    onClose();
    reset();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
        <FormInput
          label="Name"
          {...register('name')}
          error={errors.name?.message}
        />
        
        <FormInput
          label="Official Name"
          {...register('officialName')}
          error={errors.officialName?.message}
        />
        
        <FormSelect
          label="Status"
          {...register('status')}
          options={[
            { value: 'ACTIVE', label: 'Active' },
            { value: 'INACTIVE', label: 'Inactive' },
            { value: 'SUSPENDED', label: 'Suspended' },
            { value: 'PENDING', label: 'Pending' },
          ]}
        />
        
        {/* Other form fields */}
        
        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : treaty ? 'Update' : 'Create'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
```

#### Data Fetching Pattern

```typescript
// Custom hook for data fetching with caching
export const useNationTreaties = (query: NationTreatyQuery = defaultQuery) => {
  const [data, setData] = useState<{
    treaties: NationTreaty[];
    pagination: Pagination;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams({
        page: query.page.toString(),
        limit: query.limit.toString(),
        ...(query.search && { search: query.search }),
        ...(query.status && { status: query.status }),
        ...(query.sortBy && { sortBy: query.sortBy }),
        ...(query.sortOrder && { sortOrder: query.sortOrder }),
      });
      
      const response = await fetch(`/api/nation-treaties?${params}`);
      const result = await response.json();
      
      if (result.success) {
        setData(result);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('Failed to fetch nation treaties');
    } finally {
      setLoading(false);
    }
  }, [query]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
};
```

### State Management

The system uses React's built-in state management with hooks:

```typescript
// Context for shared state
interface NationTreatyContextType {
  treaties: NationTreaty[];
  selectedTreaty: NationTreaty | null;
  actions: {
    setTreaties: (treaties: NationTreaty[]) => void;
    setSelectedTreaty: (treaty: NationTreaty | null) => void;
    createTreaty: (treaty: NationTreatyCreate) => Promise<void>;
    updateTreaty: (id: string, treaty: NationTreatyUpdate) => Promise<void>;
    deleteTreaty: (id: string) => Promise<void>;
  };
}

const NationTreatyContext = createContext<NationTreatyContextType | undefined>(undefined);

export const NationTreatyProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [treaties, setTreaties] = useState<NationTreaty[]>([]);
  const [selectedTreaty, setSelectedTreaty] = useState<NationTreaty | null>(null);

  const createTreaty = useCallback(async (data: NationTreatyCreate) => {
    const response = await fetch('/api/nation-treaties', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    
    const result = await response.json();
    if (result.success) {
      setTreaties(prev => [...prev, result.data]);
      toast.success('Nation treaty created successfully');
    } else {
      toast.error(result.error);
    }
  }, []);

  // Other actions...

  return (
    <NationTreatyContext.Provider value={{
      treaties,
      selectedTreaty,
      actions: {
        setTreaties,
        setSelectedTreaty,
        createTreaty,
        // Other actions...
      },
    }}>
      {children}
    </NationTreatyContext.Provider>
  );
};
```

## Testing Strategy

### Unit Tests

```typescript
// Service testing
describe('NationTreatyService', () => {
  let service: NationTreatyService;
  let mockPrisma: any;

  beforeEach(() => {
    mockPrisma = {
      nationTreaty: {
        findMany: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        findUnique: jest.fn(),
        delete: jest.fn(),
        count: jest.fn(),
      },
    };
    service = new NationTreatyService(mockPrisma);
  });

  describe('createNationTreaty', () => {
    it('should create a new nation treaty', async () => {
      const data = {
        name: 'Test Treaty',
        officialName: 'Official Test Treaty',
        status: 'ACTIVE' as const,
      };

      const createdTreaty = { id: '1', ...data, createdAt: new Date(), updatedAt: new Date() };
      mockPrisma.nationTreaty.create.mockResolvedValue(createdTreaty);

      const result = await service.createNationTreaty(data, 'user-1');

      expect(result).toEqual(createdTreaty);
      expect(mockPrisma.nationTreaty.create).toHaveBeenCalledWith({
        data: { ...data, createdBy: 'user-1' },
      });
    });
  });
});
```

### Component Tests

```typescript
// Component testing with mocking
describe('NationTreatyManagement', () => {
  const mockTreaties = [
    {
      id: '1',
      name: 'Test Treaty',
      officialName: 'Official Test Treaty',
      status: 'ACTIVE',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ];

  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('should render nation treaties list', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: mockTreaties,
        pagination: { total: 1, page: 1, limit: 10 },
      }),
    });

    render(<NationTreatyManagement />);

    await waitFor(() => {
      expect(screen.getByText('Test Treaty')).toBeInTheDocument();
    });
  });
});
```

### Integration Tests

```typescript
// API endpoint testing
describe('Nation Treaty API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (getServerSession as jest.Mock).mockResolvedValue(mockSession);
  });

  describe('GET /api/nation-treaties', () => {
    it('should return nation treaties with pagination', async () => {
      const mockTreaties = [/* mock data */];
      prisma.nationTreaty.findMany.mockResolvedValue(mockTreaties);
      prisma.nationTreaty.count.mockResolvedValue(1);

      const request = new NextRequest('http://localhost:3000/api/nation-treaties');
      const response = await GET(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockTreaties);
    });
  });
});
```

## Validation and Error Handling

### Zod Schemas

```typescript
// Validation schema definitions
export const nationTreatyCreateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100),
  officialName: z.string().min(2, 'Official name must be at least 2 characters').max(200),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING']).optional().default('ACTIVE'),
  description: z.string().max(1000).optional(),
  contactEmail: z.string().email('Invalid email format').optional().or(z.literal('')),
  contactPhone: z.string().regex(phoneRegex, 'Invalid phone number format').optional().or(z.literal('')),
  // ... other fields
}).refine(data => {
  // Business logic validation
  if (data.emergencyContactName && !data.emergencyContactPhone) {
    return false;
  }
  return true;
}, {
  message: 'Emergency contact phone is required when emergency contact name is provided',
  path: ['emergencyContactPhone'],
});
```

### Error Handling Middleware

```typescript
// Global error handling for API routes
export async function withErrorHandling(
  handler: (req: NextRequest, ...args: any[]) => Promise<NextResponse>,
  req: NextRequest,
  ...args: any[]
): Promise<NextResponse> {
  try {
    return await handler(req, ...args);
  } catch (error) {
    console.error('API Error:', error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 400 }
      );
    }

    if (error instanceof NationTreatyError) {
      const statusCode = error.code === 'NOT_FOUND' ? 404 : 400;
      return NextResponse.json(
        { success: false, error: error.message },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

## Performance Optimization

### Database Optimization

```typescript
// Strategic indexing in Prisma schema
model NationTreaty {
  // ... fields
  @@index([status])
  @@index([createdAt])
  @@index([name])
}

// Query optimization with selective field loading
const treaties = await prisma.nationTreaty.findMany({
  select: {
    id: true,
    name: true,
    officialName: true,
    status: true,
    createdAt: true,
  },
  where: { status: 'ACTIVE' },
  orderBy: { name: 'asc' },
});
```

### Frontend Optimization

```typescript
// React memoization for expensive components
const TreatyList = React.memo(({ treaties, onTreatySelect }: TreatyListProps) => {
  return (
    <div className="space-y-2">
      {treaties.map(treaty => (
        <TreatyItem
          key={treaty.id}
          treaty={treaty}
          onSelect={onTreatySelect}
        />
      ))}
    </div>
  );
});

// Custom hooks for data caching
export const useCachedNationTreaties = (query: NationTreatyQuery) => {
  const cache = useRef<Map<string, any>>(new Map());
  const [data, setData] = useState<any>(null);
  
  useEffect(() => {
    const cacheKey = JSON.stringify(query);
    if (cache.current.has(cacheKey)) {
      setData(cache.current.get(cacheKey));
      return;
    }
    
    fetchData(query).then(result => {
      cache.current.set(cacheKey, result);
      setData(result);
    });
  }, [query]);
  
  return data;
};
```

## Security Considerations

### Input Sanitization

```typescript
// Sanitize user inputs
export function sanitizeString(input: string): string {
  if (!input) return input;
  return input.trim().replace(/[<>]/g, '');
}

// Validate file uploads
export function validateFileUpload(file: File): boolean {
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
  const maxSize = 5 * 1024 * 1024; // 5MB
  
  return allowedTypes.includes(file.type) && file.size <= maxSize;
}
```

### Access Control

```typescript
// Role-based access control middleware
export function requireRole(role: string) {
  return (handler: (req: NextRequest, ...args: any[]) => Promise<NextResponse>) => {
    return async (req: NextRequest, ...args: any[]) => {
      const session = await getServerSession(authOptions);
      
      if (!session?.user || session.user.role !== role) {
        return NextResponse.json(
          { success: false, error: 'Forbidden' },
          { status: 403 }
        );
      }
      
      return handler(req, ...args);
    };
  };
}
```

## Deployment and DevOps

### Environment Configuration

```bash
# .env.local
DATABASE_URL="postgresql://user:password@localhost:5432/nwa_alliance"
NEXTAUTH_URL="http://localhost:3001"
NEXTAUTH_SECRET="your-secret-key"
```

### Database Migrations

```bash
# Create migration
npx prisma migrate dev --name add-nation-treaty-offices

# Apply migrations to production
npx prisma migrate deploy

# Generate Prisma client
npx prisma generate
```

### CI/CD Pipeline

```yaml
# .github/workflows/deploy.yml
name: Deploy Nation Treaty System

on:
  push:
    branches: [main]
    paths: ['src/**', 'prisma/**']

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm test
      - run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm ci --production
      - run: npm run build
      - run: npm run migrate:deploy
```

## Debugging and Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check DATABASE_URL environment variable
   - Verify PostgreSQL service is running
   - Check network connectivity

2. **TypeScript Compilation Errors**
   - Run `npm run build` to identify issues
   - Check type definitions in validation schemas
   - Verify component prop types

3. **API Authentication Issues**
   - Verify NextAuth configuration
   - Check session cookies
   - Validate user permissions

### Development Tools

```typescript
// Debug logging middleware
export function withDebugLogging(handler: (req: NextRequest) => Promise<NextResponse>) {
  return async (req: NextRequest) => {
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
    const start = Date.now();
    
    const response = await handler(req);
    
    const duration = Date.now() - start;
    console.log(`[${new Date().toISOString()}] Response in ${duration}ms`);
    
    return response;
  };
}
```

## Extension Points

### Custom Validators

```typescript
// Add custom validation logic
export function validateTreatyTransition(
  currentStatus: string,
  newStatus: string
): boolean {
  const validTransitions = {
    ACTIVE: ['INACTIVE', 'SUSPENDED'],
    INACTIVE: ['ACTIVE'],
    SUSPENDED: ['ACTIVE', 'INACTIVE'],
    PENDING: ['ACTIVE', 'INACTIVE'],
  };
  
  return validTransitions[currentStatus]?.includes(newStatus) || false;
}
```

### Event Hooks

```typescript
// Add custom event handlers
export interface TreatyEvents {
  onCreate: (treaty: NationTreaty) => Promise<void>;
  onUpdate: (treaty: NationTreaty, oldTreaty: NationTreaty) => Promise<void>;
  onDelete: (treaty: NationTreaty) => Promise<void>;
}

export class TreatyEventManager {
  private events: TreatyEvents = {
    onCreate: async () => {},
    onUpdate: async () => {},
    onDelete: async () => {},
  };
  
  on(event: keyof TreatyEvents, handler: TreatyEvents[keyof TreatyEvents]) {
    this.events[event] = handler;
  }
  
  async emit(event: keyof TreatyEvents, ...args: any[]) {
    await this.events[event](...args);
  }
}
```

## Best Practices

### Code Quality

1. **TypeScript Usage**: Use strict TypeScript configuration
2. **Error Handling**: Implement comprehensive error handling
3. **Testing**: Maintain high test coverage (>80%)
4. **Documentation**: Keep documentation up to date
5. **Code Reviews**: Require peer reviews for all changes

### Performance

1. **Database Queries**: Optimize queries with proper indexing
2. **Frontend Rendering**: Use React.memo and useCallback appropriately
3. **API Responses**: Minimize payload sizes with selective field loading
4. **Caching**: Implement strategic caching for frequently accessed data

### Security

1. **Input Validation**: Validate all user inputs
2. **Authentication**: Implement proper session management
3. **Authorization**: Enforce role-based access control
4. **Audit Logging**: Log all sensitive operations

---

This developer guide provides a comprehensive overview of the Nation Treaty System architecture and development practices. For specific implementation questions or issues, please refer to the codebase documentation or contact the development team.