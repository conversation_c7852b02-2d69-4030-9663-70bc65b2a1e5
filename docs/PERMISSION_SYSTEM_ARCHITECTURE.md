# NWA Alliance Permission System Architecture

## Overview

This document provides detailed technical documentation for the permission system implemented as part of the 2025-09-24 Comprehensive Security Audit. The permission system provides granular, role-based access control with hierarchical permissions and comprehensive audit trails.

### System Status: ✅ FULLY IMPLEMENTED

The permission system includes:
- Hierarchical role-based access control (RBAC)
- Granular permission management
- Frontend integration with React components
- Comprehensive audit logging
- Performance-optimized database queries
- Security testing and validation

---

## Architecture Overview

### Core Components

#### 1. Database Layer
- **Permission Storage**: PostgreSQL with optimized schema
- **Role Management**: Hierarchical role system
- **User Assignment**: Many-to-many user-role relationships
- **Audit Trail**: Complete permission change tracking

#### 2. Backend Layer
- **Authentication Middleware**: Session and token validation
- **Authorization Middleware**: Permission checking and enforcement
- **API Endpoints**: Permission management and validation
- **Audit Logging**: Security event tracking

#### 3. Frontend Layer
- **Permission Context**: React context for permission state
- **Permission Components**: Reusable permission-aware components
- **Navigation Filtering**: Dynamic menu based on permissions
- **Error Handling**: User-friendly permission error messages

### Permission Flow

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   User Request  │───▶│  Authentication  │───▶│  Authorization  │
│                 │    │   Middleware     │    │   Middleware    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Permission    │    │   Database       │    │   Audit Log     │
│   Validation    │◀───│   Queries        │───▶│   Generation    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

---

## Database Schema

### Core Tables

#### Users Table
```sql
CREATE TABLE users (
  id TEXT PRIMARY KEY DEFAULT cuid(),
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  password_hash TEXT,
  email_verified TIMESTAMP,
  image TEXT,
  two_factor_secret TEXT,
  two_factor_enabled BOOLEAN DEFAULT false,
  two_factor_enforced BOOLEAN DEFAULT false,
  backup_codes TEXT[],
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Roles Table
```sql
CREATE TABLE roles (
  id TEXT PRIMARY KEY DEFAULT cuid(),
  name TEXT UNIQUE NOT NULL,
  description TEXT,
  is_system BOOLEAN DEFAULT false,
  hierarchy INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_roles_name ON roles(name);
CREATE INDEX idx_roles_hierarchy ON roles(hierarchy);
```

#### Permissions Table
```sql
CREATE TABLE permissions (
  id TEXT PRIMARY KEY DEFAULT cuid(),
  name TEXT UNIQUE NOT NULL,
  resource TEXT NOT NULL,
  action TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_permissions_name ON permissions(name);
CREATE INDEX idx_permissions_resource ON permissions(resource);
CREATE INDEX idx_permissions_action ON permissions(action);
```

#### User-Role Junction Table
```sql
CREATE TABLE user_roles (
  id TEXT PRIMARY KEY DEFAULT cuid(),
  user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  role_id TEXT REFERENCES roles(id) ON DELETE CASCADE,
  assigned_at TIMESTAMP DEFAULT NOW(),
  assigned_by TEXT REFERENCES users(id),
  UNIQUE(user_id, role_id)
);

-- Indexes for performance
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX idx_user_roles_assigned_at ON user_roles(assigned_at);
```

#### Role-Permission Junction Table
```sql
CREATE TABLE role_permissions (
  id TEXT PRIMARY KEY DEFAULT cuid(),
  role_id TEXT REFERENCES roles(id) ON DELETE CASCADE,
  permission_id TEXT REFERENCES permissions(id) ON DELETE CASCADE,
  UNIQUE(role_id, permission_id)
);

-- Indexes for performance
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON role_permissions(permission_id);
```

### Permission Inheritance Logic

The system implements hierarchical permission inheritance:

```sql
-- Get all permissions for a user (including inherited permissions)
SELECT DISTINCT p.name
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.id = $1;
```

---

## Backend Implementation

### Authentication Middleware

**File**: `src/lib/middleware/require-auth.ts`

#### Core Function: `requireAuth`

```typescript
export async function requireAuth(
  request: NextRequest,
  options: RequireAuthOptions = {}
): Promise<AuthContext> {
  const {
    requireRoles = [],
    requirePermissions = [],
    requireAdmin = false,
    auditLogging = false,
    customErrorMessage
  } = options;

  // 1. Validate session
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    throw new AuthenticationError('Authentication required');
  }

  // 2. Fetch user with roles and permissions
  const userWithRoles = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      userRoles: {
        include: {
          role: {
            include: {
              rolePermissions: {
                include: {
                  permission: true
                }
              }
            }
          }
        }
      }
    }
  });

  // 3. Extract permissions
  const roles = userWithRoles.userRoles.map(ur => ur.role.name);
  const permissions = userWithRoles.userRoles.flatMap(ur =>
    ur.role.rolePermissions.map(rp => rp.permission.name)
  );

  // 4. Check role requirements
  if (requireRoles.length > 0) {
    const hasRequiredRole = requireRoles.some(role => roles.includes(role));
    if (!hasRequiredRole) {
      throw new AuthorizationError(`Required roles: ${requireRoles.join(', ')}`);
    }
  }

  // 5. Check permission requirements
  if (requirePermissions.length > 0) {
    const hasRequiredPermission = requirePermissions.some(permission =>
      permissions.includes(permission)
    );
    if (!hasRequiredPermission) {
      throw new AuthorizationError(`Required permissions: ${requirePermissions.join(', ')}`);
    }
  }

  return { userId, email, name, roles, permissions };
}
```

#### Usage Examples

```typescript
// Require specific permission
const authContext = await requireAuth(request, {
  requirePermissions: ['remote_servers:write'],
  auditLogging: true,
});

// Require admin role
const authContext = await requireAuth(request, {
  requireAdmin: true,
  auditLogging: true,
});

// Require multiple permissions (OR logic)
const authContext = await requireAuth(request, {
  requirePermissions: ['users:write', 'admin:access'],
  auditLogging: true,
});
```

### API Route Protection

**Example**: Protected API Route

```typescript
// src/app/api/remote-servers/[id]/route.ts
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Apply rate limiting
    const rateLimitResult = await checkRateLimit(request, {
      windowMs: 60 * 1000,
      maxRequests: 30,
    });

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded' },
        { status: 429 }
      );
    }

    // Require authentication and permissions
    const authContext = await requireAuth(request, {
      requirePermissions: ['remote_servers:write'],
      auditLogging: true,
    });

    // Process request...
    const body = await request.json();
    const validation = remoteServerUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation Error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    // Update remote server...
    const remoteServer = await prisma.remoteServer.update({
      where: { id: serverId },
      data: updatePayload,
      select: { /* ... */ }
    });

    // Log successful operation
    await auditLoggingMiddleware.logApiAccess(
      authContext.userId,
      'update',
      'remote_server',
      true,
      request,
      { metadata: { serverId, updatedFields } }
    );

    return NextResponse.json({ remoteServer });
  } catch (error) {
    // Handle authentication/authorization errors
    if (error instanceof AuthenticationError) {
      return NextResponse.json(
        { error: 'Unauthorized', message: error.message },
        { status: 401 }
      );
    }

    if (error instanceof AuthorizationError) {
      return NextResponse.json(
        { error: 'Forbidden', message: error.message },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
```

---

## Frontend Implementation

### Permission Context Provider

**File**: `src/components/providers/PermissionProvider.tsx`

```typescript
'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';

interface PermissionContextType {
  permissions: string[];
  roles: string[];
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
  isLoading: boolean;
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined);

export function PermissionProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const [permissions, setPermissions] = useState<string[]>([]);
  const [roles, setRoles] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchPermissions() {
      if (status === 'authenticated' && session?.user) {
        try {
          const response = await fetch('/api/user/permissions');
          if (response.ok) {
            const data = await response.json();
            setPermissions(data.permissions || []);
            setRoles(data.roles || []);
          }
        } catch (error) {
          console.error('Failed to fetch permissions:', error);
        } finally {
          setIsLoading(false);
        }
      } else if (status === 'unauthenticated') {
        setPermissions([]);
        setRoles([]);
        setIsLoading(false);
      }
    }

    fetchPermissions();
  }, [session, status]);

  const hasPermission = (permission: string): boolean => {
    return permissions.includes(permission);
  };

  const hasRole = (role: string): boolean => {
    return roles.includes(role);
  };

  const hasAnyPermission = (permissionsToCheck: string[]): boolean => {
    return permissionsToCheck.some(permission => hasPermission(permission));
  };

  const hasAnyRole = (rolesToCheck: string[]): boolean => {
    return rolesToCheck.some(role => hasRole(role));
  };

  const value: PermissionContextType = {
    permissions,
    roles,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAnyRole,
    isLoading,
  };

  return (
    <PermissionContext.Provider value={value}>
      {children}
    </PermissionContext.Provider>
  );
}

export function usePermissions() {
  const context = useContext(PermissionContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionProvider');
  }
  return context;
}
```

### Permission-Aware Components

#### PermissionWrapper Component

**File**: `src/components/auth/PermissionWrapper.tsx`

```typescript
interface PermissionWrapperProps {
  permissions?: string[];
  roles?: string[];
  requireAll?: boolean; // true = AND, false = OR
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export function PermissionWrapper({
  permissions = [],
  roles = [],
  requireAll = false,
  fallback = null,
  children
}: PermissionWrapperProps) {
  const { hasPermission, hasRole, hasAnyPermission, hasAnyRole, isLoading } = usePermissions();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  // Check permissions
  let hasRequiredPermissions = true;
  if (permissions.length > 0) {
    if (requireAll) {
      hasRequiredPermissions = permissions.every(permission => hasPermission(permission));
    } else {
      hasRequiredPermissions = hasAnyPermission(permissions);
    }
  }

  // Check roles
  let hasRequiredRoles = true;
  if (roles.length > 0) {
    if (requireAll) {
      hasRequiredRoles = roles.every(role => hasRole(role));
    } else {
      hasRequiredRoles = hasAnyRole(roles);
    }
  }

  // If both permissions and roles are specified, user must have both
  const hasAccess = hasRequiredPermissions && hasRequiredRoles;

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
```

#### Usage Examples

```tsx
// Require specific permission
<PermissionWrapper permissions={['remote_servers:write']}>
  <EditServerButton />
</PermissionWrapper>

// Require admin role
<PermissionWrapper roles={['admin']}>
  <AdminPanel />
</PermissionWrapper>

// Require any of multiple permissions (OR logic)
<PermissionWrapper permissions={['users:write', 'admin:access']}>
  <UserManagement />
</PermissionWrapper>

// Require all permissions (AND logic)
<PermissionWrapper
  permissions={['users:read', 'users:write']}
  requireAll={true}
>
  <UserEditor />
</PermissionWrapper>

// Custom fallback component
<PermissionWrapper
  permissions={['admin:access']}
  fallback={<div>You don't have permission to view this content.</div>}
>
  <SensitiveData />
</PermissionWrapper>
```

### Navigation Filtering

**File**: `src/components/navigation/PermissionNavigation.tsx`

```typescript
const navigationItems = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: HomeIcon,
    permissions: [], // Public access
  },
  {
    name: 'Remote Servers',
    href: '/settings?tab=remote-servers',
    icon: ServerIcon,
    permissions: ['remote_servers:read'],
  },
  {
    name: 'User Management',
    href: '/admin/users',
    icon: UsersIcon,
    permissions: ['users:read'],
  },
  {
    name: 'Security Audit',
    href: '/admin/security',
    icon: ShieldIcon,
    permissions: ['audit:read'],
  },
  {
    name: 'System Settings',
    href: '/admin/settings',
    icon: CogIcon,
    permissions: ['admin:access'],
  },
];

export function PermissionNavigation() {
  const { hasAnyPermission } = usePermissions();

  const visibleItems = navigationItems.filter(item => {
    if (item.permissions.length === 0) return true;
    return hasAnyPermission(item.permissions);
  });

  return (
    <nav className="space-y-1">
      {visibleItems.map((item) => (
        <Link
          key={item.name}
          href={item.href}
          className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900"
        >
          <item.icon className="mr-3 h-5 w-5" />
          {item.name}
        </Link>
      ))}
    </nav>
  );
}
```

### Permission-Based UI State Management

**File**: `src/hooks/usePermissions.ts`

```typescript
export function usePermissions() {
  const { permissions, roles, hasPermission, hasRole, hasAnyPermission, hasAnyRole, isLoading } = usePermissionsContext();

  // Derived permission states
  const canEditUsers = hasPermission('users:write');
  const canDeleteUsers = hasPermission('users:delete');
  const canManageRoles = hasPermission('roles:write');
  const canViewAuditLogs = hasPermission('audit:read');
  const canManageRemoteServers = hasAnyPermission(['remote_servers:write', 'remote_servers:delete']);
  const isAdmin = hasRole('admin');
  const isSuperAdmin = hasRole('super_admin');

  // Permission groups
  const userPermissions = {
    canRead: hasPermission('users:read'),
    canWrite: hasPermission('users:write'),
    canDelete: hasPermission('users:delete'),
  };

  const remoteServerPermissions = {
    canRead: hasPermission('remote_servers:read'),
    canWrite: hasPermission('remote_servers:write'),
    canDelete: hasPermission('remote_servers:delete'),
    canSync: hasPermission('remote_servers:sync'),
  };

  return {
    // Base permissions
    permissions,
    roles,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAnyRole,
    isLoading,

    // Derived states
    canEditUsers,
    canDeleteUsers,
    canManageRoles,
    canViewAuditLogs,
    canManageRemoteServers,
    isAdmin,
    isSuperAdmin,

    // Permission groups
    userPermissions,
    remoteServerPermissions,
  };
}
```

---

## Permission Management API

### Get User Permissions

**Endpoint**: `GET /api/user/permissions`

Returns the current user's permissions and roles:

```typescript
interface UserPermissionsResponse {
  permissions: string[];
  roles: string[];
  userId: string;
  email: string;
}
```

**Usage**:
```typescript
const response = await fetch('/api/user/permissions');
const data = await response.json();
console.log('User permissions:', data.permissions);
console.log('User roles:', data.roles);
```

### Get All System Permissions

**Endpoint**: `GET /api/permissions`

Returns all system permissions and roles (requires `permissions:read`):

```typescript
interface PermissionsResponse {
  permissions: Array<{
    name: string;
    description: string;
    category: string;
  }>;
  roles: Array<{
    name: string;
    description: string;
    permissions: string[];
    hierarchy: number;
  }>;
  permissionCategories: Array<{
    name: string;
    description: string;
  }>;
}
```

### Assign Permissions to Role

**Endpoint**: `POST /api/roles/[roleId]/permissions`

Assigns permissions to a role:

```typescript
const response = await fetch(`/api/roles/${roleId}/permissions`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    permissions: ['remote_servers:write', 'users:read']
  })
});
```

### Remove Permissions from Role

**Endpoint**: `DELETE /api/roles/[roleId]/permissions`

Removes permissions from a role:

```typescript
const response = await fetch(`/api/roles/${roleId}/permissions`, {
  method: 'DELETE',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    permissions: ['remote_servers:write']
  })
});
```

---

## Permission Validation

### Client-Side Validation

```typescript
// Check single permission
if (hasPermission('remote_servers:write')) {
  // Show edit button
  return <EditButton onClick={handleEdit} />;
}

// Check multiple permissions (OR logic)
if (hasAnyPermission(['users:write', 'admin:access'])) {
  // Show management interface
  return <UserManagementInterface />;
}

// Check role
if (hasRole('admin')) {
  // Show admin features
  return <AdminFeatures />;
}
```

### Server-Side Validation

```typescript
// API route protection
const authContext = await requireAuth(request, {
  requirePermissions: ['remote_servers:write'],
  auditLogging: true,
});

// Component-level authorization
if (!authContext.permissions.includes('remote_servers:write')) {
  return NextResponse.json(
    { error: 'Forbidden', message: 'Insufficient permissions' },
    { status: 403 }
  );
}
```

### Database-Level Validation

```sql
-- Ensure user has required permission for operation
SELECT 1 FROM user_roles ur
JOIN role_permissions rp ON ur.role_id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE ur.user_id = $1 AND p.name = $2;
```

---

## Performance Optimization

### Database Query Optimization

#### Indexed Queries
```sql
-- Optimized permission lookup
SELECT DISTINCT p.name
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.id = $1;
```

#### Caching Strategy
- User permissions cached in session
- Role-permission mappings cached in memory
- Database indexes on frequently queried columns

### Frontend Optimization

#### Permission Context Caching
```typescript
// Cache permissions in React context
const [permissions, setPermissions] = useState<string[]>([]);
const [permissionCache, setPermissionCache] = useState<Map<string, boolean>>(new Map());

// Optimized permission checking
const hasPermission = useCallback((permission: string): boolean => {
  if (permissionCache.has(permission)) {
    return permissionCache.get(permission)!;
  }

  const hasPerm = permissions.includes(permission);
  setPermissionCache(prev => new Map(prev.set(permission, hasPerm)));
  return hasPerm;
}, [permissions, permissionCache]);
```

#### Lazy Loading
```typescript
// Only fetch permissions when needed
const { permissions, isLoading } = usePermissions();

useEffect(() => {
  if (!isLoading && permissions.length === 0) {
    // Handle unauthenticated state
  }
}, [permissions, isLoading]);
```

---

## Security Considerations

### Permission Escalation Prevention

#### 1. Server-Side Validation
- All permission checks performed server-side
- Client-side checks are supplementary only
- API endpoints validate permissions independently

#### 2. Role Hierarchy Enforcement
```typescript
// Prevent users from assigning higher-level roles
const userRoles = await prisma.userRoles.findMany({
  where: { userId: authContext.userId },
  include: { role: true }
});

const userMaxHierarchy = Math.max(...userRoles.map(ur => ur.role.hierarchy));

if (targetRole.hierarchy > userMaxHierarchy) {
  throw new AuthorizationError('Cannot assign role with higher hierarchy');
}
```

#### 3. Permission Inheritance Validation
```typescript
// Validate permission inheritance chains
const rolePermissions = await prisma.rolePermissions.findMany({
  where: { roleId: roleId },
  include: { permission: true }
});

// Ensure no circular dependencies
const permissionNames = rolePermissions.map(rp => rp.permission.name);
const hasCircularDependency = checkCircularDependency(permissionNames);

if (hasCircularDependency) {
  throw new Error('Circular permission dependency detected');
}
```

### Audit Trail Integrity

#### 1. Immutable Audit Logs
```sql
-- Audit logs cannot be modified after creation
CREATE TABLE audit_logs (
  id TEXT PRIMARY KEY,
  -- ... other fields
  created_at TIMESTAMP DEFAULT NOW()
);

-- Prevent updates to audit logs
CREATE RULE prevent_audit_log_updates AS
ON UPDATE TO audit_logs DO INSTEAD NOTHING;
```

#### 2. Cryptographic Verification
```typescript
// Sign audit log entries for integrity
const auditEntry = {
  userId,
  action,
  resource,
  timestamp: new Date().toISOString(),
  // ... other data
};

const signature = signAuditEntry(auditEntry, privateKey);
await prisma.auditLog.create({
  data: {
    ...auditEntry,
    signature,
  }
});
```

---

## Testing and Validation

### Unit Tests

**File**: `src/__tests__/auth/permission-system.test.ts`

```typescript
describe('Permission System', () => {
  describe('PermissionProvider', () => {
    it('should provide correct permissions', async () => {
      const { result } = renderHook(() => usePermissions(), {
        wrapper: PermissionProvider,
      });

      await waitFor(() => {
        expect(result.current.permissions).toContain('remote_servers:read');
        expect(result.current.hasPermission('remote_servers:write')).toBe(false);
      });
    });

    it('should handle permission checks correctly', () => {
      const { result } = renderHook(() => usePermissions(), {
        wrapper: PermissionProvider,
      });

      expect(result.current.hasPermission('nonexistent:permission')).toBe(false);
      expect(result.current.hasAnyPermission(['perm1', 'perm2'])).toBe(false);
    });
  });

  describe('PermissionWrapper', () => {
    it('should render children when permissions are met', () => {
      const { getByText } = render(
        <PermissionProvider>
          <PermissionWrapper permissions={['test:permission']}>
            <div>Secret Content</div>
          </PermissionWrapper>
        </PermissionProvider>
      );

      expect(getByText('Secret Content')).toBeInTheDocument();
    });

    it('should render fallback when permissions are not met', () => {
      const { getByText } = render(
        <PermissionProvider>
          <PermissionWrapper permissions={['missing:permission']}>
            <div>Secret Content</div>
          </PermissionWrapper>
        </PermissionProvider>
      );

      expect(getByText('Access Denied')).toBeInTheDocument();
    });
  });
});
```

### Integration Tests

**File**: `src/__tests__/auth/permission-integration.test.ts`

```typescript
describe('Permission Integration', () => {
  describe('API Endpoints', () => {
    it('should protect remote server endpoints', async () => {
      // Test without authentication
      const response = await fetch('/api/remote-servers/test-id');
      expect(response.status).toBe(401);

      // Test with insufficient permissions
      const authResponse = await fetch('/api/remote-servers/test-id', {
        headers: { Authorization: 'Bearer invalid-token' }
      });
      expect(authResponse.status).toBe(403);

      // Test with proper permissions
      const validResponse = await fetch('/api/remote-servers/test-id', {
        headers: { Authorization: 'Bearer valid-admin-token' }
      });
      expect(validResponse.status).toBe(200);
    });
  });

  describe('Navigation Filtering', () => {
    it('should show appropriate navigation items based on permissions', async () => {
      // Mock user with limited permissions
      const { getByRole } = render(<PermissionNavigation />, {
        permissions: ['remote_servers:read']
      });

      expect(getByRole('link', { name: 'Remote Servers' })).toBeInTheDocument();
      expect(screen.queryByRole('link', { name: 'User Management' })).not.toBeInTheDocument();
    });
  });
});
```

### Performance Tests

**File**: `src/__tests__/auth/permission-performance.test.ts`

```typescript
describe('Permission Performance', () => {
  it('should handle permission checks efficiently', async () => {
    const startTime = performance.now();

    for (let i = 0; i < 1000; i++) {
      hasPermission('test:permission');
    }

    const endTime = performance.now();
    const duration = endTime - startTime;

    expect(duration).toBeLessThan(100); // Should be under 100ms for 1000 checks
  });

  it('should cache permission results', async () => {
    const { result } = renderHook(() => usePermissions());

    // First call
    const firstCall = performance.now();
    result.current.hasPermission('cached:permission');
    const firstCallEnd = performance.now();

    // Second call (should be cached)
    const secondCall = performance.now();
    result.current.hasPermission('cached:permission');
    const secondCallEnd = performance.now();

    // Cached call should be significantly faster
    expect(secondCallEnd - secondCall).toBeLessThan(firstCallEnd - firstCall);
  });
});
```

---

## Troubleshooting

### Common Issues

#### 1. Permission Not Working
**Symptom**: User has role but permissions don't work
**Solution**:
```bash
# Check if permissions are assigned to role
SELECT r.name, p.name
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE r.name = 'admin';

# Check user role assignment
SELECT u.email, r.name
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
WHERE u.email = '<EMAIL>';
```

#### 2. Performance Issues
**Symptom**: Permission checks are slow
**Solution**:
```bash
# Check database indexes
SELECT indexname, tablename, indexdef
FROM pg_indexes
WHERE tablename IN ('users', 'roles', 'permissions', 'user_roles', 'role_permissions');

# Optimize queries
EXPLAIN ANALYZE
SELECT DISTINCT p.name
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.id = $1;
```

#### 3. Frontend Permission Issues
**Symptom**: Frontend not respecting permissions
**Solution**:
```typescript
// Check permission context
console.log('User permissions:', permissions);
console.log('Has permission:', hasPermission('required:permission'));

// Verify component rendering
const { debug } = render(<PermissionWrapper permissions={['test:perm']}>...</PermissionWrapper>);
debug(); // Shows component tree
```

### Debug Commands

```bash
# Check user permissions
npm run db:util test

# View permission assignments
npx prisma studio

# Test permission API
curl -H "Authorization: Bearer <token>" /api/user/permissions

# Check audit logs
curl -H "Authorization: Bearer <admin-token>" /api/audit/logs
```

---

## Best Practices

### For Developers

#### 1. API Development
- Always use `requireAuth` middleware
- Specify exact required permissions
- Enable audit logging for sensitive operations
- Handle permission errors gracefully

#### 2. Frontend Development
- Use permission context throughout the app
- Implement permission-aware components
- Provide meaningful fallback UI
- Cache permission results when possible

#### 3. Database Operations
- Use transactions for permission changes
- Validate permission inheritance
- Maintain audit trails
- Optimize queries with proper indexes

### For System Administrators

#### 1. Role Management
- Create roles with minimal required permissions
- Use role hierarchy appropriately
- Regularly review role assignments
- Document role purposes clearly

#### 2. Permission Assignment
- Follow principle of least privilege
- Use permission groups when possible
- Regularly audit permission assignments
- Document permission changes

#### 3. Monitoring and Maintenance
- Monitor permission usage patterns
- Alert on suspicious permission activity
- Regularly review and update permissions
- Maintain comprehensive audit logs

---

## Future Enhancements

### Planned Improvements
1. **Dynamic Permission Assignment**
   - Time-based permissions
   - Location-based permissions
   - Conditional permissions based on context

2. **Advanced Permission Features**
   - Permission delegation
   - Temporary permission elevation
   - Permission approval workflows

3. **Performance Enhancements**
   - Distributed permission caching
   - Query optimization
   - Background permission recalculation

4. **Security Enhancements**
   - Zero-trust permission model
   - Continuous permission validation
   - Advanced threat detection

### Migration Guide

#### Upgrading from Legacy Permission System
1. **Backup existing permissions**
2. **Map old permissions to new system**
3. **Update role assignments**
4. **Test thoroughly**
5. **Migrate users gradually**

#### Version Compatibility
- Current version: 1.0.0
- Minimum compatible version: 1.0.0
- Migration required: No

---

## Support and Maintenance

### Regular Maintenance
- Weekly permission audit
- Monthly role review
- Quarterly security assessment
- Annual compliance review

### Support Channels
- **Technical Issues**: <EMAIL>
- **Security Concerns**: <EMAIL>
- **Documentation**: <EMAIL>

### Emergency Procedures
- **Permission Issues**: Contact system administrator
- **Security Incidents**: Follow incident response plan
- **Data Breaches**: Activate emergency response team

---

## Conclusion

The NWA Alliance permission system provides a robust, scalable, and secure foundation for access control. The hierarchical role-based system with granular permissions ensures that users have exactly the access they need while maintaining comprehensive audit trails and security monitoring.

The system is designed to be maintainable, performant, and extensible, with comprehensive testing and documentation to support ongoing development and maintenance.

**System Status**: ✅ FULLY IMPLEMENTED AND VALIDATED
**Last Updated**: 2025-09-24
**Version**: 1.0.0