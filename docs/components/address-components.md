# Address Components Documentation

This document provides comprehensive documentation for the address-related components in the NWA Alliance application, including the new structured address field implementation.

## Overview

The application has migrated from a single `address` field to a structured address system that supports country-specific formatting, regions, and improved data quality. The following components have been updated:

- `CreateUserTab` - User creation form with structured address fields
- `UpdateUserTab` - User update form with structured address fields  
- `UserProfile` - User profile display with formatted address rendering

## Components

### CreateUserTab

The `CreateUserTab` component provides a form for creating new users with structured address fields.

#### Features

- Country-specific address field labels
- Dynamic region selection based on country
- Postal code validation per country format
- Real-time address field updates
- Form validation and error handling
- Backward compatibility support

#### Props

```typescript
interface CreateUserTabProps {
  onUserCreated?: (user: User) => void;
  onClose?: () => void;
  className?: string;
}
```

#### Usage Example

```tsx
import { CreateUserTab } from '@/components/CreateUserTab';

function AdminPanel() {
  const handleUserCreated = (user: User) => {
    console.log('New user created:', user);
  };

  return (
    <CreateUserTab 
      onUserCreated={handleUserCreated}
      onClose={() => setShowCreate(false)}
    />
  );
}
```

#### Address Fields

The component includes the following address-specific fields:

- **streetAddress1** - Primary street address (required)
- **streetAddress2** - Secondary address line (optional)
- **town** - City/town name (required)
- **postalCode** - Postal/ZIP code (validation varies by country)
- **regionId** - Selected region/state ID (when available)
- **regionText** - Manual region entry (when regions not available)
- **country** - Country selection (required)
- **city** - City association (optional)

### UpdateUserTab

The `UpdateUserTab` component provides a form for updating existing user information, including address fields.

#### Features

- Pre-populated form with existing user data
- Country-specific field labeling
- Region selection with lazy loading
- Backward compatibility with legacy address data
- Validation and error handling
- Performance optimized with debouncing

#### Props

```typescript
interface UpdateUserTabProps {
  userId: string;
  onUserUpdated?: (user: User) => void;
  onClose?: () => void;
  className?: string;
}
```

#### Usage Example

```tsx
import { UpdateUserTab } from '@/components/UpdateUserTab';

function EditUserDialog({ userId }: { userId: string }) {
  const handleUserUpdated = (user: User) => {
    console.log('User updated:', user);
  };

  return (
    <UpdateUserTab 
      userId={userId}
      onUserUpdated={handleUserUpdated}
      onClose={() => setShowEdit(false)}
    />
  );
}
```

#### Backward Compatibility

The component automatically handles legacy address data by:
- Parsing single `address` field into structured components
- Mapping legacy data to appropriate fields
- Preserving original data during updates
- Graceful fallback for missing structured data

### UserProfile

The `UserProfile` component displays user information with formatted address rendering using country-specific templates.

#### Features

- Country-specific address formatting
- Template-based address rendering
- Fallback for missing address components
- Loading states and error handling
- Responsive design
- Accessibility compliance

#### Props

```typescript
interface UserProfileProps {
  userId: string;
  showEditButton?: boolean;
  onEdit?: () => void;
  className?: string;
}
```

#### Usage Example

```tsx
import { UserProfile } from '@/components/UserProfile';

function UserDetailsPage({ userId }: { userId: string }) {
  return (
    <UserProfile 
      userId={userId}
      showEditButton={true}
      onEdit={() => setShowEditDialog(true)}
    />
  );
}
```

#### Address Formatting

The component uses country-specific templates to format addresses:

**US Example:**
```
123 Main Street, Apt 4B
Springfield, IL 62701
United States
```

**UK Example:**
```
123 High Street
Flat 4B
Springfield
Gloucestershire GL1 1AA
United Kingdom
```

## Custom Hooks

### useRegions

Custom hook for lazy loading regions based on country selection.

```typescript
import { useRegions } from '@/lib/hooks/useRegions';

function AddressForm({ countryId }: { countryId: number }) {
  const { regions, loading, error } = useRegions({ 
    countryId, 
    enabled: !!countryId 
  });

  // Use regions in component
}
```

### useDebouncedRegionSearch

Custom hook for debouncing region search to improve performance.

```typescript
import { useDebouncedRegionSearch } from '@/lib/hooks/useDebounced';

function RegionSelect({ searchTerm }: { searchTerm: string }) {
  const debouncedSearch = useDebouncedRegionSearch(searchTerm, 300);
  
  // Use debounced search term for API calls
}
```

## Address Field Validation

### Validation Rules

Each address field has specific validation rules:

- **streetAddress1**: Required, 1-255 characters
- **streetAddress2**: Optional, max 255 characters
- **town**: Required, 1-100 characters
- **postalCode**: Optional, format varies by country
- **regionId**: Optional, must be valid region for country
- **regionText**: Optional when regionId provided, max 100 characters
- **country**: Required, valid country code
- **city**: Optional, must be valid city ID

### Country-Specific Validation

Postal code validation varies by country:

- **US**: 5-digit or 5+4 format (12345 or 12345-6789)
- **UK**: Alphanumeric format (SW1A 1AA)
- **Canada**: A1A 1A1 format
- **Australia**: 4-digit format (1234)

## Styling and Theming

All address components use Tailwind CSS with the application's design system:

- Glass morphism effects
- Consistent spacing and typography
- Focus states and accessibility
- Responsive breakpoints
- Dark/light mode support

## Accessibility

The components include comprehensive accessibility features:

- ARIA labels for all form fields
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- Error announcement
- High contrast support

## Performance Considerations

### Optimization Features

- **Lazy Loading**: Regions only load when country is selected
- **Debouncing**: Search inputs debounced to prevent excessive API calls
- **Caching**: Address formats and regions cached in-memory
- **Memoization**: Component re-renders optimized
- **Code Splitting**: Components loaded on demand

### Performance Monitoring

Use the performance monitoring utilities:

```typescript
import { usePerformanceMonitor } from '@/lib/performance';

function AddressComponent() {
  const { startRender, endRender } = usePerformanceMonitor('AddressComponent');
  
  useEffect(() => {
    startRender();
    return endRender;
  }, []);
}
```

## Testing

All components include comprehensive test suites covering:

- Form validation and submission
- Country-specific field behavior
- Backward compatibility
- Error handling
- Accessibility
- Performance

Run tests with:
```bash
npm run test -- components/address
```

## Migration Notes

When migrating from legacy address fields:

1. Update component imports
2. Replace single address prop with structured fields
3. Update form validation schemas
4. Test backward compatibility with existing data
5. Update API endpoints if needed

See the [Migration Guide](./migration-guide.md) for detailed instructions.
