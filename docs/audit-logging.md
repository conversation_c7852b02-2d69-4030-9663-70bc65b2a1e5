# Audit Logging Documentation

## Overview

The NWA Member Portal implements comprehensive audit logging to track all user activities, system changes, and security events. This documentation covers the audit logging architecture, implementation details, and usage guidelines.

## Architecture

### Database Schema

Audit logs are stored in the `audit_logs` table with the following structure:

```sql
CREATE TABLE public.audit_logs (
    id text NOT NULL,
    user_id text,
    action text NOT NULL,
    resource text NOT NULL,
    resource_id text,
    old_values jsonb,
    new_values jsonb,
    ip_address text,
    user_agent text,
    "timestamp" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    success boolean NOT NULL,
    status_code integer,
    error_message text,
    request_id text,
    duration integer,
    request_size integer,
    response_size integer,
    metadata jsonb,
    project_id text,
    api_endpoint text,
    request_method text,
    country_code text,
    city text,
    remote_server_id text
);
```

### Key Fields

- `user_id`: ID of the user who performed the action
- `action`: Type of action (CREATE, UPDATE, DELETE, LOGIN, etc.)
- `resource`: Resource type being acted upon (user, profile, etc.)
- `resource_id`: ID of the specific resource
- `old_values`: JSON object containing previous values (for updates)
- `new_values`: JSON object containing new values (for creates/updates)
- `ip_address`: IP address of the requesting client
- `user_agent`: User agent string of the requesting client
- `timestamp`: When the action occurred
- `success`: Whether the action was successful
- `metadata`: Additional context-specific information

## Implementation

### User Audit Logger

The `UserAuditLogger` class in `src/lib/user-audit-logger.ts` provides utilities for logging user-related activities:

```typescript
import { userAuditLogger } from '@/lib/user-audit-logger';

// Log user creation
await userAuditLogger.logUserCreation(userId, newValues, changedByUserId, metadata);

// Log user update
await userAuditLogger.logUserUpdate(userId, oldValues, newValues, changedByUserId, metadata);

// Log user deletion
await userAuditLogger.logUserDeletion(userId, oldValues, changedByUserId, metadata);
```

### Automatic Logging

Audit logs are automatically generated for:

1. **User Creation**: When new users are created via signup or admin interface
2. **User Updates**: When user profiles or roles are modified
3. **Authentication Events**: Login, logout, and token refresh activities
4. **API Access**: All API requests with detailed metadata

### Field-Level Tracking

For user updates, the system automatically tracks which fields changed:

```typescript
// Only logs fields that actually changed
const changedFields = {};
Object.keys(newValues).forEach(key => {
  if (oldValues[key] !== newValues[key]) {
    changedFields[key] = {
      from: oldValues[key],
      to: newValues[key]
    };
  }
});
```

## Security Considerations

### Data Sensitivity

Audit logs may contain sensitive information. The system:

1. **Redacts Passwords**: Never logs password values
2. **Limits PII Storage**: Minimizes storage of personally identifiable information
3. **Access Controls**: Restricts access to audit logs to authorized personnel only

### Retention Policy

Audit logs are retained for 90 days by default (configurable via system settings).

## Compliance

The audit logging system supports compliance with:

- **GDPR**: Data protection and privacy requirements
- **SOX**: Financial reporting and internal controls
- **HIPAA**: Healthcare information privacy (if applicable)
- **Custom Organizational Policies**: Internal security and compliance requirements

## API Endpoints

### Get Audit Logs

```http
GET /api/admin/audit-logs
```

Query Parameters:
- `userId`: Filter by user ID
- `action`: Filter by action type
- `resource`: Filter by resource type
- `success`: Filter by success status
- `startDate`: Filter by date range
- `endDate`: Filter by date range
- `limit`: Pagination limit (default: 100)
- `offset`: Pagination offset (default: 0)

### Get Audit Statistics

```http
GET /api/admin/audit-stats
```

Query Parameters:
- `startDate`: Start of statistics period
- `endDate`: End of statistics period

## Best Practices

### When to Use Audit Logging

1. **All User Data Changes**: Any modification to user information
2. **Security Events**: Authentication attempts, privilege changes
3. **Administrative Actions**: System configuration changes
4. **Financial Transactions**: Any monetary or resource exchanges

### What Not to Log

1. **Passwords**: Never log password values
2. **Sensitive Documents**: Avoid logging full document contents
3. **Large Binary Data**: Don't log images, files, or large data blobs

### Performance Considerations

1. **Async Logging**: Audit logs are written asynchronously to avoid blocking main operations
2. **Batch Processing**: High-volume systems should consider batch log processing
3. **Indexing**: Critical fields are indexed for fast querying

## Troubleshooting

### Common Issues

1. **Missing Logs**: Check that audit logging is enabled in middleware configuration
2. **Performance Impact**: Ensure async logging is properly implemented
3. **Database Size**: Monitor audit log table growth and implement retention policies

### Log Analysis

Use the audit log API to analyze:

1. **User Activity Patterns**: Track when and how users interact with the system
2. **Security Incidents**: Investigate suspicious activities
3. **System Usage**: Monitor feature adoption and system performance