# OAuth2 Authentication Plan

## Overview
This document outlines the OAuth2 authentication implementation plan for the NWA Alliance platform. The plan includes the authentication flow, security considerations, and implementation details.

## Authentication Flow

### 1. Authorization Code Flow
The primary OAuth2 flow used will be the Authorization Code flow with PKCE (Proof Key for Code Exchange) for public clients.

Steps:
1. User initiates login from client application
2. Client redirects user to authorization server (/oauth/authorize)
3. User authenticates with credentials
4. Authorization server redirects back to client with authorization code
5. Client exchanges authorization code for access token (/oauth/token)
6. Client uses access token to access protected resources

### 2. Token Management
- Access tokens will be JWT (JSON Web Tokens)
- Access token lifetime: 15 minutes
- Refresh tokens will be used to obtain new access tokens
- Refresh token lifetime: 7 days
- Tokens will be stored securely (hashed in database for refresh tokens)

### 3. Scopes
The following scopes will be implemented:
- read:profile - Read user profile information
- write:profile - Modify user profile information
- read:treaties - Read user treaty information
- write:treaties - Modify user treaty information
- read:ordinances - Read user ordinance information
- write:ordinances - Modify user ordinance information
- admin:users - Manage users (admin only)
- admin:system - System administration (admin only)

## Security Considerations

### 1. Token Security
- All tokens will be transmitted over HTTPS only
- Access tokens will not be stored in localStorage or sessionStorage
- Secure, HttpOnly cookies will be used for token storage when appropriate
- Tokens will be properly invalidated on logout

### 2. PKCE Implementation
- Public clients (SPA, mobile apps) must use PKCE
- Code verifier will be generated with sufficient entropy
- Code challenge method will use S256

### 3. Client Authentication
- Confidential clients (backend services) will use client secrets
- Public clients (SPA, mobile apps) will use PKCE instead of secrets
- Client credentials will be properly validated

### 4. Rate Limiting
- Implement rate limiting on authentication endpoints
- Prevent brute force attacks on login
- Limit authorization code requests per client

### 5. Session Management
- Implement proper session invalidation
- Track active sessions per user
- Allow users to revoke sessions

## Implementation Details

### 1. Database Schema
The following tables will be used for OAuth2 implementation:

#### oauth_clients
- id (TEXT, PK)
- name (TEXT)
- client_id (TEXT, unique)
- client_secret (TEXT, hashed)
- redirect_uris (TEXT[])
- grant_types (TEXT[])
- scopes (TEXT[])
- is_confidential (BOOLEAN)
- is_active (BOOLEAN)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)

#### oauth_authorization_codes
- id (TEXT, PK)
- client_id (TEXT, FK to oauth_clients)
- user_id (TEXT, FK to users)
- code (TEXT, unique)
- redirect_uri (TEXT)
- scope (TEXT)
- expires_at (TIMESTAMP)
- created_at (TIMESTAMP)
- used_at (TIMESTAMP)

#### oauth_tokens
- id (TEXT, PK)
- client_id (TEXT, FK to oauth_clients)
- user_id (TEXT, FK to users)
- access_token (TEXT, unique)
- refresh_token (TEXT, unique, hashed)
- token_type (TEXT)
- scope (TEXT)
- expires_at (TIMESTAMP)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)

### 2. API Endpoints

#### Authorization Endpoint
`GET /oauth/authorize`
Parameters:
- response_type (required, "code")
- client_id (required)
- redirect_uri (required, must match registered URI)
- scope (optional)
- state (recommended)
- code_challenge (required for PKCE)
- code_challenge_method (required for PKCE, "S256")

#### Token Endpoint
`POST /oauth/token`
Parameters:
- grant_type (required, "authorization_code" or "refresh_token")
- code (required for authorization_code grant)
- redirect_uri (required for authorization_code grant)
- client_id (required)
- client_secret (required for confidential clients)
- code_verifier (required for PKCE)
- refresh_token (required for refresh_token grant)

#### User Info Endpoint
`GET /oauth/userinfo`
Headers:
- Authorization: Bearer <access_token>

#### Token Revocation Endpoint
`POST /oauth/revoke`
Parameters:
- token (required)
- token_type_hint (optional)

### 3. JWT Structure
Access tokens will be JWTs with the following claims:
- iss (Issuer)
- sub (Subject - user ID)
- aud (Audience - client ID)
- exp (Expiration time)
- iat (Issued at time)
- jti (JWT ID)
- scope (Space-separated scopes)

## Integration with Existing Systems

### 1. Remote Server Integration
Remote servers will be integrated as OAuth2 clients:
- Each remote server gets a client ID and secret
- Remote servers use the authorization code flow to authenticate users
- Permissions are scoped to what the remote server is allowed to access

### 2. Project Integration
Projects will also be integrated as OAuth2 clients:
- Each project gets a client ID
- Public projects use PKCE
- Confidential projects use client secrets
- Scopes are limited to project requirements

## Testing Strategy

### 1. Unit Tests
- Test token generation and validation
- Test authorization code flow
- Test PKCE implementation
- Test scope validation

### 2. Integration Tests
- End-to-end authorization flow
- Token refresh flow
- Revocation flow
- Error conditions

### 3. Security Tests
- Token injection tests
- PKCE bypass attempts
- Rate limiting tests
- Scope escalation attempts

## Deployment Considerations

### 1. Environment Configuration
- Different JWT signing keys for development, staging, and production
- Secure storage of client secrets
- Proper CORS configuration
- Rate limiting configuration

### 2. Monitoring
- Log all authentication events
- Monitor for suspicious activity
- Track token usage
- Alert on security events

## Future Enhancements

### 1. Device Flow
Implement OAuth2 Device Flow for devices that cannot open URLs

### 2. Token Introspection
Implement token introspection endpoint for resource servers

### 3. Dynamic Client Registration
Allow dynamic registration of OAuth2 clients

## Conclusion
This OAuth2 implementation will provide a secure and standardized authentication mechanism for the NWA Alliance platform. The implementation follows OAuth2 best practices and includes proper security measures to protect user data and system resources.