import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    console.log('Creating test user...');

    // Check if test user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: { userRoles: { include: { role: true } } }
    });

    if (existingUser) {
      console.log('Test user already exists with ID:', existingUser.id);
      console.log('Current roles:', existingUser.userRoles.map(ur => ur.role.name));

      // Check if user has admin role
      const hasAdminRole = existingUser.userRoles.some(ur =>
        ur.role.name.toLowerCase() === 'admin'
      );

      if (hasAdminRole) {
        console.log('User already has admin privileges');
        return;
      } else {
        console.log('User exists but needs admin role assigned');
      }
    }

    let user;

    if (!existingUser) {
      // Create test user
      const hashedPassword = await bcrypt.hash('password', 10);

      user = await prisma.user.create({
        data: {
          name: 'Test User',
          email: '<EMAIL>',
          passwordHash: hashedPassword,
          profile: {
            create: {
              firstName: 'Test',
              lastName: 'User',
              nwaEmail: '<EMAIL>'
            }
          }
        }
      });

      console.log('Test user created:', user.id);
    } else {
      user = existingUser;
    }

    // Create admin role if it doesn't exist
    let adminRole = await prisma.role.findFirst({
      where: { name: { mode: 'insensitive', equals: 'admin' } }
    });

    if (!adminRole) {
      adminRole = await prisma.role.create({
        data: { name: 'ADMIN' }
      });
      console.log('Admin role created');
    }

    // Check if user already has admin role
    const existingUserRole = await prisma.userRole.findFirst({
      where: {
        userId: user.id,
        roleId: adminRole.id
      }
    });

    if (!existingUserRole) {
      // Assign admin role to test user
      await prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: adminRole.id
        }
      });
      console.log('Admin role assigned to test user');
    } else {
      console.log('User already has admin role');
    }

  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();